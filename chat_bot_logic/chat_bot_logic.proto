syntax = "proto3";

package ga.chat_bot_logic;

import "ga_base.proto";
import "web_im_logic/web_im_logic.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/chat-bot-logic";

enum AIPartnerEntranceType {
    // 无入口
    AI_PARTNER_ENTRANCE_TYPE_UNSPECIFIED = 0;
    // 树洞入口
    AI_PARTNER_ENTRANCE_TYPE_SOULMATE = 1;
    // 多角色入口
    AI_PARTNER_ENTRANCE_TYPE_MULTI_ROLE = 2;
    // 桌宠入口
    AI_PARTNER_ENTRANCE_TYPE_PET = 3;
    // 群组入口
    AI_PARTNER_ENTRANCE_TYPE_GROUP = 4;
}

enum AIPartnerEntranceSource {
    AI_PARTNER_ENTRANCE_SOURCE_UNSPECIFIED = 0;
    // IM页面
    AI_PARTNER_ENTRANCE_SOURCE_IM_TAB = 1;
    // 登录
    AI_PARTNER_ENTRANCE_SOURCE_LOGIN = 2;
}

// AI形象类型
enum AIRoleType {
    // 树洞形象
    AI_ROLE_TYPE_PARTNER_UNSPECIFIED = 0;
    // 游戏形象/角色扮演
    AI_ROLE_TYPE_GAME = 1;
    // 桌宠
    AI_ROLE_TYPE_PET = 2;
}

enum AIRoleState {
    AI_ROLE_STATE_UNSPECIFIED = 0;
    // 公开
    AI_ROLE_STATE_PUBLIC = 1;
    // 私有
    AI_ROLE_STATE_PRIVATE = 2;
}

// AI形象卡信息
message AIRole {
    uint32 id = 1;
    // AI头像
    string avatar = 2;
    // AI风格
    string style = 3;
    // AI性别 0:女 1:男 2:其他
    int32 sex = 4;
    // 背景图
    string image = 5;
    // 形象类型 see enum AIRoleType
    uint32 type = 6;
    // 名称
    string name = 7;
    // 状态 see enum AIRoleState
    uint32 state = 8;
    // 创建角色的用户uid
    uint32 uid = 9;
    // 透传中台扩展信息
    bytes ext = 10;
	// 入口标签
	string entrance_tag = 11;
}

// AI关系
message AIRelationship {
    uint32 id = 1;
    string name = 2;
}

// AI伴侣信息
message AIPartner {
    enum Relationship {
        RELATIONSHIP_UNSPECIFIED = 0;
        // 朋友
        RELATIONSHIP_FRIEND = 1;
        // 恋人
        RELATIONSHIP_LOVER = 2;
    }
    
    uint32 id = 1;
    // ta的名字
    string name = 2;
    // 你希望ta怎么称呼你
    string call_name = 3;
    // AI伴侣形象卡
    AIRole role = 4;
    // 你们的关系(已废弃, 改成relation, 从中台获取)
    Relationship relationship = 5;
    // [不再接收ta的消息]开关 true:打开 false:关闭
    bool silent = 6;
    // 用户与AI伴侣关系
    AIRelationship relation = 7;
    // 是否展示hint内容
    bool show_hint = 8;
    // 描述文本
    string hint = 9;
}

// AI伴侣统一推送
message AIPartnerPushMsg {
    // 接收推送的用户
    uint32 uid = 1;
    // AI伴侣ID
    uint32 partner_id = 2;

    // 推送类型(WEB与中台对接)
    uint32 type = 3;
    // 推送内容
    bytes data = 4;
}

message GetAIPartnerEntranceRequest {
    ga.BaseReq base_req = 1;
}

message GetAIPartnerEntranceResponse {
    ga.BaseResp base_resp = 1;
        
    // 是否展示触达入口
    bool enable = 2;
    
    // 入口背景图
    string background = 3;
    // 跳转链接
    string jump_link = 4;
    // IM消息文案库
    repeated string im_msg_texts = 5;
    
    // 用户的AI伴侣(为空表示还没创建过AI伴侣)
    AIPartner partner = 6;
}

message AIPartnerEntrance {
    // 入口类型 see enum AIPartnerEntranceType
    uint32 type = 1;
    // 前景图
    string foreground = 2;
    // 背景图
    string background = 3;
    // 跳转链接
    string jump_link = 4;
    // 旧版本入口:返回默认第一个树洞伴侣 新版本入口:返回全量AI伴侣
    repeated AIPartner partners = 5;
    // 标题
    string title = 6;
    // 标签
    string tag = 7;
    // 副标题
    string subtitle = 8;
    repeated ga.web_im_logic.LastGroupMsg last_group_msgs = 9;  //群组列表  当ENTRANCE_CONTENT_TYPE_GROUP 返回
}

message GetAIPartnerEntranceV2Request {
    ga.BaseReq base_req = 1;
    // 请求来源
    AIPartnerEntranceSource source = 2;
}

message GetAIPartnerEntranceV2Response {
    ga.BaseResp base_resp = 1;
    AIPartnerEntrance entrance = 2;
}

message GetAIPartnerEntranceListRequest {
    ga.BaseReq base_req = 1;
    // 请求来源 see enum AIPartnerEntranceSource
    uint32 source = 2;
    // 指定需要返回的入口 see enum AIPartnerEntranceType
    repeated uint32 entrance_type_list = 3;
}

message GetAIPartnerEntranceListResponse {
    ga.BaseResp base_resp = 1;
    repeated AIPartnerEntrance entrance_list = 2;
}

// AI角色交互配置
message AIRoleInteractiveConfig {
    // 角色ID
    uint32 id = 1;
    // 透传AIGC中台扩展配置
    bytes ext = 2;
}

message GetAIRoleInteractiveConfigRequest {
    ga.BaseReq base_req = 1;
    
    // 角色ID
    uint32 id = 2;
}

message GetAIRoleInteractiveConfigResponse {
    ga.BaseResp base_resp = 1;
    AIRoleInteractiveConfig config = 2;
}

message ReportAIPetBehaviorRequest {
    ga.BaseReq base_req = 1;
    
    // 客户端与AIGC中台约定的行为枚举
    uint32 action = 2;

    uint32 role_id = 3;
    uint32 partner_id = 4;
    
    string tip = 5;
}

message ReportAIPetBehaviorResponse {
    ga.BaseResp base_resp = 1;

	// 上报后分配给tip的消息ID
	string msg_id = 2;
	// 上报时间 ms
	int64 reported_at = 3;
}
