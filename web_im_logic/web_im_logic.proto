syntax = "proto3";

package ga.web_im_logic;

import "ga_base.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/web-im-logic";

// 业务唯一标识
enum WebImCmd {
    WEB_IM_CMD_UNSPECIFIED = 0;
    WEB_IM_CMD_AI_PARTNER = 1;
    WEB_IM_CMD_AI_GROUP = 2;
}

// IM消息推送
message ImMsgPush {
    // 业务唯一标识
    WebImCmd cmd = 1;

    // 接收消息的用户id
    uint32 uid = 2;
    // 发送方唯一标识
    string source = 3;

    // 消息内容
    ImMsg msg = 4;
    // 业务自定义扩展内容
    bytes ext = 5;

    // 发送时间
    int64 sent_at = 6;

    bool is_read_heart_entrance = 7; //是否有读心
}

message ImGroupMsgPush {
    // 业务唯一标识
    WebImCmd cmd = 1;

    // 接收消息的用户id
    uint32 uid = 2;

    uint32 group_id = 3;

    uint32 group_template_id = 4;

    uint32 role_id = 5;

    // 消息内容
    ImMsg msg = 6;

    string jump_url = 7;

    // 发送时间
    int64 sent_at = 8;

    string avatar = 9; //群头像

    string group_name = 10; //群名称

    string im_tab_tag = 11; //群标签

    bool is_trigger = 12; //是否触发

    string sender_nick_name = 13; //发送者昵称

    repeated uint32 at_uids = 14; //用户at用户，角色at用户
    repeated uint32 at_role_ids = 15; //多人群聊(角色at角色，用户at角色)

    uint32 sender_uid = 16;  //发送的用户uid

    GroupSendType group_send_type = 17;
}

//多人群组开始起用
//内容类型
enum ImMsgContentType {
    IM_MSG_CONTENT_TYPE_UNSPECIFIED = 0;
    // 文本
    IM_MSG_CONTENT_TYPE_TEXT = 1;
    // 文本+语音
    IM_MSG_CONTENT_TYPE_TEXT_VOICE = 2;
    // 表情
    IM_MSG_CONTENT_TYPE_EMOTION = 3;
    // 图片
    IM_MSG_CONTENT_TYPE_IMAGE = 4;
}

//业务类型
enum ImBusiType {
    //未知类型
    IM_BUSI_TYPE_UNSPECIFIED = 0;
    // 单人群聊
    IM_BUSI_TYPE_SINGLE_GROUP = 1;
    // 多人群聊
    IM_BUSI_TYPE_MULTI_GROUP = 2;
    // 多角色聊天
    IM_BUSI_TYPE_MULTI_ROLE = 3;
}

//命令类型 非内容消息需要指定业务类型前缀
enum ImCmdType {
    //未知类型
    IM_CMD_TYPE_UNSPECIFIED = 0;
    // 内容消息
    IM_CMD_TYPE_CONTENT_MSG = 1;
}

message ImMsg {
    enum Type {
        TYPE_UNSPECIFIED = 0;
        // 文本
        TYPE_TEXT = 1;
        // 表情
        TYPE_EMOTICON = 3;
        // 沉默(询问用户是否切换AI伴侣到活跃状态)
        TYPE_SILENCE = 4;
        // AI伴侣统一消息通道
        TYPE_AI_PARTNER = 6;
    }
    
    // 沉默的扩展消息
    message ExtSilence {
        // 摸摸头，不是哦(需要调接口，AI伴侣由沉默变成活跃)
        string active_text = 1;
        // 随便你(不需要调接口，AI伴侣已是沉默状态)
        string silent_text = 2;
    }
    
    // 消息类型
    uint32 type = 1;
    // 消息内容
    string content = 2;
    // 消息扩展
    bytes ext = 3;

    // 消息发送的服务端时间(毫秒)
    int64 sent_at = 4;
    // 消息唯一标识
    string msg_id = 5;

    // 来自文案
    string come_from = 6;
    uint32 seq_id = 7; // 消息序号,用于群组

    uint32 im_busi_type = 8; //业务类型 ImBusiType
    uint32 content_type = 9; //内容类型 ImMsgContentType
    uint32 im_cmd_type = 10; //命令类型 ImCmdType
}

message WebImMsg {
    string target = 1;
    ImMsg msg = 2;
}

message SendWebImMsgRequest {
    ga.BaseReq base_req = 1;

    // 业务唯一标识
    WebImCmd cmd = 2;
    // 接收方唯一标识
    string target = 4;
    // 消息内容
    ImMsg msg = 5;
}

message SendWebImMsgResponse {
    ga.BaseResp base_resp = 1;

    // 发送时间(毫秒)
    int64 sent_at = 2;
    // 消息唯一标识
    string msg_id = 3;
}


message GetWebImMsgListRequest {
    ga.BaseReq base_req = 1;

    // 业务唯一标识
    WebImCmd cmd = 2;
    // 对话中对方的唯一标识
    string target = 3;
    // 拉取大于id的消息
    string msg_id = 4;
}

message GetWebImMsgListResponse {
    ga.BaseResp base_resp = 1;

    // 返回的消息列表(按msg_id排序)
    repeated ImMsg msgs = 2;
}

message ReadWebImMsgRequest {
    ga.BaseReq base_req = 1;

    // 业务唯一标识
    WebImCmd cmd = 2;
    // 对话中对方的唯一标识
    string target = 3;
    // 标记小于等于id的消息已读
    string msg_id = 4;
}

message ReadWebImMsgResponse {
    ga.BaseResp base_resp = 1;
}

message GetUserWebImMsgListRequest {
    ga.BaseReq base_req = 1;

    // 业务唯一标识
    WebImCmd cmd = 2;
    // 拉取消息的用户id
    uint32 uid = 3;
    // 拉取大于id的消息
    string msg_id = 4;
    // 一次拉取的数量
    uint32 limit = 5;
}

message GetUserWebImMsgListResponse {
    ga.BaseResp base_resp = 1;
    repeated WebImMsg msg_list = 2;
}

message SendWebImMsgCommonReq {
    uint32 uid = 1;
    string target = 2;
    ImMsg msg = 3;
}

message SendWebImMsgCommonResp {
    string msg_id = 1;
    int64 sent_at = 2;
}

message GetWebImMsgCommonReq {
    uint32 uid = 1;
    string target = 2;
    string msg_id = 3;
}

message GetWebImMsgCommonResp {
    repeated ImMsg msg_list = 1;
}

message ReadWebImMsgCommonReq {
    uint32 uid = 1;
    string target = 2;
    string msg_id = 3;
}

message ReadWebImMsgCommonResp {
}

message GetUserWebImMsgCommonReq {
    uint32 uid = 1;
    string msg_id = 3;
    uint32 limit = 4;
}

message GetUserWebImMsgCommonResp {
    repeated WebImMsg msg_list = 1;
}

enum H5PushType {
    H5_PUSH_TYPE_UNSPECIFIED = 0;
    // data为json.Marshal后的game_red_dot_logic.proto RedDotUpdateNotify
    H5_PUSH_TYPE_RED_DOT = 1;
    // 亲密度升级
    H5_PUSH_TYPE_INTIMACY_LEVEL_UP = 2;
    // 剧本匹配确认
    H5_PUSH_TYPE_SCRIPT_MATCH_CONFIRM = 3;
    // 剧本匹配成功
    H5_PUSH_TYPE_SCRIPT_MATCH_FOUND = 4;
    // 社区发帖引导
    H5_PUSH_TYPE_PUBLISH_POST_GUIDE = 5;
    // 社区发帖任务完成
    H5_PUSH_TYPE_POST_TASK_FINISH = 6;
}

// GAME_COMMON_H5_PUSH = 188; // 开黑通用H5推送
message CommonH5PushMsg {
    enum MarshalType {
        MARSHAL_TYPE_UNSPECIFIED = 0;
        MARSHAL_TYPE_JSON = 1;
    }
    // 推送类型, see enum H5PushType
    uint32 push_type = 1;
    // 推送内容
    bytes data = 2;
    // 数据解析类型, see enum MarshalType
    uint32 marshal_type = 3;
}

message SendGroupImMsgRequest {
    ga.BaseReq base_req = 1;

    // 业务唯一标识
    WebImCmd cmd = 2;

    uint32 group_id = 3;
    uint32 group_template_id = 4;
    repeated uint32 role_ids = 5;  //at时放这里
    // 消息内容
    ImMsg msg = 6;
    repeated uint32 at_uids = 7; //用户at用户，角色at用户
}

message SendGroupImMsgResponse {
    ga.BaseResp base_resp = 1;

    // 发送时间(毫秒)
    int64 sent_at = 2;
    // 消息唯一标识
    uint32 seq_id = 3;
}

//拉取群组的最新消息,拉取红点，拉取最新一条消息都用这个接口
message BatchGetGroupLastMsgRequest {
    ga.BaseReq base_req = 1;

    // 业务唯一标识
    WebImCmd cmd = 2;

    repeated uint32 group_ids = 3;
}

message GroupBaseInfo {
    uint32 unread_red_dot_cnt = 1;
    string avatar = 2;
    string jump_url = 3;
    string group_name = 4;
    string im_tab_tag = 5;
    string sender_nick_name = 6; //发送者昵称
}

message LastGroupMsg {
    uint32 group_id = 1;
    GroupBaseInfo group_base_info = 2;
    GroupTimeLineMsg time_line_msg = 3;
}

message BatchGetGroupLastMsgResponse {
    ga.BaseResp base_resp = 1;
    repeated LastGroupMsg last_group_msgs = 2;
}

message GetGroupMsgListRequest {
    ga.BaseReq base_req = 1;

    // 业务唯一标识
    WebImCmd cmd = 2;

    uint32 group_id = 3;
    uint32 seq_id = 4;    //当seq_id为0时，从尾开始拉

    // 一次拉取的数量
    uint32 limit = 5;
}

message GetGroupMsgListResponse {
    ga.BaseResp base_resp = 1;
    repeated GroupTimeLineMsg time_line_msgs = 2;
}

message GroupTimeLineMsg {
    // 群实例id
    uint32 group_id = 1;
    uint32 group_template_id = 2;
    repeated uint32 role_ids = 3;  //如果ai发往用户这里只有一个角色，用户发往ai是个at列表
    // 用户id
    uint32 uid = 4;

    ImMsg msg = 5;

    GroupSendType group_send_type = 6;
    repeated uint32 at_uids = 7; //用户at用户，角色at用户
    repeated uint32 at_role_ids = 8; //多人群聊(角色at角色，用户at角色)
}

enum GroupSendType {
    GROUP_SEND_TYPE_UNSPECIFIED = 0;
    // 用户上发
    GROUP_SEND_TYPE_USER2AI = 1;
    // AI下发
    GROUP_SEND_TYPE_AI2USER = 2;
}
