syntax = "proto3";

package ga.game_server_v2;

import "ga_base.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/game-server-v2";

message GetScanGameListConfReq{
    ga.BaseReq base_req = 1;
}
message ScanGameInfo{
    string game_name = 1;
    uint32 game_id   = 2;     //保留,目前都为0
    uint32 score     = 3;
    repeated string ios_package = 4;
    repeated string android_package = 5;
    bool is_hidden = 6; // true:不外显
    // 极速游戏包(扫描规则)
    repeated string pc_package = 7[deprecated=true];
    // 极速游戏包(扫描规则- Windows-进程名称)
    repeated string pc_process_name_package = 8;
    // 极速游戏包(扫描规则- Windows-显示名称)
    repeated string pc_show_name_package = 9;
}
message GetScanGameListConfResp{
    ga.BaseResp base_resp = 1;
    uint32 scan_switch  = 2;
    repeated ScanGameInfo scan_game_list = 3;
}

message GameScanResult{
    string game_name = 1;
    uint32 game_id   = 2;
}
message ReportGameScanResultReq{
    ga.BaseReq base_req = 1;
    repeated GameScanResult game_list = 2;
}

message ReportGameScanResultResp{
    ga.BaseResp base_resp = 1;
}
