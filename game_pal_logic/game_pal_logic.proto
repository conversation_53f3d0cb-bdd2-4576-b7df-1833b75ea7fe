syntax = "proto3";

package ga.game_pal_logic;

import "ga_base.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/game-pal-logic";

// 游戏搭子卡审核状态
enum GamePalCardAuditState {
  GAME_PAL_CARD_AUDIT_STATE_UNSPECIFIED = 0;
  // 审核中
  GAME_PAL_CARD_AUDIT_STATE_REVIEW = 1;
  // 审核通过
  GAME_PAL_CARD_AUDIT_STATE_PASS = 2;
  // 审核不通过
  GAME_PAL_CARD_AUDIT_STATE_REJECT = 3;
}

// 游戏搭子卡状态
enum GamePalCardState {
  GAME_PAL_CARD_STATE_UNSPECIFIED = 0;
  // 点亮
  GAME_PAL_CARD_STATE_LIT = 1;
  // 熄灭
  GAME_PAL_CARD_STATE_EXTINCT = 2;
}

// 游戏搭子发布类型
enum GamePalPublishType {
  // 默认 普通发布
  GAME_PAL_PUBLISH_TYPE_UNSPECIFIED = 0;
  // 超级发布
  GAME_PAL_PUBLISH_TYPE_SUPER = 1;
}

// 游戏玩法信息
message GamePalTab {
  uint32 id = 1;
  string name = 2;
  string image_url = 3;
  // 搭子卡背景底色值
  uint32 back_color_num = 4;
}

message GamePalBlock {
  uint32 id = 1;
  string name = 2;
  // 最多可选
  uint32 most_select_num = 3;//当mode=SETUP_NUMBER时有效

  repeated GamePalElem elems = 4;
  enum Mode {
    MODE_SINGLE_UNSPECIFIED = 0;     //单选
    MODE_MULTI = 1;      //不限
    MODE_SETUP_NUMBER = 2;    //最多选N个
  }
  uint32 mode = 5; //block可选择模式，Mode枚举中的值
}

message GamePalElem {
  uint32 id = 1;
  string name = 2;
}

message GamePalBaseCard {
  // 游戏搭子卡ID
  string id = 1;
  // 游戏玩法ID
  uint32 tab_id = 2;
  // 游戏玩法名称
  string tab_name = 3;
  // 游戏搭子卡擦亮时间
  int64 lighten_at = 4;
  // 交友宣言
  string social_decl = 5;
  // 图片链接(返回时用到)
  repeated string image_urls = 6;
  // 游戏搭子卡属性字段
  repeated GamePalBlock props = 7;
  // 小众游戏的父tab_id（其它游戏）
  uint32 parent_id = 8;
  // 玩法图片
  string tab_img = 9;
  // 最近在玩配图
  string small_card_img = 10;
  // 搭子卡背景色值（同游戏卡背景色值）
  uint32 back_color_num = 11;
  // 用户音频数据
  AudioInfo audio_info = 12;
}

message AudioInfo{
  // 发布时存储的key（发布）
  string key = 1;
  // 拉取搭子数据时完整的音频地址（拉取）
  string url = 2;
  // 录音时长，秒
  uint32 duration = 3;
  // 游戏搭子卡审核状态 同GamePalCardAuditState
  uint32 audit_state = 4;
}

message GamePalCard {
  // 游戏搭子卡基础信息
  GamePalCardInfo base_card = 1;

  // 图片key(发布时用到)
  repeated string image_keys = 2;

  // 游戏搭子卡状态 GamePalCardState
  uint32 state = 3;
  // 游戏搭子卡审核状态 GamePalCardAuditState
  uint32 audit_state = 4;

  // 游戏搭子卡当天擦亮次数
  uint32 lighten_count = 5;
  // 游戏搭子卡当天擦亮次数上限
  uint32 lighten_limit = 6;

  // 游戏搭子卡下次可擦亮时间
  int64 next_lighten_at = 7;
}

message CheckPublishConditionRequest {
  ga.BaseReq base_req = 1;

  uint32 tab_id = 2;
  // 发布类型 see enum GamePalPublishType
  uint32 publish_type = 3;
}

message CheckPublishConditionResponse {
  ga.BaseResp base_resp = 1;

  bool banned = 2;
  string ban_reason = 3;
}

message GetGamePalCardPropsRequest {
  ga.BaseReq base_req = 1;

  uint32 tab_id = 2;
}

message GetGamePalCardPropsResponse {
  ga.BaseResp base_resp = 1;

  // 搭子卡属性字段
  repeated GamePalBlock props = 2;
  // 官方交友宣言
  repeated string social_decls = 3;
  // 游戏玩法信息
  GamePalTab tab = 4;
  // 用户截图信息
  repeated PhotoInfo photo_infos = 5;
  //用户形象数据
  PersonalImage person_image = 6;
  // 音频配置
  AudioConf audio_conf = 7;
}

message AudioConf {
  // 录音模板标题
  string demo_title = 1;
  // 录音模板内容
  repeated string demo_content = 2;
}

enum ImageSource {
  IMAGE_SOURCE_UNSPECIFIED = 0;
  // 游戏卡截图
  IMAGE_SOURCE_GAME_CARD = 1;
}

message PhotoInfo {
  string image_url = 1; //图片url
  uint32 image_source = 2; //图片来源，见ImageSource
}
message PublishGamePalCardRequest {
  ga.BaseReq base_req = 1;

  GamePalCard card = 2;
}

message PublishGamePalCardResponse {
  ga.BaseResp base_resp = 1;
}

message LightenGamePalCardRequest {
  enum Op {
    OP_UNSPECIFIED = 0;
    OP_SINGLE = 1; // 单个点亮
    OP_ALL = 2; // 全部点亮
  }

  ga.BaseReq base_req = 1;

  // Op
  uint32 op = 2;
  // 仅 OP_SINGLE 必填
  string game_pal_card_id = 3;
}

message LightenGamePalCardResponse {
  ga.BaseResp base_resp = 1;
}

message GetGamePalCardListRequest {
  ga.BaseReq base_req = 1;

  uint32 uid = 2;
}

message GetGamePalCardListResponse {
  ga.BaseResp base_resp = 1;

  repeated GamePalCard cards = 2;
}

message GetGamePalCardRequest {
  ga.BaseReq base_req = 1;

  uint32 uid = 2;
  uint32 tab_id = 3;
}

message GetGamePalCardResponse {
  ga.BaseResp base_resp = 1;

  GamePalCard card = 2;
}

message DeleteGamePalCardRequest {
  ga.BaseReq base_req = 1;

  string game_pal_card_id = 2;
}

message DeleteGamePalCardResponse {
  ga.BaseResp base_resp = 1;
}

//搭子卡筛选项
message GetGamePalFilterByTabIdRequest {
  ga.BaseReq base_req = 1;
  //游戏玩法id
  uint32 tab_id = 2;
  // 是否展示热门标签筛选，客户端判断实验
  bool is_show_label = 3;
}

message GamePalCardLabel{
  string label = 1;
  enum LabelType {
    LABEL_TYPE_UNSPECIFIED = 0;
    LABEL_TYPE_HOT = 1;
  }
  uint32 label_type = 2;
}

message GetGamePalFilterByTabIdResponse {
  ga.BaseResp base_resp = 1;
  repeated GamePalBlock game_pal_blocks = 2; //搭子卡筛选项
  // 配置的热门标签
  repeated string labels = 3[deprecated = true];
  // 推荐配置的热门标签
  repeated GamePalCardLabel rcmd_labels = 4;
}

// 获取搭子卡片列表 4060
message GetGamePalListReq {
  ga.BaseReq base_req = 1;
  repeated uint32 tab_ids = 2;  // 不传:综合频道
  uint32 sex = 3; //性别0：不限，1：男，2：女
  repeated GamePalBlockOption block_option = 4;
  repeated string no_browse_card_ids = 5; // 未浏览卡片id
  uint32 source = 6; // 请求来源，1-专区，2-房间
  uint32 req_type = 7; // 1-请求下一页，2-刷新
  repeated string force_insert_cards = 8; // 需要强插的搭子卡id
  repeated string labels = 9[deprecated = true]; // 推荐热门标签筛选
  repeated GamePalCardLabel rcmd_labels = 10; // 推荐热门标签筛选
}

message GamePalBlockOption {
  uint32 block_id = 1;           //块id
  uint32 elem_id = 2;            //块里面的元素id
}

message GetGamePalListResp {
  ga.BaseResp base_resp = 1;
  repeated GamePalItem items = 2;
  bool load_finish = 3;     //返回true表示没有下一页了
  string load_res_text = 4; // （小众游戏）列表返回文案
  ForceInsertInfo force_insert_info = 5; // 强插卡片信息
}

message ForceInsertInfo {
  repeated GamePalItem force_card_info_list = 1; // 强插的回复率高的卡片
  uint32 continuous_close_num = 2; // 连续关闭卡片数量后插入
  uint32 insert_card_num = 3; // 每次强插卡片数量
  uint32 max_day_insert_times = 4; // 每天最大强插次数
}

message GamePalItem {
  GamePalUserInfo user_info = 1;
  GamePalCardInfo game_pal_card_info = 2;
  string trace_id = 3; // 推荐追踪上报 跟房间列表footprint作用，客户端数据上报用上
}

message GamePalUserInfo {
  uint32 uid = 1;
  string account = 2;
  string nickname = 3;
  uint32 sex = 4;   //性别0：不限，1：男，2：女
  string loc = 5;
  bool is_online = 6;
  message RoomInfo {
    uint32 status = 1; // 0-卡片用户不在房，1-卡片用户在房，2-用户所在房在发布中
    string follow_label_img = 2;  // 跟随好友头像标头jpg图
    string follow_label_text = 3; // 跟随好友头像标头文案
    string find_playing_text = 4; //找人玩
    string find_playing_img = 5; //找人玩
    uint32 room_cid = 6; // 卡片主在房的cid
  }
  RoomInfo room_info = 7;
  uint32 follow_status = 8; // 1-互相没有关注，2-用户关注了卡片用户，3-卡片用户关注了用户，4-互关
  string reply_rate_text = 9; // 回复文案
}

message GamePalCardInfo {
  GamePalBaseCard base_info = 1;
  string purpose = 2;  // 发布目标（想找）
  string play_time = 3; // 用户选择的常玩时间
  string other_publish_info = 4; // 展示其它填写信息，一个字段单独展示，多个选项用/拼凑
  PersonalImage person_image = 5; //用户形象
  repeated string other_publish_infos = 6; // 由other_publish_info字段拆开而来
}

//开黑形象
message PersonalImage {
  repeated DimensionItem item = 1; //评分维度信息
  float total_score = 2; //综合分
  string game_key_words = 3; //关键词
  string game_key_words_desc = 4; //描述
  string self_intro = 5; //aigc交友宣言
  bool is_image_exist = 6; //形象是否存在
}
message DimensionItem {
  string id = 1; //维度id
  string name = 2; //维度名称
  float score = 3; //维度分数
}

//搭子卡熄灭推送
message PalCardStatusPush {
  uint32 uid = 1;
  repeated string pal_card_ids = 2;
}

// 提醒回复游戏搭子消息推送
message GamePalRemindReplyPush {
  uint32 uid = 1;
  // ttid
  string account = 2;
  // 气泡文案
  string bubble_text = 3;
  // 昵称
  string nickname = 4;
}

message GamePalSuperPublishUser {
  uint32 uid = 1;
  // 性别 0:女 1:男
  int32 sex = 2;
  // account
  string username = 3;
  // 昵称
  string nickname = 4;
}

message GamePalSuperPublishGreeting {
  uint32 id = 1;
  string content = 2;
}

message GamePalSuperPublishRequest {
  enum Gender {
    GENDER_UNSPECIFIED = 0;
    // 小哥哥
    GENDER_GG = 1;
    // 小姐姐
    GENDER_MM = 2;
  }

  ga.BaseReq base_req = 1;
  // 玩法ID
  uint32 tab_id = 2;
  // 性别 see enum Gender
  uint32 gender = 4;
  // 选择的欢迎语ID
  uint32 greeting_id = 5;
}

message GamePalSuperPublishResponse {
  ga.BaseResp base_resp = 1;
  // 消息内容
  string msg_content = 2;
  // 推荐下发用户列表
  repeated GamePalSuperPublishUser user_list = 3;
}

message ReportGamePalGreetingRequest {
  ga.BaseReq base_req = 1;
}

message ReportGamePalGreetingResponse {
  ga.BaseResp base_resp = 1;

  // 剩余打招呼次数
  uint32 remain = 2;
}

message GetGamePalSuperPublishPropsRequest {
  ga.BaseReq base_req = 1;

  // 玩法ID
  uint32 tab_id = 2;
}

message GetGamePalSuperPublishPropsResponse {
  ga.BaseResp base_resp = 1;

  // 欢迎语
  repeated GamePalSuperPublishGreeting greetings = 2;
  // 冷却时间
  uint32 super_publish_cd = 3;
}

// 打招呼游戏搭子用户
message GreetingGamePalUser {
  enum OnlineState {
    ONLINE_STATE_UNSPECIFIED = 0;
    // 在线
    ONLINE_STATE_ONLINE = 1;
    // 离线
    ONLINE_STATE_OFFLINE = 2;
  }

  uint32 uid = 1;
  uint32 sex = 2;
  // 在线状态 see enum OnlineState
  uint32 online_state = 3;
  // 用于客户端拼接头像、发送消息
  string account = 4;
  // 用户昵称
  string nickname = 5;
}

message GetGreetingGamePalUserListRequest {
  ga.BaseReq base_req = 1;
}

message GetGreetingGamePalUserListResponse {
  ga.BaseResp base_resp = 1;
  repeated GreetingGamePalUser user_list = 2;
}

message FilterGreetingGamePalUserListRequest {
  ga.BaseReq base_req = 1;
  repeated string im_account_list = 2; // im未读消息为0的用户列表
}

message FilterGreetingGamePalUserListResponse {
  ga.BaseResp base_resp = 1;
  // 满足打招呼互动的用户列表
  repeated string greeting_account_list = 2;
}

message GetIMShowGamePalCardRequest {
  ga.BaseReq base_req = 1;
  // IM聊天页对方账号
  string im_account = 2;
}

message GetIMShowGamePalCardResponse {
  ga.BaseResp base_resp = 1;
  // 需要展示的对方搭子卡
  GamePalItem item = 2;
}

message ShowUserGamePalGuideRequest {
  ga.BaseReq base_req = 1;
}

message ShowUserGamePalGuideResponse {
  ga.BaseResp base_resp = 1;
  // 是否展示引导用户填写搭子卡弹窗
  bool show_guide = 2;
}

message GetImTabGamePalListReq {
  ga.BaseReq base_req = 1;
  repeated string no_browse_card_ids = 2; // 未浏览卡片id
}

message GetImTabGamePalListResp {
  ga.BaseResp base_resp = 1;
  bool show_game_pal_list = 2; // 是否展示搭子列表，false则走游戏雷达逻辑
  repeated GamePalItem items = 3; // 搭子列表数据，为空则不展示
  uint32 create_guide_pos = 4; // 展示插入创建搭子卡引导的位置，为0则不展示
}

message HandleGamePalCardOnPublishRoomRequest {
  ga.BaseReq base_req = 1;
  // 玩法ID
  uint32 tab_id = 2;
  // 房间ID
  uint32 cid = 3;
}

message HandleGamePalCardOnPublishRoomResponse {
  ga.BaseResp base_resp = 1;
  // 是否展示发布搭子引导公屏
  bool show_guide = 2;
  // 返回配置的间隔天数n
  uint32 show_guide_internal_days = 3;
}

//搭子卡审核结果推送
message PalCardAuditResultPush {
  uint32 uid = 1;
  // 游戏搭子卡审核状态 同GamePalCardAuditState
  uint32 audit_state = 2;
  // tab_id
  uint32 tab_id = 3;
}

enum AssistantPushGamePalType {
  // 默认服务器消费关键词匹配推送，非客户端触发
  ASSISTANT_PUSH_GAME_PAL_TYPE_UNSPECIFIED = 0;
  // 登录后n分钟推送
  ASSISTANT_PUSH_GAME_PAL_TYPE_LOGIN =1;
  // 用户在TT助手点击游戏搭子入口
  ASSISTANT_PUSH_GAME_PAL_TYPE_CLICK_BUTTON =2;
}

message AssistantPushGamePalReq {
  ga.BaseReq base_req = 1;
  // 推送类型, SEE AssistantPushGamePalType
  uint32 push_type = 2;
}

message AssistantPushGamePalResp {
  ga.BaseResp base_resp = 1;
}

// TT助手搭子卡推送数据
message AssistantPushGamePalData  {
  enum PushDataType {
    PUSH_DATA_TYPE_UNSPECIFIED = 0;
    // 实验组2，展示配置样式
    PUSH_DATA_TYPE_DEFAULT =1;
    // 实验组1，展示im搭子数据
    PUSH_DATA_TYPE_GAME_PAL =2;
  }
  message DefaultPushData {
    string title = 1; // 标题
    string img = 2; // 图片
    string button_text = 3; // 按钮文案
    string button_color = 4; // 按钮颜色
  }

  repeated GamePalItem items = 1; // 搭子列表数据，为空则不展示，push_data_type:2有效
  uint32 push_data_type =2; // 推送数据类型
  DefaultPushData default_push_data =3; // 默认推送数据，push_data_type:1有效
  // 推送类型, SEE AssistantPushGamePalType
  uint32 push_type = 4;
}
