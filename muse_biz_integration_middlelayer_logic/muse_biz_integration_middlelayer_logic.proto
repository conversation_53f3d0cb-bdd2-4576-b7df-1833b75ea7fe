// 文档地址： https://q9jvw0u5f5.feishu.cn/docx/FTkkd4ZdKoUXEvxThq9cdMgMnCX
syntax = "proto3";

package ga.muse_biz_integration_middlelayer_logic;
import "muse_shining_point_logic/muse_shining_point_logic.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/muse_biz_integration_middlelayer_logic";


message MtEnterChannelPublicScreenExtend{
  repeated  string common_tags=1;  //通用标签  （年龄，地区）
  repeated ga.muse_shining_point_logic.ShiningPointInfo shining_point_list = 2; // 闪光点列表
  repeated ga.muse_shining_point_logic.ShiningPointExtraInfo shining_point_extra_list = 3; // 闪光点额外信息 按照认证顺序排列
}