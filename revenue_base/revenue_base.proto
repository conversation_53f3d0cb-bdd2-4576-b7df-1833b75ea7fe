syntax = "proto3";

package ga.revenue_base;

import "ga_base.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/revenue_base;revenue_base";


// 获取用户roi信息
message GetUserRoiInfoRequest {
  ga.BaseReq base_req = 1;
}
message GetUserRoiInfoResponse {
  ga.BaseResp base_resp = 1;
  bool is_roi = 2; // 是否是roi用户
  string roi_high_pop_url =3;  // 高潜用户弹窗
}

// 判断是否可见ROI用户
message CheckCanSeeRoiUserRequest {
  ga.BaseReq base_req = 1;
  uint32 channel_id=2;
}
message CheckCanSeeRoiUserResponse {
  ga.BaseResp base_resp = 1;
  bool can=2;
}


//确认高潜付费用户弹窗显示
message ConfirmRoiHighPotentailRequest{
   ga.BaseReq base_req = 1;
}
message ConfirmRoiHighPotentailResponse{
   ga.BaseResp base_resp = 1;
}


//roi高潜付费用户弹窗推送
message RoiHighPotentailNotify {
   string pop_url = 1;  //弹窗url
}


// 获取优质用户信息
message GetUserQualityInfoRequest {
  ga.BaseReq base_req = 1;
}
message GetUserQualityInfoResponse {
  ga.BaseResp base_resp = 1;
  bool is_quality = 2; // 是否是优质用户
  string high_cert_url =3;  // 壕标识
}

//确认壕用户弹窗显示
message ConfirmQualityHighPopRequest{
   ga.BaseReq base_req = 1;
}
message ConfirmQualityHighPopResponse{
   ga.BaseResp base_resp = 1;
}


/************************营收广告位相关 begin ***************/
//广告平台类型
enum AdPlatformType {
    AD_PLATFORM_TYPE_UNSPECIFIED = 0;
    AD_PLATFORM_TYPE_QQYLF  = 1;       //QQ优量汇
    AD_PLATFORM_TYPE_GROMORE = 2;      //Gromore聚合
    AD_PLATFORM_TYPE_IOP     = 3;      //FROM IOP
    AD_PLATFORM_TYPE_TTDSP   = 4;      //TT DSP
}

//信息流-广场 广告位参数
message AdFeedSquareParam {
  uint32 start_pos = 1;               //广告开始位置
  uint32 end_pos = 2;                 //广告结束位置
  uint32 interval_num = 3;            //广告展示间隔数量
  uint32 ad_num = 4;                  //连续插入广告数量
  string tab_name = 5;                //广场-推荐-其它TAB时(ad_feed_square_other) 的tab_name
  uint32 tab_type = 6 [deprecated = true];          //已废弃，0-默认，1-同城
}

//信息流-个人主页-动态 广告位参数
message AdFeedHomePageDynamicParam {
  uint32 min_dynamic_num = 1;         //最小动态数量(动态数量大于等于3条)
  uint32 start_pos = 2;               //广告开始位置
  uint32 interval_num = 3;            //广告展示间隔数量
  uint32 ad_num = 4;                  //连续插入广告数量
  uint32 show_down_min_num = 5 [deprecated = true];       //已废弃 全部动态下方展示(动态数量小于3条且不为0时)
  uint32 show_down_max_num = 6 [deprecated = true];       //已废弃 全部动态下方展示(动态数量小于3条且不为0时)
}

//已废弃
enum AdFlashSceneType {
  AD_FLASH_SCENE_TYPE_UNSPECIFIED = 0;
  AD_FLASH_SCENE_TYPE_HOT = 1; //热启动
  AD_FLASH_SCENE_TYPE_COLD = 2; //冷启动
}
//开屏广告位参数 已废弃
message AdFlashParam {
  uint32 scene = 1 [deprecated = true];       //已废弃 场景 AdFlashSceneType 1-热启动 2-冷启动
}

//广告位信息
message AdPosInfo {
  AdPlatformType platform_type = 1;
  string cli_pos_id = 2;    //位置ID
  string ad_pos_id = 3;     //广告位ID
  bytes  params = 4;        //参数: 广场-信息流参数为：AdFeedSquareParam， 个人主页动态参数为：AdFeedHomePageDynamicParam
  string ad_app_id = 5;     //广告应用ID
  string iop_param = 6;     //iop param
}

//登录获取用户广告位配置
message GetRevenueAdPosInfoRequest {
  ga.BaseReq base_req = 1;
  repeated uint32 support_platforms = 2;  //客户端支持的广告平台列表
  string bundle_id = 3;                   //应用包名
  bool   support_iop = 4;                 //是否支持IOP
}

message GetRevenueAdPosInfoResponse {
  ga.BaseResp base_resp = 1;
  repeated AdPosInfo pos_info_list = 2;    //广告位列表信息
  bool is_personal_open = 3;               //个性化推荐广告开关状态
  bool from_iop = 4;                       //是否来源iop
  string   adn_configs = 5;                //adn configs
}

//获取个性化推荐广告开关
message GetPersonalizedAdSwitchRequest {
  ga.BaseReq base_req = 1;
}
message GetPersonalizedAdSwitchResponse {
  ga.BaseResp base_resp = 1;
  bool is_open = 2;  //是否开启个性化推荐广告
}
//设置个性化推荐广告开关
message SetPersonalizedAdSwitchRequest {
  ga.BaseReq base_req = 1;
  bool is_open = 2;  //是否开启个性化推荐广告
}
message SetPersonalizedAdSwitchResponse {
  ga.BaseResp base_resp = 1;
}
/************************营收广告位相关 end ***************/

//角色邀请状态
enum InviteStatusType {
    INVITE_STATUS_TYPE_UNSPECIFIED = 0;
    INVITE_STATUS_TYPE_PROCESSING = 1;     // 处理中
    INVITE_STATUS_TYPE_ACCEPT = 2;     // 接受
    INVITE_STATUS_TYPE_REJECT = 3;     // 拒绝
    INVITE_STATUS_TYPE_INVALID = 4;     // 失效
}

// 会长经营后台角色邀请
message GuildManageRoleInviteMsg {
   uint32 id = 1;  // 邀请id
   InviteStatusType status = 2;  // 状态
   string title = 3;  // 标题
   string content = 4; // 内容
   string content_high_light = 5;  // 内容高亮文案，为空就不展示高亮
   string proc_desc = 6;  // 处理说明
   string proc_desc_high_light = 7;  // 处理说明高亮文案，为空就不展示高亮
}


// 处理会长经营后台角色邀请
message ProcGuildManageRoleInviteRequest {
   ga.BaseReq base_req = 1;
   uint32 id = 2;  // 邀请id
   bool is_accept = 3;  // 是否接受
}
message ProcGuildManageRoleInviteResponse {
   ga.BaseResp base_resp = 1;
}
