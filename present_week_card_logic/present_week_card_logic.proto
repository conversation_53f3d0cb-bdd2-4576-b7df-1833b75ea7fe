syntax = "proto3";

/***************周卡logic*****************/

package ga.present_week_card_logic;

import "ga_base.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/present-week-card-logic";

// 奖励领取状态
enum AwardStatusType{
    AWARD_STATUS_TYPE_UNSPECIFIED = 0;
    AWARD_STATUS_TYPE_LOCKING = 1;          // 尚不可领取
    AWARD_STATUS_TYPE_WAIT_TO_RECV = 2;     // 待领取
    AWARD_STATUS_TYPE_ALREADY_RECEIVED = 3; // 已领取
    AWARD_STATUS_TYPE_EXPIRED = 4;          // 已过期
}



message WeekCardAwardInfo{
    string award_icon = 1;   // 奖励图标
    string award_name = 2;   // 奖励名称
    uint32 award_value = 3;  // 奖励价值
    uint32 amount = 4;       // 数量
    uint32 vaild_time_s = 5; // 有效时间，秒

    uint32 price_type = 6;   // 价值类型， 1-红钻 2-T豆
}

message DailyAwardInfo{
    uint32 status = 1;                // 奖励状态，see AwardStatusType
    repeated WeekCardAwardInfo gift_list = 2;  // 奖品列表

    uint32 award_idx = 3;   // 奖励次序
}

// 周卡详情
message PresentWeekCardInfo{
    string card_id = 1;       // 周卡id
    string tier_name = 2;     // 档位名称
    uint32 price = 3;         // 档位价格
    
    enum BuyStatus{
        BUY_STATUS_UNSPECIFIED = 0;
        BUY_STATUS_NOT_BUY = 1;      // 未购买
        BUY_STATUS_ALREADY_BUY = 2;  // 已购买
        BUY_STATUS_LOCKING = 3;      // 当前不可购买
    }
    uint32 buy_status = 4;                  // 周卡购买状态
    repeated DailyAwardInfo award_list = 5; // 奖励列表

    uint32 origin_price = 6;    // 原价，仅作展示作用, 不为0时展示原价文案
    string selling_point_text = 7; // 周卡卖点文案；文案为空 或 已购买档位 时不展示

    uint32 expire_time = 8;   // 已购买周卡过期的时间，未购买的返回0
    string bottom_text = 9;   // 吸底文案，为空则不展示
}

  
// 获取周卡入口信息
message GetPresentWeekCardEntryRequest{
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2;
}

message GetPresentWeekCardEntryResponse{
    ga.BaseResp base_resp = 1;
    bool have_access = 2;           // 是否有入口
    enum ReceiveStatus{
        RECEIVE_STATUS_UNSPECIFIED = 0;
        RECEIVE_STATUS_WAIT_TO_RECV = 1;  // 奖励待领取
        RECEIVE_STATUS_NO_AWARD_AVAILABLE = 2; // 不可领取
    }
    uint32 receive_status = 3;          // 可领取状态
    
    // 仅pgc 自动拉起半屏提醒配置
    bool need_to_auto_show = 4; // 是否需要自动拉起
    uint32 stay_seconds = 5;    // 当日进入pgc房间首次停留 x 秒时，自动展示半屏页
    uint32 every_n_day = 6;     // 每n天自动展示一次；为0时，当天展示一次

    uint32 ad_idx = 7;          // 房间资源位顺序，配置0时周卡最后,非0时配置第几个就是在第几个,不管有没有打龙;如果超过了最大的个数就是最后
}

// 获取周卡详情
message GetPresentWeekCardInfoRequest{
    ga.BaseReq base_req = 1;
}

message GetPresentWeekCardInfoResponse{
    ga.BaseResp base_resp = 1;
    repeated PresentWeekCardInfo card_list = 2; // 周卡列表
}

// 购买周卡
message BuyPresentWeekCardRequest{
    ga.BaseReq base_req = 1;
    string card_id = 2;
}

message BuyPresentWeekCardResponse{
    ga.BaseResp base_resp = 1;
    uint64 balance = 2;         // T豆余额
}

// 领取周卡奖励
message ReceivePresentWeekCardRewardRequest{
    ga.BaseReq base_req = 1;
    string card_id = 2;
    uint32 award_idx = 3;   // 奖励次序, 6.43.0及之后废弃
}

message ReceivePresentWeekCardRewardResponse{
    ga.BaseResp base_resp = 1;

    repeated DailyAwardInfo award_list = 2; // 领取成功的奖励列表
}