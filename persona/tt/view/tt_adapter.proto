syntax = "proto3";

import "persona/options.proto";
import "persona/tt/channel.proto";

package rcmd.persona.tt.view.tt_adapter;
option go_package = "golang.52tt.com/protocol/services/rcmd/persona/tt/view/tt_adapter";


message TTAdapterChannelView {
    option(rcmd.persona.profileId) = *********;
    uint32 tag_id = 1[(persona.ref) = "channel_basic/tag_id"];  //房间标签id
    uint32 uid = 2[(persona.ref) = "channel_basic/uid"];
    uint32 music_channel_type  = 3[(persona.ref) = "channel_basic/music_channel_type"];
    repeated uint32 unlimited_block_ids = 4[(persona.ref) = "channel_basic/unlimited_block_ids"]; // 不限或者全选的block id
    uint32 channel_id = 5[(persona.ref) = "channel_basic/channel_id"];  //房间id
}

message TTAdapterUserView {
    option(rcmd.persona.profileId) = *********;
    uint32 reg_time = 1[(persona.ref) = "user_basic/reg_time"];
    uint32 sex = 2[(persona.ref) = "user_basic/sex"];
    uint32 age = 3[(persona.ref) = "user_basic/age"];
    uint32 province_code = 4[(persona.ref) = "user_loc/province_code"];
    uint32 pref_tag_id = 5[(persona.ref) = "user_offline/pref_tag_id"];
    uint32 channel_enter_source = 6[(persona.ref) = "user_channel_enter/channel_enter_source"];
}
