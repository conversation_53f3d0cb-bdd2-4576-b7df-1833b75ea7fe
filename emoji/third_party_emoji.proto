syntax = "proto3";

package ga.emoji;

import "ga_base.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/emoji";

// 获取第三方推荐表情包
message GetHotEmojiReq {
  ga.BaseReq base_req = 1;
  uint32 msg_source = 2; // 消息来源，见 im.proto MsgSourceType
}

message GetHotEmojiResp {
  ga.BaseResp base_resp = 1;
  repeated ThirdPartyEmojiInfo emoji_infos = 2; // 表情列表
}

// 获取搜索表情包
message GetSearchEmojiReq {
  ga.BaseReq base_req = 1;
  uint32 msg_source = 2; // 消息来源，见 im.proto MsgSourceType
  string keyword = 3;
  bool is_bi_follow = 4; // 是否双向关注
  uint32 chat_uid = 5; // 聊天对象uid
}

message GetSearchEmojiResp {
  ga.BaseResp base_resp = 1;
  repeated ThirdPartyEmojiInfo emoji_infos = 2; // 表情列表
  string keyword = 3;
}

// 表情信息
message BaseEmojiInfo {
  string url = 1; // 图url
  uint32 height = 2; // 高度
  uint32 width = 3; // 宽度
}

message ThirdPartyEmojiInfo {
  string emoji_id = 1; // 表情包id
  string md5 = 2; // 表情包md4
  BaseEmojiInfo origin_emoji_infos = 3; // 原图表情
  BaseEmojiInfo thumb_emoji_infos = 4; // 缩略图表情
  bool is_obs = 5; // 是否obs
  string obs_key = 6; // obs key
}

// 是否展示热门表情包入口
message CheckHotEmojiEntranceReq {
  ga.BaseReq base_req = 1;
  uint32 msg_source = 2; // 消息来源，见 im.proto MsgSourceType
}

message CheckHotEmojiEntranceResp {
  ga.BaseResp base_resp = 1;
  bool is_show = 2; // 是否展示
}
