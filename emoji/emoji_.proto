syntax = "proto2";

package ga.emoji;

import "ga_base.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/emoji";

message EmojiItem {
  required string emoji_id = 1;   //表情包id，原图下载<七牛cdn域名>/emoji/download/<这里是表情id>，缩略图下载<七牛cdn域名>/emoji/download/<这里是表情id>/<后面接七牛需要用到的图片压缩参数>
  required string url = 2;        //下载原图url，若有优先使用，若无使用id拼接
  required string thumbnail = 3;  //缩略url，若有优先使用，若无使用id拼接
  optional string name = 4;       //表情名字
  optional string desc = 5;       //表情描述，备用字段
  required uint32 height = 6;      //表情高像素(px)
  required uint32 width = 7;      //表情宽像素(px)
}

/*
    另存为“表情”到“我的表情”
    另存为图片到“我的表情”
*/
message SaveEmojiReq {
  enum SaveEmojiMode {
    INVALID = 0;
    SAVE_EMOJI_TO_EMOJI = 1;    //另存为“表情”到“我的表情”；或者刚刚上传完表情，通过获取的id调用
    SAVE_IMG_TO_EMOJI = 2;      //另存im消息中的图片到“我的表情”
  }

  required BaseReq base_req = 1;
  required SaveEmojiMode mode = 2;
  required string key = 3;        //mode选择了另存为“表情”到“我的表情”，传emoji_id；另存图片到“我的表情”，传IM消息中image的key
}

message SaveEmojiResp {
  required BaseResp base_resp = 1;
  required EmojiItem item = 2;    //表情信息
}

/*
    删除表情
*/
message DeleteEmojiReq {
  required BaseReq base_req = 1;
  required string package_id = 2;     //表情包的id
  repeated string emoji_ids = 3;      //表情id
}

message DeleteEmojiResp {
  required BaseResp base_resp = 1;
}

/*
    获取我的表情包列表.
*/
message GetEmojiPkgListReq {
  required BaseReq base_req = 1;
  optional int32 start_index = 2;         //分页参数，从第几项开始获取（起始下标为0）
  optional int32 count = 3;               //分页参数，拿多少项，默认获取40个；最多获取200一次
  optional int32 load_first_item = 4;     //此值>0，获取表情包列表同时会返回第一个表情包的表情列表
  optional int32 first_item_limit = 5;    //第一个表情包的表情列表拿多少项，默认获取40个；最多获取200一次
}

message GetEmojiPkgListResp {
  required BaseResp base_resp = 1;
  repeated EmojiPackage pkg_list = 2;     //表情包列表
  repeated EmojiItem first_item = 3;      //第一个高亮显示的表情列表
}


message EmojiPackage {
  required string package_id = 1;     //表情包id
  required uint64 update_time = 2;    //表情包最后修改时间
  optional string name = 3;           //表情包名字，备用字段
  required int32 total_count = 4;     //表情包内表情总数
  required string cover_url = 5;      //表情包封面图标url
  required uint32 owner_id = 6;       //表情包拥有者id，可与自身用户id对比，一致即可以做增删操作
  optional string type = 7;           //表情包类型，备用字段 use "normal_emoji_pkg" 、"super_player_privilege_lv1"
}

/*
    通过指定表情包id获取表情列表
*/
message GetEmojiListByPkgReq {
  required BaseReq base_req = 1;
  required string package_id = 2;         //表情包id
  optional int32 start_index = 3;         //分页参数，从第几项开始获取（起始下标为0）
  optional int32 count = 4;               //分页参数，拿多少项，默认获取40个；最多获取200一次
  optional uint64 update_time = 5;        //本地记录获取表情包的最后更新时间，若发现无更新，表情列表则不返回，使用本地缓存即可；获取到有新数据并且通过分页获取的时候，把这个置0，防止不返回数据
}

message GetEmojiListByPkgResp {
  required BaseResp base_resp = 1;
  required uint64 update_time = 2;        //表情包最后修改时间，若获取到的最后更新时间跟本地保存一致，则表示无更新，直接使用本地缓存表情包即可
  repeated EmojiItem items = 3;           //表情列表
  required string package_id = 4;         //表情包id
  optional string name = 5;               //表情包名字，备用字段
  required int32 total_count = 6;         //表情包内的表情总数量
}

