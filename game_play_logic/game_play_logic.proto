syntax = "proto3";

package ga.game_play_logic;

import "ga_base.proto";
import "topic_channel/topic_channel_.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/game-play-logic";

// CMD_UserGameRateReport = 5901; // 用户对开黑用户评价触发上报
message UserGameRateReportReq{
  ga.BaseReq base_req = 1;
  repeated GameRateReportItem items = 2;
}

enum GameUserRateSourceType{
  GAME_USER_RATE_SOURCE_TYPE_UNSPECIFIED = 0;
  GAME_USER_RATE_SOURCE_TYPE_CHANNEL = 1; // 房间
  GAME_USER_RATE_SOURCE_TYPE_IM_CHAT = 2; // IM聊天页
  GAME_USER_RATE_SOURCE_TYPE_ASSISTANT_PUSH = 3; // 语音助手推送
  GAME_USER_RATE_SOURCE_TYPE_CHANNEL_LETTER = 4; // 房间小信封来源
  GAME_USER_RATE_SOURCE_TYPE_PERSONAL_HOMEPAGE = 5; // 个人主页来源
}

enum GameRateType {
  GAME_RATE_TYPE_UNSPECIFIED = 0;
  // 未评价
  GAME_RATE_TYPE_NOT_RATE = 1;
  // 已评价
  GAME_RATE_TYPE_RATED = 2;
  // 已过期
  GAME_RATE_TYPE_EXPIRED = 3;
}

// 游戏时间请求来源
enum GameTimeRequestSource {
  GAME_TIME_REQUEST_SOURCE_UNSPECIFIED = 0;
  // 个人资料卡
  GAME_TIME_REQUEST_SOURCE_PERSONAL_CARD = 1;
  // 麦上
  GAME_TIME_REQUEST_SOURCE_ON_MIC = 2;
}

message GameRateReportItem {
  // see GameUserRateSourceType 1:房间 2:IM聊天页
  uint32 source = 1;
  // 被评价用户id
  uint32 rate_uid = 2;
  // 触发時間
  uint64 interact_time = 3;
  // 玩法id，IMChat来源时无
  uint32 tab_id = 4;
  // 被评价用户账号id
  string rate_account = 5;
}

message UserGameRateReportResp{
  ga.BaseResp base_resp = 1;
}


// CMD_GetUserNotRateCount = 5902; // IM页面获取未评价的数量，展示红点
message GetUserNotRateCountReq{
  ga.BaseReq base_req = 1;
  // 最新已读评价的id
  string rate_id = 2;
  // 最新已读评价的创建时间戳
  uint64 create_time = 3;
}

message GetUserNotRateCountResp{
  ga.BaseResp base_resp = 1;
  // 未过期且未评价的数量
  uint32 not_rate_count = 2;
  // 未读未评价数量
  uint32 not_read_count = 3;
  // 最新的下发评价/收到评价时间戳
  int64 first_record_time = 4;
}



// GetGameRateReportStatusInfo = 5903;
// IM聊天页首次触发上报
message SetFirstChatTokenReq{
  ga.BaseReq base_req = 1;
  uint32 source = 2;  // see GameUserRateSourceType
  string rate_account = 3; // 被评价用户账号id
  uint32 tab_id = 4; // 玩法id，非超级发布，该传参必填，否则服务器过滤掉
  bool is_super_publish = 5; // 是否搭子的超级发布来源，取超级发布的玩法id
}

message SetFirstChatTokenResp{
  ga.BaseResp base_resp = 1;
}


// 评价推送具体内容
message UserRateNotify {
  repeated UserRateNotifyItem item = 1; // 新增的用户评价
}

message UserRateNotifyItem {
  uint32 uid = 1;
  // see GameUserRateSourceType
  // 同上报的source，在IM上报的只在IM页展示，在房间上报的只会在房间展示提醒，客户端需要判断处理
  uint32 source = 2;
  // 被评价用户账号id
  string account = 3;
}

//  CMD_GetRateReputationInfo = 5904; // 获取互评标签和信誉分信息
message GetRateReputationInfoReq{
  ga.BaseReq base_req = 1;
  uint32 target_uid = 2; // 被评价用户id
}

message UserRateTagItem {
  // 是否展示开关
  bool is_show = 1;
  // tag detail
  enum RateTagType {
    RATE_TAG_TYPE_UNSPECIFIED = 0;
    RATE_TAG_TYPE_RECENTLY = 1;
    RATE_TAG_TYPE_HISTORY = 2;
  }
  message RateTagItem {
    // 标签名
    string name = 1;
    // 标签数量
    uint32 count = 2;
    // 标签类型, see RateTagType
    uint32 type = 3;
  }
  repeated RateTagItem tag_info = 2;
}

message RateReputation {
  // 分数
  uint32 score = 1;
  // 是否展示开关
  bool is_show = 2;
}

message GetRateReputationInfoResp{
  ga.BaseResp base_resp = 1;
  // 信誉分
  RateReputation reputation_info = 2;
  // 评价标签
  UserRateTagItem tag_info = 3;
}

enum UserAcquisitionStatus {
  USER_ACQUISITION_STATUS_UNSPECIFIED = 0;
  // 拉新用户
  USER_ACQUISITION_STATUS_PULL_REG_USER = 1;
  // 召回用户
  USER_ACQUISITION_STATUS_RECALL_USER = 2;
  // 活跃用户
  USER_ACQUISITION_STATUS_ACTIVE_USER = 3;
}

enum ABTest {
  AB_TEST_UNSPECIFIED = 0;
  // 音色引导上麦实验
  AB_TEST_VOICE_GUIDE_UP_MIC = 1;
  // 新旧版搜索入口实验
  AB_TEST_SEARCH_ENTRANCE = 2;
  // 房间发布搭子卡筛选项实验
  AB_TEST_ROOM_PUBLISH_GAME_PAL_OPTION = 3;
  // 麦上头像关注入口实验
  AB_TEST_MIC_AVATAR_FOLLOW_ENTRANCE = 4;
  // 设置进房提醒实验
  AB_TEST_SET_ENTER_ROOM_NOTIFY = 5;
}

message GetUserAcquisitionAndABTestResultReq {
  ga.BaseReq base_req = 1;
  uint32 ab_test = 2; // 实验枚举, see enum ABTest
}

message GetUserAcquisitionAndABTestResultResp {
  ga.BaseResp base_resp = 1;
  string result = 2; // AB实验结果值, ab_test传参为0时不会查AB实验结果, 会返回获客状态
  uint32 acquisition_status = 3; // 获客状态, see enum UserAcquisitionStatus
}

message PopUpTabInfo {
  uint32 tab_id = 1;
  string name = 2;
  string icon = 3;
}

message GetRecallPopUpReq {
  ga.BaseReq base_req = 1;
}

message GetRecallPopUpResp {
  ga.BaseResp base_resp = 1;
  string game_title = 2;
  repeated PopUpTabInfo tab_infos = 3;
  uint32 limit_cnt = 4;
}

message SubmitRecallPopUpReq {
  ga.BaseReq base_req = 1;
  repeated uint32 tab_ids = 2;
}

message SubmitRecallPopUpResp {
  ga.BaseResp base_resp = 1;
  bool is_in_abtest = 2;
}


message GetRecallTeamUpReq {
  ga.BaseReq base_req = 1;
  uint32 tab_id = 2;
}

message GetRecallTeamUpResp {
  ga.BaseResp base_resp = 1;
  string game_title = 2;
  // 玩法发布属性
  repeated topic_channel.Block blocks = 3;
}

enum SubmitTeamUpSource{
  SUBMIT_TEAM_UP_SOURCE_UNSPECIFIED = 0;
  // 回归弹窗
  SUBMIT_TEAM_UP_SOURCE_RECALL = 1;
  // 问题弹窗
  SUBMIT_TEAM_UP_SOURCE_QUESTION = 2;
  // 房间内上报偏好人群
  SUBMIT_TEAM_UP_SOURCE_ROOM_PREFER_PEOPLE = 3;
}

message SubmitRecallTeamUpReq {
  ga.BaseReq base_req = 1;
  repeated topic_channel.Block blocks = 2;
  uint32 source = 3; // see SubmitTeamUpSource
  uint32 tab_id = 4;
  repeated GamePlayQuestion questions = 5; // 选择的问题
  repeated string age_group_labels = 6; // 年龄人群标签
}

message GamePlayQuestion {
  string title = 1; // 问题内容
  repeated string label_vals = 2; // 绑定的标签 val
}


message SubmitRecallTeamUpResp {
  ga.BaseResp base_resp = 1;
}

// const unsigned int CMD_TransAudioToText = 5906; // 音频转换文本asr
message TransAudioToTextReq {
  ga.BaseReq base_req = 1;
  // 上传obs后的key
  string audio_key = 2;
}

message TransAudioToTextResp {
  ga.BaseResp base_resp = 1;
  // 音频转文字
  string trans_text = 2;
}

message GetRegisterPageConfigsReq {
  ga.BaseReq base_req = 1;
}

message GetRegisterPageConfigsResp {
  ga.BaseResp base_resp = 1;
  repeated GameRegisterConfig configs = 2;
}

message GameRegisterConfig {
  string title = 1; // 标题
  string icon = 2; // 图标
  repeated GameOption options = 3; // 选项
}

enum GameRegisterAction {
  GAME_REGISTER_ACTION_UNSPECIFIED = 0; // 无动作
  GAME_REGISTER_ACTION_ENTER_GAME_ZONE = 1; // 进入专区
}

message GameOption {
  string display_name = 1; // 选项外显名称
  uint32 action = 2; // 选中选项对应的动作类型，见GameRegisterAction
}

// 进房提醒权限
enum EnterRoomNotifyPerm {
  ENTER_ROOM_NOTIFY_PERM_UNSPECIFIED = 0;
  // 我的粉丝
  ENTER_ROOM_NOTIFY_PERM_FANS = 1;
  // 我的玩伴
  ENTER_ROOM_NOTIFY_PERM_PLAYMATES = 2;
  // 谁都不行
  ENTER_ROOM_NOTIFY_PERM_NOBODY = 3;
}

// 进房提醒设置
enum EnterRoomNotifyState {
  ENTER_ROOM_NOTIFY_STATE_UNSPECIFIED = 0;
  // 已订阅
  ENTER_ROOM_NOTIFY_STATE_SUBSCRIBED = 1;
  // 未订阅
  ENTER_ROOM_NOTIFY_STATE_UNSUBSCRIBED = 2;
}

// 在房时间
message InRoomTime {
  // 具体日，.e.g.:星期一...星期日，工作日，节假日
  string day = 1;
  // 时间范围，.e.g.:早上 6:00-8:00
  repeated string slots = 2;
}

message SaveGameTimeRequest {
  ga.BaseReq base_req = 1;

  // 进房提醒权限，see enum EnterRoomNotifyPerm
  uint32 enter_room_notify_perm = 2;
  // 在房时间
  repeated InRoomTime in_room_times = 3;
}

message SaveGameTimeResponse {
  ga.BaseResp base_resp = 1;
}

message GetGameTimeRequest {
  ga.BaseReq base_req = 1;

  // 不传获取自己的游戏时间信息
  uint32 uid = 2;
  // 请求来源 see enum GameTimeRequestSource
  uint32 req_source = 3;
}

message GetGameTimeResponse {
  ga.BaseResp base_resp = 1;

  // 进房提醒权限，see enum EnterRoomRemindPerm
  uint32 enter_room_notify_perm = 2;
  // 在房时间
  repeated InRoomTime in_room_times = 3;

  // 是否设置了对方进房提醒，see enum EnterRoomNotifyState
  uint32 enter_room_notify_state = 4;

  // 是否可以订阅
  bool is_subscribable = 5;
  // 不可订阅的原因
  string unsubscribable_reason = 6;
}

message SetEnterRoomNotifyRequest {
  ga.BaseReq base_req = 1;

  // 对方的uid
  uint32 uid = 2;
  // 进房提醒状态，see enum EnterRoomNotifyState
  uint32 state = 3;
}

message SetEnterRoomNotifyResponse {
  ga.BaseResp base_resp = 1;
}

// USER_ENTER_ROOM_PUSH = 184; // 进房提醒推送
message UserEnterRoomPush {
  // 用户id
  uint32 uid = 1;
  // 用户账号
  string account = 2;
  // 用户昵称
  string nickname = 3;
  // 文案
  string title = 4;
}

message CheckUserInRoomReq {
  ga.BaseReq base_req = 1;

  uint32 uid = 2;
}

message CheckUserInRoomResp {
  ga.BaseResp base_resp = 1;

  string toast = 2; // 如果用户不在线/不在房，给出对应toast
  bool is_in_room = 3; // 用户是否在房
}

message GameTimeInfo {
  // 进房提醒权限，see enum EnterRoomRemindPerm
  uint32 enter_room_notify_perm = 2;
  // 是否设置了对方进房提醒，see enum EnterRoomNotifyState
  uint32 enter_room_notify_state = 4;

  // 是否可以订阅
  bool is_subscribable = 5;
  // 不可订阅的原因
  string unsubscribable_reason = 6;

  uint32 uid = 7;
}

message GetGameTimeListRequest {
  ga.BaseReq base_req = 1;

  repeated uint32 uid_list = 2;
}

message GetGameTimeListResponse {
  ga.BaseResp base_resp = 1;

  repeated GameTimeInfo list = 2;
}

message FastPCFeedbackReq {
  ga.BaseReq base_req = 1;
  // 反馈内容
  string content = 2;
  // 反馈图片
  repeated string images = 3;
  // 联系方式
  string contact = 4;
  // 系统版本
  string system_ver = 5;
}

message FastPCFeedbackResp {
  ga.BaseResp base_resp = 1;
}