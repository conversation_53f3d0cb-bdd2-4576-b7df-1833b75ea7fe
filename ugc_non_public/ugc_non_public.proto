syntax = "proto3";

package ga.ugc_non_public;
import "ga_base.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/ugc_non_public";


// 附件
enum NonPublicAttachmentType {
  NON_PUBLIC_ATTACHMENT_TYPE_UNSPECIFIED = 0;
  NON_PUBLIC_ATTACHMENT_TYPE_IMAGE = 1;
  NON_PUBLIC_ATTACHMENT_TYPE_GIF = 2;
  NON_PUBLIC_ATTACHMENT_TYPE_VIDEO = 3;
  NON_PUBLIC_ATTACHMENT_TYPE_URL_CARD = 4; // 链接卡片形式
}

message NonPublicAttachment {
  uint32 attachment_type = 1; // 附件类型 NonPublicAttachmentType
  string attachment_content = 2; // 附件内容
  string extra_info = 3; //给客户端自定义玩，虽然以后加也可以
  uint64 create_at = 4; // 创建时间
  string key = 5;
  string vm_content= 6; // 带水印的
  // 只有type = VIDEO 才有的
  string param = 7;
  //原视频封面
  string origin_video_cover = 8;
  //extra 额外信息
  NonPublicExtra extra = 9;
}

message NonPublicExtra {
  uint32 width = 1;
  uint32 heigth = 2;
}


message NonPublicRichTextWords {
  string text = 1;	//文本
}

message NonPublicURLCard {
  string show_text = 1;	// 展示文本
  string show_icon_url = 2; // 返回客户端填
  string show_platform_name = 3;
  string show_platform_icon = 4;
  string real_url_addr = 5; // 真正的链接
}

message NonPublicVote {
  message Option {
    string id = 1;
    string option = 2;
    uint64 vote_count = 3;
  }

  uint32 uid = 1; // 帖子所属用户uid
  string title = 2;
  repeated Option options = 3;
  int64 expired_at = 4; // 结束时间，-1 为不结束
  string vote_option_id = 5;  // 用户投票选择的选项id, 为空表示没有投过
  uint64 total_votes = 6;
}

message NonPublicRichTextElement {
  oneof content {
    NonPublicRichTextWords words = 1;
    NonPublicAttachment multi_media = 2;
    NonPublicURLCard url_card = 3;
    NonPublicVote vote = 4;
    HighLightCommon   high_light_common=5;
  }
}

message AtUserInfo{
  uint32 uid=1;
  string nick_name=2;
  string account=3;
}
message HighLightCommon{
  string prefix=1;   //@，#
  string plain_text_splice=2;  //@nickname  @社群名
  oneof common{
    AtUserInfo user=3;
    SocialCommunityInfo social_community=4;
  }

}

enum NonPublicAbnormalStatus {
  NON_PUBLIC_ABNORMAL_STATUS_UNSPECIFIED = 0;
  NON_PUBLIC_ABNORMAL_STATUS_BANNED = 1; // 被屏蔽
  NON_PUBLIC_ABNORMAL_STATUS_UNDER_REVIEW = 2; // 审核中
}

enum NonPublicAttachmentDownloadPrivacy {
  NON_PUBLIC_ATTACHMENT_DOWNLOAD_PRIVACY_UNSPECIFIED = 0;
  NON_PUBLIC_ATTACHMENT_DOWNLOAD_PRIVACY_PRIVATE = 1;
  NON_PUBLIC_ATTACHMENT_DOWNLOAD_PRIVACY_PUBLIC = 2;
}

enum SceneStreamType{
  SCENE_STREAM_TYPE_UNSPECIFIED = 0;
  SCENE_STREAM_TYPE_SOCIAL_COMMUNITY_CATEGORY = 1; // 社群品类圈
  SCENE_STREAM_TYPE_SOCIAL_COMMUNITY_TALK = 2; // 社群讨论频道
  SCENE_STREAM_TYPE_SOCIAL_COMMUNITY_KNOWLEDGE = 3; // 社群干货频道
}

enum NonPublicPostPrivacyPolicy {
  NON_PUBLIC_POST_PRIVACY_POLICY_UNSPECIFIED = 0; // 默认是公开的
  NON_PUBLIC_POST_PRIVACY_POLICY_PRIVATE = 1;  // 仅自己可见
}

enum NonPublicPostMachineStatus{
  NON_PUBLIC_POST_MACHINE_STATUS_UNSPECIFIED = 0; /*默认*/
  NON_PUBLIC_POST_MACHINE_STATUS_SUSPICIOUS = 1; /*疑似*/
  NON_PUBLIC_POST_MACHINE_STATUS_NORMAL = 2; /*同意*/
  NON_PUBLIC_POST_MACHINE_STATUS_REJECT = 3; /*拒绝*/
}

enum NonPublicInteractiveType {
  NON_PUBLIC_INTERACTIVE_TYPE_UNSPECIFIED = 0;
  NON_PUBLIC_INTERACTIVE_TYPE_SOCIAL_COMMUNITY_COMMENT = 1; // 社群的评论
  NON_PUBLIC_INTERACTIVE_TYPE_SOCIAL_COMMUNITY_ATTITUDE = 2; // 社群的点赞
}

enum NonPublicPostOrigin{
  NON_PUBLIC_POST_ORIGIN_UNSPECIFIED = 0;
  NON_PUBLIC_POST_ORIGIN_SOCIAL_COMMUNITY = 1; // 社群
}

enum SceneStreamOpt{
  SCENE_STREAM_OPT_UNSPECIFIED = 0;
  SCENE_STREAM_OPT_REQUIRED = 1; // 必选
  SCENE_STREAM_OPT_OPTIONAL = 2; // 可选
}
message SceneStream {
  string stream_id = 1; // 流id
  string short_name = 2; // 短文案 “讨论”
  string complete_text= 3; // 完整的文案 “「社团名」「品类类型短文案」的讨论”
  string logo = 4;
  uint32 stream_type = 5; // 流类型 SceneStreamType
  uint32 opt = 6; // 选项 SceneStreamOpt
  string category_type_simple_desc = 7; // 临时用 “厂牌”
}

// 帖子
message SceneFeedPost {
  NonPublicPostInfo post = 1;
  TopComment top_comment=2;
}

// SceneFeed
message SceneFeed {

  oneof feed_data {
    SceneFeedPost scene_post = 1;
  }

  string feed_id = 30;

}

message LastFeedInfo {
  uint64 time = 1;
  string id = 2;//userid与postid的结合类型
  double score = 3;
}

message NonPublicNewFeedsLoadMore {
  string feeds_name = 1;
  bytes last_feed_info = 2; // LastFeedInfo
  uint32 last_page = 3;
  uint32 last_count = 4;

  bool following_feed_empty = 10; //关注流是否未空
  bool request_recommend_feed = 11;
}


// 帖子详情
message NonPublicPostInfo {
  string post_id = 1;
  CommonUserUGCInfo common_user_info = 2;
  oneof extend_user_info{
    SocialCommunityUserUGCInfo user_info = 3; // 社群用户扩展信息
  }
  oneof post_extend_info{
    PostExtendSocialCommunity social_community_extend_info = 50; // 社群帖子扩展信息
  }
  string title = 100; // 标题
  repeated NonPublicRichTextElement content_list = 101; // 富文本内容
  uint64 post_time = 102; // 发帖时间, unix second
  uint64 post_update_time = 103; // 帖子更新时间, unix second
  uint32 comment_count = 104;
  uint32 attitude_count = 105;
  uint32 view_count = 106; // 浏览量
  uint32 my_attitude = 107; // NonPublicAttitudeType
  bool is_deleted = 108; // 是否被删除
  uint32 share_count = 109; // 分享数
  bool had_favoured = 110; //是否收藏
  bool is_sticky = 111; //  true：置顶

  uint32 abnormal_state = 112; // 异常状态判断 NonPublicAbnormalStatus

  uint32 post_source = 113; // 主要用于新版推荐流 区分帖子来源  见PostSource, 不确定旧版是否兼容, 还是用是uint32吧

  uint32 privacy = 114; // AttachmentDownloadPrivacy
  uint32 page_source = 115;     // 吉总说，给客户端占坑用，表示服务端不写页面来源

  // TT5.4.2
  uint32 post_privacy_policy = 116; // 隐私策略 // NonPublicPostPrivacyPolicy

  //TT5.4.3 发帖来源
  uint32 origin = 117; // NonPublicPostOrigin 发帖来源

  enum PostStatus {
    POST_STATUS_UNSPECIFIED = 0; // SUC 审核通过
    POST_STATUS_SUSPICIOUS = 1; // 审核中
    POST_STATUS_FAIL = 2; // 审核失败
  }
  uint32 post_status = 118;  // PostStatus 用于个人流针对用户本人发的帖子增加标注
  uint32 post_machine_status = 119;  //用于个人流针对用户本人发的帖子机器审核未过时表明发送失败 NonPublicPostMachineStatus
  string label_remind = 123;

  // ip 归属地
  string ip_location = 124;
  string force_insert_tag=125;  //帖子强插标签
  uint32 operate_permission=126; // 是否有权限发布 ContentStreamPermission 位运算 0001 0010 0100...，0:无权限

}

// 角标信息
enum CornerType{
  CORNER_TYPE_UNSPECIFIED = 0;
  CORNER_TYPE_CATEGORY = 1; // 品类圈
}

message CornerInfo{
  string id = 1;
  uint32 corner_type = 2; // CornerType
  string text = 3; // 文字
  string icon_url = 4; // icon
}

// 社群信息
message PostExtendSocialCommunity{
  string social_community_id = 1;
  string name = 2;
  string logo = 3;
  repeated CornerInfo corner_list = 4; // 品类圈角标
  string category_type_simple_desc=5;
}

// 私域ugc中的用户信息
message CommonUserUGCInfo {
  uint32 uid = 1; // id
  string account = 2; // 账号
  string alias = 3; // 数字账号
  string nickname = 4; // 昵称
  uint32 gender = 5; // 性别
  string signature = 6; // 个性签名
  string face_md5 = 7; // 头像id, 用于拼接头像下载链接
  bool following = 8; // 是否关注中
  bool follow_me = 9; // 是否关注了“我”
  uint32 online = 10; // 是否在线 0:不在线 1:在线
}

// 社群ugc中的用户信息
message SocialCommunityUserUGCInfo {
  string social_community_id = 1;
  string role_text = 2; // 身份名称
  repeated string text_color = 3;
  repeated string background_color = 4;
}

message SocialCommunityInfo{
  string social_community_id = 1;
  string social_community_name=2;
  string category_simple_text=3;
  string category_name=4;
}

enum PublishTextStyleType{
  PUBLISH_TEXT_STYLE_TYPE_UNSPECIFIED = 0;
  PUBLISH_TEXT_STYLE_TYPE_ROLE = 1; // 身份样式
  PUBLISH_TEXT_STYLE_TYPE_CHANNEL = 2; // 频道样式
}

// 发布器的社群信息（用户身份）
message SocialCommunityPublishOpt{
  uint32 text_style_type = 1; // 左下角文案的样式 PublishTextStyleType
  string prefix_text = 2; // 前面文案 “你当前在”
  string logo = 3;
  string suffix_text = 4; // 后面文案 “社团名」「品类类型短文案」的讨论” or "ACTA厂牌制作人"
  int32 remain_social_community_notice_times=5; //剩余的社群通知次数
  int32  post_can_at_social_community_limit=6;  //帖子最多可at多少个社群
  bool  is_at_white_user=7;  //是否是@的白名单用户
  string social_community_notice_bold_text=8; //通知加粗文案（部分）
  string social_community_notice_text=9;//通知文案（部分）
}

message NonPublicUploadAttachmentInfo {
  uint32 attachment_type = 1; // NonPublicAttachmentType
  string attachment_key = 2;
  string extra_info = 3; //给客户端自定义玩，虽然以后加也可以
}

message NewAttitudeMsg {
  uint32 attitude = 1; // 表态类型 NonPublicAttitudeType
  NonPublicPostInfo post_object = 2; // 表态所关联的帖子信息
  NonPublicCommentInfo comment_object = 3; // 若该字段有值，则表示被表态的是一个评论
}

message NewCommentMsg {
  NonPublicCommentInfo comment_info = 1; // 被评论的时候返回，评论的信息
  NonPublicPostInfo post_object = 2; // 该评论相关的帖子信息
  NonPublicCommentInfo comment_object = 3; // 被评论的评论的信息
}

message TopComment{
  NonPublicCommentInfo  hot_comment=1; //热门评论
  NonPublicCommentInfo replying_comment = 2; //被评论的评论信息（热门话题专用）如果是一级评论则为空
}

message NonPublicCommentInfo {
  string post_id = 1;
  string conversation_id = 2;
  string comment_id = 3;
  CommonUserUGCInfo  from_user = 4;
  string content = 5;
  repeated NonPublicAttachment attachments = 6;
  uint32 my_attitude = 7; //NonPublicAttitudeType
  CommonUserUGCInfo   to_user = 8;
  uint32 total_sub_comment_count = 9;
  repeated NonPublicCommentInfo sub_comments = 10;
  uint32 attitude_count = 11;
  uint64 create_at = 12; // 发表评论时间
  bool is_deleted = 13; // 是否被删除
  uint32 my_step = 15; // NonPublicStepOn
  uint32 step_type = 16; // NonPublicStepType
  uint32 step_count = 17;
  string ip_location = 18;
  // 社群属性「」
  oneof extend_user_info{
    SocialCommunityUserUGCInfo user_info = 50; // 社群用户扩展信息
  }
  uint32 operate_permission=19; //是否有权限发布 ContentStreamPermission 位运算 0001 0010 0100...，0:无权限

}

// 表态
enum NonPublicAttitudeType {
  NON_PUBLIC_ATTITUDE_TYPE_UNSPECIFIED = 0;  // 未表态
  NON_PUBLIC_ATTITUDE_TYPE_LIKE = 1; // 点赞
  NON_PUBLIC_ATTITUDE_TYPE_ATTITUDE_NONE = 2; //未操作
}

enum NonPublicStepOn {
  NON_PUBLIC_STEP_ON_UNSPECIFIED= 0; // 未表态
  NON_PUBLIC_STEP_ON_STEP = 1; // 踩
  NON_PUBLIC_STEP_ON_NONE = 2; //未操作
}

enum NonPublicStepType {
  NON_PUBLIC_STEP_TYPE_UNSPECIFIED = 0; // 无
  NON_PUBLIC_STEP_TYPE_MUSIC_STEP = 1; // 音乐版本的踩
}


enum ContentStreamPermission{
  CONTENT_STREAM_PERMISSION_UNSPECIFIED = 0;
  CONTENT_STREAM_PERMISSION_READ = 1; // 访问
  CONTENT_STREAM_PERMISSION_PUBLISH = 2; // 发布
  CONTENT_STREAM_PERMISSION_COMMENT = 4; // 评论
  CONTENT_STREAM_PERMISSION_ATTITUDE = 8; // 点赞/diss/respect
  CONTENT_STREAM_PERMISSION_EDIT=16;  //编辑权限
  CONTENT_STREAM_PERMISSION_DELETE=32; //删除权限
}


/******************************************************************************/

// 发帖（私域）
message PostNonPublicPostReq {
  ga.BaseReq base_req = 1;
  string scene_id = 2; // 场景 也用于 group id 例如 “social_community”
  repeated SceneStream scene_stream_list = 3; // 可以写入多个流(全部)
  SceneStream origin_scene_stream = 4; // 发帖时的stream id

  oneof scene_detail {
    SocialCommunityInfo social_community_info = 100;
  }
  string title=5;
  repeated NonPublicRichTextElement content_list = 6; // 富文本内容
  uint32 origin = 7; // NonPublicPostOrigin 发帖来源
}

message PostNonPublicPostResp {
  ga.BaseResp base_resp = 1;
  string post_id = 2; // 帖子id
  string image_token = 3; // upload image token
  string video_token = 4; // upload video token
  repeated string image_keys = 5; // 上传用的key
  repeated string video_keys = 6; // 上传用的key
}

// 发帖详情页扩展信息
message GetNonPublicPostPublishExtendReq {
  ga.BaseReq base_req = 1;
  SceneStream curr_scene_stream = 2; // 当前所在流
  oneof user_opt_info {
      SocialCommunityInfo social_community = 20;
  }
}



message GetNonPublicPostPublishExtendResp {
  ga.BaseResp base_resp = 1;
  SceneStream origin_scene_stream = 2; // 所在流的stream信息(发帖用)
  repeated SceneStream scene_stream_list = 3; // 额外可以发往哪些流
  uint32 has_publish_permission = 4; // 是否有权限发布 ContentStreamPermission 位运算 0001 0010 0100...，0:无权限
  oneof publish_opt_info {
      SocialCommunityPublishOpt social_community_opt = 20;
  }
  repeated   WhiteListSites white_list=5;
}

// 发帖详情页扩展信息
message GetNonPublicEditPostExtendRequest {
  ga.BaseReq base_req = 1;
 string post_id=2;
}



message GetNonPublicEditPostExtendResponse {
  ga.BaseResp base_resp = 1;
  SceneStream origin_scene_stream = 2; // 所在流的stream信息(发帖用)
  uint32 has_publish_permission = 4; // 是否有权限发布 ContentStreamPermission 位运算 0001 0010 0100...，0:无权限
  oneof publish_opt_info {
    SocialCommunityPublishOpt social_community_opt = 5;
  }
  repeated   WhiteListSites white_list=6;
  string social_community_id=7;

}

message  WhiteListSites{
  string show_platform_name = 1;
  string show_platform_icon = 2;
  string realm_name= 3; // 域名
}

// 附件上传完成
message MarkNonPublicPostAttachmentUploadedReq {
  ga.BaseReq base_req = 1;
  string post_id = 2;
  repeated NonPublicUploadAttachmentInfo attachment_list = 3; // 上传完的附件key，注意顺序
  // V3.4.2 - 支持图片评论，如果是上传评论的附件，则带上评论的id
  string comment_id = 4;
  uint32  operate_post_type=5;   //OperateType
  string edit_record_key=6;
}

enum OperatePostType {
  OPERATE_POST_TYPE_UNSPECIFIED=0;
  OPERATE_POST_TYPE_ADD=1;
  OPERATE_POST_TYPE_EDIT=2;
}

message MarkNonPublicPostAttachmentUploadedResp {
  ga.BaseResp base_resp = 1;
}


// 拉流（私域）
message GetNonPublicNewsFeedsReq {
  ga.BaseReq base_req = 1;
  string scene_id = 2; // 也用于group id 例如 “social_community”

  oneof request_type {
    SceneStream scene_stream_req = 50; // 流
  }

  NonPublicNewFeedsLoadMore load_more = 3; // 首次拉取不传, 加载更多时原封不动地填入上一次GetNewsFeedsResp中的load_more字段
  uint32 count = 4; // 拉取数量
  string need_top_post_id = 5; // 需要客户端置顶的帖子id
}

message GetNonPublicNewsFeedsResp {
  ga.BaseResp base_resp = 1;
  repeated SceneFeed feeds = 2; // feed列表, 数量可能会超过请求指定的count
  NonPublicNewFeedsLoadMore load_more = 3; // 下一次加载更多时, 将load_more原封不动地填入请求的load_more中; 如果不包含此字段, 表示已经拉完了
  string group = 4; //feed 类型，后台用于区分feed类型，对应请求的request_type
}

// 获取帖子详情
message GetNonPublicPostReq {
  ga.BaseReq base_req = 1;
  string post_id = 2;
  SceneStream origin_scene_stream = 3; // stream id
  // 社群id
  oneof scene_detail {
    SocialCommunityInfo social_community_info = 30;
  }
}

message GetNonPublicPostResp {
  ga.BaseResp base_resp = 1;
  NonPublicPostInfo post_info = 2;
  uint32 has_publish_permission = 3; // 是否有权限（评论、点赞） ContentStreamPermission 位运算 0001 0010 0100...，0:无权限
}

// 发评论/回复评论
message PostNonPublicCommentReq {
  ga.BaseReq base_req = 1;
  SceneStream origin_scene_stream = 2; // 发评论时的stream id
  string post_id = 3; // 帖子id
  string comment_id = 4; // 评论id：传空即评论帖子，非空则评论“某个评论“
  string conversation_id = 5;
  string content = 6; // 评论内容
  repeated NonPublicAttachment attachments = 7; // 附件类型  DEPRECATED

  // V3.4.2 - 支持图片评论（协议预留支持多图，目前只支持单图）
  // 社群id
  oneof scene_detail {
    SocialCommunityInfo social_community_info = 30;
  }
}

message PostNonPublicCommentResp {
  ga.BaseResp base_resp = 1;
  string post_id = 2;
  string comment_id = 3;
  NonPublicCommentInfo my_comment = 4;

  // V3.4.2 - 上传图片用的token及key（协议预留支持多图，目前只支持单图）
  string image_token = 5; // upload image token
  repeated string image_keys = 6; // 上传用的key
}


//2.4.8 获取评论列表,热门评论
message GetNonPublicCommentListReq {
  ga.BaseReq base_req = 1;
  string post_id = 2;
  // 如果传了，表示拉子评论
  string comment_id = 3;
  string load_more = 4;
  uint32 count = 5;
  bool query_replying_comment = 6; // 拉取子评论时，同时拉取当前评论的信息
  string extra_comment_id = 7;//需要定位强插的评论id，仅首页生效
  string extra_hot_comment_id = 8;//热门评论强插定位id
}

message GetNonPublicCommentListResp {
  ga.BaseResp base_resp = 1;
  string post_id = 2;
  repeated NonPublicCommentInfo comments = 3;
  string load_more = 4;
  repeated TopComment top_comment=5;   //热门评论  只在lodamore为null的时候才会去取，
  // 首次拉取会拉取所有的TopComment+指定数量的normalcomment
  NonPublicCommentInfo replying_comment = 6; // 被评论的评论信息 一级评论
}

//点赞
message NonPublicExpressAttitudeReq {
  ga.BaseReq base_req = 1;
  SceneStream origin_scene_stream = 2; // 点赞时的stream id
  string post_id = 3; //点赞(或者取消)的帖子id，对象是帖子传这个
  string comment_id = 4; //点赞(或者取消)的评论id，对象是评论传这个
  int32  attitude = 5; //传Attitude::NO_ATTITUDE表示取消之前的Attitude
  int32  step_on = 6;
  oneof scene_detail {
    SocialCommunityInfo social_community_info = 7;  //做权限判断
  }
}
message NonPublicExpressAttitudeResp {
  ga.BaseResp base_resp = 1;
  int32   attitude_count=2;
  uint32 has_publish_permission = 3; // 是否有权限发布 ContentStreamPermission 位运算 0001 0010 0100...，0:无权限
  int32   step_count=4;
}

message NonPublicCommentLoadMore {
  string  next_comment_id = 1;

}

message MarkUserStreamRecordReq {
  ga.BaseReq base_req = 1;
  string stream_id = 2;
  string scene_id = 3;
}
message MarkUserStreamRecordResp {
  ga.BaseResp base_resp = 1;
}


message ReportNonPublicPostShareRequest{
  ga.BaseReq base_req = 1;
  string post_id = 2;
}

message ReportNonPublicPostShareResponse{
  ga.BaseResp base_resp = 1;
  uint32 share_count=2;
}

message NonPublicPostShareMsg{
   string post_id=1;
   string social_community_id=2;
   string social_community_name=3;
   string social_community_logo=4;
    string category_simple_text=5;
  repeated  string picture_url=6;
    string title=7;
    string content=8;
   string user_name=9;
    string nick_name=10;
}

message DeleteNonPublicPostRequest{
  ga.BaseReq base_req = 1;
  string post_id = 2;
}

message DeleteNonPublicPostResponse{
  ga.BaseResp base_resp = 1;
}

message DeleteNonPublicCommentRequest {
  ga.BaseReq base_req = 1;
  string comment_id = 2;
}

message DeleteNonPublicCommentResponse {
  ga.BaseResp base_resp = 1;
}

message EditNonPublicPostRequest{
  ga.BaseReq base_req = 1;
  string post_id = 2; // 帖子id
  string title=3;
  repeated NonPublicRichTextElement content_list = 4; // 富文本内容
}

message EditNonPublicPostResponse{
  ga.BaseResp base_resp = 1;
  string post_id = 2; // 帖子id
  string image_token = 3; // upload image token
  string video_token = 4; // upload video token
  repeated string image_keys = 5; // 上传用的key
  repeated string video_keys = 6; // 上传用的key
  string edit_record_key=7;
}


message NonPublicPostIds{
  repeated string post_ids=1;
}

message BatchConversionNonPublicPostRequest{
  ga.BaseReq base_req = 1;
  map<uint32,NonPublicPostIds> non_public_post_id_map=2;
  string operator=3;
}

message BatchConversionNonPublicPostResponse{
  ga.BaseResp base_resp = 1;
  repeated string ban_post_ids=2;
  repeated string unknown_post_ids=3;
}

message ForceConversionPostPushRequest{
  ga.BaseReq base_req = 1;
      string post_id=2;
}

message ForceConversionPostPushResponse{
  ga.BaseResp base_resp = 1;

}

message NonPublicPostVoteOptionRequest {
  ga.BaseReq base_req = 1;
  uint32 uid = 2;
  string post_id = 3;
  string option_id = 4;
  SceneStream origin_scene_stream = 5; // 发评论时的stream id
  string social_community_id = 6;
}

message NonPublicPostVoteOptionResponse {
  ga.BaseResp base_resp = 1;
  NonPublicVote vote = 2;
}

message  NonPublicPostAuditResultPush{
  string post_id=1;         //帖子id
  uint32 audit_result=2;    //审核结果   see    PostStatus
}

