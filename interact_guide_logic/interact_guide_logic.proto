syntax = "proto3";

package ga.interact_guide_logic;

import "ga_base.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/interact-guide-logic";

// const unsigned int CMD_InteractionRemindNotify = 6101; // 互动提醒通知
message InteractionRemindNotifyReq {
  ga.BaseReq base_req = 1;
  // 互动提醒类型
  enum InteractionRemindType {
    INTERACTION_REMIND_TYPE_UNSPECIFIED = 0;
    // 定期互动提醒
    INTERACTION_REMIND_TYPE_REGULAR_INTERACT = 1;
    // 在线低互动提醒
    INTERACTION_REMIND_TYPE_LOW_INTERACT = 2;
  }
  uint32 remind_type = 2;
  // 用户本次触发接口时客户端记录的在线时长，单位秒
  uint32 online_duration = 3;
}

message InteractionRemindNotifyResp {
  ga.BaseResp base_resp = 1;

  message RegularInteractContent {
    // 用户id
    uint32 uid = 1;
    // 用户账号
    string account = 2;
    // 昵称
    string nickname = 3;
    // 是否在线
    bool is_online = 4;
    // 用户性别
    uint32 sex = 5;
    // 头部标题
    string head_text = 6;
    // 头部图标
    string head_icon = 7;
    // 正文标题
    string title = 8;
    // 副标题
    string sub_title = 9;
    // button文案
    string button_text = 10;
    // button图标
    string button_icon = 11;
    // 发送的文本
    string send_text = 12;
    // 弹窗背景图
    string bg_img = 13;
    // 头部标语字体颜色
    string head_text_color = 14;
    // 正文标题字体颜色
    string title_color = 15;
    // 副标题字体颜色
    string sub_title_color = 16;
    // 弹窗的button背景颜色，看错文档命名错了，文案都是默认的白色
    string button_text_color = 17;
  }

  message LowInteractContent {
    // 扩列玩法id
    uint32 chat_tab_id = 1;
    // 搭子玩法id，默认为0，综合频道搭子数据
    uint32 game_pal_tab_id = 2;
    // 待定是否还有半屏搭子快速匹配那个处理数据
  }

  oneof pop_up_content {
    // 定期互动提醒
    RegularInteractContent regular_interact_content = 2;
    // 在线低互动提醒
    LowInteractContent low_interact_content = 3;
  }
}

// FRIEND_RETURN_ONLINE_REMIND_PUSH = 183; // 好友回流上线提醒推送
message FriendReturnOnlineRemindPush {
  // 用户id
  uint32 uid = 1;
  // 用户账号
  string account = 2;
  // 用户昵称
  string nickname = 3;
  // 在线状态
  bool is_online = 4;
  // 标题
  string title = 5;
  // 副标题
  string sub_title = 6;
  // button文案
  string button_text = 7;
  // 发送的文本
  string send_text = 8;
}
