syntax = "proto3";

package ga.muse_ai_play_hub_logic;

import "ga_base.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/muse-ai-play-hub-logic";

// GetRolePlayHomePageRequest 获取角色扮演首页请求
message GetRolePlayHomePageRequest {
  BaseReq base_req = 1;
}

message RolePlayCpFilter {
  string cp_id = 1; // cp_id
  string cp_name = 2;
  repeated MuseRolePlayRoleInfo role_info_list = 3;

}

message RolePlayReceiveCp{
  string card_pic_url = 1; // 卡片图片
}

// GetRolePlayHomePageResponse 获取角色扮演首页响应
message GetRolePlayHomePageResponse {
  BaseResp base_resp = 1;
  string title=2;   //首页标题
  repeated RolePlayCpFilter cp_filter = 3; // cp筛选
  RolePlayReceiveCp receive_cp = 4; // 收到的组CP请求
  bool has_role_card = 5;   //是否已有角色卡
  repeated MuseRolePlayRoleInfo my_role_info=6;
  uint32 creatable_role_card_limit=7; //可创建的角色卡个数
}

message MuseRolePlayRoleInfo{
  string role_id=1;
  string role_name=2;
  uint32 status=3;
}

// GetRolePlayFeedsRequest 获取角色扮演流
message GetRolePlayFeedsRequest {
  BaseReq base_req = 1;
  // 推荐
  message Rcmd{
    string cp_id = 1;
  }
  oneof request_type {
    Rcmd rcmd = 2;
  }
  bool load_more = 3; //  true 加载更多
  uint32 limit = 20;
  double longitude = 21; // 经度
  double latitude = 22; // 纬度
  repeated string not_expose_card_id_list = 23; // 未曝光的卡片ID
}

// RolePlayUserCardInfo 角色扮演推荐用户卡片信息
message RolePlayRcmdUserCardInfo {
  RolePlayUserInfo user_info = 1; // 用户信息
  RolePlayUserCardInfo role_card_info = 2; // 角色卡信息
  string trace_id = 3; // trace_id 透传到客户端
}

// RolePlayUserInfo 角色扮演用户信息
message RolePlayUserInfo{
  uint32 uid = 1;
  string nickname = 2;
  string gender = 3;
  bool is_online = 4;
  uint32 sex=5;    //性别  0 female, 1 male
}

//// RolePlayUserCardUserInfo 角色扮演用户信息
//message RolePlayUserCardUserInfo{
//  uint32 uid = 1;
//  string nickname = 2;
//  string gender = 3;
//  bool is_online = 4;
//}

enum RolePlayUserLikeType {
  ROLE_PLAY_USER_LIKE_TYPE_UNSPECIFIED=0;// 未指定
  ROLE_PLAY_USER_LIKE_TYPE_NEXT=1;// 下一个
  ROLE_PLAY_USER_LIKE_TYPE_CP=2; // 组CP
  ROLE_PLAY_USER_LIKE_TYPE_UNLIKE=3;  //不喜欢
}

// RolePlayUserCardCardInfo 角色扮演卡片信息
message RolePlayUserCardInfo{
  string role_card_id = 1; // 角色卡id
  string cp_name = 2; // cp名称
  string role_name = 3; // 角色名称
  repeated string like_color=4; //点赞颜色
  string like_label = 5; // 点赞标签
  string introduce = 6; // 介绍
  repeated string pic_url_list = 7; // 图片url列表
  string distance_text = 8; // 距离:单位米
  string role_id=9;
  string cp_id=10;

}

enum RolePlayUserCardStatus{
  ROLE_PLAY_USER_CARD_STATUS_UNSPECIFIED=0; // 未指定
  ROLE_PLAY_USER_CARD_STATUS_NORMAL=1; // 正常
  ROLE_PLAY_USER_CARD_STATUS_DELETED=2; // 删除
  ROLE_PLAY_USER_CARD_STATUS_INIT=3; // 初始化
  ROLE_PLAY_USER_CARD_STATUS_SUBMIT=4; // 提交
  ROLE_PLAY_USER_CARD_STATUS_AUDIT_FAIL=5; // 审核失败
}

// GetRolePlayFeedsResponse 获取角色扮演流响应
message GetRolePlayFeedsResponse {
  BaseResp base_resp = 1;
  repeated RolePlayRcmdUserCardInfo user_card_info_list = 2; // 用户卡片信息列表
  bool bottom_reached = 3; // 是否到底
}

// RolePlayUserLikeRequest 用户卡片下一个/组CP请求
message RolePlayUserLikeRequest {
  BaseReq base_req = 1;
  string role_card_id = 2; // 角色卡id
  uint32 like_type = 3; // RolePlayUserLikeType
}

// RolePlayUserLikeResponse 用户卡片下一个/组CP响应
message RolePlayUserLikeResponse {
  BaseResp base_resp = 1;
  bool is_create_new_role_card=2;
  string cp_id=3;
  string role_id=4;
  string role_name=5;
  // 最好有一个bool，告诉客户端要不要去创建角色卡
}

message  GetRolePlayReceiveCpListRequest{
  BaseReq base_req = 1;
  string role_card_id =2;
  int32 offset=3;
  int32 limit=4;
  //加载更多的最后一条id
  string last_offset_id=5;

}

message  GetRolePlayReceiveCpListResponse{
  BaseResp base_resp = 1;
  repeated  RolePlayReceiveCpInfo receive_cp_info_list = 2;  //请求匹配列表
  int32 offset=3;
  string last_offset_id=4;
}


message RolePlayReceiveCpInfo{
  RolePlayUserInfo user_info = 1; // 用户信息
  RolePlayUserCardInfo role_card_info = 2; // 角色卡信息

}



// CPStarTag 点亮CP关系标识
message RolePlayCPStarTag {
  string cp_name = 1; // cp名称
  string target_account = 2; // 对方用户account
  string my_account = 3; // 我的用户account
  uint32 star_progress = 4; // 点亮进度 0-100
  string target_role_name = 5; // 对方角色名称
  string target_role_introduce = 6; // 对方角色介绍
  string target_role_pic_url = 7; // 对方角色图片
}

// ContinueGuideChatNotify 持续引导聊天弹窗
message ContinueGuideChatNotify {
  string target_account = 1; // 对方用户account
  string target_nickname = 2; // 对方用户昵称
  uint32 target_gender = 3; // 对方用户性别
  bool is_online = 4; // 是否在线
  string greeting_icon_url = 5; // 问候语icon
  string greeting_content = 6; // 问候语内容
  string greeting_title = 7; // 问候语标题
}



//在线推送
//成功匹配推送 3.2.2
message SuccessMatchCPMsg{
  repeated UserFirstPicture  role_first_picture_list=1; //角色首图
  string title=2;    //主标题
  string sub_title=3;  //副标题
  repeated string user_account_list=4; //对方username
}

message UserFirstPicture {
  string role_first_picture=1; //角色首图
  string user_account=2;
}


//请求发送成功推送 3.2.2
message MatchCpRequestSentSuccessfullyMsg{
  RolePlayUserInfo from_user=1; //用户信息
  string role_first_picture=2; //角色首图
  string content=3;   //
}

//接收组cp请求推送 3.2.2
message ReceviedMatchCpRequestMsg{
  RolePlayUserInfo from_user=1;  //用户信息
  string role_first_picture=2; //角色首图
  string title=3;    //标题
  string source_text=4;     //来源
  string role_card_id=5;    //角色卡id
}


/*角色扮演卡片信息*/
enum MuseRolePlayImMsgType{
  MUSE_ROLE_PLAY_IM_MSG_TYPE_UNSPECIFIED = 0;
  MUSE_ROLE_PLAY_IM_MSG_TYPE_CARD=1; //  RolePlayCard
  MUSE_ROLE_PLAY_IM_MSG_TYPE_SYSTEM=2; //系统消息  ---MuseRolePlayRichTextIMMsg
  MUSE_ROLE_PLAY_IM_MSG_TYPE_SOURCE=3; //来源消息类型
  MUSE_ROLE_PLAY_IM_MSG_TYPE_PLOT_CARD=4; //剧情卡消息   --MuseRolePlayPlotCard
}
message RolePlayCard{
  string account=1;  //对方头像
  RolePlayUserCardInfo  target_role_card_info =2;   //对方角色卡信息
}


message MuseRolePlayIMMsgBase{
  uint32 msg_type = 1; // MuseRolePlayImMsgType
  bool is_present_out = 2; // 是否显示在消息列表
}

message MuseRolePlayRichTextIMMsg{
  repeated MuseRolePlayRichTextElement msg_content = 1; // 消息内容
}
message MuseRolePlayRichTextElement{
  oneof content{
    MuseRolePlayRichTextWord text = 1; // 文本
    MuseRolePlayRichTextImg img = 2; // 图片
  }
}

message MuseRolePlayRichTextWord{
  string text = 1; // 文本内容
  uint32 text_type = 2; // 文本类型  MuseRolePlayRichTextWordType
}

message MuseRolePlayRichTextImg{
  string url = 1; // 图片url
  uint32 url_type = 2; // 图片类型  MuseRolePlayRichTextImgType
}



// AI玩法IM消息
message MuseRolePlayIMMsg{
  MuseRolePlayIMMsgBase base = 1;
  bytes msg_content = 2; // 消息内容
}
/*角色扮演卡片信息*/

/*匹配成功/退出推送，状态改变*/
message AIRolePlayChangeStatusPush{
  string from_account=1;
  string target_account=2;
  uint32 status = 3; // 状态 AIRolePlayStatus
  string chat_bg_url = 4; // 聊天背景

}

enum AIRolePlayStatus{
  AI_ROLE_PLAY_STATUS_UNSPECIFIED = 0;
  AI_ROLE_PLAY_STATUS_NORMAL = 1; // 正常
  AI_ROLE_PLAY_STATUS_QUIT = 2; // 退出
}

/*匹配成功/退出推送，状态改变*/


//我的角色卡变更推送
message  MyRoleCardChangePush{
  repeated MuseRolePlayRoleInfo my_role_info=1;
}


message RolePlayCPBaseConfig{
  string chat_bg_url = 1; // 聊天背景

}


message MuseRolePlayPlotCard{
  //标题
  string title=1;
  //背景图url
  string background_url=2;
  //我的角色
  string my_role_name=3;
  //ta的角色
  string  to_role_name=4;
  //剧情
  string plot_content=5;
  //我的目标
  string my_target=6;
}

