syntax = "proto3";

package ga.rhythm;

import "ga_base.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/rhythm";

/* 律动现场 开关 管理 */
message GetRhythmSwitchReq {
    ga.BaseReq base_req = 1;
    uint32 uid = 2;
    uint32 switch_type = 3; /* RhythmSwitchType */
    uint32 channel_id = 4;
}
enum RhythmSwitchType{
    UnknownSwitchType = 0;
    VoteAndFollow = 1; /* 投票后顺便关注Ta */
    UserDefinedVotePk = 2; /* 自定义投票pk开关 */
}
enum RhythmSwitchStatus{
    UnknownSwitchStatus = 0;
    Open = 1;
    Close = 2;
}
message GetRhythmSwitchResp {
    ga.BaseResp base_resp = 1;
    uint32 switch_status = 2; /* RhythmSwitchStatus */
    bool is_visible = 3; /* 是否展示 true 展示 */
}

message SetRhythmSwitchReq {
    ga.BaseReq base_req = 1;
    uint32 uid = 2;
    uint32 switch_type = 3; /* RhythmSwitchType */
    uint32 switch_status = 4; /* RhythmSwitchStatus */
}
message SetRhythmSwitchResp {
    ga.BaseResp base_resp = 1;
}

/* 厂牌 */
message GetBrandListReq{
    ga.BaseReq base_req = 1;
}
message BrandListInfo{
    string brand_id = 1;
    string brand_name = 2;
    uint32 follower_num = 4; // 粉丝数
}
message BrandCategoryListInfo{
    repeated BrandListInfo info = 1;
    string character = 2;   // 排序的首字母，前端按我们返回的顺序展示
}
message GetBrandListResp{
    ga.BaseResp base_resp = 1;
    repeated BrandListInfo top_brand_list = 2;
    repeated BrandCategoryListInfo category_brand_list = 3;
    string url = 4;     // 入驻的url 可为短链或者是问卷星的url
    // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
    string IntroText = 5; /* 入口问号说明文案 */
    string out_share_pic_url = 6; /* 外面分享图片url */
    string my_brand=7;  //我的厂牌id
}

enum BrandMemberRole{
    UnknownRole = 0;
    TeamLeader = 1;
    Normal = 2;
    ViceCaption=3;   //副队长
    Producer=4;   //制作人
}
message GetBrandInfoReq{
    ga.BaseReq base_req = 1;
    string brand_id = 2; // 厂牌ID
}
message BrandMemberInfo{
    uint32 uid = 1;
    string account = 2; // 用户头像
    string nick_name = 3; // 用户昵称
    uint32 follower_num = 4;    // 粉丝数
    uint32 sex = 5; // 性别
    string intro = 6; // 成员介绍
    uint32 role = 7; /* 角色 BrandMemberRole */
}
message BrandSimpleInfo{
    string brand_id = 1;
    string brand_name = 2;
    string intro = 3; // 厂牌介绍
    uint32 follower_num = 4;
    repeated BrandMemberInfo member_list = 5;
    repeated PhotoAlbumKeyURL photo_list = 6; /* 厂牌相册 */
//    string url = 7;
}
message GetBrandInfoResp{
    ga.BaseResp base_resp = 1;
    BrandSimpleInfo brand_info = 2;
    uint32 role = 3; /* BrandMemberRole */
    string in_share_pic_url = 4; /* 里面分享图片url */
    repeated string robot_url=5;//机器人头像
    string topic_id=6;//当前厂牌话题的id
    uint32 view_count=7;//话题热度
    int32 brand_integral=8;//厂牌积分
}

// 当要修改厂牌成员介绍时，uid!=0，且要带上brand_id
message UpdateBrandInfoReq{
    ga.BaseReq base_req = 1;
    string brand_id = 2; /* 修改厂牌介绍 */
    uint32 uid = 3; /* 修改厂牌用户*/
    string intro = 4;
}
message UpdateBrandInfoResp{
    ga.BaseResp base_resp = 1;
}

/* 用户厂牌 */
message UserBrandInfo{
    string id = 1; /* 厂牌ID */
    string name = 2; /* 厂牌名 */
    string type = 3; /* 厂牌类型 */
    string attr = 4; /* 厂牌属性名 */
    string attr_info = 5; /* 厂牌属性信息 */
    string role = 6; /* 厂牌身份 */
}
message GetUserBrandInfoReq{
    ga.BaseReq base_req = 1;
    uint32 uid = 2;
}
message GetUserBrandInfoResp{
    ga.BaseResp base_resp = 1;
    UserBrandInfo info = 2;
}



/* ========= 相册 ========= */
enum PhotoAlbumType{
    UnknownAlbum = 0;
    BrandAlbum = 1;
}
//message GetCommonPhotoAlbumReq{
//    ga.BaseReq base_req = 1;
//    string id = 2;
//    uint32 album_type = 3; /* PhotoAlbumType */
//}
message PhotoAlbumKeyURL{
    string key = 1;
    string url = 2;
}
//message GetCommonPhotoAlbumResp{
//    ga.BaseResp base_resp = 1;
//    string id = 2;
//    repeated PhotoAlbumKeyURL photo_list = 4;
//}

message UpdateCommonPhotoAlbumReq {
    BaseReq base_req = 1;
    string id = 2;
    repeated string img_key_list = 4; /* 新版obs，全量key */
}
message UpdateCommonPhotoAlbumResp {
    BaseResp base_resp = 1;
}

// 音乐专区
message GetMusicZoneTemplateReq {
    BaseReq base_req = 1;
    string id = 2;  // 音乐专区分类ID
}
message GetMusicZoneTemplateResp {
    BaseResp base_resp = 1;
    string zone_name = 2;  // 音乐专区栏目名（头部区域）
    uint32 banner_id = 3; // banner & 金刚区ID
    string tab_req_str = 4; // tab分类区域
    string room_url = 5; // 我的房间跳转短链，后端可根据客户端版本号，返回空短链
    string room_tab_name = 6; // 我的房间名字
    string find_room_name = 7; // 帮你找房名字
    string find_room_id = 8; // 帮你找房传送给后端的请求id
    bool   star_flag=9;//小纸条标志
    string  background_image_url=10;//背景图片
    ColorConfiguration   color_configuration=11;//专区色调
}
enum ColorConfiguration{
    ColorWhite = 0;
    ColorBlack = 1;
}
// 可乐玩法
// 用户新增可乐
message AddCokeReq {
    BaseReq base_req = 1;
    uint32 channel_id = 2;
    uint32 act_id = 3;
    uint32 coke_num = 4;
}

message AddCokeResp {
    BaseResp base_resp = 1;
    uint32 left_coke = 2;
}

// 用户赠送可乐到麦上用户
message SendCokeReq {
    BaseReq base_req = 1;
    uint32 channel_id = 2;
    uint32 act_id = 3;
    map<uint32, uint32> map_uid_gain_coke = 4;
}

message SendCokeResp {
    BaseResp base_resp = 1;
    uint32 left_coke = 2;
}

message SendCokeToUserNotify{
    uint32 channel_id = 1;
    uint32 from_uid = 2;
    string from_nickname = 3;
    string from_account = 4;
    uint32 to_uid = 5;
    string to_nickname = 6;
    string to_account = 7;
    uint32 coke_cnt = 8;
}


/* ------------------ 自定义投票 ------------------ */
message UserDefinedVotePkOptionInfo{
    string option_name = 1;
}

enum UserDefinedVotePKType{
    UnknownType = 0;
    /* 和 channelpk 不重复 */
    StringOptionType = 100; /* 字符串投票类型 */
}

message UserDefinedVotePKStartReq{
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2;
    uint32 duration_min = 3; //持续分钟
    uint32 pk_type = 4; /* UserDefinedVotePKType */
    repeated UserDefinedVotePkOptionInfo option_list = 5; /* 自定义投票选项 客户端检查选项不重复 */
    uint32 vote_cnt = 6; //每个观众的票数
    string pk_name = 7; //PK 名
}

message UserDefinedVotePKStartResp
{
    ga.BaseResp base_resp = 1;
    uint32 channel_id = 2;
    uint32 start_timestamp = 3; //服务端时间，PK开始时间戳  channel_id和start_timestamp唯一确定一个PK对象
    repeated UserDefinedOptInfo option_list = 4;
}

message UserDefinedCompetitor
{
    UserDefinedOptInfo opt_info = 1;
    uint32 vote = 2;
}

message UserDefinedOptInfo{
    uint32 opt_id = 1;
    UserDefinedVotePkOptionInfo opt_info = 2;
}

message UserDefinedVotePKInfo
{
    uint32 uid = 1; //发起玩家
    uint32 channel_id = 2;
    uint32 duration_min = 3;
    uint32 start_timestamp  = 4; //开始时间戳
    uint32 vote_cnt = 5; //如果是投票类型的情况，每个观众的票数
    string pk_name = 6; //PK 名字
//    repeated UserDefinedOptInfo option_list = 7;
}

/* 全量信息 */
message UserDefinedPKRankInfo{
    UserDefinedVotePKInfo info = 1;
    repeated UserDefinedCompetitor competitor_list = 2;
}

message GetUserDefinedVotePKReq{
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2;
}

message GetUserDefinedVotePKResp{
    ga.BaseResp base_resp = 1;
    uint32 left_vote = 2; //剩余票数
    UserDefinedPKRankInfo pk_info = 3; //PK全量信息
    StayAddTicketInfo add_ticket_info = 4; /* 加票停留奖励 */
}

/* 投票 */
message UserDefinedVoteReq{
    ga.BaseReq base_req = 1;
    uint32 opt_id = 2; //目标选项
    uint32 vote_cnt = 3; //投票数量
    uint32 channel_id = 4;
    uint32 start_timestamp  = 5; //开始时间戳
}

message UserDefinedVoteResp{
    ga.BaseResp base_resp = 1;
    uint32 left_vote = 2; //剩余票数
    uint32 remain_extra_vote=3;//剩余额外票数
}

message UserDefinedVotePKCancelReq{
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2;
    uint32 start_timestamp = 3;
}

message UserDefinedVotePKCancelResp{
    ga.BaseResp base_resp = 1;
}

// 自定义PK全量PUSH信息
message UserDefinedVotePKNotify{
    enum UDVotePKStatus {
        UserDefinedStatusUnknown = 0;
        UserDefinedStatusStart = 1;   // PK 开始
        UserDefinedStatusInPK = 2;     // PK 进行中
        UserDefinedStatusTimeout = 3; // PK 时间到结束
        UserDefinedStatusCancel = 4;  // PK 被取消
    }
    UserDefinedPKRankInfo pk_info = 1; // 全量信息
    uint32 notify_status = 2; // UDVotePKStatus
    // 操作者数据 仅在PK 开始和被取消时 需要填写
    uint32 op_uid = 3;           // 操作者UID
    string op_nickname = 4;      // 操作者昵称
    string op_account = 5;
}

/* 加票推送 */
message AddVotePkUserTicketNotify{
    uint32 channel_id = 1;
    uint32 start_timestamp = 2; //开始时间戳
    uint32 ticket_cnt = 3; /* 额外票数 */
    uint32 current_total_ticket = 4; /* 当前剩余总票数 */
    uint32 push_version = 5; /* push时间戳*/
    uint32 user_add_ticket_cnt = 6; /* 用户当前累积获得的总加票数 */
    uint32 user_remain_ticket_cnt = 7; /* 用户剩余可投额外票总数 */
    bool is_end_act = 8; /* 是否结束活动，客户端弹窗提示（乐窝活动未上架/未到预热时间/开关否到否为false，其余为true） */
}



/* ============ AI 说唱 ============ */
message VoiceWatermarkResultNotify{
    uint32 uid = 1;
    string stage_name = 2; /* 艺名 */
    string voice_url = 3;
    bool is_valid = 4;
}

/* 中台返回的声音质量分数&干音音频 */
message VoicePartialQualityNotify{
    uint32 uid = 1;
    uint32 timestamp = 2; /* 客户端上报音频的时间戳 */
    double voice_start_second = 3;
    double voice_end_second = 4;
    uint32 score = 5;
    bool pass = 6; /* 是否通过 */
    repeated string text = 7; /* 文案 */
    uint32 client_report_timestamp = 8;    // 客户端自身发送时的时间戳，作为客户端唯一凭证
    uint32 ai_type = 9; /* ai玩法类型  AIType */
}

message RapperPostTopicInfo{
    string topic_name = 1;
    string topic_id = 2;
}

/* 中台返回的说唱音频 */
message RapperVoiceNotify{
    uint32 uid = 1;
    uint32 timestamp = 2; /* 客户端上报音频的时间戳 */
    string voice_url = 3; /* 说唱音频 */
    repeated string nickname_list = 4; /* 花名 */
    string bg_video_url = 5; /* 背景视频url */
    string bg_video_md5 = 6; /* 背景视频md5 */
    string end_video_url = 7; /* 背景视频url */
    string end_video_md5 = 8; /* 背景视频md5 */
    repeated RapperPostTopicInfo topic_list = 9; /* 发帖带的话题 */
    string stage_name = 10; /* 艺名 */
    string music_producer = 11; /* 制作人 */
    string music_director = 12 ; /* 音乐指导 */
    uint32 client_report_timestamp = 13;    // 客户端自身发送时的时间戳，作为客户端唯一凭证
    string market_url=14; //根据不同marketid返回的马甲包链接
    string bg_origin_vedio_url = 15; /* 背景视频原视频url */
    string bg_video_first_frame_url = 16;   // 背景首帧 用于分享的图片
    uint32 ai_type = 17; /* ai玩法类型  AIType */
}

/* 房间推送 是否开启厂牌专属房间背景及话题入口 也用于请求的返回 */
message BrandChannelBGTopicNotify{
    uint32 channel_id = 1;
    bool switch_channel_bg = 2;
    string under_pic_url = 3; /* 底层背景图 */
    string under_pic_md5 = 4;
    string stage_pic_url = 5; /* 舞台背景图*/
    string stage_pic_md5 = 6;
    uint32 pic_valid_begin_ts = 7; /* 房间背景图生效开始时间 */
    uint32 pic_valid_end_ts = 8; /* 房间背景图生效结束时间 */
    string pc_under_pic_url = 9; /* 底层背景图 */
    string pc_under_pic_md5 = 10;
    string pc_stage_pic_url = 11; /* 舞台背景图*/
    string pc_stage_pic_md5 = 12;
    string topic_id = 13; /*话题ID*/
    string topic_name=14;/*话题名*/
}

/* 设置声音水印 */
message SetVoiceWatermarkReq {
    ga.BaseReq base_req = 1;
    uint32 uid = 2;
    string stage_name = 3; /* 艺名 */
    string voice_composed_key = 4; /* 合成的 */
}
message SetVoiceWatermarkResp {
    ga.BaseResp base_resp = 1;
}

message GetVoiceWatermarkReq {
    ga.BaseReq base_req = 1;
    uint32 uid = 2;
}
message GetVoiceWatermarkResp {
    ga.BaseResp base_resp = 1;
    string stage_name = 2; /* 艺名 */
    string voice_url = 3;
}

message DelVoiceWatermarkReq {
    ga.BaseReq base_req = 1;
    uint32 uid = 2;
}
message DelVoiceWatermarkResp {
    ga.BaseResp base_resp = 1;
}

//message VideoURL{
//    string bg_video_url = 1; /* 背景视频url */
//    string bg_video_md5 = 2; /* 背景视频md5 */
//    string bg_origin_video_url = 3; /* 背景视频原视频url */
//    string bg_video_first_frame_url = 4;   // 背景首帧 用于分享的图片
//}
//
///* 透传信息 */
//message AIExtraInfo{
//    VideoURL video_url = 1;
//}

/* 用户念的音频 */
message SendReadVoiceReq {
    ga.BaseReq base_req = 1;
    uint32 uid = 2;
    string song_id = 3;
    string voice_key = 4;
    uint32 client_report_timestamp = 5;    // 客户端自身发送时的时间戳，作为客户端唯一凭证
    uint32 ai_type = 6; /* ai玩法类型  AIType */
}
message SendReadVoiceResp {
    ga.BaseResp base_resp = 1;
    uint32 timestamp = 2; /* 客户端上报音频的时间戳 */
    uint32 client_report_timestamp = 3;    // 客户端自身发送时的时间戳，作为客户端唯一凭证
}

/* 等级 */
message LevelConfig{
    string level_name = 1;
    uint32 min_num = 2;
    uint32 max_num = 3;
}

message UserLevel{
    uint32 uid = 1;
    string account = 2;
    LevelConfig level = 3;
    uint32 point = 4;
}

message GetUserAIRapperLevelReq{
    ga.BaseReq base_req = 1;
}
message GetUserAIRapperLevelResp {
    ga.BaseResp base_resp = 1;
    UserLevel user_level = 2;
}

/* 用户发完帖子后将帖子id发给服务端 */
message PostAIRapperPostReq{
    ga.BaseReq base_req = 1;
    uint32 uid = 2;
    string post_id = 3;
    uint32 ai_type = 4; /* ai玩法类型  AIType */
}
message PostAIRapperPostResp {
    ga.BaseResp base_resp = 1;
}
/* 获取tab */
message GetTabInfoReq{
    ga.BaseReq base_req = 1;
    uint32 ai_type = 2; /* ai玩法类型  AIType */
}
message TabAttribute{
    string tab_id=1;      //tab唯一标识id
    string tab_name=2;    //分类名
}
message GetTabInfoResp{
    ga.BaseResp base_resp = 1;
    repeated TabAttribute tab_info=2;     //分类信息
    LyricInfo newbie_song = 3; /* 新手引导的歌 */
}

/* rapper首页歌词列表 */
message GetLyricInfoReq{
    ga.BaseReq base_req = 1;
    string tab_id = 2;
    uint32 limit = 3;        //分页限制  默认最多50
    uint32 offset = 4;       //分页偏移量
    string song_id=5;        // 获取同款歌曲首次带上此字段
    uint32 ai_type = 6; /* ai玩法类型  AIType */
}

/* AI 外语 */
enum AIType{
    AiRapperType = 0; /* ai说唱 */
    AiForeignLanguageType = 1; /* ai外语 */
}

/* 赛博世界首页 */
message GetCyberWorldHomeReq{
    ga.BaseReq base_req = 1;
}
message CyberWorldModel{
    string pic_url = 1; /* 背景图 */
    string title = 2; /* 标题 */
    string sub_title = 3; /* 副标题 */
    string jump_url = 4; /* 跳转短链 */
    uint32 ai_type = 5; /* ai玩法类型  AIType */
}
message GetCyberWorldHomeResp{
    ga.BaseResp base_resp = 1;
    repeated CyberWorldModel model_list = 2; /* 内部模块列表 */
}

/* ai外语首页歌曲卡片的视频+按钮链接 */
message CardBGVideo{
    string home_pic_url = 1;
    string button_url = 2;
}

message LyricInfo{
    string song_id = 1;
    string song_name = 2;                 //歌曲名
    string rap_name = 3;                  //演唱者
    repeated string lyric_content = 4;    //歌词
    string lyric_url = 5;                 //歌词url
    string file_md5 = 6;                /* 上传文件的md5 */
    string level_name = 7;                //解锁等级名称
    uint32 level_score = 8;               //解锁所需数值
    bool is_locked = 9;                     //用户是否解锁歌曲  true--未解锁 ，false--解锁
    string excerpt_content_url = 10;       // 精选lrc、高潮mp3、用户录音样本
    string excerpt_content_md5 = 11;
    uint64 friend_circle_heat = 12;        // 朋友圈人气
    string producer_nick_name = 13;         // 制作人昵称
    string new_lyric_url = 14;              // 仅含预览页歌词的url
    string new_lyric_md5 = 15;
    string bg_video_url = 16;               // 背景视频url
    string bg_video_md5 = 17;
    uint64 friend_circle_heat_datum_line = 18; // 朋友圈热度基准线
    uint32 ai_type = 19; /* ai玩法类型  AIType */
    CardBGVideo card_bg_video_url = 20; /* ai外语首页歌曲卡片的视频+按钮链接 */
}
message GetLyricInfoResp{
    ga.BaseResp base_resp = 1;
    repeated LyricInfo lyric_info = 2;   //歌词信息
    bool has_more = 3; /* true 可加载更多 */
    bool is_open_original_sound=4 ;  /*是否开启听原声         true 开启   false 关闭   */
}
/*------------------------合成，曝光，分享的聚合接口-------------------------------------------*/
/*上报类型枚举*/
enum AggregationChoose{
    EXposure=0; //曝光
    Compose=1;  //合成
    Share=2;    //分享
}
message AggregationInfo{
    AggregationChoose aggregation_choose=1;
    string song_id=2;
    uint32 number=3;
}
message AggregationInfoReq{
    ga.BaseReq base_req = 1;
    repeated AggregationInfo aggregation_info=2;
    uint32 ai_type = 3; /* ai玩法类型  AIType */
}
message AggregationInfoResp{
    ga.BaseResp base_resp = 1;
}
/*------------------------合成，曝光，分享的聚合接口-------------------------------------------*/


/*获取用户体验小问卷*/
message GetQuestionnaireReq{
    ga.BaseReq base_req = 1;
    string song_id=2;    //歌曲的唯一标识
    uint32 ai_type = 3; /* ai玩法类型  AIType */
}
message GetQuestionnaireResp{
    ga.BaseResp base_resp = 1;
    bool display_flag=2;//是否战展示小问卷标识  true展示，false不展示
    string topic=3;     //标题
    repeated string options=4;  //不定选项
    string feedback_text=5;   //反馈文案
}

/* ai 说唱三期 分享朋友圈 */
message GetH5UrlWithAICtxReq {
    ga.BaseReq base_req = 1;
    string background_video_url = 2;  // 背景视频
    string voice_url = 3;   // AI合成音频
    string song_name = 4; // 歌曲名
    repeated string lyrics = 5;   // 将lrc变为字符串数组，lrc的每一行是一个字符串
    string song_id = 6; // 歌曲唯一ID
    uint32 ai_type = 7; /* ai玩法类型  AIType */
}

message GetH5UrlWithAICtxResp {
    ga.BaseResp base_resp = 1;
    string h5_url = 2; // 前端链接
    string main_title = 3; // 主标题
    string sub_title = 4; // 副标题
    string main_text=5;   //文案(抖音专用)
    repeated string topic=6;//若干话题(抖音专用)
}
/*专区改造*/
//获取筛选器
message GetMusicBlockFilterReq{
    BaseReq base_req = 1;
    string filter_type = 2;//默认音乐首页
}

message GetMusicBlockFilterResp{
    BaseResp base_resp = 1;
    repeated FilterItem filter_items = 2;
    message FilterItem {
        string title = 1;
        string filter_item_type = 2;//首页Item HOME_FILTER_ITEM,页面房间Item PAGE_FILTER_ITERM,页面帖子 PAGE_POST
        string filter_id = 3;//具体接口的通用参数，后续可能还需要不同类型有不同参数
        repeated FilterSubItem filter_sub_items = 4;
    }

    message FilterSubItem {
        string title = 1;
        string filter_sub_id = 2;//具体接口的通用参数，后续可能还需要不同类型有不同参数
        string filter_sub_item_type = 3;//SUB_ITEM_POST帖子,SUB_ITEM_MUSIC音乐流
    }

    enum FilterItemType {
        HOME_FILTER_ITEM = 0;
        PAGE_FILTER_ITERM = 1;
        PAGE_POST = 2;
    }
}

message GetBrandChannelBGTopicInfoReq{
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2;
}
message GetBrandChannelBGTopicInfoResp{
   ga.BaseResp base_resp = 1;
    BrandChannelBGTopicNotify info = 2;
}

message UpgradeUserAIRapperLevelReq{
    BaseReq base_req = 1;
}

message UpgradeUserAIRapperLevelResp{
    BaseResp base_resp = 1;
}


message RhythmRecommendRoomItem{
    uint32 channel_id = 1;
    string channel_name = 2;
    string channel_owner_account = 3;
    int32 channel_owner_sex = 4;
    uint32 channel_member_count = 5;
    bytes pb_view_data = 6; // see hobby-channel-view_.proto  HobbyChannelViewRap
    string publish_desc = 7; //后台直接拼接分类简称与分区
}
message RhythmGetRecommendRoomReq{
    ga.BaseReq base_req = 1;
}
message RhythmGetRecommendRoomResp{
    ga.BaseResp base_resp = 1;
    repeated RhythmRecommendRoomItem item=2;
    string dynamic_text=3;
}

message RhythmGetPostsReq{
    ga.BaseReq base_req = 1;
}
message RhythmGetPostsResp{
    ga.BaseResp base_resp = 1;
    repeated  RhythmGetPostsItem item=2;
}
message RhythmGetPostsItem {
    string main_title =1;
    string sub_title=2;
    string url_link=3;
    string user_image =4;
    string topic_id=5;

}

/* 停留加票 6.13.0 */
message ReportStayAddTicketReq{
    ga.BaseReq base_req = 1;
    uint32 uid = 2;
    uint32 channel_id = 3;
    uint32 act_id = 4; /* 乐窝活动id 加票奖励按照场次算 */
}
enum StayAddTicketStatus{
    UnknownStatus = 0;
    OnStatus = 1; /* 活动上架，也可理解为：加票生效 */
    OffStatus = 2; /* 活动下架，也可理解为：加票失效  */
}
message StayAddTicketInfo{
    uint32 status = 1; /* StayAddTicketStatus */
    uint32 stay_interval = 2; /* 停留时长 加票 单位：分钟 */
    uint32 act_id = 3; /* 乐窝活动id 加票奖励按照场次算 新覆盖旧 */
    uint32 channel_id = 4;
    uint32 ticket_cnt = 5;  /* 停留加票单次加的票数 */
    uint32 total_add_ticket_cnt = 6; /* 单场活动 单人最多加的票数上限 */
    uint32 user_add_ticket_cnt = 7; /* 用户当前累积获得的总加票数 */
    uint32 user_remain_ticket_cnt = 8; /* 用户剩余可投额外票总数 */
    bool is_end_act = 9; /* 是否结束活动，客户端弹窗提示（乐窝活动未上架/未到预热时间/开关否到否为false，其余为true） */
}
message ReportStayAddTicketResp{
    ga.BaseResp base_resp = 1;
    StayAddTicketInfo info = 2; /* 有可能多场同时 以最新的为准*/
}

message StayAddTicketNotify {
    StayAddTicketInfo info = 2;
}


/*麦位排序相关协议*/

message SetMicSortReq{
    ga.BaseReq base_req = 1;
    uint32 channel_id=2;
    MicSortInfo info=3;
}
message SetMicSortResp{
    ga.BaseResp base_resp = 1;
}
message MicUserInfo{
    uint32 uid=1;
    string nickname=2;
    string account=3;
    uint32 sex=4;
}
message MicSortInfo{
    bool mic_flag=1;
    repeated MicUserInfo user_info=2;
}

message GetMicSortReq{
    ga.BaseReq base_req = 1;
    uint32 channel_id=2;
}

message GetMicSortResp{
    ga.BaseResp base_resp = 1;
    MicSortInfo info=2;
}
message SwitchMicSortReq{
    ga.BaseReq base_req = 1;
    bool   switch_flag=2;    //开启或关闭麦位排序标识   true--开启
    uint32 channel_id=3;
}
message SwitchMicSortResp{
    ga.BaseResp base_resp = 1;
}

/* 讨论话题 强插 */
message GetForceTopicReq{
    ga.BaseReq base_req = 1;
    uint32 uid = 2;
    string tab_name = 3; //当前子tab名称，空代表“全部”
    uint32 tab_type = 4;// tab类型，0默认类型，1同城类型
}
message PostContentProfileInfo{
    string username = 1;
    string post_content = 2; /* 截取一定字数 */
    uint32 attitude_count = 3; /* 点赞数 */
}
message ForceTopicInfo{
    string topic_id = 1;
    string topic_name = 2;
    uint32 topic_type = 3; /* 指定类型 ugc_.proto TopicType */
    uint32 member_cnt = 4; /* 看过人数 */
    repeated PostContentProfileInfo post_list = 5; /* 帖子列表 */
    repeated string username = 6; /* 轮播头像 */
    uint32 position = 7; /* 强插位置 从1开始 */
    repeated string multi_pic_url_list = 8; // 多图
}
message GetForceTopicResp{
    ga.BaseResp base_resp = 1;
    repeated ForceTopicInfo topic_list = 2; /* 轮流话题强插 */
}

/*视频背景上传*/

message GetBackgroundVideoUrlReq{
    ga.BaseReq base_req = 1;
    string video_md5=2;
    string song_id=3;
}
message GetBackgroundVideoUrlResp{
    ga.BaseResp base_resp = 1;
    repeated BackgroundVideoInfo info=2;
}
message BackgroundVideoInfo{
    string video_url=1;
    string video_md5=2;
    string share_cover_pic_url=3;   //获取分享，首帧图片
    string origin_video_url=4;
}


/* Battle-增加伴奏、开启投票功能 */
/* 开启battle */
message SetBattleStartRequest{
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2;
    uint32 duration_min = 3; // 持续分钟 单位分钟
    uint32 type = 4;         // enum ChannelVotePkType PK类型
    repeated uint32 uid_list = 5;     // 参与UID
    uint32 vote_cnt = 6;     // 每个观众的票数
    string pk_name = 7;      // PK名
}
message SetBattleStartResponse{
    ga.BaseResp base_resp = 1;
}

/* 关闭battle */
message CancelBattleStartRequest{
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2;
}
message CancelBattleStartResponse{
    ga.BaseResp base_resp = 1;
}

/* 获取battle */
message GetBattleStartRequest{
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2;
}
message GetBattleStartResponse{
    ga.BaseResp base_resp = 1;
    BattleStartNotify info = 2;
}

/* battle 状态 */
enum BattleStatusEnum{
    UnknownBattleStatus = 0;
    StartBattle = 1;
    CancelBattle = 2;
}

/* 开启battle推送 */
message BattleStartNotify{
    uint32 channel_id = 2;
    uint32 duration_min = 3; // 持续分钟 单位分钟
    uint32 channel_vote_pk_type = 4;         // enum ChannelVotePkType PK类型
    repeated uint32 uid_list = 5;     // 参与UID
    uint32 vote_cnt = 6;     // 每个观众的票数
    string pk_name = 7;      // PK名
    uint32 battle_status = 8; /* battle 状态 BattleStatusEnum */
}
/*麦位名称---------------------------*/
message SwitchMicNameFuncReq{
    ga.BaseReq base_req = 1;
    uint32 channel_id=2;     //房间CID
    bool  func_switch=3;    //麦位名称功能开关
}
message SwitchMicNameFuncResp{
    ga.BaseResp base_resp = 1;

}
message GetMicNameReq{
    ga.BaseReq base_req = 1;
    uint32 channel_id=2;
}
message GetMicNameResp{
    ga.BaseResp base_resp = 1;
    MicNameInfo mic_info=2;     //麦位名称信息

}
message SetMicNameReq{
    ga.BaseReq base_req = 1;
    uint32 channel_id=2;
    MicNameInfo mic_info=3;    //麦位名称数组
}
message SetMicNameResp{
    ga.BaseResp base_resp = 1;
}

enum ActionMode{
    UnknownActionMode = 0;
    SwitchActionMode = 1; /* 麦位开关模式 */
    ExamineModeActionMode = 2; /* 审核模式 */
}

//麦位名称推送
message MicNameInfo{
    bool status_audit=1;        //审核状态
    bool func_switch=2;               //麦位名称功能开关
    repeated MicNameArray mic_name_array=3;
    uint32 uid=4;       //提交审核的人
    ActionMode action_mode=5;  //判断是那种动作模式

}
message MicNameArray{
    uint32 mic_id=1;      //麦序
    string mic_name=2;    //麦位名称
}
/*麦位名称---------------------------*/