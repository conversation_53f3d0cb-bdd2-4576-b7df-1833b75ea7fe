syntax = "proto2";

package ga.userpresent;

import "ga_base.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/userpresent";

// 礼物来源类型
enum PresentSourceType
{
  PRESENT_SOURCE_BUY = 0;      // 购买（红钻、T豆）
  PRESENT_SOURCE_PACKAGE = 1;    // 背包
  PRESENT_SOURCE_PACKAGE_FIRST = 2;    // 背包优先
  PRESENT_SOURCE_FELLOW = 3;    // 挚友信物
  PRESENT_SOURCE_MAGIC = 4;    // 幸运礼物
  PRESENT_SOURCE_LOTTERY_BUY = 5;    // 抽奖 - 购买
  PRESENT_SOURCE_LOTTERY_PACKAGE = 6;    // 抽奖 - 背包
  PRESENT_SOURCE_LOTTERY_MAGIC = 7;    // 抽奖 - 幸运礼物
  PRESENT_SOURCE_OFFERING_ROOM = 8;    // 拍卖房
  PRESENT_SOURCE_CHANNEL_GIFT_PK = 9;    // 礼物对决
  PRESENT_SOURCE_GRAB_CHAIR_GAME = 10;    // 抢椅子游戏奖励-购买
}

// 批量送礼类型
enum PresentBatchSendType
{
  PRESENT_SOURCE_NONE = 0;
  PRESENT_SOURCE_ALL_MIC = 1;    // 全麦送礼
  PRESENT_SOURCE_WITH_UID = 2;    // 用传入的uid_list批量送礼
}

// 送礼类型
enum PresentSendType
{
  PRESENT_SEND_NORMAL = 0;       // 普通送礼
  PRESENT_SEND_DRAW = 1;         // 涂鸦送礼
  PRESENT_SENT_FANS_LOTTERY = 2; // 粉丝团送礼
}

// 送礼来源类型
enum PresentSendSourceType
{
  E_SEND_SOURCE_DEFLAUTE = 0;         //默认类型(兼容旧版本)
  E_SEND_SOURCE_GIFT_TURNTABLE = 1;   //送礼转盘
  E_SEND_SOURCE_GIFT_SHELF = 2;       //礼物架
  E_SEND_SOURCE_SPEECH_BALL = 3;      //语音球
  E_SEND_SOURCE_DRAW_GIFT = 4;         //手绘
  E_SEND_SOURCE_MASKED_CALL = 5;      //语音匹配聊天
  E_SEND_SOURCE_IM = 6;                // IM
  E_SEND_SOURCE_OFFICIAL_CHANNEL = 7;      // 粉丝团抽奖
  E_SEND_SOURCE_FELLOW = 8;      //挚友信物
  E_SEND_SOURCE_MAGIC = 9;      //幸运礼物
  E_SEND_SOURCE_LOTTERY = 10;      //新版抽奖
  E_SEND_SOURCE_OFFERING_ROOM = 11;    // 拍卖房
  E_SEND_SOURCE_CHANNEL_GIFT_PK = 12; // 礼物对决
  E_SEND_SOURCE_CHANNEL_BIRTHDAY = 13;  // 生日送礼
}

// 送礼方式 
enum PresentSendMethodType
{
  PRESENT_TYPE_ROOM = 0;      //房间送礼
  PRESENT_TYPE_IM = 1;      //IM送礼
  PRESENT_TYPE_FELLOW = 2;      //挚友信物
}

message PresentPoint
{
  required float x = 1;
  required float y = 2;
}

message PresentLine
{
  required uint32 item_id = 1;      // 礼物id
  repeated PresentPoint point_list = 2;   // 礼物坐标
}

// 涂鸦礼物图
message DrawPresentPicture
{
  repeated PresentLine line_list = 1;
}

//赠送礼物
message SendPresentReq
{
  required BaseReq base_req = 1;
  required uint32 target_uid = 2;
  required uint32 item_id = 3;    // 礼物ID
  optional uint32 channel_id = 4;  // 通过房间赠送礼物时才需要填
  required uint32 config_update_time = 5; // 上次拉礼物配置时，服务器返回的update_time(这个东东没用咯)
  optional uint32 count = 6;          // 兼容旧版本，代码需要特殊处理，值为0时默认为1
  optional uint32 send_source = 7;  // 赠送时的点击来源 PresentSendSourceType 0.默认类型(兼容旧版本) 1.送礼转盘 2.礼物架（点击头像或成员列表） 3.语音球
  optional uint32 item_source = 8;  // 礼物来源 PresentSourceType
  optional uint32 source_id = 9;    // item_source对应的类型ID。比如如果是背包物品，那这里就是背包物品ID
  optional uint32 send_type = 10;   // 送礼类型 PresentSendType
  optional DrawPresentPicture draw_present_pic = 11;       // 涂鸦礼物图
}

message SendPresentResp
{
  required BaseResp base_resp = 1;
  required uint32 item_id = 2;
  optional PresentSendMsg msg_info = 3;
  optional uint32 member_contribution_added = 4;  // 增加的个人公会贡献
  optional uint32 count = 5;    // 礼物数量
  optional uint64 cur_tbeans = 6;    // 当前T豆余额
  optional uint32 item_source = 7;  // 礼物来源 PresentSourceType
  optional uint32 source_id = 8;  // 根据 item_source 来决定取值。如果是背包物品，就是背包物品ID
  optional uint32 source_remain = 9;  // 根据 item_source 来决定取值。如果是背包物品，就是背包物品的剩余数量
  optional uint32 expire_time = 10;  // 根据 item_source 来决定取值。如果是背包物品，就是背包物品的更新后的过期时间
  optional PresentBoxDetail box_detail = 11; // 开盒信息，如果是前置特效的全服礼物会用到
}

enum PresentTextType{
  E_TEXT_TYPE_DEFAULT = 0;
  E_TEXT_TYPE_INTIMACY = 1; // 亲密度奖励
}

// IM赠送礼物
message IMSendPresentReq
{
  required BaseReq base_req = 1;
  required uint32 target_uid = 2;
  required uint32 item_id = 3; //礼物id
  optional uint32 count = 4; // 兼容旧版本，代码需要特殊处理，值为0时默认为1
  optional uint32 send_source = 5;  // 赠送时的点击来源 PresentSendSourceType 0.默认类型(兼容旧版本) 1.送礼转盘 2.礼物架（点击头像或成员列表） 3.语音球
  optional uint32 item_source = 6;  // 礼物来源 PresentSourceType
  optional uint32 source_id = 7;    // 如果是背包里的礼物，就是背包礼物对应的唯一id
  optional uint32 send_type = 8;     // 送礼类型 PresentSendType
  optional uint32 present_text_type = 9; // send_source == E_SEND_SOURCE_IM 时才填，enum PresentTextType 送礼文案类型，根据不同活动区分IM的富文本内容
  optional string pre_effect_text = 10; // 前置特效文案
  repeated uint32 target_uid_list = 11; // 如果需要一次给多个用户送礼传这个，此时回包里的PresentSendMsg只会传其中第一个的信息
}

message IMSendPresentResp
{
  required BaseResp base_resp = 1;
  required uint32 item_id = 2;
  optional PresentSendMsg msg_info = 3;
  optional uint32 member_contribution_added = 4;  // 增加的个人公会贡献
  optional uint32 count = 5; // 礼物数量
  optional uint64 cur_tbeans = 6; // 当前T豆余额
  optional uint32 item_source = 7;  // 礼物来源 PresentSourceType
  optional uint32 source_id = 8;    // 如果是背包里的礼物，就是背包礼物对应的唯一id
  optional uint32 source_remain = 9;    // 如果是背包里的礼物，就是背包礼物的剩余数量
  optional uint32 expire_time = 10;  // 根据 item_source 来决定取值。如果是背包物品，就是背包物品的更新后的过期时间
}

message PresentTargetUserInfo
{
  required uint32 uid = 1;
  required string account = 2;
  required string name = 3;
  optional UserProfile user_profile = 4;  // 特权信息
  optional string custom_text = 5;
}

//批量赠送礼物
message BatchSendPresentReq
{
  required BaseReq base_req = 1;
  required uint32 item_id = 2;
  optional uint32 channel_id = 3;  // 通过房间赠送礼物时才需要填
  required uint32 count = 4;  // 礼物数量
  required uint32 send_source = 5;  // 赠送时的点击来源 PresentSendSourceType 0.默认类型(兼容旧版本) 1.送礼转盘 2.礼物架（点击头像或成员列表） 3.语音球
  required uint32 item_source = 6;  // 礼物来源 PresentSourceType
  optional uint32 source_id = 7;    // 如果是背包里的礼物，就是背包礼物对应的唯一id
  required uint32 batch_type = 8;  // 批量送礼类型 PresentBatchSendType
  optional uint32 send_type = 9;     // 送礼类型 PresentSendType
  optional DrawPresentPicture draw_present_pic = 10;       // 涂鸦礼物图
  repeated uint32 uid_list = 11;       // 送礼id列表，如果PresentSourceType = 2则需要填
}

message BatchSendPresentResp
{
  required BaseResp base_resp = 1;
  optional PresentBatchInfoMsg msg_info = 2;
  optional uint64 cur_tbeans = 3; // 当前T豆余额
  optional uint32 item_source = 4;  // 礼物来源 PresentSourceType
  optional uint32 source_id = 5;    // 如果是背包里的礼物，就是背包礼物对应的唯一id
  optional uint32 source_remain = 6;    // 如果是背包里的礼物，就是背包礼物的剩余数量
  repeated PresentTargetUserInfo target_list = 7;  // 收礼对象列表
  optional PresentSendItemInfo item_info = 8;    // 礼物信息
  optional uint32 expire_time = 9;  // 根据 item_source 来决定取值。如果是背包物品，就是背包物品的更新后的过期时间
}

// 用户收到的礼物明细
message UserPresentDetail
{
  required uint32 uid = 1;
  required uint32 from_uid = 2;
  required string from_account = 3;
  required string from_name = 4;
  required string from_face_md5 = 5;
  required PresentItemBriefConfig item_brief_config = 6;
  required uint32 send_time = 7;
  optional uint32 item_count = 8;
  optional uint32 item_id = 9;
  optional uint32 charm = 10;
  optional uint32 send_source = 11;  // PresentSendSourceType
  optional int32  from_sex = 12;
  optional int32  send_method = 13;  //PresentSendMethodType
  optional UserProfile user_profile = 14;  // 特权信息
  optional UserUKWInfo user_ukw_info = 15;  // 神秘人信息
}

// 用户送出的礼物明细
message UserPresentSendDetail
{
  required uint32 uid = 1;
  required uint32 to_uid = 2;
  required string to_account = 3;
  required string to_name = 4;
  required string to_face_md5 = 5;
  required PresentItemBriefConfig item_brief_config = 6;
  required uint32 send_time = 7;
  optional uint32 item_count = 8;
  optional uint32 item_id = 9;
  optional uint32 rich = 10;
  optional uint32 send_source = 11;  // PresentSendSourceType
  optional int32  to_sex = 12;
  optional int32  send_method = 13;  //PresentSendMethodType
  optional UserProfile user_profile = 14;  // 特权信息
  optional UserUKWInfo user_ukw_info = 15;  // 神秘人信息
}


// 获取用户的礼物信息
message GetUserPresentInfoReq
{
  required BaseReq base_req = 1;
  required uint32 target_uid = 2;
}

message GetUserPresentInfoResp
{
  required BaseResp base_resp = 1;
  required uint32 total_value = 2;  // 礼物总值
  required uint32 total_count = 3;  // 礼物总数
  repeated PresentCount present_count_list = 4;
  required uint32 target_uid = 5;
}


// 要获取的明细类型
enum PresentDetailType
{
  E_DETAIL_PRESENT_RECEIVE = 0;   //收礼明细
  E_DETAIL_PRESENT_SEND = 1;      //送礼明细
  E_DETAIL_PRESENT_ALL = 2;        //全部明细
}

// 获取用户的礼物明细列表
message GetUserPresentDetailListReq
{
  required BaseReq base_req = 1;
  optional uint32 detail_type = 2;   // 明细类型 PresentDetailType
}

message GetUserPresentDetailListResp
{
  required BaseResp base_resp = 1;
  repeated UserPresentDetail present_detail_list = 2;   // 收礼明细
  repeated UserPresentSendDetail present_send_detail_list = 3;  // 送礼明细
}

// 获取礼物配置列表
message GetPresentConfigListReq
{
  required BaseReq base_req = 1;
}

message GetPresentConfigListResp
{
  required BaseResp base_resp = 1;
  repeated PresentItemConfig config_list = 2;
  required uint32 last_update_time = 3;
}

message GetPresentConfigByIdReq
{
  required BaseReq base_req = 1;
  required uint32 item_id = 2;
}

message GetPresentConfigByIdResp
{
  required BaseResp base_resp = 1;
  optional PresentItemConfig item_config = 2;
}

message PresentSendItemInfo
{
  required uint32 item_id = 1;
  required uint32 count = 2;
  optional uint32 show_effect = 3;  // 播放特效 ga::PRESENT_SHOW_EFFECT_TYPE
  optional uint32 show_effect_v2 = 4;  // 播放特效 ga::PRESENT_SHOW_EFFECT_TYPE_V2
  optional uint32 flow_id = 5;    // 流光id
  optional bool is_batch = 6;      // 0.普通送礼 1.批量送礼
  optional bool show_batch_effect = 7;
  optional uint32 send_type = 8;      // 送礼类型 PresentSendType
  optional DrawPresentPicture draw_present_pic = 9;  // 涂鸦礼物图
  optional uint32 dynamic_template_id = 10;   // 非全屏礼物动效模板id
  optional bool is_visible_to_sender = 11;  //消息是否送礼者可见
  optional bool is_show_surprise = 12; // 是否触发了升级礼物的彩蛋特效，触发了就不展示原本的礼物特效
  optional uint32 surprise_count = 13; // 如果触发了彩蛋特效，要展示几次
  optional string custom_text_json = 14; // 用于替换礼物资源内自定义文案的json
  optional bool show_im_pre_effect = 15; // 是否展示im送礼的前置特效
  optional string pre_effect_text = 16; // 前置特效文案
}


// 可靠礼物消息
message PresentSendMsg
{
  required PresentSendItemInfo item_info = 1;
  required uint64 send_time = 2;
  optional uint32 channel_id = 3;
  required uint32 send_uid = 4;
  required string send_account = 5;
  required string send_nickname = 6;
  required uint32 target_uid = 7;
  required string target_account = 8;
  required string target_nickname = 9;
  optional string extend_json = 10;
  optional UserProfile from_user_profile = 11;
  optional UserProfile to_user_profile = 12;
  optional bool only_show_msg = 13; // 只显示公屏消息
}

message PresentBatchTargetInfo
{
  required uint32 uid = 1;
  required string account = 2;
  required string nickname = 3;
  optional string extend_json = 4;
  optional UserProfile user_profile = 5;
  optional string custom_text = 6;
}

// 批量送礼信息
message PresentBatchInfoMsg
{
  required uint32 item_id = 1;
  required uint32 total_item_count = 2;  // 送出的礼物总数量
  required uint32 batch_type = 3;  // 批量送礼类型 PresentBatchSendType
  required uint64 send_time = 4;
  optional uint32 channel_id = 5;
  required uint32 send_uid = 6;
  required string send_account = 7;
  required string send_nickname = 8;
  optional string extend_json = 9;
  repeated PresentBatchTargetInfo target_list = 10;
  optional PresentSendItemInfo item_info = 11;
  optional UserProfile from_user_profile = 12;
  optional bool is_multi = 13; // 是否多麦，false则为全麦
}

// 获取礼物流光配置列表
message GetPresentFlowConfigListReq
{
  required BaseReq base_req = 1;
  required uint32 last_update_time = 2;  // last_update_time和配置的last_update_time一致时，不返回配置数据
}

message GetPresentFlowConfigListResp
{
  required BaseResp base_resp = 1;
  repeated PresentFlowConfig config_list = 2;
  required uint32 last_update_time = 3;
}

message GetPresentFlowConfigByIdReq
{
  required BaseReq base_req = 1;
  required uint32 flow_id = 2;
}

message GetPresentFlowConfigByIdResp
{
  required BaseResp base_resp = 1;
  optional PresentFlowConfig flow_config = 2;
}

message DrawPresentPara
{
  required uint32 item_id = 1;
  required string img_url = 2;
}

// 获取涂鸦礼物参数信息
message GetDrawPresentParaReq
{
  required BaseReq base_req = 1;
}

message GetDrawPresentParaResp
{
  required BaseResp base_resp = 1;
  repeated DrawPresentPara para_list = 2;
}


//礼物冠名配置信息
message NamingPresentConfig
{
  optional uint32 uid = 1;
  optional uint32 gift_id = 2;
  optional string naming_content = 3;
  optional string account = 4;
}

message GetNamingPresentConfigListReq
{
  required BaseReq base_req = 1;
}

message GetNamingPresentConfigListResp
{
  required BaseResp base_resp = 1;
  repeated NamingPresentConfig config_list = 2;
}

//获取非全屏礼物送礼动效模板配置
message GetPresentDynamicTemplateConfigReq
{
  required BaseReq base_req = 1;
}

message GetPresentDynamicTemplateConfigResp
{
  required BaseResp base_resp = 1;
  optional PresentTemplateConfigs configs = 2;
}

//获取IM礼物配置

message GetImPresentItemIdListReq
{
  required BaseReq base_req = 1;
}

message GetImPresentItemIdListResp
{
  required BaseResp base_resp = 1;
  repeated uint32 item_id_list = 2;
}



//获取陌生人送礼解除限制弹窗礼物配置

message GetStangerImItemIdListReq
{
  required BaseReq base_req = 1;
}

message GetStangerImItemIdListResp
{
  required BaseResp base_resp = 1;
  repeated uint32 item_id_list = 2;
}

//粉丝团抽奖的中奖信息
message FansPresentMessage
{
  repeated string account = 1;
  required string present_name = 2;
  required string present_icon = 3;
}

// 展示礼物盒会用到的信息
message PresentBoxInfo{
  required PresentSendMsg item_msg = 1;  // 原本的礼物结构
  required PresentBoxDetail box_detail = 2;  // 开盒相关信息
}

message PresentBoxDetail{
  required string box_id = 1;
  required UserProfile from_user_profile = 2;
  required UserProfile to_user_profile = 3;
  required uint32 item_id = 4;
  required string item_name = 5;
  required uint64 send_time = 6;
  required string extend_json = 7; // 客户端用来构造特效的通用json
  required uint64 delay_time = 8;  // 开盒推迟的时间
  required bool   is_visible_to_sender = 9; // 是否送礼者可见（抽奖 - true, 普通送礼 - false）
}

// 全服礼物前置特效 - 开盒
message UnpackPresentBoxReq{
  required BaseReq base_req = 1;
  required string box_id = 2;
  required uint32 channel_id = 3;
}

message UnpackPresentBoxResp{
  required BaseResp base_resp = 1;
  required PresentBoxInfo box_info = 2;
}

// 展示礼物盒会用到的信息
message PresentBoxOpenMsg{
  required string box_id = 1;
}