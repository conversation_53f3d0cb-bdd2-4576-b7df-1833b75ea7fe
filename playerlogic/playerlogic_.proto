syntax = "proto3";

package ga.playerlogic;

import "ga_base.proto";
import "ugc/ugc_.proto";
import "topic_channel/topic_channel_.proto";
//import "muse_shining_point_logic/muse_shining_point_logic.proto";


option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/playerlogic";

message PlayerLoadMore {
    uint32 last_page = 1;
    uint32 last_count = 2;
    int64 last_time = 3;
}

message PlayerReq {
    ga.BaseReq base_req = 1;
    int32 gender = 2;
    uint32 count = 3;
    PlayerLoadMore loadmore = 4;
}

message PlayerResp {
    ga.BaseResp base_resp = 1;
    repeated PlayerInfo play_info = 2;
    PlayerLoadMore loadmore = 3;
}


message PlayerPostInfo {
    string post_id = 1;
    ga.ugc.PostType post_type = 2;
    repeated ga.ugc.Attachment attachments = 3;
    uint64 post_time = 4; // 发帖时间, unix second
    uint32 attitude_count = 5;//点赞数
    uint32 share_count = 6;//分享数
    uint32 comment_count =7;//评论数
    string content = 8;//进行内容处理
}

message PlayerInfo {
    repeated PlayerPostInfo post_infos = 1;
    uint32 uid = 2;
    string account =3;
    int32 gender = 4;
    string city_tab = 5;
    string recommend_note = 6;
    string nickname = 7;
    repeated string recommend_text_list = 8;
}

message PostsReq {
    ga.BaseReq base_req = 1;
    string account =2;
}

message PostsRsp {
    ga.BaseResp base_resp = 1;
    // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
    repeated ga.ugc.Feed Feed_infos = 2;//热门图片列表的流 ugc_的Feed
    uint32 uid = 3;
    string account =4;
}

message NewPostsReq {
    ga.BaseReq base_req = 1;
    repeated uint32 uids = 2;
}

message NewPostInfo {
    PlayerPostInfo post_infos = 1;
    uint32 uid = 2;
}

message NewPostsRsp {
    ga.BaseResp base_resp = 1;
    repeated NewPostInfo new_post_info = 2;
    string text = 3;
}

//游戏段位信息
message GameLevelInfo{
    uint32 uid = 1;
    map<string,uint32> game_level = 2;
}


//只有是默认值 或其他场景7才能通过
enum PageNoSettings{
    NORMAL = 0;//0.默认值
    IN_HOME = 1;//  1. 房间内
    IN_IM = 2;//  2. IM内
    PULISH_POST = 3;//  3. 发布动态
    EDIT_POST = 4;//  4. 编辑内容
    PULISH_ROOM = 5;//  5. 发布房间
    MATCHING_ROOM = 6;//  6. 匹配房间
    OTHERS = 7;//    7. 其他场景
    MUSIC_NEST = 8; // 乐窝房
}

//发放玩伴请求类型
enum PlayerProvidedReqType {
    NORMAL_TYPE = 0;//普通请求
    OPEN_TYPE = 1;//打开开关的请求 
    NORMAL_TIME_ONLY_TYPE = 2;//普通只请求时间
    OPEN_TIME_ONLY_TYPE = 3;//打开开关只请求时间
    RADAR_OPEN_TIME_ONLY_TYPE = 4;//打开雷达的开关只请求时间
}

//房间状态
enum PlayerProvidedReqRoomType {
    NOT_IN_ROOM = 0;//不在房间
    IN_ROOM_NORMAL = 1;//在房间
}

/*是否强制请求以下类型*/
enum ForceRequestType {
    Force_Normal = 0; //
    Force_Chanel_Push = 1; // 房间下发
}


message PlayerProvidedReq{
    ga.BaseReq base_req = 1;
    uint32 uid = 2;
    PageNoSettings page_no = 3;//请求的页数
    PlayerProvidedReqType type = 4; //请求类型
    PlayerProvidedReqRoomType room_type = 5;//房间类型

    uint32 get_count = 6;                  //第几次获取了，从1开始
    bool never_into_channel = 7;           //本地没进过房给个true
    string channel_package_id = 8;        //渠道包id
    bool is_player_provide_open = 9; // 发放玩伴是否开启，用于和雷达的房间下发做比例判断
    ForceRequestType force_type = 10; // 如果强制请求房间下发，则返回房间下发
    uint32 in_room_duration = 11; // 在房时长
}

message ClosePopUpReq{
    ga.BaseReq base_req = 1;
}
message ClosePopUpResp{
    ga.BaseResp base_resp = 1;
}

/*
message GetDialogV2Resp {
    required ga.BaseResp base_resp = 1;
    required uint32 tab_id = 2;                         //先判断tab id，为0表示没有可推荐的房间就不需要弹窗了
    optional uint32 channel_id = 3;                     //临时房没有这个值，个人房有这个值
    optional TopicChannelInfoV2Type type = 4;           //用于判断是否临时房
    optional DialogConfig config = 5;                   //弹窗的样式配置
    repeated Player player_list = 6;                    //玩家列表，头像中心显示的拿第0个
    optional string desc = 7;                           //房间区服的那些描述，返回值 "安Q | 娱乐匹配 | 五排"
    optional string tab_name = 8;                       //eg:王者荣耀，和平精英
}
*/

enum TypePlayerProvidedRsp {
    playerProvided = 0; // 发放玩伴
    dialogProvided = 1;
    channelPushProvided = 2; // 房间下发

}
message PlayerProvidedRsp{
    ga.BaseResp base_resp = 1;
    uint32 uid = 2;
    string account = 3;
    int32 gender = 4;
    string nickname = 5;
    string city = 6;
    string recommend_text = 7;
    repeated string recommend_text_list = 8;
    // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
    repeated ga.ugc.Feed Feed_infos = 9;//热门图片列表的流 ugc_的Feed
    repeated GameLevelInfo game_info_list = 10;

    int64 query_interval = 11;//请求的时间间隔  单位为秒 例如60
    // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
    TypePlayerProvidedRsp typeResp = 12;
    ga.topic_channel.GetDialogV2Resp dialog_info = 13; //怼脸弹窗信息
    bool is_channel_push_close = 14;    // true:关闭首页弹窗推荐请求
}

message StrangerCardReq{
    ga.BaseReq base_req = 1;
    uint32 uid = 2;
    string target_account = 3;
}

message StrangerCardResp{
    ga.BaseResp base_resp = 1;
    uint32 uid = 2;
    string city = 3;
    repeated string recommend_text = 4;
    string target_account = 5;
    int32 gender = 6;
    // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
    repeated ga.ugc.Feed Feed_infos = 7;//热门图片列表的流 ugc_的Feed
//    repeated ga.muse_shining_point_logic.ShiningPointInfo shining_point_list = 8; // 闪光点列表
//    repeated ga.muse_shining_point_logic.ShiningPointExtraInfo shining_point_extra_list = 9; // 闪光点额外信息 按照认证顺序排列
}

enum Settings{
    PLAYER_FOUND = 0;
    CITY_TAG = 1;
}

message GetPlayerFoundSettingReq {
    ga.BaseReq base_req = 1;
    repeated uint32 uids = 2;
    Settings type = 3;
}

message UserPlayerFoundSetting{
    uint32 uid = 1;
    bool on = 2;
}

message GetPlayerFoundSettingResp {
    ga.BaseResp base_resp = 1;
    repeated UserPlayerFoundSetting settings = 2;
    Settings type = 3;
}

message UpdatePlayerFoundSettingReq{
    ga.BaseReq base_req = 1;
    UserPlayerFoundSetting setting = 2;
    Settings type = 3;
}

message UpdatePlayerFoundSettingResp{
    ga.BaseResp base_resp = 1;
}

// kafka produce
message UpdatePlayerFoundSettingEvent{
    uint32 uid = 1; //用户id
    bool   on = 2;  //用户开启了还是关闭了
    uint64 modify_time = 3; //修改设置的时间
}
