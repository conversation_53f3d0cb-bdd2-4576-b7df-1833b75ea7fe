syntax = "proto3";
package ga.channel_mic_audio;

import "ga_base.proto";

option go_package = "golang.52tt.com/protocol/app/channel_mic_audio";
option java_package = "com.yiyou.ga.model.proto";

message AudioBitRateConfig {
  uint32 bit_rate = 1; // 码率，单位kbps
  uint64 left_count = 2; // 当日剩余可用次数
  string remark = 3; // 码率备注
}
message GetAudioBitRateUpgradeConfigRequest {
  ga.BaseReq base_req = 1;
}
message GetAudioBitRateUpgradeConfigResponse {
  ga.BaseResp base_resp = 1;
  // 用户个人房当前高码率，单位kbps，非零代表该用户已抢到高码率
  uint32 cur_high_bit_rate = 2;
  // 可选高码率配置（但该列表为空时，则客户端应当隐藏码率升级入口）
  repeated AudioBitRateConfig optional_bit_rate_list = 3;
  // 高码率生效时长，单位秒
  int64 effective_duration_sec = 4;
}

message UpgradeAudioBitRateRequest {
  ga.BaseReq base_req = 1;
  uint32 target_high_bit_rate = 2; // 目标码率，单位kbps
}
message UpgradeAudioBitRateResponse {
  ga.BaseResp base_resp = 1;
}

// 码率变更推送
message ChannelAudioBitRateChangeMsg {
  enum ChangeType {
    CHANGE_TYPE_UNSPECIFIED = 0;
    CHANGE_TYPE_UPGRADE = 1; // 升级码率
    CHANGE_TYPE_RECOVER = 2; // 恢复默认
  }

  uint32 cid = 1;
  // 变更类型
  ChangeType change_type = 2;
  // 当前高码率，单位kbps（恢复默认时使用，变更类型为 CHANGE_TYPE_RECOVER 时才非零）
  uint32 cur_high_bit_rate = 3;
  // 目标高码率，单位kbps（升级码率时使用，变更类型为 CHANGE_TYPE_UPGRADE 时才非零）
  uint32 target_high_bit_rate = 4;
  // key为客户端类型，see ga.TT_CLIENT_TYPE（value为sdk配置信息json格式，客户端自己解析）
  map<uint32, string> mic_audio_sdk_info = 5;
  // sdk配置信息更新时间，毫秒时间戳
  uint64 mic_audio_sdk_info_update_ms = 6;
}