syntax = "proto3";

package ga.im;

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/im";

// IMTwinMsg 马甲包与TT互通的消息
// 马甲包可以自定义自己的IM消息
// 在马甲包内通过 [TwinContent] 展示详情
// 在TT端通过 [TTContent] 展示简要的内容（内容为空时，TT隐藏该消息）
message IMTwinMsg {
  //有跳转内容时，需要高亮显示
  message Action {
    //跳转的文本
    string desc = 1;
    //Android跳转的uri
    string android_uri = 2;
    //iOS跳转的uri
    string ios_uri = 3;
  }
  // TT显示的样式
  message TTContent {
    // 主态显示的内容（如果是发送者，该内容为空时，TT隐藏该消息）
    string sender_content = 1;
    // 客态显示的内容（如果是接收者，该内容为空时，TT隐藏该消息）
    string receiver_content = 2;
    // 跳转内容，需要高亮展示
    Action action = 3;
  }
  // 马甲包显示的样式
  message TwinContent {
    // 马甲包显示的消息内容
    bytes payload = 1;
    // 马甲包对应的id
    // 马甲包只处理，自己对应的marketId的消息
    uint32 market_id = 2;
  }
  // TT展示的内容
  // TT主态（发送者）：sender_content + action(高亮)
  // 如果是发送者，该内容为空时，TT隐藏该消息
  // 
  // TT客态（接受者）：receiver_content + action(高亮)
  // 如果是接收者，该内容为空时，TT隐藏该消息
  TTContent tt_content = 1;
  // 马甲包显示的内容
  TwinContent twin_content = 2;
}