// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/virtual-image-card/virtual-image-card.proto

package virtual_image_card // import "golang.52tt.com/protocol/services/virtual-image-card"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/services/extension/options"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type PayChannel int32

const (
	PayChannel_PAY_CHANNEL_UNSPECIFIED PayChannel = 0
	PayChannel_PAY_CHANNEL_ALIPAY      PayChannel = 1
	PayChannel_PAY_CHANNEL_WECHAT      PayChannel = 2
	PayChannel_PAY_CHANNEL_APPSTORE    PayChannel = 3
)

var PayChannel_name = map[int32]string{
	0: "PAY_CHANNEL_UNSPECIFIED",
	1: "PAY_CHANNEL_ALIPAY",
	2: "PAY_CHANNEL_WECHAT",
	3: "PAY_CHANNEL_APPSTORE",
}
var PayChannel_value = map[string]int32{
	"PAY_CHANNEL_UNSPECIFIED": 0,
	"PAY_CHANNEL_ALIPAY":      1,
	"PAY_CHANNEL_WECHAT":      2,
	"PAY_CHANNEL_APPSTORE":    3,
}

func (x PayChannel) String() string {
	return proto.EnumName(PayChannel_name, int32(x))
}
func (PayChannel) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_1e8e060b450bf39d, []int{0}
}

// 套餐类型
type PackageType int32

const (
	PackageType_PACKAGE_TYPE_UNSPECIFIED PackageType = 0
	PackageType_PACKAGE_TYPE_NORMAL      PackageType = 1
	PackageType_PACKAGE_TYPE_AUTO_RENEW  PackageType = 2
)

var PackageType_name = map[int32]string{
	0: "PACKAGE_TYPE_UNSPECIFIED",
	1: "PACKAGE_TYPE_NORMAL",
	2: "PACKAGE_TYPE_AUTO_RENEW",
}
var PackageType_value = map[string]int32{
	"PACKAGE_TYPE_UNSPECIFIED": 0,
	"PACKAGE_TYPE_NORMAL":      1,
	"PACKAGE_TYPE_AUTO_RENEW":  2,
}

func (x PackageType) String() string {
	return proto.EnumName(PackageType_name, int32(x))
}
func (PackageType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_1e8e060b450bf39d, []int{1}
}

// 在售架套餐状态
type SalePackageStatus int32

const (
	SalePackageStatus_ENUM_SALE_PACKAGE_STATUS_UNSPECIFIED  SalePackageStatus = 0
	SalePackageStatus_ENUM_SALE_PACKAGE_STATUS_SHELF        SalePackageStatus = 1
	SalePackageStatus_ENUM_SALE_PACKAGE_STATUS_NOT_ON_SHELF SalePackageStatus = 2
	SalePackageStatus_ENUM_SALE_PACKAGE_STATUS_WAIT_SHELF   SalePackageStatus = 3
)

var SalePackageStatus_name = map[int32]string{
	0: "ENUM_SALE_PACKAGE_STATUS_UNSPECIFIED",
	1: "ENUM_SALE_PACKAGE_STATUS_SHELF",
	2: "ENUM_SALE_PACKAGE_STATUS_NOT_ON_SHELF",
	3: "ENUM_SALE_PACKAGE_STATUS_WAIT_SHELF",
}
var SalePackageStatus_value = map[string]int32{
	"ENUM_SALE_PACKAGE_STATUS_UNSPECIFIED":  0,
	"ENUM_SALE_PACKAGE_STATUS_SHELF":        1,
	"ENUM_SALE_PACKAGE_STATUS_NOT_ON_SHELF": 2,
	"ENUM_SALE_PACKAGE_STATUS_WAIT_SHELF":   3,
}

func (x SalePackageStatus) String() string {
	return proto.EnumName(SalePackageStatus_name, int32(x))
}
func (SalePackageStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_1e8e060b450bf39d, []int{2}
}

type CardStatus int32

const (
	CardStatus_ENUM_STATUS_NO_OPEN     CardStatus = 0
	CardStatus_ENUM_STATUS_OPENING     CardStatus = 1
	CardStatus_ENUM_STATUS_SOON_EXPIRE CardStatus = 2
	CardStatus_ENUM_STATUS_EXPIRED     CardStatus = 3
	CardStatus_ENUM_STATUS_SIGN        CardStatus = 4
)

var CardStatus_name = map[int32]string{
	0: "ENUM_STATUS_NO_OPEN",
	1: "ENUM_STATUS_OPENING",
	2: "ENUM_STATUS_SOON_EXPIRE",
	3: "ENUM_STATUS_EXPIRED",
	4: "ENUM_STATUS_SIGN",
}
var CardStatus_value = map[string]int32{
	"ENUM_STATUS_NO_OPEN":     0,
	"ENUM_STATUS_OPENING":     1,
	"ENUM_STATUS_SOON_EXPIRE": 2,
	"ENUM_STATUS_EXPIRED":     3,
	"ENUM_STATUS_SIGN":        4,
}

func (x CardStatus) String() string {
	return proto.EnumName(CardStatus_name, int32(x))
}
func (CardStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_1e8e060b450bf39d, []int{3}
}

type DisplayCondition_CondType int32

const (
	DisplayCondition_ENUM_USER_ALL       DisplayCondition_CondType = 0
	DisplayCondition_ENUM_USER_SPECIFIED DisplayCondition_CondType = 1
)

var DisplayCondition_CondType_name = map[int32]string{
	0: "ENUM_USER_ALL",
	1: "ENUM_USER_SPECIFIED",
}
var DisplayCondition_CondType_value = map[string]int32{
	"ENUM_USER_ALL":       0,
	"ENUM_USER_SPECIFIED": 1,
}

func (x DisplayCondition_CondType) String() string {
	return proto.EnumName(DisplayCondition_CondType_name, int32(x))
}
func (DisplayCondition_CondType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_1e8e060b450bf39d, []int{34, 0}
}

type PlaceOrderReq struct {
	Uid                    uint32     `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PackageId              uint32     `protobuf:"varint,2,opt,name=package_id,json=packageId,proto3" json:"package_id,omitempty"`
	PayChannel             PayChannel `protobuf:"varint,3,opt,name=pay_channel,json=payChannel,proto3,enum=virtual_image_card.PayChannel" json:"pay_channel,omitempty"`
	OriginalTransactionIds []string   `protobuf:"bytes,4,rep,name=original_transaction_ids,json=originalTransactionIds,proto3" json:"original_transaction_ids,omitempty"`
	PayPriceCent           uint32     `protobuf:"varint,5,opt,name=pay_price_cent,json=payPriceCent,proto3" json:"pay_price_cent,omitempty"`
	XXX_NoUnkeyedLiteral   struct{}   `json:"-"`
	XXX_unrecognized       []byte     `json:"-"`
	XXX_sizecache          int32      `json:"-"`
}

func (m *PlaceOrderReq) Reset()         { *m = PlaceOrderReq{} }
func (m *PlaceOrderReq) String() string { return proto.CompactTextString(m) }
func (*PlaceOrderReq) ProtoMessage()    {}
func (*PlaceOrderReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_1e8e060b450bf39d, []int{0}
}
func (m *PlaceOrderReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PlaceOrderReq.Unmarshal(m, b)
}
func (m *PlaceOrderReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PlaceOrderReq.Marshal(b, m, deterministic)
}
func (dst *PlaceOrderReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PlaceOrderReq.Merge(dst, src)
}
func (m *PlaceOrderReq) XXX_Size() int {
	return xxx_messageInfo_PlaceOrderReq.Size(m)
}
func (m *PlaceOrderReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PlaceOrderReq.DiscardUnknown(m)
}

var xxx_messageInfo_PlaceOrderReq proto.InternalMessageInfo

func (m *PlaceOrderReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *PlaceOrderReq) GetPackageId() uint32 {
	if m != nil {
		return m.PackageId
	}
	return 0
}

func (m *PlaceOrderReq) GetPayChannel() PayChannel {
	if m != nil {
		return m.PayChannel
	}
	return PayChannel_PAY_CHANNEL_UNSPECIFIED
}

func (m *PlaceOrderReq) GetOriginalTransactionIds() []string {
	if m != nil {
		return m.OriginalTransactionIds
	}
	return nil
}

func (m *PlaceOrderReq) GetPayPriceCent() uint32 {
	if m != nil {
		return m.PayPriceCent
	}
	return 0
}

type PlaceOrderResp struct {
	OrderNo              string   `protobuf:"bytes,1,opt,name=order_no,json=orderNo,proto3" json:"order_no,omitempty"`
	Token                string   `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`
	CliOrderNo           string   `protobuf:"bytes,3,opt,name=cli_order_no,json=cliOrderNo,proto3" json:"cli_order_no,omitempty"`
	CliOrderTitle        string   `protobuf:"bytes,4,opt,name=cli_order_title,json=cliOrderTitle,proto3" json:"cli_order_title,omitempty"`
	OrderPrice           string   `protobuf:"bytes,5,opt,name=order_price,json=orderPrice,proto3" json:"order_price,omitempty"`
	Tsk                  string   `protobuf:"bytes,6,opt,name=tsk,proto3" json:"tsk,omitempty"`
	ChannelMap           string   `protobuf:"bytes,7,opt,name=channel_map,json=channelMap,proto3" json:"channel_map,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PlaceOrderResp) Reset()         { *m = PlaceOrderResp{} }
func (m *PlaceOrderResp) String() string { return proto.CompactTextString(m) }
func (*PlaceOrderResp) ProtoMessage()    {}
func (*PlaceOrderResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_1e8e060b450bf39d, []int{1}
}
func (m *PlaceOrderResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PlaceOrderResp.Unmarshal(m, b)
}
func (m *PlaceOrderResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PlaceOrderResp.Marshal(b, m, deterministic)
}
func (dst *PlaceOrderResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PlaceOrderResp.Merge(dst, src)
}
func (m *PlaceOrderResp) XXX_Size() int {
	return xxx_messageInfo_PlaceOrderResp.Size(m)
}
func (m *PlaceOrderResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PlaceOrderResp.DiscardUnknown(m)
}

var xxx_messageInfo_PlaceOrderResp proto.InternalMessageInfo

func (m *PlaceOrderResp) GetOrderNo() string {
	if m != nil {
		return m.OrderNo
	}
	return ""
}

func (m *PlaceOrderResp) GetToken() string {
	if m != nil {
		return m.Token
	}
	return ""
}

func (m *PlaceOrderResp) GetCliOrderNo() string {
	if m != nil {
		return m.CliOrderNo
	}
	return ""
}

func (m *PlaceOrderResp) GetCliOrderTitle() string {
	if m != nil {
		return m.CliOrderTitle
	}
	return ""
}

func (m *PlaceOrderResp) GetOrderPrice() string {
	if m != nil {
		return m.OrderPrice
	}
	return ""
}

func (m *PlaceOrderResp) GetTsk() string {
	if m != nil {
		return m.Tsk
	}
	return ""
}

func (m *PlaceOrderResp) GetChannelMap() string {
	if m != nil {
		return m.ChannelMap
	}
	return ""
}

type CancelOrderReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	OrderNo              string   `protobuf:"bytes,2,opt,name=order_no,json=orderNo,proto3" json:"order_no,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CancelOrderReq) Reset()         { *m = CancelOrderReq{} }
func (m *CancelOrderReq) String() string { return proto.CompactTextString(m) }
func (*CancelOrderReq) ProtoMessage()    {}
func (*CancelOrderReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_1e8e060b450bf39d, []int{2}
}
func (m *CancelOrderReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelOrderReq.Unmarshal(m, b)
}
func (m *CancelOrderReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelOrderReq.Marshal(b, m, deterministic)
}
func (dst *CancelOrderReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelOrderReq.Merge(dst, src)
}
func (m *CancelOrderReq) XXX_Size() int {
	return xxx_messageInfo_CancelOrderReq.Size(m)
}
func (m *CancelOrderReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelOrderReq.DiscardUnknown(m)
}

var xxx_messageInfo_CancelOrderReq proto.InternalMessageInfo

func (m *CancelOrderReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CancelOrderReq) GetOrderNo() string {
	if m != nil {
		return m.OrderNo
	}
	return ""
}

type CancelOrderResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CancelOrderResp) Reset()         { *m = CancelOrderResp{} }
func (m *CancelOrderResp) String() string { return proto.CompactTextString(m) }
func (*CancelOrderResp) ProtoMessage()    {}
func (*CancelOrderResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_1e8e060b450bf39d, []int{3}
}
func (m *CancelOrderResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelOrderResp.Unmarshal(m, b)
}
func (m *CancelOrderResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelOrderResp.Marshal(b, m, deterministic)
}
func (dst *CancelOrderResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelOrderResp.Merge(dst, src)
}
func (m *CancelOrderResp) XXX_Size() int {
	return xxx_messageInfo_CancelOrderResp.Size(m)
}
func (m *CancelOrderResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelOrderResp.DiscardUnknown(m)
}

var xxx_messageInfo_CancelOrderResp proto.InternalMessageInfo

type PayCallbackReq struct {
	OrderNo              string   `protobuf:"bytes,1,opt,name=order_no,json=orderNo,proto3" json:"order_no,omitempty"`
	CliOrderNo           string   `protobuf:"bytes,2,opt,name=cli_order_no,json=cliOrderNo,proto3" json:"cli_order_no,omitempty"`
	OtherOrderNo         string   `protobuf:"bytes,3,opt,name=other_order_no,json=otherOrderNo,proto3" json:"other_order_no,omitempty"`
	CoinPayChannel       string   `protobuf:"bytes,4,opt,name=coin_pay_channel,json=coinPayChannel,proto3" json:"coin_pay_channel,omitempty"`
	PayPriceCent         uint32   `protobuf:"varint,5,opt,name=pay_price_cent,json=payPriceCent,proto3" json:"pay_price_cent,omitempty"`
	PayTs                int64    `protobuf:"varint,6,opt,name=pay_ts,json=payTs,proto3" json:"pay_ts,omitempty"`
	ActiveUid            uint32   `protobuf:"varint,7,opt,name=active_uid,json=activeUid,proto3" json:"active_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PayCallbackReq) Reset()         { *m = PayCallbackReq{} }
func (m *PayCallbackReq) String() string { return proto.CompactTextString(m) }
func (*PayCallbackReq) ProtoMessage()    {}
func (*PayCallbackReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_1e8e060b450bf39d, []int{4}
}
func (m *PayCallbackReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PayCallbackReq.Unmarshal(m, b)
}
func (m *PayCallbackReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PayCallbackReq.Marshal(b, m, deterministic)
}
func (dst *PayCallbackReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PayCallbackReq.Merge(dst, src)
}
func (m *PayCallbackReq) XXX_Size() int {
	return xxx_messageInfo_PayCallbackReq.Size(m)
}
func (m *PayCallbackReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PayCallbackReq.DiscardUnknown(m)
}

var xxx_messageInfo_PayCallbackReq proto.InternalMessageInfo

func (m *PayCallbackReq) GetOrderNo() string {
	if m != nil {
		return m.OrderNo
	}
	return ""
}

func (m *PayCallbackReq) GetCliOrderNo() string {
	if m != nil {
		return m.CliOrderNo
	}
	return ""
}

func (m *PayCallbackReq) GetOtherOrderNo() string {
	if m != nil {
		return m.OtherOrderNo
	}
	return ""
}

func (m *PayCallbackReq) GetCoinPayChannel() string {
	if m != nil {
		return m.CoinPayChannel
	}
	return ""
}

func (m *PayCallbackReq) GetPayPriceCent() uint32 {
	if m != nil {
		return m.PayPriceCent
	}
	return 0
}

func (m *PayCallbackReq) GetPayTs() int64 {
	if m != nil {
		return m.PayTs
	}
	return 0
}

func (m *PayCallbackReq) GetActiveUid() uint32 {
	if m != nil {
		return m.ActiveUid
	}
	return 0
}

type PayCallbackResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PayCallbackResp) Reset()         { *m = PayCallbackResp{} }
func (m *PayCallbackResp) String() string { return proto.CompactTextString(m) }
func (*PayCallbackResp) ProtoMessage()    {}
func (*PayCallbackResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_1e8e060b450bf39d, []int{5}
}
func (m *PayCallbackResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PayCallbackResp.Unmarshal(m, b)
}
func (m *PayCallbackResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PayCallbackResp.Marshal(b, m, deterministic)
}
func (dst *PayCallbackResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PayCallbackResp.Merge(dst, src)
}
func (m *PayCallbackResp) XXX_Size() int {
	return xxx_messageInfo_PayCallbackResp.Size(m)
}
func (m *PayCallbackResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PayCallbackResp.DiscardUnknown(m)
}

var xxx_messageInfo_PayCallbackResp proto.InternalMessageInfo

type NotifyContractReq struct {
	ContractId           string   `protobuf:"bytes,1,opt,name=contract_id,json=contractId,proto3" json:"contract_id,omitempty"`
	IsSign               bool     `protobuf:"varint,2,opt,name=is_sign,json=isSign,proto3" json:"is_sign,omitempty"`
	ActiveUid            uint32   `protobuf:"varint,3,opt,name=active_uid,json=activeUid,proto3" json:"active_uid,omitempty"`
	RealBuyerUid         uint32   `protobuf:"varint,4,opt,name=real_buyer_uid,json=realBuyerUid,proto3" json:"real_buyer_uid,omitempty"`
	ProductId            string   `protobuf:"bytes,5,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"`
	NextPayTs            int64    `protobuf:"varint,6,opt,name=next_pay_ts,json=nextPayTs,proto3" json:"next_pay_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NotifyContractReq) Reset()         { *m = NotifyContractReq{} }
func (m *NotifyContractReq) String() string { return proto.CompactTextString(m) }
func (*NotifyContractReq) ProtoMessage()    {}
func (*NotifyContractReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_1e8e060b450bf39d, []int{6}
}
func (m *NotifyContractReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NotifyContractReq.Unmarshal(m, b)
}
func (m *NotifyContractReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NotifyContractReq.Marshal(b, m, deterministic)
}
func (dst *NotifyContractReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NotifyContractReq.Merge(dst, src)
}
func (m *NotifyContractReq) XXX_Size() int {
	return xxx_messageInfo_NotifyContractReq.Size(m)
}
func (m *NotifyContractReq) XXX_DiscardUnknown() {
	xxx_messageInfo_NotifyContractReq.DiscardUnknown(m)
}

var xxx_messageInfo_NotifyContractReq proto.InternalMessageInfo

func (m *NotifyContractReq) GetContractId() string {
	if m != nil {
		return m.ContractId
	}
	return ""
}

func (m *NotifyContractReq) GetIsSign() bool {
	if m != nil {
		return m.IsSign
	}
	return false
}

func (m *NotifyContractReq) GetActiveUid() uint32 {
	if m != nil {
		return m.ActiveUid
	}
	return 0
}

func (m *NotifyContractReq) GetRealBuyerUid() uint32 {
	if m != nil {
		return m.RealBuyerUid
	}
	return 0
}

func (m *NotifyContractReq) GetProductId() string {
	if m != nil {
		return m.ProductId
	}
	return ""
}

func (m *NotifyContractReq) GetNextPayTs() int64 {
	if m != nil {
		return m.NextPayTs
	}
	return 0
}

type NotifyContractResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NotifyContractResp) Reset()         { *m = NotifyContractResp{} }
func (m *NotifyContractResp) String() string { return proto.CompactTextString(m) }
func (*NotifyContractResp) ProtoMessage()    {}
func (*NotifyContractResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_1e8e060b450bf39d, []int{7}
}
func (m *NotifyContractResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NotifyContractResp.Unmarshal(m, b)
}
func (m *NotifyContractResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NotifyContractResp.Marshal(b, m, deterministic)
}
func (dst *NotifyContractResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NotifyContractResp.Merge(dst, src)
}
func (m *NotifyContractResp) XXX_Size() int {
	return xxx_messageInfo_NotifyContractResp.Size(m)
}
func (m *NotifyContractResp) XXX_DiscardUnknown() {
	xxx_messageInfo_NotifyContractResp.DiscardUnknown(m)
}

var xxx_messageInfo_NotifyContractResp proto.InternalMessageInfo

type PlaceAutoPayOrderReq struct {
	ContractId           string   `protobuf:"bytes,1,opt,name=contract_id,json=contractId,proto3" json:"contract_id,omitempty"`
	ProductId            string   `protobuf:"bytes,2,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PlaceAutoPayOrderReq) Reset()         { *m = PlaceAutoPayOrderReq{} }
func (m *PlaceAutoPayOrderReq) String() string { return proto.CompactTextString(m) }
func (*PlaceAutoPayOrderReq) ProtoMessage()    {}
func (*PlaceAutoPayOrderReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_1e8e060b450bf39d, []int{8}
}
func (m *PlaceAutoPayOrderReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PlaceAutoPayOrderReq.Unmarshal(m, b)
}
func (m *PlaceAutoPayOrderReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PlaceAutoPayOrderReq.Marshal(b, m, deterministic)
}
func (dst *PlaceAutoPayOrderReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PlaceAutoPayOrderReq.Merge(dst, src)
}
func (m *PlaceAutoPayOrderReq) XXX_Size() int {
	return xxx_messageInfo_PlaceAutoPayOrderReq.Size(m)
}
func (m *PlaceAutoPayOrderReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PlaceAutoPayOrderReq.DiscardUnknown(m)
}

var xxx_messageInfo_PlaceAutoPayOrderReq proto.InternalMessageInfo

func (m *PlaceAutoPayOrderReq) GetContractId() string {
	if m != nil {
		return m.ContractId
	}
	return ""
}

func (m *PlaceAutoPayOrderReq) GetProductId() string {
	if m != nil {
		return m.ProductId
	}
	return ""
}

type PlaceAutoPayOrderResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PlaceAutoPayOrderResp) Reset()         { *m = PlaceAutoPayOrderResp{} }
func (m *PlaceAutoPayOrderResp) String() string { return proto.CompactTextString(m) }
func (*PlaceAutoPayOrderResp) ProtoMessage()    {}
func (*PlaceAutoPayOrderResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_1e8e060b450bf39d, []int{9}
}
func (m *PlaceAutoPayOrderResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PlaceAutoPayOrderResp.Unmarshal(m, b)
}
func (m *PlaceAutoPayOrderResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PlaceAutoPayOrderResp.Marshal(b, m, deterministic)
}
func (dst *PlaceAutoPayOrderResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PlaceAutoPayOrderResp.Merge(dst, src)
}
func (m *PlaceAutoPayOrderResp) XXX_Size() int {
	return xxx_messageInfo_PlaceAutoPayOrderResp.Size(m)
}
func (m *PlaceAutoPayOrderResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PlaceAutoPayOrderResp.DiscardUnknown(m)
}

var xxx_messageInfo_PlaceAutoPayOrderResp proto.InternalMessageInfo

// 订单退款
type RevokeOrderReq struct {
	OrderId              string   `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	NotifyTime           string   `protobuf:"bytes,2,opt,name=notify_time,json=notifyTime,proto3" json:"notify_time,omitempty"`
	Uid                  uint32   `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RevokeOrderReq) Reset()         { *m = RevokeOrderReq{} }
func (m *RevokeOrderReq) String() string { return proto.CompactTextString(m) }
func (*RevokeOrderReq) ProtoMessage()    {}
func (*RevokeOrderReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_1e8e060b450bf39d, []int{10}
}
func (m *RevokeOrderReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RevokeOrderReq.Unmarshal(m, b)
}
func (m *RevokeOrderReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RevokeOrderReq.Marshal(b, m, deterministic)
}
func (dst *RevokeOrderReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RevokeOrderReq.Merge(dst, src)
}
func (m *RevokeOrderReq) XXX_Size() int {
	return xxx_messageInfo_RevokeOrderReq.Size(m)
}
func (m *RevokeOrderReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RevokeOrderReq.DiscardUnknown(m)
}

var xxx_messageInfo_RevokeOrderReq proto.InternalMessageInfo

func (m *RevokeOrderReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *RevokeOrderReq) GetNotifyTime() string {
	if m != nil {
		return m.NotifyTime
	}
	return ""
}

func (m *RevokeOrderReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type RevokeOrderResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RevokeOrderResp) Reset()         { *m = RevokeOrderResp{} }
func (m *RevokeOrderResp) String() string { return proto.CompactTextString(m) }
func (*RevokeOrderResp) ProtoMessage()    {}
func (*RevokeOrderResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_1e8e060b450bf39d, []int{11}
}
func (m *RevokeOrderResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RevokeOrderResp.Unmarshal(m, b)
}
func (m *RevokeOrderResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RevokeOrderResp.Marshal(b, m, deterministic)
}
func (dst *RevokeOrderResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RevokeOrderResp.Merge(dst, src)
}
func (m *RevokeOrderResp) XXX_Size() int {
	return xxx_messageInfo_RevokeOrderResp.Size(m)
}
func (m *RevokeOrderResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RevokeOrderResp.DiscardUnknown(m)
}

var xxx_messageInfo_RevokeOrderResp proto.InternalMessageInfo

type GenerateStatReq struct {
	// 没有严格1号开始/结束，留点tricky测试想象
	StartTime            string   `protobuf:"bytes,1,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime              string   `protobuf:"bytes,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GenerateStatReq) Reset()         { *m = GenerateStatReq{} }
func (m *GenerateStatReq) String() string { return proto.CompactTextString(m) }
func (*GenerateStatReq) ProtoMessage()    {}
func (*GenerateStatReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_1e8e060b450bf39d, []int{12}
}
func (m *GenerateStatReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GenerateStatReq.Unmarshal(m, b)
}
func (m *GenerateStatReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GenerateStatReq.Marshal(b, m, deterministic)
}
func (dst *GenerateStatReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GenerateStatReq.Merge(dst, src)
}
func (m *GenerateStatReq) XXX_Size() int {
	return xxx_messageInfo_GenerateStatReq.Size(m)
}
func (m *GenerateStatReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GenerateStatReq.DiscardUnknown(m)
}

var xxx_messageInfo_GenerateStatReq proto.InternalMessageInfo

func (m *GenerateStatReq) GetStartTime() string {
	if m != nil {
		return m.StartTime
	}
	return ""
}

func (m *GenerateStatReq) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

type GenerateStatResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GenerateStatResp) Reset()         { *m = GenerateStatResp{} }
func (m *GenerateStatResp) String() string { return proto.CompactTextString(m) }
func (*GenerateStatResp) ProtoMessage()    {}
func (*GenerateStatResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_1e8e060b450bf39d, []int{13}
}
func (m *GenerateStatResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GenerateStatResp.Unmarshal(m, b)
}
func (m *GenerateStatResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GenerateStatResp.Marshal(b, m, deterministic)
}
func (dst *GenerateStatResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GenerateStatResp.Merge(dst, src)
}
func (m *GenerateStatResp) XXX_Size() int {
	return xxx_messageInfo_GenerateStatResp.Size(m)
}
func (m *GenerateStatResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GenerateStatResp.DiscardUnknown(m)
}

var xxx_messageInfo_GenerateStatResp proto.InternalMessageInfo

type UserCardInfo struct {
	Uid                  uint32              `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	EffectTs             int64               `protobuf:"varint,2,opt,name=effect_ts,json=effectTs,proto3" json:"effect_ts,omitempty"`
	ExpireTs             int64               `protobuf:"varint,3,opt,name=expire_ts,json=expireTs,proto3" json:"expire_ts,omitempty"`
	Contracts            []*UserContractInfo `protobuf:"bytes,4,rep,name=contracts,proto3" json:"contracts,omitempty"`
	DiscountOrderId      string              `protobuf:"bytes,5,opt,name=discount_order_id,json=discountOrderId,proto3" json:"discount_order_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *UserCardInfo) Reset()         { *m = UserCardInfo{} }
func (m *UserCardInfo) String() string { return proto.CompactTextString(m) }
func (*UserCardInfo) ProtoMessage()    {}
func (*UserCardInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_1e8e060b450bf39d, []int{14}
}
func (m *UserCardInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserCardInfo.Unmarshal(m, b)
}
func (m *UserCardInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserCardInfo.Marshal(b, m, deterministic)
}
func (dst *UserCardInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserCardInfo.Merge(dst, src)
}
func (m *UserCardInfo) XXX_Size() int {
	return xxx_messageInfo_UserCardInfo.Size(m)
}
func (m *UserCardInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserCardInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserCardInfo proto.InternalMessageInfo

func (m *UserCardInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserCardInfo) GetEffectTs() int64 {
	if m != nil {
		return m.EffectTs
	}
	return 0
}

func (m *UserCardInfo) GetExpireTs() int64 {
	if m != nil {
		return m.ExpireTs
	}
	return 0
}

func (m *UserCardInfo) GetContracts() []*UserContractInfo {
	if m != nil {
		return m.Contracts
	}
	return nil
}

func (m *UserCardInfo) GetDiscountOrderId() string {
	if m != nil {
		return m.DiscountOrderId
	}
	return ""
}

type UserContractInfo struct {
	Uid                  uint32     `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ContractId           string     `protobuf:"bytes,2,opt,name=contract_id,json=contractId,proto3" json:"contract_id,omitempty"`
	PayChannel           PayChannel `protobuf:"varint,3,opt,name=pay_channel,json=payChannel,proto3,enum=virtual_image_card.PayChannel" json:"pay_channel,omitempty"`
	NextPayTs            int64      `protobuf:"varint,4,opt,name=next_pay_ts,json=nextPayTs,proto3" json:"next_pay_ts,omitempty"`
	CreateTs             int64      `protobuf:"varint,5,opt,name=create_ts,json=createTs,proto3" json:"create_ts,omitempty"`
	Package              *Package   `protobuf:"bytes,6,opt,name=package,proto3" json:"package,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *UserContractInfo) Reset()         { *m = UserContractInfo{} }
func (m *UserContractInfo) String() string { return proto.CompactTextString(m) }
func (*UserContractInfo) ProtoMessage()    {}
func (*UserContractInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_1e8e060b450bf39d, []int{15}
}
func (m *UserContractInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserContractInfo.Unmarshal(m, b)
}
func (m *UserContractInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserContractInfo.Marshal(b, m, deterministic)
}
func (dst *UserContractInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserContractInfo.Merge(dst, src)
}
func (m *UserContractInfo) XXX_Size() int {
	return xxx_messageInfo_UserContractInfo.Size(m)
}
func (m *UserContractInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserContractInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserContractInfo proto.InternalMessageInfo

func (m *UserContractInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserContractInfo) GetContractId() string {
	if m != nil {
		return m.ContractId
	}
	return ""
}

func (m *UserContractInfo) GetPayChannel() PayChannel {
	if m != nil {
		return m.PayChannel
	}
	return PayChannel_PAY_CHANNEL_UNSPECIFIED
}

func (m *UserContractInfo) GetNextPayTs() int64 {
	if m != nil {
		return m.NextPayTs
	}
	return 0
}

func (m *UserContractInfo) GetCreateTs() int64 {
	if m != nil {
		return m.CreateTs
	}
	return 0
}

func (m *UserContractInfo) GetPackage() *Package {
	if m != nil {
		return m.Package
	}
	return nil
}

type GetUserCardInfoReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserCardInfoReq) Reset()         { *m = GetUserCardInfoReq{} }
func (m *GetUserCardInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetUserCardInfoReq) ProtoMessage()    {}
func (*GetUserCardInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_1e8e060b450bf39d, []int{16}
}
func (m *GetUserCardInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserCardInfoReq.Unmarshal(m, b)
}
func (m *GetUserCardInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserCardInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetUserCardInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserCardInfoReq.Merge(dst, src)
}
func (m *GetUserCardInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetUserCardInfoReq.Size(m)
}
func (m *GetUserCardInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserCardInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserCardInfoReq proto.InternalMessageInfo

func (m *GetUserCardInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserCardInfoResp struct {
	Card                      *UserCardInfo `protobuf:"bytes,1,opt,name=card,proto3" json:"card,omitempty"`
	ExpireAlertStatusKeepHour uint32        `protobuf:"varint,2,opt,name=expire_alert_status_keep_hour,json=expireAlertStatusKeepHour,proto3" json:"expire_alert_status_keep_hour,omitempty"`
	XXX_NoUnkeyedLiteral      struct{}      `json:"-"`
	XXX_unrecognized          []byte        `json:"-"`
	XXX_sizecache             int32         `json:"-"`
}

func (m *GetUserCardInfoResp) Reset()         { *m = GetUserCardInfoResp{} }
func (m *GetUserCardInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetUserCardInfoResp) ProtoMessage()    {}
func (*GetUserCardInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_1e8e060b450bf39d, []int{17}
}
func (m *GetUserCardInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserCardInfoResp.Unmarshal(m, b)
}
func (m *GetUserCardInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserCardInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetUserCardInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserCardInfoResp.Merge(dst, src)
}
func (m *GetUserCardInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetUserCardInfoResp.Size(m)
}
func (m *GetUserCardInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserCardInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserCardInfoResp proto.InternalMessageInfo

func (m *GetUserCardInfoResp) GetCard() *UserCardInfo {
	if m != nil {
		return m.Card
	}
	return nil
}

func (m *GetUserCardInfoResp) GetExpireAlertStatusKeepHour() uint32 {
	if m != nil {
		return m.ExpireAlertStatusKeepHour
	}
	return 0
}

type GetUserRedemptionInfoReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserRedemptionInfoReq) Reset()         { *m = GetUserRedemptionInfoReq{} }
func (m *GetUserRedemptionInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetUserRedemptionInfoReq) ProtoMessage()    {}
func (*GetUserRedemptionInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_1e8e060b450bf39d, []int{18}
}
func (m *GetUserRedemptionInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserRedemptionInfoReq.Unmarshal(m, b)
}
func (m *GetUserRedemptionInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserRedemptionInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetUserRedemptionInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserRedemptionInfoReq.Merge(dst, src)
}
func (m *GetUserRedemptionInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetUserRedemptionInfoReq.Size(m)
}
func (m *GetUserRedemptionInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserRedemptionInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserRedemptionInfoReq proto.InternalMessageInfo

func (m *GetUserRedemptionInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserRedemptionInfoResp struct {
	HasSingleBuyOrderWaitingRedemption bool     `protobuf:"varint,1,opt,name=has_single_buy_order_waiting_redemption,json=hasSingleBuyOrderWaitingRedemption,proto3" json:"has_single_buy_order_waiting_redemption,omitempty"`
	XXX_NoUnkeyedLiteral               struct{} `json:"-"`
	XXX_unrecognized                   []byte   `json:"-"`
	XXX_sizecache                      int32    `json:"-"`
}

func (m *GetUserRedemptionInfoResp) Reset()         { *m = GetUserRedemptionInfoResp{} }
func (m *GetUserRedemptionInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetUserRedemptionInfoResp) ProtoMessage()    {}
func (*GetUserRedemptionInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_1e8e060b450bf39d, []int{19}
}
func (m *GetUserRedemptionInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserRedemptionInfoResp.Unmarshal(m, b)
}
func (m *GetUserRedemptionInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserRedemptionInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetUserRedemptionInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserRedemptionInfoResp.Merge(dst, src)
}
func (m *GetUserRedemptionInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetUserRedemptionInfoResp.Size(m)
}
func (m *GetUserRedemptionInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserRedemptionInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserRedemptionInfoResp proto.InternalMessageInfo

func (m *GetUserRedemptionInfoResp) GetHasSingleBuyOrderWaitingRedemption() bool {
	if m != nil {
		return m.HasSingleBuyOrderWaitingRedemption
	}
	return false
}

type GetUserPackageListReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	MarketId             uint32   `protobuf:"varint,2,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	ClientType           uint32   `protobuf:"varint,3,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserPackageListReq) Reset()         { *m = GetUserPackageListReq{} }
func (m *GetUserPackageListReq) String() string { return proto.CompactTextString(m) }
func (*GetUserPackageListReq) ProtoMessage()    {}
func (*GetUserPackageListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_1e8e060b450bf39d, []int{20}
}
func (m *GetUserPackageListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserPackageListReq.Unmarshal(m, b)
}
func (m *GetUserPackageListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserPackageListReq.Marshal(b, m, deterministic)
}
func (dst *GetUserPackageListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserPackageListReq.Merge(dst, src)
}
func (m *GetUserPackageListReq) XXX_Size() int {
	return xxx_messageInfo_GetUserPackageListReq.Size(m)
}
func (m *GetUserPackageListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserPackageListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserPackageListReq proto.InternalMessageInfo

func (m *GetUserPackageListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserPackageListReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *GetUserPackageListReq) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

type GetUserPackageListResp struct {
	PackageList          []*UserPackage `protobuf:"bytes,1,rep,name=package_list,json=packageList,proto3" json:"package_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetUserPackageListResp) Reset()         { *m = GetUserPackageListResp{} }
func (m *GetUserPackageListResp) String() string { return proto.CompactTextString(m) }
func (*GetUserPackageListResp) ProtoMessage()    {}
func (*GetUserPackageListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_1e8e060b450bf39d, []int{21}
}
func (m *GetUserPackageListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserPackageListResp.Unmarshal(m, b)
}
func (m *GetUserPackageListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserPackageListResp.Marshal(b, m, deterministic)
}
func (dst *GetUserPackageListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserPackageListResp.Merge(dst, src)
}
func (m *GetUserPackageListResp) XXX_Size() int {
	return xxx_messageInfo_GetUserPackageListResp.Size(m)
}
func (m *GetUserPackageListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserPackageListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserPackageListResp proto.InternalMessageInfo

func (m *GetUserPackageListResp) GetPackageList() []*UserPackage {
	if m != nil {
		return m.PackageList
	}
	return nil
}

type UserPackage struct {
	PackageId            uint32            `protobuf:"varint,1,opt,name=package_id,json=packageId,proto3" json:"package_id,omitempty"`
	Name                 string            `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Desc                 string            `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
	CurrentPriceCent     uint32            `protobuf:"varint,4,opt,name=current_price_cent,json=currentPriceCent,proto3" json:"current_price_cent,omitempty"`
	OriginalPriceCent    uint32            `protobuf:"varint,5,opt,name=original_price_cent,json=originalPriceCent,proto3" json:"original_price_cent,omitempty"`
	DiscountLabel        string            `protobuf:"bytes,6,opt,name=discount_label,json=discountLabel,proto3" json:"discount_label,omitempty"`
	DailyPriceCent       uint32            `protobuf:"varint,7,opt,name=daily_price_cent,json=dailyPriceCent,proto3" json:"daily_price_cent,omitempty"`
	PayPriceCent         uint32            `protobuf:"varint,8,opt,name=pay_price_cent,json=payPriceCent,proto3" json:"pay_price_cent,omitempty"`
	Explanation          string            `protobuf:"bytes,9,opt,name=explanation,proto3" json:"explanation,omitempty"`
	IsAuto               bool              `protobuf:"varint,10,opt,name=is_auto,json=isAuto,proto3" json:"is_auto,omitempty"`
	PayChannelList       []PayChannel      `protobuf:"varint,11,rep,packed,name=pay_channel_list,json=payChannelList,proto3,enum=virtual_image_card.PayChannel" json:"pay_channel_list,omitempty"`
	ProductId            string            `protobuf:"bytes,12,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"`
	ShowCondition        *DisplayCondition `protobuf:"bytes,13,opt,name=show_condition,json=showCondition,proto3" json:"show_condition,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *UserPackage) Reset()         { *m = UserPackage{} }
func (m *UserPackage) String() string { return proto.CompactTextString(m) }
func (*UserPackage) ProtoMessage()    {}
func (*UserPackage) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_1e8e060b450bf39d, []int{22}
}
func (m *UserPackage) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserPackage.Unmarshal(m, b)
}
func (m *UserPackage) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserPackage.Marshal(b, m, deterministic)
}
func (dst *UserPackage) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserPackage.Merge(dst, src)
}
func (m *UserPackage) XXX_Size() int {
	return xxx_messageInfo_UserPackage.Size(m)
}
func (m *UserPackage) XXX_DiscardUnknown() {
	xxx_messageInfo_UserPackage.DiscardUnknown(m)
}

var xxx_messageInfo_UserPackage proto.InternalMessageInfo

func (m *UserPackage) GetPackageId() uint32 {
	if m != nil {
		return m.PackageId
	}
	return 0
}

func (m *UserPackage) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *UserPackage) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *UserPackage) GetCurrentPriceCent() uint32 {
	if m != nil {
		return m.CurrentPriceCent
	}
	return 0
}

func (m *UserPackage) GetOriginalPriceCent() uint32 {
	if m != nil {
		return m.OriginalPriceCent
	}
	return 0
}

func (m *UserPackage) GetDiscountLabel() string {
	if m != nil {
		return m.DiscountLabel
	}
	return ""
}

func (m *UserPackage) GetDailyPriceCent() uint32 {
	if m != nil {
		return m.DailyPriceCent
	}
	return 0
}

func (m *UserPackage) GetPayPriceCent() uint32 {
	if m != nil {
		return m.PayPriceCent
	}
	return 0
}

func (m *UserPackage) GetExplanation() string {
	if m != nil {
		return m.Explanation
	}
	return ""
}

func (m *UserPackage) GetIsAuto() bool {
	if m != nil {
		return m.IsAuto
	}
	return false
}

func (m *UserPackage) GetPayChannelList() []PayChannel {
	if m != nil {
		return m.PayChannelList
	}
	return nil
}

func (m *UserPackage) GetProductId() string {
	if m != nil {
		return m.ProductId
	}
	return ""
}

func (m *UserPackage) GetShowCondition() *DisplayCondition {
	if m != nil {
		return m.ShowCondition
	}
	return nil
}

type GetPurchaseHistoryReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Page                 uint32   `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	PageSize             uint32   `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPurchaseHistoryReq) Reset()         { *m = GetPurchaseHistoryReq{} }
func (m *GetPurchaseHistoryReq) String() string { return proto.CompactTextString(m) }
func (*GetPurchaseHistoryReq) ProtoMessage()    {}
func (*GetPurchaseHistoryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_1e8e060b450bf39d, []int{23}
}
func (m *GetPurchaseHistoryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPurchaseHistoryReq.Unmarshal(m, b)
}
func (m *GetPurchaseHistoryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPurchaseHistoryReq.Marshal(b, m, deterministic)
}
func (dst *GetPurchaseHistoryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPurchaseHistoryReq.Merge(dst, src)
}
func (m *GetPurchaseHistoryReq) XXX_Size() int {
	return xxx_messageInfo_GetPurchaseHistoryReq.Size(m)
}
func (m *GetPurchaseHistoryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPurchaseHistoryReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPurchaseHistoryReq proto.InternalMessageInfo

func (m *GetPurchaseHistoryReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetPurchaseHistoryReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetPurchaseHistoryReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type GetPurchaseHistoryResp struct {
	PurchaseList         []*PurchaseRecord `protobuf:"bytes,1,rep,name=purchase_list,json=purchaseList,proto3" json:"purchase_list,omitempty"`
	HasMore              bool              `protobuf:"varint,2,opt,name=has_more,json=hasMore,proto3" json:"has_more,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetPurchaseHistoryResp) Reset()         { *m = GetPurchaseHistoryResp{} }
func (m *GetPurchaseHistoryResp) String() string { return proto.CompactTextString(m) }
func (*GetPurchaseHistoryResp) ProtoMessage()    {}
func (*GetPurchaseHistoryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_1e8e060b450bf39d, []int{24}
}
func (m *GetPurchaseHistoryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPurchaseHistoryResp.Unmarshal(m, b)
}
func (m *GetPurchaseHistoryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPurchaseHistoryResp.Marshal(b, m, deterministic)
}
func (dst *GetPurchaseHistoryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPurchaseHistoryResp.Merge(dst, src)
}
func (m *GetPurchaseHistoryResp) XXX_Size() int {
	return xxx_messageInfo_GetPurchaseHistoryResp.Size(m)
}
func (m *GetPurchaseHistoryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPurchaseHistoryResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPurchaseHistoryResp proto.InternalMessageInfo

func (m *GetPurchaseHistoryResp) GetPurchaseList() []*PurchaseRecord {
	if m != nil {
		return m.PurchaseList
	}
	return nil
}

func (m *GetPurchaseHistoryResp) GetHasMore() bool {
	if m != nil {
		return m.HasMore
	}
	return false
}

type PurchaseRecord struct {
	OrderId              string   `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	Time                 string   `protobuf:"bytes,2,opt,name=time,proto3" json:"time,omitempty"`
	Desc                 string   `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
	Price                string   `protobuf:"bytes,4,opt,name=price,proto3" json:"price,omitempty"`
	HasRefunded          bool     `protobuf:"varint,5,opt,name=has_refunded,json=hasRefunded,proto3" json:"has_refunded,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PurchaseRecord) Reset()         { *m = PurchaseRecord{} }
func (m *PurchaseRecord) String() string { return proto.CompactTextString(m) }
func (*PurchaseRecord) ProtoMessage()    {}
func (*PurchaseRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_1e8e060b450bf39d, []int{25}
}
func (m *PurchaseRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PurchaseRecord.Unmarshal(m, b)
}
func (m *PurchaseRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PurchaseRecord.Marshal(b, m, deterministic)
}
func (dst *PurchaseRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PurchaseRecord.Merge(dst, src)
}
func (m *PurchaseRecord) XXX_Size() int {
	return xxx_messageInfo_PurchaseRecord.Size(m)
}
func (m *PurchaseRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_PurchaseRecord.DiscardUnknown(m)
}

var xxx_messageInfo_PurchaseRecord proto.InternalMessageInfo

func (m *PurchaseRecord) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *PurchaseRecord) GetTime() string {
	if m != nil {
		return m.Time
	}
	return ""
}

func (m *PurchaseRecord) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *PurchaseRecord) GetPrice() string {
	if m != nil {
		return m.Price
	}
	return ""
}

func (m *PurchaseRecord) GetHasRefunded() bool {
	if m != nil {
		return m.HasRefunded
	}
	return false
}

// 套餐
type Package struct {
	Id                   uint32      `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	ProductId            string      `protobuf:"bytes,2,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"`
	Name                 string      `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Desc                 string      `protobuf:"bytes,4,opt,name=desc,proto3" json:"desc,omitempty"`
	Days                 uint32      `protobuf:"varint,5,opt,name=days,proto3" json:"days,omitempty"`
	OriginalPriceCent    uint32      `protobuf:"varint,6,opt,name=original_price_cent,json=originalPriceCent,proto3" json:"original_price_cent,omitempty"`
	PriceCent            uint32      `protobuf:"varint,7,opt,name=price_cent,json=priceCent,proto3" json:"price_cent,omitempty"`
	IsEnabled            bool        `protobuf:"varint,8,opt,name=is_enabled,json=isEnabled,proto3" json:"is_enabled,omitempty"`
	Operator             string      `protobuf:"bytes,9,opt,name=operator,proto3" json:"operator,omitempty"`
	MarketId             uint32      `protobuf:"varint,10,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	GroupId              uint32      `protobuf:"varint,11,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	UpdateTs             uint64      `protobuf:"varint,12,opt,name=update_ts,json=updateTs,proto3" json:"update_ts,omitempty"`
	PackageType          PackageType `protobuf:"varint,13,opt,name=package_type,json=packageType,proto3,enum=virtual_image_card.PackageType" json:"package_type,omitempty"`
	DiscountPriceCent    uint32      `protobuf:"varint,14,opt,name=discount_price_cent,json=discountPriceCent,proto3" json:"discount_price_cent,omitempty"`
	DiscountLabel        string      `protobuf:"bytes,15,opt,name=discount_label,json=discountLabel,proto3" json:"discount_label,omitempty"`
	DiscountDesc         string      `protobuf:"bytes,16,opt,name=discount_desc,json=discountDesc,proto3" json:"discount_desc,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *Package) Reset()         { *m = Package{} }
func (m *Package) String() string { return proto.CompactTextString(m) }
func (*Package) ProtoMessage()    {}
func (*Package) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_1e8e060b450bf39d, []int{26}
}
func (m *Package) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Package.Unmarshal(m, b)
}
func (m *Package) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Package.Marshal(b, m, deterministic)
}
func (dst *Package) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Package.Merge(dst, src)
}
func (m *Package) XXX_Size() int {
	return xxx_messageInfo_Package.Size(m)
}
func (m *Package) XXX_DiscardUnknown() {
	xxx_messageInfo_Package.DiscardUnknown(m)
}

var xxx_messageInfo_Package proto.InternalMessageInfo

func (m *Package) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *Package) GetProductId() string {
	if m != nil {
		return m.ProductId
	}
	return ""
}

func (m *Package) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *Package) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *Package) GetDays() uint32 {
	if m != nil {
		return m.Days
	}
	return 0
}

func (m *Package) GetOriginalPriceCent() uint32 {
	if m != nil {
		return m.OriginalPriceCent
	}
	return 0
}

func (m *Package) GetPriceCent() uint32 {
	if m != nil {
		return m.PriceCent
	}
	return 0
}

func (m *Package) GetIsEnabled() bool {
	if m != nil {
		return m.IsEnabled
	}
	return false
}

func (m *Package) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *Package) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *Package) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *Package) GetUpdateTs() uint64 {
	if m != nil {
		return m.UpdateTs
	}
	return 0
}

func (m *Package) GetPackageType() PackageType {
	if m != nil {
		return m.PackageType
	}
	return PackageType_PACKAGE_TYPE_UNSPECIFIED
}

func (m *Package) GetDiscountPriceCent() uint32 {
	if m != nil {
		return m.DiscountPriceCent
	}
	return 0
}

func (m *Package) GetDiscountLabel() string {
	if m != nil {
		return m.DiscountLabel
	}
	return ""
}

func (m *Package) GetDiscountDesc() string {
	if m != nil {
		return m.DiscountDesc
	}
	return ""
}

// 增加套餐配置
type AddPackageReq struct {
	PackageInfo          *Package `protobuf:"bytes,1,opt,name=package_info,json=packageInfo,proto3" json:"package_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddPackageReq) Reset()         { *m = AddPackageReq{} }
func (m *AddPackageReq) String() string { return proto.CompactTextString(m) }
func (*AddPackageReq) ProtoMessage()    {}
func (*AddPackageReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_1e8e060b450bf39d, []int{27}
}
func (m *AddPackageReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddPackageReq.Unmarshal(m, b)
}
func (m *AddPackageReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddPackageReq.Marshal(b, m, deterministic)
}
func (dst *AddPackageReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddPackageReq.Merge(dst, src)
}
func (m *AddPackageReq) XXX_Size() int {
	return xxx_messageInfo_AddPackageReq.Size(m)
}
func (m *AddPackageReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddPackageReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddPackageReq proto.InternalMessageInfo

func (m *AddPackageReq) GetPackageInfo() *Package {
	if m != nil {
		return m.PackageInfo
	}
	return nil
}

type AddPackageResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddPackageResp) Reset()         { *m = AddPackageResp{} }
func (m *AddPackageResp) String() string { return proto.CompactTextString(m) }
func (*AddPackageResp) ProtoMessage()    {}
func (*AddPackageResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_1e8e060b450bf39d, []int{28}
}
func (m *AddPackageResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddPackageResp.Unmarshal(m, b)
}
func (m *AddPackageResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddPackageResp.Marshal(b, m, deterministic)
}
func (dst *AddPackageResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddPackageResp.Merge(dst, src)
}
func (m *AddPackageResp) XXX_Size() int {
	return xxx_messageInfo_AddPackageResp.Size(m)
}
func (m *AddPackageResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddPackageResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddPackageResp proto.InternalMessageInfo

// 更新套餐状态
type UpdatePackageStatusReq struct {
	IsEnabled            bool     `protobuf:"varint,1,opt,name=is_enabled,json=isEnabled,proto3" json:"is_enabled,omitempty"`
	Id                   uint32   `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	IsGroup              bool     `protobuf:"varint,3,opt,name=is_group,json=isGroup,proto3" json:"is_group,omitempty"`
	Operator             string   `protobuf:"bytes,4,opt,name=operator,proto3" json:"operator,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdatePackageStatusReq) Reset()         { *m = UpdatePackageStatusReq{} }
func (m *UpdatePackageStatusReq) String() string { return proto.CompactTextString(m) }
func (*UpdatePackageStatusReq) ProtoMessage()    {}
func (*UpdatePackageStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_1e8e060b450bf39d, []int{29}
}
func (m *UpdatePackageStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdatePackageStatusReq.Unmarshal(m, b)
}
func (m *UpdatePackageStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdatePackageStatusReq.Marshal(b, m, deterministic)
}
func (dst *UpdatePackageStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdatePackageStatusReq.Merge(dst, src)
}
func (m *UpdatePackageStatusReq) XXX_Size() int {
	return xxx_messageInfo_UpdatePackageStatusReq.Size(m)
}
func (m *UpdatePackageStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdatePackageStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdatePackageStatusReq proto.InternalMessageInfo

func (m *UpdatePackageStatusReq) GetIsEnabled() bool {
	if m != nil {
		return m.IsEnabled
	}
	return false
}

func (m *UpdatePackageStatusReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *UpdatePackageStatusReq) GetIsGroup() bool {
	if m != nil {
		return m.IsGroup
	}
	return false
}

func (m *UpdatePackageStatusReq) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

type UpdatePackageStatusResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdatePackageStatusResp) Reset()         { *m = UpdatePackageStatusResp{} }
func (m *UpdatePackageStatusResp) String() string { return proto.CompactTextString(m) }
func (*UpdatePackageStatusResp) ProtoMessage()    {}
func (*UpdatePackageStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_1e8e060b450bf39d, []int{30}
}
func (m *UpdatePackageStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdatePackageStatusResp.Unmarshal(m, b)
}
func (m *UpdatePackageStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdatePackageStatusResp.Marshal(b, m, deterministic)
}
func (dst *UpdatePackageStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdatePackageStatusResp.Merge(dst, src)
}
func (m *UpdatePackageStatusResp) XXX_Size() int {
	return xxx_messageInfo_UpdatePackageStatusResp.Size(m)
}
func (m *UpdatePackageStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdatePackageStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdatePackageStatusResp proto.InternalMessageInfo

// 获取套餐列表
type GetPackageListByStatusReq struct {
	IsEnabled            bool        `protobuf:"varint,1,opt,name=is_enabled,json=isEnabled,proto3" json:"is_enabled,omitempty"`
	PackageType          PackageType `protobuf:"varint,2,opt,name=package_type,json=packageType,proto3,enum=virtual_image_card.PackageType" json:"package_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetPackageListByStatusReq) Reset()         { *m = GetPackageListByStatusReq{} }
func (m *GetPackageListByStatusReq) String() string { return proto.CompactTextString(m) }
func (*GetPackageListByStatusReq) ProtoMessage()    {}
func (*GetPackageListByStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_1e8e060b450bf39d, []int{31}
}
func (m *GetPackageListByStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPackageListByStatusReq.Unmarshal(m, b)
}
func (m *GetPackageListByStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPackageListByStatusReq.Marshal(b, m, deterministic)
}
func (dst *GetPackageListByStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPackageListByStatusReq.Merge(dst, src)
}
func (m *GetPackageListByStatusReq) XXX_Size() int {
	return xxx_messageInfo_GetPackageListByStatusReq.Size(m)
}
func (m *GetPackageListByStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPackageListByStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPackageListByStatusReq proto.InternalMessageInfo

func (m *GetPackageListByStatusReq) GetIsEnabled() bool {
	if m != nil {
		return m.IsEnabled
	}
	return false
}

func (m *GetPackageListByStatusReq) GetPackageType() PackageType {
	if m != nil {
		return m.PackageType
	}
	return PackageType_PACKAGE_TYPE_UNSPECIFIED
}

type MixPackage struct {
	IsGroup              bool          `protobuf:"varint,1,opt,name=is_group,json=isGroup,proto3" json:"is_group,omitempty"`
	PackageInfo          *Package      `protobuf:"bytes,2,opt,name=package_info,json=packageInfo,proto3" json:"package_info,omitempty"`
	GroupPackage         *GroupPackage `protobuf:"bytes,3,opt,name=group_package,json=groupPackage,proto3" json:"group_package,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *MixPackage) Reset()         { *m = MixPackage{} }
func (m *MixPackage) String() string { return proto.CompactTextString(m) }
func (*MixPackage) ProtoMessage()    {}
func (*MixPackage) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_1e8e060b450bf39d, []int{32}
}
func (m *MixPackage) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MixPackage.Unmarshal(m, b)
}
func (m *MixPackage) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MixPackage.Marshal(b, m, deterministic)
}
func (dst *MixPackage) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MixPackage.Merge(dst, src)
}
func (m *MixPackage) XXX_Size() int {
	return xxx_messageInfo_MixPackage.Size(m)
}
func (m *MixPackage) XXX_DiscardUnknown() {
	xxx_messageInfo_MixPackage.DiscardUnknown(m)
}

var xxx_messageInfo_MixPackage proto.InternalMessageInfo

func (m *MixPackage) GetIsGroup() bool {
	if m != nil {
		return m.IsGroup
	}
	return false
}

func (m *MixPackage) GetPackageInfo() *Package {
	if m != nil {
		return m.PackageInfo
	}
	return nil
}

func (m *MixPackage) GetGroupPackage() *GroupPackage {
	if m != nil {
		return m.GroupPackage
	}
	return nil
}

type GetPackageListByStatusResp struct {
	PackageList          []*MixPackage `protobuf:"bytes,1,rep,name=package_list,json=packageList,proto3" json:"package_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetPackageListByStatusResp) Reset()         { *m = GetPackageListByStatusResp{} }
func (m *GetPackageListByStatusResp) String() string { return proto.CompactTextString(m) }
func (*GetPackageListByStatusResp) ProtoMessage()    {}
func (*GetPackageListByStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_1e8e060b450bf39d, []int{33}
}
func (m *GetPackageListByStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPackageListByStatusResp.Unmarshal(m, b)
}
func (m *GetPackageListByStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPackageListByStatusResp.Marshal(b, m, deterministic)
}
func (dst *GetPackageListByStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPackageListByStatusResp.Merge(dst, src)
}
func (m *GetPackageListByStatusResp) XXX_Size() int {
	return xxx_messageInfo_GetPackageListByStatusResp.Size(m)
}
func (m *GetPackageListByStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPackageListByStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPackageListByStatusResp proto.InternalMessageInfo

func (m *GetPackageListByStatusResp) GetPackageList() []*MixPackage {
	if m != nil {
		return m.PackageList
	}
	return nil
}

// 展示条件
type DisplayCondition struct {
	CondType             uint32       `protobuf:"varint,1,opt,name=cond_type,json=condType,proto3" json:"cond_type,omitempty"`
	CardStatus           []CardStatus `protobuf:"varint,2,rep,packed,name=card_status,json=cardStatus,proto3,enum=virtual_image_card.CardStatus" json:"card_status,omitempty"`
	UserLv               uint32       `protobuf:"varint,3,opt,name=user_lv,json=userLv,proto3" json:"user_lv,omitempty"`
	RichLv               uint32       `protobuf:"varint,4,opt,name=rich_lv,json=richLv,proto3" json:"rich_lv,omitempty"`
	NobilityLv           uint32       `protobuf:"varint,5,opt,name=nobility_lv,json=nobilityLv,proto3" json:"nobility_lv,omitempty"`
	PeopleGroupList      []string     `protobuf:"bytes,6,rep,name=people_group_list,json=peopleGroupList,proto3" json:"people_group_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *DisplayCondition) Reset()         { *m = DisplayCondition{} }
func (m *DisplayCondition) String() string { return proto.CompactTextString(m) }
func (*DisplayCondition) ProtoMessage()    {}
func (*DisplayCondition) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_1e8e060b450bf39d, []int{34}
}
func (m *DisplayCondition) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisplayCondition.Unmarshal(m, b)
}
func (m *DisplayCondition) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisplayCondition.Marshal(b, m, deterministic)
}
func (dst *DisplayCondition) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisplayCondition.Merge(dst, src)
}
func (m *DisplayCondition) XXX_Size() int {
	return xxx_messageInfo_DisplayCondition.Size(m)
}
func (m *DisplayCondition) XXX_DiscardUnknown() {
	xxx_messageInfo_DisplayCondition.DiscardUnknown(m)
}

var xxx_messageInfo_DisplayCondition proto.InternalMessageInfo

func (m *DisplayCondition) GetCondType() uint32 {
	if m != nil {
		return m.CondType
	}
	return 0
}

func (m *DisplayCondition) GetCardStatus() []CardStatus {
	if m != nil {
		return m.CardStatus
	}
	return nil
}

func (m *DisplayCondition) GetUserLv() uint32 {
	if m != nil {
		return m.UserLv
	}
	return 0
}

func (m *DisplayCondition) GetRichLv() uint32 {
	if m != nil {
		return m.RichLv
	}
	return 0
}

func (m *DisplayCondition) GetNobilityLv() uint32 {
	if m != nil {
		return m.NobilityLv
	}
	return 0
}

func (m *DisplayCondition) GetPeopleGroupList() []string {
	if m != nil {
		return m.PeopleGroupList
	}
	return nil
}

// 在售架的套餐信息
type SalePackage struct {
	SaleId               uint32            `protobuf:"varint,1,opt,name=sale_id,json=saleId,proto3" json:"sale_id,omitempty"`
	PackageInfo          *Package          `protobuf:"bytes,2,opt,name=package_info,json=packageInfo,proto3" json:"package_info,omitempty"`
	BeginTs              uint64            `protobuf:"varint,3,opt,name=begin_ts,json=beginTs,proto3" json:"begin_ts,omitempty"`
	EndTs                uint64            `protobuf:"varint,4,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	Condition            *DisplayCondition `protobuf:"bytes,5,opt,name=condition,proto3" json:"condition,omitempty"`
	Weight               uint32            `protobuf:"varint,6,opt,name=weight,proto3" json:"weight,omitempty"`
	SaleStatus           SalePackageStatus `protobuf:"varint,7,opt,name=sale_status,json=saleStatus,proto3,enum=virtual_image_card.SalePackageStatus" json:"sale_status,omitempty"`
	Label                string            `protobuf:"bytes,8,opt,name=label,proto3" json:"label,omitempty"`
	Operator             string            `protobuf:"bytes,9,opt,name=operator,proto3" json:"operator,omitempty"`
	UpdateTs             uint64            `protobuf:"varint,10,opt,name=update_ts,json=updateTs,proto3" json:"update_ts,omitempty"`
	Remarks              string            `protobuf:"bytes,11,opt,name=remarks,proto3" json:"remarks,omitempty"`
	GroupId              uint32            `protobuf:"varint,12,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	MarketId             uint32            `protobuf:"varint,13,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *SalePackage) Reset()         { *m = SalePackage{} }
func (m *SalePackage) String() string { return proto.CompactTextString(m) }
func (*SalePackage) ProtoMessage()    {}
func (*SalePackage) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_1e8e060b450bf39d, []int{35}
}
func (m *SalePackage) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SalePackage.Unmarshal(m, b)
}
func (m *SalePackage) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SalePackage.Marshal(b, m, deterministic)
}
func (dst *SalePackage) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SalePackage.Merge(dst, src)
}
func (m *SalePackage) XXX_Size() int {
	return xxx_messageInfo_SalePackage.Size(m)
}
func (m *SalePackage) XXX_DiscardUnknown() {
	xxx_messageInfo_SalePackage.DiscardUnknown(m)
}

var xxx_messageInfo_SalePackage proto.InternalMessageInfo

func (m *SalePackage) GetSaleId() uint32 {
	if m != nil {
		return m.SaleId
	}
	return 0
}

func (m *SalePackage) GetPackageInfo() *Package {
	if m != nil {
		return m.PackageInfo
	}
	return nil
}

func (m *SalePackage) GetBeginTs() uint64 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *SalePackage) GetEndTs() uint64 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

func (m *SalePackage) GetCondition() *DisplayCondition {
	if m != nil {
		return m.Condition
	}
	return nil
}

func (m *SalePackage) GetWeight() uint32 {
	if m != nil {
		return m.Weight
	}
	return 0
}

func (m *SalePackage) GetSaleStatus() SalePackageStatus {
	if m != nil {
		return m.SaleStatus
	}
	return SalePackageStatus_ENUM_SALE_PACKAGE_STATUS_UNSPECIFIED
}

func (m *SalePackage) GetLabel() string {
	if m != nil {
		return m.Label
	}
	return ""
}

func (m *SalePackage) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *SalePackage) GetUpdateTs() uint64 {
	if m != nil {
		return m.UpdateTs
	}
	return 0
}

func (m *SalePackage) GetRemarks() string {
	if m != nil {
		return m.Remarks
	}
	return ""
}

func (m *SalePackage) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *SalePackage) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

// 马甲包、系统信息
type MarketInfo struct {
	MarketId             uint32   `protobuf:"varint,1,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	ProductId            string   `protobuf:"bytes,2,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"`
	PackageId            uint32   `protobuf:"varint,3,opt,name=package_id,json=packageId,proto3" json:"package_id,omitempty"`
	UpdateTs             uint64   `protobuf:"varint,4,opt,name=update_ts,json=updateTs,proto3" json:"update_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MarketInfo) Reset()         { *m = MarketInfo{} }
func (m *MarketInfo) String() string { return proto.CompactTextString(m) }
func (*MarketInfo) ProtoMessage()    {}
func (*MarketInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_1e8e060b450bf39d, []int{36}
}
func (m *MarketInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarketInfo.Unmarshal(m, b)
}
func (m *MarketInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarketInfo.Marshal(b, m, deterministic)
}
func (dst *MarketInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarketInfo.Merge(dst, src)
}
func (m *MarketInfo) XXX_Size() int {
	return xxx_messageInfo_MarketInfo.Size(m)
}
func (m *MarketInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MarketInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MarketInfo proto.InternalMessageInfo

func (m *MarketInfo) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *MarketInfo) GetProductId() string {
	if m != nil {
		return m.ProductId
	}
	return ""
}

func (m *MarketInfo) GetPackageId() uint32 {
	if m != nil {
		return m.PackageId
	}
	return 0
}

func (m *MarketInfo) GetUpdateTs() uint64 {
	if m != nil {
		return m.UpdateTs
	}
	return 0
}

// 分组套餐配置
type GroupPackage struct {
	GroupId              uint32        `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	PackageType          PackageType   `protobuf:"varint,2,opt,name=package_type,json=packageType,proto3,enum=virtual_image_card.PackageType" json:"package_type,omitempty"`
	Name                 string        `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Desc                 string        `protobuf:"bytes,4,opt,name=desc,proto3" json:"desc,omitempty"`
	OriginalPriceCent    uint32        `protobuf:"varint,5,opt,name=original_price_cent,json=originalPriceCent,proto3" json:"original_price_cent,omitempty"`
	PriceCent            uint32        `protobuf:"varint,6,opt,name=price_cent,json=priceCent,proto3" json:"price_cent,omitempty"`
	Days                 int32         `protobuf:"varint,7,opt,name=days,proto3" json:"days,omitempty"`
	MarketList           []*MarketInfo `protobuf:"bytes,8,rep,name=market_list,json=marketList,proto3" json:"market_list,omitempty"`
	IsEnabled            bool          `protobuf:"varint,9,opt,name=is_enabled,json=isEnabled,proto3" json:"is_enabled,omitempty"`
	Operator             string        `protobuf:"bytes,10,opt,name=operator,proto3" json:"operator,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GroupPackage) Reset()         { *m = GroupPackage{} }
func (m *GroupPackage) String() string { return proto.CompactTextString(m) }
func (*GroupPackage) ProtoMessage()    {}
func (*GroupPackage) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_1e8e060b450bf39d, []int{37}
}
func (m *GroupPackage) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupPackage.Unmarshal(m, b)
}
func (m *GroupPackage) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupPackage.Marshal(b, m, deterministic)
}
func (dst *GroupPackage) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupPackage.Merge(dst, src)
}
func (m *GroupPackage) XXX_Size() int {
	return xxx_messageInfo_GroupPackage.Size(m)
}
func (m *GroupPackage) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupPackage.DiscardUnknown(m)
}

var xxx_messageInfo_GroupPackage proto.InternalMessageInfo

func (m *GroupPackage) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *GroupPackage) GetPackageType() PackageType {
	if m != nil {
		return m.PackageType
	}
	return PackageType_PACKAGE_TYPE_UNSPECIFIED
}

func (m *GroupPackage) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GroupPackage) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *GroupPackage) GetOriginalPriceCent() uint32 {
	if m != nil {
		return m.OriginalPriceCent
	}
	return 0
}

func (m *GroupPackage) GetPriceCent() uint32 {
	if m != nil {
		return m.PriceCent
	}
	return 0
}

func (m *GroupPackage) GetDays() int32 {
	if m != nil {
		return m.Days
	}
	return 0
}

func (m *GroupPackage) GetMarketList() []*MarketInfo {
	if m != nil {
		return m.MarketList
	}
	return nil
}

func (m *GroupPackage) GetIsEnabled() bool {
	if m != nil {
		return m.IsEnabled
	}
	return false
}

func (m *GroupPackage) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

// 批量添加分组套餐配置
type BatchAddGroupPackageReq struct {
	GroupList            []*GroupPackage `protobuf:"bytes,1,rep,name=group_list,json=groupList,proto3" json:"group_list,omitempty"`
	IsCheck              bool            `protobuf:"varint,2,opt,name=is_check,json=isCheck,proto3" json:"is_check,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *BatchAddGroupPackageReq) Reset()         { *m = BatchAddGroupPackageReq{} }
func (m *BatchAddGroupPackageReq) String() string { return proto.CompactTextString(m) }
func (*BatchAddGroupPackageReq) ProtoMessage()    {}
func (*BatchAddGroupPackageReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_1e8e060b450bf39d, []int{38}
}
func (m *BatchAddGroupPackageReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchAddGroupPackageReq.Unmarshal(m, b)
}
func (m *BatchAddGroupPackageReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchAddGroupPackageReq.Marshal(b, m, deterministic)
}
func (dst *BatchAddGroupPackageReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchAddGroupPackageReq.Merge(dst, src)
}
func (m *BatchAddGroupPackageReq) XXX_Size() int {
	return xxx_messageInfo_BatchAddGroupPackageReq.Size(m)
}
func (m *BatchAddGroupPackageReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchAddGroupPackageReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchAddGroupPackageReq proto.InternalMessageInfo

func (m *BatchAddGroupPackageReq) GetGroupList() []*GroupPackage {
	if m != nil {
		return m.GroupList
	}
	return nil
}

func (m *BatchAddGroupPackageReq) GetIsCheck() bool {
	if m != nil {
		return m.IsCheck
	}
	return false
}

// 批量添加分组套餐配置
type BatchAddGroupPackageResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchAddGroupPackageResp) Reset()         { *m = BatchAddGroupPackageResp{} }
func (m *BatchAddGroupPackageResp) String() string { return proto.CompactTextString(m) }
func (*BatchAddGroupPackageResp) ProtoMessage()    {}
func (*BatchAddGroupPackageResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_1e8e060b450bf39d, []int{39}
}
func (m *BatchAddGroupPackageResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchAddGroupPackageResp.Unmarshal(m, b)
}
func (m *BatchAddGroupPackageResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchAddGroupPackageResp.Marshal(b, m, deterministic)
}
func (dst *BatchAddGroupPackageResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchAddGroupPackageResp.Merge(dst, src)
}
func (m *BatchAddGroupPackageResp) XXX_Size() int {
	return xxx_messageInfo_BatchAddGroupPackageResp.Size(m)
}
func (m *BatchAddGroupPackageResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchAddGroupPackageResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchAddGroupPackageResp proto.InternalMessageInfo

// 新增/编辑分组
type EditGroupPackageReq struct {
	GroupId              uint32        `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	MarketList           []*MarketInfo `protobuf:"bytes,2,rep,name=market_list,json=marketList,proto3" json:"market_list,omitempty"`
	Operator             string        `protobuf:"bytes,3,opt,name=operator,proto3" json:"operator,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *EditGroupPackageReq) Reset()         { *m = EditGroupPackageReq{} }
func (m *EditGroupPackageReq) String() string { return proto.CompactTextString(m) }
func (*EditGroupPackageReq) ProtoMessage()    {}
func (*EditGroupPackageReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_1e8e060b450bf39d, []int{40}
}
func (m *EditGroupPackageReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EditGroupPackageReq.Unmarshal(m, b)
}
func (m *EditGroupPackageReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EditGroupPackageReq.Marshal(b, m, deterministic)
}
func (dst *EditGroupPackageReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EditGroupPackageReq.Merge(dst, src)
}
func (m *EditGroupPackageReq) XXX_Size() int {
	return xxx_messageInfo_EditGroupPackageReq.Size(m)
}
func (m *EditGroupPackageReq) XXX_DiscardUnknown() {
	xxx_messageInfo_EditGroupPackageReq.DiscardUnknown(m)
}

var xxx_messageInfo_EditGroupPackageReq proto.InternalMessageInfo

func (m *EditGroupPackageReq) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *EditGroupPackageReq) GetMarketList() []*MarketInfo {
	if m != nil {
		return m.MarketList
	}
	return nil
}

func (m *EditGroupPackageReq) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

// 新增/编辑分组
type EditGroupPackageResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EditGroupPackageResp) Reset()         { *m = EditGroupPackageResp{} }
func (m *EditGroupPackageResp) String() string { return proto.CompactTextString(m) }
func (*EditGroupPackageResp) ProtoMessage()    {}
func (*EditGroupPackageResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_1e8e060b450bf39d, []int{41}
}
func (m *EditGroupPackageResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EditGroupPackageResp.Unmarshal(m, b)
}
func (m *EditGroupPackageResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EditGroupPackageResp.Marshal(b, m, deterministic)
}
func (dst *EditGroupPackageResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EditGroupPackageResp.Merge(dst, src)
}
func (m *EditGroupPackageResp) XXX_Size() int {
	return xxx_messageInfo_EditGroupPackageResp.Size(m)
}
func (m *EditGroupPackageResp) XXX_DiscardUnknown() {
	xxx_messageInfo_EditGroupPackageResp.DiscardUnknown(m)
}

var xxx_messageInfo_EditGroupPackageResp proto.InternalMessageInfo

// 修改套餐内容
type ModifyPackageReq struct {
	PackageInfo          *Package `protobuf:"bytes,1,opt,name=package_info,json=packageInfo,proto3" json:"package_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ModifyPackageReq) Reset()         { *m = ModifyPackageReq{} }
func (m *ModifyPackageReq) String() string { return proto.CompactTextString(m) }
func (*ModifyPackageReq) ProtoMessage()    {}
func (*ModifyPackageReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_1e8e060b450bf39d, []int{42}
}
func (m *ModifyPackageReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModifyPackageReq.Unmarshal(m, b)
}
func (m *ModifyPackageReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModifyPackageReq.Marshal(b, m, deterministic)
}
func (dst *ModifyPackageReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModifyPackageReq.Merge(dst, src)
}
func (m *ModifyPackageReq) XXX_Size() int {
	return xxx_messageInfo_ModifyPackageReq.Size(m)
}
func (m *ModifyPackageReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ModifyPackageReq.DiscardUnknown(m)
}

var xxx_messageInfo_ModifyPackageReq proto.InternalMessageInfo

func (m *ModifyPackageReq) GetPackageInfo() *Package {
	if m != nil {
		return m.PackageInfo
	}
	return nil
}

type ModifyPackageResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ModifyPackageResp) Reset()         { *m = ModifyPackageResp{} }
func (m *ModifyPackageResp) String() string { return proto.CompactTextString(m) }
func (*ModifyPackageResp) ProtoMessage()    {}
func (*ModifyPackageResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_1e8e060b450bf39d, []int{43}
}
func (m *ModifyPackageResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModifyPackageResp.Unmarshal(m, b)
}
func (m *ModifyPackageResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModifyPackageResp.Marshal(b, m, deterministic)
}
func (dst *ModifyPackageResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModifyPackageResp.Merge(dst, src)
}
func (m *ModifyPackageResp) XXX_Size() int {
	return xxx_messageInfo_ModifyPackageResp.Size(m)
}
func (m *ModifyPackageResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ModifyPackageResp.DiscardUnknown(m)
}

var xxx_messageInfo_ModifyPackageResp proto.InternalMessageInfo

// 修改分组套餐内容
type ModifyGroupPackageReq struct {
	GroupPackage         *GroupPackage `protobuf:"bytes,1,opt,name=group_package,json=groupPackage,proto3" json:"group_package,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ModifyGroupPackageReq) Reset()         { *m = ModifyGroupPackageReq{} }
func (m *ModifyGroupPackageReq) String() string { return proto.CompactTextString(m) }
func (*ModifyGroupPackageReq) ProtoMessage()    {}
func (*ModifyGroupPackageReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_1e8e060b450bf39d, []int{44}
}
func (m *ModifyGroupPackageReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModifyGroupPackageReq.Unmarshal(m, b)
}
func (m *ModifyGroupPackageReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModifyGroupPackageReq.Marshal(b, m, deterministic)
}
func (dst *ModifyGroupPackageReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModifyGroupPackageReq.Merge(dst, src)
}
func (m *ModifyGroupPackageReq) XXX_Size() int {
	return xxx_messageInfo_ModifyGroupPackageReq.Size(m)
}
func (m *ModifyGroupPackageReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ModifyGroupPackageReq.DiscardUnknown(m)
}

var xxx_messageInfo_ModifyGroupPackageReq proto.InternalMessageInfo

func (m *ModifyGroupPackageReq) GetGroupPackage() *GroupPackage {
	if m != nil {
		return m.GroupPackage
	}
	return nil
}

// 修改分组套餐内容
type ModifyGroupPackageResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ModifyGroupPackageResp) Reset()         { *m = ModifyGroupPackageResp{} }
func (m *ModifyGroupPackageResp) String() string { return proto.CompactTextString(m) }
func (*ModifyGroupPackageResp) ProtoMessage()    {}
func (*ModifyGroupPackageResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_1e8e060b450bf39d, []int{45}
}
func (m *ModifyGroupPackageResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModifyGroupPackageResp.Unmarshal(m, b)
}
func (m *ModifyGroupPackageResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModifyGroupPackageResp.Marshal(b, m, deterministic)
}
func (dst *ModifyGroupPackageResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModifyGroupPackageResp.Merge(dst, src)
}
func (m *ModifyGroupPackageResp) XXX_Size() int {
	return xxx_messageInfo_ModifyGroupPackageResp.Size(m)
}
func (m *ModifyGroupPackageResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ModifyGroupPackageResp.DiscardUnknown(m)
}

var xxx_messageInfo_ModifyGroupPackageResp proto.InternalMessageInfo

// 增加在售架套餐配置
type AddSalePackageReq struct {
	Info                 *SalePackage `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	IsCheck              bool         `protobuf:"varint,2,opt,name=is_check,json=isCheck,proto3" json:"is_check,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *AddSalePackageReq) Reset()         { *m = AddSalePackageReq{} }
func (m *AddSalePackageReq) String() string { return proto.CompactTextString(m) }
func (*AddSalePackageReq) ProtoMessage()    {}
func (*AddSalePackageReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_1e8e060b450bf39d, []int{46}
}
func (m *AddSalePackageReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddSalePackageReq.Unmarshal(m, b)
}
func (m *AddSalePackageReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddSalePackageReq.Marshal(b, m, deterministic)
}
func (dst *AddSalePackageReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddSalePackageReq.Merge(dst, src)
}
func (m *AddSalePackageReq) XXX_Size() int {
	return xxx_messageInfo_AddSalePackageReq.Size(m)
}
func (m *AddSalePackageReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddSalePackageReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddSalePackageReq proto.InternalMessageInfo

func (m *AddSalePackageReq) GetInfo() *SalePackage {
	if m != nil {
		return m.Info
	}
	return nil
}

func (m *AddSalePackageReq) GetIsCheck() bool {
	if m != nil {
		return m.IsCheck
	}
	return false
}

// 增加在售架套餐配置
type AddSalePackageResp struct {
	AlreadyOnShelfList   []uint32 `protobuf:"varint,1,rep,packed,name=already_on_shelf_list,json=alreadyOnShelfList,proto3" json:"already_on_shelf_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddSalePackageResp) Reset()         { *m = AddSalePackageResp{} }
func (m *AddSalePackageResp) String() string { return proto.CompactTextString(m) }
func (*AddSalePackageResp) ProtoMessage()    {}
func (*AddSalePackageResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_1e8e060b450bf39d, []int{47}
}
func (m *AddSalePackageResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddSalePackageResp.Unmarshal(m, b)
}
func (m *AddSalePackageResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddSalePackageResp.Marshal(b, m, deterministic)
}
func (dst *AddSalePackageResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddSalePackageResp.Merge(dst, src)
}
func (m *AddSalePackageResp) XXX_Size() int {
	return xxx_messageInfo_AddSalePackageResp.Size(m)
}
func (m *AddSalePackageResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddSalePackageResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddSalePackageResp proto.InternalMessageInfo

func (m *AddSalePackageResp) GetAlreadyOnShelfList() []uint32 {
	if m != nil {
		return m.AlreadyOnShelfList
	}
	return nil
}

// 修改在售架套餐配置
type UpdateSalePackageReq struct {
	Info                 *SalePackage `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *UpdateSalePackageReq) Reset()         { *m = UpdateSalePackageReq{} }
func (m *UpdateSalePackageReq) String() string { return proto.CompactTextString(m) }
func (*UpdateSalePackageReq) ProtoMessage()    {}
func (*UpdateSalePackageReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_1e8e060b450bf39d, []int{48}
}
func (m *UpdateSalePackageReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateSalePackageReq.Unmarshal(m, b)
}
func (m *UpdateSalePackageReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateSalePackageReq.Marshal(b, m, deterministic)
}
func (dst *UpdateSalePackageReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateSalePackageReq.Merge(dst, src)
}
func (m *UpdateSalePackageReq) XXX_Size() int {
	return xxx_messageInfo_UpdateSalePackageReq.Size(m)
}
func (m *UpdateSalePackageReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateSalePackageReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateSalePackageReq proto.InternalMessageInfo

func (m *UpdateSalePackageReq) GetInfo() *SalePackage {
	if m != nil {
		return m.Info
	}
	return nil
}

// 修改在售架套餐配置
type UpdateSalePackageResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateSalePackageResp) Reset()         { *m = UpdateSalePackageResp{} }
func (m *UpdateSalePackageResp) String() string { return proto.CompactTextString(m) }
func (*UpdateSalePackageResp) ProtoMessage()    {}
func (*UpdateSalePackageResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_1e8e060b450bf39d, []int{49}
}
func (m *UpdateSalePackageResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateSalePackageResp.Unmarshal(m, b)
}
func (m *UpdateSalePackageResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateSalePackageResp.Marshal(b, m, deterministic)
}
func (dst *UpdateSalePackageResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateSalePackageResp.Merge(dst, src)
}
func (m *UpdateSalePackageResp) XXX_Size() int {
	return xxx_messageInfo_UpdateSalePackageResp.Size(m)
}
func (m *UpdateSalePackageResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateSalePackageResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateSalePackageResp proto.InternalMessageInfo

// 获取在售架套餐列表
type GetSalePackageListByStatusReq struct {
	Status               SalePackageStatus `protobuf:"varint,1,opt,name=status,proto3,enum=virtual_image_card.SalePackageStatus" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetSalePackageListByStatusReq) Reset()         { *m = GetSalePackageListByStatusReq{} }
func (m *GetSalePackageListByStatusReq) String() string { return proto.CompactTextString(m) }
func (*GetSalePackageListByStatusReq) ProtoMessage()    {}
func (*GetSalePackageListByStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_1e8e060b450bf39d, []int{50}
}
func (m *GetSalePackageListByStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSalePackageListByStatusReq.Unmarshal(m, b)
}
func (m *GetSalePackageListByStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSalePackageListByStatusReq.Marshal(b, m, deterministic)
}
func (dst *GetSalePackageListByStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSalePackageListByStatusReq.Merge(dst, src)
}
func (m *GetSalePackageListByStatusReq) XXX_Size() int {
	return xxx_messageInfo_GetSalePackageListByStatusReq.Size(m)
}
func (m *GetSalePackageListByStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSalePackageListByStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSalePackageListByStatusReq proto.InternalMessageInfo

func (m *GetSalePackageListByStatusReq) GetStatus() SalePackageStatus {
	if m != nil {
		return m.Status
	}
	return SalePackageStatus_ENUM_SALE_PACKAGE_STATUS_UNSPECIFIED
}

// 获取在售架套餐列表
type GetSalePackageListByStatusResp struct {
	// 不做分页
	PackageList          []*SalePackage `protobuf:"bytes,1,rep,name=package_list,json=packageList,proto3" json:"package_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetSalePackageListByStatusResp) Reset()         { *m = GetSalePackageListByStatusResp{} }
func (m *GetSalePackageListByStatusResp) String() string { return proto.CompactTextString(m) }
func (*GetSalePackageListByStatusResp) ProtoMessage()    {}
func (*GetSalePackageListByStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_1e8e060b450bf39d, []int{51}
}
func (m *GetSalePackageListByStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSalePackageListByStatusResp.Unmarshal(m, b)
}
func (m *GetSalePackageListByStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSalePackageListByStatusResp.Marshal(b, m, deterministic)
}
func (dst *GetSalePackageListByStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSalePackageListByStatusResp.Merge(dst, src)
}
func (m *GetSalePackageListByStatusResp) XXX_Size() int {
	return xxx_messageInfo_GetSalePackageListByStatusResp.Size(m)
}
func (m *GetSalePackageListByStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSalePackageListByStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSalePackageListByStatusResp proto.InternalMessageInfo

func (m *GetSalePackageListByStatusResp) GetPackageList() []*SalePackage {
	if m != nil {
		return m.PackageList
	}
	return nil
}

// 套餐排序
type SalePackageSortReq struct {
	SaleIdList           []uint32 `protobuf:"varint,1,rep,packed,name=sale_id_list,json=saleIdList,proto3" json:"sale_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SalePackageSortReq) Reset()         { *m = SalePackageSortReq{} }
func (m *SalePackageSortReq) String() string { return proto.CompactTextString(m) }
func (*SalePackageSortReq) ProtoMessage()    {}
func (*SalePackageSortReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_1e8e060b450bf39d, []int{52}
}
func (m *SalePackageSortReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SalePackageSortReq.Unmarshal(m, b)
}
func (m *SalePackageSortReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SalePackageSortReq.Marshal(b, m, deterministic)
}
func (dst *SalePackageSortReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SalePackageSortReq.Merge(dst, src)
}
func (m *SalePackageSortReq) XXX_Size() int {
	return xxx_messageInfo_SalePackageSortReq.Size(m)
}
func (m *SalePackageSortReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SalePackageSortReq.DiscardUnknown(m)
}

var xxx_messageInfo_SalePackageSortReq proto.InternalMessageInfo

func (m *SalePackageSortReq) GetSaleIdList() []uint32 {
	if m != nil {
		return m.SaleIdList
	}
	return nil
}

// 套餐排序
type SalePackageSortResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SalePackageSortResp) Reset()         { *m = SalePackageSortResp{} }
func (m *SalePackageSortResp) String() string { return proto.CompactTextString(m) }
func (*SalePackageSortResp) ProtoMessage()    {}
func (*SalePackageSortResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_1e8e060b450bf39d, []int{53}
}
func (m *SalePackageSortResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SalePackageSortResp.Unmarshal(m, b)
}
func (m *SalePackageSortResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SalePackageSortResp.Marshal(b, m, deterministic)
}
func (dst *SalePackageSortResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SalePackageSortResp.Merge(dst, src)
}
func (m *SalePackageSortResp) XXX_Size() int {
	return xxx_messageInfo_SalePackageSortResp.Size(m)
}
func (m *SalePackageSortResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SalePackageSortResp.DiscardUnknown(m)
}

var xxx_messageInfo_SalePackageSortResp proto.InternalMessageInfo

type AboutToExpireCfg struct {
	Icon                 string   `protobuf:"bytes,1,opt,name=icon,proto3" json:"icon,omitempty"`
	ExpireAlertTime      uint32   `protobuf:"varint,15,opt,name=expire_alert_time,json=expireAlertTime,proto3" json:"expire_alert_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AboutToExpireCfg) Reset()         { *m = AboutToExpireCfg{} }
func (m *AboutToExpireCfg) String() string { return proto.CompactTextString(m) }
func (*AboutToExpireCfg) ProtoMessage()    {}
func (*AboutToExpireCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_1e8e060b450bf39d, []int{54}
}
func (m *AboutToExpireCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AboutToExpireCfg.Unmarshal(m, b)
}
func (m *AboutToExpireCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AboutToExpireCfg.Marshal(b, m, deterministic)
}
func (dst *AboutToExpireCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AboutToExpireCfg.Merge(dst, src)
}
func (m *AboutToExpireCfg) XXX_Size() int {
	return xxx_messageInfo_AboutToExpireCfg.Size(m)
}
func (m *AboutToExpireCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_AboutToExpireCfg.DiscardUnknown(m)
}

var xxx_messageInfo_AboutToExpireCfg proto.InternalMessageInfo

func (m *AboutToExpireCfg) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *AboutToExpireCfg) GetExpireAlertTime() uint32 {
	if m != nil {
		return m.ExpireAlertTime
	}
	return 0
}

// 获取虚拟形象卡片通用配置 cfg_version 变大时调用
type GetVirtualImageCardCommonCfgRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetVirtualImageCardCommonCfgRequest) Reset()         { *m = GetVirtualImageCardCommonCfgRequest{} }
func (m *GetVirtualImageCardCommonCfgRequest) String() string { return proto.CompactTextString(m) }
func (*GetVirtualImageCardCommonCfgRequest) ProtoMessage()    {}
func (*GetVirtualImageCardCommonCfgRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_1e8e060b450bf39d, []int{55}
}
func (m *GetVirtualImageCardCommonCfgRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVirtualImageCardCommonCfgRequest.Unmarshal(m, b)
}
func (m *GetVirtualImageCardCommonCfgRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVirtualImageCardCommonCfgRequest.Marshal(b, m, deterministic)
}
func (dst *GetVirtualImageCardCommonCfgRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVirtualImageCardCommonCfgRequest.Merge(dst, src)
}
func (m *GetVirtualImageCardCommonCfgRequest) XXX_Size() int {
	return xxx_messageInfo_GetVirtualImageCardCommonCfgRequest.Size(m)
}
func (m *GetVirtualImageCardCommonCfgRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVirtualImageCardCommonCfgRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetVirtualImageCardCommonCfgRequest proto.InternalMessageInfo

type GetVirtualImageCardCommonCfgResponse struct {
	WaitToBuyIcon          string              `protobuf:"bytes,1,opt,name=wait_to_buy_icon,json=waitToBuyIcon,proto3" json:"wait_to_buy_icon,omitempty"`
	AlreadyBuyIcon         string              `protobuf:"bytes,2,opt,name=already_buy_icon,json=alreadyBuyIcon,proto3" json:"already_buy_icon,omitempty"`
	AboutToExpireCfgList   []*AboutToExpireCfg `protobuf:"bytes,3,rep,name=about_to_expire_cfg_list,json=aboutToExpireCfgList,proto3" json:"about_to_expire_cfg_list,omitempty"`
	FirstEnterCardStoreUrl string              `protobuf:"bytes,4,opt,name=first_enter_card_store_url,json=firstEnterCardStoreUrl,proto3" json:"first_enter_card_store_url,omitempty"`
	AdText                 string              `protobuf:"bytes,5,opt,name=ad_text,json=adText,proto3" json:"ad_text,omitempty"`
	NDayShowOnce           uint32              `protobuf:"varint,6,opt,name=n_day_show_once,json=nDayShowOnce,proto3" json:"n_day_show_once,omitempty"`
	CfgVersion             uint32              `protobuf:"varint,7,opt,name=cfg_version,json=cfgVersion,proto3" json:"cfg_version,omitempty"`
	WaitToBuyBg            string              `protobuf:"bytes,8,opt,name=wait_to_buy_bg,json=waitToBuyBg,proto3" json:"wait_to_buy_bg,omitempty"`
	AlreadyBuyBg           string              `protobuf:"bytes,9,opt,name=already_buy_bg,json=alreadyBuyBg,proto3" json:"already_buy_bg,omitempty"`
	AboutToExpireBg        string              `protobuf:"bytes,10,opt,name=about_to_expire_bg,json=aboutToExpireBg,proto3" json:"about_to_expire_bg,omitempty"`
	StoreResidentEntryIcon string              `protobuf:"bytes,11,opt,name=store_resident_entry_icon,json=storeResidentEntryIcon,proto3" json:"store_resident_entry_icon,omitempty"`
	StoreTabIconSelected   string              `protobuf:"bytes,12,opt,name=store_tab_icon_selected,json=storeTabIconSelected,proto3" json:"store_tab_icon_selected,omitempty"`
	StoreTabIconUnselected string              `protobuf:"bytes,13,opt,name=store_tab_icon_unselected,json=storeTabIconUnselected,proto3" json:"store_tab_icon_unselected,omitempty"`
	ExpireAlertTime        uint32              `protobuf:"varint,14,opt,name=expire_alert_time,json=expireAlertTime,proto3" json:"expire_alert_time,omitempty"`
	FirstEnterCardStoreMd5 string              `protobuf:"bytes,15,opt,name=first_enter_card_store_md5,json=firstEnterCardStoreMd5,proto3" json:"first_enter_card_store_md5,omitempty"`
	PcWaitToBuyBg          string              `protobuf:"bytes,16,opt,name=pc_wait_to_buy_bg,json=pcWaitToBuyBg,proto3" json:"pc_wait_to_buy_bg,omitempty"`
	PcAlreadyBuyBg         string              `protobuf:"bytes,17,opt,name=pc_already_buy_bg,json=pcAlreadyBuyBg,proto3" json:"pc_already_buy_bg,omitempty"`
	PcAboutToExpireBg      string              `protobuf:"bytes,18,opt,name=pc_about_to_expire_bg,json=pcAboutToExpireBg,proto3" json:"pc_about_to_expire_bg,omitempty"`
	XXX_NoUnkeyedLiteral   struct{}            `json:"-"`
	XXX_unrecognized       []byte              `json:"-"`
	XXX_sizecache          int32               `json:"-"`
}

func (m *GetVirtualImageCardCommonCfgResponse) Reset()         { *m = GetVirtualImageCardCommonCfgResponse{} }
func (m *GetVirtualImageCardCommonCfgResponse) String() string { return proto.CompactTextString(m) }
func (*GetVirtualImageCardCommonCfgResponse) ProtoMessage()    {}
func (*GetVirtualImageCardCommonCfgResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_1e8e060b450bf39d, []int{56}
}
func (m *GetVirtualImageCardCommonCfgResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVirtualImageCardCommonCfgResponse.Unmarshal(m, b)
}
func (m *GetVirtualImageCardCommonCfgResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVirtualImageCardCommonCfgResponse.Marshal(b, m, deterministic)
}
func (dst *GetVirtualImageCardCommonCfgResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVirtualImageCardCommonCfgResponse.Merge(dst, src)
}
func (m *GetVirtualImageCardCommonCfgResponse) XXX_Size() int {
	return xxx_messageInfo_GetVirtualImageCardCommonCfgResponse.Size(m)
}
func (m *GetVirtualImageCardCommonCfgResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVirtualImageCardCommonCfgResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetVirtualImageCardCommonCfgResponse proto.InternalMessageInfo

func (m *GetVirtualImageCardCommonCfgResponse) GetWaitToBuyIcon() string {
	if m != nil {
		return m.WaitToBuyIcon
	}
	return ""
}

func (m *GetVirtualImageCardCommonCfgResponse) GetAlreadyBuyIcon() string {
	if m != nil {
		return m.AlreadyBuyIcon
	}
	return ""
}

func (m *GetVirtualImageCardCommonCfgResponse) GetAboutToExpireCfgList() []*AboutToExpireCfg {
	if m != nil {
		return m.AboutToExpireCfgList
	}
	return nil
}

func (m *GetVirtualImageCardCommonCfgResponse) GetFirstEnterCardStoreUrl() string {
	if m != nil {
		return m.FirstEnterCardStoreUrl
	}
	return ""
}

func (m *GetVirtualImageCardCommonCfgResponse) GetAdText() string {
	if m != nil {
		return m.AdText
	}
	return ""
}

func (m *GetVirtualImageCardCommonCfgResponse) GetNDayShowOnce() uint32 {
	if m != nil {
		return m.NDayShowOnce
	}
	return 0
}

func (m *GetVirtualImageCardCommonCfgResponse) GetCfgVersion() uint32 {
	if m != nil {
		return m.CfgVersion
	}
	return 0
}

func (m *GetVirtualImageCardCommonCfgResponse) GetWaitToBuyBg() string {
	if m != nil {
		return m.WaitToBuyBg
	}
	return ""
}

func (m *GetVirtualImageCardCommonCfgResponse) GetAlreadyBuyBg() string {
	if m != nil {
		return m.AlreadyBuyBg
	}
	return ""
}

func (m *GetVirtualImageCardCommonCfgResponse) GetAboutToExpireBg() string {
	if m != nil {
		return m.AboutToExpireBg
	}
	return ""
}

func (m *GetVirtualImageCardCommonCfgResponse) GetStoreResidentEntryIcon() string {
	if m != nil {
		return m.StoreResidentEntryIcon
	}
	return ""
}

func (m *GetVirtualImageCardCommonCfgResponse) GetStoreTabIconSelected() string {
	if m != nil {
		return m.StoreTabIconSelected
	}
	return ""
}

func (m *GetVirtualImageCardCommonCfgResponse) GetStoreTabIconUnselected() string {
	if m != nil {
		return m.StoreTabIconUnselected
	}
	return ""
}

func (m *GetVirtualImageCardCommonCfgResponse) GetExpireAlertTime() uint32 {
	if m != nil {
		return m.ExpireAlertTime
	}
	return 0
}

func (m *GetVirtualImageCardCommonCfgResponse) GetFirstEnterCardStoreMd5() string {
	if m != nil {
		return m.FirstEnterCardStoreMd5
	}
	return ""
}

func (m *GetVirtualImageCardCommonCfgResponse) GetPcWaitToBuyBg() string {
	if m != nil {
		return m.PcWaitToBuyBg
	}
	return ""
}

func (m *GetVirtualImageCardCommonCfgResponse) GetPcAlreadyBuyBg() string {
	if m != nil {
		return m.PcAlreadyBuyBg
	}
	return ""
}

func (m *GetVirtualImageCardCommonCfgResponse) GetPcAboutToExpireBg() string {
	if m != nil {
		return m.PcAboutToExpireBg
	}
	return ""
}

// 获取虚拟形象卡片入口状态
type GetVirtualImageCardEntryStatusRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetVirtualImageCardEntryStatusRequest) Reset()         { *m = GetVirtualImageCardEntryStatusRequest{} }
func (m *GetVirtualImageCardEntryStatusRequest) String() string { return proto.CompactTextString(m) }
func (*GetVirtualImageCardEntryStatusRequest) ProtoMessage()    {}
func (*GetVirtualImageCardEntryStatusRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_1e8e060b450bf39d, []int{57}
}
func (m *GetVirtualImageCardEntryStatusRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVirtualImageCardEntryStatusRequest.Unmarshal(m, b)
}
func (m *GetVirtualImageCardEntryStatusRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVirtualImageCardEntryStatusRequest.Marshal(b, m, deterministic)
}
func (dst *GetVirtualImageCardEntryStatusRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVirtualImageCardEntryStatusRequest.Merge(dst, src)
}
func (m *GetVirtualImageCardEntryStatusRequest) XXX_Size() int {
	return xxx_messageInfo_GetVirtualImageCardEntryStatusRequest.Size(m)
}
func (m *GetVirtualImageCardEntryStatusRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVirtualImageCardEntryStatusRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetVirtualImageCardEntryStatusRequest proto.InternalMessageInfo

func (m *GetVirtualImageCardEntryStatusRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

// 虚拟形象卡片入口状态
type GetVirtualImageCardEntryStatusResponse struct {
	ExpireTime           uint32   `protobuf:"varint,1,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
	LowPriceText         string   `protobuf:"bytes,2,opt,name=low_price_text,json=lowPriceText,proto3" json:"low_price_text,omitempty"`
	AdIdx                uint32   `protobuf:"varint,3,opt,name=ad_idx,json=adIdx,proto3" json:"ad_idx,omitempty"`
	CfgVersion           uint32   `protobuf:"varint,4,opt,name=cfg_version,json=cfgVersion,proto3" json:"cfg_version,omitempty"`
	Switch               bool     `protobuf:"varint,5,opt,name=switch,proto3" json:"switch,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetVirtualImageCardEntryStatusResponse) Reset() {
	*m = GetVirtualImageCardEntryStatusResponse{}
}
func (m *GetVirtualImageCardEntryStatusResponse) String() string { return proto.CompactTextString(m) }
func (*GetVirtualImageCardEntryStatusResponse) ProtoMessage()    {}
func (*GetVirtualImageCardEntryStatusResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_card_1e8e060b450bf39d, []int{58}
}
func (m *GetVirtualImageCardEntryStatusResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVirtualImageCardEntryStatusResponse.Unmarshal(m, b)
}
func (m *GetVirtualImageCardEntryStatusResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVirtualImageCardEntryStatusResponse.Marshal(b, m, deterministic)
}
func (dst *GetVirtualImageCardEntryStatusResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVirtualImageCardEntryStatusResponse.Merge(dst, src)
}
func (m *GetVirtualImageCardEntryStatusResponse) XXX_Size() int {
	return xxx_messageInfo_GetVirtualImageCardEntryStatusResponse.Size(m)
}
func (m *GetVirtualImageCardEntryStatusResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVirtualImageCardEntryStatusResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetVirtualImageCardEntryStatusResponse proto.InternalMessageInfo

func (m *GetVirtualImageCardEntryStatusResponse) GetExpireTime() uint32 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

func (m *GetVirtualImageCardEntryStatusResponse) GetLowPriceText() string {
	if m != nil {
		return m.LowPriceText
	}
	return ""
}

func (m *GetVirtualImageCardEntryStatusResponse) GetAdIdx() uint32 {
	if m != nil {
		return m.AdIdx
	}
	return 0
}

func (m *GetVirtualImageCardEntryStatusResponse) GetCfgVersion() uint32 {
	if m != nil {
		return m.CfgVersion
	}
	return 0
}

func (m *GetVirtualImageCardEntryStatusResponse) GetSwitch() bool {
	if m != nil {
		return m.Switch
	}
	return false
}

func init() {
	proto.RegisterType((*PlaceOrderReq)(nil), "virtual_image_card.PlaceOrderReq")
	proto.RegisterType((*PlaceOrderResp)(nil), "virtual_image_card.PlaceOrderResp")
	proto.RegisterType((*CancelOrderReq)(nil), "virtual_image_card.CancelOrderReq")
	proto.RegisterType((*CancelOrderResp)(nil), "virtual_image_card.CancelOrderResp")
	proto.RegisterType((*PayCallbackReq)(nil), "virtual_image_card.PayCallbackReq")
	proto.RegisterType((*PayCallbackResp)(nil), "virtual_image_card.PayCallbackResp")
	proto.RegisterType((*NotifyContractReq)(nil), "virtual_image_card.NotifyContractReq")
	proto.RegisterType((*NotifyContractResp)(nil), "virtual_image_card.NotifyContractResp")
	proto.RegisterType((*PlaceAutoPayOrderReq)(nil), "virtual_image_card.PlaceAutoPayOrderReq")
	proto.RegisterType((*PlaceAutoPayOrderResp)(nil), "virtual_image_card.PlaceAutoPayOrderResp")
	proto.RegisterType((*RevokeOrderReq)(nil), "virtual_image_card.RevokeOrderReq")
	proto.RegisterType((*RevokeOrderResp)(nil), "virtual_image_card.RevokeOrderResp")
	proto.RegisterType((*GenerateStatReq)(nil), "virtual_image_card.GenerateStatReq")
	proto.RegisterType((*GenerateStatResp)(nil), "virtual_image_card.GenerateStatResp")
	proto.RegisterType((*UserCardInfo)(nil), "virtual_image_card.UserCardInfo")
	proto.RegisterType((*UserContractInfo)(nil), "virtual_image_card.UserContractInfo")
	proto.RegisterType((*GetUserCardInfoReq)(nil), "virtual_image_card.GetUserCardInfoReq")
	proto.RegisterType((*GetUserCardInfoResp)(nil), "virtual_image_card.GetUserCardInfoResp")
	proto.RegisterType((*GetUserRedemptionInfoReq)(nil), "virtual_image_card.GetUserRedemptionInfoReq")
	proto.RegisterType((*GetUserRedemptionInfoResp)(nil), "virtual_image_card.GetUserRedemptionInfoResp")
	proto.RegisterType((*GetUserPackageListReq)(nil), "virtual_image_card.GetUserPackageListReq")
	proto.RegisterType((*GetUserPackageListResp)(nil), "virtual_image_card.GetUserPackageListResp")
	proto.RegisterType((*UserPackage)(nil), "virtual_image_card.UserPackage")
	proto.RegisterType((*GetPurchaseHistoryReq)(nil), "virtual_image_card.GetPurchaseHistoryReq")
	proto.RegisterType((*GetPurchaseHistoryResp)(nil), "virtual_image_card.GetPurchaseHistoryResp")
	proto.RegisterType((*PurchaseRecord)(nil), "virtual_image_card.PurchaseRecord")
	proto.RegisterType((*Package)(nil), "virtual_image_card.Package")
	proto.RegisterType((*AddPackageReq)(nil), "virtual_image_card.AddPackageReq")
	proto.RegisterType((*AddPackageResp)(nil), "virtual_image_card.AddPackageResp")
	proto.RegisterType((*UpdatePackageStatusReq)(nil), "virtual_image_card.UpdatePackageStatusReq")
	proto.RegisterType((*UpdatePackageStatusResp)(nil), "virtual_image_card.UpdatePackageStatusResp")
	proto.RegisterType((*GetPackageListByStatusReq)(nil), "virtual_image_card.GetPackageListByStatusReq")
	proto.RegisterType((*MixPackage)(nil), "virtual_image_card.MixPackage")
	proto.RegisterType((*GetPackageListByStatusResp)(nil), "virtual_image_card.GetPackageListByStatusResp")
	proto.RegisterType((*DisplayCondition)(nil), "virtual_image_card.DisplayCondition")
	proto.RegisterType((*SalePackage)(nil), "virtual_image_card.SalePackage")
	proto.RegisterType((*MarketInfo)(nil), "virtual_image_card.MarketInfo")
	proto.RegisterType((*GroupPackage)(nil), "virtual_image_card.GroupPackage")
	proto.RegisterType((*BatchAddGroupPackageReq)(nil), "virtual_image_card.BatchAddGroupPackageReq")
	proto.RegisterType((*BatchAddGroupPackageResp)(nil), "virtual_image_card.BatchAddGroupPackageResp")
	proto.RegisterType((*EditGroupPackageReq)(nil), "virtual_image_card.EditGroupPackageReq")
	proto.RegisterType((*EditGroupPackageResp)(nil), "virtual_image_card.EditGroupPackageResp")
	proto.RegisterType((*ModifyPackageReq)(nil), "virtual_image_card.ModifyPackageReq")
	proto.RegisterType((*ModifyPackageResp)(nil), "virtual_image_card.ModifyPackageResp")
	proto.RegisterType((*ModifyGroupPackageReq)(nil), "virtual_image_card.ModifyGroupPackageReq")
	proto.RegisterType((*ModifyGroupPackageResp)(nil), "virtual_image_card.ModifyGroupPackageResp")
	proto.RegisterType((*AddSalePackageReq)(nil), "virtual_image_card.AddSalePackageReq")
	proto.RegisterType((*AddSalePackageResp)(nil), "virtual_image_card.AddSalePackageResp")
	proto.RegisterType((*UpdateSalePackageReq)(nil), "virtual_image_card.UpdateSalePackageReq")
	proto.RegisterType((*UpdateSalePackageResp)(nil), "virtual_image_card.UpdateSalePackageResp")
	proto.RegisterType((*GetSalePackageListByStatusReq)(nil), "virtual_image_card.GetSalePackageListByStatusReq")
	proto.RegisterType((*GetSalePackageListByStatusResp)(nil), "virtual_image_card.GetSalePackageListByStatusResp")
	proto.RegisterType((*SalePackageSortReq)(nil), "virtual_image_card.SalePackageSortReq")
	proto.RegisterType((*SalePackageSortResp)(nil), "virtual_image_card.SalePackageSortResp")
	proto.RegisterType((*AboutToExpireCfg)(nil), "virtual_image_card.AboutToExpireCfg")
	proto.RegisterType((*GetVirtualImageCardCommonCfgRequest)(nil), "virtual_image_card.GetVirtualImageCardCommonCfgRequest")
	proto.RegisterType((*GetVirtualImageCardCommonCfgResponse)(nil), "virtual_image_card.GetVirtualImageCardCommonCfgResponse")
	proto.RegisterType((*GetVirtualImageCardEntryStatusRequest)(nil), "virtual_image_card.GetVirtualImageCardEntryStatusRequest")
	proto.RegisterType((*GetVirtualImageCardEntryStatusResponse)(nil), "virtual_image_card.GetVirtualImageCardEntryStatusResponse")
	proto.RegisterEnum("virtual_image_card.PayChannel", PayChannel_name, PayChannel_value)
	proto.RegisterEnum("virtual_image_card.PackageType", PackageType_name, PackageType_value)
	proto.RegisterEnum("virtual_image_card.SalePackageStatus", SalePackageStatus_name, SalePackageStatus_value)
	proto.RegisterEnum("virtual_image_card.CardStatus", CardStatus_name, CardStatus_value)
	proto.RegisterEnum("virtual_image_card.DisplayCondition_CondType", DisplayCondition_CondType_name, DisplayCondition_CondType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// VirtualImageCardClient is the client API for VirtualImageCard service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type VirtualImageCardClient interface {
	// 下单
	PlaceOrder(ctx context.Context, in *PlaceOrderReq, opts ...grpc.CallOption) (*PlaceOrderResp, error)
	// 取消订单
	CancelOrder(ctx context.Context, in *CancelOrderReq, opts ...grpc.CallOption) (*CancelOrderResp, error)
	// 支付回调
	PayCallback(ctx context.Context, in *PayCallbackReq, opts ...grpc.CallOption) (*PayCallbackResp, error)
	// 签约回调
	NotifyContract(ctx context.Context, in *NotifyContractReq, opts ...grpc.CallOption) (*NotifyContractResp, error)
	// 苹果的自动下单回调
	PlaceAutoPayOrder(ctx context.Context, in *PlaceAutoPayOrderReq, opts ...grpc.CallOption) (*PlaceAutoPayOrderResp, error)
	// 订单退款
	RevokeOrder(ctx context.Context, in *RevokeOrderReq, opts ...grpc.CallOption) (*RevokeOrderResp, error)
	// 生成统计报表
	GenerateStat(ctx context.Context, in *GenerateStatReq, opts ...grpc.CallOption) (*GenerateStatResp, error)
	// 获取用户卡信息
	GetUserCardInfo(ctx context.Context, in *GetUserCardInfoReq, opts ...grpc.CallOption) (*GetUserCardInfoResp, error)
	// 获取用户核销信息
	GetUserRedemptionInfo(ctx context.Context, in *GetUserRedemptionInfoReq, opts ...grpc.CallOption) (*GetUserRedemptionInfoResp, error)
	// 获取用户可用套餐列表
	GetUserPackageList(ctx context.Context, in *GetUserPackageListReq, opts ...grpc.CallOption) (*GetUserPackageListResp, error)
	// 获取购买历史
	GetPurchaseHistory(ctx context.Context, in *GetPurchaseHistoryReq, opts ...grpc.CallOption) (*GetPurchaseHistoryResp, error)
	// ========================== 套餐运营后台 ==================================
	// 新增套餐
	AddPackage(ctx context.Context, in *AddPackageReq, opts ...grpc.CallOption) (*AddPackageResp, error)
	// 批量新增分组套餐
	BatchAddGroupPackage(ctx context.Context, in *BatchAddGroupPackageReq, opts ...grpc.CallOption) (*BatchAddGroupPackageResp, error)
	// 修改套餐内容
	ModifyPackage(ctx context.Context, in *ModifyPackageReq, opts ...grpc.CallOption) (*ModifyPackageResp, error)
	// 更新套餐/分组状态（停用/启用）
	UpdatePackageStatus(ctx context.Context, in *UpdatePackageStatusReq, opts ...grpc.CallOption) (*UpdatePackageStatusResp, error)
	// 获取套餐/分组列表(按更新时间排序)
	GetPackageListByStatus(ctx context.Context, in *GetPackageListByStatusReq, opts ...grpc.CallOption) (*GetPackageListByStatusResp, error)
	// 新增/编辑分组套餐
	EditGroupPackage(ctx context.Context, in *EditGroupPackageReq, opts ...grpc.CallOption) (*EditGroupPackageResp, error)
	// 修改分组套餐内容
	ModifyGroupPackage(ctx context.Context, in *ModifyGroupPackageReq, opts ...grpc.CallOption) (*ModifyGroupPackageResp, error)
	// 增加在售架套餐
	AddSalePackage(ctx context.Context, in *AddSalePackageReq, opts ...grpc.CallOption) (*AddSalePackageResp, error)
	// 修改在售架套餐
	UpdateSalePackage(ctx context.Context, in *UpdateSalePackageReq, opts ...grpc.CallOption) (*UpdateSalePackageResp, error)
	// 获取在售架套餐列表
	GetSalePackageListByStatus(ctx context.Context, in *GetSalePackageListByStatusReq, opts ...grpc.CallOption) (*GetSalePackageListByStatusResp, error)
	// 修改在售架套餐排序
	SalePackageSort(ctx context.Context, in *SalePackageSortReq, opts ...grpc.CallOption) (*SalePackageSortResp, error)
	// 获取无限卡配置
	GetVirtualImageCardCommonCfg(ctx context.Context, in *GetVirtualImageCardCommonCfgRequest, opts ...grpc.CallOption) (*GetVirtualImageCardCommonCfgResponse, error)
	// 获取无限卡入口状态
	GetVirtualImageCardEntryStatus(ctx context.Context, in *GetVirtualImageCardEntryStatusRequest, opts ...grpc.CallOption) (*GetVirtualImageCardEntryStatusResponse, error)
}

type virtualImageCardClient struct {
	cc *grpc.ClientConn
}

func NewVirtualImageCardClient(cc *grpc.ClientConn) VirtualImageCardClient {
	return &virtualImageCardClient{cc}
}

func (c *virtualImageCardClient) PlaceOrder(ctx context.Context, in *PlaceOrderReq, opts ...grpc.CallOption) (*PlaceOrderResp, error) {
	out := new(PlaceOrderResp)
	err := c.cc.Invoke(ctx, "/virtual_image_card.VirtualImageCard/PlaceOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageCardClient) CancelOrder(ctx context.Context, in *CancelOrderReq, opts ...grpc.CallOption) (*CancelOrderResp, error) {
	out := new(CancelOrderResp)
	err := c.cc.Invoke(ctx, "/virtual_image_card.VirtualImageCard/CancelOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageCardClient) PayCallback(ctx context.Context, in *PayCallbackReq, opts ...grpc.CallOption) (*PayCallbackResp, error) {
	out := new(PayCallbackResp)
	err := c.cc.Invoke(ctx, "/virtual_image_card.VirtualImageCard/PayCallback", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageCardClient) NotifyContract(ctx context.Context, in *NotifyContractReq, opts ...grpc.CallOption) (*NotifyContractResp, error) {
	out := new(NotifyContractResp)
	err := c.cc.Invoke(ctx, "/virtual_image_card.VirtualImageCard/NotifyContract", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageCardClient) PlaceAutoPayOrder(ctx context.Context, in *PlaceAutoPayOrderReq, opts ...grpc.CallOption) (*PlaceAutoPayOrderResp, error) {
	out := new(PlaceAutoPayOrderResp)
	err := c.cc.Invoke(ctx, "/virtual_image_card.VirtualImageCard/PlaceAutoPayOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageCardClient) RevokeOrder(ctx context.Context, in *RevokeOrderReq, opts ...grpc.CallOption) (*RevokeOrderResp, error) {
	out := new(RevokeOrderResp)
	err := c.cc.Invoke(ctx, "/virtual_image_card.VirtualImageCard/RevokeOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageCardClient) GenerateStat(ctx context.Context, in *GenerateStatReq, opts ...grpc.CallOption) (*GenerateStatResp, error) {
	out := new(GenerateStatResp)
	err := c.cc.Invoke(ctx, "/virtual_image_card.VirtualImageCard/GenerateStat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageCardClient) GetUserCardInfo(ctx context.Context, in *GetUserCardInfoReq, opts ...grpc.CallOption) (*GetUserCardInfoResp, error) {
	out := new(GetUserCardInfoResp)
	err := c.cc.Invoke(ctx, "/virtual_image_card.VirtualImageCard/GetUserCardInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageCardClient) GetUserRedemptionInfo(ctx context.Context, in *GetUserRedemptionInfoReq, opts ...grpc.CallOption) (*GetUserRedemptionInfoResp, error) {
	out := new(GetUserRedemptionInfoResp)
	err := c.cc.Invoke(ctx, "/virtual_image_card.VirtualImageCard/GetUserRedemptionInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageCardClient) GetUserPackageList(ctx context.Context, in *GetUserPackageListReq, opts ...grpc.CallOption) (*GetUserPackageListResp, error) {
	out := new(GetUserPackageListResp)
	err := c.cc.Invoke(ctx, "/virtual_image_card.VirtualImageCard/GetUserPackageList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageCardClient) GetPurchaseHistory(ctx context.Context, in *GetPurchaseHistoryReq, opts ...grpc.CallOption) (*GetPurchaseHistoryResp, error) {
	out := new(GetPurchaseHistoryResp)
	err := c.cc.Invoke(ctx, "/virtual_image_card.VirtualImageCard/GetPurchaseHistory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageCardClient) AddPackage(ctx context.Context, in *AddPackageReq, opts ...grpc.CallOption) (*AddPackageResp, error) {
	out := new(AddPackageResp)
	err := c.cc.Invoke(ctx, "/virtual_image_card.VirtualImageCard/AddPackage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageCardClient) BatchAddGroupPackage(ctx context.Context, in *BatchAddGroupPackageReq, opts ...grpc.CallOption) (*BatchAddGroupPackageResp, error) {
	out := new(BatchAddGroupPackageResp)
	err := c.cc.Invoke(ctx, "/virtual_image_card.VirtualImageCard/BatchAddGroupPackage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageCardClient) ModifyPackage(ctx context.Context, in *ModifyPackageReq, opts ...grpc.CallOption) (*ModifyPackageResp, error) {
	out := new(ModifyPackageResp)
	err := c.cc.Invoke(ctx, "/virtual_image_card.VirtualImageCard/ModifyPackage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageCardClient) UpdatePackageStatus(ctx context.Context, in *UpdatePackageStatusReq, opts ...grpc.CallOption) (*UpdatePackageStatusResp, error) {
	out := new(UpdatePackageStatusResp)
	err := c.cc.Invoke(ctx, "/virtual_image_card.VirtualImageCard/UpdatePackageStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageCardClient) GetPackageListByStatus(ctx context.Context, in *GetPackageListByStatusReq, opts ...grpc.CallOption) (*GetPackageListByStatusResp, error) {
	out := new(GetPackageListByStatusResp)
	err := c.cc.Invoke(ctx, "/virtual_image_card.VirtualImageCard/GetPackageListByStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageCardClient) EditGroupPackage(ctx context.Context, in *EditGroupPackageReq, opts ...grpc.CallOption) (*EditGroupPackageResp, error) {
	out := new(EditGroupPackageResp)
	err := c.cc.Invoke(ctx, "/virtual_image_card.VirtualImageCard/EditGroupPackage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageCardClient) ModifyGroupPackage(ctx context.Context, in *ModifyGroupPackageReq, opts ...grpc.CallOption) (*ModifyGroupPackageResp, error) {
	out := new(ModifyGroupPackageResp)
	err := c.cc.Invoke(ctx, "/virtual_image_card.VirtualImageCard/ModifyGroupPackage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageCardClient) AddSalePackage(ctx context.Context, in *AddSalePackageReq, opts ...grpc.CallOption) (*AddSalePackageResp, error) {
	out := new(AddSalePackageResp)
	err := c.cc.Invoke(ctx, "/virtual_image_card.VirtualImageCard/AddSalePackage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageCardClient) UpdateSalePackage(ctx context.Context, in *UpdateSalePackageReq, opts ...grpc.CallOption) (*UpdateSalePackageResp, error) {
	out := new(UpdateSalePackageResp)
	err := c.cc.Invoke(ctx, "/virtual_image_card.VirtualImageCard/UpdateSalePackage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageCardClient) GetSalePackageListByStatus(ctx context.Context, in *GetSalePackageListByStatusReq, opts ...grpc.CallOption) (*GetSalePackageListByStatusResp, error) {
	out := new(GetSalePackageListByStatusResp)
	err := c.cc.Invoke(ctx, "/virtual_image_card.VirtualImageCard/GetSalePackageListByStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageCardClient) SalePackageSort(ctx context.Context, in *SalePackageSortReq, opts ...grpc.CallOption) (*SalePackageSortResp, error) {
	out := new(SalePackageSortResp)
	err := c.cc.Invoke(ctx, "/virtual_image_card.VirtualImageCard/SalePackageSort", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageCardClient) GetVirtualImageCardCommonCfg(ctx context.Context, in *GetVirtualImageCardCommonCfgRequest, opts ...grpc.CallOption) (*GetVirtualImageCardCommonCfgResponse, error) {
	out := new(GetVirtualImageCardCommonCfgResponse)
	err := c.cc.Invoke(ctx, "/virtual_image_card.VirtualImageCard/GetVirtualImageCardCommonCfg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageCardClient) GetVirtualImageCardEntryStatus(ctx context.Context, in *GetVirtualImageCardEntryStatusRequest, opts ...grpc.CallOption) (*GetVirtualImageCardEntryStatusResponse, error) {
	out := new(GetVirtualImageCardEntryStatusResponse)
	err := c.cc.Invoke(ctx, "/virtual_image_card.VirtualImageCard/GetVirtualImageCardEntryStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// VirtualImageCardServer is the server API for VirtualImageCard service.
type VirtualImageCardServer interface {
	// 下单
	PlaceOrder(context.Context, *PlaceOrderReq) (*PlaceOrderResp, error)
	// 取消订单
	CancelOrder(context.Context, *CancelOrderReq) (*CancelOrderResp, error)
	// 支付回调
	PayCallback(context.Context, *PayCallbackReq) (*PayCallbackResp, error)
	// 签约回调
	NotifyContract(context.Context, *NotifyContractReq) (*NotifyContractResp, error)
	// 苹果的自动下单回调
	PlaceAutoPayOrder(context.Context, *PlaceAutoPayOrderReq) (*PlaceAutoPayOrderResp, error)
	// 订单退款
	RevokeOrder(context.Context, *RevokeOrderReq) (*RevokeOrderResp, error)
	// 生成统计报表
	GenerateStat(context.Context, *GenerateStatReq) (*GenerateStatResp, error)
	// 获取用户卡信息
	GetUserCardInfo(context.Context, *GetUserCardInfoReq) (*GetUserCardInfoResp, error)
	// 获取用户核销信息
	GetUserRedemptionInfo(context.Context, *GetUserRedemptionInfoReq) (*GetUserRedemptionInfoResp, error)
	// 获取用户可用套餐列表
	GetUserPackageList(context.Context, *GetUserPackageListReq) (*GetUserPackageListResp, error)
	// 获取购买历史
	GetPurchaseHistory(context.Context, *GetPurchaseHistoryReq) (*GetPurchaseHistoryResp, error)
	// ========================== 套餐运营后台 ==================================
	// 新增套餐
	AddPackage(context.Context, *AddPackageReq) (*AddPackageResp, error)
	// 批量新增分组套餐
	BatchAddGroupPackage(context.Context, *BatchAddGroupPackageReq) (*BatchAddGroupPackageResp, error)
	// 修改套餐内容
	ModifyPackage(context.Context, *ModifyPackageReq) (*ModifyPackageResp, error)
	// 更新套餐/分组状态（停用/启用）
	UpdatePackageStatus(context.Context, *UpdatePackageStatusReq) (*UpdatePackageStatusResp, error)
	// 获取套餐/分组列表(按更新时间排序)
	GetPackageListByStatus(context.Context, *GetPackageListByStatusReq) (*GetPackageListByStatusResp, error)
	// 新增/编辑分组套餐
	EditGroupPackage(context.Context, *EditGroupPackageReq) (*EditGroupPackageResp, error)
	// 修改分组套餐内容
	ModifyGroupPackage(context.Context, *ModifyGroupPackageReq) (*ModifyGroupPackageResp, error)
	// 增加在售架套餐
	AddSalePackage(context.Context, *AddSalePackageReq) (*AddSalePackageResp, error)
	// 修改在售架套餐
	UpdateSalePackage(context.Context, *UpdateSalePackageReq) (*UpdateSalePackageResp, error)
	// 获取在售架套餐列表
	GetSalePackageListByStatus(context.Context, *GetSalePackageListByStatusReq) (*GetSalePackageListByStatusResp, error)
	// 修改在售架套餐排序
	SalePackageSort(context.Context, *SalePackageSortReq) (*SalePackageSortResp, error)
	// 获取无限卡配置
	GetVirtualImageCardCommonCfg(context.Context, *GetVirtualImageCardCommonCfgRequest) (*GetVirtualImageCardCommonCfgResponse, error)
	// 获取无限卡入口状态
	GetVirtualImageCardEntryStatus(context.Context, *GetVirtualImageCardEntryStatusRequest) (*GetVirtualImageCardEntryStatusResponse, error)
}

func RegisterVirtualImageCardServer(s *grpc.Server, srv VirtualImageCardServer) {
	s.RegisterService(&_VirtualImageCard_serviceDesc, srv)
}

func _VirtualImageCard_PlaceOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PlaceOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageCardServer).PlaceOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_card.VirtualImageCard/PlaceOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageCardServer).PlaceOrder(ctx, req.(*PlaceOrderReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageCard_CancelOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CancelOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageCardServer).CancelOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_card.VirtualImageCard/CancelOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageCardServer).CancelOrder(ctx, req.(*CancelOrderReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageCard_PayCallback_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PayCallbackReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageCardServer).PayCallback(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_card.VirtualImageCard/PayCallback",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageCardServer).PayCallback(ctx, req.(*PayCallbackReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageCard_NotifyContract_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NotifyContractReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageCardServer).NotifyContract(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_card.VirtualImageCard/NotifyContract",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageCardServer).NotifyContract(ctx, req.(*NotifyContractReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageCard_PlaceAutoPayOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PlaceAutoPayOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageCardServer).PlaceAutoPayOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_card.VirtualImageCard/PlaceAutoPayOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageCardServer).PlaceAutoPayOrder(ctx, req.(*PlaceAutoPayOrderReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageCard_RevokeOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RevokeOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageCardServer).RevokeOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_card.VirtualImageCard/RevokeOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageCardServer).RevokeOrder(ctx, req.(*RevokeOrderReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageCard_GenerateStat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenerateStatReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageCardServer).GenerateStat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_card.VirtualImageCard/GenerateStat",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageCardServer).GenerateStat(ctx, req.(*GenerateStatReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageCard_GetUserCardInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserCardInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageCardServer).GetUserCardInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_card.VirtualImageCard/GetUserCardInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageCardServer).GetUserCardInfo(ctx, req.(*GetUserCardInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageCard_GetUserRedemptionInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserRedemptionInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageCardServer).GetUserRedemptionInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_card.VirtualImageCard/GetUserRedemptionInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageCardServer).GetUserRedemptionInfo(ctx, req.(*GetUserRedemptionInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageCard_GetUserPackageList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserPackageListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageCardServer).GetUserPackageList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_card.VirtualImageCard/GetUserPackageList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageCardServer).GetUserPackageList(ctx, req.(*GetUserPackageListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageCard_GetPurchaseHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPurchaseHistoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageCardServer).GetPurchaseHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_card.VirtualImageCard/GetPurchaseHistory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageCardServer).GetPurchaseHistory(ctx, req.(*GetPurchaseHistoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageCard_AddPackage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddPackageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageCardServer).AddPackage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_card.VirtualImageCard/AddPackage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageCardServer).AddPackage(ctx, req.(*AddPackageReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageCard_BatchAddGroupPackage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchAddGroupPackageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageCardServer).BatchAddGroupPackage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_card.VirtualImageCard/BatchAddGroupPackage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageCardServer).BatchAddGroupPackage(ctx, req.(*BatchAddGroupPackageReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageCard_ModifyPackage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyPackageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageCardServer).ModifyPackage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_card.VirtualImageCard/ModifyPackage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageCardServer).ModifyPackage(ctx, req.(*ModifyPackageReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageCard_UpdatePackageStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePackageStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageCardServer).UpdatePackageStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_card.VirtualImageCard/UpdatePackageStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageCardServer).UpdatePackageStatus(ctx, req.(*UpdatePackageStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageCard_GetPackageListByStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPackageListByStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageCardServer).GetPackageListByStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_card.VirtualImageCard/GetPackageListByStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageCardServer).GetPackageListByStatus(ctx, req.(*GetPackageListByStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageCard_EditGroupPackage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EditGroupPackageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageCardServer).EditGroupPackage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_card.VirtualImageCard/EditGroupPackage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageCardServer).EditGroupPackage(ctx, req.(*EditGroupPackageReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageCard_ModifyGroupPackage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyGroupPackageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageCardServer).ModifyGroupPackage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_card.VirtualImageCard/ModifyGroupPackage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageCardServer).ModifyGroupPackage(ctx, req.(*ModifyGroupPackageReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageCard_AddSalePackage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddSalePackageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageCardServer).AddSalePackage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_card.VirtualImageCard/AddSalePackage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageCardServer).AddSalePackage(ctx, req.(*AddSalePackageReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageCard_UpdateSalePackage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateSalePackageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageCardServer).UpdateSalePackage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_card.VirtualImageCard/UpdateSalePackage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageCardServer).UpdateSalePackage(ctx, req.(*UpdateSalePackageReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageCard_GetSalePackageListByStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSalePackageListByStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageCardServer).GetSalePackageListByStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_card.VirtualImageCard/GetSalePackageListByStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageCardServer).GetSalePackageListByStatus(ctx, req.(*GetSalePackageListByStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageCard_SalePackageSort_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SalePackageSortReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageCardServer).SalePackageSort(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_card.VirtualImageCard/SalePackageSort",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageCardServer).SalePackageSort(ctx, req.(*SalePackageSortReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageCard_GetVirtualImageCardCommonCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetVirtualImageCardCommonCfgRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageCardServer).GetVirtualImageCardCommonCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_card.VirtualImageCard/GetVirtualImageCardCommonCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageCardServer).GetVirtualImageCardCommonCfg(ctx, req.(*GetVirtualImageCardCommonCfgRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageCard_GetVirtualImageCardEntryStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetVirtualImageCardEntryStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageCardServer).GetVirtualImageCardEntryStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_card.VirtualImageCard/GetVirtualImageCardEntryStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageCardServer).GetVirtualImageCardEntryStatus(ctx, req.(*GetVirtualImageCardEntryStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _VirtualImageCard_serviceDesc = grpc.ServiceDesc{
	ServiceName: "virtual_image_card.VirtualImageCard",
	HandlerType: (*VirtualImageCardServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "PlaceOrder",
			Handler:    _VirtualImageCard_PlaceOrder_Handler,
		},
		{
			MethodName: "CancelOrder",
			Handler:    _VirtualImageCard_CancelOrder_Handler,
		},
		{
			MethodName: "PayCallback",
			Handler:    _VirtualImageCard_PayCallback_Handler,
		},
		{
			MethodName: "NotifyContract",
			Handler:    _VirtualImageCard_NotifyContract_Handler,
		},
		{
			MethodName: "PlaceAutoPayOrder",
			Handler:    _VirtualImageCard_PlaceAutoPayOrder_Handler,
		},
		{
			MethodName: "RevokeOrder",
			Handler:    _VirtualImageCard_RevokeOrder_Handler,
		},
		{
			MethodName: "GenerateStat",
			Handler:    _VirtualImageCard_GenerateStat_Handler,
		},
		{
			MethodName: "GetUserCardInfo",
			Handler:    _VirtualImageCard_GetUserCardInfo_Handler,
		},
		{
			MethodName: "GetUserRedemptionInfo",
			Handler:    _VirtualImageCard_GetUserRedemptionInfo_Handler,
		},
		{
			MethodName: "GetUserPackageList",
			Handler:    _VirtualImageCard_GetUserPackageList_Handler,
		},
		{
			MethodName: "GetPurchaseHistory",
			Handler:    _VirtualImageCard_GetPurchaseHistory_Handler,
		},
		{
			MethodName: "AddPackage",
			Handler:    _VirtualImageCard_AddPackage_Handler,
		},
		{
			MethodName: "BatchAddGroupPackage",
			Handler:    _VirtualImageCard_BatchAddGroupPackage_Handler,
		},
		{
			MethodName: "ModifyPackage",
			Handler:    _VirtualImageCard_ModifyPackage_Handler,
		},
		{
			MethodName: "UpdatePackageStatus",
			Handler:    _VirtualImageCard_UpdatePackageStatus_Handler,
		},
		{
			MethodName: "GetPackageListByStatus",
			Handler:    _VirtualImageCard_GetPackageListByStatus_Handler,
		},
		{
			MethodName: "EditGroupPackage",
			Handler:    _VirtualImageCard_EditGroupPackage_Handler,
		},
		{
			MethodName: "ModifyGroupPackage",
			Handler:    _VirtualImageCard_ModifyGroupPackage_Handler,
		},
		{
			MethodName: "AddSalePackage",
			Handler:    _VirtualImageCard_AddSalePackage_Handler,
		},
		{
			MethodName: "UpdateSalePackage",
			Handler:    _VirtualImageCard_UpdateSalePackage_Handler,
		},
		{
			MethodName: "GetSalePackageListByStatus",
			Handler:    _VirtualImageCard_GetSalePackageListByStatus_Handler,
		},
		{
			MethodName: "SalePackageSort",
			Handler:    _VirtualImageCard_SalePackageSort_Handler,
		},
		{
			MethodName: "GetVirtualImageCardCommonCfg",
			Handler:    _VirtualImageCard_GetVirtualImageCardCommonCfg_Handler,
		},
		{
			MethodName: "GetVirtualImageCardEntryStatus",
			Handler:    _VirtualImageCard_GetVirtualImageCardEntryStatus_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/virtual-image-card/virtual-image-card.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/virtual-image-card/virtual-image-card.proto", fileDescriptor_virtual_image_card_1e8e060b450bf39d)
}

var fileDescriptor_virtual_image_card_1e8e060b450bf39d = []byte{
	// 3562 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x5a, 0xcd, 0x6f, 0x1b, 0x49,
	0x76, 0x1f, 0x7e, 0x88, 0x22, 0x1f, 0x3f, 0x44, 0x95, 0xf5, 0x41, 0xd1, 0x6b, 0x5b, 0xdb, 0x96,
	0x6d, 0xd9, 0xe3, 0x91, 0xb3, 0xde, 0xf5, 0x64, 0x67, 0x80, 0x8d, 0x43, 0xd1, 0x1c, 0x99, 0xb0,
	0x2c, 0x09, 0x4d, 0xca, 0x5e, 0x2f, 0x06, 0x69, 0xb4, 0xba, 0x4b, 0x64, 0x43, 0xad, 0xee, 0x76,
	0x57, 0x53, 0x12, 0x17, 0x41, 0x2e, 0xc9, 0x21, 0x08, 0x92, 0x43, 0x90, 0x43, 0x80, 0xfc, 0x03,
	0xb9, 0x04, 0xb9, 0x05, 0x39, 0xe6, 0x1e, 0xe4, 0x92, 0x3f, 0x20, 0x08, 0xf2, 0x3f, 0xe4, 0x92,
	0x3d, 0x05, 0xf5, 0xd1, 0xcd, 0xee, 0x66, 0x91, 0x92, 0x76, 0xe6, 0xc4, 0xae, 0xf7, 0x5e, 0x7d,
	0xbd, 0xf7, 0xea, 0xfd, 0x5e, 0xbd, 0x22, 0x7c, 0x1b, 0x04, 0x2f, 0x3e, 0x8f, 0x2c, 0xe3, 0x8c,
	0x58, 0xf6, 0x05, 0xf6, 0x5f, 0x5c, 0x58, 0x7e, 0x30, 0xd2, 0xed, 0xaf, 0xac, 0x73, 0x7d, 0x80,
	0xbf, 0x32, 0x74, 0xdf, 0x94, 0x90, 0x76, 0x3c, 0xdf, 0x0d, 0x5c, 0x84, 0x04, 0x47, 0x63, 0x1c,
	0x8d, 0x72, 0x9a, 0x3b, 0xa9, 0xf1, 0xf0, 0x55, 0x80, 0x1d, 0x62, 0xb9, 0xce, 0x0b, 0xd7, 0x0b,
	0x2c, 0xd7, 0x21, 0xe1, 0x2f, 0x1f, 0x43, 0xf9, 0x9f, 0x0c, 0x54, 0x8f, 0x6c, 0xdd, 0xc0, 0x87,
	0xbe, 0x89, 0x7d, 0x15, 0x7f, 0x46, 0x75, 0xc8, 0x8d, 0x2c, 0xb3, 0x91, 0xd9, 0xcc, 0x6c, 0x57,
	0x55, 0xfa, 0x89, 0xee, 0x01, 0x78, 0xba, 0x71, 0x46, 0xe7, 0xb0, 0xcc, 0x46, 0x96, 0x31, 0x4a,
	0x82, 0xd2, 0x35, 0xd1, 0x6b, 0x28, 0x7b, 0xfa, 0x58, 0x33, 0x86, 0xba, 0xe3, 0x60, 0xbb, 0x91,
	0xdb, 0xcc, 0x6c, 0xd7, 0x5e, 0xde, 0xdf, 0x99, 0x5e, 0xdc, 0xce, 0x91, 0x3e, 0x6e, 0x73, 0x29,
	0x15, 0xbc, 0xe8, 0x1b, 0xfd, 0x12, 0x1a, 0xae, 0x6f, 0x0d, 0x2c, 0x47, 0xb7, 0xb5, 0xc0, 0xd7,
	0x1d, 0xa2, 0x1b, 0x74, 0x89, 0x9a, 0x65, 0x92, 0x46, 0x7e, 0x33, 0xb7, 0x5d, 0x52, 0xd7, 0x42,
	0x7e, 0x7f, 0xc2, 0xee, 0x9a, 0x04, 0x6d, 0x41, 0x8d, 0x4e, 0xed, 0xf9, 0x96, 0x81, 0x35, 0x03,
	0x3b, 0x41, 0x63, 0x81, 0xad, 0xae, 0xe2, 0xe9, 0xe3, 0x23, 0x4a, 0x6c, 0x63, 0x27, 0x50, 0xfe,
	0x3b, 0x03, 0xb5, 0xf8, 0x1e, 0x89, 0x87, 0x36, 0xa0, 0xe8, 0xd2, 0x86, 0xe6, 0xb8, 0x6c, 0xa7,
	0x25, 0x75, 0x91, 0xb5, 0x0f, 0x5c, 0xb4, 0x02, 0x0b, 0x81, 0x7b, 0x86, 0x1d, 0xb6, 0xd1, 0x92,
	0xca, 0x1b, 0x68, 0x13, 0x2a, 0x86, 0x6d, 0x69, 0x51, 0xa7, 0x1c, 0x63, 0x82, 0x61, 0x5b, 0x87,
	0xa2, 0xdf, 0x63, 0x58, 0x9a, 0x48, 0x04, 0x56, 0x60, 0xe3, 0x46, 0x9e, 0x09, 0x55, 0x43, 0xa1,
	0x3e, 0x25, 0xa2, 0x07, 0x50, 0xe6, 0x32, 0x6c, 0xd5, 0x6c, 0xc1, 0x25, 0x15, 0x18, 0x89, 0x2d,
	0x99, 0x1a, 0x20, 0x20, 0x67, 0x8d, 0x02, 0x63, 0xd0, 0x4f, 0xda, 0x45, 0x68, 0x57, 0x3b, 0xd7,
	0xbd, 0xc6, 0xa2, 0x98, 0x9b, 0x93, 0xde, 0xeb, 0x9e, 0xf2, 0x2b, 0xa8, 0xb5, 0x75, 0xc7, 0xc0,
	0xf6, 0x1c, 0x2b, 0xc6, 0xb7, 0x9c, 0x4d, 0x6c, 0x59, 0x59, 0x86, 0xa5, 0x44, 0x77, 0xe2, 0x29,
	0xff, 0x47, 0x75, 0xa6, 0x8f, 0xdb, 0xba, 0x6d, 0x9f, 0xe8, 0xc6, 0x19, 0x1d, 0x72, 0x8e, 0xce,
	0xd2, 0xda, 0xc9, 0x4e, 0x69, 0x67, 0x0b, 0x6a, 0x6e, 0x30, 0xc4, 0x7e, 0x5a, 0x83, 0x15, 0x46,
	0x0d, 0xa5, 0xb6, 0xa1, 0x6e, 0xb8, 0x96, 0xa3, 0xc5, 0xfd, 0x89, 0x2b, 0xb1, 0x46, 0xe9, 0x13,
	0xff, 0xb9, 0x99, 0xe5, 0xd1, 0x2a, 0x14, 0xa8, 0x54, 0x40, 0x98, 0x36, 0x73, 0xea, 0x82, 0xa7,
	0x8f, 0xfb, 0x84, 0x3a, 0x34, 0xf5, 0xa1, 0x0b, 0xac, 0x51, 0x1d, 0x2d, 0x72, 0x87, 0xe6, 0x94,
	0x63, 0xcb, 0xa4, 0xea, 0x48, 0x6c, 0x9d, 0x78, 0xca, 0x7f, 0x66, 0x60, 0xf9, 0xc0, 0x0d, 0xac,
	0xd3, 0x71, 0xdb, 0x75, 0x02, 0x5f, 0x37, 0x02, 0xaa, 0x11, 0x6a, 0x17, 0xd1, 0xd4, 0x84, 0xb2,
	0xe9, 0xae, 0x05, 0xa9, 0x6b, 0xa2, 0x75, 0x58, 0xb4, 0x88, 0x46, 0xac, 0x01, 0xf7, 0xa6, 0xa2,
	0x5a, 0xb0, 0x48, 0xcf, 0x1a, 0x38, 0xa9, 0x15, 0xe4, 0x52, 0x2b, 0xa0, 0xbb, 0xf3, 0xb1, 0x6e,
	0x6b, 0x27, 0xa3, 0x31, 0xf6, 0x99, 0x48, 0x9e, 0xef, 0x8e, 0x52, 0x77, 0x29, 0xf1, 0x58, 0x9c,
	0x4b, 0xdf, 0x35, 0x47, 0x7c, 0x76, 0xee, 0x48, 0x25, 0x41, 0xe9, 0x9a, 0xe8, 0x3e, 0x94, 0x1d,
	0x7c, 0x15, 0x68, 0x09, 0x0d, 0x94, 0x28, 0xe9, 0x88, 0x6a, 0x41, 0x59, 0x01, 0x94, 0xde, 0x12,
	0xf1, 0x94, 0x0f, 0xb0, 0xc2, 0xce, 0x4a, 0x6b, 0x14, 0xb8, 0x47, 0xfa, 0x38, 0x72, 0xa8, 0x6b,
	0xf7, 0x9a, 0x5c, 0x4d, 0x36, 0xb5, 0x1a, 0x65, 0x1d, 0x56, 0x25, 0xe3, 0x12, 0x4f, 0xf9, 0x13,
	0xa8, 0xa9, 0xf8, 0xc2, 0x3d, 0x9b, 0x44, 0xa0, 0xc8, 0xd1, 0xa2, 0x79, 0xb8, 0xa3, 0x75, 0x4d,
	0xba, 0x0a, 0x87, 0xad, 0x59, 0x0b, 0xac, 0x73, 0x1c, 0xfa, 0x19, 0x27, 0xf5, 0xad, 0x73, 0x1c,
	0xfa, 0x7d, 0x2e, 0xf2, 0x7b, 0x6a, 0xcd, 0xc4, 0xf8, 0xc4, 0x53, 0xde, 0xc1, 0xd2, 0x1e, 0x76,
	0xb0, 0xaf, 0x07, 0xb8, 0x17, 0xe8, 0xcc, 0x94, 0xf7, 0x00, 0x48, 0xa0, 0xfb, 0x01, 0x1f, 0x97,
	0xcf, 0x5a, 0x62, 0x14, 0x36, 0xec, 0x06, 0x14, 0xb1, 0x63, 0xc6, 0x27, 0x5d, 0xc4, 0x8e, 0x49,
	0x59, 0x0a, 0x82, 0x7a, 0x72, 0x30, 0xe2, 0x29, 0xff, 0x9e, 0x81, 0xca, 0x31, 0xc1, 0x7e, 0x5b,
	0xf7, 0xcd, 0xae, 0x73, 0xea, 0x4a, 0x8e, 0xe3, 0x5d, 0x28, 0xe1, 0xd3, 0x53, 0x6c, 0x04, 0xd4,
	0x36, 0x59, 0x66, 0x9b, 0x22, 0x27, 0xf4, 0x09, 0x63, 0x5e, 0x79, 0x96, 0x8f, 0x29, 0x33, 0x27,
	0x98, 0x8c, 0xd0, 0x27, 0x68, 0x17, 0x4a, 0xa1, 0xda, 0x79, 0x7c, 0x2c, 0xbf, 0xdc, 0x92, 0x45,
	0x5b, 0xb6, 0x80, 0xd0, 0x3e, 0xce, 0xa9, 0xab, 0x4e, 0xba, 0xa1, 0x67, 0xb0, 0x6c, 0x5a, 0xc4,
	0x70, 0x47, 0x4e, 0xa0, 0x45, 0xba, 0xe6, 0x1e, 0xb4, 0x14, 0x32, 0x0e, 0xb9, 0xce, 0x95, 0xdf,
	0x65, 0xa0, 0x9e, 0x1e, 0x4b, 0xb2, 0xa1, 0x94, 0x83, 0x64, 0xa7, 0x1c, 0xe4, 0x07, 0xe3, 0x44,
	0xca, 0xa1, 0xf3, 0x29, 0x87, 0xa6, 0x5a, 0x33, 0x7c, 0xac, 0x07, 0x4c, 0x6b, 0x0b, 0x5c, 0x6b,
	0x9c, 0xd0, 0x27, 0xe8, 0x15, 0x2c, 0x0a, 0xc8, 0x62, 0x27, 0xa1, 0xfc, 0xf2, 0xae, 0x7c, 0x66,
	0x26, 0xa2, 0x86, 0xb2, 0xca, 0x63, 0x40, 0x7b, 0x38, 0x88, 0xdb, 0x52, 0x1a, 0x5d, 0x95, 0xbf,
	0xc9, 0xc0, 0x9d, 0x29, 0x41, 0xe2, 0xa1, 0x5f, 0x40, 0x9e, 0x0e, 0xcc, 0x44, 0xcb, 0x2f, 0x37,
	0x67, 0xda, 0x29, 0xec, 0xc3, 0xa4, 0xd1, 0x1f, 0xc3, 0x3d, 0x61, 0x7f, 0xdd, 0xc6, 0x7e, 0xa0,
	0x91, 0x40, 0x0f, 0x46, 0x44, 0x3b, 0xc3, 0xd8, 0xd3, 0x86, 0xee, 0xc8, 0x17, 0x20, 0xbc, 0xc1,
	0x85, 0x5a, 0x54, 0xa6, 0xc7, 0x44, 0xde, 0x61, 0xec, 0xbd, 0x75, 0x47, 0xbe, 0xf2, 0x1c, 0x1a,
	0x62, 0x39, 0x2a, 0x36, 0xf1, 0x39, 0xc3, 0xfc, 0xd9, 0xab, 0xf7, 0x60, 0x63, 0x86, 0x34, 0xf1,
	0x50, 0x0f, 0x9e, 0x0c, 0x75, 0x1a, 0xc5, 0x9c, 0x81, 0x8d, 0x69, 0x48, 0x12, 0x1e, 0x73, 0xa9,
	0x5b, 0x81, 0xe5, 0x0c, 0x34, 0x3f, 0xea, 0xc2, 0x86, 0x2c, 0xaa, 0xca, 0x50, 0x27, 0x3d, 0x26,
	0xbd, 0x3b, 0xe2, 0x07, 0xfd, 0x23, 0x17, 0x9d, 0x0c, 0xae, 0x0c, 0x60, 0x55, 0xcc, 0x28, 0x54,
	0xbe, 0x6f, 0x91, 0x40, 0x0e, 0x5c, 0x77, 0xa1, 0x74, 0xae, 0xfb, 0x67, 0x38, 0x98, 0x64, 0x1f,
	0x45, 0x4e, 0xe0, 0x01, 0xc1, 0xb0, 0x2d, 0xec, 0x04, 0x5a, 0x30, 0xf6, 0xb0, 0x38, 0xf7, 0xc0,
	0x49, 0xfd, 0xb1, 0x87, 0x95, 0xef, 0x61, 0x4d, 0x36, 0x11, 0xf1, 0xd0, 0x2e, 0x54, 0xc2, 0xb4,
	0xc6, 0xb6, 0x48, 0xd0, 0xc8, 0xb0, 0xa3, 0xf4, 0x60, 0x96, 0x89, 0x42, 0xd7, 0x28, 0x7b, 0x93,
	0x71, 0x94, 0x7f, 0xc8, 0x43, 0x39, 0xc6, 0x4c, 0xa5, 0x4a, 0x99, 0x74, 0xaa, 0x84, 0x20, 0xef,
	0xe8, 0x51, 0x08, 0x61, 0xdf, 0x94, 0x66, 0x62, 0x62, 0x08, 0x3c, 0x64, 0xdf, 0xe8, 0x39, 0x20,
	0x63, 0xe4, 0xfb, 0x74, 0x5b, 0x31, 0x84, 0xe3, 0x18, 0x50, 0x17, 0x9c, 0x09, 0xca, 0xed, 0xc0,
	0x9d, 0x28, 0x7f, 0x9a, 0x02, 0xc4, 0xe5, 0x90, 0x35, 0x91, 0x7f, 0x04, 0xb5, 0xe8, 0xf0, 0xdb,
	0xfa, 0x09, 0xb6, 0x45, 0xae, 0x51, 0x0d, 0xa9, 0xfb, 0x94, 0x48, 0xc1, 0xd8, 0xd4, 0x2d, 0x3b,
	0x01, 0xb2, 0x1c, 0x2b, 0x6b, 0x8c, 0x3e, 0x19, 0x70, 0x1a, 0x8c, 0x8b, 0x12, 0x30, 0xde, 0x84,
	0x32, 0xbe, 0xf2, 0x6c, 0xdd, 0xd1, 0x99, 0xaf, 0x94, 0xd8, 0x9c, 0x71, 0x92, 0x80, 0x4b, 0x7d,
	0x14, 0xb8, 0x0d, 0x08, 0xe1, 0x92, 0xe2, 0x05, 0x7a, 0x0b, 0xf5, 0x58, 0xe8, 0xe0, 0xe6, 0x2a,
	0x6f, 0xe6, 0x6e, 0x10, 0x3f, 0x6a, 0x93, 0xf8, 0x41, 0x0d, 0x96, 0x42, 0xa9, 0x4a, 0x1a, 0x33,
	0xdf, 0x41, 0x8d, 0x0c, 0xdd, 0x4b, 0xcd, 0x70, 0x1d, 0xd3, 0x62, 0xcb, 0xac, 0xb2, 0x83, 0x2b,
	0x0d, 0xb0, 0x6f, 0x2c, 0xe2, 0xd9, 0x3a, 0x85, 0x4f, 0x2e, 0xab, 0x56, 0x69, 0xdf, 0xa8, 0xa9,
	0xfc, 0x86, 0xf9, 0xf8, 0xd1, 0xc8, 0x37, 0x86, 0x3a, 0xc1, 0x6f, 0x2d, 0x12, 0xb8, 0xfe, 0x58,
	0xee, 0xe3, 0x08, 0xf2, 0x1e, 0x0d, 0x4d, 0xdc, 0xbd, 0xd9, 0x37, 0xf5, 0x7b, 0xfa, 0xab, 0x11,
	0xeb, 0xb7, 0xa1, 0x63, 0x17, 0x29, 0xa1, 0x67, 0xfd, 0x16, 0x2b, 0x7f, 0xca, 0xdc, 0x7a, 0x6a,
	0x6c, 0xe2, 0xa1, 0x3d, 0xa8, 0x7a, 0x82, 0x1c, 0xf7, 0x6b, 0x45, 0xaa, 0x28, 0x21, 0xa8, 0x62,
	0xc3, 0xf5, 0x4d, 0xb5, 0x12, 0x76, 0x64, 0xaa, 0xda, 0x80, 0x22, 0x3d, 0xf7, 0xe7, 0xae, 0x8f,
	0x45, 0xf6, 0xb2, 0x38, 0xd4, 0xc9, 0x7b, 0xd7, 0xc7, 0xca, 0x5f, 0xd3, 0xec, 0x30, 0xd1, 0x77,
	0x1e, 0x68, 0x23, 0xc8, 0xc7, 0x80, 0x93, 0x7d, 0x4b, 0xbd, 0x7e, 0x05, 0x16, 0x78, 0x4e, 0xcc,
	0x53, 0x3e, 0xde, 0x40, 0x3f, 0x85, 0x0a, 0x5d, 0x86, 0x8f, 0x4f, 0x47, 0x8e, 0x89, 0x39, 0x4a,
	0x15, 0xd5, 0xf2, 0x50, 0x27, 0xaa, 0x20, 0x29, 0x7f, 0x97, 0x87, 0xc5, 0xf0, 0x04, 0xd6, 0x20,
	0x1b, 0xa9, 0x36, 0x6b, 0x5d, 0x97, 0x96, 0x44, 0x27, 0x32, 0x27, 0x39, 0x91, 0xf9, 0xd8, 0xda,
	0x28, 0x4d, 0x1f, 0x13, 0x71, 0xa8, 0xd8, 0xf7, 0xac, 0x73, 0x57, 0x98, 0x75, 0xee, 0xd8, 0x52,
	0x52, 0x47, 0xa9, 0xe4, 0xc5, 0xd9, 0x16, 0xd1, 0xb0, 0xa3, 0x9f, 0xd8, 0xd8, 0x64, 0x27, 0xa8,
	0xa8, 0x96, 0x2c, 0xd2, 0xe1, 0x04, 0xd4, 0x84, 0xa2, 0xeb, 0xd1, 0x34, 0xc3, 0xf5, 0xc5, 0xd9,
	0x89, 0xda, 0xc9, 0x10, 0x09, 0xa9, 0x10, 0xb9, 0x01, 0xc5, 0x81, 0xef, 0x8e, 0x3c, 0xca, 0x2b,
	0x33, 0xde, 0x22, 0x6b, 0x77, 0x59, 0x68, 0x1d, 0x79, 0xa6, 0x40, 0x4c, 0x7a, 0x18, 0xf2, 0x6a,
	0x91, 0x13, 0x58, 0x9e, 0x11, 0xc5, 0x47, 0x16, 0x5b, 0xab, 0x0c, 0xb0, 0x1f, 0xcc, 0x81, 0x4d,
	0x1a, 0x70, 0xa3, 0xf8, 0x48, 0x1b, 0x54, 0x45, 0x51, 0xa8, 0x89, 0xed, 0xbd, 0xc6, 0x55, 0x14,
	0xb2, 0xe6, 0x85, 0xa6, 0x25, 0x59, 0x68, 0x7a, 0x08, 0x11, 0x41, 0x63, 0xa6, 0xaa, 0xf3, 0xcb,
	0x44, 0x48, 0x7c, 0x83, 0x89, 0xa1, 0x1c, 0x42, 0xb5, 0x65, 0x9a, 0x61, 0xd8, 0xc6, 0x9f, 0xd1,
	0x1f, 0x4d, 0x36, 0x64, 0x39, 0xa7, 0xae, 0xc0, 0xe4, 0xb9, 0x79, 0x40, 0xb8, 0x19, 0x0a, 0x86,
	0x4a, 0x1d, 0x6a, 0xf1, 0x01, 0x89, 0xa7, 0xfc, 0x19, 0xac, 0x1d, 0x33, 0x75, 0x09, 0x22, 0x07,
	0x61, 0x91, 0x4f, 0xc6, 0x8c, 0x99, 0x49, 0x1b, 0x93, 0x7b, 0x69, 0x36, 0xf2, 0xd2, 0x0d, 0x28,
	0x5a, 0x44, 0x63, 0x66, 0x61, 0xae, 0x58, 0x54, 0x17, 0x2d, 0xb2, 0x47, 0x9b, 0x09, 0xbb, 0xe7,
	0x93, 0x76, 0x57, 0x36, 0x60, 0x5d, 0x3a, 0x3f, 0x5b, 0x1a, 0x85, 0xf4, 0x18, 0xe6, 0xed, 0x8e,
	0x6f, 0xbc, 0xba, 0xb4, 0xe5, 0xb3, 0xb7, 0xb7, 0xbc, 0xf2, 0xcf, 0x19, 0x80, 0xf7, 0xd6, 0x55,
	0x78, 0x2c, 0xe3, 0x1b, 0xcc, 0x24, 0x37, 0x98, 0x36, 0x4b, 0xf6, 0x76, 0x66, 0x41, 0x1d, 0xa8,
	0x72, 0xff, 0x0e, 0xf3, 0xbb, 0xdc, 0xec, 0x5c, 0x8b, 0xcd, 0x18, 0x8e, 0x52, 0x19, 0xc4, 0x5a,
	0x8a, 0x06, 0xcd, 0x59, 0x0a, 0x23, 0x1e, 0x6a, 0x49, 0x93, 0x05, 0x29, 0xfa, 0x4c, 0x76, 0x9d,
	0xcc, 0x15, 0xfe, 0x31, 0x0b, 0xf5, 0x34, 0x64, 0xb0, 0x9c, 0xd5, 0xa5, 0x37, 0x0b, 0xaa, 0x67,
	0x1e, 0xb5, 0x8a, 0x94, 0xc0, 0x4e, 0xcf, 0x6b, 0x28, 0xd3, 0x11, 0x45, 0xfa, 0xd7, 0xc8, 0xce,
	0x46, 0x3c, 0x9a, 0x3f, 0x8a, 0xd5, 0x82, 0x11, 0x7d, 0x53, 0x40, 0x1d, 0x11, 0xec, 0x6b, 0xf6,
	0x85, 0x00, 0x90, 0x02, 0x6d, 0xee, 0x5f, 0x50, 0x86, 0x6f, 0x19, 0x43, 0xca, 0xe0, 0x59, 0x45,
	0x81, 0x36, 0xf7, 0x2f, 0xf8, 0x05, 0xeb, 0xc4, 0xb2, 0xad, 0x60, 0x4c, 0x99, 0x3c, 0xdc, 0x41,
	0x48, 0xda, 0xbf, 0xa0, 0x37, 0x07, 0x0f, 0xbb, 0x9e, 0x8d, 0xb9, 0x31, 0xb9, 0x36, 0x0a, 0xac,
	0x4a, 0xb3, 0xc4, 0x19, 0x4c, 0xc7, 0x6c, 0xc7, 0x5f, 0x43, 0xb1, 0x1d, 0xee, 0x65, 0x19, 0xaa,
	0x9d, 0x83, 0xe3, 0xf7, 0xda, 0x71, 0xaf, 0xa3, 0x6a, 0xad, 0xfd, 0xfd, 0xfa, 0x17, 0x68, 0x1d,
	0xee, 0x4c, 0x48, 0xbd, 0xa3, 0x4e, 0xbb, 0xfb, 0x5d, 0xb7, 0xf3, 0xa6, 0x9e, 0x51, 0xfe, 0x2b,
	0x07, 0xe5, 0x9e, 0x6e, 0x87, 0x5e, 0x4d, 0x57, 0x4b, 0x74, 0x3b, 0x96, 0x52, 0x15, 0x68, 0xb3,
	0x6b, 0xfe, 0x60, 0xd7, 0xd9, 0x80, 0xe2, 0x09, 0x1e, 0x58, 0x4e, 0x78, 0xcd, 0xca, 0xab, 0x8b,
	0xac, 0xdd, 0x27, 0x68, 0x15, 0x0a, 0xec, 0xc6, 0xc7, 0xef, 0x19, 0x79, 0x75, 0x81, 0xde, 0xf7,
	0xc2, 0xcb, 0x97, 0xc8, 0x0d, 0x16, 0x6e, 0x91, 0x1b, 0x4c, 0xba, 0xa1, 0x35, 0x28, 0x5c, 0x62,
	0x6b, 0x30, 0x0c, 0xa1, 0x42, 0xb4, 0xd0, 0x77, 0x50, 0x66, 0xdb, 0x14, 0xe6, 0x5e, 0x64, 0xa7,
	0xee, 0x91, 0x6c, 0xf4, 0x98, 0x72, 0x42, 0xab, 0xd3, 0x9e, 0xc2, 0xea, 0x2b, 0xb0, 0xc0, 0x63,
	0x67, 0x91, 0xe3, 0x28, 0x6b, 0x5c, 0x87, 0x1f, 0x13, 0x1c, 0x80, 0x14, 0x0e, 0x34, 0x60, 0xd1,
	0xc7, 0x14, 0x4d, 0x08, 0x83, 0x8f, 0x92, 0x1a, 0x36, 0x13, 0xc8, 0x52, 0x99, 0x42, 0x96, 0x09,
	0x22, 0x55, 0x93, 0x88, 0xa4, 0xfc, 0x05, 0x8d, 0x0d, 0xbc, 0x41, 0xad, 0x90, 0x90, 0xcd, 0xa4,
	0xd0, 0xeb, 0x1a, 0xfc, 0x4e, 0x26, 0xdc, 0xb9, 0x74, 0xc2, 0x9d, 0xd8, 0x58, 0x3e, 0xb9, 0x31,
	0xe5, 0x7f, 0xb3, 0x50, 0x89, 0x07, 0x84, 0xc4, 0x7e, 0x32, 0xc9, 0xfd, 0xfc, 0x08, 0x21, 0xf1,
	0xc6, 0xb9, 0xc6, 0x6d, 0xf3, 0xf9, 0x64, 0x5e, 0x51, 0x48, 0xe7, 0x15, 0x61, 0xea, 0x42, 0xfd,
	0x69, 0x41, 0xa4, 0x2e, 0xaf, 0xa1, 0x2c, 0x54, 0xce, 0xce, 0x6f, 0x71, 0x4e, 0x34, 0x8b, 0xec,
	0xa4, 0x02, 0xef, 0x12, 0xe6, 0xd1, 0x31, 0x04, 0x29, 0xcd, 0x4b, 0x56, 0x20, 0x05, 0x5a, 0x23,
	0x58, 0xdf, 0xd5, 0x03, 0x63, 0xd8, 0x32, 0xcd, 0x44, 0x38, 0xc6, 0x9f, 0xd1, 0x6b, 0x80, 0x58,
	0x54, 0xe1, 0x31, 0xf6, 0xfa, 0x38, 0x5e, 0x1a, 0x84, 0x11, 0x47, 0xc0, 0x8c, 0x31, 0xc4, 0xc6,
	0x59, 0x98, 0xb3, 0x5a, 0xa4, 0x4d, 0x9b, 0x4a, 0x13, 0x1a, 0xf2, 0x69, 0x89, 0xc7, 0x6e, 0xef,
	0x1d, 0xd3, 0x0a, 0xd2, 0xeb, 0x99, 0xe3, 0x10, 0x29, 0x0d, 0x66, 0x6f, 0xad, 0xc1, 0xb8, 0x8a,
	0x72, 0x29, 0x15, 0xad, 0xc1, 0xca, 0xf4, 0x72, 0x88, 0xa7, 0xa8, 0x50, 0x7f, 0xef, 0x9a, 0xd6,
	0xe9, 0xf8, 0x47, 0xcc, 0x6a, 0xee, 0xc0, 0x72, 0x6a, 0x4c, 0x56, 0x94, 0x5b, 0xe5, 0xc4, 0xb4,
	0x46, 0xa6, 0xc0, 0x36, 0xf3, 0x7b, 0x81, 0x6d, 0x03, 0xd6, 0x64, 0xe3, 0x13, 0x4f, 0x31, 0x60,
	0xb9, 0x65, 0x9a, 0xb1, 0x00, 0x47, 0x67, 0xfd, 0x39, 0xe4, 0x63, 0x7b, 0x7b, 0x70, 0x4d, 0x48,
	0x54, 0x99, 0xf0, 0x3c, 0x5f, 0xd8, 0x03, 0x94, 0x9e, 0x84, 0x78, 0xe8, 0x67, 0xb0, 0xaa, 0xdb,
	0x3e, 0xd6, 0xcd, 0xb1, 0xe6, 0x3a, 0x1a, 0x19, 0x62, 0xfb, 0x74, 0xe2, 0x88, 0x55, 0x15, 0x09,
	0xe6, 0xa1, 0xd3, 0xa3, 0x2c, 0x86, 0x70, 0xef, 0x60, 0x85, 0x27, 0x60, 0x3f, 0xc2, 0x82, 0x95,
	0x75, 0x58, 0x95, 0x0c, 0xc6, 0xac, 0x71, 0x6f, 0x0f, 0x07, 0x31, 0x6a, 0x3a, 0x9f, 0xfb, 0x15,
	0x14, 0x04, 0x68, 0x64, 0x6e, 0x03, 0x1a, 0xa2, 0x93, 0x62, 0xc2, 0xfd, 0x79, 0xe3, 0xdf, 0xae,
	0x56, 0x12, 0x5f, 0x7c, 0x22, 0xff, 0xf9, 0x1a, 0x50, 0x7c, 0x09, 0xae, 0xcf, 0xea, 0x3d, 0x9b,
	0x50, 0x11, 0xd8, 0x1e, 0xd7, 0x35, 0x70, 0x80, 0x67, 0xfd, 0x56, 0xe1, 0xce, 0x54, 0x3f, 0x7e,
	0x16, 0x5a, 0x27, 0xee, 0x28, 0xe8, 0xbb, 0x1d, 0x56, 0x05, 0x6b, 0x9f, 0x0e, 0x68, 0xa8, 0xb3,
	0x0c, 0x51, 0x87, 0x2a, 0xa9, 0xec, 0x9b, 0x26, 0x2c, 0x89, 0x5a, 0x1a, 0xbb, 0x8a, 0x2e, 0xb1,
	0xc3, 0xbc, 0x14, 0xab, 0x9f, 0xb1, 0x5a, 0xee, 0x23, 0x78, 0xb8, 0x87, 0x83, 0x0f, 0x7c, 0x53,
	0x5d, 0xba, 0x27, 0x9a, 0x58, 0xb5, 0xdd, 0xf3, 0x73, 0xd7, 0x69, 0x9f, 0x0e, 0x54, 0xfc, 0x79,
	0x84, 0x49, 0xa0, 0xfc, 0xe5, 0x22, 0x6c, 0xcd, 0x97, 0x23, 0x9e, 0xeb, 0x10, 0x8c, 0x9e, 0x40,
	0xfd, 0x52, 0xb7, 0x02, 0x2d, 0x70, 0x59, 0xdd, 0x2c, 0xb6, 0xb6, 0x2a, 0xa5, 0xf7, 0xdd, 0xdd,
	0xd1, 0xb8, 0x4b, 0x17, 0xb9, 0x0d, 0xf5, 0xd0, 0xf5, 0x22, 0x41, 0x8e, 0x75, 0x35, 0x41, 0x0f,
	0x25, 0xbf, 0x87, 0x86, 0x4e, 0xb7, 0x4d, 0xc7, 0x14, 0xfb, 0x32, 0x4e, 0x07, 0x5c, 0x77, 0xb9,
	0xd9, 0xc5, 0xe0, 0xb4, 0xaa, 0xd4, 0x15, 0x3d, 0x45, 0x61, 0x41, 0xe9, 0x5b, 0x68, 0x9e, 0x5a,
	0x3e, 0x09, 0x34, 0xec, 0x04, 0xd8, 0xd7, 0x44, 0xf6, 0xe9, 0xfa, 0x58, 0x1b, 0xf9, 0xe1, 0x53,
	0xcc, 0x1a, 0x93, 0xe8, 0x50, 0x01, 0x9e, 0x76, 0xba, 0x3e, 0x3e, 0xf6, 0x6d, 0x9a, 0xa5, 0xe9,
	0xa6, 0x16, 0xe0, 0xab, 0x40, 0x54, 0x92, 0x0b, 0xba, 0xd9, 0xc7, 0x57, 0xf4, 0x52, 0xb7, 0xe4,
	0x68, 0xa6, 0x3e, 0xd6, 0x58, 0x69, 0xc5, 0x75, 0x0c, 0x2c, 0x40, 0xaa, 0xe2, 0xbc, 0xd1, 0xc7,
	0xbd, 0xa1, 0x7b, 0x79, 0xe8, 0x18, 0xec, 0x61, 0x8c, 0xee, 0xe4, 0x02, 0xfb, 0x84, 0x26, 0x57,
	0x8b, 0xa2, 0x94, 0x77, 0x3a, 0xf8, 0xc0, 0x29, 0xe8, 0x21, 0xd4, 0xe2, 0xda, 0x3c, 0x19, 0x88,
	0x04, 0xa7, 0x1c, 0xe9, 0x72, 0x77, 0x80, 0xb6, 0xa0, 0x16, 0xd7, 0xe4, 0xc9, 0x40, 0x24, 0x3b,
	0x95, 0x89, 0x1e, 0x77, 0x07, 0xe8, 0x4b, 0x40, 0x69, 0x2d, 0x9e, 0x0c, 0x04, 0x52, 0x2d, 0x25,
	0x34, 0xb3, 0x3b, 0x40, 0xdf, 0xc0, 0x06, 0xd7, 0x81, 0x8f, 0x89, 0x65, 0x62, 0x87, 0x69, 0xc7,
	0x17, 0x56, 0xe2, 0x29, 0xd1, 0x1a, 0x13, 0x50, 0x05, 0xbf, 0x43, 0xd9, 0xcc, 0x5a, 0xaf, 0x60,
	0x9d, 0x77, 0x0d, 0xf4, 0x13, 0x26, 0xaf, 0x11, 0x6c, 0x63, 0x23, 0xc0, 0x61, 0xed, 0x69, 0x85,
	0xb1, 0xfb, 0xfa, 0x09, 0x15, 0xef, 0x09, 0xde, 0x64, 0xc6, 0xa8, 0xdb, 0xc8, 0x89, 0x3a, 0x56,
	0x63, 0x33, 0x8a, 0x8e, 0xc7, 0x11, 0x57, 0xee, 0xee, 0x35, 0xa9, 0xbb, 0xcf, 0xb1, 0xf6, 0xb9,
	0xf9, 0x4a, 0xdc, 0xbc, 0x65, 0xd6, 0x7e, 0x6f, 0xbe, 0x42, 0xdb, 0xb0, 0xec, 0x19, 0x5a, 0xca,
	0x1e, 0xfc, 0x1a, 0x5e, 0xf5, 0x8c, 0x8f, 0x31, 0x8b, 0x3c, 0x65, 0x92, 0x29, 0xa3, 0x2c, 0x73,
	0xe7, 0xf6, 0x8c, 0x56, 0xdc, 0x2c, 0x7f, 0x00, 0xab, 0x54, 0x74, 0xda, 0x32, 0x88, 0x89, 0x2f,
	0x7b, 0x46, 0x2b, 0x69, 0x1b, 0xe5, 0x1b, 0x78, 0x24, 0x39, 0x89, 0xcc, 0x00, 0x51, 0x78, 0xa4,
	0x67, 0x56, 0x52, 0xf4, 0xfe, 0xb7, 0x0c, 0x3c, 0xbe, 0xae, 0xaf, 0x38, 0xc7, 0x0f, 0x58, 0xe9,
	0x92, 0xbd, 0xc7, 0x84, 0xcf, 0x43, 0x55, 0x15, 0xc4, 0x8b, 0x0c, 0xd5, 0xe4, 0x16, 0xd4, 0x6c,
	0xf7, 0x52, 0x64, 0x6b, 0xec, 0x08, 0xf0, 0xd3, 0x5b, 0xb1, 0xdd, 0x4b, 0x96, 0xa8, 0xb1, 0x83,
	0xb0, 0x0a, 0x05, 0xdd, 0xd4, 0x2c, 0xf3, 0x4a, 0x24, 0xaa, 0x0b, 0xba, 0xd9, 0x35, 0xaf, 0xd2,
	0x8e, 0x9f, 0x9f, 0x72, 0xfc, 0x35, 0x28, 0x90, 0x4b, 0x2b, 0x30, 0x86, 0xa2, 0xf8, 0x25, 0x5a,
	0xcf, 0x08, 0x40, 0xec, 0x49, 0xf4, 0x2e, 0xac, 0x1f, 0xb5, 0x3e, 0x69, 0xed, 0xb7, 0xad, 0x83,
	0x83, 0xce, 0xbe, 0x76, 0x7c, 0x30, 0xb9, 0x52, 0x7d, 0x81, 0xd6, 0x00, 0xc5, 0x99, 0xad, 0xfd,
	0xee, 0x51, 0xeb, 0x53, 0x3d, 0x93, 0xa6, 0x7f, 0xec, 0xb4, 0xdf, 0xb6, 0xfa, 0xf5, 0x2c, 0x6a,
	0xc0, 0x4a, 0x42, 0xfe, 0xe8, 0xa8, 0xd7, 0x3f, 0x54, 0x3b, 0xf5, 0xdc, 0x33, 0x1d, 0xca, 0xb1,
	0x0c, 0x17, 0xfd, 0x04, 0x1a, 0x47, 0xad, 0xf6, 0xbb, 0xd6, 0x5e, 0x47, 0xeb, 0x7f, 0x3a, 0xea,
	0xa4, 0xa6, 0x5d, 0x87, 0x3b, 0x09, 0xee, 0xc1, 0xa1, 0xfa, 0xbe, 0xb5, 0x5f, 0xcf, 0xf0, 0xc5,
	0xc6, 0x18, 0xad, 0xe3, 0xfe, 0xa1, 0xa6, 0x76, 0x0e, 0x3a, 0x1f, 0xeb, 0xd9, 0x67, 0xff, 0x92,
	0x81, 0xe5, 0x29, 0xb4, 0x42, 0xdb, 0xb0, 0xc5, 0xae, 0x8b, 0xbd, 0xd6, 0x7e, 0x47, 0x0b, 0x3b,
	0xf7, 0xfa, 0xad, 0xfe, 0x71, 0x2f, 0x35, 0xab, 0x02, 0xf7, 0x67, 0x4a, 0xf6, 0xde, 0x76, 0xf6,
	0xbf, 0xab, 0x67, 0xd0, 0x53, 0x78, 0x34, 0x53, 0xe6, 0xe0, 0xb0, 0xaf, 0x1d, 0x1e, 0x08, 0xd1,
	0x2c, 0x7a, 0x02, 0x0f, 0x67, 0x8a, 0x7e, 0x6c, 0x75, 0xfb, 0x42, 0x30, 0xf7, 0xec, 0xaf, 0x32,
	0x00, 0xed, 0xf8, 0xed, 0x9b, 0xdf, 0x6f, 0xa3, 0x51, 0xb5, 0xc3, 0xa3, 0xce, 0x41, 0xec, 0xe2,
	0x2b, 0x18, 0x94, 0xda, 0x3d, 0xd8, 0xe3, 0x5a, 0x89, 0x33, 0x7a, 0x87, 0x87, 0x07, 0x5a, 0xe7,
	0xd7, 0x47, 0x5d, 0xb5, 0x53, 0xcf, 0xa6, 0x7b, 0x71, 0xfa, 0x9b, 0x7a, 0x0e, 0xad, 0x40, 0x3d,
	0xd1, 0xab, 0xbb, 0x77, 0x50, 0xcf, 0xbf, 0xfc, 0xa7, 0x15, 0xa8, 0xa7, 0x7d, 0x1b, 0xf5, 0x00,
	0x26, 0xff, 0x84, 0x40, 0x3f, 0x95, 0x26, 0x89, 0xf1, 0x7f, 0x83, 0x34, 0x95, 0xeb, 0x44, 0x88,
	0x87, 0x3e, 0x40, 0x39, 0xf6, 0xf7, 0x01, 0xa4, 0xc8, 0x0b, 0x14, 0xf1, 0xbf, 0x27, 0x34, 0x1f,
	0x5e, 0x2b, 0xc3, 0xc7, 0x8d, 0xbd, 0xc3, 0xcb, 0xc7, 0x4d, 0xfe, 0x47, 0x41, 0x3e, 0x6e, 0xea,
	0x31, 0x1f, 0x69, 0x50, 0x4b, 0x3e, 0x7c, 0x23, 0x69, 0xbe, 0x34, 0xf5, 0xde, 0xdf, 0x7c, 0x7c,
	0x13, 0x31, 0xe2, 0xa1, 0x21, 0x2c, 0x4f, 0xbd, 0x75, 0xa3, 0xed, 0x99, 0x9a, 0x4c, 0x3d, 0xb5,
	0x37, 0x9f, 0xde, 0x50, 0x92, 0x78, 0xe8, 0xd7, 0x50, 0x8e, 0x3d, 0x6e, 0xcb, 0x55, 0x94, 0x7c,
	0x5d, 0x97, 0xab, 0x28, 0xfd, 0x42, 0xfe, 0x05, 0xfa, 0x04, 0x95, 0xf8, 0xb3, 0x36, 0x92, 0x76,
	0x4b, 0xbd, 0xa2, 0x37, 0xb7, 0xae, 0x17, 0x22, 0x1e, 0x3a, 0x81, 0xa5, 0xd4, 0x53, 0x29, 0x7a,
	0x2c, 0xef, 0x98, 0x7e, 0x78, 0x6d, 0x3e, 0xb9, 0x91, 0x1c, 0xf1, 0x50, 0x10, 0xbd, 0x2f, 0x26,
	0x5f, 0x34, 0xd1, 0xf3, 0x39, 0x23, 0x4c, 0x3d, 0x95, 0x36, 0xbf, 0xba, 0x85, 0x34, 0xf1, 0xd0,
	0x59, 0xf4, 0x5a, 0x1c, 0x4b, 0xa4, 0xd1, 0xd3, 0x39, 0x83, 0x24, 0x5f, 0x3f, 0x9b, 0xcf, 0x6e,
	0x2a, 0x1a, 0x4d, 0x96, 0x7a, 0x02, 0x9a, 0x39, 0xd9, 0xf4, 0x33, 0xd4, 0xcc, 0xc9, 0x64, 0xaf,
	0x4a, 0x3d, 0x80, 0x49, 0xed, 0x5b, 0x1e, 0x38, 0x12, 0xc5, 0x76, 0x79, 0xe0, 0x48, 0x96, 0xcf,
	0xd1, 0x67, 0x58, 0x91, 0x5d, 0xc9, 0xd1, 0x97, 0xb2, 0xbe, 0x33, 0x6a, 0x06, 0xcd, 0xe7, 0x37,
	0x17, 0x26, 0x1e, 0xfa, 0x1e, 0xaa, 0x89, 0xdb, 0x2e, 0x92, 0xba, 0x6c, 0xfa, 0x92, 0xdd, 0x7c,
	0x74, 0x03, 0x29, 0xe2, 0x21, 0x07, 0xee, 0x48, 0xea, 0xf1, 0x48, 0xaa, 0x68, 0xf9, 0xc3, 0x41,
	0xf3, 0xcb, 0x1b, 0xcb, 0x12, 0x0f, 0x5d, 0xf2, 0x57, 0xc0, 0xe9, 0x4b, 0x1b, 0x9a, 0xe5, 0xb8,
	0xf2, 0x0b, 0x64, 0x73, 0xe7, 0x36, 0xe2, 0xc4, 0x43, 0x18, 0xea, 0xe9, 0x02, 0x05, 0x92, 0x9e,
	0x4d, 0x49, 0x55, 0xa5, 0xb9, 0x7d, 0x33, 0x41, 0xee, 0xe2, 0xd3, 0x65, 0x02, 0xb9, 0x8b, 0x4b,
	0xcb, 0x15, 0x72, 0x17, 0x97, 0x57, 0x1e, 0x28, 0x2c, 0x24, 0x8b, 0x02, 0x72, 0x58, 0x98, 0xaa,
	0x4e, 0xc8, 0x61, 0x41, 0x52, 0x5f, 0x18, 0xc2, 0xf2, 0xd4, 0xfd, 0x5e, 0x0e, 0x0b, 0xb2, 0x9a,
	0x82, 0x1c, 0x16, 0xa4, 0x05, 0x03, 0xf4, 0xe7, 0x19, 0xf6, 0x98, 0x31, 0xe3, 0x46, 0x8f, 0x7e,
	0x36, 0xc3, 0xda, 0xb3, 0x2b, 0x0c, 0xcd, 0x97, 0xb7, 0xed, 0xc2, 0xe3, 0x7c, 0xea, 0xe2, 0x2e,
	0x8f, 0xf3, 0xd3, 0x55, 0x01, 0x79, 0x9c, 0x97, 0x54, 0x01, 0xd0, 0xdf, 0x66, 0xe0, 0x27, 0xf3,
	0xae, 0xe2, 0xe8, 0x0f, 0x67, 0x2c, 0xfc, 0xba, 0x4b, 0x7e, 0xf3, 0x97, 0xb7, 0xef, 0x28, 0x6e,
	0x0b, 0x7f, 0x9f, 0x61, 0xf5, 0x94, 0x39, 0x17, 0x0b, 0xf4, 0xcd, 0x0d, 0x07, 0x9f, 0xbe, 0xc8,
	0x34, 0xbf, 0xfd, 0x7d, 0xba, 0xf2, 0x95, 0x35, 0x37, 0x7e, 0xf7, 0xaf, 0xff, 0xd1, 0x5f, 0x01,
	0x34, 0xfd, 0x97, 0xe2, 0xdd, 0xaf, 0x7f, 0xf3, 0x8b, 0x81, 0x6b, 0xeb, 0xce, 0x60, 0xe7, 0xd5,
	0xcb, 0x20, 0xd8, 0x31, 0xdc, 0xf3, 0x17, 0xec, 0x1f, 0xc2, 0x86, 0x6b, 0xbf, 0x20, 0xd8, 0xbf,
	0xb0, 0x0c, 0x4c, 0x24, 0x7f, 0x45, 0x3e, 0x29, 0x30, 0xa9, 0x9f, 0xff, 0x7f, 0x00, 0x00, 0x00,
	0xff, 0xff, 0x16, 0x11, 0xe7, 0x05, 0xc9, 0x2c, 0x00, 0x00,
}
