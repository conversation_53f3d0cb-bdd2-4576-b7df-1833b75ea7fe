// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/imstranger-go/imstranger-go.proto

package imstrangergo // import "golang.52tt.com/protocol/services/imstrangergo"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type BatchGetStrangerRecvMsgLimitReq struct {
	TargetUids           []uint32 `protobuf:"varint,1,rep,packed,name=target_uids,json=targetUids,proto3" json:"target_uids,omitempty"`
	Scene                string   `protobuf:"bytes,2,opt,name=scene,proto3" json:"scene,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetStrangerRecvMsgLimitReq) Reset()         { *m = BatchGetStrangerRecvMsgLimitReq{} }
func (m *BatchGetStrangerRecvMsgLimitReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetStrangerRecvMsgLimitReq) ProtoMessage()    {}
func (*BatchGetStrangerRecvMsgLimitReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_imstranger_go_803788eaa33a98ff, []int{0}
}
func (m *BatchGetStrangerRecvMsgLimitReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetStrangerRecvMsgLimitReq.Unmarshal(m, b)
}
func (m *BatchGetStrangerRecvMsgLimitReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetStrangerRecvMsgLimitReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetStrangerRecvMsgLimitReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetStrangerRecvMsgLimitReq.Merge(dst, src)
}
func (m *BatchGetStrangerRecvMsgLimitReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetStrangerRecvMsgLimitReq.Size(m)
}
func (m *BatchGetStrangerRecvMsgLimitReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetStrangerRecvMsgLimitReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetStrangerRecvMsgLimitReq proto.InternalMessageInfo

func (m *BatchGetStrangerRecvMsgLimitReq) GetTargetUids() []uint32 {
	if m != nil {
		return m.TargetUids
	}
	return nil
}

func (m *BatchGetStrangerRecvMsgLimitReq) GetScene() string {
	if m != nil {
		return m.Scene
	}
	return ""
}

type BatchGetStrangerRecvMsgLimitResp struct {
	Info                 []*BatchGetStrangerRecvMsgLimitRespLimitInfo `protobuf:"bytes,1,rep,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                     `json:"-"`
	XXX_unrecognized     []byte                                       `json:"-"`
	XXX_sizecache        int32                                        `json:"-"`
}

func (m *BatchGetStrangerRecvMsgLimitResp) Reset()         { *m = BatchGetStrangerRecvMsgLimitResp{} }
func (m *BatchGetStrangerRecvMsgLimitResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetStrangerRecvMsgLimitResp) ProtoMessage()    {}
func (*BatchGetStrangerRecvMsgLimitResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_imstranger_go_803788eaa33a98ff, []int{1}
}
func (m *BatchGetStrangerRecvMsgLimitResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetStrangerRecvMsgLimitResp.Unmarshal(m, b)
}
func (m *BatchGetStrangerRecvMsgLimitResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetStrangerRecvMsgLimitResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetStrangerRecvMsgLimitResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetStrangerRecvMsgLimitResp.Merge(dst, src)
}
func (m *BatchGetStrangerRecvMsgLimitResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetStrangerRecvMsgLimitResp.Size(m)
}
func (m *BatchGetStrangerRecvMsgLimitResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetStrangerRecvMsgLimitResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetStrangerRecvMsgLimitResp proto.InternalMessageInfo

func (m *BatchGetStrangerRecvMsgLimitResp) GetInfo() []*BatchGetStrangerRecvMsgLimitRespLimitInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

// buf:lint:ignore MESSAGE_PASCAL_CASE
type BatchGetStrangerRecvMsgLimitRespLimitInfo struct {
	TargetUid            uint32   `protobuf:"varint,1,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	Cnt                  uint32   `protobuf:"varint,2,opt,name=cnt,proto3" json:"cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetStrangerRecvMsgLimitRespLimitInfo) Reset() {
	*m = BatchGetStrangerRecvMsgLimitRespLimitInfo{}
}
func (m *BatchGetStrangerRecvMsgLimitRespLimitInfo) String() string { return proto.CompactTextString(m) }
func (*BatchGetStrangerRecvMsgLimitRespLimitInfo) ProtoMessage()    {}
func (*BatchGetStrangerRecvMsgLimitRespLimitInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_imstranger_go_803788eaa33a98ff, []int{1, 0}
}
func (m *BatchGetStrangerRecvMsgLimitRespLimitInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetStrangerRecvMsgLimitRespLimitInfo.Unmarshal(m, b)
}
func (m *BatchGetStrangerRecvMsgLimitRespLimitInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetStrangerRecvMsgLimitRespLimitInfo.Marshal(b, m, deterministic)
}
func (dst *BatchGetStrangerRecvMsgLimitRespLimitInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetStrangerRecvMsgLimitRespLimitInfo.Merge(dst, src)
}
func (m *BatchGetStrangerRecvMsgLimitRespLimitInfo) XXX_Size() int {
	return xxx_messageInfo_BatchGetStrangerRecvMsgLimitRespLimitInfo.Size(m)
}
func (m *BatchGetStrangerRecvMsgLimitRespLimitInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetStrangerRecvMsgLimitRespLimitInfo.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetStrangerRecvMsgLimitRespLimitInfo proto.InternalMessageInfo

func (m *BatchGetStrangerRecvMsgLimitRespLimitInfo) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *BatchGetStrangerRecvMsgLimitRespLimitInfo) GetCnt() uint32 {
	if m != nil {
		return m.Cnt
	}
	return 0
}

type IsStrangerMsgSenderReq struct {
	Sender               uint32   `protobuf:"varint,1,opt,name=sender,proto3" json:"sender,omitempty"`
	Target               uint32   `protobuf:"varint,2,opt,name=target,proto3" json:"target,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IsStrangerMsgSenderReq) Reset()         { *m = IsStrangerMsgSenderReq{} }
func (m *IsStrangerMsgSenderReq) String() string { return proto.CompactTextString(m) }
func (*IsStrangerMsgSenderReq) ProtoMessage()    {}
func (*IsStrangerMsgSenderReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_imstranger_go_803788eaa33a98ff, []int{2}
}
func (m *IsStrangerMsgSenderReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IsStrangerMsgSenderReq.Unmarshal(m, b)
}
func (m *IsStrangerMsgSenderReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IsStrangerMsgSenderReq.Marshal(b, m, deterministic)
}
func (dst *IsStrangerMsgSenderReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IsStrangerMsgSenderReq.Merge(dst, src)
}
func (m *IsStrangerMsgSenderReq) XXX_Size() int {
	return xxx_messageInfo_IsStrangerMsgSenderReq.Size(m)
}
func (m *IsStrangerMsgSenderReq) XXX_DiscardUnknown() {
	xxx_messageInfo_IsStrangerMsgSenderReq.DiscardUnknown(m)
}

var xxx_messageInfo_IsStrangerMsgSenderReq proto.InternalMessageInfo

func (m *IsStrangerMsgSenderReq) GetSender() uint32 {
	if m != nil {
		return m.Sender
	}
	return 0
}

func (m *IsStrangerMsgSenderReq) GetTarget() uint32 {
	if m != nil {
		return m.Target
	}
	return 0
}

type IsStrangerMsgSenderResp struct {
	Result               bool     `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IsStrangerMsgSenderResp) Reset()         { *m = IsStrangerMsgSenderResp{} }
func (m *IsStrangerMsgSenderResp) String() string { return proto.CompactTextString(m) }
func (*IsStrangerMsgSenderResp) ProtoMessage()    {}
func (*IsStrangerMsgSenderResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_imstranger_go_803788eaa33a98ff, []int{3}
}
func (m *IsStrangerMsgSenderResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IsStrangerMsgSenderResp.Unmarshal(m, b)
}
func (m *IsStrangerMsgSenderResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IsStrangerMsgSenderResp.Marshal(b, m, deterministic)
}
func (dst *IsStrangerMsgSenderResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IsStrangerMsgSenderResp.Merge(dst, src)
}
func (m *IsStrangerMsgSenderResp) XXX_Size() int {
	return xxx_messageInfo_IsStrangerMsgSenderResp.Size(m)
}
func (m *IsStrangerMsgSenderResp) XXX_DiscardUnknown() {
	xxx_messageInfo_IsStrangerMsgSenderResp.DiscardUnknown(m)
}

var xxx_messageInfo_IsStrangerMsgSenderResp proto.InternalMessageInfo

func (m *IsStrangerMsgSenderResp) GetResult() bool {
	if m != nil {
		return m.Result
	}
	return false
}

type CheckStrangerMsgTargeterReq struct {
	Sender               uint32   `protobuf:"varint,1,opt,name=sender,proto3" json:"sender,omitempty"`
	Target               uint32   `protobuf:"varint,2,opt,name=target,proto3" json:"target,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckStrangerMsgTargeterReq) Reset()         { *m = CheckStrangerMsgTargeterReq{} }
func (m *CheckStrangerMsgTargeterReq) String() string { return proto.CompactTextString(m) }
func (*CheckStrangerMsgTargeterReq) ProtoMessage()    {}
func (*CheckStrangerMsgTargeterReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_imstranger_go_803788eaa33a98ff, []int{4}
}
func (m *CheckStrangerMsgTargeterReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckStrangerMsgTargeterReq.Unmarshal(m, b)
}
func (m *CheckStrangerMsgTargeterReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckStrangerMsgTargeterReq.Marshal(b, m, deterministic)
}
func (dst *CheckStrangerMsgTargeterReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckStrangerMsgTargeterReq.Merge(dst, src)
}
func (m *CheckStrangerMsgTargeterReq) XXX_Size() int {
	return xxx_messageInfo_CheckStrangerMsgTargeterReq.Size(m)
}
func (m *CheckStrangerMsgTargeterReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckStrangerMsgTargeterReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckStrangerMsgTargeterReq proto.InternalMessageInfo

func (m *CheckStrangerMsgTargeterReq) GetSender() uint32 {
	if m != nil {
		return m.Sender
	}
	return 0
}

func (m *CheckStrangerMsgTargeterReq) GetTarget() uint32 {
	if m != nil {
		return m.Target
	}
	return 0
}

type CheckStrangerMsgTargeterResp struct {
	Result               bool     `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckStrangerMsgTargeterResp) Reset()         { *m = CheckStrangerMsgTargeterResp{} }
func (m *CheckStrangerMsgTargeterResp) String() string { return proto.CompactTextString(m) }
func (*CheckStrangerMsgTargeterResp) ProtoMessage()    {}
func (*CheckStrangerMsgTargeterResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_imstranger_go_803788eaa33a98ff, []int{5}
}
func (m *CheckStrangerMsgTargeterResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckStrangerMsgTargeterResp.Unmarshal(m, b)
}
func (m *CheckStrangerMsgTargeterResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckStrangerMsgTargeterResp.Marshal(b, m, deterministic)
}
func (dst *CheckStrangerMsgTargeterResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckStrangerMsgTargeterResp.Merge(dst, src)
}
func (m *CheckStrangerMsgTargeterResp) XXX_Size() int {
	return xxx_messageInfo_CheckStrangerMsgTargeterResp.Size(m)
}
func (m *CheckStrangerMsgTargeterResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckStrangerMsgTargeterResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckStrangerMsgTargeterResp proto.InternalMessageInfo

func (m *CheckStrangerMsgTargeterResp) GetResult() bool {
	if m != nil {
		return m.Result
	}
	return false
}

type CheckStrangerMsgLimitReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TargetUid            uint32   `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	Ip                   string   `protobuf:"bytes,3,opt,name=ip,proto3" json:"ip,omitempty"`
	CheckManCntKey       string   `protobuf:"bytes,4,opt,name=check_man_cnt_key,json=checkManCntKey,proto3" json:"check_man_cnt_key,omitempty"`
	MsgSourceType        uint32   `protobuf:"varint,5,opt,name=msg_source_type,json=msgSourceType,proto3" json:"msg_source_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckStrangerMsgLimitReq) Reset()         { *m = CheckStrangerMsgLimitReq{} }
func (m *CheckStrangerMsgLimitReq) String() string { return proto.CompactTextString(m) }
func (*CheckStrangerMsgLimitReq) ProtoMessage()    {}
func (*CheckStrangerMsgLimitReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_imstranger_go_803788eaa33a98ff, []int{6}
}
func (m *CheckStrangerMsgLimitReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckStrangerMsgLimitReq.Unmarshal(m, b)
}
func (m *CheckStrangerMsgLimitReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckStrangerMsgLimitReq.Marshal(b, m, deterministic)
}
func (dst *CheckStrangerMsgLimitReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckStrangerMsgLimitReq.Merge(dst, src)
}
func (m *CheckStrangerMsgLimitReq) XXX_Size() int {
	return xxx_messageInfo_CheckStrangerMsgLimitReq.Size(m)
}
func (m *CheckStrangerMsgLimitReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckStrangerMsgLimitReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckStrangerMsgLimitReq proto.InternalMessageInfo

func (m *CheckStrangerMsgLimitReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CheckStrangerMsgLimitReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *CheckStrangerMsgLimitReq) GetIp() string {
	if m != nil {
		return m.Ip
	}
	return ""
}

func (m *CheckStrangerMsgLimitReq) GetCheckManCntKey() string {
	if m != nil {
		return m.CheckManCntKey
	}
	return ""
}

func (m *CheckStrangerMsgLimitReq) GetMsgSourceType() uint32 {
	if m != nil {
		return m.MsgSourceType
	}
	return 0
}

type CheckStrangerMsgLimitResp struct {
	Result                  int32    `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	IsNewStrangerTargetUser bool     `protobuf:"varint,2,opt,name=is_new_stranger_target_user,json=isNewStrangerTargetUser,proto3" json:"is_new_stranger_target_user,omitempty"`
	IsNewStrangerIp         bool     `protobuf:"varint,3,opt,name=is_new_stranger_ip,json=isNewStrangerIp,proto3" json:"is_new_stranger_ip,omitempty"`
	ResultMsg               string   `protobuf:"bytes,4,opt,name=result_msg,json=resultMsg,proto3" json:"result_msg,omitempty"`
	XXX_NoUnkeyedLiteral    struct{} `json:"-"`
	XXX_unrecognized        []byte   `json:"-"`
	XXX_sizecache           int32    `json:"-"`
}

func (m *CheckStrangerMsgLimitResp) Reset()         { *m = CheckStrangerMsgLimitResp{} }
func (m *CheckStrangerMsgLimitResp) String() string { return proto.CompactTextString(m) }
func (*CheckStrangerMsgLimitResp) ProtoMessage()    {}
func (*CheckStrangerMsgLimitResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_imstranger_go_803788eaa33a98ff, []int{7}
}
func (m *CheckStrangerMsgLimitResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckStrangerMsgLimitResp.Unmarshal(m, b)
}
func (m *CheckStrangerMsgLimitResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckStrangerMsgLimitResp.Marshal(b, m, deterministic)
}
func (dst *CheckStrangerMsgLimitResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckStrangerMsgLimitResp.Merge(dst, src)
}
func (m *CheckStrangerMsgLimitResp) XXX_Size() int {
	return xxx_messageInfo_CheckStrangerMsgLimitResp.Size(m)
}
func (m *CheckStrangerMsgLimitResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckStrangerMsgLimitResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckStrangerMsgLimitResp proto.InternalMessageInfo

func (m *CheckStrangerMsgLimitResp) GetResult() int32 {
	if m != nil {
		return m.Result
	}
	return 0
}

func (m *CheckStrangerMsgLimitResp) GetIsNewStrangerTargetUser() bool {
	if m != nil {
		return m.IsNewStrangerTargetUser
	}
	return false
}

func (m *CheckStrangerMsgLimitResp) GetIsNewStrangerIp() bool {
	if m != nil {
		return m.IsNewStrangerIp
	}
	return false
}

func (m *CheckStrangerMsgLimitResp) GetResultMsg() string {
	if m != nil {
		return m.ResultMsg
	}
	return ""
}

type IncrStrangerMsgLimitReq struct {
	Uid                     uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TargetUid               uint32   `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	Ip                      string   `protobuf:"bytes,3,opt,name=ip,proto3" json:"ip,omitempty"`
	CheckManCntKey          string   `protobuf:"bytes,4,opt,name=check_man_cnt_key,json=checkManCntKey,proto3" json:"check_man_cnt_key,omitempty"`
	IsNewStrangerTargetUser bool     `protobuf:"varint,5,opt,name=is_new_stranger_target_user,json=isNewStrangerTargetUser,proto3" json:"is_new_stranger_target_user,omitempty"`
	IsNewStrangerIp         bool     `protobuf:"varint,6,opt,name=is_new_stranger_ip,json=isNewStrangerIp,proto3" json:"is_new_stranger_ip,omitempty"`
	MsgSourceType           uint32   `protobuf:"varint,7,opt,name=msg_source_type,json=msgSourceType,proto3" json:"msg_source_type,omitempty"`
	IsOnlyReply             bool     `protobuf:"varint,8,opt,name=is_only_reply,json=isOnlyReply,proto3" json:"is_only_reply,omitempty"`
	XXX_NoUnkeyedLiteral    struct{} `json:"-"`
	XXX_unrecognized        []byte   `json:"-"`
	XXX_sizecache           int32    `json:"-"`
}

func (m *IncrStrangerMsgLimitReq) Reset()         { *m = IncrStrangerMsgLimitReq{} }
func (m *IncrStrangerMsgLimitReq) String() string { return proto.CompactTextString(m) }
func (*IncrStrangerMsgLimitReq) ProtoMessage()    {}
func (*IncrStrangerMsgLimitReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_imstranger_go_803788eaa33a98ff, []int{8}
}
func (m *IncrStrangerMsgLimitReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IncrStrangerMsgLimitReq.Unmarshal(m, b)
}
func (m *IncrStrangerMsgLimitReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IncrStrangerMsgLimitReq.Marshal(b, m, deterministic)
}
func (dst *IncrStrangerMsgLimitReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IncrStrangerMsgLimitReq.Merge(dst, src)
}
func (m *IncrStrangerMsgLimitReq) XXX_Size() int {
	return xxx_messageInfo_IncrStrangerMsgLimitReq.Size(m)
}
func (m *IncrStrangerMsgLimitReq) XXX_DiscardUnknown() {
	xxx_messageInfo_IncrStrangerMsgLimitReq.DiscardUnknown(m)
}

var xxx_messageInfo_IncrStrangerMsgLimitReq proto.InternalMessageInfo

func (m *IncrStrangerMsgLimitReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *IncrStrangerMsgLimitReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *IncrStrangerMsgLimitReq) GetIp() string {
	if m != nil {
		return m.Ip
	}
	return ""
}

func (m *IncrStrangerMsgLimitReq) GetCheckManCntKey() string {
	if m != nil {
		return m.CheckManCntKey
	}
	return ""
}

func (m *IncrStrangerMsgLimitReq) GetIsNewStrangerTargetUser() bool {
	if m != nil {
		return m.IsNewStrangerTargetUser
	}
	return false
}

func (m *IncrStrangerMsgLimitReq) GetIsNewStrangerIp() bool {
	if m != nil {
		return m.IsNewStrangerIp
	}
	return false
}

func (m *IncrStrangerMsgLimitReq) GetMsgSourceType() uint32 {
	if m != nil {
		return m.MsgSourceType
	}
	return 0
}

func (m *IncrStrangerMsgLimitReq) GetIsOnlyReply() bool {
	if m != nil {
		return m.IsOnlyReply
	}
	return false
}

type SetHighConsumeIn24HourReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetHighConsumeIn24HourReq) Reset()         { *m = SetHighConsumeIn24HourReq{} }
func (m *SetHighConsumeIn24HourReq) String() string { return proto.CompactTextString(m) }
func (*SetHighConsumeIn24HourReq) ProtoMessage()    {}
func (*SetHighConsumeIn24HourReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_imstranger_go_803788eaa33a98ff, []int{9}
}
func (m *SetHighConsumeIn24HourReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetHighConsumeIn24HourReq.Unmarshal(m, b)
}
func (m *SetHighConsumeIn24HourReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetHighConsumeIn24HourReq.Marshal(b, m, deterministic)
}
func (dst *SetHighConsumeIn24HourReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetHighConsumeIn24HourReq.Merge(dst, src)
}
func (m *SetHighConsumeIn24HourReq) XXX_Size() int {
	return xxx_messageInfo_SetHighConsumeIn24HourReq.Size(m)
}
func (m *SetHighConsumeIn24HourReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetHighConsumeIn24HourReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetHighConsumeIn24HourReq proto.InternalMessageInfo

func (m *SetHighConsumeIn24HourReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type SetHighConsumeIn24HourResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetHighConsumeIn24HourResp) Reset()         { *m = SetHighConsumeIn24HourResp{} }
func (m *SetHighConsumeIn24HourResp) String() string { return proto.CompactTextString(m) }
func (*SetHighConsumeIn24HourResp) ProtoMessage()    {}
func (*SetHighConsumeIn24HourResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_imstranger_go_803788eaa33a98ff, []int{10}
}
func (m *SetHighConsumeIn24HourResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetHighConsumeIn24HourResp.Unmarshal(m, b)
}
func (m *SetHighConsumeIn24HourResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetHighConsumeIn24HourResp.Marshal(b, m, deterministic)
}
func (dst *SetHighConsumeIn24HourResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetHighConsumeIn24HourResp.Merge(dst, src)
}
func (m *SetHighConsumeIn24HourResp) XXX_Size() int {
	return xxx_messageInfo_SetHighConsumeIn24HourResp.Size(m)
}
func (m *SetHighConsumeIn24HourResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetHighConsumeIn24HourResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetHighConsumeIn24HourResp proto.InternalMessageInfo

type IsHighConsumeIn24HourReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IsHighConsumeIn24HourReq) Reset()         { *m = IsHighConsumeIn24HourReq{} }
func (m *IsHighConsumeIn24HourReq) String() string { return proto.CompactTextString(m) }
func (*IsHighConsumeIn24HourReq) ProtoMessage()    {}
func (*IsHighConsumeIn24HourReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_imstranger_go_803788eaa33a98ff, []int{11}
}
func (m *IsHighConsumeIn24HourReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IsHighConsumeIn24HourReq.Unmarshal(m, b)
}
func (m *IsHighConsumeIn24HourReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IsHighConsumeIn24HourReq.Marshal(b, m, deterministic)
}
func (dst *IsHighConsumeIn24HourReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IsHighConsumeIn24HourReq.Merge(dst, src)
}
func (m *IsHighConsumeIn24HourReq) XXX_Size() int {
	return xxx_messageInfo_IsHighConsumeIn24HourReq.Size(m)
}
func (m *IsHighConsumeIn24HourReq) XXX_DiscardUnknown() {
	xxx_messageInfo_IsHighConsumeIn24HourReq.DiscardUnknown(m)
}

var xxx_messageInfo_IsHighConsumeIn24HourReq proto.InternalMessageInfo

func (m *IsHighConsumeIn24HourReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type IsHighConsumeIn24HourResp struct {
	IsHighConsume        bool     `protobuf:"varint,1,opt,name=isHighConsume,proto3" json:"isHighConsume,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IsHighConsumeIn24HourResp) Reset()         { *m = IsHighConsumeIn24HourResp{} }
func (m *IsHighConsumeIn24HourResp) String() string { return proto.CompactTextString(m) }
func (*IsHighConsumeIn24HourResp) ProtoMessage()    {}
func (*IsHighConsumeIn24HourResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_imstranger_go_803788eaa33a98ff, []int{12}
}
func (m *IsHighConsumeIn24HourResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IsHighConsumeIn24HourResp.Unmarshal(m, b)
}
func (m *IsHighConsumeIn24HourResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IsHighConsumeIn24HourResp.Marshal(b, m, deterministic)
}
func (dst *IsHighConsumeIn24HourResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IsHighConsumeIn24HourResp.Merge(dst, src)
}
func (m *IsHighConsumeIn24HourResp) XXX_Size() int {
	return xxx_messageInfo_IsHighConsumeIn24HourResp.Size(m)
}
func (m *IsHighConsumeIn24HourResp) XXX_DiscardUnknown() {
	xxx_messageInfo_IsHighConsumeIn24HourResp.DiscardUnknown(m)
}

var xxx_messageInfo_IsHighConsumeIn24HourResp proto.InternalMessageInfo

func (m *IsHighConsumeIn24HourResp) GetIsHighConsume() bool {
	if m != nil {
		return m.IsHighConsume
	}
	return false
}

type IncrStrangerMsgLimitResp struct {
	Result               uint32   `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IncrStrangerMsgLimitResp) Reset()         { *m = IncrStrangerMsgLimitResp{} }
func (m *IncrStrangerMsgLimitResp) String() string { return proto.CompactTextString(m) }
func (*IncrStrangerMsgLimitResp) ProtoMessage()    {}
func (*IncrStrangerMsgLimitResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_imstranger_go_803788eaa33a98ff, []int{13}
}
func (m *IncrStrangerMsgLimitResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IncrStrangerMsgLimitResp.Unmarshal(m, b)
}
func (m *IncrStrangerMsgLimitResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IncrStrangerMsgLimitResp.Marshal(b, m, deterministic)
}
func (dst *IncrStrangerMsgLimitResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IncrStrangerMsgLimitResp.Merge(dst, src)
}
func (m *IncrStrangerMsgLimitResp) XXX_Size() int {
	return xxx_messageInfo_IncrStrangerMsgLimitResp.Size(m)
}
func (m *IncrStrangerMsgLimitResp) XXX_DiscardUnknown() {
	xxx_messageInfo_IncrStrangerMsgLimitResp.DiscardUnknown(m)
}

var xxx_messageInfo_IncrStrangerMsgLimitResp proto.InternalMessageInfo

func (m *IncrStrangerMsgLimitResp) GetResult() uint32 {
	if m != nil {
		return m.Result
	}
	return 0
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type ChatAwardReq struct {
	Uin                  uint32   `protobuf:"varint,1,opt,name=uin,proto3" json:"uin,omitempty"`
	TargetUid            uint32   `protobuf:"varint,2,opt,name=targetUid,proto3" json:"targetUid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChatAwardReq) Reset()         { *m = ChatAwardReq{} }
func (m *ChatAwardReq) String() string { return proto.CompactTextString(m) }
func (*ChatAwardReq) ProtoMessage()    {}
func (*ChatAwardReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_imstranger_go_803788eaa33a98ff, []int{14}
}
func (m *ChatAwardReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChatAwardReq.Unmarshal(m, b)
}
func (m *ChatAwardReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChatAwardReq.Marshal(b, m, deterministic)
}
func (dst *ChatAwardReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChatAwardReq.Merge(dst, src)
}
func (m *ChatAwardReq) XXX_Size() int {
	return xxx_messageInfo_ChatAwardReq.Size(m)
}
func (m *ChatAwardReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChatAwardReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChatAwardReq proto.InternalMessageInfo

func (m *ChatAwardReq) GetUin() uint32 {
	if m != nil {
		return m.Uin
	}
	return 0
}

func (m *ChatAwardReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type ChatAwardResp struct {
	AwardUin             bool     `protobuf:"varint,1,opt,name=awardUin,proto3" json:"awardUin,omitempty"`
	AwardTarget          bool     `protobuf:"varint,2,opt,name=awardTarget,proto3" json:"awardTarget,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChatAwardResp) Reset()         { *m = ChatAwardResp{} }
func (m *ChatAwardResp) String() string { return proto.CompactTextString(m) }
func (*ChatAwardResp) ProtoMessage()    {}
func (*ChatAwardResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_imstranger_go_803788eaa33a98ff, []int{15}
}
func (m *ChatAwardResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChatAwardResp.Unmarshal(m, b)
}
func (m *ChatAwardResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChatAwardResp.Marshal(b, m, deterministic)
}
func (dst *ChatAwardResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChatAwardResp.Merge(dst, src)
}
func (m *ChatAwardResp) XXX_Size() int {
	return xxx_messageInfo_ChatAwardResp.Size(m)
}
func (m *ChatAwardResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ChatAwardResp.DiscardUnknown(m)
}

var xxx_messageInfo_ChatAwardResp proto.InternalMessageInfo

func (m *ChatAwardResp) GetAwardUin() bool {
	if m != nil {
		return m.AwardUin
	}
	return false
}

func (m *ChatAwardResp) GetAwardTarget() bool {
	if m != nil {
		return m.AwardTarget
	}
	return false
}

// 用户向陌生人聊天 targetuid的集合查询（当天）
type GetStrangerGreetDetailReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetStrangerGreetDetailReq) Reset()         { *m = GetStrangerGreetDetailReq{} }
func (m *GetStrangerGreetDetailReq) String() string { return proto.CompactTextString(m) }
func (*GetStrangerGreetDetailReq) ProtoMessage()    {}
func (*GetStrangerGreetDetailReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_imstranger_go_803788eaa33a98ff, []int{16}
}
func (m *GetStrangerGreetDetailReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStrangerGreetDetailReq.Unmarshal(m, b)
}
func (m *GetStrangerGreetDetailReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStrangerGreetDetailReq.Marshal(b, m, deterministic)
}
func (dst *GetStrangerGreetDetailReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStrangerGreetDetailReq.Merge(dst, src)
}
func (m *GetStrangerGreetDetailReq) XXX_Size() int {
	return xxx_messageInfo_GetStrangerGreetDetailReq.Size(m)
}
func (m *GetStrangerGreetDetailReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStrangerGreetDetailReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetStrangerGreetDetailReq proto.InternalMessageInfo

func (m *GetStrangerGreetDetailReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetStrangerGreetDetailResp struct {
	TargetUids           []uint32 `protobuf:"varint,1,rep,packed,name=target_uids,json=targetUids,proto3" json:"target_uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetStrangerGreetDetailResp) Reset()         { *m = GetStrangerGreetDetailResp{} }
func (m *GetStrangerGreetDetailResp) String() string { return proto.CompactTextString(m) }
func (*GetStrangerGreetDetailResp) ProtoMessage()    {}
func (*GetStrangerGreetDetailResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_imstranger_go_803788eaa33a98ff, []int{17}
}
func (m *GetStrangerGreetDetailResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStrangerGreetDetailResp.Unmarshal(m, b)
}
func (m *GetStrangerGreetDetailResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStrangerGreetDetailResp.Marshal(b, m, deterministic)
}
func (dst *GetStrangerGreetDetailResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStrangerGreetDetailResp.Merge(dst, src)
}
func (m *GetStrangerGreetDetailResp) XXX_Size() int {
	return xxx_messageInfo_GetStrangerGreetDetailResp.Size(m)
}
func (m *GetStrangerGreetDetailResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStrangerGreetDetailResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetStrangerGreetDetailResp proto.InternalMessageInfo

func (m *GetStrangerGreetDetailResp) GetTargetUids() []uint32 {
	if m != nil {
		return m.TargetUids
	}
	return nil
}

// 检查用户有没有到被撩上限 批量
type BatchCheckUserRecvLimitReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	UidList              []uint32 `protobuf:"varint,2,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchCheckUserRecvLimitReq) Reset()         { *m = BatchCheckUserRecvLimitReq{} }
func (m *BatchCheckUserRecvLimitReq) String() string { return proto.CompactTextString(m) }
func (*BatchCheckUserRecvLimitReq) ProtoMessage()    {}
func (*BatchCheckUserRecvLimitReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_imstranger_go_803788eaa33a98ff, []int{18}
}
func (m *BatchCheckUserRecvLimitReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchCheckUserRecvLimitReq.Unmarshal(m, b)
}
func (m *BatchCheckUserRecvLimitReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchCheckUserRecvLimitReq.Marshal(b, m, deterministic)
}
func (dst *BatchCheckUserRecvLimitReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchCheckUserRecvLimitReq.Merge(dst, src)
}
func (m *BatchCheckUserRecvLimitReq) XXX_Size() int {
	return xxx_messageInfo_BatchCheckUserRecvLimitReq.Size(m)
}
func (m *BatchCheckUserRecvLimitReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchCheckUserRecvLimitReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchCheckUserRecvLimitReq proto.InternalMessageInfo

func (m *BatchCheckUserRecvLimitReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *BatchCheckUserRecvLimitReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type LimitInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	IsLimit              bool     `protobuf:"varint,2,opt,name=isLimit,proto3" json:"isLimit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LimitInfo) Reset()         { *m = LimitInfo{} }
func (m *LimitInfo) String() string { return proto.CompactTextString(m) }
func (*LimitInfo) ProtoMessage()    {}
func (*LimitInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_imstranger_go_803788eaa33a98ff, []int{19}
}
func (m *LimitInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LimitInfo.Unmarshal(m, b)
}
func (m *LimitInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LimitInfo.Marshal(b, m, deterministic)
}
func (dst *LimitInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LimitInfo.Merge(dst, src)
}
func (m *LimitInfo) XXX_Size() int {
	return xxx_messageInfo_LimitInfo.Size(m)
}
func (m *LimitInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_LimitInfo.DiscardUnknown(m)
}

var xxx_messageInfo_LimitInfo proto.InternalMessageInfo

func (m *LimitInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *LimitInfo) GetIsLimit() bool {
	if m != nil {
		return m.IsLimit
	}
	return false
}

type BatchCheckUserRecvLimitResp struct {
	ResultList           []*LimitInfo `protobuf:"bytes,1,rep,name=result_list,json=resultList,proto3" json:"result_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *BatchCheckUserRecvLimitResp) Reset()         { *m = BatchCheckUserRecvLimitResp{} }
func (m *BatchCheckUserRecvLimitResp) String() string { return proto.CompactTextString(m) }
func (*BatchCheckUserRecvLimitResp) ProtoMessage()    {}
func (*BatchCheckUserRecvLimitResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_imstranger_go_803788eaa33a98ff, []int{20}
}
func (m *BatchCheckUserRecvLimitResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchCheckUserRecvLimitResp.Unmarshal(m, b)
}
func (m *BatchCheckUserRecvLimitResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchCheckUserRecvLimitResp.Marshal(b, m, deterministic)
}
func (dst *BatchCheckUserRecvLimitResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchCheckUserRecvLimitResp.Merge(dst, src)
}
func (m *BatchCheckUserRecvLimitResp) XXX_Size() int {
	return xxx_messageInfo_BatchCheckUserRecvLimitResp.Size(m)
}
func (m *BatchCheckUserRecvLimitResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchCheckUserRecvLimitResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchCheckUserRecvLimitResp proto.InternalMessageInfo

func (m *BatchCheckUserRecvLimitResp) GetResultList() []*LimitInfo {
	if m != nil {
		return m.ResultList
	}
	return nil
}

// 设置当天IM送礼记录
type SetIMPresentToTargetReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TargetUid            uint32   `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetIMPresentToTargetReq) Reset()         { *m = SetIMPresentToTargetReq{} }
func (m *SetIMPresentToTargetReq) String() string { return proto.CompactTextString(m) }
func (*SetIMPresentToTargetReq) ProtoMessage()    {}
func (*SetIMPresentToTargetReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_imstranger_go_803788eaa33a98ff, []int{21}
}
func (m *SetIMPresentToTargetReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetIMPresentToTargetReq.Unmarshal(m, b)
}
func (m *SetIMPresentToTargetReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetIMPresentToTargetReq.Marshal(b, m, deterministic)
}
func (dst *SetIMPresentToTargetReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetIMPresentToTargetReq.Merge(dst, src)
}
func (m *SetIMPresentToTargetReq) XXX_Size() int {
	return xxx_messageInfo_SetIMPresentToTargetReq.Size(m)
}
func (m *SetIMPresentToTargetReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetIMPresentToTargetReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetIMPresentToTargetReq proto.InternalMessageInfo

func (m *SetIMPresentToTargetReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetIMPresentToTargetReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

type SetIMPresentToTargetResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetIMPresentToTargetResp) Reset()         { *m = SetIMPresentToTargetResp{} }
func (m *SetIMPresentToTargetResp) String() string { return proto.CompactTextString(m) }
func (*SetIMPresentToTargetResp) ProtoMessage()    {}
func (*SetIMPresentToTargetResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_imstranger_go_803788eaa33a98ff, []int{22}
}
func (m *SetIMPresentToTargetResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetIMPresentToTargetResp.Unmarshal(m, b)
}
func (m *SetIMPresentToTargetResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetIMPresentToTargetResp.Marshal(b, m, deterministic)
}
func (dst *SetIMPresentToTargetResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetIMPresentToTargetResp.Merge(dst, src)
}
func (m *SetIMPresentToTargetResp) XXX_Size() int {
	return xxx_messageInfo_SetIMPresentToTargetResp.Size(m)
}
func (m *SetIMPresentToTargetResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetIMPresentToTargetResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetIMPresentToTargetResp proto.InternalMessageInfo

type CheckIMPresentToTargetReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TargetUid            uint32   `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckIMPresentToTargetReq) Reset()         { *m = CheckIMPresentToTargetReq{} }
func (m *CheckIMPresentToTargetReq) String() string { return proto.CompactTextString(m) }
func (*CheckIMPresentToTargetReq) ProtoMessage()    {}
func (*CheckIMPresentToTargetReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_imstranger_go_803788eaa33a98ff, []int{23}
}
func (m *CheckIMPresentToTargetReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckIMPresentToTargetReq.Unmarshal(m, b)
}
func (m *CheckIMPresentToTargetReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckIMPresentToTargetReq.Marshal(b, m, deterministic)
}
func (dst *CheckIMPresentToTargetReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckIMPresentToTargetReq.Merge(dst, src)
}
func (m *CheckIMPresentToTargetReq) XXX_Size() int {
	return xxx_messageInfo_CheckIMPresentToTargetReq.Size(m)
}
func (m *CheckIMPresentToTargetReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckIMPresentToTargetReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckIMPresentToTargetReq proto.InternalMessageInfo

func (m *CheckIMPresentToTargetReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CheckIMPresentToTargetReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

type CheckIMPresentToTargetResp struct {
	HasRecord            bool     `protobuf:"varint,1,opt,name=has_record,json=hasRecord,proto3" json:"has_record,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckIMPresentToTargetResp) Reset()         { *m = CheckIMPresentToTargetResp{} }
func (m *CheckIMPresentToTargetResp) String() string { return proto.CompactTextString(m) }
func (*CheckIMPresentToTargetResp) ProtoMessage()    {}
func (*CheckIMPresentToTargetResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_imstranger_go_803788eaa33a98ff, []int{24}
}
func (m *CheckIMPresentToTargetResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckIMPresentToTargetResp.Unmarshal(m, b)
}
func (m *CheckIMPresentToTargetResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckIMPresentToTargetResp.Marshal(b, m, deterministic)
}
func (dst *CheckIMPresentToTargetResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckIMPresentToTargetResp.Merge(dst, src)
}
func (m *CheckIMPresentToTargetResp) XXX_Size() int {
	return xxx_messageInfo_CheckIMPresentToTargetResp.Size(m)
}
func (m *CheckIMPresentToTargetResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckIMPresentToTargetResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckIMPresentToTargetResp proto.InternalMessageInfo

func (m *CheckIMPresentToTargetResp) GetHasRecord() bool {
	if m != nil {
		return m.HasRecord
	}
	return false
}

type CheckSuperPlayerStatusReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckSuperPlayerStatusReq) Reset()         { *m = CheckSuperPlayerStatusReq{} }
func (m *CheckSuperPlayerStatusReq) String() string { return proto.CompactTextString(m) }
func (*CheckSuperPlayerStatusReq) ProtoMessage()    {}
func (*CheckSuperPlayerStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_imstranger_go_803788eaa33a98ff, []int{25}
}
func (m *CheckSuperPlayerStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckSuperPlayerStatusReq.Unmarshal(m, b)
}
func (m *CheckSuperPlayerStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckSuperPlayerStatusReq.Marshal(b, m, deterministic)
}
func (dst *CheckSuperPlayerStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckSuperPlayerStatusReq.Merge(dst, src)
}
func (m *CheckSuperPlayerStatusReq) XXX_Size() int {
	return xxx_messageInfo_CheckSuperPlayerStatusReq.Size(m)
}
func (m *CheckSuperPlayerStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckSuperPlayerStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckSuperPlayerStatusReq proto.InternalMessageInfo

func (m *CheckSuperPlayerStatusReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type CheckSuperPlayerStatusResp struct {
	NotExpired           bool     `protobuf:"varint,1,opt,name=not_expired,json=notExpired,proto3" json:"not_expired,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckSuperPlayerStatusResp) Reset()         { *m = CheckSuperPlayerStatusResp{} }
func (m *CheckSuperPlayerStatusResp) String() string { return proto.CompactTextString(m) }
func (*CheckSuperPlayerStatusResp) ProtoMessage()    {}
func (*CheckSuperPlayerStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_imstranger_go_803788eaa33a98ff, []int{26}
}
func (m *CheckSuperPlayerStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckSuperPlayerStatusResp.Unmarshal(m, b)
}
func (m *CheckSuperPlayerStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckSuperPlayerStatusResp.Marshal(b, m, deterministic)
}
func (dst *CheckSuperPlayerStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckSuperPlayerStatusResp.Merge(dst, src)
}
func (m *CheckSuperPlayerStatusResp) XXX_Size() int {
	return xxx_messageInfo_CheckSuperPlayerStatusResp.Size(m)
}
func (m *CheckSuperPlayerStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckSuperPlayerStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckSuperPlayerStatusResp proto.InternalMessageInfo

func (m *CheckSuperPlayerStatusResp) GetNotExpired() bool {
	if m != nil {
		return m.NotExpired
	}
	return false
}

// 检查贵族特权限制
type CheckNobilityPrivilegeReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TargetUid            uint32   `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckNobilityPrivilegeReq) Reset()         { *m = CheckNobilityPrivilegeReq{} }
func (m *CheckNobilityPrivilegeReq) String() string { return proto.CompactTextString(m) }
func (*CheckNobilityPrivilegeReq) ProtoMessage()    {}
func (*CheckNobilityPrivilegeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_imstranger_go_803788eaa33a98ff, []int{27}
}
func (m *CheckNobilityPrivilegeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckNobilityPrivilegeReq.Unmarshal(m, b)
}
func (m *CheckNobilityPrivilegeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckNobilityPrivilegeReq.Marshal(b, m, deterministic)
}
func (dst *CheckNobilityPrivilegeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckNobilityPrivilegeReq.Merge(dst, src)
}
func (m *CheckNobilityPrivilegeReq) XXX_Size() int {
	return xxx_messageInfo_CheckNobilityPrivilegeReq.Size(m)
}
func (m *CheckNobilityPrivilegeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckNobilityPrivilegeReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckNobilityPrivilegeReq proto.InternalMessageInfo

func (m *CheckNobilityPrivilegeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CheckNobilityPrivilegeReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

type CheckNobilityPrivilegeResp struct {
	IsLimit              bool     `protobuf:"varint,1,opt,name=is_limit,json=isLimit,proto3" json:"is_limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckNobilityPrivilegeResp) Reset()         { *m = CheckNobilityPrivilegeResp{} }
func (m *CheckNobilityPrivilegeResp) String() string { return proto.CompactTextString(m) }
func (*CheckNobilityPrivilegeResp) ProtoMessage()    {}
func (*CheckNobilityPrivilegeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_imstranger_go_803788eaa33a98ff, []int{28}
}
func (m *CheckNobilityPrivilegeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckNobilityPrivilegeResp.Unmarshal(m, b)
}
func (m *CheckNobilityPrivilegeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckNobilityPrivilegeResp.Marshal(b, m, deterministic)
}
func (dst *CheckNobilityPrivilegeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckNobilityPrivilegeResp.Merge(dst, src)
}
func (m *CheckNobilityPrivilegeResp) XXX_Size() int {
	return xxx_messageInfo_CheckNobilityPrivilegeResp.Size(m)
}
func (m *CheckNobilityPrivilegeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckNobilityPrivilegeResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckNobilityPrivilegeResp proto.InternalMessageInfo

func (m *CheckNobilityPrivilegeResp) GetIsLimit() bool {
	if m != nil {
		return m.IsLimit
	}
	return false
}

type BatchCheckNobilityPrivilegeReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TargetUidList        []uint32 `protobuf:"varint,2,rep,packed,name=target_uid_list,json=targetUidList,proto3" json:"target_uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchCheckNobilityPrivilegeReq) Reset()         { *m = BatchCheckNobilityPrivilegeReq{} }
func (m *BatchCheckNobilityPrivilegeReq) String() string { return proto.CompactTextString(m) }
func (*BatchCheckNobilityPrivilegeReq) ProtoMessage()    {}
func (*BatchCheckNobilityPrivilegeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_imstranger_go_803788eaa33a98ff, []int{29}
}
func (m *BatchCheckNobilityPrivilegeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchCheckNobilityPrivilegeReq.Unmarshal(m, b)
}
func (m *BatchCheckNobilityPrivilegeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchCheckNobilityPrivilegeReq.Marshal(b, m, deterministic)
}
func (dst *BatchCheckNobilityPrivilegeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchCheckNobilityPrivilegeReq.Merge(dst, src)
}
func (m *BatchCheckNobilityPrivilegeReq) XXX_Size() int {
	return xxx_messageInfo_BatchCheckNobilityPrivilegeReq.Size(m)
}
func (m *BatchCheckNobilityPrivilegeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchCheckNobilityPrivilegeReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchCheckNobilityPrivilegeReq proto.InternalMessageInfo

func (m *BatchCheckNobilityPrivilegeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *BatchCheckNobilityPrivilegeReq) GetTargetUidList() []uint32 {
	if m != nil {
		return m.TargetUidList
	}
	return nil
}

type NobilityPrivilegeCheckInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TargetUid            uint32   `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	IsLimit              bool     `protobuf:"varint,3,opt,name=is_limit,json=isLimit,proto3" json:"is_limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NobilityPrivilegeCheckInfo) Reset()         { *m = NobilityPrivilegeCheckInfo{} }
func (m *NobilityPrivilegeCheckInfo) String() string { return proto.CompactTextString(m) }
func (*NobilityPrivilegeCheckInfo) ProtoMessage()    {}
func (*NobilityPrivilegeCheckInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_imstranger_go_803788eaa33a98ff, []int{30}
}
func (m *NobilityPrivilegeCheckInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NobilityPrivilegeCheckInfo.Unmarshal(m, b)
}
func (m *NobilityPrivilegeCheckInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NobilityPrivilegeCheckInfo.Marshal(b, m, deterministic)
}
func (dst *NobilityPrivilegeCheckInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NobilityPrivilegeCheckInfo.Merge(dst, src)
}
func (m *NobilityPrivilegeCheckInfo) XXX_Size() int {
	return xxx_messageInfo_NobilityPrivilegeCheckInfo.Size(m)
}
func (m *NobilityPrivilegeCheckInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_NobilityPrivilegeCheckInfo.DiscardUnknown(m)
}

var xxx_messageInfo_NobilityPrivilegeCheckInfo proto.InternalMessageInfo

func (m *NobilityPrivilegeCheckInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *NobilityPrivilegeCheckInfo) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *NobilityPrivilegeCheckInfo) GetIsLimit() bool {
	if m != nil {
		return m.IsLimit
	}
	return false
}

type BatchCheckNobilityPrivilegeResp struct {
	CheckInfoList        []*NobilityPrivilegeCheckInfo `protobuf:"bytes,1,rep,name=check_info_list,json=checkInfoList,proto3" json:"check_info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *BatchCheckNobilityPrivilegeResp) Reset()         { *m = BatchCheckNobilityPrivilegeResp{} }
func (m *BatchCheckNobilityPrivilegeResp) String() string { return proto.CompactTextString(m) }
func (*BatchCheckNobilityPrivilegeResp) ProtoMessage()    {}
func (*BatchCheckNobilityPrivilegeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_imstranger_go_803788eaa33a98ff, []int{31}
}
func (m *BatchCheckNobilityPrivilegeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchCheckNobilityPrivilegeResp.Unmarshal(m, b)
}
func (m *BatchCheckNobilityPrivilegeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchCheckNobilityPrivilegeResp.Marshal(b, m, deterministic)
}
func (dst *BatchCheckNobilityPrivilegeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchCheckNobilityPrivilegeResp.Merge(dst, src)
}
func (m *BatchCheckNobilityPrivilegeResp) XXX_Size() int {
	return xxx_messageInfo_BatchCheckNobilityPrivilegeResp.Size(m)
}
func (m *BatchCheckNobilityPrivilegeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchCheckNobilityPrivilegeResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchCheckNobilityPrivilegeResp proto.InternalMessageInfo

func (m *BatchCheckNobilityPrivilegeResp) GetCheckInfoList() []*NobilityPrivilegeCheckInfo {
	if m != nil {
		return m.CheckInfoList
	}
	return nil
}

type IsContractGuildSuperiorAndSubordinateReq struct {
	Uid                  uint64   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Target               uint64   `protobuf:"varint,2,opt,name=target,proto3" json:"target,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IsContractGuildSuperiorAndSubordinateReq) Reset() {
	*m = IsContractGuildSuperiorAndSubordinateReq{}
}
func (m *IsContractGuildSuperiorAndSubordinateReq) String() string { return proto.CompactTextString(m) }
func (*IsContractGuildSuperiorAndSubordinateReq) ProtoMessage()    {}
func (*IsContractGuildSuperiorAndSubordinateReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_imstranger_go_803788eaa33a98ff, []int{32}
}
func (m *IsContractGuildSuperiorAndSubordinateReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IsContractGuildSuperiorAndSubordinateReq.Unmarshal(m, b)
}
func (m *IsContractGuildSuperiorAndSubordinateReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IsContractGuildSuperiorAndSubordinateReq.Marshal(b, m, deterministic)
}
func (dst *IsContractGuildSuperiorAndSubordinateReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IsContractGuildSuperiorAndSubordinateReq.Merge(dst, src)
}
func (m *IsContractGuildSuperiorAndSubordinateReq) XXX_Size() int {
	return xxx_messageInfo_IsContractGuildSuperiorAndSubordinateReq.Size(m)
}
func (m *IsContractGuildSuperiorAndSubordinateReq) XXX_DiscardUnknown() {
	xxx_messageInfo_IsContractGuildSuperiorAndSubordinateReq.DiscardUnknown(m)
}

var xxx_messageInfo_IsContractGuildSuperiorAndSubordinateReq proto.InternalMessageInfo

func (m *IsContractGuildSuperiorAndSubordinateReq) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *IsContractGuildSuperiorAndSubordinateReq) GetTarget() uint64 {
	if m != nil {
		return m.Target
	}
	return 0
}

type IsContractGuildSuperiorAndSubordinateResp struct {
	Is                   bool     `protobuf:"varint,1,opt,name=is,proto3" json:"is,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IsContractGuildSuperiorAndSubordinateResp) Reset() {
	*m = IsContractGuildSuperiorAndSubordinateResp{}
}
func (m *IsContractGuildSuperiorAndSubordinateResp) String() string { return proto.CompactTextString(m) }
func (*IsContractGuildSuperiorAndSubordinateResp) ProtoMessage()    {}
func (*IsContractGuildSuperiorAndSubordinateResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_imstranger_go_803788eaa33a98ff, []int{33}
}
func (m *IsContractGuildSuperiorAndSubordinateResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IsContractGuildSuperiorAndSubordinateResp.Unmarshal(m, b)
}
func (m *IsContractGuildSuperiorAndSubordinateResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IsContractGuildSuperiorAndSubordinateResp.Marshal(b, m, deterministic)
}
func (dst *IsContractGuildSuperiorAndSubordinateResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IsContractGuildSuperiorAndSubordinateResp.Merge(dst, src)
}
func (m *IsContractGuildSuperiorAndSubordinateResp) XXX_Size() int {
	return xxx_messageInfo_IsContractGuildSuperiorAndSubordinateResp.Size(m)
}
func (m *IsContractGuildSuperiorAndSubordinateResp) XXX_DiscardUnknown() {
	xxx_messageInfo_IsContractGuildSuperiorAndSubordinateResp.DiscardUnknown(m)
}

var xxx_messageInfo_IsContractGuildSuperiorAndSubordinateResp proto.InternalMessageInfo

func (m *IsContractGuildSuperiorAndSubordinateResp) GetIs() bool {
	if m != nil {
		return m.Is
	}
	return false
}

func init() {
	proto.RegisterType((*BatchGetStrangerRecvMsgLimitReq)(nil), "imstrangergo.BatchGetStrangerRecvMsgLimitReq")
	proto.RegisterType((*BatchGetStrangerRecvMsgLimitResp)(nil), "imstrangergo.BatchGetStrangerRecvMsgLimitResp")
	proto.RegisterType((*BatchGetStrangerRecvMsgLimitRespLimitInfo)(nil), "imstrangergo.BatchGetStrangerRecvMsgLimitResp.limitInfo")
	proto.RegisterType((*IsStrangerMsgSenderReq)(nil), "imstrangergo.IsStrangerMsgSenderReq")
	proto.RegisterType((*IsStrangerMsgSenderResp)(nil), "imstrangergo.IsStrangerMsgSenderResp")
	proto.RegisterType((*CheckStrangerMsgTargeterReq)(nil), "imstrangergo.CheckStrangerMsgTargeterReq")
	proto.RegisterType((*CheckStrangerMsgTargeterResp)(nil), "imstrangergo.CheckStrangerMsgTargeterResp")
	proto.RegisterType((*CheckStrangerMsgLimitReq)(nil), "imstrangergo.CheckStrangerMsgLimitReq")
	proto.RegisterType((*CheckStrangerMsgLimitResp)(nil), "imstrangergo.CheckStrangerMsgLimitResp")
	proto.RegisterType((*IncrStrangerMsgLimitReq)(nil), "imstrangergo.IncrStrangerMsgLimitReq")
	proto.RegisterType((*SetHighConsumeIn24HourReq)(nil), "imstrangergo.SetHighConsumeIn24HourReq")
	proto.RegisterType((*SetHighConsumeIn24HourResp)(nil), "imstrangergo.SetHighConsumeIn24HourResp")
	proto.RegisterType((*IsHighConsumeIn24HourReq)(nil), "imstrangergo.IsHighConsumeIn24HourReq")
	proto.RegisterType((*IsHighConsumeIn24HourResp)(nil), "imstrangergo.IsHighConsumeIn24HourResp")
	proto.RegisterType((*IncrStrangerMsgLimitResp)(nil), "imstrangergo.IncrStrangerMsgLimitResp")
	proto.RegisterType((*ChatAwardReq)(nil), "imstrangergo.ChatAwardReq")
	proto.RegisterType((*ChatAwardResp)(nil), "imstrangergo.ChatAwardResp")
	proto.RegisterType((*GetStrangerGreetDetailReq)(nil), "imstrangergo.GetStrangerGreetDetailReq")
	proto.RegisterType((*GetStrangerGreetDetailResp)(nil), "imstrangergo.GetStrangerGreetDetailResp")
	proto.RegisterType((*BatchCheckUserRecvLimitReq)(nil), "imstrangergo.BatchCheckUserRecvLimitReq")
	proto.RegisterType((*LimitInfo)(nil), "imstrangergo.LimitInfo")
	proto.RegisterType((*BatchCheckUserRecvLimitResp)(nil), "imstrangergo.BatchCheckUserRecvLimitResp")
	proto.RegisterType((*SetIMPresentToTargetReq)(nil), "imstrangergo.SetIMPresentToTargetReq")
	proto.RegisterType((*SetIMPresentToTargetResp)(nil), "imstrangergo.SetIMPresentToTargetResp")
	proto.RegisterType((*CheckIMPresentToTargetReq)(nil), "imstrangergo.CheckIMPresentToTargetReq")
	proto.RegisterType((*CheckIMPresentToTargetResp)(nil), "imstrangergo.CheckIMPresentToTargetResp")
	proto.RegisterType((*CheckSuperPlayerStatusReq)(nil), "imstrangergo.CheckSuperPlayerStatusReq")
	proto.RegisterType((*CheckSuperPlayerStatusResp)(nil), "imstrangergo.CheckSuperPlayerStatusResp")
	proto.RegisterType((*CheckNobilityPrivilegeReq)(nil), "imstrangergo.CheckNobilityPrivilegeReq")
	proto.RegisterType((*CheckNobilityPrivilegeResp)(nil), "imstrangergo.CheckNobilityPrivilegeResp")
	proto.RegisterType((*BatchCheckNobilityPrivilegeReq)(nil), "imstrangergo.BatchCheckNobilityPrivilegeReq")
	proto.RegisterType((*NobilityPrivilegeCheckInfo)(nil), "imstrangergo.NobilityPrivilegeCheckInfo")
	proto.RegisterType((*BatchCheckNobilityPrivilegeResp)(nil), "imstrangergo.BatchCheckNobilityPrivilegeResp")
	proto.RegisterType((*IsContractGuildSuperiorAndSubordinateReq)(nil), "imstrangergo.IsContractGuildSuperiorAndSubordinateReq")
	proto.RegisterType((*IsContractGuildSuperiorAndSubordinateResp)(nil), "imstrangergo.IsContractGuildSuperiorAndSubordinateResp")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ImstrangerGoClient is the client API for ImstrangerGo service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ImstrangerGoClient interface {
	// 是否是陌生人聊天的发起者
	IsStrangerMsgSender(ctx context.Context, in *IsStrangerMsgSenderReq, opts ...grpc.CallOption) (*IsStrangerMsgSenderResp, error)
	// 是陌生人聊天的接收者，需要记录回复记录
	CheckStrangerMsgTargeter(ctx context.Context, in *CheckStrangerMsgTargeterReq, opts ...grpc.CallOption) (*CheckStrangerMsgTargeterResp, error)
	CheckStrangerMsgLimit(ctx context.Context, in *CheckStrangerMsgLimitReq, opts ...grpc.CallOption) (*CheckStrangerMsgLimitResp, error)
	IncrStrangerMsgLimit(ctx context.Context, in *IncrStrangerMsgLimitReq, opts ...grpc.CallOption) (*IncrStrangerMsgLimitResp, error)
	SetHighConsumeIn24Hour(ctx context.Context, in *SetHighConsumeIn24HourReq, opts ...grpc.CallOption) (*SetHighConsumeIn24HourResp, error)
	IsHighConsumeIn24Hour(ctx context.Context, in *IsHighConsumeIn24HourReq, opts ...grpc.CallOption) (*IsHighConsumeIn24HourResp, error)
	ChatAward(ctx context.Context, in *ChatAwardReq, opts ...grpc.CallOption) (*ChatAwardResp, error)
	GetStrangerGreetDetail(ctx context.Context, in *GetStrangerGreetDetailReq, opts ...grpc.CallOption) (*GetStrangerGreetDetailResp, error)
	// 检查用户有没有到被撩上限 批量
	BatchCheckUserRecvLimit(ctx context.Context, in *BatchCheckUserRecvLimitReq, opts ...grpc.CallOption) (*BatchCheckUserRecvLimitResp, error)
	// 设置当天IM送礼记录
	SetIMPresentToTarget(ctx context.Context, in *SetIMPresentToTargetReq, opts ...grpc.CallOption) (*SetIMPresentToTargetResp, error)
	CheckIMPresentToTarget(ctx context.Context, in *CheckIMPresentToTargetReq, opts ...grpc.CallOption) (*CheckIMPresentToTargetResp, error)
	// 判断用户会员状态
	CheckSuperPlayerStatus(ctx context.Context, in *CheckSuperPlayerStatusReq, opts ...grpc.CallOption) (*CheckSuperPlayerStatusResp, error)
	// 检查贵族特权限制
	CheckNobilityPrivilege(ctx context.Context, in *CheckNobilityPrivilegeReq, opts ...grpc.CallOption) (*CheckNobilityPrivilegeResp, error)
	BatchCheckNobilityPrivilege(ctx context.Context, in *BatchCheckNobilityPrivilegeReq, opts ...grpc.CallOption) (*BatchCheckNobilityPrivilegeResp, error)
	// 批量获取被撩上限
	BatchGetStrangerRecvMsgLimit(ctx context.Context, in *BatchGetStrangerRecvMsgLimitReq, opts ...grpc.CallOption) (*BatchGetStrangerRecvMsgLimitResp, error)
	// 是否签约公会的上下属关系
	IsContractGuildSuperiorAndSubordinate(ctx context.Context, in *IsContractGuildSuperiorAndSubordinateReq, opts ...grpc.CallOption) (*IsContractGuildSuperiorAndSubordinateResp, error)
}

type imstrangerGoClient struct {
	cc *grpc.ClientConn
}

func NewImstrangerGoClient(cc *grpc.ClientConn) ImstrangerGoClient {
	return &imstrangerGoClient{cc}
}

func (c *imstrangerGoClient) IsStrangerMsgSender(ctx context.Context, in *IsStrangerMsgSenderReq, opts ...grpc.CallOption) (*IsStrangerMsgSenderResp, error) {
	out := new(IsStrangerMsgSenderResp)
	err := c.cc.Invoke(ctx, "/imstrangergo.ImstrangerGo/IsStrangerMsgSender", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *imstrangerGoClient) CheckStrangerMsgTargeter(ctx context.Context, in *CheckStrangerMsgTargeterReq, opts ...grpc.CallOption) (*CheckStrangerMsgTargeterResp, error) {
	out := new(CheckStrangerMsgTargeterResp)
	err := c.cc.Invoke(ctx, "/imstrangergo.ImstrangerGo/CheckStrangerMsgTargeter", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *imstrangerGoClient) CheckStrangerMsgLimit(ctx context.Context, in *CheckStrangerMsgLimitReq, opts ...grpc.CallOption) (*CheckStrangerMsgLimitResp, error) {
	out := new(CheckStrangerMsgLimitResp)
	err := c.cc.Invoke(ctx, "/imstrangergo.ImstrangerGo/CheckStrangerMsgLimit", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *imstrangerGoClient) IncrStrangerMsgLimit(ctx context.Context, in *IncrStrangerMsgLimitReq, opts ...grpc.CallOption) (*IncrStrangerMsgLimitResp, error) {
	out := new(IncrStrangerMsgLimitResp)
	err := c.cc.Invoke(ctx, "/imstrangergo.ImstrangerGo/IncrStrangerMsgLimit", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *imstrangerGoClient) SetHighConsumeIn24Hour(ctx context.Context, in *SetHighConsumeIn24HourReq, opts ...grpc.CallOption) (*SetHighConsumeIn24HourResp, error) {
	out := new(SetHighConsumeIn24HourResp)
	err := c.cc.Invoke(ctx, "/imstrangergo.ImstrangerGo/SetHighConsumeIn24Hour", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *imstrangerGoClient) IsHighConsumeIn24Hour(ctx context.Context, in *IsHighConsumeIn24HourReq, opts ...grpc.CallOption) (*IsHighConsumeIn24HourResp, error) {
	out := new(IsHighConsumeIn24HourResp)
	err := c.cc.Invoke(ctx, "/imstrangergo.ImstrangerGo/IsHighConsumeIn24Hour", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *imstrangerGoClient) ChatAward(ctx context.Context, in *ChatAwardReq, opts ...grpc.CallOption) (*ChatAwardResp, error) {
	out := new(ChatAwardResp)
	err := c.cc.Invoke(ctx, "/imstrangergo.ImstrangerGo/ChatAward", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *imstrangerGoClient) GetStrangerGreetDetail(ctx context.Context, in *GetStrangerGreetDetailReq, opts ...grpc.CallOption) (*GetStrangerGreetDetailResp, error) {
	out := new(GetStrangerGreetDetailResp)
	err := c.cc.Invoke(ctx, "/imstrangergo.ImstrangerGo/GetStrangerGreetDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *imstrangerGoClient) BatchCheckUserRecvLimit(ctx context.Context, in *BatchCheckUserRecvLimitReq, opts ...grpc.CallOption) (*BatchCheckUserRecvLimitResp, error) {
	out := new(BatchCheckUserRecvLimitResp)
	err := c.cc.Invoke(ctx, "/imstrangergo.ImstrangerGo/BatchCheckUserRecvLimit", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *imstrangerGoClient) SetIMPresentToTarget(ctx context.Context, in *SetIMPresentToTargetReq, opts ...grpc.CallOption) (*SetIMPresentToTargetResp, error) {
	out := new(SetIMPresentToTargetResp)
	err := c.cc.Invoke(ctx, "/imstrangergo.ImstrangerGo/SetIMPresentToTarget", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *imstrangerGoClient) CheckIMPresentToTarget(ctx context.Context, in *CheckIMPresentToTargetReq, opts ...grpc.CallOption) (*CheckIMPresentToTargetResp, error) {
	out := new(CheckIMPresentToTargetResp)
	err := c.cc.Invoke(ctx, "/imstrangergo.ImstrangerGo/CheckIMPresentToTarget", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *imstrangerGoClient) CheckSuperPlayerStatus(ctx context.Context, in *CheckSuperPlayerStatusReq, opts ...grpc.CallOption) (*CheckSuperPlayerStatusResp, error) {
	out := new(CheckSuperPlayerStatusResp)
	err := c.cc.Invoke(ctx, "/imstrangergo.ImstrangerGo/CheckSuperPlayerStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *imstrangerGoClient) CheckNobilityPrivilege(ctx context.Context, in *CheckNobilityPrivilegeReq, opts ...grpc.CallOption) (*CheckNobilityPrivilegeResp, error) {
	out := new(CheckNobilityPrivilegeResp)
	err := c.cc.Invoke(ctx, "/imstrangergo.ImstrangerGo/CheckNobilityPrivilege", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *imstrangerGoClient) BatchCheckNobilityPrivilege(ctx context.Context, in *BatchCheckNobilityPrivilegeReq, opts ...grpc.CallOption) (*BatchCheckNobilityPrivilegeResp, error) {
	out := new(BatchCheckNobilityPrivilegeResp)
	err := c.cc.Invoke(ctx, "/imstrangergo.ImstrangerGo/BatchCheckNobilityPrivilege", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *imstrangerGoClient) BatchGetStrangerRecvMsgLimit(ctx context.Context, in *BatchGetStrangerRecvMsgLimitReq, opts ...grpc.CallOption) (*BatchGetStrangerRecvMsgLimitResp, error) {
	out := new(BatchGetStrangerRecvMsgLimitResp)
	err := c.cc.Invoke(ctx, "/imstrangergo.ImstrangerGo/BatchGetStrangerRecvMsgLimit", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *imstrangerGoClient) IsContractGuildSuperiorAndSubordinate(ctx context.Context, in *IsContractGuildSuperiorAndSubordinateReq, opts ...grpc.CallOption) (*IsContractGuildSuperiorAndSubordinateResp, error) {
	out := new(IsContractGuildSuperiorAndSubordinateResp)
	err := c.cc.Invoke(ctx, "/imstrangergo.ImstrangerGo/IsContractGuildSuperiorAndSubordinate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ImstrangerGoServer is the server API for ImstrangerGo service.
type ImstrangerGoServer interface {
	// 是否是陌生人聊天的发起者
	IsStrangerMsgSender(context.Context, *IsStrangerMsgSenderReq) (*IsStrangerMsgSenderResp, error)
	// 是陌生人聊天的接收者，需要记录回复记录
	CheckStrangerMsgTargeter(context.Context, *CheckStrangerMsgTargeterReq) (*CheckStrangerMsgTargeterResp, error)
	CheckStrangerMsgLimit(context.Context, *CheckStrangerMsgLimitReq) (*CheckStrangerMsgLimitResp, error)
	IncrStrangerMsgLimit(context.Context, *IncrStrangerMsgLimitReq) (*IncrStrangerMsgLimitResp, error)
	SetHighConsumeIn24Hour(context.Context, *SetHighConsumeIn24HourReq) (*SetHighConsumeIn24HourResp, error)
	IsHighConsumeIn24Hour(context.Context, *IsHighConsumeIn24HourReq) (*IsHighConsumeIn24HourResp, error)
	ChatAward(context.Context, *ChatAwardReq) (*ChatAwardResp, error)
	GetStrangerGreetDetail(context.Context, *GetStrangerGreetDetailReq) (*GetStrangerGreetDetailResp, error)
	// 检查用户有没有到被撩上限 批量
	BatchCheckUserRecvLimit(context.Context, *BatchCheckUserRecvLimitReq) (*BatchCheckUserRecvLimitResp, error)
	// 设置当天IM送礼记录
	SetIMPresentToTarget(context.Context, *SetIMPresentToTargetReq) (*SetIMPresentToTargetResp, error)
	CheckIMPresentToTarget(context.Context, *CheckIMPresentToTargetReq) (*CheckIMPresentToTargetResp, error)
	// 判断用户会员状态
	CheckSuperPlayerStatus(context.Context, *CheckSuperPlayerStatusReq) (*CheckSuperPlayerStatusResp, error)
	// 检查贵族特权限制
	CheckNobilityPrivilege(context.Context, *CheckNobilityPrivilegeReq) (*CheckNobilityPrivilegeResp, error)
	BatchCheckNobilityPrivilege(context.Context, *BatchCheckNobilityPrivilegeReq) (*BatchCheckNobilityPrivilegeResp, error)
	// 批量获取被撩上限
	BatchGetStrangerRecvMsgLimit(context.Context, *BatchGetStrangerRecvMsgLimitReq) (*BatchGetStrangerRecvMsgLimitResp, error)
	// 是否签约公会的上下属关系
	IsContractGuildSuperiorAndSubordinate(context.Context, *IsContractGuildSuperiorAndSubordinateReq) (*IsContractGuildSuperiorAndSubordinateResp, error)
}

func RegisterImstrangerGoServer(s *grpc.Server, srv ImstrangerGoServer) {
	s.RegisterService(&_ImstrangerGo_serviceDesc, srv)
}

func _ImstrangerGo_IsStrangerMsgSender_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IsStrangerMsgSenderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImstrangerGoServer).IsStrangerMsgSender(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/imstrangergo.ImstrangerGo/IsStrangerMsgSender",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImstrangerGoServer).IsStrangerMsgSender(ctx, req.(*IsStrangerMsgSenderReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ImstrangerGo_CheckStrangerMsgTargeter_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckStrangerMsgTargeterReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImstrangerGoServer).CheckStrangerMsgTargeter(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/imstrangergo.ImstrangerGo/CheckStrangerMsgTargeter",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImstrangerGoServer).CheckStrangerMsgTargeter(ctx, req.(*CheckStrangerMsgTargeterReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ImstrangerGo_CheckStrangerMsgLimit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckStrangerMsgLimitReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImstrangerGoServer).CheckStrangerMsgLimit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/imstrangergo.ImstrangerGo/CheckStrangerMsgLimit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImstrangerGoServer).CheckStrangerMsgLimit(ctx, req.(*CheckStrangerMsgLimitReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ImstrangerGo_IncrStrangerMsgLimit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IncrStrangerMsgLimitReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImstrangerGoServer).IncrStrangerMsgLimit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/imstrangergo.ImstrangerGo/IncrStrangerMsgLimit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImstrangerGoServer).IncrStrangerMsgLimit(ctx, req.(*IncrStrangerMsgLimitReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ImstrangerGo_SetHighConsumeIn24Hour_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetHighConsumeIn24HourReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImstrangerGoServer).SetHighConsumeIn24Hour(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/imstrangergo.ImstrangerGo/SetHighConsumeIn24Hour",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImstrangerGoServer).SetHighConsumeIn24Hour(ctx, req.(*SetHighConsumeIn24HourReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ImstrangerGo_IsHighConsumeIn24Hour_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IsHighConsumeIn24HourReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImstrangerGoServer).IsHighConsumeIn24Hour(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/imstrangergo.ImstrangerGo/IsHighConsumeIn24Hour",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImstrangerGoServer).IsHighConsumeIn24Hour(ctx, req.(*IsHighConsumeIn24HourReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ImstrangerGo_ChatAward_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChatAwardReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImstrangerGoServer).ChatAward(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/imstrangergo.ImstrangerGo/ChatAward",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImstrangerGoServer).ChatAward(ctx, req.(*ChatAwardReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ImstrangerGo_GetStrangerGreetDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStrangerGreetDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImstrangerGoServer).GetStrangerGreetDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/imstrangergo.ImstrangerGo/GetStrangerGreetDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImstrangerGoServer).GetStrangerGreetDetail(ctx, req.(*GetStrangerGreetDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ImstrangerGo_BatchCheckUserRecvLimit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchCheckUserRecvLimitReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImstrangerGoServer).BatchCheckUserRecvLimit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/imstrangergo.ImstrangerGo/BatchCheckUserRecvLimit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImstrangerGoServer).BatchCheckUserRecvLimit(ctx, req.(*BatchCheckUserRecvLimitReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ImstrangerGo_SetIMPresentToTarget_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetIMPresentToTargetReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImstrangerGoServer).SetIMPresentToTarget(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/imstrangergo.ImstrangerGo/SetIMPresentToTarget",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImstrangerGoServer).SetIMPresentToTarget(ctx, req.(*SetIMPresentToTargetReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ImstrangerGo_CheckIMPresentToTarget_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckIMPresentToTargetReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImstrangerGoServer).CheckIMPresentToTarget(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/imstrangergo.ImstrangerGo/CheckIMPresentToTarget",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImstrangerGoServer).CheckIMPresentToTarget(ctx, req.(*CheckIMPresentToTargetReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ImstrangerGo_CheckSuperPlayerStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckSuperPlayerStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImstrangerGoServer).CheckSuperPlayerStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/imstrangergo.ImstrangerGo/CheckSuperPlayerStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImstrangerGoServer).CheckSuperPlayerStatus(ctx, req.(*CheckSuperPlayerStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ImstrangerGo_CheckNobilityPrivilege_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckNobilityPrivilegeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImstrangerGoServer).CheckNobilityPrivilege(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/imstrangergo.ImstrangerGo/CheckNobilityPrivilege",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImstrangerGoServer).CheckNobilityPrivilege(ctx, req.(*CheckNobilityPrivilegeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ImstrangerGo_BatchCheckNobilityPrivilege_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchCheckNobilityPrivilegeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImstrangerGoServer).BatchCheckNobilityPrivilege(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/imstrangergo.ImstrangerGo/BatchCheckNobilityPrivilege",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImstrangerGoServer).BatchCheckNobilityPrivilege(ctx, req.(*BatchCheckNobilityPrivilegeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ImstrangerGo_BatchGetStrangerRecvMsgLimit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetStrangerRecvMsgLimitReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImstrangerGoServer).BatchGetStrangerRecvMsgLimit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/imstrangergo.ImstrangerGo/BatchGetStrangerRecvMsgLimit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImstrangerGoServer).BatchGetStrangerRecvMsgLimit(ctx, req.(*BatchGetStrangerRecvMsgLimitReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ImstrangerGo_IsContractGuildSuperiorAndSubordinate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IsContractGuildSuperiorAndSubordinateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImstrangerGoServer).IsContractGuildSuperiorAndSubordinate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/imstrangergo.ImstrangerGo/IsContractGuildSuperiorAndSubordinate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImstrangerGoServer).IsContractGuildSuperiorAndSubordinate(ctx, req.(*IsContractGuildSuperiorAndSubordinateReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ImstrangerGo_serviceDesc = grpc.ServiceDesc{
	ServiceName: "imstrangergo.ImstrangerGo",
	HandlerType: (*ImstrangerGoServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "IsStrangerMsgSender",
			Handler:    _ImstrangerGo_IsStrangerMsgSender_Handler,
		},
		{
			MethodName: "CheckStrangerMsgTargeter",
			Handler:    _ImstrangerGo_CheckStrangerMsgTargeter_Handler,
		},
		{
			MethodName: "CheckStrangerMsgLimit",
			Handler:    _ImstrangerGo_CheckStrangerMsgLimit_Handler,
		},
		{
			MethodName: "IncrStrangerMsgLimit",
			Handler:    _ImstrangerGo_IncrStrangerMsgLimit_Handler,
		},
		{
			MethodName: "SetHighConsumeIn24Hour",
			Handler:    _ImstrangerGo_SetHighConsumeIn24Hour_Handler,
		},
		{
			MethodName: "IsHighConsumeIn24Hour",
			Handler:    _ImstrangerGo_IsHighConsumeIn24Hour_Handler,
		},
		{
			MethodName: "ChatAward",
			Handler:    _ImstrangerGo_ChatAward_Handler,
		},
		{
			MethodName: "GetStrangerGreetDetail",
			Handler:    _ImstrangerGo_GetStrangerGreetDetail_Handler,
		},
		{
			MethodName: "BatchCheckUserRecvLimit",
			Handler:    _ImstrangerGo_BatchCheckUserRecvLimit_Handler,
		},
		{
			MethodName: "SetIMPresentToTarget",
			Handler:    _ImstrangerGo_SetIMPresentToTarget_Handler,
		},
		{
			MethodName: "CheckIMPresentToTarget",
			Handler:    _ImstrangerGo_CheckIMPresentToTarget_Handler,
		},
		{
			MethodName: "CheckSuperPlayerStatus",
			Handler:    _ImstrangerGo_CheckSuperPlayerStatus_Handler,
		},
		{
			MethodName: "CheckNobilityPrivilege",
			Handler:    _ImstrangerGo_CheckNobilityPrivilege_Handler,
		},
		{
			MethodName: "BatchCheckNobilityPrivilege",
			Handler:    _ImstrangerGo_BatchCheckNobilityPrivilege_Handler,
		},
		{
			MethodName: "BatchGetStrangerRecvMsgLimit",
			Handler:    _ImstrangerGo_BatchGetStrangerRecvMsgLimit_Handler,
		},
		{
			MethodName: "IsContractGuildSuperiorAndSubordinate",
			Handler:    _ImstrangerGo_IsContractGuildSuperiorAndSubordinate_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/imstranger-go/imstranger-go.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/imstranger-go/imstranger-go.proto", fileDescriptor_imstranger_go_803788eaa33a98ff)
}

var fileDescriptor_imstranger_go_803788eaa33a98ff = []byte{
	// 1308 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xc4, 0x58, 0x6f, 0x6f, 0x1b, 0xc5,
	0x13, 0x8e, 0xf3, 0xa7, 0xb5, 0x27, 0x75, 0xd3, 0xdf, 0xfe, 0x4a, 0xed, 0x5c, 0x02, 0x8d, 0x4e,
	0x6d, 0x70, 0xa0, 0x71, 0x4a, 0x80, 0xb6, 0x52, 0x0b, 0x52, 0x1b, 0x20, 0x35, 0xc4, 0x25, 0x3a,
	0xa7, 0x02, 0xf5, 0xcd, 0xe9, 0x7a, 0xde, 0x9e, 0x97, 0x9e, 0xf7, 0x2e, 0x3b, 0xeb, 0x14, 0x4b,
	0x48, 0x7c, 0x09, 0x5e, 0xf1, 0x21, 0xfa, 0x09, 0xe0, 0xbb, 0xa1, 0xdb, 0xfb, 0x63, 0xfb, 0xbc,
	0xe7, 0x38, 0x11, 0x12, 0xef, 0x6e, 0xe7, 0x66, 0x9f, 0x99, 0x79, 0x6e, 0x76, 0x9f, 0xb1, 0xe1,
	0xbe, 0x94, 0x7b, 0xa7, 0x03, 0xe6, 0xbe, 0x45, 0xe6, 0x9f, 0x51, 0xb1, 0xc7, 0xfa, 0x28, 0x85,
	0xc3, 0x3d, 0x2a, 0x76, 0xbd, 0x60, 0x72, 0xd5, 0x0c, 0x45, 0x20, 0x03, 0x72, 0x6d, 0x64, 0xf4,
	0x02, 0xf3, 0x67, 0xb8, 0xfd, 0xcc, 0x91, 0x6e, 0xef, 0x90, 0xca, 0x4e, 0x62, 0xb5, 0xa8, 0x7b,
	0xd6, 0x46, 0xef, 0x88, 0xf5, 0x99, 0xb4, 0xe8, 0x29, 0xb9, 0x0d, 0xab, 0xd2, 0x11, 0x1e, 0x95,
	0xf6, 0x80, 0x75, 0xb1, 0x5e, 0xda, 0x5a, 0x6a, 0x54, 0x2d, 0x88, 0x4d, 0x2f, 0x59, 0x17, 0xc9,
	0x4d, 0x58, 0x41, 0x97, 0x72, 0x5a, 0x5f, 0xdc, 0x2a, 0x35, 0x2a, 0x56, 0xbc, 0x30, 0xdf, 0x97,
	0x60, 0x6b, 0x36, 0x34, 0x86, 0xe4, 0x08, 0x96, 0x19, 0x7f, 0x13, 0x28, 0xd0, 0xd5, 0xfd, 0x47,
	0xcd, 0xf1, 0xdc, 0x9a, 0xe7, 0xed, 0x6e, 0xfa, 0xd1, 0x53, 0x8b, 0xbf, 0x09, 0x2c, 0x85, 0x62,
	0x3c, 0x81, 0x4a, 0x66, 0x22, 0x1f, 0x02, 0x8c, 0xd2, 0xae, 0x97, 0xb6, 0x4a, 0x8d, 0xaa, 0x55,
	0xc9, 0xb2, 0x26, 0x37, 0x60, 0xc9, 0xe5, 0x52, 0xa5, 0x5c, 0xb5, 0xa2, 0x47, 0xf3, 0x39, 0xdc,
	0x6a, 0x61, 0x1a, 0xab, 0x8d, 0x5e, 0x87, 0xf2, 0x6e, 0x14, 0xf4, 0x94, 0xdc, 0x82, 0x2b, 0xa8,
	0x16, 0x09, 0x4c, 0xb2, 0x8a, 0xec, 0x31, 0x60, 0x02, 0x93, 0xac, 0xcc, 0xcf, 0xa0, 0xa6, 0x45,
	0xc2, 0x30, 0xda, 0x22, 0x28, 0x0e, 0x7c, 0xa9, 0xa0, 0xca, 0x56, 0xb2, 0x32, 0xdb, 0xb0, 0x71,
	0xd0, 0xa3, 0xee, 0xdb, 0xb1, 0x5d, 0x27, 0x0a, 0xec, 0x72, 0x19, 0x3c, 0x80, 0xcd, 0x62, 0xb8,
	0x19, 0x69, 0xbc, 0x2f, 0x41, 0x3d, 0xbf, 0x31, 0x6b, 0x84, 0x1b, 0xb0, 0x34, 0xa2, 0x32, 0x7a,
	0xcc, 0x71, 0xbc, 0x98, 0xe7, 0xf8, 0x3a, 0x2c, 0xb2, 0xb0, 0xbe, 0xa4, 0xba, 0x62, 0x91, 0x85,
	0x64, 0x07, 0xfe, 0xe7, 0x46, 0xe0, 0x76, 0xdf, 0xe1, 0xb6, 0xcb, 0xa5, 0xfd, 0x96, 0x0e, 0xeb,
	0xcb, 0xea, 0xf5, 0x75, 0xf5, 0xa2, 0xed, 0xf0, 0x03, 0x2e, 0x7f, 0xa0, 0x43, 0xb2, 0x0d, 0x6b,
	0x7d, 0xf4, 0x6c, 0x0c, 0x06, 0xc2, 0xa5, 0xb6, 0x1c, 0x86, 0xb4, 0xbe, 0xa2, 0xe0, 0xab, 0x7d,
	0xf4, 0x3a, 0xca, 0x7a, 0x32, 0x0c, 0xa9, 0xf9, 0x77, 0x09, 0xd6, 0x0b, 0x12, 0x9e, 0x2a, 0x73,
	0x25, 0x2d, 0x93, 0x3c, 0x81, 0x0d, 0x86, 0x36, 0xa7, 0xef, 0xec, 0xb4, 0xdd, 0xec, 0xb4, 0x0e,
	0xa4, 0x42, 0x15, 0x52, 0xb6, 0x6a, 0x0c, 0x5f, 0xd0, 0x77, 0x29, 0x6e, 0x4c, 0xdf, 0x4b, 0xa4,
	0x82, 0x7c, 0x0a, 0x24, 0xbf, 0x3b, 0x29, 0xb3, 0x6c, 0xad, 0x4d, 0x6c, 0x6a, 0x85, 0x11, 0x45,
	0x71, 0x50, 0xbb, 0x8f, 0x5e, 0x52, 0x6c, 0x25, 0xb6, 0xb4, 0xd1, 0x33, 0xff, 0x5a, 0x84, 0x5a,
	0x8b, 0xbb, 0xe2, 0xbf, 0xe6, 0xfb, 0x1c, 0x46, 0x56, 0x2e, 0xc3, 0xc8, 0x15, 0x3d, 0x23, 0x9a,
	0x4f, 0x7b, 0x55, 0xf3, 0x69, 0x89, 0x09, 0x55, 0x86, 0x76, 0xc0, 0xfd, 0xa1, 0x2d, 0x68, 0xe8,
	0x0f, 0xeb, 0x65, 0x85, 0xb7, 0xca, 0xf0, 0x47, 0xee, 0x0f, 0xad, 0xc8, 0x64, 0xee, 0xc2, 0x7a,
	0x87, 0xca, 0xe7, 0xcc, 0xeb, 0x1d, 0x04, 0x1c, 0x07, 0x7d, 0xda, 0xe2, 0xfb, 0x5f, 0x3c, 0x0f,
	0x06, 0x42, 0xcb, 0x9f, 0xb9, 0x09, 0x46, 0x91, 0x3b, 0x86, 0xe6, 0x3d, 0xa8, 0xb7, 0x70, 0x6e,
	0xac, 0xa7, 0xb0, 0x5e, 0xe0, 0x8d, 0x21, 0xb9, 0x13, 0xe5, 0x3e, 0xf6, 0x32, 0x39, 0x66, 0x93,
	0x46, 0x73, 0x1f, 0xea, 0xfa, 0x6f, 0x3f, 0xd5, 0xba, 0xd5, 0xec, 0x84, 0x7e, 0x0d, 0xd7, 0x0e,
	0x7a, 0x8e, 0x7c, 0xfa, 0xce, 0x11, 0xdd, 0x2c, 0x31, 0x3e, 0x4a, 0x8c, 0x93, 0x4d, 0x18, 0xb5,
	0xc4, 0x54, 0x8f, 0x98, 0x6d, 0xa8, 0x8e, 0xed, 0xc7, 0x90, 0x18, 0x50, 0x76, 0xa2, 0xc5, 0xcb,
	0x04, 0xa5, 0x6c, 0x65, 0x6b, 0xb2, 0x05, 0xab, 0xea, 0xf9, 0x64, 0x74, 0xc7, 0x94, 0xad, 0x71,
	0x53, 0xf4, 0x01, 0xc6, 0x6e, 0xe8, 0x43, 0x41, 0xa9, 0xfc, 0x86, 0x4a, 0x87, 0xf9, 0x7a, 0xd2,
	0xbe, 0x02, 0xa3, 0xc8, 0x1d, 0xc3, 0x73, 0x95, 0xc6, 0x6c, 0x81, 0xa1, 0x44, 0x41, 0x9d, 0xf8,
	0xa8, 0xf3, 0x22, 0x49, 0x98, 0x71, 0x5e, 0xd6, 0xa1, 0x3c, 0x60, 0x5d, 0xdb, 0x67, 0x18, 0x25,
	0x1f, 0xa1, 0x5d, 0x1d, 0xb0, 0xee, 0x11, 0x43, 0x69, 0x3e, 0x84, 0xca, 0x51, 0xa6, 0x15, 0xd3,
	0x3b, 0xeb, 0x70, 0x95, 0xa1, 0x72, 0x48, 0xaa, 0x4e, 0x97, 0xe6, 0x4f, 0xb0, 0x51, 0x98, 0x03,
	0x86, 0xe4, 0x11, 0xac, 0x26, 0xe7, 0x5d, 0x45, 0x8d, 0x85, 0xad, 0x36, 0x29, 0x6c, 0x59, 0x60,
	0x2b, 0xb9, 0x1b, 0x54, 0x46, 0xdf, 0x43, 0xad, 0x43, 0x65, 0xab, 0x7d, 0x2c, 0x28, 0x52, 0x2e,
	0x4f, 0x82, 0x98, 0xe2, 0xcb, 0xdc, 0x04, 0xa6, 0x01, 0x75, 0x3d, 0x16, 0x86, 0xe6, 0x51, 0x72,
	0x63, 0xfe, 0x3b, 0x91, 0x1e, 0x83, 0x51, 0x84, 0x86, 0xea, 0xf6, 0xeb, 0x39, 0x68, 0x0b, 0xea,
	0x06, 0xa2, 0x9b, 0xb4, 0x57, 0xa5, 0xe7, 0xa0, 0xa5, 0x0c, 0x51, 0xf7, 0xc4, 0x97, 0xf7, 0x20,
	0xa4, 0xe2, 0xd8, 0x77, 0x86, 0x54, 0x74, 0xa4, 0x23, 0x07, 0x58, 0xd8, 0x3d, 0x45, 0xee, 0x71,
	0xf7, 0xf0, 0x40, 0xda, 0xf4, 0xd7, 0x90, 0x09, 0x9a, 0x06, 0x03, 0x1e, 0xc8, 0x6f, 0x63, 0x4b,
	0x56, 0xf8, 0x8b, 0xe0, 0x35, 0xf3, 0x99, 0x1c, 0x1e, 0x0b, 0x76, 0xc6, 0x7c, 0xea, 0xd1, 0x4b,
	0x15, 0xfe, 0x30, 0x49, 0x46, 0x83, 0x86, 0x61, 0xd4, 0x79, 0x0c, 0x6d, 0x35, 0x8d, 0x24, 0x99,
	0x64, 0x0d, 0xf4, 0x0a, 0x3e, 0x1a, 0x35, 0xd0, 0x9c, 0xb9, 0x6c, 0xc3, 0xda, 0x28, 0x97, 0xf1,
	0x7e, 0xae, 0x66, 0x09, 0xa9, 0x1e, 0xea, 0x81, 0x31, 0x85, 0x18, 0x7f, 0x1e, 0x7d, 0x9b, 0x9f,
	0x23, 0x28, 0xe3, 0x55, 0x2c, 0x4d, 0x56, 0x81, 0xc9, 0xe0, 0x38, 0x83, 0x83, 0x63, 0x58, 0x8b,
	0xe5, 0x27, 0x1a, 0xce, 0xc6, 0x8f, 0x43, 0x63, 0xf2, 0x38, 0x14, 0x67, 0x6c, 0x55, 0xdd, 0xf4,
	0x51, 0x95, 0x77, 0x02, 0x8d, 0x16, 0x1e, 0x04, 0x5c, 0x0a, 0xc7, 0x95, 0x87, 0x03, 0xe6, 0x77,
	0x55, 0x2b, 0xb0, 0x40, 0x3c, 0xe5, 0xdd, 0xce, 0xe0, 0x75, 0x20, 0xba, 0x8c, 0x3b, 0x32, 0x4f,
	0xe2, 0x72, 0x5c, 0xec, 0xe4, 0xb0, 0xb4, 0x9c, 0x0d, 0x4b, 0x8f, 0x61, 0x67, 0x4e, 0x54, 0x0c,
	0x95, 0xc6, 0x62, 0xf2, 0x49, 0x17, 0x19, 0xee, 0xff, 0x71, 0x1d, 0xae, 0xb5, 0xb2, 0x6a, 0x0e,
	0x03, 0xd2, 0x85, 0xff, 0x6b, 0x86, 0x3f, 0x72, 0x67, 0xb2, 0x66, 0xfd, 0xa4, 0x69, 0xdc, 0x9d,
	0xc3, 0x0b, 0x43, 0x73, 0x81, 0xe0, 0xf4, 0x9c, 0x96, 0x0e, 0x78, 0x64, 0x67, 0x12, 0x64, 0xc6,
	0x5c, 0x69, 0x7c, 0x32, 0xaf, 0xab, 0x0a, 0xfa, 0x0b, 0x7c, 0xa0, 0x9d, 0xb5, 0xc8, 0xf6, 0x6c,
	0x98, 0xf4, 0x86, 0x36, 0x3e, 0x9e, 0xcb, 0x4f, 0xc5, 0xf2, 0xe0, 0xa6, 0x4e, 0x1b, 0x49, 0x9e,
	0x21, 0xfd, 0xec, 0x64, 0x6c, 0xcf, 0xe3, 0xa6, 0x02, 0xf5, 0xe1, 0x96, 0x7e, 0x26, 0x20, 0xb9,
	0x6c, 0x0b, 0x07, 0x0d, 0xa3, 0x31, 0x9f, 0x63, 0xca, 0xa1, 0x76, 0x6c, 0xc8, 0x73, 0x58, 0x34,
	0x89, 0xe4, 0x39, 0x2c, 0x9c, 0x41, 0xcc, 0x05, 0xf2, 0x1d, 0x54, 0x32, 0xad, 0x27, 0x46, 0x9e,
	0xfb, 0xd1, 0x10, 0x61, 0x6c, 0x14, 0xbe, 0x4b, 0x29, 0xd2, 0xab, 0x76, 0x9e, 0xa2, 0xc2, 0x51,
	0x20, 0x4f, 0x51, 0xf1, 0x10, 0x60, 0x2e, 0x90, 0x10, 0x6a, 0x05, 0x0a, 0x4b, 0x1a, 0x9a, 0x5f,
	0x88, 0xda, 0x61, 0xc0, 0xd8, 0x99, 0xd3, 0x33, 0x6d, 0x36, 0x9d, 0x5c, 0xe6, 0x9b, 0xad, 0x40,
	0x9e, 0xf3, 0xcd, 0x56, 0xa8, 0xbc, 0x8a, 0x49, 0xbd, 0x5a, 0x12, 0xdd, 0xd1, 0xd0, 0x06, 0x6b,
	0xcc, 0xe7, 0x38, 0x11, 0x6e, 0x4a, 0x30, 0xb5, 0xe1, 0x74, 0x2a, 0xac, 0x0d, 0xa7, 0xd5, 0xdf,
	0xb1, 0x70, 0x53, 0x17, 0xba, 0x36, 0x9c, 0x4e, 0xfa, 0xb4, 0xe1, 0xb4, 0xea, 0x62, 0x2e, 0x90,
	0xdf, 0xc6, 0x27, 0xb1, 0xe9, 0x98, 0xf7, 0x8a, 0x3a, 0x40, 0x1b, 0x78, 0xf7, 0x02, 0xde, 0x2a,
	0xfa, 0xef, 0xb0, 0x39, 0xeb, 0x0f, 0x0a, 0xb2, 0x7b, 0x91, 0x3f, 0x33, 0x4e, 0x8d, 0xe6, 0xc5,
	0xfe, 0xfb, 0x30, 0x17, 0xc8, 0x9f, 0x25, 0xb8, 0x3b, 0x97, 0x6e, 0x91, 0x07, 0xf9, 0x2b, 0x63,
	0x3e, 0x09, 0x35, 0x1e, 0x5e, 0x6a, 0x5f, 0x94, 0xdc, 0xb3, 0xfb, 0xaf, 0x9a, 0x5e, 0xe0, 0x3b,
	0xdc, 0x6b, 0x7e, 0xb9, 0x2f, 0x65, 0xd3, 0x0d, 0xfa, 0x7b, 0xea, 0xef, 0x27, 0x37, 0xf0, 0xf7,
	0x90, 0x8a, 0x33, 0xe6, 0x52, 0xdc, 0x1b, 0x47, 0x7f, 0x7d, 0x45, 0xbd, 0xff, 0xfc, 0x9f, 0x00,
	0x00, 0x00, 0xff, 0xff, 0x4d, 0x17, 0xe7, 0x4f, 0xd2, 0x12, 0x00, 0x00,
}
