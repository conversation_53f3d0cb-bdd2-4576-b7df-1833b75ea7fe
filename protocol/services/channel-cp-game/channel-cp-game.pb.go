// Code generated by protoc-gen-go. DO NOT EDIT.
// source: channel-cp-game/channel-cp-game.proto

package channel_cp_game // import "golang.52tt.com/protocol/services/channel-cp-game"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/services/extension/options"
import reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 阶段类型
type CpGamePhaseType int32

const (
	CpGamePhaseType_CP_GAME_PHASE_CLOSE      CpGamePhaseType = 0
	CpGamePhaseType_CP_GAME_PHASE_CREATE_CP  CpGamePhaseType = 1
	CpGamePhaseType_CP_GAME_PHASE_PK         CpGamePhaseType = 2
	CpGamePhaseType_CP_GAME_PHASE_PUNISH     CpGamePhaseType = 3
	CpGamePhaseType_CP_GAME_PHASE_CHOSE_RARE CpGamePhaseType = 4
)

var CpGamePhaseType_name = map[int32]string{
	0: "CP_GAME_PHASE_CLOSE",
	1: "CP_GAME_PHASE_CREATE_CP",
	2: "CP_GAME_PHASE_PK",
	3: "CP_GAME_PHASE_PUNISH",
	4: "CP_GAME_PHASE_CHOSE_RARE",
}
var CpGamePhaseType_value = map[string]int32{
	"CP_GAME_PHASE_CLOSE":      0,
	"CP_GAME_PHASE_CREATE_CP":  1,
	"CP_GAME_PHASE_PK":         2,
	"CP_GAME_PHASE_PUNISH":     3,
	"CP_GAME_PHASE_CHOSE_RARE": 4,
}

func (x CpGamePhaseType) String() string {
	return proto.EnumName(CpGamePhaseType_name, int32(x))
}
func (CpGamePhaseType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_cp_game_1310697ea80348e6, []int{0}
}

type EUserPrivilegeType int32

const (
	EUserPrivilegeType_ENUM_USER_PRIVILEGE_UNKNOWN EUserPrivilegeType = 0
	EUserPrivilegeType_ENUM_USER_PRIVILEGE_UKW     EUserPrivilegeType = 1
)

var EUserPrivilegeType_name = map[int32]string{
	0: "ENUM_USER_PRIVILEGE_UNKNOWN",
	1: "ENUM_USER_PRIVILEGE_UKW",
}
var EUserPrivilegeType_value = map[string]int32{
	"ENUM_USER_PRIVILEGE_UNKNOWN": 0,
	"ENUM_USER_PRIVILEGE_UKW":     1,
}

func (x EUserPrivilegeType) String() string {
	return proto.EnumName(EUserPrivilegeType_name, int32(x))
}
func (EUserPrivilegeType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_cp_game_1310697ea80348e6, []int{1}
}

type CpGameInfo_PKSubPhase int32

const (
	CpGameInfo_Common        CpGameInfo_PKSubPhase = 0
	CpGameInfo_WaitToAddTime CpGameInfo_PKSubPhase = 1
)

var CpGameInfo_PKSubPhase_name = map[int32]string{
	0: "Common",
	1: "WaitToAddTime",
}
var CpGameInfo_PKSubPhase_value = map[string]int32{
	"Common":        0,
	"WaitToAddTime": 1,
}

func (x CpGameInfo_PKSubPhase) String() string {
	return proto.EnumName(CpGameInfo_PKSubPhase_name, int32(x))
}
func (CpGameInfo_PKSubPhase) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_cp_game_1310697ea80348e6, []int{13, 0}
}

type CpCardConf_SubResourceIdx int32

const (
	CpCardConf_Male_Male     CpCardConf_SubResourceIdx = 0
	CpCardConf_Male_Female   CpCardConf_SubResourceIdx = 1
	CpCardConf_Female_Female CpCardConf_SubResourceIdx = 2
)

var CpCardConf_SubResourceIdx_name = map[int32]string{
	0: "Male_Male",
	1: "Male_Female",
	2: "Female_Female",
}
var CpCardConf_SubResourceIdx_value = map[string]int32{
	"Male_Male":     0,
	"Male_Female":   1,
	"Female_Female": 2,
}

func (x CpCardConf_SubResourceIdx) String() string {
	return proto.EnumName(CpCardConf_SubResourceIdx_name, int32(x))
}
func (CpCardConf_SubResourceIdx) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_cp_game_1310697ea80348e6, []int{24, 0}
}

type BatchGetChannelCpGamePhaseReq struct {
	ChannelId            []uint32 `protobuf:"varint,1,rep,packed,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetChannelCpGamePhaseReq) Reset()         { *m = BatchGetChannelCpGamePhaseReq{} }
func (m *BatchGetChannelCpGamePhaseReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetChannelCpGamePhaseReq) ProtoMessage()    {}
func (*BatchGetChannelCpGamePhaseReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_cp_game_1310697ea80348e6, []int{0}
}
func (m *BatchGetChannelCpGamePhaseReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetChannelCpGamePhaseReq.Unmarshal(m, b)
}
func (m *BatchGetChannelCpGamePhaseReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetChannelCpGamePhaseReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetChannelCpGamePhaseReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetChannelCpGamePhaseReq.Merge(dst, src)
}
func (m *BatchGetChannelCpGamePhaseReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetChannelCpGamePhaseReq.Size(m)
}
func (m *BatchGetChannelCpGamePhaseReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetChannelCpGamePhaseReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetChannelCpGamePhaseReq proto.InternalMessageInfo

func (m *BatchGetChannelCpGamePhaseReq) GetChannelId() []uint32 {
	if m != nil {
		return m.ChannelId
	}
	return nil
}

type BatchGetChannelCpGamePhaseResp struct {
	PhaseMap             map[uint32]uint32 `protobuf:"bytes,1,rep,name=phase_map,json=phaseMap,proto3" json:"phase_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *BatchGetChannelCpGamePhaseResp) Reset()         { *m = BatchGetChannelCpGamePhaseResp{} }
func (m *BatchGetChannelCpGamePhaseResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetChannelCpGamePhaseResp) ProtoMessage()    {}
func (*BatchGetChannelCpGamePhaseResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_cp_game_1310697ea80348e6, []int{1}
}
func (m *BatchGetChannelCpGamePhaseResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetChannelCpGamePhaseResp.Unmarshal(m, b)
}
func (m *BatchGetChannelCpGamePhaseResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetChannelCpGamePhaseResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetChannelCpGamePhaseResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetChannelCpGamePhaseResp.Merge(dst, src)
}
func (m *BatchGetChannelCpGamePhaseResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetChannelCpGamePhaseResp.Size(m)
}
func (m *BatchGetChannelCpGamePhaseResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetChannelCpGamePhaseResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetChannelCpGamePhaseResp proto.InternalMessageInfo

func (m *BatchGetChannelCpGamePhaseResp) GetPhaseMap() map[uint32]uint32 {
	if m != nil {
		return m.PhaseMap
	}
	return nil
}

type GetChannelCpGamePhaseReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelCpGamePhaseReq) Reset()         { *m = GetChannelCpGamePhaseReq{} }
func (m *GetChannelCpGamePhaseReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelCpGamePhaseReq) ProtoMessage()    {}
func (*GetChannelCpGamePhaseReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_cp_game_1310697ea80348e6, []int{2}
}
func (m *GetChannelCpGamePhaseReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelCpGamePhaseReq.Unmarshal(m, b)
}
func (m *GetChannelCpGamePhaseReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelCpGamePhaseReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelCpGamePhaseReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelCpGamePhaseReq.Merge(dst, src)
}
func (m *GetChannelCpGamePhaseReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelCpGamePhaseReq.Size(m)
}
func (m *GetChannelCpGamePhaseReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelCpGamePhaseReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelCpGamePhaseReq proto.InternalMessageInfo

func (m *GetChannelCpGamePhaseReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetChannelCpGamePhaseResp struct {
	Phase                uint32   `protobuf:"varint,1,opt,name=phase,proto3" json:"phase,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelCpGamePhaseResp) Reset()         { *m = GetChannelCpGamePhaseResp{} }
func (m *GetChannelCpGamePhaseResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelCpGamePhaseResp) ProtoMessage()    {}
func (*GetChannelCpGamePhaseResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_cp_game_1310697ea80348e6, []int{3}
}
func (m *GetChannelCpGamePhaseResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelCpGamePhaseResp.Unmarshal(m, b)
}
func (m *GetChannelCpGamePhaseResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelCpGamePhaseResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelCpGamePhaseResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelCpGamePhaseResp.Merge(dst, src)
}
func (m *GetChannelCpGamePhaseResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelCpGamePhaseResp.Size(m)
}
func (m *GetChannelCpGamePhaseResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelCpGamePhaseResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelCpGamePhaseResp proto.InternalMessageInfo

func (m *GetChannelCpGamePhaseResp) GetPhase() uint32 {
	if m != nil {
		return m.Phase
	}
	return 0
}

// 设置相亲游戏的阶段
type SetChannelCpGamePhaseReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Phase                uint32   `protobuf:"varint,2,opt,name=phase,proto3" json:"phase,omitempty"`
	OpUid                uint32   `protobuf:"varint,3,opt,name=op_uid,json=opUid,proto3" json:"op_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetChannelCpGamePhaseReq) Reset()         { *m = SetChannelCpGamePhaseReq{} }
func (m *SetChannelCpGamePhaseReq) String() string { return proto.CompactTextString(m) }
func (*SetChannelCpGamePhaseReq) ProtoMessage()    {}
func (*SetChannelCpGamePhaseReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_cp_game_1310697ea80348e6, []int{4}
}
func (m *SetChannelCpGamePhaseReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelCpGamePhaseReq.Unmarshal(m, b)
}
func (m *SetChannelCpGamePhaseReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelCpGamePhaseReq.Marshal(b, m, deterministic)
}
func (dst *SetChannelCpGamePhaseReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelCpGamePhaseReq.Merge(dst, src)
}
func (m *SetChannelCpGamePhaseReq) XXX_Size() int {
	return xxx_messageInfo_SetChannelCpGamePhaseReq.Size(m)
}
func (m *SetChannelCpGamePhaseReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelCpGamePhaseReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelCpGamePhaseReq proto.InternalMessageInfo

func (m *SetChannelCpGamePhaseReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetChannelCpGamePhaseReq) GetPhase() uint32 {
	if m != nil {
		return m.Phase
	}
	return 0
}

func (m *SetChannelCpGamePhaseReq) GetOpUid() uint32 {
	if m != nil {
		return m.OpUid
	}
	return 0
}

type SetChannelCpGamePhaseResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetChannelCpGamePhaseResp) Reset()         { *m = SetChannelCpGamePhaseResp{} }
func (m *SetChannelCpGamePhaseResp) String() string { return proto.CompactTextString(m) }
func (*SetChannelCpGamePhaseResp) ProtoMessage()    {}
func (*SetChannelCpGamePhaseResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_cp_game_1310697ea80348e6, []int{5}
}
func (m *SetChannelCpGamePhaseResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelCpGamePhaseResp.Unmarshal(m, b)
}
func (m *SetChannelCpGamePhaseResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelCpGamePhaseResp.Marshal(b, m, deterministic)
}
func (dst *SetChannelCpGamePhaseResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelCpGamePhaseResp.Merge(dst, src)
}
func (m *SetChannelCpGamePhaseResp) XXX_Size() int {
	return xxx_messageInfo_SetChannelCpGamePhaseResp.Size(m)
}
func (m *SetChannelCpGamePhaseResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelCpGamePhaseResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelCpGamePhaseResp proto.InternalMessageInfo

// 延长相亲游戏的阶段时长（加时）
type AddCpGamePhaseEndTimeReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Phase                uint32   `protobuf:"varint,2,opt,name=phase,proto3" json:"phase,omitempty"`
	AddTimeSec           uint32   `protobuf:"varint,3,opt,name=add_time_sec,json=addTimeSec,proto3" json:"add_time_sec,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddCpGamePhaseEndTimeReq) Reset()         { *m = AddCpGamePhaseEndTimeReq{} }
func (m *AddCpGamePhaseEndTimeReq) String() string { return proto.CompactTextString(m) }
func (*AddCpGamePhaseEndTimeReq) ProtoMessage()    {}
func (*AddCpGamePhaseEndTimeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_cp_game_1310697ea80348e6, []int{6}
}
func (m *AddCpGamePhaseEndTimeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddCpGamePhaseEndTimeReq.Unmarshal(m, b)
}
func (m *AddCpGamePhaseEndTimeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddCpGamePhaseEndTimeReq.Marshal(b, m, deterministic)
}
func (dst *AddCpGamePhaseEndTimeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddCpGamePhaseEndTimeReq.Merge(dst, src)
}
func (m *AddCpGamePhaseEndTimeReq) XXX_Size() int {
	return xxx_messageInfo_AddCpGamePhaseEndTimeReq.Size(m)
}
func (m *AddCpGamePhaseEndTimeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddCpGamePhaseEndTimeReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddCpGamePhaseEndTimeReq proto.InternalMessageInfo

func (m *AddCpGamePhaseEndTimeReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *AddCpGamePhaseEndTimeReq) GetPhase() uint32 {
	if m != nil {
		return m.Phase
	}
	return 0
}

func (m *AddCpGamePhaseEndTimeReq) GetAddTimeSec() uint32 {
	if m != nil {
		return m.AddTimeSec
	}
	return 0
}

type AddCpGamePhaseEndTimeResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddCpGamePhaseEndTimeResp) Reset()         { *m = AddCpGamePhaseEndTimeResp{} }
func (m *AddCpGamePhaseEndTimeResp) String() string { return proto.CompactTextString(m) }
func (*AddCpGamePhaseEndTimeResp) ProtoMessage()    {}
func (*AddCpGamePhaseEndTimeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_cp_game_1310697ea80348e6, []int{7}
}
func (m *AddCpGamePhaseEndTimeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddCpGamePhaseEndTimeResp.Unmarshal(m, b)
}
func (m *AddCpGamePhaseEndTimeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddCpGamePhaseEndTimeResp.Marshal(b, m, deterministic)
}
func (dst *AddCpGamePhaseEndTimeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddCpGamePhaseEndTimeResp.Merge(dst, src)
}
func (m *AddCpGamePhaseEndTimeResp) XXX_Size() int {
	return xxx_messageInfo_AddCpGamePhaseEndTimeResp.Size(m)
}
func (m *AddCpGamePhaseEndTimeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddCpGamePhaseEndTimeResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddCpGamePhaseEndTimeResp proto.InternalMessageInfo

// cp成员信息
type CpMemberInfo struct {
	Uid uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// bool is_mvp = 2;
	MicId                uint32   `protobuf:"varint,2,opt,name=mic_id,json=micId,proto3" json:"mic_id,omitempty"`
	PersonalScore        uint32   `protobuf:"varint,3,opt,name=personal_score,json=personalScore,proto3" json:"personal_score,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CpMemberInfo) Reset()         { *m = CpMemberInfo{} }
func (m *CpMemberInfo) String() string { return proto.CompactTextString(m) }
func (*CpMemberInfo) ProtoMessage()    {}
func (*CpMemberInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_cp_game_1310697ea80348e6, []int{8}
}
func (m *CpMemberInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CpMemberInfo.Unmarshal(m, b)
}
func (m *CpMemberInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CpMemberInfo.Marshal(b, m, deterministic)
}
func (dst *CpMemberInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CpMemberInfo.Merge(dst, src)
}
func (m *CpMemberInfo) XXX_Size() int {
	return xxx_messageInfo_CpMemberInfo.Size(m)
}
func (m *CpMemberInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_CpMemberInfo.DiscardUnknown(m)
}

var xxx_messageInfo_CpMemberInfo proto.InternalMessageInfo

func (m *CpMemberInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CpMemberInfo) GetMicId() uint32 {
	if m != nil {
		return m.MicId
	}
	return 0
}

func (m *CpMemberInfo) GetPersonalScore() uint32 {
	if m != nil {
		return m.PersonalScore
	}
	return 0
}

// 稀缺关系天数对应的动画资源
type RareAnimation struct {
	Url                  string   `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	Md5                  string   `protobuf:"bytes,2,opt,name=md5,proto3" json:"md5,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RareAnimation) Reset()         { *m = RareAnimation{} }
func (m *RareAnimation) String() string { return proto.CompactTextString(m) }
func (*RareAnimation) ProtoMessage()    {}
func (*RareAnimation) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_cp_game_1310697ea80348e6, []int{9}
}
func (m *RareAnimation) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RareAnimation.Unmarshal(m, b)
}
func (m *RareAnimation) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RareAnimation.Marshal(b, m, deterministic)
}
func (dst *RareAnimation) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RareAnimation.Merge(dst, src)
}
func (m *RareAnimation) XXX_Size() int {
	return xxx_messageInfo_RareAnimation.Size(m)
}
func (m *RareAnimation) XXX_DiscardUnknown() {
	xxx_messageInfo_RareAnimation.DiscardUnknown(m)
}

var xxx_messageInfo_RareAnimation proto.InternalMessageInfo

func (m *RareAnimation) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *RareAnimation) GetMd5() string {
	if m != nil {
		return m.Md5
	}
	return ""
}

// cp队伍信息
type CpTeamInfo struct {
	TeamId               string           `protobuf:"bytes,1,opt,name=team_id,json=teamId,proto3" json:"team_id,omitempty"`
	MemList              []*CpMemberInfo  `protobuf:"bytes,2,rep,name=mem_list,json=memList,proto3" json:"mem_list,omitempty"`
	Score                uint32           `protobuf:"varint,3,opt,name=score,proto3" json:"score,omitempty"`
	IsDangerous          bool             `protobuf:"varint,4,opt,name=is_dangerous,json=isDangerous,proto3" json:"is_dangerous,omitempty"`
	WingLevel            uint32           `protobuf:"varint,5,opt,name=wing_level,json=wingLevel,proto3" json:"wing_level,omitempty"`
	ToNextLevelDesc      string           `protobuf:"bytes,6,opt,name=to_next_level_desc,json=toNextLevelDesc,proto3" json:"to_next_level_desc,omitempty"`
	ScoreUpdateAt        uint32           `protobuf:"varint,7,opt,name=score_update_at,json=scoreUpdateAt,proto3" json:"score_update_at,omitempty"`
	CpNameplateInfo      *CpNameplateInfo `protobuf:"bytes,8,opt,name=cp_nameplate_info,json=cpNameplateInfo,proto3" json:"cp_nameplate_info,omitempty"`
	NextCpDesc           string           `protobuf:"bytes,9,opt,name=next_cp_desc,json=nextCpDesc,proto3" json:"next_cp_desc,omitempty"`
	RareAnimation        *RareAnimation   `protobuf:"bytes,10,opt,name=rare_animation,json=rareAnimation,proto3" json:"rare_animation,omitempty"`
	ReachRareDesc        string           `protobuf:"bytes,11,opt,name=reach_rare_desc,json=reachRareDesc,proto3" json:"reach_rare_desc,omitempty"`
	RareBgUrl            string           `protobuf:"bytes,12,opt,name=rare_bg_url,json=rareBgUrl,proto3" json:"rare_bg_url,omitempty"`
	RareDay              uint32           `protobuf:"varint,13,opt,name=rare_day,json=rareDay,proto3" json:"rare_day,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *CpTeamInfo) Reset()         { *m = CpTeamInfo{} }
func (m *CpTeamInfo) String() string { return proto.CompactTextString(m) }
func (*CpTeamInfo) ProtoMessage()    {}
func (*CpTeamInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_cp_game_1310697ea80348e6, []int{10}
}
func (m *CpTeamInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CpTeamInfo.Unmarshal(m, b)
}
func (m *CpTeamInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CpTeamInfo.Marshal(b, m, deterministic)
}
func (dst *CpTeamInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CpTeamInfo.Merge(dst, src)
}
func (m *CpTeamInfo) XXX_Size() int {
	return xxx_messageInfo_CpTeamInfo.Size(m)
}
func (m *CpTeamInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_CpTeamInfo.DiscardUnknown(m)
}

var xxx_messageInfo_CpTeamInfo proto.InternalMessageInfo

func (m *CpTeamInfo) GetTeamId() string {
	if m != nil {
		return m.TeamId
	}
	return ""
}

func (m *CpTeamInfo) GetMemList() []*CpMemberInfo {
	if m != nil {
		return m.MemList
	}
	return nil
}

func (m *CpTeamInfo) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *CpTeamInfo) GetIsDangerous() bool {
	if m != nil {
		return m.IsDangerous
	}
	return false
}

func (m *CpTeamInfo) GetWingLevel() uint32 {
	if m != nil {
		return m.WingLevel
	}
	return 0
}

func (m *CpTeamInfo) GetToNextLevelDesc() string {
	if m != nil {
		return m.ToNextLevelDesc
	}
	return ""
}

func (m *CpTeamInfo) GetScoreUpdateAt() uint32 {
	if m != nil {
		return m.ScoreUpdateAt
	}
	return 0
}

func (m *CpTeamInfo) GetCpNameplateInfo() *CpNameplateInfo {
	if m != nil {
		return m.CpNameplateInfo
	}
	return nil
}

func (m *CpTeamInfo) GetNextCpDesc() string {
	if m != nil {
		return m.NextCpDesc
	}
	return ""
}

func (m *CpTeamInfo) GetRareAnimation() *RareAnimation {
	if m != nil {
		return m.RareAnimation
	}
	return nil
}

func (m *CpTeamInfo) GetReachRareDesc() string {
	if m != nil {
		return m.ReachRareDesc
	}
	return ""
}

func (m *CpTeamInfo) GetRareBgUrl() string {
	if m != nil {
		return m.RareBgUrl
	}
	return ""
}

func (m *CpTeamInfo) GetRareDay() uint32 {
	if m != nil {
		return m.RareDay
	}
	return 0
}

type LoseInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	IconUrl              string   `protobuf:"bytes,2,opt,name=icon_url,json=iconUrl,proto3" json:"icon_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LoseInfo) Reset()         { *m = LoseInfo{} }
func (m *LoseInfo) String() string { return proto.CompactTextString(m) }
func (*LoseInfo) ProtoMessage()    {}
func (*LoseInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_cp_game_1310697ea80348e6, []int{11}
}
func (m *LoseInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LoseInfo.Unmarshal(m, b)
}
func (m *LoseInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LoseInfo.Marshal(b, m, deterministic)
}
func (dst *LoseInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LoseInfo.Merge(dst, src)
}
func (m *LoseInfo) XXX_Size() int {
	return xxx_messageInfo_LoseInfo.Size(m)
}
func (m *LoseInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_LoseInfo.DiscardUnknown(m)
}

var xxx_messageInfo_LoseInfo proto.InternalMessageInfo

func (m *LoseInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *LoseInfo) GetIconUrl() string {
	if m != nil {
		return m.IconUrl
	}
	return ""
}

type RareTeamInfo struct {
	TeamId               string          `protobuf:"bytes,1,opt,name=team_id,json=teamId,proto3" json:"team_id,omitempty"`
	Day                  uint32          `protobuf:"varint,2,opt,name=day,proto3" json:"day,omitempty"`
	ChoseStatus          uint32          `protobuf:"varint,3,opt,name=chose_status,json=choseStatus,proto3" json:"chose_status,omitempty"`
	Score                uint32          `protobuf:"varint,4,opt,name=score,proto3" json:"score,omitempty"`
	MemList              []*CpMemberInfo `protobuf:"bytes,5,rep,name=mem_list,json=memList,proto3" json:"mem_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *RareTeamInfo) Reset()         { *m = RareTeamInfo{} }
func (m *RareTeamInfo) String() string { return proto.CompactTextString(m) }
func (*RareTeamInfo) ProtoMessage()    {}
func (*RareTeamInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_cp_game_1310697ea80348e6, []int{12}
}
func (m *RareTeamInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RareTeamInfo.Unmarshal(m, b)
}
func (m *RareTeamInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RareTeamInfo.Marshal(b, m, deterministic)
}
func (dst *RareTeamInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RareTeamInfo.Merge(dst, src)
}
func (m *RareTeamInfo) XXX_Size() int {
	return xxx_messageInfo_RareTeamInfo.Size(m)
}
func (m *RareTeamInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_RareTeamInfo.DiscardUnknown(m)
}

var xxx_messageInfo_RareTeamInfo proto.InternalMessageInfo

func (m *RareTeamInfo) GetTeamId() string {
	if m != nil {
		return m.TeamId
	}
	return ""
}

func (m *RareTeamInfo) GetDay() uint32 {
	if m != nil {
		return m.Day
	}
	return 0
}

func (m *RareTeamInfo) GetChoseStatus() uint32 {
	if m != nil {
		return m.ChoseStatus
	}
	return 0
}

func (m *RareTeamInfo) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *RareTeamInfo) GetMemList() []*CpMemberInfo {
	if m != nil {
		return m.MemList
	}
	return nil
}

// cp游戏信息
type CpGameInfo struct {
	ChannelId              uint32             `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Phase                  uint32             `protobuf:"varint,2,opt,name=phase,proto3" json:"phase,omitempty"`
	PhaseEndTs             uint32             `protobuf:"varint,3,opt,name=phase_end_ts,json=phaseEndTs,proto3" json:"phase_end_ts,omitempty"`
	CpList                 []*CpTeamInfo      `protobuf:"bytes,4,rep,name=cp_list,json=cpList,proto3" json:"cp_list,omitempty"`
	ServerTimeSec          uint32             `protobuf:"varint,5,opt,name=server_time_sec,json=serverTimeSec,proto3" json:"server_time_sec,omitempty"`
	RestAddTimeSec         uint32             `protobuf:"varint,6,opt,name=rest_add_time_sec,json=restAddTimeSec,proto3" json:"rest_add_time_sec,omitempty"`
	AddTimeSecOptionList   []uint32           `protobuf:"varint,7,rep,packed,name=add_time_sec_option_list,json=addTimeSecOptionList,proto3" json:"add_time_sec_option_list,omitempty"`
	MvpUid                 uint32             `protobuf:"varint,8,opt,name=mvp_uid,json=mvpUid,proto3" json:"mvp_uid,omitempty"`
	GodRankTop             *CpScoreInfo       `protobuf:"bytes,9,opt,name=god_rank_top,json=godRankTop,proto3" json:"god_rank_top,omitempty"`
	GameId                 uint32             `protobuf:"varint,10,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	CharmMvpUid            uint32             `protobuf:"varint,11,opt,name=charm_mvp_uid,json=charmMvpUid,proto3" json:"charm_mvp_uid,omitempty"`
	RestAddCnt             uint32             `protobuf:"varint,12,opt,name=rest_add_cnt,json=restAddCnt,proto3" json:"rest_add_cnt,omitempty"`
	PkSubPhase             uint32             `protobuf:"varint,13,opt,name=pk_sub_phase,json=pkSubPhase,proto3" json:"pk_sub_phase,omitempty"`
	LoseList               []*LoseInfo        `protobuf:"bytes,14,rep,name=lose_list,json=loseList,proto3" json:"lose_list,omitempty"`
	MvpCharmVal            uint32             `protobuf:"varint,15,opt,name=mvp_charm_val,json=mvpCharmVal,proto3" json:"mvp_charm_val,omitempty"`
	MvpMic2Uid             uint32             `protobuf:"varint,16,opt,name=mvp_mic2uid,json=mvpMic2uid,proto3" json:"mvp_mic2uid,omitempty"`
	RareList               []*RareTeamInfo    `protobuf:"bytes,17,rep,name=rare_list,json=rareList,proto3" json:"rare_list,omitempty"`
	AnimationBoundaryValue uint32             `protobuf:"varint,18,opt,name=animation_boundary_value,json=animationBoundaryValue,proto3" json:"animation_boundary_value,omitempty"`
	CpChoseRareResult      *CpChoseRareResult `protobuf:"bytes,19,opt,name=cp_chose_rare_result,json=cpChoseRareResult,proto3" json:"cp_chose_rare_result,omitempty"`
	XXX_NoUnkeyedLiteral   struct{}           `json:"-"`
	XXX_unrecognized       []byte             `json:"-"`
	XXX_sizecache          int32              `json:"-"`
}

func (m *CpGameInfo) Reset()         { *m = CpGameInfo{} }
func (m *CpGameInfo) String() string { return proto.CompactTextString(m) }
func (*CpGameInfo) ProtoMessage()    {}
func (*CpGameInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_cp_game_1310697ea80348e6, []int{13}
}
func (m *CpGameInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CpGameInfo.Unmarshal(m, b)
}
func (m *CpGameInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CpGameInfo.Marshal(b, m, deterministic)
}
func (dst *CpGameInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CpGameInfo.Merge(dst, src)
}
func (m *CpGameInfo) XXX_Size() int {
	return xxx_messageInfo_CpGameInfo.Size(m)
}
func (m *CpGameInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_CpGameInfo.DiscardUnknown(m)
}

var xxx_messageInfo_CpGameInfo proto.InternalMessageInfo

func (m *CpGameInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *CpGameInfo) GetPhase() uint32 {
	if m != nil {
		return m.Phase
	}
	return 0
}

func (m *CpGameInfo) GetPhaseEndTs() uint32 {
	if m != nil {
		return m.PhaseEndTs
	}
	return 0
}

func (m *CpGameInfo) GetCpList() []*CpTeamInfo {
	if m != nil {
		return m.CpList
	}
	return nil
}

func (m *CpGameInfo) GetServerTimeSec() uint32 {
	if m != nil {
		return m.ServerTimeSec
	}
	return 0
}

func (m *CpGameInfo) GetRestAddTimeSec() uint32 {
	if m != nil {
		return m.RestAddTimeSec
	}
	return 0
}

func (m *CpGameInfo) GetAddTimeSecOptionList() []uint32 {
	if m != nil {
		return m.AddTimeSecOptionList
	}
	return nil
}

func (m *CpGameInfo) GetMvpUid() uint32 {
	if m != nil {
		return m.MvpUid
	}
	return 0
}

func (m *CpGameInfo) GetGodRankTop() *CpScoreInfo {
	if m != nil {
		return m.GodRankTop
	}
	return nil
}

func (m *CpGameInfo) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *CpGameInfo) GetCharmMvpUid() uint32 {
	if m != nil {
		return m.CharmMvpUid
	}
	return 0
}

func (m *CpGameInfo) GetRestAddCnt() uint32 {
	if m != nil {
		return m.RestAddCnt
	}
	return 0
}

func (m *CpGameInfo) GetPkSubPhase() uint32 {
	if m != nil {
		return m.PkSubPhase
	}
	return 0
}

func (m *CpGameInfo) GetLoseList() []*LoseInfo {
	if m != nil {
		return m.LoseList
	}
	return nil
}

func (m *CpGameInfo) GetMvpCharmVal() uint32 {
	if m != nil {
		return m.MvpCharmVal
	}
	return 0
}

func (m *CpGameInfo) GetMvpMic2Uid() uint32 {
	if m != nil {
		return m.MvpMic2Uid
	}
	return 0
}

func (m *CpGameInfo) GetRareList() []*RareTeamInfo {
	if m != nil {
		return m.RareList
	}
	return nil
}

func (m *CpGameInfo) GetAnimationBoundaryValue() uint32 {
	if m != nil {
		return m.AnimationBoundaryValue
	}
	return 0
}

func (m *CpGameInfo) GetCpChoseRareResult() *CpChoseRareResult {
	if m != nil {
		return m.CpChoseRareResult
	}
	return nil
}

// 获取当前cp游戏信息
type GetCurrCpGameInfoReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCurrCpGameInfoReq) Reset()         { *m = GetCurrCpGameInfoReq{} }
func (m *GetCurrCpGameInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetCurrCpGameInfoReq) ProtoMessage()    {}
func (*GetCurrCpGameInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_cp_game_1310697ea80348e6, []int{14}
}
func (m *GetCurrCpGameInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCurrCpGameInfoReq.Unmarshal(m, b)
}
func (m *GetCurrCpGameInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCurrCpGameInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetCurrCpGameInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCurrCpGameInfoReq.Merge(dst, src)
}
func (m *GetCurrCpGameInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetCurrCpGameInfoReq.Size(m)
}
func (m *GetCurrCpGameInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCurrCpGameInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetCurrCpGameInfoReq proto.InternalMessageInfo

func (m *GetCurrCpGameInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetCurrCpGameInfoResp struct {
	CpGameInfo           *CpGameInfo `protobuf:"bytes,1,opt,name=cp_game_info,json=cpGameInfo,proto3" json:"cp_game_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetCurrCpGameInfoResp) Reset()         { *m = GetCurrCpGameInfoResp{} }
func (m *GetCurrCpGameInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetCurrCpGameInfoResp) ProtoMessage()    {}
func (*GetCurrCpGameInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_cp_game_1310697ea80348e6, []int{15}
}
func (m *GetCurrCpGameInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCurrCpGameInfoResp.Unmarshal(m, b)
}
func (m *GetCurrCpGameInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCurrCpGameInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetCurrCpGameInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCurrCpGameInfoResp.Merge(dst, src)
}
func (m *GetCurrCpGameInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetCurrCpGameInfoResp.Size(m)
}
func (m *GetCurrCpGameInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCurrCpGameInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetCurrCpGameInfoResp proto.InternalMessageInfo

func (m *GetCurrCpGameInfoResp) GetCpGameInfo() *CpGameInfo {
	if m != nil {
		return m.CpGameInfo
	}
	return nil
}

type ChannelCpGameEntry struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Level                uint32   `protobuf:"varint,2,opt,name=level,proto3" json:"level,omitempty"`
	UpdateAt             uint32   `protobuf:"varint,3,opt,name=update_at,json=updateAt,proto3" json:"update_at,omitempty"`
	Operator             string   `protobuf:"bytes,4,opt,name=operator,proto3" json:"operator,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelCpGameEntry) Reset()         { *m = ChannelCpGameEntry{} }
func (m *ChannelCpGameEntry) String() string { return proto.CompactTextString(m) }
func (*ChannelCpGameEntry) ProtoMessage()    {}
func (*ChannelCpGameEntry) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_cp_game_1310697ea80348e6, []int{16}
}
func (m *ChannelCpGameEntry) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelCpGameEntry.Unmarshal(m, b)
}
func (m *ChannelCpGameEntry) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelCpGameEntry.Marshal(b, m, deterministic)
}
func (dst *ChannelCpGameEntry) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelCpGameEntry.Merge(dst, src)
}
func (m *ChannelCpGameEntry) XXX_Size() int {
	return xxx_messageInfo_ChannelCpGameEntry.Size(m)
}
func (m *ChannelCpGameEntry) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelCpGameEntry.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelCpGameEntry proto.InternalMessageInfo

func (m *ChannelCpGameEntry) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelCpGameEntry) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *ChannelCpGameEntry) GetUpdateAt() uint32 {
	if m != nil {
		return m.UpdateAt
	}
	return 0
}

func (m *ChannelCpGameEntry) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

type SetChannelCpGameEntryLevelReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Level                uint32   `protobuf:"varint,2,opt,name=level,proto3" json:"level,omitempty"`
	Operator             string   `protobuf:"bytes,3,opt,name=operator,proto3" json:"operator,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetChannelCpGameEntryLevelReq) Reset()         { *m = SetChannelCpGameEntryLevelReq{} }
func (m *SetChannelCpGameEntryLevelReq) String() string { return proto.CompactTextString(m) }
func (*SetChannelCpGameEntryLevelReq) ProtoMessage()    {}
func (*SetChannelCpGameEntryLevelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_cp_game_1310697ea80348e6, []int{17}
}
func (m *SetChannelCpGameEntryLevelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelCpGameEntryLevelReq.Unmarshal(m, b)
}
func (m *SetChannelCpGameEntryLevelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelCpGameEntryLevelReq.Marshal(b, m, deterministic)
}
func (dst *SetChannelCpGameEntryLevelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelCpGameEntryLevelReq.Merge(dst, src)
}
func (m *SetChannelCpGameEntryLevelReq) XXX_Size() int {
	return xxx_messageInfo_SetChannelCpGameEntryLevelReq.Size(m)
}
func (m *SetChannelCpGameEntryLevelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelCpGameEntryLevelReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelCpGameEntryLevelReq proto.InternalMessageInfo

func (m *SetChannelCpGameEntryLevelReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetChannelCpGameEntryLevelReq) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *SetChannelCpGameEntryLevelReq) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

type SetChannelCpGameEntryLevelResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetChannelCpGameEntryLevelResp) Reset()         { *m = SetChannelCpGameEntryLevelResp{} }
func (m *SetChannelCpGameEntryLevelResp) String() string { return proto.CompactTextString(m) }
func (*SetChannelCpGameEntryLevelResp) ProtoMessage()    {}
func (*SetChannelCpGameEntryLevelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_cp_game_1310697ea80348e6, []int{18}
}
func (m *SetChannelCpGameEntryLevelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelCpGameEntryLevelResp.Unmarshal(m, b)
}
func (m *SetChannelCpGameEntryLevelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelCpGameEntryLevelResp.Marshal(b, m, deterministic)
}
func (dst *SetChannelCpGameEntryLevelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelCpGameEntryLevelResp.Merge(dst, src)
}
func (m *SetChannelCpGameEntryLevelResp) XXX_Size() int {
	return xxx_messageInfo_SetChannelCpGameEntryLevelResp.Size(m)
}
func (m *SetChannelCpGameEntryLevelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelCpGameEntryLevelResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelCpGameEntryLevelResp proto.InternalMessageInfo

type GetChannelCpGameEntryLevelReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelCpGameEntryLevelReq) Reset()         { *m = GetChannelCpGameEntryLevelReq{} }
func (m *GetChannelCpGameEntryLevelReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelCpGameEntryLevelReq) ProtoMessage()    {}
func (*GetChannelCpGameEntryLevelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_cp_game_1310697ea80348e6, []int{19}
}
func (m *GetChannelCpGameEntryLevelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelCpGameEntryLevelReq.Unmarshal(m, b)
}
func (m *GetChannelCpGameEntryLevelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelCpGameEntryLevelReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelCpGameEntryLevelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelCpGameEntryLevelReq.Merge(dst, src)
}
func (m *GetChannelCpGameEntryLevelReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelCpGameEntryLevelReq.Size(m)
}
func (m *GetChannelCpGameEntryLevelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelCpGameEntryLevelReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelCpGameEntryLevelReq proto.InternalMessageInfo

func (m *GetChannelCpGameEntryLevelReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetChannelCpGameEntryLevelResp struct {
	Level                uint32   `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelCpGameEntryLevelResp) Reset()         { *m = GetChannelCpGameEntryLevelResp{} }
func (m *GetChannelCpGameEntryLevelResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelCpGameEntryLevelResp) ProtoMessage()    {}
func (*GetChannelCpGameEntryLevelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_cp_game_1310697ea80348e6, []int{20}
}
func (m *GetChannelCpGameEntryLevelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelCpGameEntryLevelResp.Unmarshal(m, b)
}
func (m *GetChannelCpGameEntryLevelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelCpGameEntryLevelResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelCpGameEntryLevelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelCpGameEntryLevelResp.Merge(dst, src)
}
func (m *GetChannelCpGameEntryLevelResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelCpGameEntryLevelResp.Size(m)
}
func (m *GetChannelCpGameEntryLevelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelCpGameEntryLevelResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelCpGameEntryLevelResp proto.InternalMessageInfo

func (m *GetChannelCpGameEntryLevelResp) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

type BatchGetCpGameEntryReq struct {
	Level                uint32   `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"`
	Begin                uint32   `protobuf:"varint,2,opt,name=begin,proto3" json:"begin,omitempty"`
	Limit                uint32   `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetCpGameEntryReq) Reset()         { *m = BatchGetCpGameEntryReq{} }
func (m *BatchGetCpGameEntryReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetCpGameEntryReq) ProtoMessage()    {}
func (*BatchGetCpGameEntryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_cp_game_1310697ea80348e6, []int{21}
}
func (m *BatchGetCpGameEntryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetCpGameEntryReq.Unmarshal(m, b)
}
func (m *BatchGetCpGameEntryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetCpGameEntryReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetCpGameEntryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetCpGameEntryReq.Merge(dst, src)
}
func (m *BatchGetCpGameEntryReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetCpGameEntryReq.Size(m)
}
func (m *BatchGetCpGameEntryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetCpGameEntryReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetCpGameEntryReq proto.InternalMessageInfo

func (m *BatchGetCpGameEntryReq) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *BatchGetCpGameEntryReq) GetBegin() uint32 {
	if m != nil {
		return m.Begin
	}
	return 0
}

func (m *BatchGetCpGameEntryReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type BatchGetCpGameEntryResp struct {
	List                 []*ChannelCpGameEntry `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Total                uint32                `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *BatchGetCpGameEntryResp) Reset()         { *m = BatchGetCpGameEntryResp{} }
func (m *BatchGetCpGameEntryResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetCpGameEntryResp) ProtoMessage()    {}
func (*BatchGetCpGameEntryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_cp_game_1310697ea80348e6, []int{22}
}
func (m *BatchGetCpGameEntryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetCpGameEntryResp.Unmarshal(m, b)
}
func (m *BatchGetCpGameEntryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetCpGameEntryResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetCpGameEntryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetCpGameEntryResp.Merge(dst, src)
}
func (m *BatchGetCpGameEntryResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetCpGameEntryResp.Size(m)
}
func (m *BatchGetCpGameEntryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetCpGameEntryResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetCpGameEntryResp proto.InternalMessageInfo

func (m *BatchGetCpGameEntryResp) GetList() []*ChannelCpGameEntry {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *BatchGetCpGameEntryResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type CpCardResource struct {
	ChannelResourceUrl   string   `protobuf:"bytes,1,opt,name=channel_resource_url,json=channelResourceUrl,proto3" json:"channel_resource_url,omitempty"`
	Md5Sum               string   `protobuf:"bytes,2,opt,name=md5sum,proto3" json:"md5sum,omitempty"`
	ImPicSmallUrl        string   `protobuf:"bytes,3,opt,name=im_pic_small_url,json=imPicSmallUrl,proto3" json:"im_pic_small_url,omitempty"`
	ImPicUrl             string   `protobuf:"bytes,4,opt,name=im_pic_url,json=imPicUrl,proto3" json:"im_pic_url,omitempty"`
	RankBgUrl            string   `protobuf:"bytes,5,opt,name=rank_bg_url,json=rankBgUrl,proto3" json:"rank_bg_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CpCardResource) Reset()         { *m = CpCardResource{} }
func (m *CpCardResource) String() string { return proto.CompactTextString(m) }
func (*CpCardResource) ProtoMessage()    {}
func (*CpCardResource) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_cp_game_1310697ea80348e6, []int{23}
}
func (m *CpCardResource) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CpCardResource.Unmarshal(m, b)
}
func (m *CpCardResource) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CpCardResource.Marshal(b, m, deterministic)
}
func (dst *CpCardResource) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CpCardResource.Merge(dst, src)
}
func (m *CpCardResource) XXX_Size() int {
	return xxx_messageInfo_CpCardResource.Size(m)
}
func (m *CpCardResource) XXX_DiscardUnknown() {
	xxx_messageInfo_CpCardResource.DiscardUnknown(m)
}

var xxx_messageInfo_CpCardResource proto.InternalMessageInfo

func (m *CpCardResource) GetChannelResourceUrl() string {
	if m != nil {
		return m.ChannelResourceUrl
	}
	return ""
}

func (m *CpCardResource) GetMd5Sum() string {
	if m != nil {
		return m.Md5Sum
	}
	return ""
}

func (m *CpCardResource) GetImPicSmallUrl() string {
	if m != nil {
		return m.ImPicSmallUrl
	}
	return ""
}

func (m *CpCardResource) GetImPicUrl() string {
	if m != nil {
		return m.ImPicUrl
	}
	return ""
}

func (m *CpCardResource) GetRankBgUrl() string {
	if m != nil {
		return m.RankBgUrl
	}
	return ""
}

type CpCardConf struct {
	Id                   uint32            `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string            `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	MinScore             uint32            `protobuf:"varint,3,opt,name=min_score,json=minScore,proto3" json:"min_score,omitempty"`
	UpdateAt             uint32            `protobuf:"varint,4,opt,name=update_at,json=updateAt,proto3" json:"update_at,omitempty"`
	Operator             string            `protobuf:"bytes,5,opt,name=operator,proto3" json:"operator,omitempty"`
	ResourceList         []*CpCardResource `protobuf:"bytes,6,rep,name=resource_list,json=resourceList,proto3" json:"resource_list,omitempty"`
	ResourceTotalUrl     string            `protobuf:"bytes,7,opt,name=resource_total_url,json=resourceTotalUrl,proto3" json:"resource_total_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *CpCardConf) Reset()         { *m = CpCardConf{} }
func (m *CpCardConf) String() string { return proto.CompactTextString(m) }
func (*CpCardConf) ProtoMessage()    {}
func (*CpCardConf) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_cp_game_1310697ea80348e6, []int{24}
}
func (m *CpCardConf) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CpCardConf.Unmarshal(m, b)
}
func (m *CpCardConf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CpCardConf.Marshal(b, m, deterministic)
}
func (dst *CpCardConf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CpCardConf.Merge(dst, src)
}
func (m *CpCardConf) XXX_Size() int {
	return xxx_messageInfo_CpCardConf.Size(m)
}
func (m *CpCardConf) XXX_DiscardUnknown() {
	xxx_messageInfo_CpCardConf.DiscardUnknown(m)
}

var xxx_messageInfo_CpCardConf proto.InternalMessageInfo

func (m *CpCardConf) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *CpCardConf) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *CpCardConf) GetMinScore() uint32 {
	if m != nil {
		return m.MinScore
	}
	return 0
}

func (m *CpCardConf) GetUpdateAt() uint32 {
	if m != nil {
		return m.UpdateAt
	}
	return 0
}

func (m *CpCardConf) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *CpCardConf) GetResourceList() []*CpCardResource {
	if m != nil {
		return m.ResourceList
	}
	return nil
}

func (m *CpCardConf) GetResourceTotalUrl() string {
	if m != nil {
		return m.ResourceTotalUrl
	}
	return ""
}

type AllCpCardConf struct {
	List                 []*CpCardConf `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *AllCpCardConf) Reset()         { *m = AllCpCardConf{} }
func (m *AllCpCardConf) String() string { return proto.CompactTextString(m) }
func (*AllCpCardConf) ProtoMessage()    {}
func (*AllCpCardConf) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_cp_game_1310697ea80348e6, []int{25}
}
func (m *AllCpCardConf) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AllCpCardConf.Unmarshal(m, b)
}
func (m *AllCpCardConf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AllCpCardConf.Marshal(b, m, deterministic)
}
func (dst *AllCpCardConf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AllCpCardConf.Merge(dst, src)
}
func (m *AllCpCardConf) XXX_Size() int {
	return xxx_messageInfo_AllCpCardConf.Size(m)
}
func (m *AllCpCardConf) XXX_DiscardUnknown() {
	xxx_messageInfo_AllCpCardConf.DiscardUnknown(m)
}

var xxx_messageInfo_AllCpCardConf proto.InternalMessageInfo

func (m *AllCpCardConf) GetList() []*CpCardConf {
	if m != nil {
		return m.List
	}
	return nil
}

type AddCpCardConfReq struct {
	Conf                 *CpCardConf `protobuf:"bytes,1,opt,name=conf,proto3" json:"conf,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *AddCpCardConfReq) Reset()         { *m = AddCpCardConfReq{} }
func (m *AddCpCardConfReq) String() string { return proto.CompactTextString(m) }
func (*AddCpCardConfReq) ProtoMessage()    {}
func (*AddCpCardConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_cp_game_1310697ea80348e6, []int{26}
}
func (m *AddCpCardConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddCpCardConfReq.Unmarshal(m, b)
}
func (m *AddCpCardConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddCpCardConfReq.Marshal(b, m, deterministic)
}
func (dst *AddCpCardConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddCpCardConfReq.Merge(dst, src)
}
func (m *AddCpCardConfReq) XXX_Size() int {
	return xxx_messageInfo_AddCpCardConfReq.Size(m)
}
func (m *AddCpCardConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddCpCardConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddCpCardConfReq proto.InternalMessageInfo

func (m *AddCpCardConfReq) GetConf() *CpCardConf {
	if m != nil {
		return m.Conf
	}
	return nil
}

type AddCpCardConfResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddCpCardConfResp) Reset()         { *m = AddCpCardConfResp{} }
func (m *AddCpCardConfResp) String() string { return proto.CompactTextString(m) }
func (*AddCpCardConfResp) ProtoMessage()    {}
func (*AddCpCardConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_cp_game_1310697ea80348e6, []int{27}
}
func (m *AddCpCardConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddCpCardConfResp.Unmarshal(m, b)
}
func (m *AddCpCardConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddCpCardConfResp.Marshal(b, m, deterministic)
}
func (dst *AddCpCardConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddCpCardConfResp.Merge(dst, src)
}
func (m *AddCpCardConfResp) XXX_Size() int {
	return xxx_messageInfo_AddCpCardConfResp.Size(m)
}
func (m *AddCpCardConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddCpCardConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddCpCardConfResp proto.InternalMessageInfo

type UpdateCpCardConfReq struct {
	Conf                 *CpCardConf `protobuf:"bytes,1,opt,name=conf,proto3" json:"conf,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *UpdateCpCardConfReq) Reset()         { *m = UpdateCpCardConfReq{} }
func (m *UpdateCpCardConfReq) String() string { return proto.CompactTextString(m) }
func (*UpdateCpCardConfReq) ProtoMessage()    {}
func (*UpdateCpCardConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_cp_game_1310697ea80348e6, []int{28}
}
func (m *UpdateCpCardConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateCpCardConfReq.Unmarshal(m, b)
}
func (m *UpdateCpCardConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateCpCardConfReq.Marshal(b, m, deterministic)
}
func (dst *UpdateCpCardConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateCpCardConfReq.Merge(dst, src)
}
func (m *UpdateCpCardConfReq) XXX_Size() int {
	return xxx_messageInfo_UpdateCpCardConfReq.Size(m)
}
func (m *UpdateCpCardConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateCpCardConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateCpCardConfReq proto.InternalMessageInfo

func (m *UpdateCpCardConfReq) GetConf() *CpCardConf {
	if m != nil {
		return m.Conf
	}
	return nil
}

type UpdateCpCardConfResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateCpCardConfResp) Reset()         { *m = UpdateCpCardConfResp{} }
func (m *UpdateCpCardConfResp) String() string { return proto.CompactTextString(m) }
func (*UpdateCpCardConfResp) ProtoMessage()    {}
func (*UpdateCpCardConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_cp_game_1310697ea80348e6, []int{29}
}
func (m *UpdateCpCardConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateCpCardConfResp.Unmarshal(m, b)
}
func (m *UpdateCpCardConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateCpCardConfResp.Marshal(b, m, deterministic)
}
func (dst *UpdateCpCardConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateCpCardConfResp.Merge(dst, src)
}
func (m *UpdateCpCardConfResp) XXX_Size() int {
	return xxx_messageInfo_UpdateCpCardConfResp.Size(m)
}
func (m *UpdateCpCardConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateCpCardConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateCpCardConfResp proto.InternalMessageInfo

type DelCpCardConfReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelCpCardConfReq) Reset()         { *m = DelCpCardConfReq{} }
func (m *DelCpCardConfReq) String() string { return proto.CompactTextString(m) }
func (*DelCpCardConfReq) ProtoMessage()    {}
func (*DelCpCardConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_cp_game_1310697ea80348e6, []int{30}
}
func (m *DelCpCardConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelCpCardConfReq.Unmarshal(m, b)
}
func (m *DelCpCardConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelCpCardConfReq.Marshal(b, m, deterministic)
}
func (dst *DelCpCardConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelCpCardConfReq.Merge(dst, src)
}
func (m *DelCpCardConfReq) XXX_Size() int {
	return xxx_messageInfo_DelCpCardConfReq.Size(m)
}
func (m *DelCpCardConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelCpCardConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelCpCardConfReq proto.InternalMessageInfo

func (m *DelCpCardConfReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type DelCpCardConfResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelCpCardConfResp) Reset()         { *m = DelCpCardConfResp{} }
func (m *DelCpCardConfResp) String() string { return proto.CompactTextString(m) }
func (*DelCpCardConfResp) ProtoMessage()    {}
func (*DelCpCardConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_cp_game_1310697ea80348e6, []int{31}
}
func (m *DelCpCardConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelCpCardConfResp.Unmarshal(m, b)
}
func (m *DelCpCardConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelCpCardConfResp.Marshal(b, m, deterministic)
}
func (dst *DelCpCardConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelCpCardConfResp.Merge(dst, src)
}
func (m *DelCpCardConfResp) XXX_Size() int {
	return xxx_messageInfo_DelCpCardConfResp.Size(m)
}
func (m *DelCpCardConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelCpCardConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelCpCardConfResp proto.InternalMessageInfo

type GetAllCpCardConfReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllCpCardConfReq) Reset()         { *m = GetAllCpCardConfReq{} }
func (m *GetAllCpCardConfReq) String() string { return proto.CompactTextString(m) }
func (*GetAllCpCardConfReq) ProtoMessage()    {}
func (*GetAllCpCardConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_cp_game_1310697ea80348e6, []int{32}
}
func (m *GetAllCpCardConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllCpCardConfReq.Unmarshal(m, b)
}
func (m *GetAllCpCardConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllCpCardConfReq.Marshal(b, m, deterministic)
}
func (dst *GetAllCpCardConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllCpCardConfReq.Merge(dst, src)
}
func (m *GetAllCpCardConfReq) XXX_Size() int {
	return xxx_messageInfo_GetAllCpCardConfReq.Size(m)
}
func (m *GetAllCpCardConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllCpCardConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllCpCardConfReq proto.InternalMessageInfo

type GetAllCpCardConfResp struct {
	List                 []*CpCardConf `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetAllCpCardConfResp) Reset()         { *m = GetAllCpCardConfResp{} }
func (m *GetAllCpCardConfResp) String() string { return proto.CompactTextString(m) }
func (*GetAllCpCardConfResp) ProtoMessage()    {}
func (*GetAllCpCardConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_cp_game_1310697ea80348e6, []int{33}
}
func (m *GetAllCpCardConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllCpCardConfResp.Unmarshal(m, b)
}
func (m *GetAllCpCardConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllCpCardConfResp.Marshal(b, m, deterministic)
}
func (dst *GetAllCpCardConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllCpCardConfResp.Merge(dst, src)
}
func (m *GetAllCpCardConfResp) XXX_Size() int {
	return xxx_messageInfo_GetAllCpCardConfResp.Size(m)
}
func (m *GetAllCpCardConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllCpCardConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllCpCardConfResp proto.InternalMessageInfo

func (m *GetAllCpCardConfResp) GetList() []*CpCardConf {
	if m != nil {
		return m.List
	}
	return nil
}

type UserProfile struct {
	Uid                  uint32         `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Account              string         `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	Nickname             string         `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`
	AccountAlias         string         `protobuf:"bytes,4,opt,name=account_alias,json=accountAlias,proto3" json:"account_alias,omitempty"`
	Sex                  uint32         `protobuf:"varint,5,opt,name=sex,proto3" json:"sex,omitempty"`
	Privilege            *UserPrivilege `protobuf:"bytes,6,opt,name=privilege,proto3" json:"privilege,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *UserProfile) Reset()         { *m = UserProfile{} }
func (m *UserProfile) String() string { return proto.CompactTextString(m) }
func (*UserProfile) ProtoMessage()    {}
func (*UserProfile) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_cp_game_1310697ea80348e6, []int{34}
}
func (m *UserProfile) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserProfile.Unmarshal(m, b)
}
func (m *UserProfile) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserProfile.Marshal(b, m, deterministic)
}
func (dst *UserProfile) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserProfile.Merge(dst, src)
}
func (m *UserProfile) XXX_Size() int {
	return xxx_messageInfo_UserProfile.Size(m)
}
func (m *UserProfile) XXX_DiscardUnknown() {
	xxx_messageInfo_UserProfile.DiscardUnknown(m)
}

var xxx_messageInfo_UserProfile proto.InternalMessageInfo

func (m *UserProfile) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserProfile) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *UserProfile) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *UserProfile) GetAccountAlias() string {
	if m != nil {
		return m.AccountAlias
	}
	return ""
}

func (m *UserProfile) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *UserProfile) GetPrivilege() *UserPrivilege {
	if m != nil {
		return m.Privilege
	}
	return nil
}

type UserPrivilege struct {
	Account              string   `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	Nickname             string   `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Type                 uint32   `protobuf:"varint,3,opt,name=type,proto3" json:"type,omitempty"`
	Options              []byte   `protobuf:"bytes,4,opt,name=options,proto3" json:"options,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserPrivilege) Reset()         { *m = UserPrivilege{} }
func (m *UserPrivilege) String() string { return proto.CompactTextString(m) }
func (*UserPrivilege) ProtoMessage()    {}
func (*UserPrivilege) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_cp_game_1310697ea80348e6, []int{35}
}
func (m *UserPrivilege) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserPrivilege.Unmarshal(m, b)
}
func (m *UserPrivilege) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserPrivilege.Marshal(b, m, deterministic)
}
func (dst *UserPrivilege) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserPrivilege.Merge(dst, src)
}
func (m *UserPrivilege) XXX_Size() int {
	return xxx_messageInfo_UserPrivilege.Size(m)
}
func (m *UserPrivilege) XXX_DiscardUnknown() {
	xxx_messageInfo_UserPrivilege.DiscardUnknown(m)
}

var xxx_messageInfo_UserPrivilege proto.InternalMessageInfo

func (m *UserPrivilege) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *UserPrivilege) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *UserPrivilege) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *UserPrivilege) GetOptions() []byte {
	if m != nil {
		return m.Options
	}
	return nil
}

type CpScoreInfo struct {
	ChannelId            uint32       `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	UidA                 uint32       `protobuf:"varint,2,opt,name=uid_a,json=uidA,proto3" json:"uid_a,omitempty"`
	UidB                 uint32       `protobuf:"varint,3,opt,name=uid_b,json=uidB,proto3" json:"uid_b,omitempty"`
	Score                uint32       `protobuf:"varint,4,opt,name=score,proto3" json:"score,omitempty"`
	CreateTs             uint32       `protobuf:"varint,5,opt,name=create_ts,json=createTs,proto3" json:"create_ts,omitempty"`
	BgUrl                string       `protobuf:"bytes,6,opt,name=bg_url,json=bgUrl,proto3" json:"bg_url,omitempty"`
	SendValueA           uint32       `protobuf:"varint,7,opt,name=send_value_a,json=sendValueA,proto3" json:"send_value_a,omitempty"`
	SendValueB           uint32       `protobuf:"varint,8,opt,name=send_value_b,json=sendValueB,proto3" json:"send_value_b,omitempty"`
	MvpUid               uint32       `protobuf:"varint,9,opt,name=mvp_uid,json=mvpUid,proto3" json:"mvp_uid,omitempty"`
	UserProfile          *UserProfile `protobuf:"bytes,10,opt,name=user_profile,json=userProfile,proto3" json:"user_profile,omitempty"`
	CpUserProfile        *UserProfile `protobuf:"bytes,11,opt,name=cp_user_profile,json=cpUserProfile,proto3" json:"cp_user_profile,omitempty"`
	UidAType             uint32       `protobuf:"varint,12,opt,name=uid_a_type,json=uidAType,proto3" json:"uid_a_type,omitempty"`
	UidBType             uint32       `protobuf:"varint,13,opt,name=uid_b_type,json=uidBType,proto3" json:"uid_b_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *CpScoreInfo) Reset()         { *m = CpScoreInfo{} }
func (m *CpScoreInfo) String() string { return proto.CompactTextString(m) }
func (*CpScoreInfo) ProtoMessage()    {}
func (*CpScoreInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_cp_game_1310697ea80348e6, []int{36}
}
func (m *CpScoreInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CpScoreInfo.Unmarshal(m, b)
}
func (m *CpScoreInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CpScoreInfo.Marshal(b, m, deterministic)
}
func (dst *CpScoreInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CpScoreInfo.Merge(dst, src)
}
func (m *CpScoreInfo) XXX_Size() int {
	return xxx_messageInfo_CpScoreInfo.Size(m)
}
func (m *CpScoreInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_CpScoreInfo.DiscardUnknown(m)
}

var xxx_messageInfo_CpScoreInfo proto.InternalMessageInfo

func (m *CpScoreInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *CpScoreInfo) GetUidA() uint32 {
	if m != nil {
		return m.UidA
	}
	return 0
}

func (m *CpScoreInfo) GetUidB() uint32 {
	if m != nil {
		return m.UidB
	}
	return 0
}

func (m *CpScoreInfo) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *CpScoreInfo) GetCreateTs() uint32 {
	if m != nil {
		return m.CreateTs
	}
	return 0
}

func (m *CpScoreInfo) GetBgUrl() string {
	if m != nil {
		return m.BgUrl
	}
	return ""
}

func (m *CpScoreInfo) GetSendValueA() uint32 {
	if m != nil {
		return m.SendValueA
	}
	return 0
}

func (m *CpScoreInfo) GetSendValueB() uint32 {
	if m != nil {
		return m.SendValueB
	}
	return 0
}

func (m *CpScoreInfo) GetMvpUid() uint32 {
	if m != nil {
		return m.MvpUid
	}
	return 0
}

func (m *CpScoreInfo) GetUserProfile() *UserProfile {
	if m != nil {
		return m.UserProfile
	}
	return nil
}

func (m *CpScoreInfo) GetCpUserProfile() *UserProfile {
	if m != nil {
		return m.CpUserProfile
	}
	return nil
}

func (m *CpScoreInfo) GetUidAType() uint32 {
	if m != nil {
		return m.UidAType
	}
	return 0
}

func (m *CpScoreInfo) GetUidBType() uint32 {
	if m != nil {
		return m.UidBType
	}
	return 0
}

// 获取房间神仙榜单
type GetChannelCpGodRankListReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelCpGodRankListReq) Reset()         { *m = GetChannelCpGodRankListReq{} }
func (m *GetChannelCpGodRankListReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelCpGodRankListReq) ProtoMessage()    {}
func (*GetChannelCpGodRankListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_cp_game_1310697ea80348e6, []int{37}
}
func (m *GetChannelCpGodRankListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelCpGodRankListReq.Unmarshal(m, b)
}
func (m *GetChannelCpGodRankListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelCpGodRankListReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelCpGodRankListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelCpGodRankListReq.Merge(dst, src)
}
func (m *GetChannelCpGodRankListReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelCpGodRankListReq.Size(m)
}
func (m *GetChannelCpGodRankListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelCpGodRankListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelCpGodRankListReq proto.InternalMessageInfo

func (m *GetChannelCpGodRankListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetChannelCpGodRankListResp struct {
	List                 []*CpScoreInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetChannelCpGodRankListResp) Reset()         { *m = GetChannelCpGodRankListResp{} }
func (m *GetChannelCpGodRankListResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelCpGodRankListResp) ProtoMessage()    {}
func (*GetChannelCpGodRankListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_cp_game_1310697ea80348e6, []int{38}
}
func (m *GetChannelCpGodRankListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelCpGodRankListResp.Unmarshal(m, b)
}
func (m *GetChannelCpGodRankListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelCpGodRankListResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelCpGodRankListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelCpGodRankListResp.Merge(dst, src)
}
func (m *GetChannelCpGodRankListResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelCpGodRankListResp.Size(m)
}
func (m *GetChannelCpGodRankListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelCpGodRankListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelCpGodRankListResp proto.InternalMessageInfo

func (m *GetChannelCpGodRankListResp) GetList() []*CpScoreInfo {
	if m != nil {
		return m.List
	}
	return nil
}

type TestPushCpGameResultReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	UidA                 uint32   `protobuf:"varint,2,opt,name=uid_a,json=uidA,proto3" json:"uid_a,omitempty"`
	UidB                 uint32   `protobuf:"varint,3,opt,name=uid_b,json=uidB,proto3" json:"uid_b,omitempty"`
	Score                uint32   `protobuf:"varint,4,opt,name=score,proto3" json:"score,omitempty"`
	ResultTime           uint32   `protobuf:"varint,5,opt,name=result_time,json=resultTime,proto3" json:"result_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TestPushCpGameResultReq) Reset()         { *m = TestPushCpGameResultReq{} }
func (m *TestPushCpGameResultReq) String() string { return proto.CompactTextString(m) }
func (*TestPushCpGameResultReq) ProtoMessage()    {}
func (*TestPushCpGameResultReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_cp_game_1310697ea80348e6, []int{39}
}
func (m *TestPushCpGameResultReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TestPushCpGameResultReq.Unmarshal(m, b)
}
func (m *TestPushCpGameResultReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TestPushCpGameResultReq.Marshal(b, m, deterministic)
}
func (dst *TestPushCpGameResultReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TestPushCpGameResultReq.Merge(dst, src)
}
func (m *TestPushCpGameResultReq) XXX_Size() int {
	return xxx_messageInfo_TestPushCpGameResultReq.Size(m)
}
func (m *TestPushCpGameResultReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TestPushCpGameResultReq.DiscardUnknown(m)
}

var xxx_messageInfo_TestPushCpGameResultReq proto.InternalMessageInfo

func (m *TestPushCpGameResultReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *TestPushCpGameResultReq) GetUidA() uint32 {
	if m != nil {
		return m.UidA
	}
	return 0
}

func (m *TestPushCpGameResultReq) GetUidB() uint32 {
	if m != nil {
		return m.UidB
	}
	return 0
}

func (m *TestPushCpGameResultReq) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *TestPushCpGameResultReq) GetResultTime() uint32 {
	if m != nil {
		return m.ResultTime
	}
	return 0
}

type TestPushCpGameResultResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TestPushCpGameResultResp) Reset()         { *m = TestPushCpGameResultResp{} }
func (m *TestPushCpGameResultResp) String() string { return proto.CompactTextString(m) }
func (*TestPushCpGameResultResp) ProtoMessage()    {}
func (*TestPushCpGameResultResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_cp_game_1310697ea80348e6, []int{40}
}
func (m *TestPushCpGameResultResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TestPushCpGameResultResp.Unmarshal(m, b)
}
func (m *TestPushCpGameResultResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TestPushCpGameResultResp.Marshal(b, m, deterministic)
}
func (dst *TestPushCpGameResultResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TestPushCpGameResultResp.Merge(dst, src)
}
func (m *TestPushCpGameResultResp) XXX_Size() int {
	return xxx_messageInfo_TestPushCpGameResultResp.Size(m)
}
func (m *TestPushCpGameResultResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TestPushCpGameResultResp.DiscardUnknown(m)
}

var xxx_messageInfo_TestPushCpGameResultResp proto.InternalMessageInfo

type GetCpStrengthHistoryReq struct {
	MyUid                uint32   `protobuf:"varint,1,opt,name=my_uid,json=myUid,proto3" json:"my_uid,omitempty"`
	ToUid                uint32   `protobuf:"varint,2,opt,name=to_uid,json=toUid,proto3" json:"to_uid,omitempty"`
	Offset               uint32   `protobuf:"varint,3,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,4,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCpStrengthHistoryReq) Reset()         { *m = GetCpStrengthHistoryReq{} }
func (m *GetCpStrengthHistoryReq) String() string { return proto.CompactTextString(m) }
func (*GetCpStrengthHistoryReq) ProtoMessage()    {}
func (*GetCpStrengthHistoryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_cp_game_1310697ea80348e6, []int{41}
}
func (m *GetCpStrengthHistoryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCpStrengthHistoryReq.Unmarshal(m, b)
}
func (m *GetCpStrengthHistoryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCpStrengthHistoryReq.Marshal(b, m, deterministic)
}
func (dst *GetCpStrengthHistoryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCpStrengthHistoryReq.Merge(dst, src)
}
func (m *GetCpStrengthHistoryReq) XXX_Size() int {
	return xxx_messageInfo_GetCpStrengthHistoryReq.Size(m)
}
func (m *GetCpStrengthHistoryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCpStrengthHistoryReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetCpStrengthHistoryReq proto.InternalMessageInfo

func (m *GetCpStrengthHistoryReq) GetMyUid() uint32 {
	if m != nil {
		return m.MyUid
	}
	return 0
}

func (m *GetCpStrengthHistoryReq) GetToUid() uint32 {
	if m != nil {
		return m.ToUid
	}
	return 0
}

func (m *GetCpStrengthHistoryReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetCpStrengthHistoryReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type StrengthHistory struct {
	Date                 string   `protobuf:"bytes,1,opt,name=date,proto3" json:"date,omitempty"`
	Reason               string   `protobuf:"bytes,2,opt,name=reason,proto3" json:"reason,omitempty"`
	Strength             int32    `protobuf:"varint,3,opt,name=strength,proto3" json:"strength,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StrengthHistory) Reset()         { *m = StrengthHistory{} }
func (m *StrengthHistory) String() string { return proto.CompactTextString(m) }
func (*StrengthHistory) ProtoMessage()    {}
func (*StrengthHistory) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_cp_game_1310697ea80348e6, []int{42}
}
func (m *StrengthHistory) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StrengthHistory.Unmarshal(m, b)
}
func (m *StrengthHistory) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StrengthHistory.Marshal(b, m, deterministic)
}
func (dst *StrengthHistory) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StrengthHistory.Merge(dst, src)
}
func (m *StrengthHistory) XXX_Size() int {
	return xxx_messageInfo_StrengthHistory.Size(m)
}
func (m *StrengthHistory) XXX_DiscardUnknown() {
	xxx_messageInfo_StrengthHistory.DiscardUnknown(m)
}

var xxx_messageInfo_StrengthHistory proto.InternalMessageInfo

func (m *StrengthHistory) GetDate() string {
	if m != nil {
		return m.Date
	}
	return ""
}

func (m *StrengthHistory) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

func (m *StrengthHistory) GetStrength() int32 {
	if m != nil {
		return m.Strength
	}
	return 0
}

type GetCpStrengthHistoryResp struct {
	MyUid                uint32             `protobuf:"varint,1,opt,name=my_uid,json=myUid,proto3" json:"my_uid,omitempty"`
	ToUid                uint32             `protobuf:"varint,2,opt,name=to_uid,json=toUid,proto3" json:"to_uid,omitempty"`
	Offset               uint32             `protobuf:"varint,3,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32             `protobuf:"varint,4,opt,name=limit,proto3" json:"limit,omitempty"`
	List                 []*StrengthHistory `protobuf:"bytes,5,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetCpStrengthHistoryResp) Reset()         { *m = GetCpStrengthHistoryResp{} }
func (m *GetCpStrengthHistoryResp) String() string { return proto.CompactTextString(m) }
func (*GetCpStrengthHistoryResp) ProtoMessage()    {}
func (*GetCpStrengthHistoryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_cp_game_1310697ea80348e6, []int{43}
}
func (m *GetCpStrengthHistoryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCpStrengthHistoryResp.Unmarshal(m, b)
}
func (m *GetCpStrengthHistoryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCpStrengthHistoryResp.Marshal(b, m, deterministic)
}
func (dst *GetCpStrengthHistoryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCpStrengthHistoryResp.Merge(dst, src)
}
func (m *GetCpStrengthHistoryResp) XXX_Size() int {
	return xxx_messageInfo_GetCpStrengthHistoryResp.Size(m)
}
func (m *GetCpStrengthHistoryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCpStrengthHistoryResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetCpStrengthHistoryResp proto.InternalMessageInfo

func (m *GetCpStrengthHistoryResp) GetMyUid() uint32 {
	if m != nil {
		return m.MyUid
	}
	return 0
}

func (m *GetCpStrengthHistoryResp) GetToUid() uint32 {
	if m != nil {
		return m.ToUid
	}
	return 0
}

func (m *GetCpStrengthHistoryResp) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetCpStrengthHistoryResp) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetCpStrengthHistoryResp) GetList() []*StrengthHistory {
	if m != nil {
		return m.List
	}
	return nil
}

type GetCpStrengthReq struct {
	MyUid                uint32   `protobuf:"varint,1,opt,name=my_uid,json=myUid,proto3" json:"my_uid,omitempty"`
	ToUid                uint32   `protobuf:"varint,2,opt,name=to_uid,json=toUid,proto3" json:"to_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCpStrengthReq) Reset()         { *m = GetCpStrengthReq{} }
func (m *GetCpStrengthReq) String() string { return proto.CompactTextString(m) }
func (*GetCpStrengthReq) ProtoMessage()    {}
func (*GetCpStrengthReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_cp_game_1310697ea80348e6, []int{44}
}
func (m *GetCpStrengthReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCpStrengthReq.Unmarshal(m, b)
}
func (m *GetCpStrengthReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCpStrengthReq.Marshal(b, m, deterministic)
}
func (dst *GetCpStrengthReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCpStrengthReq.Merge(dst, src)
}
func (m *GetCpStrengthReq) XXX_Size() int {
	return xxx_messageInfo_GetCpStrengthReq.Size(m)
}
func (m *GetCpStrengthReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCpStrengthReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetCpStrengthReq proto.InternalMessageInfo

func (m *GetCpStrengthReq) GetMyUid() uint32 {
	if m != nil {
		return m.MyUid
	}
	return 0
}

func (m *GetCpStrengthReq) GetToUid() uint32 {
	if m != nil {
		return m.ToUid
	}
	return 0
}

type GetCpStrengthResp struct {
	MyUid                uint32   `protobuf:"varint,1,opt,name=my_uid,json=myUid,proto3" json:"my_uid,omitempty"`
	ToUid                uint32   `protobuf:"varint,2,opt,name=to_uid,json=toUid,proto3" json:"to_uid,omitempty"`
	Strength             uint32   `protobuf:"varint,3,opt,name=strength,proto3" json:"strength,omitempty"`
	UniquePlate          string   `protobuf:"bytes,4,opt,name=unique_plate,json=uniquePlate,proto3" json:"unique_plate,omitempty"`
	MultiPlate           string   `protobuf:"bytes,5,opt,name=multi_plate,json=multiPlate,proto3" json:"multi_plate,omitempty"`
	WebPlate             string   `protobuf:"bytes,6,opt,name=web_plate,json=webPlate,proto3" json:"web_plate,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCpStrengthResp) Reset()         { *m = GetCpStrengthResp{} }
func (m *GetCpStrengthResp) String() string { return proto.CompactTextString(m) }
func (*GetCpStrengthResp) ProtoMessage()    {}
func (*GetCpStrengthResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_cp_game_1310697ea80348e6, []int{45}
}
func (m *GetCpStrengthResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCpStrengthResp.Unmarshal(m, b)
}
func (m *GetCpStrengthResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCpStrengthResp.Marshal(b, m, deterministic)
}
func (dst *GetCpStrengthResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCpStrengthResp.Merge(dst, src)
}
func (m *GetCpStrengthResp) XXX_Size() int {
	return xxx_messageInfo_GetCpStrengthResp.Size(m)
}
func (m *GetCpStrengthResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCpStrengthResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetCpStrengthResp proto.InternalMessageInfo

func (m *GetCpStrengthResp) GetMyUid() uint32 {
	if m != nil {
		return m.MyUid
	}
	return 0
}

func (m *GetCpStrengthResp) GetToUid() uint32 {
	if m != nil {
		return m.ToUid
	}
	return 0
}

func (m *GetCpStrengthResp) GetStrength() uint32 {
	if m != nil {
		return m.Strength
	}
	return 0
}

func (m *GetCpStrengthResp) GetUniquePlate() string {
	if m != nil {
		return m.UniquePlate
	}
	return ""
}

func (m *GetCpStrengthResp) GetMultiPlate() string {
	if m != nil {
		return m.MultiPlate
	}
	return ""
}

func (m *GetCpStrengthResp) GetWebPlate() string {
	if m != nil {
		return m.WebPlate
	}
	return ""
}

type CpNameplateInfo struct {
	Level                uint32   `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"`
	Url                  string   `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
	IsUpgrade            bool     `protobuf:"varint,3,opt,name=is_upgrade,json=isUpgrade,proto3" json:"is_upgrade,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CpNameplateInfo) Reset()         { *m = CpNameplateInfo{} }
func (m *CpNameplateInfo) String() string { return proto.CompactTextString(m) }
func (*CpNameplateInfo) ProtoMessage()    {}
func (*CpNameplateInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_cp_game_1310697ea80348e6, []int{46}
}
func (m *CpNameplateInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CpNameplateInfo.Unmarshal(m, b)
}
func (m *CpNameplateInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CpNameplateInfo.Marshal(b, m, deterministic)
}
func (dst *CpNameplateInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CpNameplateInfo.Merge(dst, src)
}
func (m *CpNameplateInfo) XXX_Size() int {
	return xxx_messageInfo_CpNameplateInfo.Size(m)
}
func (m *CpNameplateInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_CpNameplateInfo.DiscardUnknown(m)
}

var xxx_messageInfo_CpNameplateInfo proto.InternalMessageInfo

func (m *CpNameplateInfo) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *CpNameplateInfo) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *CpNameplateInfo) GetIsUpgrade() bool {
	if m != nil {
		return m.IsUpgrade
	}
	return false
}

type GetNameplateInfoReq struct {
	MyUid                uint32   `protobuf:"varint,1,opt,name=my_uid,json=myUid,proto3" json:"my_uid,omitempty"`
	FellowUid            uint32   `protobuf:"varint,2,opt,name=fellow_uid,json=fellowUid,proto3" json:"fellow_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetNameplateInfoReq) Reset()         { *m = GetNameplateInfoReq{} }
func (m *GetNameplateInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetNameplateInfoReq) ProtoMessage()    {}
func (*GetNameplateInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_cp_game_1310697ea80348e6, []int{47}
}
func (m *GetNameplateInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNameplateInfoReq.Unmarshal(m, b)
}
func (m *GetNameplateInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNameplateInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetNameplateInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNameplateInfoReq.Merge(dst, src)
}
func (m *GetNameplateInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetNameplateInfoReq.Size(m)
}
func (m *GetNameplateInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNameplateInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetNameplateInfoReq proto.InternalMessageInfo

func (m *GetNameplateInfoReq) GetMyUid() uint32 {
	if m != nil {
		return m.MyUid
	}
	return 0
}

func (m *GetNameplateInfoReq) GetFellowUid() uint32 {
	if m != nil {
		return m.FellowUid
	}
	return 0
}

type GetNameplateInfoResp struct {
	CurrentLv            uint32   `protobuf:"varint,1,opt,name=current_lv,json=currentLv,proto3" json:"current_lv,omitempty"`
	CurrentStrength      uint32   `protobuf:"varint,2,opt,name=current_strength,json=currentStrength,proto3" json:"current_strength,omitempty"`
	CurrentLvStrength    uint32   `protobuf:"varint,3,opt,name=current_lv_strength,json=currentLvStrength,proto3" json:"current_lv_strength,omitempty"`
	NextLvStrength       uint32   `protobuf:"varint,4,opt,name=next_lv_strength,json=nextLvStrength,proto3" json:"next_lv_strength,omitempty"`
	NameplateUrl         string   `protobuf:"bytes,5,opt,name=nameplate_url,json=nameplateUrl,proto3" json:"nameplate_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetNameplateInfoResp) Reset()         { *m = GetNameplateInfoResp{} }
func (m *GetNameplateInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetNameplateInfoResp) ProtoMessage()    {}
func (*GetNameplateInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_cp_game_1310697ea80348e6, []int{48}
}
func (m *GetNameplateInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNameplateInfoResp.Unmarshal(m, b)
}
func (m *GetNameplateInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNameplateInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetNameplateInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNameplateInfoResp.Merge(dst, src)
}
func (m *GetNameplateInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetNameplateInfoResp.Size(m)
}
func (m *GetNameplateInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNameplateInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetNameplateInfoResp proto.InternalMessageInfo

func (m *GetNameplateInfoResp) GetCurrentLv() uint32 {
	if m != nil {
		return m.CurrentLv
	}
	return 0
}

func (m *GetNameplateInfoResp) GetCurrentStrength() uint32 {
	if m != nil {
		return m.CurrentStrength
	}
	return 0
}

func (m *GetNameplateInfoResp) GetCurrentLvStrength() uint32 {
	if m != nil {
		return m.CurrentLvStrength
	}
	return 0
}

func (m *GetNameplateInfoResp) GetNextLvStrength() uint32 {
	if m != nil {
		return m.NextLvStrength
	}
	return 0
}

func (m *GetNameplateInfoResp) GetNameplateUrl() string {
	if m != nil {
		return m.NameplateUrl
	}
	return ""
}

// MVP上麦申请
type MvpAutoHoldMicReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	MvpUid               uint32   `protobuf:"varint,2,opt,name=mvp_uid,json=mvpUid,proto3" json:"mvp_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MvpAutoHoldMicReq) Reset()         { *m = MvpAutoHoldMicReq{} }
func (m *MvpAutoHoldMicReq) String() string { return proto.CompactTextString(m) }
func (*MvpAutoHoldMicReq) ProtoMessage()    {}
func (*MvpAutoHoldMicReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_cp_game_1310697ea80348e6, []int{49}
}
func (m *MvpAutoHoldMicReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MvpAutoHoldMicReq.Unmarshal(m, b)
}
func (m *MvpAutoHoldMicReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MvpAutoHoldMicReq.Marshal(b, m, deterministic)
}
func (dst *MvpAutoHoldMicReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MvpAutoHoldMicReq.Merge(dst, src)
}
func (m *MvpAutoHoldMicReq) XXX_Size() int {
	return xxx_messageInfo_MvpAutoHoldMicReq.Size(m)
}
func (m *MvpAutoHoldMicReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MvpAutoHoldMicReq.DiscardUnknown(m)
}

var xxx_messageInfo_MvpAutoHoldMicReq proto.InternalMessageInfo

func (m *MvpAutoHoldMicReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *MvpAutoHoldMicReq) GetMvpUid() uint32 {
	if m != nil {
		return m.MvpUid
	}
	return 0
}

type MvpAutoHoldMicResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MvpAutoHoldMicResp) Reset()         { *m = MvpAutoHoldMicResp{} }
func (m *MvpAutoHoldMicResp) String() string { return proto.CompactTextString(m) }
func (*MvpAutoHoldMicResp) ProtoMessage()    {}
func (*MvpAutoHoldMicResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_cp_game_1310697ea80348e6, []int{50}
}
func (m *MvpAutoHoldMicResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MvpAutoHoldMicResp.Unmarshal(m, b)
}
func (m *MvpAutoHoldMicResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MvpAutoHoldMicResp.Marshal(b, m, deterministic)
}
func (dst *MvpAutoHoldMicResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MvpAutoHoldMicResp.Merge(dst, src)
}
func (m *MvpAutoHoldMicResp) XXX_Size() int {
	return xxx_messageInfo_MvpAutoHoldMicResp.Size(m)
}
func (m *MvpAutoHoldMicResp) XXX_DiscardUnknown() {
	xxx_messageInfo_MvpAutoHoldMicResp.DiscardUnknown(m)
}

var xxx_messageInfo_MvpAutoHoldMicResp proto.InternalMessageInfo

type ChoseRare struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Account              string   `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	SubRareId            uint32   `protobuf:"varint,3,opt,name=sub_rare_id,json=subRareId,proto3" json:"sub_rare_id,omitempty"`
	SubRareName          string   `protobuf:"bytes,4,opt,name=sub_rare_name,json=subRareName,proto3" json:"sub_rare_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChoseRare) Reset()         { *m = ChoseRare{} }
func (m *ChoseRare) String() string { return proto.CompactTextString(m) }
func (*ChoseRare) ProtoMessage()    {}
func (*ChoseRare) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_cp_game_1310697ea80348e6, []int{51}
}
func (m *ChoseRare) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChoseRare.Unmarshal(m, b)
}
func (m *ChoseRare) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChoseRare.Marshal(b, m, deterministic)
}
func (dst *ChoseRare) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChoseRare.Merge(dst, src)
}
func (m *ChoseRare) XXX_Size() int {
	return xxx_messageInfo_ChoseRare.Size(m)
}
func (m *ChoseRare) XXX_DiscardUnknown() {
	xxx_messageInfo_ChoseRare.DiscardUnknown(m)
}

var xxx_messageInfo_ChoseRare proto.InternalMessageInfo

func (m *ChoseRare) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ChoseRare) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *ChoseRare) GetSubRareId() uint32 {
	if m != nil {
		return m.SubRareId
	}
	return 0
}

func (m *ChoseRare) GetSubRareName() string {
	if m != nil {
		return m.SubRareName
	}
	return ""
}

// 主持选择(或者确定)稀缺关系
type ChoseCpRareReq struct {
	ChannelId            uint32       `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	TeamId               string       `protobuf:"bytes,2,opt,name=team_id,json=teamId,proto3" json:"team_id,omitempty"`
	RareId               uint32       `protobuf:"varint,3,opt,name=rare_id,json=rareId,proto3" json:"rare_id,omitempty"`
	ChoseRare            []*ChoseRare `protobuf:"bytes,4,rep,name=chose_rare,json=choseRare,proto3" json:"chose_rare,omitempty"`
	Confirm              bool         `protobuf:"varint,5,opt,name=confirm,proto3" json:"confirm,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ChoseCpRareReq) Reset()         { *m = ChoseCpRareReq{} }
func (m *ChoseCpRareReq) String() string { return proto.CompactTextString(m) }
func (*ChoseCpRareReq) ProtoMessage()    {}
func (*ChoseCpRareReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_cp_game_1310697ea80348e6, []int{52}
}
func (m *ChoseCpRareReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChoseCpRareReq.Unmarshal(m, b)
}
func (m *ChoseCpRareReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChoseCpRareReq.Marshal(b, m, deterministic)
}
func (dst *ChoseCpRareReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChoseCpRareReq.Merge(dst, src)
}
func (m *ChoseCpRareReq) XXX_Size() int {
	return xxx_messageInfo_ChoseCpRareReq.Size(m)
}
func (m *ChoseCpRareReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChoseCpRareReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChoseCpRareReq proto.InternalMessageInfo

func (m *ChoseCpRareReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChoseCpRareReq) GetTeamId() string {
	if m != nil {
		return m.TeamId
	}
	return ""
}

func (m *ChoseCpRareReq) GetRareId() uint32 {
	if m != nil {
		return m.RareId
	}
	return 0
}

func (m *ChoseCpRareReq) GetChoseRare() []*ChoseRare {
	if m != nil {
		return m.ChoseRare
	}
	return nil
}

func (m *ChoseCpRareReq) GetConfirm() bool {
	if m != nil {
		return m.Confirm
	}
	return false
}

type ChoseCpRareResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChoseCpRareResp) Reset()         { *m = ChoseCpRareResp{} }
func (m *ChoseCpRareResp) String() string { return proto.CompactTextString(m) }
func (*ChoseCpRareResp) ProtoMessage()    {}
func (*ChoseCpRareResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_cp_game_1310697ea80348e6, []int{53}
}
func (m *ChoseCpRareResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChoseCpRareResp.Unmarshal(m, b)
}
func (m *ChoseCpRareResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChoseCpRareResp.Marshal(b, m, deterministic)
}
func (dst *ChoseCpRareResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChoseCpRareResp.Merge(dst, src)
}
func (m *ChoseCpRareResp) XXX_Size() int {
	return xxx_messageInfo_ChoseCpRareResp.Size(m)
}
func (m *ChoseCpRareResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ChoseCpRareResp.DiscardUnknown(m)
}

var xxx_messageInfo_ChoseCpRareResp proto.InternalMessageInfo

type BatchGetStrengthReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ToUid                []uint32 `protobuf:"varint,2,rep,packed,name=to_uid,json=toUid,proto3" json:"to_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetStrengthReq) Reset()         { *m = BatchGetStrengthReq{} }
func (m *BatchGetStrengthReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetStrengthReq) ProtoMessage()    {}
func (*BatchGetStrengthReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_cp_game_1310697ea80348e6, []int{54}
}
func (m *BatchGetStrengthReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetStrengthReq.Unmarshal(m, b)
}
func (m *BatchGetStrengthReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetStrengthReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetStrengthReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetStrengthReq.Merge(dst, src)
}
func (m *BatchGetStrengthReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetStrengthReq.Size(m)
}
func (m *BatchGetStrengthReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetStrengthReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetStrengthReq proto.InternalMessageInfo

func (m *BatchGetStrengthReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *BatchGetStrengthReq) GetToUid() []uint32 {
	if m != nil {
		return m.ToUid
	}
	return nil
}

type CpStrengthInfo struct {
	ToUid                uint32   `protobuf:"varint,1,opt,name=to_uid,json=toUid,proto3" json:"to_uid,omitempty"`
	Strength             uint32   `protobuf:"varint,2,opt,name=strength,proto3" json:"strength,omitempty"`
	UniquePlate          string   `protobuf:"bytes,3,opt,name=unique_plate,json=uniquePlate,proto3" json:"unique_plate,omitempty"`
	MultiPlate           string   `protobuf:"bytes,4,opt,name=multi_plate,json=multiPlate,proto3" json:"multi_plate,omitempty"`
	WebPlate             string   `protobuf:"bytes,5,opt,name=web_plate,json=webPlate,proto3" json:"web_plate,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CpStrengthInfo) Reset()         { *m = CpStrengthInfo{} }
func (m *CpStrengthInfo) String() string { return proto.CompactTextString(m) }
func (*CpStrengthInfo) ProtoMessage()    {}
func (*CpStrengthInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_cp_game_1310697ea80348e6, []int{55}
}
func (m *CpStrengthInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CpStrengthInfo.Unmarshal(m, b)
}
func (m *CpStrengthInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CpStrengthInfo.Marshal(b, m, deterministic)
}
func (dst *CpStrengthInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CpStrengthInfo.Merge(dst, src)
}
func (m *CpStrengthInfo) XXX_Size() int {
	return xxx_messageInfo_CpStrengthInfo.Size(m)
}
func (m *CpStrengthInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_CpStrengthInfo.DiscardUnknown(m)
}

var xxx_messageInfo_CpStrengthInfo proto.InternalMessageInfo

func (m *CpStrengthInfo) GetToUid() uint32 {
	if m != nil {
		return m.ToUid
	}
	return 0
}

func (m *CpStrengthInfo) GetStrength() uint32 {
	if m != nil {
		return m.Strength
	}
	return 0
}

func (m *CpStrengthInfo) GetUniquePlate() string {
	if m != nil {
		return m.UniquePlate
	}
	return ""
}

func (m *CpStrengthInfo) GetMultiPlate() string {
	if m != nil {
		return m.MultiPlate
	}
	return ""
}

func (m *CpStrengthInfo) GetWebPlate() string {
	if m != nil {
		return m.WebPlate
	}
	return ""
}

type BatchGetStrengthResp struct {
	Uid                  uint32                     `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	MapStrengthInfo      map[uint32]*CpStrengthInfo `protobuf:"bytes,2,rep,name=map_strength_info,json=mapStrengthInfo,proto3" json:"map_strength_info,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *BatchGetStrengthResp) Reset()         { *m = BatchGetStrengthResp{} }
func (m *BatchGetStrengthResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetStrengthResp) ProtoMessage()    {}
func (*BatchGetStrengthResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_cp_game_1310697ea80348e6, []int{56}
}
func (m *BatchGetStrengthResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetStrengthResp.Unmarshal(m, b)
}
func (m *BatchGetStrengthResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetStrengthResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetStrengthResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetStrengthResp.Merge(dst, src)
}
func (m *BatchGetStrengthResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetStrengthResp.Size(m)
}
func (m *BatchGetStrengthResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetStrengthResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetStrengthResp proto.InternalMessageInfo

func (m *BatchGetStrengthResp) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *BatchGetStrengthResp) GetMapStrengthInfo() map[uint32]*CpStrengthInfo {
	if m != nil {
		return m.MapStrengthInfo
	}
	return nil
}

// 选择稀缺关系广播
type CpChoseRareResult struct {
	ChannelId            uint32       `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	TeamId               string       `protobuf:"bytes,2,opt,name=team_id,json=teamId,proto3" json:"team_id,omitempty"`
	RareId               uint32       `protobuf:"varint,3,opt,name=rare_id,json=rareId,proto3" json:"rare_id,omitempty"`
	RareList             []*ChoseRare `protobuf:"bytes,4,rep,name=rare_list,json=rareList,proto3" json:"rare_list,omitempty"`
	Confirm              bool         `protobuf:"varint,5,opt,name=confirm,proto3" json:"confirm,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *CpChoseRareResult) Reset()         { *m = CpChoseRareResult{} }
func (m *CpChoseRareResult) String() string { return proto.CompactTextString(m) }
func (*CpChoseRareResult) ProtoMessage()    {}
func (*CpChoseRareResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_cp_game_1310697ea80348e6, []int{57}
}
func (m *CpChoseRareResult) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CpChoseRareResult.Unmarshal(m, b)
}
func (m *CpChoseRareResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CpChoseRareResult.Marshal(b, m, deterministic)
}
func (dst *CpChoseRareResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CpChoseRareResult.Merge(dst, src)
}
func (m *CpChoseRareResult) XXX_Size() int {
	return xxx_messageInfo_CpChoseRareResult.Size(m)
}
func (m *CpChoseRareResult) XXX_DiscardUnknown() {
	xxx_messageInfo_CpChoseRareResult.DiscardUnknown(m)
}

var xxx_messageInfo_CpChoseRareResult proto.InternalMessageInfo

func (m *CpChoseRareResult) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *CpChoseRareResult) GetTeamId() string {
	if m != nil {
		return m.TeamId
	}
	return ""
}

func (m *CpChoseRareResult) GetRareId() uint32 {
	if m != nil {
		return m.RareId
	}
	return 0
}

func (m *CpChoseRareResult) GetRareList() []*ChoseRare {
	if m != nil {
		return m.RareList
	}
	return nil
}

func (m *CpChoseRareResult) GetConfirm() bool {
	if m != nil {
		return m.Confirm
	}
	return false
}

type    AddCpStrengthDelayReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ToUid                uint32   `protobuf:"varint,2,opt,name=to_uid,json=toUid,proto3" json:"to_uid,omitempty"`
	Strength             uint32   `protobuf:"varint,3,opt,name=strength,proto3" json:"strength,omitempty"`
	Reason               string   `protobuf:"bytes,4,opt,name=reason,proto3" json:"reason,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddCpStrengthDelayReq) Reset()         { *m = AddCpStrengthDelayReq{} }
func (m *AddCpStrengthDelayReq) String() string { return proto.CompactTextString(m) }
func (*AddCpStrengthDelayReq) ProtoMessage()    {}
func (*AddCpStrengthDelayReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_cp_game_1310697ea80348e6, []int{58}
}
func (m *AddCpStrengthDelayReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddCpStrengthDelayReq.Unmarshal(m, b)
}
func (m *AddCpStrengthDelayReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddCpStrengthDelayReq.Marshal(b, m, deterministic)
}
func (dst *AddCpStrengthDelayReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddCpStrengthDelayReq.Merge(dst, src)
}
func (m *AddCpStrengthDelayReq) XXX_Size() int {
	return xxx_messageInfo_AddCpStrengthDelayReq.Size(m)
}
func (m *AddCpStrengthDelayReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddCpStrengthDelayReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddCpStrengthDelayReq proto.InternalMessageInfo

func (m *AddCpStrengthDelayReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddCpStrengthDelayReq) GetToUid() uint32 {
	if m != nil {
		return m.ToUid
	}
	return 0
}

func (m *AddCpStrengthDelayReq) GetStrength() uint32 {
	if m != nil {
		return m.Strength
	}
	return 0
}

func (m *AddCpStrengthDelayReq) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

type AddCpStrengthDelayResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddCpStrengthDelayResp) Reset()         { *m = AddCpStrengthDelayResp{} }
func (m *AddCpStrengthDelayResp) String() string { return proto.CompactTextString(m) }
func (*AddCpStrengthDelayResp) ProtoMessage()    {}
func (*AddCpStrengthDelayResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_cp_game_1310697ea80348e6, []int{59}
}
func (m *AddCpStrengthDelayResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddCpStrengthDelayResp.Unmarshal(m, b)
}
func (m *AddCpStrengthDelayResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddCpStrengthDelayResp.Marshal(b, m, deterministic)
}
func (dst *AddCpStrengthDelayResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddCpStrengthDelayResp.Merge(dst, src)
}
func (m *AddCpStrengthDelayResp) XXX_Size() int {
	return xxx_messageInfo_AddCpStrengthDelayResp.Size(m)
}
func (m *AddCpStrengthDelayResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddCpStrengthDelayResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddCpStrengthDelayResp proto.InternalMessageInfo

type AddCpStrengthLogDelayReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ToUid                uint32   `protobuf:"varint,2,opt,name=to_uid,json=toUid,proto3" json:"to_uid,omitempty"`
	TaskType             uint32   `protobuf:"varint,3,opt,name=task_type,json=taskType,proto3" json:"task_type,omitempty"`
	Score                uint32   `protobuf:"varint,4,opt,name=score,proto3" json:"score,omitempty"`
	CreateAt             uint32   `protobuf:"varint,5,opt,name=create_at,json=createAt,proto3" json:"create_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddCpStrengthLogDelayReq) Reset()         { *m = AddCpStrengthLogDelayReq{} }
func (m *AddCpStrengthLogDelayReq) String() string { return proto.CompactTextString(m) }
func (*AddCpStrengthLogDelayReq) ProtoMessage()    {}
func (*AddCpStrengthLogDelayReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_cp_game_1310697ea80348e6, []int{60}
}
func (m *AddCpStrengthLogDelayReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddCpStrengthLogDelayReq.Unmarshal(m, b)
}
func (m *AddCpStrengthLogDelayReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddCpStrengthLogDelayReq.Marshal(b, m, deterministic)
}
func (dst *AddCpStrengthLogDelayReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddCpStrengthLogDelayReq.Merge(dst, src)
}
func (m *AddCpStrengthLogDelayReq) XXX_Size() int {
	return xxx_messageInfo_AddCpStrengthLogDelayReq.Size(m)
}
func (m *AddCpStrengthLogDelayReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddCpStrengthLogDelayReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddCpStrengthLogDelayReq proto.InternalMessageInfo

func (m *AddCpStrengthLogDelayReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddCpStrengthLogDelayReq) GetToUid() uint32 {
	if m != nil {
		return m.ToUid
	}
	return 0
}

func (m *AddCpStrengthLogDelayReq) GetTaskType() uint32 {
	if m != nil {
		return m.TaskType
	}
	return 0
}

func (m *AddCpStrengthLogDelayReq) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *AddCpStrengthLogDelayReq) GetCreateAt() uint32 {
	if m != nil {
		return m.CreateAt
	}
	return 0
}

type AddCpStrengthLogDelayResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddCpStrengthLogDelayResp) Reset()         { *m = AddCpStrengthLogDelayResp{} }
func (m *AddCpStrengthLogDelayResp) String() string { return proto.CompactTextString(m) }
func (*AddCpStrengthLogDelayResp) ProtoMessage()    {}
func (*AddCpStrengthLogDelayResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_cp_game_1310697ea80348e6, []int{61}
}
func (m *AddCpStrengthLogDelayResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddCpStrengthLogDelayResp.Unmarshal(m, b)
}
func (m *AddCpStrengthLogDelayResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddCpStrengthLogDelayResp.Marshal(b, m, deterministic)
}
func (dst *AddCpStrengthLogDelayResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddCpStrengthLogDelayResp.Merge(dst, src)
}
func (m *AddCpStrengthLogDelayResp) XXX_Size() int {
	return xxx_messageInfo_AddCpStrengthLogDelayResp.Size(m)
}
func (m *AddCpStrengthLogDelayResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddCpStrengthLogDelayResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddCpStrengthLogDelayResp proto.InternalMessageInfo

type ReportCpGameInfoDelayReq struct {
	CpGameInfo           *CpGameInfo     `protobuf:"bytes,1,opt,name=cp_game_info,json=cpGameInfo,proto3" json:"cp_game_info,omitempty"`
	CpMemberInfo         []*CpMemberInfo `protobuf:"bytes,2,rep,name=cp_member_info,json=cpMemberInfo,proto3" json:"cp_member_info,omitempty"`
	Score                uint32          `protobuf:"varint,3,opt,name=score,proto3" json:"score,omitempty"`
	LevelScore           uint32          `protobuf:"varint,4,opt,name=level_score,json=levelScore,proto3" json:"level_score,omitempty"`
	Rank                 uint32          `protobuf:"varint,5,opt,name=rank,proto3" json:"rank,omitempty"`
	SourcePhrase         uint32          `protobuf:"varint,6,opt,name=source_phrase,json=sourcePhrase,proto3" json:"source_phrase,omitempty"`
	AppendTimeSec        uint32          `protobuf:"varint,7,opt,name=append_time_sec,json=appendTimeSec,proto3" json:"append_time_sec,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *ReportCpGameInfoDelayReq) Reset()         { *m = ReportCpGameInfoDelayReq{} }
func (m *ReportCpGameInfoDelayReq) String() string { return proto.CompactTextString(m) }
func (*ReportCpGameInfoDelayReq) ProtoMessage()    {}
func (*ReportCpGameInfoDelayReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_cp_game_1310697ea80348e6, []int{62}
}
func (m *ReportCpGameInfoDelayReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportCpGameInfoDelayReq.Unmarshal(m, b)
}
func (m *ReportCpGameInfoDelayReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportCpGameInfoDelayReq.Marshal(b, m, deterministic)
}
func (dst *ReportCpGameInfoDelayReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportCpGameInfoDelayReq.Merge(dst, src)
}
func (m *ReportCpGameInfoDelayReq) XXX_Size() int {
	return xxx_messageInfo_ReportCpGameInfoDelayReq.Size(m)
}
func (m *ReportCpGameInfoDelayReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportCpGameInfoDelayReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReportCpGameInfoDelayReq proto.InternalMessageInfo

func (m *ReportCpGameInfoDelayReq) GetCpGameInfo() *CpGameInfo {
	if m != nil {
		return m.CpGameInfo
	}
	return nil
}

func (m *ReportCpGameInfoDelayReq) GetCpMemberInfo() []*CpMemberInfo {
	if m != nil {
		return m.CpMemberInfo
	}
	return nil
}

func (m *ReportCpGameInfoDelayReq) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *ReportCpGameInfoDelayReq) GetLevelScore() uint32 {
	if m != nil {
		return m.LevelScore
	}
	return 0
}

func (m *ReportCpGameInfoDelayReq) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *ReportCpGameInfoDelayReq) GetSourcePhrase() uint32 {
	if m != nil {
		return m.SourcePhrase
	}
	return 0
}

func (m *ReportCpGameInfoDelayReq) GetAppendTimeSec() uint32 {
	if m != nil {
		return m.AppendTimeSec
	}
	return 0
}

type ReportCpGameInfoDelayResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportCpGameInfoDelayResp) Reset()         { *m = ReportCpGameInfoDelayResp{} }
func (m *ReportCpGameInfoDelayResp) String() string { return proto.CompactTextString(m) }
func (*ReportCpGameInfoDelayResp) ProtoMessage()    {}
func (*ReportCpGameInfoDelayResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_cp_game_1310697ea80348e6, []int{63}
}
func (m *ReportCpGameInfoDelayResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportCpGameInfoDelayResp.Unmarshal(m, b)
}
func (m *ReportCpGameInfoDelayResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportCpGameInfoDelayResp.Marshal(b, m, deterministic)
}
func (dst *ReportCpGameInfoDelayResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportCpGameInfoDelayResp.Merge(dst, src)
}
func (m *ReportCpGameInfoDelayResp) XXX_Size() int {
	return xxx_messageInfo_ReportCpGameInfoDelayResp.Size(m)
}
func (m *ReportCpGameInfoDelayResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportCpGameInfoDelayResp.DiscardUnknown(m)
}

var xxx_messageInfo_ReportCpGameInfoDelayResp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*BatchGetChannelCpGamePhaseReq)(nil), "channel_cp_game.BatchGetChannelCpGamePhaseReq")
	proto.RegisterType((*BatchGetChannelCpGamePhaseResp)(nil), "channel_cp_game.BatchGetChannelCpGamePhaseResp")
	proto.RegisterMapType((map[uint32]uint32)(nil), "channel_cp_game.BatchGetChannelCpGamePhaseResp.PhaseMapEntry")
	proto.RegisterType((*GetChannelCpGamePhaseReq)(nil), "channel_cp_game.GetChannelCpGamePhaseReq")
	proto.RegisterType((*GetChannelCpGamePhaseResp)(nil), "channel_cp_game.GetChannelCpGamePhaseResp")
	proto.RegisterType((*SetChannelCpGamePhaseReq)(nil), "channel_cp_game.SetChannelCpGamePhaseReq")
	proto.RegisterType((*SetChannelCpGamePhaseResp)(nil), "channel_cp_game.SetChannelCpGamePhaseResp")
	proto.RegisterType((*AddCpGamePhaseEndTimeReq)(nil), "channel_cp_game.AddCpGamePhaseEndTimeReq")
	proto.RegisterType((*AddCpGamePhaseEndTimeResp)(nil), "channel_cp_game.AddCpGamePhaseEndTimeResp")
	proto.RegisterType((*CpMemberInfo)(nil), "channel_cp_game.CpMemberInfo")
	proto.RegisterType((*RareAnimation)(nil), "channel_cp_game.RareAnimation")
	proto.RegisterType((*CpTeamInfo)(nil), "channel_cp_game.CpTeamInfo")
	proto.RegisterType((*LoseInfo)(nil), "channel_cp_game.LoseInfo")
	proto.RegisterType((*RareTeamInfo)(nil), "channel_cp_game.RareTeamInfo")
	proto.RegisterType((*CpGameInfo)(nil), "channel_cp_game.CpGameInfo")
	proto.RegisterType((*GetCurrCpGameInfoReq)(nil), "channel_cp_game.GetCurrCpGameInfoReq")
	proto.RegisterType((*GetCurrCpGameInfoResp)(nil), "channel_cp_game.GetCurrCpGameInfoResp")
	proto.RegisterType((*ChannelCpGameEntry)(nil), "channel_cp_game.ChannelCpGameEntry")
	proto.RegisterType((*SetChannelCpGameEntryLevelReq)(nil), "channel_cp_game.SetChannelCpGameEntryLevelReq")
	proto.RegisterType((*SetChannelCpGameEntryLevelResp)(nil), "channel_cp_game.SetChannelCpGameEntryLevelResp")
	proto.RegisterType((*GetChannelCpGameEntryLevelReq)(nil), "channel_cp_game.GetChannelCpGameEntryLevelReq")
	proto.RegisterType((*GetChannelCpGameEntryLevelResp)(nil), "channel_cp_game.GetChannelCpGameEntryLevelResp")
	proto.RegisterType((*BatchGetCpGameEntryReq)(nil), "channel_cp_game.BatchGetCpGameEntryReq")
	proto.RegisterType((*BatchGetCpGameEntryResp)(nil), "channel_cp_game.BatchGetCpGameEntryResp")
	proto.RegisterType((*CpCardResource)(nil), "channel_cp_game.CpCardResource")
	proto.RegisterType((*CpCardConf)(nil), "channel_cp_game.CpCardConf")
	proto.RegisterType((*AllCpCardConf)(nil), "channel_cp_game.AllCpCardConf")
	proto.RegisterType((*AddCpCardConfReq)(nil), "channel_cp_game.AddCpCardConfReq")
	proto.RegisterType((*AddCpCardConfResp)(nil), "channel_cp_game.AddCpCardConfResp")
	proto.RegisterType((*UpdateCpCardConfReq)(nil), "channel_cp_game.UpdateCpCardConfReq")
	proto.RegisterType((*UpdateCpCardConfResp)(nil), "channel_cp_game.UpdateCpCardConfResp")
	proto.RegisterType((*DelCpCardConfReq)(nil), "channel_cp_game.DelCpCardConfReq")
	proto.RegisterType((*DelCpCardConfResp)(nil), "channel_cp_game.DelCpCardConfResp")
	proto.RegisterType((*GetAllCpCardConfReq)(nil), "channel_cp_game.GetAllCpCardConfReq")
	proto.RegisterType((*GetAllCpCardConfResp)(nil), "channel_cp_game.GetAllCpCardConfResp")
	proto.RegisterType((*UserProfile)(nil), "channel_cp_game.UserProfile")
	proto.RegisterType((*UserPrivilege)(nil), "channel_cp_game.UserPrivilege")
	proto.RegisterType((*CpScoreInfo)(nil), "channel_cp_game.CpScoreInfo")
	proto.RegisterType((*GetChannelCpGodRankListReq)(nil), "channel_cp_game.GetChannelCpGodRankListReq")
	proto.RegisterType((*GetChannelCpGodRankListResp)(nil), "channel_cp_game.GetChannelCpGodRankListResp")
	proto.RegisterType((*TestPushCpGameResultReq)(nil), "channel_cp_game.TestPushCpGameResultReq")
	proto.RegisterType((*TestPushCpGameResultResp)(nil), "channel_cp_game.TestPushCpGameResultResp")
	proto.RegisterType((*GetCpStrengthHistoryReq)(nil), "channel_cp_game.GetCpStrengthHistoryReq")
	proto.RegisterType((*StrengthHistory)(nil), "channel_cp_game.StrengthHistory")
	proto.RegisterType((*GetCpStrengthHistoryResp)(nil), "channel_cp_game.GetCpStrengthHistoryResp")
	proto.RegisterType((*GetCpStrengthReq)(nil), "channel_cp_game.GetCpStrengthReq")
	proto.RegisterType((*GetCpStrengthResp)(nil), "channel_cp_game.GetCpStrengthResp")
	proto.RegisterType((*CpNameplateInfo)(nil), "channel_cp_game.CpNameplateInfo")
	proto.RegisterType((*GetNameplateInfoReq)(nil), "channel_cp_game.GetNameplateInfoReq")
	proto.RegisterType((*GetNameplateInfoResp)(nil), "channel_cp_game.GetNameplateInfoResp")
	proto.RegisterType((*MvpAutoHoldMicReq)(nil), "channel_cp_game.MvpAutoHoldMicReq")
	proto.RegisterType((*MvpAutoHoldMicResp)(nil), "channel_cp_game.MvpAutoHoldMicResp")
	proto.RegisterType((*ChoseRare)(nil), "channel_cp_game.ChoseRare")
	proto.RegisterType((*ChoseCpRareReq)(nil), "channel_cp_game.ChoseCpRareReq")
	proto.RegisterType((*ChoseCpRareResp)(nil), "channel_cp_game.ChoseCpRareResp")
	proto.RegisterType((*BatchGetStrengthReq)(nil), "channel_cp_game.BatchGetStrengthReq")
	proto.RegisterType((*CpStrengthInfo)(nil), "channel_cp_game.CpStrengthInfo")
	proto.RegisterType((*BatchGetStrengthResp)(nil), "channel_cp_game.BatchGetStrengthResp")
	proto.RegisterMapType((map[uint32]*CpStrengthInfo)(nil), "channel_cp_game.BatchGetStrengthResp.MapStrengthInfoEntry")
	proto.RegisterType((*CpChoseRareResult)(nil), "channel_cp_game.CpChoseRareResult")
	proto.RegisterType((*AddCpStrengthDelayReq)(nil), "channel_cp_game.AddCpStrengthDelayReq")
	proto.RegisterType((*AddCpStrengthDelayResp)(nil), "channel_cp_game.AddCpStrengthDelayResp")
	proto.RegisterType((*AddCpStrengthLogDelayReq)(nil), "channel_cp_game.AddCpStrengthLogDelayReq")
	proto.RegisterType((*AddCpStrengthLogDelayResp)(nil), "channel_cp_game.AddCpStrengthLogDelayResp")
	proto.RegisterType((*ReportCpGameInfoDelayReq)(nil), "channel_cp_game.ReportCpGameInfoDelayReq")
	proto.RegisterType((*ReportCpGameInfoDelayResp)(nil), "channel_cp_game.ReportCpGameInfoDelayResp")
	proto.RegisterEnum("channel_cp_game.CpGamePhaseType", CpGamePhaseType_name, CpGamePhaseType_value)
	proto.RegisterEnum("channel_cp_game.EUserPrivilegeType", EUserPrivilegeType_name, EUserPrivilegeType_value)
	proto.RegisterEnum("channel_cp_game.CpGameInfo_PKSubPhase", CpGameInfo_PKSubPhase_name, CpGameInfo_PKSubPhase_value)
	proto.RegisterEnum("channel_cp_game.CpCardConf_SubResourceIdx", CpCardConf_SubResourceIdx_name, CpCardConf_SubResourceIdx_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ChannelCpGameClient is the client API for ChannelCpGame service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChannelCpGameClient interface {
	// 获取房间cp战当前阶段
	GetChannelCpGamePhase(ctx context.Context, in *GetChannelCpGamePhaseReq, opts ...grpc.CallOption) (*GetChannelCpGamePhaseResp, error)
	// 切换房间cp战当前阶段
	SetChannelCpGamePhase(ctx context.Context, in *SetChannelCpGamePhaseReq, opts ...grpc.CallOption) (*SetChannelCpGamePhaseResp, error)
	// 加时
	AddCpGamePhaseEndTime(ctx context.Context, in *AddCpGamePhaseEndTimeReq, opts ...grpc.CallOption) (*AddCpGamePhaseEndTimeResp, error)
	// 获取房间当前cp战信息
	GetCurrCpGameInfo(ctx context.Context, in *GetCurrCpGameInfoReq, opts ...grpc.CallOption) (*GetCurrCpGameInfoResp, error)
	// 设置cp战权限等级
	SetChannelCpGameEntryLevel(ctx context.Context, in *SetChannelCpGameEntryLevelReq, opts ...grpc.CallOption) (*SetChannelCpGameEntryLevelResp, error)
	// 查询房间cp战权限
	GetChannelCpGameEntryLevel(ctx context.Context, in *GetChannelCpGameEntryLevelReq, opts ...grpc.CallOption) (*GetChannelCpGameEntryLevelResp, error)
	// 根据等级批量查询查询cp战权限列表
	BatchGetCpGameEntry(ctx context.Context, in *BatchGetCpGameEntryReq, opts ...grpc.CallOption) (*BatchGetCpGameEntryResp, error)
	// 添加cp战卡片特效配置
	AddCpCardConf(ctx context.Context, in *AddCpCardConfReq, opts ...grpc.CallOption) (*AddCpCardConfResp, error)
	// 更新cp战卡片特效配置
	UpdateCpCardConf(ctx context.Context, in *UpdateCpCardConfReq, opts ...grpc.CallOption) (*UpdateCpCardConfResp, error)
	// 添加cp战卡片特效配置
	DelCpCardConf(ctx context.Context, in *DelCpCardConfReq, opts ...grpc.CallOption) (*DelCpCardConfResp, error)
	// 获取所有cp战卡片特效配置
	GetAllCpCardConf(ctx context.Context, in *GetAllCpCardConfReq, opts ...grpc.CallOption) (*GetAllCpCardConfResp, error)
	// 获取神仙榜单
	GetChannelCpGodRankList(ctx context.Context, in *GetChannelCpGodRankListReq, opts ...grpc.CallOption) (*GetChannelCpGodRankListResp, error)
	// 测试cp战结果推送
	TestPushCpGameResult(ctx context.Context, in *TestPushCpGameResultReq, opts ...grpc.CallOption) (*TestPushCpGameResultResp, error)
	// 获取CP战战力值变化历史
	GetCpStrengthHistory(ctx context.Context, in *GetCpStrengthHistoryReq, opts ...grpc.CallOption) (*GetCpStrengthHistoryResp, error)
	// 获取CP战战力值
	GetCpStrength(ctx context.Context, in *GetCpStrengthReq, opts ...grpc.CallOption) (*GetCpStrengthResp, error)
	// 获取铭牌信息
	GetNameplateInfo(ctx context.Context, in *GetNameplateInfoReq, opts ...grpc.CallOption) (*GetNameplateInfoResp, error)
	// Mvp用户上麦申请
	MvpAutoHoldMic(ctx context.Context, in *MvpAutoHoldMicReq, opts ...grpc.CallOption) (*MvpAutoHoldMicResp, error)
	// 主持选择稀缺关系
	ChoseCpRare(ctx context.Context, in *ChoseCpRareReq, opts ...grpc.CallOption) (*ChoseCpRareResp, error)
	// 批量获取CP战信息
	BatchGetStrength(ctx context.Context, in *BatchGetStrengthReq, opts ...grpc.CallOption) (*BatchGetStrengthResp, error)
	// 战力值神秘人关闭延迟加分
	AddCpStrengthDelay(ctx context.Context, in *AddCpStrengthDelayReq, opts ...grpc.CallOption) (*AddCpStrengthDelayResp, error)
	AddCpStrengthLogDelay(ctx context.Context, in *AddCpStrengthLogDelayReq, opts ...grpc.CallOption) (*AddCpStrengthLogDelayResp, error)
	ReportCpGameInfoDelay(ctx context.Context, in *ReportCpGameInfoDelayReq, opts ...grpc.CallOption) (*ReportCpGameInfoDelayResp, error)
	// ===================== 礼物kafka对账 =========================
	// 礼物->CP战对账
	TimeRangeCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error)
	// 获取礼物kafka消费订单
	TimeRangeOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error)
	// 补单
	FixPresentCountOrder(ctx context.Context, in *reconcile_v2.ReplaceOrderReq, opts ...grpc.CallOption) (*reconcile_v2.EmptyResp, error)
	// 批量获取房间cp战当前阶段
	BatchGetChannelCpGamePhase(ctx context.Context, in *BatchGetChannelCpGamePhaseReq, opts ...grpc.CallOption) (*BatchGetChannelCpGamePhaseResp, error)
}

type channelCpGameClient struct {
	cc *grpc.ClientConn
}

func NewChannelCpGameClient(cc *grpc.ClientConn) ChannelCpGameClient {
	return &channelCpGameClient{cc}
}

func (c *channelCpGameClient) GetChannelCpGamePhase(ctx context.Context, in *GetChannelCpGamePhaseReq, opts ...grpc.CallOption) (*GetChannelCpGamePhaseResp, error) {
	out := new(GetChannelCpGamePhaseResp)
	err := c.cc.Invoke(ctx, "/channel_cp_game.ChannelCpGame/GetChannelCpGamePhase", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelCpGameClient) SetChannelCpGamePhase(ctx context.Context, in *SetChannelCpGamePhaseReq, opts ...grpc.CallOption) (*SetChannelCpGamePhaseResp, error) {
	out := new(SetChannelCpGamePhaseResp)
	err := c.cc.Invoke(ctx, "/channel_cp_game.ChannelCpGame/SetChannelCpGamePhase", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelCpGameClient) AddCpGamePhaseEndTime(ctx context.Context, in *AddCpGamePhaseEndTimeReq, opts ...grpc.CallOption) (*AddCpGamePhaseEndTimeResp, error) {
	out := new(AddCpGamePhaseEndTimeResp)
	err := c.cc.Invoke(ctx, "/channel_cp_game.ChannelCpGame/AddCpGamePhaseEndTime", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelCpGameClient) GetCurrCpGameInfo(ctx context.Context, in *GetCurrCpGameInfoReq, opts ...grpc.CallOption) (*GetCurrCpGameInfoResp, error) {
	out := new(GetCurrCpGameInfoResp)
	err := c.cc.Invoke(ctx, "/channel_cp_game.ChannelCpGame/GetCurrCpGameInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelCpGameClient) SetChannelCpGameEntryLevel(ctx context.Context, in *SetChannelCpGameEntryLevelReq, opts ...grpc.CallOption) (*SetChannelCpGameEntryLevelResp, error) {
	out := new(SetChannelCpGameEntryLevelResp)
	err := c.cc.Invoke(ctx, "/channel_cp_game.ChannelCpGame/SetChannelCpGameEntryLevel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelCpGameClient) GetChannelCpGameEntryLevel(ctx context.Context, in *GetChannelCpGameEntryLevelReq, opts ...grpc.CallOption) (*GetChannelCpGameEntryLevelResp, error) {
	out := new(GetChannelCpGameEntryLevelResp)
	err := c.cc.Invoke(ctx, "/channel_cp_game.ChannelCpGame/GetChannelCpGameEntryLevel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelCpGameClient) BatchGetCpGameEntry(ctx context.Context, in *BatchGetCpGameEntryReq, opts ...grpc.CallOption) (*BatchGetCpGameEntryResp, error) {
	out := new(BatchGetCpGameEntryResp)
	err := c.cc.Invoke(ctx, "/channel_cp_game.ChannelCpGame/BatchGetCpGameEntry", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelCpGameClient) AddCpCardConf(ctx context.Context, in *AddCpCardConfReq, opts ...grpc.CallOption) (*AddCpCardConfResp, error) {
	out := new(AddCpCardConfResp)
	err := c.cc.Invoke(ctx, "/channel_cp_game.ChannelCpGame/AddCpCardConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelCpGameClient) UpdateCpCardConf(ctx context.Context, in *UpdateCpCardConfReq, opts ...grpc.CallOption) (*UpdateCpCardConfResp, error) {
	out := new(UpdateCpCardConfResp)
	err := c.cc.Invoke(ctx, "/channel_cp_game.ChannelCpGame/UpdateCpCardConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelCpGameClient) DelCpCardConf(ctx context.Context, in *DelCpCardConfReq, opts ...grpc.CallOption) (*DelCpCardConfResp, error) {
	out := new(DelCpCardConfResp)
	err := c.cc.Invoke(ctx, "/channel_cp_game.ChannelCpGame/DelCpCardConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelCpGameClient) GetAllCpCardConf(ctx context.Context, in *GetAllCpCardConfReq, opts ...grpc.CallOption) (*GetAllCpCardConfResp, error) {
	out := new(GetAllCpCardConfResp)
	err := c.cc.Invoke(ctx, "/channel_cp_game.ChannelCpGame/GetAllCpCardConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelCpGameClient) GetChannelCpGodRankList(ctx context.Context, in *GetChannelCpGodRankListReq, opts ...grpc.CallOption) (*GetChannelCpGodRankListResp, error) {
	out := new(GetChannelCpGodRankListResp)
	err := c.cc.Invoke(ctx, "/channel_cp_game.ChannelCpGame/GetChannelCpGodRankList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelCpGameClient) TestPushCpGameResult(ctx context.Context, in *TestPushCpGameResultReq, opts ...grpc.CallOption) (*TestPushCpGameResultResp, error) {
	out := new(TestPushCpGameResultResp)
	err := c.cc.Invoke(ctx, "/channel_cp_game.ChannelCpGame/TestPushCpGameResult", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelCpGameClient) GetCpStrengthHistory(ctx context.Context, in *GetCpStrengthHistoryReq, opts ...grpc.CallOption) (*GetCpStrengthHistoryResp, error) {
	out := new(GetCpStrengthHistoryResp)
	err := c.cc.Invoke(ctx, "/channel_cp_game.ChannelCpGame/GetCpStrengthHistory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelCpGameClient) GetCpStrength(ctx context.Context, in *GetCpStrengthReq, opts ...grpc.CallOption) (*GetCpStrengthResp, error) {
	out := new(GetCpStrengthResp)
	err := c.cc.Invoke(ctx, "/channel_cp_game.ChannelCpGame/GetCpStrength", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelCpGameClient) GetNameplateInfo(ctx context.Context, in *GetNameplateInfoReq, opts ...grpc.CallOption) (*GetNameplateInfoResp, error) {
	out := new(GetNameplateInfoResp)
	err := c.cc.Invoke(ctx, "/channel_cp_game.ChannelCpGame/GetNameplateInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelCpGameClient) MvpAutoHoldMic(ctx context.Context, in *MvpAutoHoldMicReq, opts ...grpc.CallOption) (*MvpAutoHoldMicResp, error) {
	out := new(MvpAutoHoldMicResp)
	err := c.cc.Invoke(ctx, "/channel_cp_game.ChannelCpGame/MvpAutoHoldMic", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelCpGameClient) ChoseCpRare(ctx context.Context, in *ChoseCpRareReq, opts ...grpc.CallOption) (*ChoseCpRareResp, error) {
	out := new(ChoseCpRareResp)
	err := c.cc.Invoke(ctx, "/channel_cp_game.ChannelCpGame/ChoseCpRare", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelCpGameClient) BatchGetStrength(ctx context.Context, in *BatchGetStrengthReq, opts ...grpc.CallOption) (*BatchGetStrengthResp, error) {
	out := new(BatchGetStrengthResp)
	err := c.cc.Invoke(ctx, "/channel_cp_game.ChannelCpGame/BatchGetStrength", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelCpGameClient) AddCpStrengthDelay(ctx context.Context, in *AddCpStrengthDelayReq, opts ...grpc.CallOption) (*AddCpStrengthDelayResp, error) {
	out := new(AddCpStrengthDelayResp)
	err := c.cc.Invoke(ctx, "/channel_cp_game.ChannelCpGame/AddCpStrengthDelay", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelCpGameClient) AddCpStrengthLogDelay(ctx context.Context, in *AddCpStrengthLogDelayReq, opts ...grpc.CallOption) (*AddCpStrengthLogDelayResp, error) {
	out := new(AddCpStrengthLogDelayResp)
	err := c.cc.Invoke(ctx, "/channel_cp_game.ChannelCpGame/AddCpStrengthLogDelay", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelCpGameClient) ReportCpGameInfoDelay(ctx context.Context, in *ReportCpGameInfoDelayReq, opts ...grpc.CallOption) (*ReportCpGameInfoDelayResp, error) {
	out := new(ReportCpGameInfoDelayResp)
	err := c.cc.Invoke(ctx, "/channel_cp_game.ChannelCpGame/ReportCpGameInfoDelay", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelCpGameClient) TimeRangeCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	out := new(reconcile_v2.CountResp)
	err := c.cc.Invoke(ctx, "/channel_cp_game.ChannelCpGame/TimeRangeCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelCpGameClient) TimeRangeOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	out := new(reconcile_v2.OrderIdsResp)
	err := c.cc.Invoke(ctx, "/channel_cp_game.ChannelCpGame/TimeRangeOrderIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelCpGameClient) FixPresentCountOrder(ctx context.Context, in *reconcile_v2.ReplaceOrderReq, opts ...grpc.CallOption) (*reconcile_v2.EmptyResp, error) {
	out := new(reconcile_v2.EmptyResp)
	err := c.cc.Invoke(ctx, "/channel_cp_game.ChannelCpGame/FixPresentCountOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelCpGameClient) BatchGetChannelCpGamePhase(ctx context.Context, in *BatchGetChannelCpGamePhaseReq, opts ...grpc.CallOption) (*BatchGetChannelCpGamePhaseResp, error) {
	out := new(BatchGetChannelCpGamePhaseResp)
	err := c.cc.Invoke(ctx, "/channel_cp_game.ChannelCpGame/BatchGetChannelCpGamePhase", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChannelCpGameServer is the server API for ChannelCpGame service.
type ChannelCpGameServer interface {
	// 获取房间cp战当前阶段
	GetChannelCpGamePhase(context.Context, *GetChannelCpGamePhaseReq) (*GetChannelCpGamePhaseResp, error)
	// 切换房间cp战当前阶段
	SetChannelCpGamePhase(context.Context, *SetChannelCpGamePhaseReq) (*SetChannelCpGamePhaseResp, error)
	// 加时
	AddCpGamePhaseEndTime(context.Context, *AddCpGamePhaseEndTimeReq) (*AddCpGamePhaseEndTimeResp, error)
	// 获取房间当前cp战信息
	GetCurrCpGameInfo(context.Context, *GetCurrCpGameInfoReq) (*GetCurrCpGameInfoResp, error)
	// 设置cp战权限等级
	SetChannelCpGameEntryLevel(context.Context, *SetChannelCpGameEntryLevelReq) (*SetChannelCpGameEntryLevelResp, error)
	// 查询房间cp战权限
	GetChannelCpGameEntryLevel(context.Context, *GetChannelCpGameEntryLevelReq) (*GetChannelCpGameEntryLevelResp, error)
	// 根据等级批量查询查询cp战权限列表
	BatchGetCpGameEntry(context.Context, *BatchGetCpGameEntryReq) (*BatchGetCpGameEntryResp, error)
	// 添加cp战卡片特效配置
	AddCpCardConf(context.Context, *AddCpCardConfReq) (*AddCpCardConfResp, error)
	// 更新cp战卡片特效配置
	UpdateCpCardConf(context.Context, *UpdateCpCardConfReq) (*UpdateCpCardConfResp, error)
	// 添加cp战卡片特效配置
	DelCpCardConf(context.Context, *DelCpCardConfReq) (*DelCpCardConfResp, error)
	// 获取所有cp战卡片特效配置
	GetAllCpCardConf(context.Context, *GetAllCpCardConfReq) (*GetAllCpCardConfResp, error)
	// 获取神仙榜单
	GetChannelCpGodRankList(context.Context, *GetChannelCpGodRankListReq) (*GetChannelCpGodRankListResp, error)
	// 测试cp战结果推送
	TestPushCpGameResult(context.Context, *TestPushCpGameResultReq) (*TestPushCpGameResultResp, error)
	// 获取CP战战力值变化历史
	GetCpStrengthHistory(context.Context, *GetCpStrengthHistoryReq) (*GetCpStrengthHistoryResp, error)
	// 获取CP战战力值
	GetCpStrength(context.Context, *GetCpStrengthReq) (*GetCpStrengthResp, error)
	// 获取铭牌信息
	GetNameplateInfo(context.Context, *GetNameplateInfoReq) (*GetNameplateInfoResp, error)
	// Mvp用户上麦申请
	MvpAutoHoldMic(context.Context, *MvpAutoHoldMicReq) (*MvpAutoHoldMicResp, error)
	// 主持选择稀缺关系
	ChoseCpRare(context.Context, *ChoseCpRareReq) (*ChoseCpRareResp, error)
	// 批量获取CP战信息
	BatchGetStrength(context.Context, *BatchGetStrengthReq) (*BatchGetStrengthResp, error)
	// 战力值神秘人关闭延迟加分
	AddCpStrengthDelay(context.Context, *AddCpStrengthDelayReq) (*AddCpStrengthDelayResp, error)
	AddCpStrengthLogDelay(context.Context, *AddCpStrengthLogDelayReq) (*AddCpStrengthLogDelayResp, error)
	ReportCpGameInfoDelay(context.Context, *ReportCpGameInfoDelayReq) (*ReportCpGameInfoDelayResp, error)
	// ===================== 礼物kafka对账 =========================
	// 礼物->CP战对账
	TimeRangeCount(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error)
	// 获取礼物kafka消费订单
	TimeRangeOrderIds(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error)
	// 补单
	FixPresentCountOrder(context.Context, *reconcile_v2.ReplaceOrderReq) (*reconcile_v2.EmptyResp, error)
	// 批量获取房间cp战当前阶段
	BatchGetChannelCpGamePhase(context.Context, *BatchGetChannelCpGamePhaseReq) (*BatchGetChannelCpGamePhaseResp, error)
}

func RegisterChannelCpGameServer(s *grpc.Server, srv ChannelCpGameServer) {
	s.RegisterService(&_ChannelCpGame_serviceDesc, srv)
}

func _ChannelCpGame_GetChannelCpGamePhase_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelCpGamePhaseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelCpGameServer).GetChannelCpGamePhase(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_cp_game.ChannelCpGame/GetChannelCpGamePhase",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelCpGameServer).GetChannelCpGamePhase(ctx, req.(*GetChannelCpGamePhaseReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelCpGame_SetChannelCpGamePhase_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetChannelCpGamePhaseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelCpGameServer).SetChannelCpGamePhase(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_cp_game.ChannelCpGame/SetChannelCpGamePhase",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelCpGameServer).SetChannelCpGamePhase(ctx, req.(*SetChannelCpGamePhaseReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelCpGame_AddCpGamePhaseEndTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddCpGamePhaseEndTimeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelCpGameServer).AddCpGamePhaseEndTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_cp_game.ChannelCpGame/AddCpGamePhaseEndTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelCpGameServer).AddCpGamePhaseEndTime(ctx, req.(*AddCpGamePhaseEndTimeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelCpGame_GetCurrCpGameInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCurrCpGameInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelCpGameServer).GetCurrCpGameInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_cp_game.ChannelCpGame/GetCurrCpGameInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelCpGameServer).GetCurrCpGameInfo(ctx, req.(*GetCurrCpGameInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelCpGame_SetChannelCpGameEntryLevel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetChannelCpGameEntryLevelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelCpGameServer).SetChannelCpGameEntryLevel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_cp_game.ChannelCpGame/SetChannelCpGameEntryLevel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelCpGameServer).SetChannelCpGameEntryLevel(ctx, req.(*SetChannelCpGameEntryLevelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelCpGame_GetChannelCpGameEntryLevel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelCpGameEntryLevelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelCpGameServer).GetChannelCpGameEntryLevel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_cp_game.ChannelCpGame/GetChannelCpGameEntryLevel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelCpGameServer).GetChannelCpGameEntryLevel(ctx, req.(*GetChannelCpGameEntryLevelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelCpGame_BatchGetCpGameEntry_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetCpGameEntryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelCpGameServer).BatchGetCpGameEntry(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_cp_game.ChannelCpGame/BatchGetCpGameEntry",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelCpGameServer).BatchGetCpGameEntry(ctx, req.(*BatchGetCpGameEntryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelCpGame_AddCpCardConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddCpCardConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelCpGameServer).AddCpCardConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_cp_game.ChannelCpGame/AddCpCardConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelCpGameServer).AddCpCardConf(ctx, req.(*AddCpCardConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelCpGame_UpdateCpCardConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateCpCardConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelCpGameServer).UpdateCpCardConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_cp_game.ChannelCpGame/UpdateCpCardConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelCpGameServer).UpdateCpCardConf(ctx, req.(*UpdateCpCardConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelCpGame_DelCpCardConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelCpCardConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelCpGameServer).DelCpCardConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_cp_game.ChannelCpGame/DelCpCardConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelCpGameServer).DelCpCardConf(ctx, req.(*DelCpCardConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelCpGame_GetAllCpCardConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllCpCardConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelCpGameServer).GetAllCpCardConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_cp_game.ChannelCpGame/GetAllCpCardConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelCpGameServer).GetAllCpCardConf(ctx, req.(*GetAllCpCardConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelCpGame_GetChannelCpGodRankList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelCpGodRankListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelCpGameServer).GetChannelCpGodRankList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_cp_game.ChannelCpGame/GetChannelCpGodRankList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelCpGameServer).GetChannelCpGodRankList(ctx, req.(*GetChannelCpGodRankListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelCpGame_TestPushCpGameResult_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TestPushCpGameResultReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelCpGameServer).TestPushCpGameResult(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_cp_game.ChannelCpGame/TestPushCpGameResult",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelCpGameServer).TestPushCpGameResult(ctx, req.(*TestPushCpGameResultReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelCpGame_GetCpStrengthHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCpStrengthHistoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelCpGameServer).GetCpStrengthHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_cp_game.ChannelCpGame/GetCpStrengthHistory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelCpGameServer).GetCpStrengthHistory(ctx, req.(*GetCpStrengthHistoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelCpGame_GetCpStrength_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCpStrengthReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelCpGameServer).GetCpStrength(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_cp_game.ChannelCpGame/GetCpStrength",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelCpGameServer).GetCpStrength(ctx, req.(*GetCpStrengthReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelCpGame_GetNameplateInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNameplateInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelCpGameServer).GetNameplateInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_cp_game.ChannelCpGame/GetNameplateInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelCpGameServer).GetNameplateInfo(ctx, req.(*GetNameplateInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelCpGame_MvpAutoHoldMic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MvpAutoHoldMicReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelCpGameServer).MvpAutoHoldMic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_cp_game.ChannelCpGame/MvpAutoHoldMic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelCpGameServer).MvpAutoHoldMic(ctx, req.(*MvpAutoHoldMicReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelCpGame_ChoseCpRare_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChoseCpRareReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelCpGameServer).ChoseCpRare(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_cp_game.ChannelCpGame/ChoseCpRare",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelCpGameServer).ChoseCpRare(ctx, req.(*ChoseCpRareReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelCpGame_BatchGetStrength_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetStrengthReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelCpGameServer).BatchGetStrength(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_cp_game.ChannelCpGame/BatchGetStrength",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelCpGameServer).BatchGetStrength(ctx, req.(*BatchGetStrengthReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelCpGame_AddCpStrengthDelay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddCpStrengthDelayReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelCpGameServer).AddCpStrengthDelay(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_cp_game.ChannelCpGame/AddCpStrengthDelay",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelCpGameServer).AddCpStrengthDelay(ctx, req.(*AddCpStrengthDelayReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelCpGame_AddCpStrengthLogDelay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddCpStrengthLogDelayReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelCpGameServer).AddCpStrengthLogDelay(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_cp_game.ChannelCpGame/AddCpStrengthLogDelay",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelCpGameServer).AddCpStrengthLogDelay(ctx, req.(*AddCpStrengthLogDelayReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelCpGame_ReportCpGameInfoDelay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReportCpGameInfoDelayReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelCpGameServer).ReportCpGameInfoDelay(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_cp_game.ChannelCpGame/ReportCpGameInfoDelay",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelCpGameServer).ReportCpGameInfoDelay(ctx, req.(*ReportCpGameInfoDelayReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelCpGame_TimeRangeCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelCpGameServer).TimeRangeCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_cp_game.ChannelCpGame/TimeRangeCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelCpGameServer).TimeRangeCount(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelCpGame_TimeRangeOrderIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelCpGameServer).TimeRangeOrderIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_cp_game.ChannelCpGame/TimeRangeOrderIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelCpGameServer).TimeRangeOrderIds(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelCpGame_FixPresentCountOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.ReplaceOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelCpGameServer).FixPresentCountOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_cp_game.ChannelCpGame/FixPresentCountOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelCpGameServer).FixPresentCountOrder(ctx, req.(*reconcile_v2.ReplaceOrderReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelCpGame_BatchGetChannelCpGamePhase_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetChannelCpGamePhaseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelCpGameServer).BatchGetChannelCpGamePhase(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_cp_game.ChannelCpGame/BatchGetChannelCpGamePhase",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelCpGameServer).BatchGetChannelCpGamePhase(ctx, req.(*BatchGetChannelCpGamePhaseReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelCpGame_serviceDesc = grpc.ServiceDesc{
	ServiceName: "channel_cp_game.ChannelCpGame",
	HandlerType: (*ChannelCpGameServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetChannelCpGamePhase",
			Handler:    _ChannelCpGame_GetChannelCpGamePhase_Handler,
		},
		{
			MethodName: "SetChannelCpGamePhase",
			Handler:    _ChannelCpGame_SetChannelCpGamePhase_Handler,
		},
		{
			MethodName: "AddCpGamePhaseEndTime",
			Handler:    _ChannelCpGame_AddCpGamePhaseEndTime_Handler,
		},
		{
			MethodName: "GetCurrCpGameInfo",
			Handler:    _ChannelCpGame_GetCurrCpGameInfo_Handler,
		},
		{
			MethodName: "SetChannelCpGameEntryLevel",
			Handler:    _ChannelCpGame_SetChannelCpGameEntryLevel_Handler,
		},
		{
			MethodName: "GetChannelCpGameEntryLevel",
			Handler:    _ChannelCpGame_GetChannelCpGameEntryLevel_Handler,
		},
		{
			MethodName: "BatchGetCpGameEntry",
			Handler:    _ChannelCpGame_BatchGetCpGameEntry_Handler,
		},
		{
			MethodName: "AddCpCardConf",
			Handler:    _ChannelCpGame_AddCpCardConf_Handler,
		},
		{
			MethodName: "UpdateCpCardConf",
			Handler:    _ChannelCpGame_UpdateCpCardConf_Handler,
		},
		{
			MethodName: "DelCpCardConf",
			Handler:    _ChannelCpGame_DelCpCardConf_Handler,
		},
		{
			MethodName: "GetAllCpCardConf",
			Handler:    _ChannelCpGame_GetAllCpCardConf_Handler,
		},
		{
			MethodName: "GetChannelCpGodRankList",
			Handler:    _ChannelCpGame_GetChannelCpGodRankList_Handler,
		},
		{
			MethodName: "TestPushCpGameResult",
			Handler:    _ChannelCpGame_TestPushCpGameResult_Handler,
		},
		{
			MethodName: "GetCpStrengthHistory",
			Handler:    _ChannelCpGame_GetCpStrengthHistory_Handler,
		},
		{
			MethodName: "GetCpStrength",
			Handler:    _ChannelCpGame_GetCpStrength_Handler,
		},
		{
			MethodName: "GetNameplateInfo",
			Handler:    _ChannelCpGame_GetNameplateInfo_Handler,
		},
		{
			MethodName: "MvpAutoHoldMic",
			Handler:    _ChannelCpGame_MvpAutoHoldMic_Handler,
		},
		{
			MethodName: "ChoseCpRare",
			Handler:    _ChannelCpGame_ChoseCpRare_Handler,
		},
		{
			MethodName: "BatchGetStrength",
			Handler:    _ChannelCpGame_BatchGetStrength_Handler,
		},
		{
			MethodName: "AddCpStrengthDelay",
			Handler:    _ChannelCpGame_AddCpStrengthDelay_Handler,
		},
		{
			MethodName: "AddCpStrengthLogDelay",
			Handler:    _ChannelCpGame_AddCpStrengthLogDelay_Handler,
		},
		{
			MethodName: "ReportCpGameInfoDelay",
			Handler:    _ChannelCpGame_ReportCpGameInfoDelay_Handler,
		},
		{
			MethodName: "TimeRangeCount",
			Handler:    _ChannelCpGame_TimeRangeCount_Handler,
		},
		{
			MethodName: "TimeRangeOrderIds",
			Handler:    _ChannelCpGame_TimeRangeOrderIds_Handler,
		},
		{
			MethodName: "FixPresentCountOrder",
			Handler:    _ChannelCpGame_FixPresentCountOrder_Handler,
		},
		{
			MethodName: "BatchGetChannelCpGamePhase",
			Handler:    _ChannelCpGame_BatchGetChannelCpGamePhase_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "channel-cp-game/channel-cp-game.proto",
}

func init() {
	proto.RegisterFile("channel-cp-game/channel-cp-game.proto", fileDescriptor_channel_cp_game_1310697ea80348e6)
}

var fileDescriptor_channel_cp_game_1310697ea80348e6 = []byte{
	// 3405 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x5a, 0xcd, 0x73, 0x1b, 0xc7,
	0x72, 0x27, 0xf8, 0x09, 0x34, 0x00, 0x12, 0x1c, 0x52, 0x24, 0x04, 0x4a, 0x22, 0xbd, 0x8a, 0x64,
	0xc9, 0x1f, 0x94, 0x4d, 0x5b, 0xfe, 0x8c, 0x1d, 0x53, 0x10, 0x4c, 0xb1, 0x44, 0x52, 0xac, 0x05,
	0x29, 0x3b, 0x4e, 0x55, 0xb6, 0x16, 0xbb, 0x43, 0x70, 0xc3, 0xfd, 0x18, 0xed, 0xec, 0x52, 0x62,
	0xe5, 0x90, 0xaa, 0xfc, 0x0b, 0xa9, 0x1c, 0x72, 0x77, 0xe5, 0x94, 0xb3, 0x5d, 0xb9, 0xe4, 0xe3,
	0x9a, 0xca, 0xff, 0xf0, 0xfe, 0x88, 0x57, 0xef, 0xf2, 0x4e, 0xaf, 0xa6, 0x67, 0x77, 0xb1, 0xbb,
	0x58, 0x90, 0xa0, 0xf5, 0xde, 0x85, 0xdc, 0xe9, 0xe9, 0xe9, 0xee, 0x99, 0xe9, 0xe9, 0xf9, 0x75,
	0x0f, 0xe0, 0x9e, 0x71, 0xaa, 0xbb, 0x2e, 0xb5, 0x3f, 0x34, 0xd8, 0x87, 0x7d, 0xdd, 0xa1, 0x8f,
	0x72, 0xed, 0x4d, 0xe6, 0x7b, 0x81, 0x47, 0x16, 0x22, 0xb2, 0x66, 0x30, 0x4d, 0x90, 0x5b, 0xeb,
	0xf4, 0x4d, 0x40, 0x5d, 0x6e, 0x79, 0xee, 0x23, 0x8f, 0x05, 0x96, 0xe7, 0xf2, 0xf8, 0xbf, 0x1c,
	0xd1, 0x5a, 0xf7, 0xa9, 0xe1, 0xb9, 0x86, 0x65, 0xd3, 0x0f, 0xcf, 0xb7, 0x1e, 0xa5, 0x1b, 0x92,
	0x41, 0xf9, 0x16, 0x6e, 0x3f, 0xd1, 0x03, 0xe3, 0x74, 0x87, 0x06, 0x6d, 0x29, 0xbc, 0xcd, 0x76,
	0x74, 0x87, 0x1e, 0x9e, 0xea, 0x9c, 0xaa, 0xf4, 0x15, 0xb9, 0x0d, 0x10, 0x6b, 0xb5, 0xcc, 0x66,
	0x69, 0x63, 0xea, 0x41, 0x5d, 0xad, 0x44, 0x94, 0x5d, 0x53, 0xf9, 0xcf, 0x12, 0xdc, 0xb9, 0x4c,
	0x00, 0x67, 0xe4, 0x27, 0xa8, 0x30, 0xd1, 0xd0, 0x1c, 0x9d, 0xa1, 0x80, 0xea, 0xd6, 0x37, 0x9b,
	0xb9, 0x99, 0x6c, 0x5e, 0x2e, 0x63, 0x13, 0xbf, 0xf6, 0x75, 0xd6, 0x71, 0x03, 0xff, 0x42, 0x2d,
	0xb3, 0xa8, 0xd9, 0xfa, 0x1a, 0xea, 0x99, 0x2e, 0xd2, 0x80, 0xa9, 0x33, 0x7a, 0xd1, 0x2c, 0x6d,
	0x94, 0x1e, 0xd4, 0x55, 0xf1, 0x49, 0x96, 0x61, 0xe6, 0x5c, 0xb7, 0x43, 0xda, 0x9c, 0x44, 0x9a,
	0x6c, 0x7c, 0x35, 0xf9, 0x45, 0x49, 0xf9, 0x12, 0x9a, 0x63, 0x4f, 0xbb, 0x94, 0x9d, 0xf6, 0xc7,
	0x70, 0x73, 0xf4, 0x84, 0x97, 0x61, 0x06, 0x0d, 0x8c, 0x86, 0xc9, 0x86, 0x72, 0x02, 0xcd, 0xee,
	0x6f, 0xd3, 0x36, 0x10, 0x38, 0x99, 0x12, 0x48, 0x6e, 0xc0, 0xac, 0xc7, 0xb4, 0xd0, 0x32, 0x9b,
	0x53, 0x92, 0xec, 0xb1, 0x63, 0xcb, 0x54, 0xd6, 0xe0, 0x66, 0x77, 0x94, 0x69, 0xca, 0x2b, 0x68,
	0x6e, 0x9b, 0x66, 0x8a, 0xda, 0x71, 0xcd, 0x23, 0xcb, 0xf9, 0xed, 0x46, 0x6c, 0x40, 0x4d, 0x37,
	0x4d, 0x2d, 0xb0, 0x1c, 0xaa, 0x71, 0x6a, 0x44, 0xa6, 0x80, 0x6e, 0xa2, 0xd8, 0x2e, 0x35, 0x84,
	0x3d, 0x23, 0x54, 0x72, 0xa6, 0xfc, 0x3d, 0xd4, 0xda, 0x6c, 0x9f, 0x3a, 0x3d, 0xea, 0xef, 0xba,
	0x27, 0x9e, 0xd8, 0xbe, 0x30, 0x51, 0x2e, 0x3e, 0xc5, 0x2c, 0x1d, 0xcb, 0x10, 0x16, 0x45, 0x7a,
	0x1d, 0xcb, 0xd8, 0x35, 0xc9, 0x3d, 0x98, 0x67, 0xd4, 0xe7, 0x9e, 0xab, 0xdb, 0x1a, 0x37, 0x3c,
	0x9f, 0x46, 0x9a, 0xeb, 0x31, 0xb5, 0x2b, 0x88, 0xca, 0x27, 0x50, 0x57, 0x75, 0x9f, 0x6e, 0xbb,
	0x96, 0xa3, 0x8b, 0x73, 0x81, 0x0a, 0x7c, 0x1b, 0x15, 0x54, 0x54, 0xf1, 0x29, 0x28, 0x8e, 0xf9,
	0x18, 0xa5, 0x57, 0x54, 0xf1, 0xa9, 0xfc, 0xfb, 0x34, 0x40, 0x9b, 0x1d, 0x51, 0xdd, 0x41, 0x9b,
	0x56, 0x61, 0x2e, 0xa0, 0xba, 0x13, 0x2f, 0x4a, 0x45, 0x9d, 0x15, 0xcd, 0x5d, 0x93, 0x7c, 0x01,
	0x65, 0x87, 0x3a, 0x9a, 0x6d, 0xf1, 0xa0, 0x39, 0x89, 0x7e, 0x7d, 0x7b, 0xc8, 0xaf, 0xd3, 0xb3,
	0x53, 0xe7, 0x1c, 0xea, 0xec, 0x59, 0x3c, 0x10, 0x6b, 0x99, 0x36, 0x5a, 0x36, 0xc8, 0x3b, 0x50,
	0xb3, 0xb8, 0x66, 0xea, 0x6e, 0x9f, 0xfa, 0x5e, 0xc8, 0x9b, 0xd3, 0x1b, 0xa5, 0x07, 0x65, 0xb5,
	0x6a, 0xf1, 0xa7, 0x31, 0x49, 0xec, 0xd1, 0x6b, 0xcb, 0xed, 0x6b, 0x36, 0x3d, 0xa7, 0x76, 0x73,
	0x46, 0xee, 0x91, 0xa0, 0xec, 0x09, 0x02, 0x79, 0x1f, 0x48, 0xe0, 0x69, 0x2e, 0x7d, 0x13, 0x48,
	0x0e, 0xcd, 0xa4, 0xdc, 0x68, 0xce, 0xa2, 0xd5, 0x0b, 0x81, 0x77, 0x40, 0xdf, 0x04, 0xc8, 0xf8,
	0x94, 0x72, 0x83, 0xdc, 0x87, 0x05, 0xd4, 0xab, 0x85, 0xcc, 0xd4, 0x03, 0xaa, 0xe9, 0x41, 0x73,
	0x4e, 0xae, 0x21, 0x92, 0x8f, 0x91, 0xba, 0x1d, 0x90, 0x3d, 0x58, 0x34, 0x98, 0xe6, 0xea, 0x0e,
	0x65, 0xb6, 0xe0, 0xb3, 0xdc, 0x13, 0xaf, 0x59, 0xde, 0x28, 0x3d, 0xa8, 0x6e, 0x6d, 0x14, 0xcc,
	0xf7, 0x20, 0x66, 0xc4, 0x29, 0x2f, 0x18, 0x59, 0x82, 0x70, 0x18, 0xb4, 0xcf, 0x60, 0xd2, 0xb8,
	0x0a, 0x1a, 0x07, 0x82, 0xd6, 0x66, 0x68, 0x57, 0x07, 0xe6, 0x7d, 0xdd, 0xa7, 0x9a, 0x1e, 0x6f,
	0x5a, 0x13, 0x50, 0xd9, 0x9d, 0x21, 0x65, 0x99, 0xad, 0x55, 0xeb, 0x7e, 0x66, 0xa7, 0xef, 0xc3,
	0x82, 0x4f, 0x75, 0xe3, 0x54, 0x43, 0x61, 0xa8, 0xab, 0x8a, 0xba, 0xea, 0x48, 0x16, 0x63, 0x51,
	0xdd, 0x1d, 0xa8, 0x22, 0x47, 0xaf, 0xaf, 0x09, 0xcf, 0xa8, 0x21, 0x4f, 0x45, 0x90, 0x9e, 0xf4,
	0x8f, 0x7d, 0x9b, 0xdc, 0x84, 0xb2, 0x94, 0xa0, 0x5f, 0x34, 0xeb, 0xb8, 0x3e, 0x73, 0xa2, 0xfd,
	0x54, 0xbf, 0x50, 0x3e, 0x87, 0xf2, 0x9e, 0xc7, 0xe9, 0x08, 0xcf, 0xbd, 0x09, 0x65, 0xcb, 0xf0,
	0x5c, 0x94, 0x2a, 0xbd, 0x6b, 0x4e, 0xb4, 0x8f, 0x7d, 0x5b, 0xf9, 0x8f, 0x12, 0xd4, 0x84, 0x01,
	0x57, 0xfb, 0x58, 0x03, 0xa6, 0x84, 0x62, 0xe9, 0xfb, 0xe2, 0x53, 0x78, 0x89, 0x71, 0xea, 0x71,
	0xaa, 0xf1, 0x40, 0x0f, 0x42, 0x1e, 0xb9, 0x50, 0x15, 0x69, 0x5d, 0x24, 0x0d, 0xdc, 0x6b, 0x3a,
	0xed, 0x5e, 0x69, 0x77, 0x9d, 0xb9, 0x8e, 0xbb, 0x2a, 0xbf, 0x9f, 0x15, 0x07, 0x42, 0x1c, 0x60,
	0x34, 0xf6, 0xb7, 0x06, 0x0a, 0x79, 0x0b, 0x50, 0xd7, 0xd4, 0x82, 0xd8, 0x6c, 0x60, 0x71, 0x44,
	0xe0, 0xe4, 0x53, 0x98, 0x33, 0x98, 0x34, 0x6f, 0x1a, 0xcd, 0x5b, 0x2b, 0x30, 0x2f, 0x5e, 0x31,
	0x75, 0xd6, 0x60, 0x78, 0x94, 0x84, 0x17, 0x53, 0xff, 0x9c, 0xfa, 0x83, 0x18, 0x34, 0x13, 0x79,
	0x31, 0x92, 0xa3, 0x30, 0x44, 0x1e, 0xc2, 0xa2, 0x4f, 0x79, 0xa0, 0x65, 0xa2, 0xd5, 0x2c, 0x72,
	0xce, 0x8b, 0x8e, 0xed, 0x24, 0x62, 0x91, 0xcf, 0xa0, 0x99, 0xe6, 0xd2, 0xe4, 0x95, 0x2a, 0x2d,
	0x9b, 0xc3, 0x0b, 0x70, 0x79, 0x10, 0xdf, 0x5e, 0x60, 0x27, 0x9a, 0xb2, 0x0a, 0x73, 0xce, 0xb9,
	0x8c, 0xc8, 0x65, 0x14, 0x3c, 0xeb, 0x9c, 0x8b, 0x90, 0x4c, 0xbe, 0x85, 0x5a, 0xdf, 0x33, 0x35,
	0x5f, 0x77, 0xcf, 0xb4, 0xc0, 0x63, 0xe8, 0xf3, 0xd5, 0xad, 0x5b, 0x05, 0xd3, 0xc3, 0xa8, 0x85,
	0xf3, 0x83, 0xbe, 0x67, 0xaa, 0xba, 0x7b, 0x76, 0xe4, 0x31, 0x21, 0x58, 0xf4, 0x8b, 0xd5, 0x06,
	0x29, 0x58, 0x34, 0x77, 0x4d, 0xa2, 0x40, 0xdd, 0x38, 0xd5, 0x7d, 0x47, 0x8b, 0xf5, 0x56, 0x63,
	0x67, 0xd0, 0x7d, 0x67, 0x5f, 0x2a, 0xdf, 0x80, 0x5a, 0x32, 0x71, 0xc3, 0x0d, 0xd0, 0xc1, 0xeb,
	0x2a, 0x44, 0x73, 0x6e, 0xbb, 0x01, 0x6e, 0xcd, 0x99, 0xc6, 0xc3, 0x9e, 0x26, 0xf7, 0xad, 0x1e,
	0x6d, 0xcd, 0x59, 0x37, 0xec, 0x61, 0xc4, 0x26, 0x9f, 0x41, 0xc5, 0x16, 0x2e, 0x87, 0x4b, 0x30,
	0x8f, 0x9b, 0x73, 0x73, 0xc8, 0xfa, 0xf8, 0x28, 0xa8, 0x65, 0xc1, 0x8b, 0x2b, 0xa2, 0x40, 0x5d,
	0x58, 0x26, 0x6d, 0x3c, 0xd7, 0xed, 0xe6, 0x82, 0xb4, 0xcf, 0x39, 0x67, 0x6d, 0x41, 0x7b, 0xa9,
	0xdb, 0x64, 0x1d, 0x44, 0x53, 0x73, 0x2c, 0x63, 0x4b, 0xcc, 0xa0, 0x21, 0x95, 0x3b, 0xe7, 0x6c,
	0x5f, 0x52, 0xc8, 0x57, 0x80, 0xa7, 0x51, 0x2a, 0x5f, 0x1c, 0xe1, 0xb8, 0xe9, 0xd3, 0xa4, 0xe2,
	0x81, 0x45, 0x03, 0xbe, 0x80, 0x66, 0x12, 0x46, 0xb4, 0x9e, 0x17, 0xba, 0xa6, 0xee, 0x5f, 0x68,
	0x12, 0x0f, 0x10, 0xd4, 0xb4, 0x92, 0xf4, 0x3f, 0x89, 0xba, 0x5f, 0x8a, 0x5e, 0xd2, 0x85, 0x65,
	0x43, 0x58, 0x2e, 0xa6, 0x8d, 0xea, 0x7d, 0xca, 0x43, 0x3b, 0x68, 0x2e, 0xe1, 0xde, 0x29, 0x05,
	0x7b, 0xd7, 0x16, 0xbc, 0xc2, 0x0e, 0x15, 0x39, 0xd5, 0x45, 0x23, 0x4f, 0x52, 0xde, 0x07, 0x38,
	0x7c, 0x9e, 0xac, 0x2a, 0xc0, 0x6c, 0xdb, 0x73, 0x1c, 0xcf, 0x6d, 0x4c, 0x90, 0x45, 0xa8, 0xff,
	0xa0, 0x5b, 0xc1, 0x91, 0x17, 0xf9, 0x61, 0xa3, 0xa4, 0x3c, 0x86, 0x65, 0x81, 0x31, 0x42, 0xdf,
	0x1f, 0x9c, 0xbd, 0x31, 0xa0, 0xc9, 0x4b, 0xb8, 0x51, 0x30, 0x8c, 0x33, 0xf2, 0x0d, 0xd4, 0x22,
	0x63, 0x65, 0x08, 0x2f, 0xe1, 0x4c, 0x8a, 0x0e, 0x59, 0x32, 0x0c, 0x8c, 0xe4, 0x5b, 0xf9, 0xe7,
	0x12, 0x90, 0x0c, 0xaa, 0x90, 0x80, 0xeb, 0xea, 0x60, 0x20, 0xef, 0xaa, 0x28, 0x18, 0x60, 0x83,
	0xac, 0x41, 0x65, 0x70, 0xe9, 0xc8, 0x48, 0x50, 0x0e, 0xe3, 0xfb, 0xa6, 0x05, 0x65, 0x8f, 0x51,
	0x5f, 0x0f, 0x3c, 0x1f, 0x03, 0x58, 0x45, 0x4d, 0xda, 0x0a, 0x83, 0xdb, 0x79, 0x70, 0x83, 0x66,
	0xe0, 0xad, 0x36, 0x1e, 0x88, 0x29, 0x30, 0x27, 0xad, 0x71, 0x2a, 0xa7, 0x71, 0x03, 0xee, 0x5c,
	0xa6, 0x91, 0x33, 0x01, 0xa1, 0x77, 0xde, 0xc2, 0x26, 0xe5, 0x33, 0xb8, 0xb3, 0x73, 0xa9, 0x86,
	0x81, 0xd5, 0xa5, 0x94, 0xd5, 0xca, 0x4f, 0xb0, 0x92, 0xa0, 0xe6, 0xc1, 0x28, 0xa1, 0xb0, 0x90,
	0x5f, 0x50, 0x7b, 0xb4, 0x6f, 0xb9, 0xf1, 0xdc, 0xb1, 0x81, 0xbc, 0x96, 0x63, 0xc5, 0xdb, 0x20,
	0x1b, 0xca, 0x29, 0xac, 0x16, 0xca, 0xe6, 0x8c, 0x7c, 0x0e, 0xd3, 0x78, 0x12, 0x25, 0x92, 0xbf,
	0x3b, 0xec, 0x3e, 0x43, 0x13, 0x51, 0x71, 0x80, 0xd0, 0x14, 0x78, 0x81, 0x9e, 0xac, 0x3d, 0x36,
	0x94, 0xff, 0x2d, 0xc1, 0x7c, 0x9b, 0xb5, 0x75, 0xdf, 0x54, 0x29, 0xf7, 0x42, 0xdf, 0xa0, 0xe4,
	0x23, 0x58, 0x8e, 0x85, 0xfa, 0x11, 0x4d, 0x1b, 0x80, 0x36, 0x12, 0xf5, 0xc5, 0xec, 0xe2, 0x8e,
	0x5e, 0x81, 0x59, 0xc7, 0x7c, 0xcc, 0x43, 0x27, 0xba, 0x68, 0xa3, 0x16, 0x79, 0x17, 0x1a, 0x96,
	0xa3, 0x31, 0xcb, 0xd0, 0xb8, 0xa3, 0xdb, 0x36, 0x4a, 0x91, 0x1b, 0x5c, 0xb7, 0x9c, 0x43, 0xcb,
	0xe8, 0x0a, 0xaa, 0x10, 0x70, 0x0b, 0x20, 0x62, 0x14, 0x2c, 0x91, 0xd7, 0x21, 0x8b, 0xe8, 0x45,
	0x88, 0xe0, 0x9e, 0xc5, 0x10, 0x61, 0x26, 0x86, 0x08, 0xee, 0x19, 0x42, 0x04, 0xe5, 0xbf, 0x27,
	0xc5, 0xfd, 0x28, 0xe6, 0xd0, 0xf6, 0xdc, 0x13, 0x32, 0x0f, 0x93, 0xc9, 0x3e, 0x4f, 0x5a, 0x26,
	0x21, 0x30, 0x2d, 0xd0, 0x53, 0x64, 0x1b, 0x7e, 0x8b, 0x13, 0xe0, 0x58, 0x6e, 0x06, 0xba, 0x96,
	0x1d, 0xcb, 0xc5, 0xf8, 0x9f, 0x3d, 0x1e, 0xd3, 0x97, 0x1c, 0x8f, 0x99, 0xac, 0xb3, 0x92, 0xa7,
	0x50, 0x4f, 0x56, 0x0c, 0x37, 0x69, 0x16, 0x37, 0x69, 0xbd, 0x28, 0x5a, 0xa5, 0x56, 0x5c, 0xad,
	0xc5, 0xa3, 0x30, 0x68, 0x7e, 0x00, 0x24, 0x91, 0x82, 0x9b, 0x84, 0xb3, 0x9e, 0x43, 0x5d, 0x8d,
	0xb8, 0xe7, 0x48, 0x74, 0x88, 0xc9, 0xb7, 0x61, 0xbe, 0x1b, 0xf6, 0x62, 0x51, 0xbb, 0xe6, 0x1b,
	0x52, 0x87, 0xca, 0xbe, 0x6e, 0x53, 0x4d, 0xfc, 0x69, 0x4c, 0x90, 0x05, 0xa8, 0x62, 0xf3, 0x7b,
	0xea, 0x08, 0x42, 0x49, 0xc4, 0x3a, 0xf9, 0x1d, 0x93, 0x26, 0x95, 0xef, 0xa0, 0xbe, 0x6d, 0xdb,
	0xa9, 0x35, 0x7c, 0x94, 0xf1, 0xb2, 0xb5, 0x11, 0x13, 0x10, 0xac, 0xd2, 0xbb, 0x94, 0x36, 0x34,
	0x30, 0xcd, 0x48, 0xc8, 0xf4, 0x95, 0x10, 0x62, 0x78, 0xee, 0xc9, 0x25, 0x91, 0x6e, 0x20, 0x44,
	0x30, 0x2a, 0x4b, 0xb0, 0x98, 0x13, 0xc2, 0x99, 0xf2, 0x3d, 0x2c, 0x49, 0x2c, 0xfc, 0x96, 0xc2,
	0x57, 0x60, 0x79, 0x58, 0x0e, 0x67, 0x8a, 0x02, 0x8d, 0xa7, 0xd4, 0xce, 0x0a, 0xcf, 0xb9, 0x90,
	0x30, 0x2c, 0xc7, 0xc3, 0x99, 0x72, 0x03, 0x96, 0x76, 0x68, 0x90, 0x59, 0x37, 0x95, 0xbe, 0x52,
	0x76, 0xf0, 0xde, 0xc8, 0x91, 0x39, 0xbb, 0xfe, 0x92, 0xfe, 0x5f, 0x09, 0xaa, 0xc7, 0x9c, 0xfa,
	0x87, 0xbe, 0x77, 0x62, 0xd9, 0xb4, 0x00, 0xe2, 0x36, 0x61, 0x4e, 0x37, 0x0c, 0x2f, 0x74, 0x83,
	0x18, 0xe1, 0x46, 0x4d, 0xe1, 0xa5, 0xae, 0x65, 0x9c, 0xa1, 0xdf, 0x47, 0x21, 0x35, 0x6e, 0x93,
	0xbb, 0x50, 0x8f, 0xd8, 0x34, 0xdd, 0xb6, 0x74, 0x1e, 0x9d, 0xb7, 0x5a, 0x44, 0xdc, 0x16, 0x34,
	0xa1, 0x8c, 0xd3, 0x37, 0x11, 0x96, 0x13, 0x9f, 0xe4, 0xaf, 0xa1, 0xc2, 0x7c, 0xeb, 0xdc, 0xb2,
	0x69, 0x9f, 0x22, 0x72, 0x2b, 0x4a, 0x09, 0xa4, 0xbd, 0x11, 0x97, 0x3a, 0x18, 0xa0, 0x70, 0xa8,
	0x67, 0xfa, 0xd2, 0xb6, 0x97, 0x46, 0xdb, 0x3e, 0x99, 0xb3, 0x9d, 0xc0, 0x74, 0x70, 0xc1, 0xe2,
	0x23, 0x8b, 0xdf, 0x42, 0x52, 0x54, 0x75, 0xc1, 0x99, 0xd4, 0xd4, 0xb8, 0xa9, 0xfc, 0xcf, 0x14,
	0x54, 0x53, 0xa0, 0xee, 0xaa, 0xdb, 0x69, 0x09, 0x66, 0x42, 0xcb, 0xd4, 0xf4, 0x28, 0x42, 0x4e,
	0x87, 0x96, 0xb9, 0x1d, 0x13, 0x7b, 0xb1, 0xca, 0xd0, 0x32, 0x9f, 0x8c, 0x40, 0xf8, 0x6b, 0x50,
	0x31, 0x7c, 0x2a, 0xe2, 0x46, 0xc0, 0xa3, 0x95, 0x2b, 0x4b, 0xc2, 0x11, 0x17, 0x89, 0x74, 0x14,
	0xbf, 0x64, 0x3e, 0x38, 0xd3, 0xc3, 0xf4, 0x66, 0x03, 0x6a, 0x5c, 0x40, 0x72, 0xc4, 0x44, 0x9a,
	0x1e, 0xa5, 0x80, 0x20, 0x68, 0x08, 0x84, 0xb6, 0x73, 0x1c, 0xbd, 0x08, 0xdb, 0x0e, 0x38, 0x9e,
	0xa4, 0x81, 0x6f, 0x25, 0x03, 0x7c, 0xff, 0x06, 0x6a, 0x21, 0xa7, 0xbe, 0xc6, 0xa4, 0x07, 0x45,
	0x89, 0xdc, 0xad, 0x11, 0xbb, 0x86, 0x3c, 0x6a, 0x35, 0x4c, 0xb9, 0xdc, 0x53, 0x58, 0x30, 0x98,
	0x96, 0x91, 0x51, 0x1d, 0x43, 0x46, 0xdd, 0x60, 0x69, 0xc7, 0xbd, 0x05, 0x80, 0xeb, 0xaa, 0xe1,
	0xd6, 0xd5, 0xa2, 0x80, 0x6a, 0x99, 0xdb, 0x47, 0x62, 0xfb, 0xa2, 0xde, 0x9e, 0xec, 0xad, 0x27,
	0xbd, 0x4f, 0x44, 0xaf, 0xf2, 0x35, 0xb4, 0x32, 0xb7, 0xb3, 0x44, 0xe5, 0x22, 0x4e, 0x8e, 0x71,
	0xb5, 0xbf, 0x80, 0xb5, 0x91, 0x83, 0x39, 0x23, 0x1f, 0x65, 0x4e, 0xe4, 0xe5, 0xf9, 0x80, 0x3c,
	0x92, 0xff, 0x56, 0x82, 0xd5, 0x23, 0xca, 0x83, 0xc3, 0x90, 0x9f, 0xca, 0x1b, 0x36, 0x02, 0x9b,
	0x57, 0x43, 0x9f, 0xb7, 0x75, 0xae, 0x75, 0xa8, 0x4a, 0x08, 0x8c, 0x89, 0x51, 0xe4, 0x5e, 0x20,
	0x49, 0x02, 0xb1, 0x2a, 0x2d, 0x68, 0x16, 0x9b, 0xc6, 0x99, 0x12, 0xc2, 0x2a, 0x42, 0x89, 0x6e,
	0xe0, 0x53, 0xb7, 0x1f, 0x9c, 0x3e, 0xb3, 0x78, 0xe0, 0x49, 0xb0, 0x72, 0x03, 0x66, 0x9d, 0x0b,
	0x6d, 0x10, 0x58, 0x66, 0x9c, 0x8b, 0x63, 0x59, 0xf7, 0x09, 0x3c, 0x24, 0x27, 0x70, 0x41, 0x90,
	0x57, 0x60, 0xd6, 0x3b, 0x39, 0xe1, 0x34, 0xc6, 0x2b, 0x51, 0x6b, 0x00, 0x63, 0xa6, 0xd3, 0x30,
	0xe6, 0x6f, 0x61, 0x21, 0xa7, 0x51, 0x1c, 0x60, 0x11, 0x83, 0xa3, 0x33, 0x8f, 0xdf, 0x42, 0xa8,
	0x4f, 0x75, 0xee, 0xb9, 0x31, 0x7c, 0x90, 0x2d, 0x11, 0x08, 0x78, 0x34, 0x1c, 0xd5, 0xcd, 0xa8,
	0x49, 0x5b, 0xa4, 0xf0, 0xcd, 0xe2, 0x29, 0x71, 0xf6, 0x97, 0x9c, 0x13, 0xf9, 0x34, 0x72, 0x1a,
	0x99, 0xc2, 0x0f, 0x57, 0x60, 0xf2, 0xf6, 0x48, 0xc7, 0xf9, 0x0e, 0x1a, 0x19, 0x6b, 0xaf, 0xbd,
	0xf2, 0xca, 0x7f, 0x95, 0x60, 0x31, 0x27, 0xe2, 0xda, 0x33, 0xcd, 0x2f, 0x68, 0x7d, 0xb0, 0xa0,
	0xe4, 0x1d, 0xa8, 0x85, 0xae, 0xf5, 0x2a, 0xa4, 0x1a, 0x16, 0x8b, 0xa2, 0x4b, 0xa1, 0x2a, 0x69,
	0x87, 0x82, 0x84, 0xa9, 0x62, 0x68, 0x07, 0x56, 0xc4, 0x21, 0xd1, 0x0f, 0x20, 0x49, 0x32, 0xac,
	0x41, 0xe5, 0x35, 0xed, 0x45, 0xdd, 0x32, 0xcc, 0x95, 0x5f, 0xd3, 0x1e, 0x76, 0x2a, 0x3f, 0xc2,
	0x42, 0xae, 0x3a, 0x35, 0x02, 0x28, 0x47, 0x35, 0xc2, 0xc9, 0x41, 0x8d, 0xf0, 0x36, 0x80, 0xc5,
	0xb5, 0x90, 0xf5, 0x7d, 0xdd, 0x94, 0xb1, 0xbf, 0xac, 0x56, 0x2c, 0x7e, 0x2c, 0x09, 0xca, 0x73,
	0xbc, 0x88, 0xb3, 0x85, 0xaf, 0xd1, 0xeb, 0x7b, 0x1b, 0xe0, 0x84, 0xda, 0xb6, 0xf7, 0x3a, 0xb5,
	0x3e, 0x15, 0x49, 0x11, 0xeb, 0xfc, 0xbb, 0x12, 0xde, 0xdf, 0x39, 0x69, 0x9c, 0xe1, 0xf9, 0x0e,
	0x7d, 0x9f, 0xba, 0x81, 0x66, 0x9f, 0x27, 0xe7, 0x5b, 0x52, 0xf6, 0xce, 0xc9, 0x43, 0x68, 0xc4,
	0xdd, 0xc9, 0x1a, 0x4b, 0xe1, 0x0b, 0x11, 0x3d, 0xde, 0x38, 0xb2, 0x09, 0x4b, 0x03, 0x49, 0x5a,
	0x6e, 0x47, 0x16, 0x13, 0x91, 0x09, 0xff, 0x03, 0x68, 0xc8, 0x9a, 0x62, 0x8a, 0x59, 0xfa, 0xe4,
	0xbc, 0xa0, 0xa7, 0x38, 0xef, 0x42, 0x7d, 0x50, 0x28, 0x1c, 0x60, 0xe5, 0x5a, 0x42, 0x14, 0x88,
	0xf1, 0x39, 0x2c, 0xee, 0x9f, 0xb3, 0xed, 0x30, 0xf0, 0x9e, 0x79, 0xb6, 0xb9, 0x6f, 0x19, 0x63,
	0x44, 0xaf, 0xd4, 0x15, 0x33, 0x99, 0xbe, 0x62, 0x94, 0x65, 0x20, 0x79, 0x61, 0x9c, 0x29, 0xff,
	0x08, 0x95, 0x24, 0xf7, 0xbe, 0x16, 0x6e, 0xb9, 0x03, 0x55, 0x1e, 0xf6, 0x64, 0xc6, 0x9f, 0x54,
	0xd6, 0x2b, 0x3c, 0xec, 0x09, 0x49, 0xb2, 0xe2, 0x92, 0xf4, 0x23, 0x40, 0x88, 0xdc, 0x34, 0xe2,
	0x10, 0xbb, 0xa6, 0xfc, 0x22, 0x52, 0x1a, 0xa1, 0xbd, 0xcd, 0x64, 0xee, 0x3f, 0xce, 0xec, 0xe2,
	0xf2, 0xdf, 0x64, 0xa6, 0xfc, 0xb7, 0x0a, 0x73, 0x59, 0x53, 0x66, 0x7d, 0x69, 0xc7, 0x97, 0x42,
	0x60, 0x5c, 0x9b, 0x88, 0xea, 0x65, 0xad, 0x82, 0x5c, 0x2c, 0xae, 0x3f, 0x54, 0x8c, 0x64, 0x39,
	0x9a, 0x30, 0x27, 0xf0, 0xa8, 0xe5, 0x3b, 0xb8, 0x3b, 0x65, 0x35, 0x6e, 0x2a, 0x8b, 0xb0, 0x90,
	0xb1, 0x1b, 0x93, 0xdb, 0xa5, 0x38, 0x11, 0x4c, 0x87, 0x8e, 0xc2, 0x3a, 0x7d, 0x72, 0xe2, 0xa7,
	0x06, 0x51, 0xe3, 0x67, 0x4c, 0xef, 0xe2, 0xa1, 0x78, 0xe8, 0x06, 0x9c, 0xa5, 0x51, 0xb1, 0x61,
	0xf2, 0x8a, 0xd8, 0x30, 0x75, 0x65, 0x6c, 0x98, 0xbe, 0x3c, 0x36, 0xcc, 0xe4, 0x62, 0xc3, 0x1f,
	0x4a, 0xb0, 0x3c, 0x3c, 0x4f, 0xce, 0x0a, 0x26, 0x7a, 0x02, 0x8b, 0x8e, 0xce, 0x92, 0x83, 0x20,
	0x6b, 0x29, 0xb2, 0xfc, 0xff, 0xd5, 0xc8, 0x67, 0xad, 0xb4, 0xcc, 0xcd, 0x7d, 0x3d, 0xb3, 0x20,
	0x32, 0x47, 0x5e, 0x70, 0xb2, 0xd4, 0x96, 0x01, 0xcb, 0x45, 0x8c, 0x05, 0x2f, 0x5c, 0x8f, 0xd3,
	0x2f, 0x5c, 0xc5, 0xd9, 0x5e, 0x5a, 0x4c, 0xfa, 0x09, 0xec, 0xd7, 0x12, 0x2c, 0x0e, 0x55, 0xae,
	0xfe, 0xfc, 0xde, 0xfa, 0x79, 0xba, 0x84, 0x77, 0xb5, 0xb3, 0x0e, 0xea, 0x77, 0xa3, 0x7d, 0x35,
	0x80, 0x1b, 0x98, 0xaa, 0xc5, 0x33, 0x7b, 0x4a, 0x6d, 0xfd, 0xe2, 0x6a, 0xd7, 0x1c, 0xf3, 0x32,
	0x1a, 0x20, 0x82, 0xe9, 0x34, 0x22, 0x50, 0x9a, 0xb0, 0x52, 0xa4, 0x95, 0x33, 0xe5, 0x5f, 0x4a,
	0xd1, 0xd3, 0x5a, 0xdc, 0xb5, 0xe7, 0xf5, 0xaf, 0x6f, 0xd3, 0x1a, 0x54, 0x02, 0x9d, 0x9f, 0x69,
	0xa9, 0x1c, 0xa3, 0x2c, 0x08, 0x08, 0x54, 0xaf, 0x02, 0xfd, 0x7a, 0x90, 0x05, 0xfd, 0xdb, 0x41,
	0xf2, 0xf8, 0x36, 0x6c, 0x14, 0x67, 0xca, 0x2f, 0x93, 0xd0, 0x54, 0x29, 0xf3, 0xfc, 0x60, 0x50,
	0xf2, 0x4b, 0x4c, 0x7e, 0xbb, 0x6a, 0x21, 0x69, 0xc3, 0xbc, 0xc1, 0x34, 0x07, 0x1f, 0x13, 0xd2,
	0x47, 0xe4, 0x8a, 0x27, 0x87, 0x9a, 0x91, 0x7e, 0x0d, 0x2c, 0x7e, 0x26, 0x5b, 0x87, 0xaa, 0x7c,
	0xdc, 0x4a, 0x2f, 0x06, 0x20, 0x49, 0x96, 0x4f, 0x08, 0x4c, 0xfb, 0xba, 0x7b, 0x16, 0x2d, 0x06,
	0x7e, 0x8b, 0x8b, 0x29, 0xaa, 0x68, 0xb0, 0x53, 0x5f, 0xe7, 0x34, 0x2a, 0xfd, 0xd7, 0x24, 0xf1,
	0x10, 0x69, 0xe4, 0x3e, 0x2c, 0xe8, 0x8c, 0xe1, 0x03, 0x45, 0xfc, 0x42, 0x10, 0xbd, 0x88, 0x49,
	0x72, 0xea, 0x49, 0x73, 0xc4, 0xba, 0x71, 0xf6, 0xde, 0xbf, 0x96, 0x04, 0xce, 0x48, 0x5e, 0x3b,
	0x71, 0xe7, 0x56, 0x61, 0xa9, 0x7d, 0xa8, 0xed, 0x6c, 0xef, 0x77, 0xb4, 0xc3, 0x67, 0xdb, 0xdd,
	0x8e, 0xd6, 0xde, 0x7b, 0xd1, 0xed, 0x34, 0x26, 0xc8, 0x1a, 0xac, 0xe6, 0x3a, 0xd4, 0xce, 0xf6,
	0x51, 0x47, 0x6b, 0x1f, 0x36, 0x4a, 0x64, 0x19, 0x1a, 0xd9, 0xce, 0xc3, 0xe7, 0x8d, 0x49, 0xd2,
	0x84, 0xe5, 0x1c, 0xf5, 0xf8, 0x60, 0xb7, 0xfb, 0xac, 0x31, 0x45, 0x6e, 0x41, 0x33, 0x27, 0xec,
	0xd9, 0x8b, 0x6e, 0x47, 0x53, 0xb7, 0xd5, 0x4e, 0x63, 0xfa, 0x3d, 0x15, 0x48, 0x27, 0x93, 0x01,
	0xa3, 0x65, 0xeb, 0xb0, 0xd6, 0x39, 0x38, 0xde, 0xd7, 0x8e, 0xbb, 0x1d, 0x55, 0x3b, 0x54, 0x77,
	0x5f, 0xee, 0xee, 0x75, 0x76, 0x3a, 0xda, 0xf1, 0xc1, 0xf3, 0x83, 0x17, 0x3f, 0x1c, 0x48, 0x0b,
	0x0b, 0x19, 0x9e, 0xff, 0xd0, 0x28, 0x6d, 0xfd, 0x7c, 0x03, 0xea, 0x99, 0x7a, 0x1f, 0x61, 0xb2,
	0xfa, 0x3c, 0xf4, 0xfa, 0x4c, 0x1e, 0x0e, 0x6d, 0xfc, 0xa8, 0xb7, 0xf7, 0xd6, 0x7b, 0xe3, 0xb2,
	0x72, 0xa6, 0x4c, 0x08, 0x8d, 0xdd, 0x31, 0x35, 0x76, 0xc7, 0xd7, 0xd8, 0xbd, 0x5c, 0x63, 0xe1,
	0x8b, 0x76, 0x81, 0xc6, 0x51, 0x8f, 0xed, 0x05, 0x1a, 0x47, 0x3f, 0x92, 0x4f, 0x10, 0x53, 0x42,
	0xef, 0x4c, 0x4d, 0x9f, 0xdc, 0x2b, 0x5c, 0xa6, 0xfc, 0x73, 0x41, 0xeb, 0xfe, 0x38, 0x6c, 0xa8,
	0xe5, 0x9f, 0xa0, 0x35, 0xba, 0xd4, 0x4d, 0x36, 0xaf, 0x5c, 0xa3, 0x4c, 0xd5, 0xbb, 0xf5, 0xe8,
	0x5a, 0xfc, 0xb1, 0x01, 0x3b, 0xd7, 0x31, 0x60, 0xe7, 0x9a, 0x06, 0xec, 0x5c, 0x65, 0xc0, 0x3f,
	0x0c, 0xd0, 0x4e, 0xfa, 0x8d, 0xe3, 0xdd, 0xd1, 0x3f, 0x57, 0xc9, 0x14, 0xde, 0x5b, 0x0f, 0xc6,
	0x63, 0x44, 0x5d, 0x3f, 0x42, 0x3d, 0x53, 0x6b, 0x24, 0xef, 0x14, 0xbb, 0x44, 0xaa, 0xb4, 0xd7,
	0x52, 0xae, 0x62, 0x41, 0xc9, 0x3a, 0x34, 0xf2, 0x85, 0x46, 0xf2, 0x57, 0xc3, 0xf5, 0x92, 0xe1,
	0x9a, 0x66, 0xeb, 0xde, 0x18, 0x5c, 0xb1, 0xf1, 0x99, 0x7a, 0x64, 0x81, 0xf1, 0xf9, 0x9a, 0x66,
	0x81, 0xf1, 0xc3, 0x25, 0x4d, 0x34, 0x3e, 0x5f, 0xbd, 0x2c, 0x30, 0xbe, 0xa0, 0xee, 0xd9, 0xba,
	0x37, 0x06, 0x17, 0xaa, 0x38, 0x97, 0xc5, 0x88, 0x82, 0xaa, 0x0c, 0x79, 0xff, 0x72, 0x9f, 0xc9,
	0x14, 0x7f, 0x5a, 0x1f, 0x8c, 0xcf, 0x8c, 0x7a, 0x1d, 0x58, 0x2e, 0x2a, 0x90, 0x90, 0x61, 0xaf,
	0x19, 0x51, 0xe2, 0x69, 0x3d, 0x1c, 0x93, 0x33, 0x56, 0x57, 0x54, 0xa0, 0x28, 0x50, 0x37, 0xa2,
	0x34, 0xd3, 0x7a, 0x38, 0x26, 0x67, 0xec, 0x12, 0x99, 0xde, 0x02, 0x97, 0xc8, 0x57, 0x20, 0x0a,
	0x5c, 0x62, 0xa8, 0xc2, 0x90, 0xb8, 0x44, 0x36, 0x73, 0x2f, 0x74, 0x89, 0x7c, 0x06, 0x5e, 0xec,
	0x12, 0x43, 0x99, 0xb5, 0x32, 0x41, 0xfe, 0x0e, 0xe6, 0xb3, 0x59, 0x24, 0x19, 0x36, 0x6d, 0x28,
	0x67, 0x6d, 0xdd, 0xbd, 0x92, 0x07, 0x85, 0xab, 0x50, 0x4d, 0xa5, 0x55, 0x64, 0xbd, 0x18, 0xf9,
	0x26, 0xc9, 0x62, 0x6b, 0xe3, 0x72, 0x86, 0x78, 0x4d, 0xf2, 0xb9, 0x45, 0xc1, 0x9a, 0x14, 0xa4,
	0x6e, 0x05, 0x6b, 0x52, 0x94, 0xa4, 0x28, 0x13, 0xa4, 0x0f, 0x64, 0x18, 0xeb, 0x92, 0xfb, 0xc5,
	0x21, 0x28, 0x0f, 0xc3, 0x5b, 0xef, 0x8e, 0xc5, 0x97, 0xb9, 0x4f, 0xf3, 0x20, 0x75, 0xd4, 0x7d,
	0x5a, 0x80, 0xb0, 0x47, 0xdd, 0xa7, 0x85, 0xb8, 0x17, 0x35, 0x16, 0x02, 0xb8, 0x02, 0x8d, 0xa3,
	0x00, 0x72, 0x81, 0xc6, 0x91, 0x98, 0x50, 0x99, 0x20, 0x1d, 0x98, 0xc7, 0xfb, 0x5c, 0x77, 0xfb,
	0xb4, 0x8d, 0x95, 0x86, 0x9b, 0x9b, 0x6a, 0xfc, 0x73, 0xcc, 0x97, 0x5b, 0x9b, 0x49, 0xa7, 0x10,
	0xbd, 0x92, 0xe9, 0x42, 0xf6, 0x48, 0xcc, 0x73, 0x58, 0x4c, 0x38, 0x5f, 0xf8, 0x26, 0xf5, 0x77,
	0x4d, 0x7e, 0x99, 0xa4, 0x6c, 0x57, 0x3c, 0x22, 0x12, 0x76, 0x00, 0xcb, 0xdf, 0x5b, 0x6f, 0x0e,
	0x7d, 0xca, 0xa9, 0x1b, 0xa0, 0x16, 0x64, 0x20, 0xb7, 0x32, 0x83, 0x54, 0x71, 0x58, 0x0c, 0xa9,
	0x6d, 0xd8, 0xb8, 0x8e, 0xc3, 0x82, 0x8b, 0xc1, 0xf5, 0x3d, 0xfa, 0x67, 0x9c, 0x05, 0xd7, 0xf7,
	0xa5, 0x3f, 0x3c, 0x2d, 0xb8, 0xbe, 0x2f, 0xff, 0x8d, 0xa8, 0x32, 0xd1, 0x5a, 0xf9, 0xe3, 0xaf,
	0xff, 0x7f, 0xb4, 0x08, 0x0b, 0xb9, 0x5f, 0xcf, 0x3e, 0xf9, 0xe4, 0xa7, 0x8f, 0xfb, 0x9e, 0xad,
	0xbb, 0xfd, 0xcd, 0xc7, 0x5b, 0x41, 0xb0, 0x69, 0x78, 0xce, 0x23, 0xfc, 0xf5, 0xab, 0xe1, 0xd9,
	0x8f, 0x38, 0xf5, 0xcf, 0x2d, 0x83, 0xf2, 0xfc, 0x4f, 0x6e, 0x7b, 0xb3, 0xc8, 0xf2, 0xc9, 0x9f,
	0x02, 0x00, 0x00, 0xff, 0xff, 0x52, 0x7e, 0xbb, 0xc8, 0x9c, 0x2b, 0x00, 0x00,
}
