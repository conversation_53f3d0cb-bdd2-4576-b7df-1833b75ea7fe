// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/channel-wedding-plan/channel-wedding-plan.proto

package channel_wedding_plan // import "golang.52tt.com/protocol/services/channel-wedding-plan"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/services/extension/options"
import reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type ThemeType int32

const (
	ThemeType_UNKNOWN ThemeType = 0
	ThemeType_FREE    ThemeType = 1
	ThemeType_PAY     ThemeType = 2
)

var ThemeType_name = map[int32]string{
	0: "UNKNOWN",
	1: "FREE",
	2: "PAY",
}
var ThemeType_value = map[string]int32{
	"UNKNOWN": 0,
	"FREE":    1,
	"PAY":     2,
}

func (x ThemeType) String() string {
	return proto.EnumName(ThemeType_name, int32(x))
}
func (ThemeType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{0}
}

type WeddingInviteStatus int32

const (
	WeddingInviteStatus_WEDDING_INVITE_STATUS_UNKNOWN  WeddingInviteStatus = 0
	WeddingInviteStatus_WEDDING_INVITE_STATUS_INVITED  WeddingInviteStatus = 1
	WeddingInviteStatus_WEDDING_INVITE_STATUS_ACCEPTED WeddingInviteStatus = 2
	WeddingInviteStatus_WEDDING_INVITE_STATUS_REFUSED  WeddingInviteStatus = 3
)

var WeddingInviteStatus_name = map[int32]string{
	0: "WEDDING_INVITE_STATUS_UNKNOWN",
	1: "WEDDING_INVITE_STATUS_INVITED",
	2: "WEDDING_INVITE_STATUS_ACCEPTED",
	3: "WEDDING_INVITE_STATUS_REFUSED",
}
var WeddingInviteStatus_value = map[string]int32{
	"WEDDING_INVITE_STATUS_UNKNOWN":  0,
	"WEDDING_INVITE_STATUS_INVITED":  1,
	"WEDDING_INVITE_STATUS_ACCEPTED": 2,
	"WEDDING_INVITE_STATUS_REFUSED":  3,
}

func (x WeddingInviteStatus) String() string {
	return proto.EnumName(WeddingInviteStatus_name, int32(x))
}
func (WeddingInviteStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{1}
}

type WeddingPlanStatus int32

const (
	WeddingPlanStatus_WEDDING_PLAN_STATUS_INIT    WeddingPlanStatus = 0
	WeddingPlanStatus_WEDDING_PLAN_STATUS_PLAYING WeddingPlanStatus = 1
	WeddingPlanStatus_WEDDING_PLAN_STATUS_FINISH  WeddingPlanStatus = 2
	WeddingPlanStatus_WEDDING_PLAN_STATUS_CANCEL  WeddingPlanStatus = 3
)

var WeddingPlanStatus_name = map[int32]string{
	0: "WEDDING_PLAN_STATUS_INIT",
	1: "WEDDING_PLAN_STATUS_PLAYING",
	2: "WEDDING_PLAN_STATUS_FINISH",
	3: "WEDDING_PLAN_STATUS_CANCEL",
}
var WeddingPlanStatus_value = map[string]int32{
	"WEDDING_PLAN_STATUS_INIT":    0,
	"WEDDING_PLAN_STATUS_PLAYING": 1,
	"WEDDING_PLAN_STATUS_FINISH":  2,
	"WEDDING_PLAN_STATUS_CANCEL":  3,
}

func (x WeddingPlanStatus) String() string {
	return proto.EnumName(WeddingPlanStatus_name, int32(x))
}
func (WeddingPlanStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{2}
}

type ReviewStatus int32

const (
	ReviewStatus_REVIEW_STATUS_REVIEWING ReviewStatus = 0
	ReviewStatus_REVIEW_STATUS_PASS      ReviewStatus = 1
	ReviewStatus_REVIEW_STATUS_REJECT    ReviewStatus = 2
)

var ReviewStatus_name = map[int32]string{
	0: "REVIEW_STATUS_REVIEWING",
	1: "REVIEW_STATUS_PASS",
	2: "REVIEW_STATUS_REJECT",
}
var ReviewStatus_value = map[string]int32{
	"REVIEW_STATUS_REVIEWING": 0,
	"REVIEW_STATUS_PASS":      1,
	"REVIEW_STATUS_REJECT":    2,
}

func (x ReviewStatus) String() string {
	return proto.EnumName(ReviewStatus_name, int32(x))
}
func (ReviewStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{3}
}

type WeddingReserveDataType int32

const (
	WeddingReserveDataType_WEDDING_RESERVE_DATA_TYPE_ALL       WeddingReserveDataType = 0
	WeddingReserveDataType_WEDDING_RESERVE_DATA_TYPE_NOT_START WeddingReserveDataType = 1
	WeddingReserveDataType_WEDDING_RESERVE_DATA_TYPE_END       WeddingReserveDataType = 2
)

var WeddingReserveDataType_name = map[int32]string{
	0: "WEDDING_RESERVE_DATA_TYPE_ALL",
	1: "WEDDING_RESERVE_DATA_TYPE_NOT_START",
	2: "WEDDING_RESERVE_DATA_TYPE_END",
}
var WeddingReserveDataType_value = map[string]int32{
	"WEDDING_RESERVE_DATA_TYPE_ALL":       0,
	"WEDDING_RESERVE_DATA_TYPE_NOT_START": 1,
	"WEDDING_RESERVE_DATA_TYPE_END":       2,
}

func (x WeddingReserveDataType) String() string {
	return proto.EnumName(WeddingReserveDataType_name, int32(x))
}
func (WeddingReserveDataType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{4}
}

type HideOpType int32

const (
	HideOpType_HIDE_OP_TYPE_UNEXPECTED HideOpType = 0
	HideOpType_HIDE_OP_TYPE_HIDE       HideOpType = 1
	HideOpType_HIDE_OP_TYPE_SHOW       HideOpType = 2
)

var HideOpType_name = map[int32]string{
	0: "HIDE_OP_TYPE_UNEXPECTED",
	1: "HIDE_OP_TYPE_HIDE",
	2: "HIDE_OP_TYPE_SHOW",
}
var HideOpType_value = map[string]int32{
	"HIDE_OP_TYPE_UNEXPECTED": 0,
	"HIDE_OP_TYPE_HIDE":       1,
	"HIDE_OP_TYPE_SHOW":       2,
}

func (x HideOpType) String() string {
	return proto.EnumName(HideOpType_name, int32(x))
}
func (HideOpType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{5}
}

type AdminReservableSwitch int32

const (
	AdminReservableSwitch_RESERVABLE_SWITCH_UNEXPECTED AdminReservableSwitch = 0
	AdminReservableSwitch_RESERVABLE_SWITCH_OPEN       AdminReservableSwitch = 1
	AdminReservableSwitch_RESERVABLE_SWITCH_CLOSE      AdminReservableSwitch = 2
)

var AdminReservableSwitch_name = map[int32]string{
	0: "RESERVABLE_SWITCH_UNEXPECTED",
	1: "RESERVABLE_SWITCH_OPEN",
	2: "RESERVABLE_SWITCH_CLOSE",
}
var AdminReservableSwitch_value = map[string]int32{
	"RESERVABLE_SWITCH_UNEXPECTED": 0,
	"RESERVABLE_SWITCH_OPEN":       1,
	"RESERVABLE_SWITCH_CLOSE":      2,
}

func (x AdminReservableSwitch) String() string {
	return proto.EnumName(AdminReservableSwitch_name, int32(x))
}
func (AdminReservableSwitch) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{6}
}

type TestWeddingAnniversaryPopupRequest_OpType int32

const (
	TestWeddingAnniversaryPopupRequest_OP_TYPE_UNEXPECTED   TestWeddingAnniversaryPopupRequest_OpType = 0
	TestWeddingAnniversaryPopupRequest_OP_TYPE_GEN_POPUP    TestWeddingAnniversaryPopupRequest_OpType = 1
	TestWeddingAnniversaryPopupRequest_OP_TYPE_ONLINE_EVENT TestWeddingAnniversaryPopupRequest_OpType = 2
	TestWeddingAnniversaryPopupRequest_OP_TYPE_PUSH         TestWeddingAnniversaryPopupRequest_OpType = 3
)

var TestWeddingAnniversaryPopupRequest_OpType_name = map[int32]string{
	0: "OP_TYPE_UNEXPECTED",
	1: "OP_TYPE_GEN_POPUP",
	2: "OP_TYPE_ONLINE_EVENT",
	3: "OP_TYPE_PUSH",
}
var TestWeddingAnniversaryPopupRequest_OpType_value = map[string]int32{
	"OP_TYPE_UNEXPECTED":   0,
	"OP_TYPE_GEN_POPUP":    1,
	"OP_TYPE_ONLINE_EVENT": 2,
	"OP_TYPE_PUSH":         3,
}

func (x TestWeddingAnniversaryPopupRequest_OpType) String() string {
	return proto.EnumName(TestWeddingAnniversaryPopupRequest_OpType_name, int32(x))
}
func (TestWeddingAnniversaryPopupRequest_OpType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{111, 0}
}

type BuyWeddingRequest struct {
	ThemeId              uint32          `protobuf:"varint,1,opt,name=theme_id,json=themeId,proto3" json:"theme_id,omitempty"`
	BuyerUid             uint32          `protobuf:"varint,2,opt,name=buyer_uid,json=buyerUid,proto3" json:"buyer_uid,omitempty"`
	BuyReserveInfo       *BuyReserveInfo `protobuf:"bytes,3,opt,name=buy_reserve_info,json=buyReserveInfo,proto3" json:"buy_reserve_info,omitempty"`
	SourceMsgId          uint32          `protobuf:"varint,4,opt,name=source_msg_id,json=sourceMsgId,proto3" json:"source_msg_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *BuyWeddingRequest) Reset()         { *m = BuyWeddingRequest{} }
func (m *BuyWeddingRequest) String() string { return proto.CompactTextString(m) }
func (*BuyWeddingRequest) ProtoMessage()    {}
func (*BuyWeddingRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{0}
}
func (m *BuyWeddingRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BuyWeddingRequest.Unmarshal(m, b)
}
func (m *BuyWeddingRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BuyWeddingRequest.Marshal(b, m, deterministic)
}
func (dst *BuyWeddingRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BuyWeddingRequest.Merge(dst, src)
}
func (m *BuyWeddingRequest) XXX_Size() int {
	return xxx_messageInfo_BuyWeddingRequest.Size(m)
}
func (m *BuyWeddingRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BuyWeddingRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BuyWeddingRequest proto.InternalMessageInfo

func (m *BuyWeddingRequest) GetThemeId() uint32 {
	if m != nil {
		return m.ThemeId
	}
	return 0
}

func (m *BuyWeddingRequest) GetBuyerUid() uint32 {
	if m != nil {
		return m.BuyerUid
	}
	return 0
}

func (m *BuyWeddingRequest) GetBuyReserveInfo() *BuyReserveInfo {
	if m != nil {
		return m.BuyReserveInfo
	}
	return nil
}

func (m *BuyWeddingRequest) GetSourceMsgId() uint32 {
	if m != nil {
		return m.SourceMsgId
	}
	return 0
}

type BuyReserveInfo struct {
	ReserveDate          uint32   `protobuf:"varint,1,opt,name=reserve_date,json=reserveDate,proto3" json:"reserve_date,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ReserveTime          []string `protobuf:"bytes,3,rep,name=reserve_time,json=reserveTime,proto3" json:"reserve_time,omitempty"`
	IsHot                bool     `protobuf:"varint,4,opt,name=is_hot,json=isHot,proto3" json:"is_hot,omitempty"`
	GiftId               uint32   `protobuf:"varint,5,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BuyReserveInfo) Reset()         { *m = BuyReserveInfo{} }
func (m *BuyReserveInfo) String() string { return proto.CompactTextString(m) }
func (*BuyReserveInfo) ProtoMessage()    {}
func (*BuyReserveInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{1}
}
func (m *BuyReserveInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BuyReserveInfo.Unmarshal(m, b)
}
func (m *BuyReserveInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BuyReserveInfo.Marshal(b, m, deterministic)
}
func (dst *BuyReserveInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BuyReserveInfo.Merge(dst, src)
}
func (m *BuyReserveInfo) XXX_Size() int {
	return xxx_messageInfo_BuyReserveInfo.Size(m)
}
func (m *BuyReserveInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_BuyReserveInfo.DiscardUnknown(m)
}

var xxx_messageInfo_BuyReserveInfo proto.InternalMessageInfo

func (m *BuyReserveInfo) GetReserveDate() uint32 {
	if m != nil {
		return m.ReserveDate
	}
	return 0
}

func (m *BuyReserveInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *BuyReserveInfo) GetReserveTime() []string {
	if m != nil {
		return m.ReserveTime
	}
	return nil
}

func (m *BuyReserveInfo) GetIsHot() bool {
	if m != nil {
		return m.IsHot
	}
	return false
}

func (m *BuyReserveInfo) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

type BuyWeddingResponse struct {
	WeddingPlanId        uint32   `protobuf:"varint,1,opt,name=wedding_plan_id,json=weddingPlanId,proto3" json:"wedding_plan_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BuyWeddingResponse) Reset()         { *m = BuyWeddingResponse{} }
func (m *BuyWeddingResponse) String() string { return proto.CompactTextString(m) }
func (*BuyWeddingResponse) ProtoMessage()    {}
func (*BuyWeddingResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{2}
}
func (m *BuyWeddingResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BuyWeddingResponse.Unmarshal(m, b)
}
func (m *BuyWeddingResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BuyWeddingResponse.Marshal(b, m, deterministic)
}
func (dst *BuyWeddingResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BuyWeddingResponse.Merge(dst, src)
}
func (m *BuyWeddingResponse) XXX_Size() int {
	return xxx_messageInfo_BuyWeddingResponse.Size(m)
}
func (m *BuyWeddingResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BuyWeddingResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BuyWeddingResponse proto.InternalMessageInfo

func (m *BuyWeddingResponse) GetWeddingPlanId() uint32 {
	if m != nil {
		return m.WeddingPlanId
	}
	return 0
}

type GetMyWeddingReserveInfoRequest struct {
	WeddingPlanId        uint32   `protobuf:"varint,1,opt,name=wedding_plan_id,json=weddingPlanId,proto3" json:"wedding_plan_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMyWeddingReserveInfoRequest) Reset()         { *m = GetMyWeddingReserveInfoRequest{} }
func (m *GetMyWeddingReserveInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetMyWeddingReserveInfoRequest) ProtoMessage()    {}
func (*GetMyWeddingReserveInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{3}
}
func (m *GetMyWeddingReserveInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMyWeddingReserveInfoRequest.Unmarshal(m, b)
}
func (m *GetMyWeddingReserveInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMyWeddingReserveInfoRequest.Marshal(b, m, deterministic)
}
func (dst *GetMyWeddingReserveInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMyWeddingReserveInfoRequest.Merge(dst, src)
}
func (m *GetMyWeddingReserveInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetMyWeddingReserveInfoRequest.Size(m)
}
func (m *GetMyWeddingReserveInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMyWeddingReserveInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetMyWeddingReserveInfoRequest proto.InternalMessageInfo

func (m *GetMyWeddingReserveInfoRequest) GetWeddingPlanId() uint32 {
	if m != nil {
		return m.WeddingPlanId
	}
	return 0
}

type GetMyWeddingReserveInfoResponse struct {
	ReserveDate          uint32   `protobuf:"varint,1,opt,name=reserve_date,json=reserveDate,proto3" json:"reserve_date,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ReserveTimeSection   []string `protobuf:"bytes,3,rep,name=reserve_time_section,json=reserveTimeSection,proto3" json:"reserve_time_section,omitempty"`
	RemainChangeTimes    uint32   `protobuf:"varint,4,opt,name=remain_change_times,json=remainChangeTimes,proto3" json:"remain_change_times,omitempty"`
	ChangeLimitTime      uint32   `protobuf:"varint,5,opt,name=change_limit_time,json=changeLimitTime,proto3" json:"change_limit_time,omitempty"`
	InChangeTime         bool     `protobuf:"varint,6,opt,name=in_change_time,json=inChangeTime,proto3" json:"in_change_time,omitempty"`
	ReserveStartTime     uint32   `protobuf:"varint,7,opt,name=reserve_start_time,json=reserveStartTime,proto3" json:"reserve_start_time,omitempty"`
	ReserveEndTime       uint32   `protobuf:"varint,8,opt,name=reserve_end_time,json=reserveEndTime,proto3" json:"reserve_end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMyWeddingReserveInfoResponse) Reset()         { *m = GetMyWeddingReserveInfoResponse{} }
func (m *GetMyWeddingReserveInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetMyWeddingReserveInfoResponse) ProtoMessage()    {}
func (*GetMyWeddingReserveInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{4}
}
func (m *GetMyWeddingReserveInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMyWeddingReserveInfoResponse.Unmarshal(m, b)
}
func (m *GetMyWeddingReserveInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMyWeddingReserveInfoResponse.Marshal(b, m, deterministic)
}
func (dst *GetMyWeddingReserveInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMyWeddingReserveInfoResponse.Merge(dst, src)
}
func (m *GetMyWeddingReserveInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetMyWeddingReserveInfoResponse.Size(m)
}
func (m *GetMyWeddingReserveInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMyWeddingReserveInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetMyWeddingReserveInfoResponse proto.InternalMessageInfo

func (m *GetMyWeddingReserveInfoResponse) GetReserveDate() uint32 {
	if m != nil {
		return m.ReserveDate
	}
	return 0
}

func (m *GetMyWeddingReserveInfoResponse) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetMyWeddingReserveInfoResponse) GetReserveTimeSection() []string {
	if m != nil {
		return m.ReserveTimeSection
	}
	return nil
}

func (m *GetMyWeddingReserveInfoResponse) GetRemainChangeTimes() uint32 {
	if m != nil {
		return m.RemainChangeTimes
	}
	return 0
}

func (m *GetMyWeddingReserveInfoResponse) GetChangeLimitTime() uint32 {
	if m != nil {
		return m.ChangeLimitTime
	}
	return 0
}

func (m *GetMyWeddingReserveInfoResponse) GetInChangeTime() bool {
	if m != nil {
		return m.InChangeTime
	}
	return false
}

func (m *GetMyWeddingReserveInfoResponse) GetReserveStartTime() uint32 {
	if m != nil {
		return m.ReserveStartTime
	}
	return 0
}

func (m *GetMyWeddingReserveInfoResponse) GetReserveEndTime() uint32 {
	if m != nil {
		return m.ReserveEndTime
	}
	return 0
}

type ChannelInfo struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ManagerUid           uint32   `protobuf:"varint,2,opt,name=manager_uid,json=managerUid,proto3" json:"manager_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelInfo) Reset()         { *m = ChannelInfo{} }
func (m *ChannelInfo) String() string { return proto.CompactTextString(m) }
func (*ChannelInfo) ProtoMessage()    {}
func (*ChannelInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{5}
}
func (m *ChannelInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelInfo.Unmarshal(m, b)
}
func (m *ChannelInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelInfo.Marshal(b, m, deterministic)
}
func (dst *ChannelInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelInfo.Merge(dst, src)
}
func (m *ChannelInfo) XXX_Size() int {
	return xxx_messageInfo_ChannelInfo.Size(m)
}
func (m *ChannelInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelInfo proto.InternalMessageInfo

func (m *ChannelInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelInfo) GetManagerUid() uint32 {
	if m != nil {
		return m.ManagerUid
	}
	return 0
}

type GetWeddingReserveInfoRequest struct {
	ThemeType            uint32   `protobuf:"varint,1,opt,name=theme_type,json=themeType,proto3" json:"theme_type,omitempty"`
	ReserveDate          uint32   `protobuf:"varint,2,opt,name=reserve_date,json=reserveDate,proto3" json:"reserve_date,omitempty"`
	ChannelId            uint32   `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetWeddingReserveInfoRequest) Reset()         { *m = GetWeddingReserveInfoRequest{} }
func (m *GetWeddingReserveInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetWeddingReserveInfoRequest) ProtoMessage()    {}
func (*GetWeddingReserveInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{6}
}
func (m *GetWeddingReserveInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWeddingReserveInfoRequest.Unmarshal(m, b)
}
func (m *GetWeddingReserveInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWeddingReserveInfoRequest.Marshal(b, m, deterministic)
}
func (dst *GetWeddingReserveInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWeddingReserveInfoRequest.Merge(dst, src)
}
func (m *GetWeddingReserveInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetWeddingReserveInfoRequest.Size(m)
}
func (m *GetWeddingReserveInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWeddingReserveInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetWeddingReserveInfoRequest proto.InternalMessageInfo

func (m *GetWeddingReserveInfoRequest) GetThemeType() uint32 {
	if m != nil {
		return m.ThemeType
	}
	return 0
}

func (m *GetWeddingReserveInfoRequest) GetReserveDate() uint32 {
	if m != nil {
		return m.ReserveDate
	}
	return 0
}

func (m *GetWeddingReserveInfoRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetWeddingReserveInfoResponse struct {
	CurChannelId         uint32             `protobuf:"varint,1,opt,name=cur_channel_id,json=curChannelId,proto3" json:"cur_channel_id,omitempty"`
	CurReserveDate       uint32             `protobuf:"varint,2,opt,name=cur_reserve_date,json=curReserveDate,proto3" json:"cur_reserve_date,omitempty"`
	ChannelInfo          []*ChannelInfo     `protobuf:"bytes,3,rep,name=channel_info,json=channelInfo,proto3" json:"channel_info,omitempty"`
	ReserveTimeInfo      []*ReserveTimeInfo `protobuf:"bytes,4,rep,name=reserve_time_info,json=reserveTimeInfo,proto3" json:"reserve_time_info,omitempty"`
	MinReserveDate       uint32             `protobuf:"varint,5,opt,name=min_reserve_date,json=minReserveDate,proto3" json:"min_reserve_date,omitempty"`
	MaxReserveDate       uint32             `protobuf:"varint,6,opt,name=max_reserve_date,json=maxReserveDate,proto3" json:"max_reserve_date,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetWeddingReserveInfoResponse) Reset()         { *m = GetWeddingReserveInfoResponse{} }
func (m *GetWeddingReserveInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetWeddingReserveInfoResponse) ProtoMessage()    {}
func (*GetWeddingReserveInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{7}
}
func (m *GetWeddingReserveInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWeddingReserveInfoResponse.Unmarshal(m, b)
}
func (m *GetWeddingReserveInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWeddingReserveInfoResponse.Marshal(b, m, deterministic)
}
func (dst *GetWeddingReserveInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWeddingReserveInfoResponse.Merge(dst, src)
}
func (m *GetWeddingReserveInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetWeddingReserveInfoResponse.Size(m)
}
func (m *GetWeddingReserveInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWeddingReserveInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetWeddingReserveInfoResponse proto.InternalMessageInfo

func (m *GetWeddingReserveInfoResponse) GetCurChannelId() uint32 {
	if m != nil {
		return m.CurChannelId
	}
	return 0
}

func (m *GetWeddingReserveInfoResponse) GetCurReserveDate() uint32 {
	if m != nil {
		return m.CurReserveDate
	}
	return 0
}

func (m *GetWeddingReserveInfoResponse) GetChannelInfo() []*ChannelInfo {
	if m != nil {
		return m.ChannelInfo
	}
	return nil
}

func (m *GetWeddingReserveInfoResponse) GetReserveTimeInfo() []*ReserveTimeInfo {
	if m != nil {
		return m.ReserveTimeInfo
	}
	return nil
}

func (m *GetWeddingReserveInfoResponse) GetMinReserveDate() uint32 {
	if m != nil {
		return m.MinReserveDate
	}
	return 0
}

func (m *GetWeddingReserveInfoResponse) GetMaxReserveDate() uint32 {
	if m != nil {
		return m.MaxReserveDate
	}
	return 0
}

type SaveWeddingReserveRequest struct {
	WeddingPlanId        uint32   `protobuf:"varint,1,opt,name=wedding_plan_id,json=weddingPlanId,proto3" json:"wedding_plan_id,omitempty"`
	ReserveDate          uint32   `protobuf:"varint,2,opt,name=reserve_date,json=reserveDate,proto3" json:"reserve_date,omitempty"`
	ChannelId            uint32   `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ReserveTime          []string `protobuf:"bytes,4,rep,name=reserve_time,json=reserveTime,proto3" json:"reserve_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SaveWeddingReserveRequest) Reset()         { *m = SaveWeddingReserveRequest{} }
func (m *SaveWeddingReserveRequest) String() string { return proto.CompactTextString(m) }
func (*SaveWeddingReserveRequest) ProtoMessage()    {}
func (*SaveWeddingReserveRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{8}
}
func (m *SaveWeddingReserveRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SaveWeddingReserveRequest.Unmarshal(m, b)
}
func (m *SaveWeddingReserveRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SaveWeddingReserveRequest.Marshal(b, m, deterministic)
}
func (dst *SaveWeddingReserveRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SaveWeddingReserveRequest.Merge(dst, src)
}
func (m *SaveWeddingReserveRequest) XXX_Size() int {
	return xxx_messageInfo_SaveWeddingReserveRequest.Size(m)
}
func (m *SaveWeddingReserveRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SaveWeddingReserveRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SaveWeddingReserveRequest proto.InternalMessageInfo

func (m *SaveWeddingReserveRequest) GetWeddingPlanId() uint32 {
	if m != nil {
		return m.WeddingPlanId
	}
	return 0
}

func (m *SaveWeddingReserveRequest) GetReserveDate() uint32 {
	if m != nil {
		return m.ReserveDate
	}
	return 0
}

func (m *SaveWeddingReserveRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SaveWeddingReserveRequest) GetReserveTime() []string {
	if m != nil {
		return m.ReserveTime
	}
	return nil
}

type SaveWeddingReserveInfoResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SaveWeddingReserveInfoResponse) Reset()         { *m = SaveWeddingReserveInfoResponse{} }
func (m *SaveWeddingReserveInfoResponse) String() string { return proto.CompactTextString(m) }
func (*SaveWeddingReserveInfoResponse) ProtoMessage()    {}
func (*SaveWeddingReserveInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{9}
}
func (m *SaveWeddingReserveInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SaveWeddingReserveInfoResponse.Unmarshal(m, b)
}
func (m *SaveWeddingReserveInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SaveWeddingReserveInfoResponse.Marshal(b, m, deterministic)
}
func (dst *SaveWeddingReserveInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SaveWeddingReserveInfoResponse.Merge(dst, src)
}
func (m *SaveWeddingReserveInfoResponse) XXX_Size() int {
	return xxx_messageInfo_SaveWeddingReserveInfoResponse.Size(m)
}
func (m *SaveWeddingReserveInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SaveWeddingReserveInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SaveWeddingReserveInfoResponse proto.InternalMessageInfo

type ChangeWeddingReserveRequest struct {
	WeddingPlanId        uint32   `protobuf:"varint,1,opt,name=wedding_plan_id,json=weddingPlanId,proto3" json:"wedding_plan_id,omitempty"`
	ReserveDate          uint32   `protobuf:"varint,2,opt,name=reserve_date,json=reserveDate,proto3" json:"reserve_date,omitempty"`
	ChannelId            uint32   `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ReserveTime          []string `protobuf:"bytes,4,rep,name=reserve_time,json=reserveTime,proto3" json:"reserve_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChangeWeddingReserveRequest) Reset()         { *m = ChangeWeddingReserveRequest{} }
func (m *ChangeWeddingReserveRequest) String() string { return proto.CompactTextString(m) }
func (*ChangeWeddingReserveRequest) ProtoMessage()    {}
func (*ChangeWeddingReserveRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{10}
}
func (m *ChangeWeddingReserveRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChangeWeddingReserveRequest.Unmarshal(m, b)
}
func (m *ChangeWeddingReserveRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChangeWeddingReserveRequest.Marshal(b, m, deterministic)
}
func (dst *ChangeWeddingReserveRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangeWeddingReserveRequest.Merge(dst, src)
}
func (m *ChangeWeddingReserveRequest) XXX_Size() int {
	return xxx_messageInfo_ChangeWeddingReserveRequest.Size(m)
}
func (m *ChangeWeddingReserveRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangeWeddingReserveRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ChangeWeddingReserveRequest proto.InternalMessageInfo

func (m *ChangeWeddingReserveRequest) GetWeddingPlanId() uint32 {
	if m != nil {
		return m.WeddingPlanId
	}
	return 0
}

func (m *ChangeWeddingReserveRequest) GetReserveDate() uint32 {
	if m != nil {
		return m.ReserveDate
	}
	return 0
}

func (m *ChangeWeddingReserveRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChangeWeddingReserveRequest) GetReserveTime() []string {
	if m != nil {
		return m.ReserveTime
	}
	return nil
}

type ChangeWeddingReserveInfoResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChangeWeddingReserveInfoResponse) Reset()         { *m = ChangeWeddingReserveInfoResponse{} }
func (m *ChangeWeddingReserveInfoResponse) String() string { return proto.CompactTextString(m) }
func (*ChangeWeddingReserveInfoResponse) ProtoMessage()    {}
func (*ChangeWeddingReserveInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{11}
}
func (m *ChangeWeddingReserveInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChangeWeddingReserveInfoResponse.Unmarshal(m, b)
}
func (m *ChangeWeddingReserveInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChangeWeddingReserveInfoResponse.Marshal(b, m, deterministic)
}
func (dst *ChangeWeddingReserveInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangeWeddingReserveInfoResponse.Merge(dst, src)
}
func (m *ChangeWeddingReserveInfoResponse) XXX_Size() int {
	return xxx_messageInfo_ChangeWeddingReserveInfoResponse.Size(m)
}
func (m *ChangeWeddingReserveInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangeWeddingReserveInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ChangeWeddingReserveInfoResponse proto.InternalMessageInfo

type WeddingGuestInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	InviteUid            uint32   `protobuf:"varint,2,opt,name=invite_uid,json=inviteUid,proto3" json:"invite_uid,omitempty"`
	CreateTime           uint32   `protobuf:"varint,3,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WeddingGuestInfo) Reset()         { *m = WeddingGuestInfo{} }
func (m *WeddingGuestInfo) String() string { return proto.CompactTextString(m) }
func (*WeddingGuestInfo) ProtoMessage()    {}
func (*WeddingGuestInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{12}
}
func (m *WeddingGuestInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingGuestInfo.Unmarshal(m, b)
}
func (m *WeddingGuestInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingGuestInfo.Marshal(b, m, deterministic)
}
func (dst *WeddingGuestInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingGuestInfo.Merge(dst, src)
}
func (m *WeddingGuestInfo) XXX_Size() int {
	return xxx_messageInfo_WeddingGuestInfo.Size(m)
}
func (m *WeddingGuestInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingGuestInfo.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingGuestInfo proto.InternalMessageInfo

func (m *WeddingGuestInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *WeddingGuestInfo) GetInviteUid() uint32 {
	if m != nil {
		return m.InviteUid
	}
	return 0
}

func (m *WeddingGuestInfo) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

// 获取伴郎伴娘信息 url: /tt-revenue-http-logic/channel_wedding/get_groomsman_and_bridesmaid_info
type GetGroomsmanAndBridesmaidInfoRequest struct {
	WeddingPlanId        uint32   `protobuf:"varint,1,opt,name=wedding_plan_id,json=weddingPlanId,proto3" json:"wedding_plan_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGroomsmanAndBridesmaidInfoRequest) Reset()         { *m = GetGroomsmanAndBridesmaidInfoRequest{} }
func (m *GetGroomsmanAndBridesmaidInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetGroomsmanAndBridesmaidInfoRequest) ProtoMessage()    {}
func (*GetGroomsmanAndBridesmaidInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{13}
}
func (m *GetGroomsmanAndBridesmaidInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGroomsmanAndBridesmaidInfoRequest.Unmarshal(m, b)
}
func (m *GetGroomsmanAndBridesmaidInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGroomsmanAndBridesmaidInfoRequest.Marshal(b, m, deterministic)
}
func (dst *GetGroomsmanAndBridesmaidInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGroomsmanAndBridesmaidInfoRequest.Merge(dst, src)
}
func (m *GetGroomsmanAndBridesmaidInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetGroomsmanAndBridesmaidInfoRequest.Size(m)
}
func (m *GetGroomsmanAndBridesmaidInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGroomsmanAndBridesmaidInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetGroomsmanAndBridesmaidInfoRequest proto.InternalMessageInfo

func (m *GetGroomsmanAndBridesmaidInfoRequest) GetWeddingPlanId() uint32 {
	if m != nil {
		return m.WeddingPlanId
	}
	return 0
}

type GetGroomsmanAndBridesmaidInfoResponse struct {
	BridesmaidList       []*WeddingGuestInfo `protobuf:"bytes,1,rep,name=bridesmaid_list,json=bridesmaidList,proto3" json:"bridesmaid_list,omitempty"`
	GroomsmanList        []*WeddingGuestInfo `protobuf:"bytes,2,rep,name=groomsman_list,json=groomsmanList,proto3" json:"groomsman_list,omitempty"`
	InvitedList          []*WeddingGuestInfo `protobuf:"bytes,3,rep,name=invited_list,json=invitedList,proto3" json:"invited_list,omitempty"`
	AgreedList           []*WeddingGuestInfo `protobuf:"bytes,4,rep,name=agreed_list,json=agreedList,proto3" json:"agreed_list,omitempty"`
	RefusedList          []*WeddingGuestInfo `protobuf:"bytes,5,rep,name=refused_list,json=refusedList,proto3" json:"refused_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetGroomsmanAndBridesmaidInfoResponse) Reset()         { *m = GetGroomsmanAndBridesmaidInfoResponse{} }
func (m *GetGroomsmanAndBridesmaidInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetGroomsmanAndBridesmaidInfoResponse) ProtoMessage()    {}
func (*GetGroomsmanAndBridesmaidInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{14}
}
func (m *GetGroomsmanAndBridesmaidInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGroomsmanAndBridesmaidInfoResponse.Unmarshal(m, b)
}
func (m *GetGroomsmanAndBridesmaidInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGroomsmanAndBridesmaidInfoResponse.Marshal(b, m, deterministic)
}
func (dst *GetGroomsmanAndBridesmaidInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGroomsmanAndBridesmaidInfoResponse.Merge(dst, src)
}
func (m *GetGroomsmanAndBridesmaidInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetGroomsmanAndBridesmaidInfoResponse.Size(m)
}
func (m *GetGroomsmanAndBridesmaidInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGroomsmanAndBridesmaidInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetGroomsmanAndBridesmaidInfoResponse proto.InternalMessageInfo

func (m *GetGroomsmanAndBridesmaidInfoResponse) GetBridesmaidList() []*WeddingGuestInfo {
	if m != nil {
		return m.BridesmaidList
	}
	return nil
}

func (m *GetGroomsmanAndBridesmaidInfoResponse) GetGroomsmanList() []*WeddingGuestInfo {
	if m != nil {
		return m.GroomsmanList
	}
	return nil
}

func (m *GetGroomsmanAndBridesmaidInfoResponse) GetInvitedList() []*WeddingGuestInfo {
	if m != nil {
		return m.InvitedList
	}
	return nil
}

func (m *GetGroomsmanAndBridesmaidInfoResponse) GetAgreedList() []*WeddingGuestInfo {
	if m != nil {
		return m.AgreedList
	}
	return nil
}

func (m *GetGroomsmanAndBridesmaidInfoResponse) GetRefusedList() []*WeddingGuestInfo {
	if m != nil {
		return m.RefusedList
	}
	return nil
}

// 获取亲友团信息 url: /tt-revenue-http-logic/channel_wedding/get_wedding_friend_info
type GetWeddingFriendInfoRequest struct {
	WeddingPlanId        uint32   `protobuf:"varint,1,opt,name=wedding_plan_id,json=weddingPlanId,proto3" json:"wedding_plan_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetWeddingFriendInfoRequest) Reset()         { *m = GetWeddingFriendInfoRequest{} }
func (m *GetWeddingFriendInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetWeddingFriendInfoRequest) ProtoMessage()    {}
func (*GetWeddingFriendInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{15}
}
func (m *GetWeddingFriendInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWeddingFriendInfoRequest.Unmarshal(m, b)
}
func (m *GetWeddingFriendInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWeddingFriendInfoRequest.Marshal(b, m, deterministic)
}
func (dst *GetWeddingFriendInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWeddingFriendInfoRequest.Merge(dst, src)
}
func (m *GetWeddingFriendInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetWeddingFriendInfoRequest.Size(m)
}
func (m *GetWeddingFriendInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWeddingFriendInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetWeddingFriendInfoRequest proto.InternalMessageInfo

func (m *GetWeddingFriendInfoRequest) GetWeddingPlanId() uint32 {
	if m != nil {
		return m.WeddingPlanId
	}
	return 0
}

type GetWeddingFriendInfoResponse struct {
	FriendList           []*WeddingGuestInfo `protobuf:"bytes,1,rep,name=friend_list,json=friendList,proto3" json:"friend_list,omitempty"`
	InvitedList          []*WeddingGuestInfo `protobuf:"bytes,2,rep,name=invited_list,json=invitedList,proto3" json:"invited_list,omitempty"`
	AgreedList           []*WeddingGuestInfo `protobuf:"bytes,3,rep,name=agreed_list,json=agreedList,proto3" json:"agreed_list,omitempty"`
	RefusedList          []*WeddingGuestInfo `protobuf:"bytes,4,rep,name=refused_list,json=refusedList,proto3" json:"refused_list,omitempty"`
	MaxFriendNum         uint32              `protobuf:"varint,5,opt,name=max_friend_num,json=maxFriendNum,proto3" json:"max_friend_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetWeddingFriendInfoResponse) Reset()         { *m = GetWeddingFriendInfoResponse{} }
func (m *GetWeddingFriendInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetWeddingFriendInfoResponse) ProtoMessage()    {}
func (*GetWeddingFriendInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{16}
}
func (m *GetWeddingFriendInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWeddingFriendInfoResponse.Unmarshal(m, b)
}
func (m *GetWeddingFriendInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWeddingFriendInfoResponse.Marshal(b, m, deterministic)
}
func (dst *GetWeddingFriendInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWeddingFriendInfoResponse.Merge(dst, src)
}
func (m *GetWeddingFriendInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetWeddingFriendInfoResponse.Size(m)
}
func (m *GetWeddingFriendInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWeddingFriendInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetWeddingFriendInfoResponse proto.InternalMessageInfo

func (m *GetWeddingFriendInfoResponse) GetFriendList() []*WeddingGuestInfo {
	if m != nil {
		return m.FriendList
	}
	return nil
}

func (m *GetWeddingFriendInfoResponse) GetInvitedList() []*WeddingGuestInfo {
	if m != nil {
		return m.InvitedList
	}
	return nil
}

func (m *GetWeddingFriendInfoResponse) GetAgreedList() []*WeddingGuestInfo {
	if m != nil {
		return m.AgreedList
	}
	return nil
}

func (m *GetWeddingFriendInfoResponse) GetRefusedList() []*WeddingGuestInfo {
	if m != nil {
		return m.RefusedList
	}
	return nil
}

func (m *GetWeddingFriendInfoResponse) GetMaxFriendNum() uint32 {
	if m != nil {
		return m.MaxFriendNum
	}
	return 0
}

// 邀请嘉宾 url: /tt-revenue-http-logic/channel_wedding/invite_wedding_guest
type InviteWeddingGuestRequest struct {
	WeddingPlanId        uint32   `protobuf:"varint,1,opt,name=wedding_plan_id,json=weddingPlanId,proto3" json:"wedding_plan_id,omitempty"`
	WeddingGuestType     uint32   `protobuf:"varint,2,opt,name=wedding_guest_type,json=weddingGuestType,proto3" json:"wedding_guest_type,omitempty"`
	Uid                  uint32   `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	IsCancel             bool     `protobuf:"varint,4,opt,name=is_cancel,json=isCancel,proto3" json:"is_cancel,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InviteWeddingGuestRequest) Reset()         { *m = InviteWeddingGuestRequest{} }
func (m *InviteWeddingGuestRequest) String() string { return proto.CompactTextString(m) }
func (*InviteWeddingGuestRequest) ProtoMessage()    {}
func (*InviteWeddingGuestRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{17}
}
func (m *InviteWeddingGuestRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InviteWeddingGuestRequest.Unmarshal(m, b)
}
func (m *InviteWeddingGuestRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InviteWeddingGuestRequest.Marshal(b, m, deterministic)
}
func (dst *InviteWeddingGuestRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InviteWeddingGuestRequest.Merge(dst, src)
}
func (m *InviteWeddingGuestRequest) XXX_Size() int {
	return xxx_messageInfo_InviteWeddingGuestRequest.Size(m)
}
func (m *InviteWeddingGuestRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_InviteWeddingGuestRequest.DiscardUnknown(m)
}

var xxx_messageInfo_InviteWeddingGuestRequest proto.InternalMessageInfo

func (m *InviteWeddingGuestRequest) GetWeddingPlanId() uint32 {
	if m != nil {
		return m.WeddingPlanId
	}
	return 0
}

func (m *InviteWeddingGuestRequest) GetWeddingGuestType() uint32 {
	if m != nil {
		return m.WeddingGuestType
	}
	return 0
}

func (m *InviteWeddingGuestRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *InviteWeddingGuestRequest) GetIsCancel() bool {
	if m != nil {
		return m.IsCancel
	}
	return false
}

type InviteWeddingGuestResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InviteWeddingGuestResponse) Reset()         { *m = InviteWeddingGuestResponse{} }
func (m *InviteWeddingGuestResponse) String() string { return proto.CompactTextString(m) }
func (*InviteWeddingGuestResponse) ProtoMessage()    {}
func (*InviteWeddingGuestResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{18}
}
func (m *InviteWeddingGuestResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InviteWeddingGuestResponse.Unmarshal(m, b)
}
func (m *InviteWeddingGuestResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InviteWeddingGuestResponse.Marshal(b, m, deterministic)
}
func (dst *InviteWeddingGuestResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InviteWeddingGuestResponse.Merge(dst, src)
}
func (m *InviteWeddingGuestResponse) XXX_Size() int {
	return xxx_messageInfo_InviteWeddingGuestResponse.Size(m)
}
func (m *InviteWeddingGuestResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_InviteWeddingGuestResponse.DiscardUnknown(m)
}

var xxx_messageInfo_InviteWeddingGuestResponse proto.InternalMessageInfo

type HandleWeddingInviteRequest struct {
	InviteId             uint32   `protobuf:"varint,1,opt,name=invite_id,json=inviteId,proto3" json:"invite_id,omitempty"`
	HandleStatus         uint32   `protobuf:"varint,2,opt,name=handle_status,json=handleStatus,proto3" json:"handle_status,omitempty"`
	Uid                  uint32   `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HandleWeddingInviteRequest) Reset()         { *m = HandleWeddingInviteRequest{} }
func (m *HandleWeddingInviteRequest) String() string { return proto.CompactTextString(m) }
func (*HandleWeddingInviteRequest) ProtoMessage()    {}
func (*HandleWeddingInviteRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{19}
}
func (m *HandleWeddingInviteRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HandleWeddingInviteRequest.Unmarshal(m, b)
}
func (m *HandleWeddingInviteRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HandleWeddingInviteRequest.Marshal(b, m, deterministic)
}
func (dst *HandleWeddingInviteRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HandleWeddingInviteRequest.Merge(dst, src)
}
func (m *HandleWeddingInviteRequest) XXX_Size() int {
	return xxx_messageInfo_HandleWeddingInviteRequest.Size(m)
}
func (m *HandleWeddingInviteRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_HandleWeddingInviteRequest.DiscardUnknown(m)
}

var xxx_messageInfo_HandleWeddingInviteRequest proto.InternalMessageInfo

func (m *HandleWeddingInviteRequest) GetInviteId() uint32 {
	if m != nil {
		return m.InviteId
	}
	return 0
}

func (m *HandleWeddingInviteRequest) GetHandleStatus() uint32 {
	if m != nil {
		return m.HandleStatus
	}
	return 0
}

func (m *HandleWeddingInviteRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type HandleWeddingInviteResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HandleWeddingInviteResponse) Reset()         { *m = HandleWeddingInviteResponse{} }
func (m *HandleWeddingInviteResponse) String() string { return proto.CompactTextString(m) }
func (*HandleWeddingInviteResponse) ProtoMessage()    {}
func (*HandleWeddingInviteResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{20}
}
func (m *HandleWeddingInviteResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HandleWeddingInviteResponse.Unmarshal(m, b)
}
func (m *HandleWeddingInviteResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HandleWeddingInviteResponse.Marshal(b, m, deterministic)
}
func (dst *HandleWeddingInviteResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HandleWeddingInviteResponse.Merge(dst, src)
}
func (m *HandleWeddingInviteResponse) XXX_Size() int {
	return xxx_messageInfo_HandleWeddingInviteResponse.Size(m)
}
func (m *HandleWeddingInviteResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_HandleWeddingInviteResponse.DiscardUnknown(m)
}

var xxx_messageInfo_HandleWeddingInviteResponse proto.InternalMessageInfo

// 删除嘉宾 url: /tt-revenue-http-logic/channel_wedding/del_wedding_guest
type DelWeddingGuestRequest struct {
	WeddingPlanId        uint32   `protobuf:"varint,1,opt,name=wedding_plan_id,json=weddingPlanId,proto3" json:"wedding_plan_id,omitempty"`
	WeddingGuestType     uint32   `protobuf:"varint,2,opt,name=wedding_guest_type,json=weddingGuestType,proto3" json:"wedding_guest_type,omitempty"`
	Uid                  uint32   `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelWeddingGuestRequest) Reset()         { *m = DelWeddingGuestRequest{} }
func (m *DelWeddingGuestRequest) String() string { return proto.CompactTextString(m) }
func (*DelWeddingGuestRequest) ProtoMessage()    {}
func (*DelWeddingGuestRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{21}
}
func (m *DelWeddingGuestRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelWeddingGuestRequest.Unmarshal(m, b)
}
func (m *DelWeddingGuestRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelWeddingGuestRequest.Marshal(b, m, deterministic)
}
func (dst *DelWeddingGuestRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelWeddingGuestRequest.Merge(dst, src)
}
func (m *DelWeddingGuestRequest) XXX_Size() int {
	return xxx_messageInfo_DelWeddingGuestRequest.Size(m)
}
func (m *DelWeddingGuestRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DelWeddingGuestRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DelWeddingGuestRequest proto.InternalMessageInfo

func (m *DelWeddingGuestRequest) GetWeddingPlanId() uint32 {
	if m != nil {
		return m.WeddingPlanId
	}
	return 0
}

func (m *DelWeddingGuestRequest) GetWeddingGuestType() uint32 {
	if m != nil {
		return m.WeddingGuestType
	}
	return 0
}

func (m *DelWeddingGuestRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type DelWeddingGuestResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelWeddingGuestResponse) Reset()         { *m = DelWeddingGuestResponse{} }
func (m *DelWeddingGuestResponse) String() string { return proto.CompactTextString(m) }
func (*DelWeddingGuestResponse) ProtoMessage()    {}
func (*DelWeddingGuestResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{22}
}
func (m *DelWeddingGuestResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelWeddingGuestResponse.Unmarshal(m, b)
}
func (m *DelWeddingGuestResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelWeddingGuestResponse.Marshal(b, m, deterministic)
}
func (dst *DelWeddingGuestResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelWeddingGuestResponse.Merge(dst, src)
}
func (m *DelWeddingGuestResponse) XXX_Size() int {
	return xxx_messageInfo_DelWeddingGuestResponse.Size(m)
}
func (m *DelWeddingGuestResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DelWeddingGuestResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DelWeddingGuestResponse proto.InternalMessageInfo

// 预约时段信息
type ReserveTimeInfo struct {
	ReserveTimeSection   string   `protobuf:"bytes,2,opt,name=reserve_time_section,json=reserveTimeSection,proto3" json:"reserve_time_section,omitempty"`
	IsFullyReserved      bool     `protobuf:"varint,3,opt,name=is_fully_reserved,json=isFullyReserved,proto3" json:"is_fully_reserved,omitempty"`
	GroomUid             uint32   `protobuf:"varint,4,opt,name=groom_uid,json=groomUid,proto3" json:"groom_uid,omitempty"`
	BrideUid             uint32   `protobuf:"varint,5,opt,name=bride_uid,json=brideUid,proto3" json:"bride_uid,omitempty"`
	IsHot                bool     `protobuf:"varint,6,opt,name=is_hot,json=isHot,proto3" json:"is_hot,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReserveTimeInfo) Reset()         { *m = ReserveTimeInfo{} }
func (m *ReserveTimeInfo) String() string { return proto.CompactTextString(m) }
func (*ReserveTimeInfo) ProtoMessage()    {}
func (*ReserveTimeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{23}
}
func (m *ReserveTimeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReserveTimeInfo.Unmarshal(m, b)
}
func (m *ReserveTimeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReserveTimeInfo.Marshal(b, m, deterministic)
}
func (dst *ReserveTimeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReserveTimeInfo.Merge(dst, src)
}
func (m *ReserveTimeInfo) XXX_Size() int {
	return xxx_messageInfo_ReserveTimeInfo.Size(m)
}
func (m *ReserveTimeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ReserveTimeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ReserveTimeInfo proto.InternalMessageInfo

func (m *ReserveTimeInfo) GetReserveTimeSection() string {
	if m != nil {
		return m.ReserveTimeSection
	}
	return ""
}

func (m *ReserveTimeInfo) GetIsFullyReserved() bool {
	if m != nil {
		return m.IsFullyReserved
	}
	return false
}

func (m *ReserveTimeInfo) GetGroomUid() uint32 {
	if m != nil {
		return m.GroomUid
	}
	return 0
}

func (m *ReserveTimeInfo) GetBrideUid() uint32 {
	if m != nil {
		return m.BrideUid
	}
	return 0
}

func (m *ReserveTimeInfo) GetIsHot() bool {
	if m != nil {
		return m.IsHot
	}
	return false
}

type GetAllThemeCfgRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllThemeCfgRequest) Reset()         { *m = GetAllThemeCfgRequest{} }
func (m *GetAllThemeCfgRequest) String() string { return proto.CompactTextString(m) }
func (*GetAllThemeCfgRequest) ProtoMessage()    {}
func (*GetAllThemeCfgRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{24}
}
func (m *GetAllThemeCfgRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllThemeCfgRequest.Unmarshal(m, b)
}
func (m *GetAllThemeCfgRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllThemeCfgRequest.Marshal(b, m, deterministic)
}
func (dst *GetAllThemeCfgRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllThemeCfgRequest.Merge(dst, src)
}
func (m *GetAllThemeCfgRequest) XXX_Size() int {
	return xxx_messageInfo_GetAllThemeCfgRequest.Size(m)
}
func (m *GetAllThemeCfgRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllThemeCfgRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllThemeCfgRequest proto.InternalMessageInfo

type WeddingDressPreview struct {
	Level                uint32                `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"`
	GuessType            uint32                `protobuf:"varint,2,opt,name=guess_type,json=guessType,proto3" json:"guess_type,omitempty"`
	WeddingDress         *WeddingAnimationInfo `protobuf:"bytes,3,opt,name=wedding_dress,json=weddingDress,proto3" json:"wedding_dress,omitempty"`
	DressText            string                `protobuf:"bytes,4,opt,name=dress_text,json=dressText,proto3" json:"dress_text,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *WeddingDressPreview) Reset()         { *m = WeddingDressPreview{} }
func (m *WeddingDressPreview) String() string { return proto.CompactTextString(m) }
func (*WeddingDressPreview) ProtoMessage()    {}
func (*WeddingDressPreview) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{25}
}
func (m *WeddingDressPreview) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingDressPreview.Unmarshal(m, b)
}
func (m *WeddingDressPreview) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingDressPreview.Marshal(b, m, deterministic)
}
func (dst *WeddingDressPreview) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingDressPreview.Merge(dst, src)
}
func (m *WeddingDressPreview) XXX_Size() int {
	return xxx_messageInfo_WeddingDressPreview.Size(m)
}
func (m *WeddingDressPreview) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingDressPreview.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingDressPreview proto.InternalMessageInfo

func (m *WeddingDressPreview) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *WeddingDressPreview) GetGuessType() uint32 {
	if m != nil {
		return m.GuessType
	}
	return 0
}

func (m *WeddingDressPreview) GetWeddingDress() *WeddingAnimationInfo {
	if m != nil {
		return m.WeddingDress
	}
	return nil
}

func (m *WeddingDressPreview) GetDressText() string {
	if m != nil {
		return m.DressText
	}
	return ""
}

type WeddingDressPreviewList struct {
	// 初中高三个等级资源, 免费只有一个
	DressPreviewList     []*WeddingDressPreview `protobuf:"bytes,1,rep,name=dress_preview_list,json=dressPreviewList,proto3" json:"dress_preview_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *WeddingDressPreviewList) Reset()         { *m = WeddingDressPreviewList{} }
func (m *WeddingDressPreviewList) String() string { return proto.CompactTextString(m) }
func (*WeddingDressPreviewList) ProtoMessage()    {}
func (*WeddingDressPreviewList) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{26}
}
func (m *WeddingDressPreviewList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingDressPreviewList.Unmarshal(m, b)
}
func (m *WeddingDressPreviewList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingDressPreviewList.Marshal(b, m, deterministic)
}
func (dst *WeddingDressPreviewList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingDressPreviewList.Merge(dst, src)
}
func (m *WeddingDressPreviewList) XXX_Size() int {
	return xxx_messageInfo_WeddingDressPreviewList.Size(m)
}
func (m *WeddingDressPreviewList) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingDressPreviewList.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingDressPreviewList proto.InternalMessageInfo

func (m *WeddingDressPreviewList) GetDressPreviewList() []*WeddingDressPreview {
	if m != nil {
		return m.DressPreviewList
	}
	return nil
}

type FinishWeddingAward struct {
	AwardAnimation       *WeddingAnimationInfo `protobuf:"bytes,1,opt,name=award_animation,json=awardAnimation,proto3" json:"award_animation,omitempty"`
	TopText              string                `protobuf:"bytes,2,opt,name=top_text,json=topText,proto3" json:"top_text,omitempty"`
	BottomText           string                `protobuf:"bytes,3,opt,name=bottom_text,json=bottomText,proto3" json:"bottom_text,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *FinishWeddingAward) Reset()         { *m = FinishWeddingAward{} }
func (m *FinishWeddingAward) String() string { return proto.CompactTextString(m) }
func (*FinishWeddingAward) ProtoMessage()    {}
func (*FinishWeddingAward) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{27}
}
func (m *FinishWeddingAward) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FinishWeddingAward.Unmarshal(m, b)
}
func (m *FinishWeddingAward) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FinishWeddingAward.Marshal(b, m, deterministic)
}
func (dst *FinishWeddingAward) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FinishWeddingAward.Merge(dst, src)
}
func (m *FinishWeddingAward) XXX_Size() int {
	return xxx_messageInfo_FinishWeddingAward.Size(m)
}
func (m *FinishWeddingAward) XXX_DiscardUnknown() {
	xxx_messageInfo_FinishWeddingAward.DiscardUnknown(m)
}

var xxx_messageInfo_FinishWeddingAward proto.InternalMessageInfo

func (m *FinishWeddingAward) GetAwardAnimation() *WeddingAnimationInfo {
	if m != nil {
		return m.AwardAnimation
	}
	return nil
}

func (m *FinishWeddingAward) GetTopText() string {
	if m != nil {
		return m.TopText
	}
	return ""
}

func (m *FinishWeddingAward) GetBottomText() string {
	if m != nil {
		return m.BottomText
	}
	return ""
}

type WeddingPriceInfo struct {
	Price                uint32   `protobuf:"varint,1,opt,name=price,proto3" json:"price,omitempty"`
	PriceType            uint32   `protobuf:"varint,2,opt,name=price_type,json=priceType,proto3" json:"price_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WeddingPriceInfo) Reset()         { *m = WeddingPriceInfo{} }
func (m *WeddingPriceInfo) String() string { return proto.CompactTextString(m) }
func (*WeddingPriceInfo) ProtoMessage()    {}
func (*WeddingPriceInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{28}
}
func (m *WeddingPriceInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingPriceInfo.Unmarshal(m, b)
}
func (m *WeddingPriceInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingPriceInfo.Marshal(b, m, deterministic)
}
func (dst *WeddingPriceInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingPriceInfo.Merge(dst, src)
}
func (m *WeddingPriceInfo) XXX_Size() int {
	return xxx_messageInfo_WeddingPriceInfo.Size(m)
}
func (m *WeddingPriceInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingPriceInfo.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingPriceInfo proto.InternalMessageInfo

func (m *WeddingPriceInfo) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *WeddingPriceInfo) GetPriceType() uint32 {
	if m != nil {
		return m.PriceType
	}
	return 0
}

type ThemeCfg struct {
	ThemeId             uint32                              `protobuf:"varint,1,opt,name=theme_id,json=themeId,proto3" json:"theme_id,omitempty"`
	ThemeName           string                              `protobuf:"bytes,2,opt,name=theme_name,json=themeName,proto3" json:"theme_name,omitempty"`
	PriceInfo           *WeddingPriceInfo                   `protobuf:"bytes,3,opt,name=price_info,json=priceInfo,proto3" json:"price_info,omitempty"`
	ScenePreviewList    []*WeddingScenePreview              `protobuf:"bytes,4,rep,name=scene_preview_list,json=scenePreviewList,proto3" json:"scene_preview_list,omitempty"`
	DressPreviewMap     map[uint32]*WeddingDressPreviewList `protobuf:"bytes,5,rep,name=dress_preview_map,json=dressPreviewMap,proto3" json:"dress_preview_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	MaxShowGroomsmanNum uint32                              `protobuf:"varint,6,opt,name=max_show_groomsman_num,json=maxShowGroomsmanNum,proto3" json:"max_show_groomsman_num,omitempty"`
	MaxShowFamilyNum    uint32                              `protobuf:"varint,7,opt,name=max_show_family_num,json=maxShowFamilyNum,proto3" json:"max_show_family_num,omitempty"`
	SelectedIcon        string                              `protobuf:"bytes,8,opt,name=selected_icon,json=selectedIcon,proto3" json:"selected_icon,omitempty"`
	UnselectedIcon      string                              `protobuf:"bytes,9,opt,name=unselected_icon,json=unselectedIcon,proto3" json:"unselected_icon,omitempty"`
	// 礼成奖励
	RewardInfoList       []*FinishWeddingAward `protobuf:"bytes,10,rep,name=reward_info_list,json=rewardInfoList,proto3" json:"reward_info_list,omitempty"`
	MaxBigScreenNum      uint32                `protobuf:"varint,11,opt,name=max_big_screen_num,json=maxBigScreenNum,proto3" json:"max_big_screen_num,omitempty"`
	ThemeBackground      string                `protobuf:"bytes,12,opt,name=theme_background,json=themeBackground,proto3" json:"theme_background,omitempty"`
	ThemePreviewText     string                `protobuf:"bytes,13,opt,name=theme_preview_text,json=themePreviewText,proto3" json:"theme_preview_text,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *ThemeCfg) Reset()         { *m = ThemeCfg{} }
func (m *ThemeCfg) String() string { return proto.CompactTextString(m) }
func (*ThemeCfg) ProtoMessage()    {}
func (*ThemeCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{29}
}
func (m *ThemeCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ThemeCfg.Unmarshal(m, b)
}
func (m *ThemeCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ThemeCfg.Marshal(b, m, deterministic)
}
func (dst *ThemeCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ThemeCfg.Merge(dst, src)
}
func (m *ThemeCfg) XXX_Size() int {
	return xxx_messageInfo_ThemeCfg.Size(m)
}
func (m *ThemeCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_ThemeCfg.DiscardUnknown(m)
}

var xxx_messageInfo_ThemeCfg proto.InternalMessageInfo

func (m *ThemeCfg) GetThemeId() uint32 {
	if m != nil {
		return m.ThemeId
	}
	return 0
}

func (m *ThemeCfg) GetThemeName() string {
	if m != nil {
		return m.ThemeName
	}
	return ""
}

func (m *ThemeCfg) GetPriceInfo() *WeddingPriceInfo {
	if m != nil {
		return m.PriceInfo
	}
	return nil
}

func (m *ThemeCfg) GetScenePreviewList() []*WeddingScenePreview {
	if m != nil {
		return m.ScenePreviewList
	}
	return nil
}

func (m *ThemeCfg) GetDressPreviewMap() map[uint32]*WeddingDressPreviewList {
	if m != nil {
		return m.DressPreviewMap
	}
	return nil
}

func (m *ThemeCfg) GetMaxShowGroomsmanNum() uint32 {
	if m != nil {
		return m.MaxShowGroomsmanNum
	}
	return 0
}

func (m *ThemeCfg) GetMaxShowFamilyNum() uint32 {
	if m != nil {
		return m.MaxShowFamilyNum
	}
	return 0
}

func (m *ThemeCfg) GetSelectedIcon() string {
	if m != nil {
		return m.SelectedIcon
	}
	return ""
}

func (m *ThemeCfg) GetUnselectedIcon() string {
	if m != nil {
		return m.UnselectedIcon
	}
	return ""
}

func (m *ThemeCfg) GetRewardInfoList() []*FinishWeddingAward {
	if m != nil {
		return m.RewardInfoList
	}
	return nil
}

func (m *ThemeCfg) GetMaxBigScreenNum() uint32 {
	if m != nil {
		return m.MaxBigScreenNum
	}
	return 0
}

func (m *ThemeCfg) GetThemeBackground() string {
	if m != nil {
		return m.ThemeBackground
	}
	return ""
}

func (m *ThemeCfg) GetThemePreviewText() string {
	if m != nil {
		return m.ThemePreviewText
	}
	return ""
}

type AddThemeCfgRequest struct {
	ThemeCfg             *ThemeCfg `protobuf:"bytes,1,opt,name=theme_cfg,json=themeCfg,proto3" json:"theme_cfg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *AddThemeCfgRequest) Reset()         { *m = AddThemeCfgRequest{} }
func (m *AddThemeCfgRequest) String() string { return proto.CompactTextString(m) }
func (*AddThemeCfgRequest) ProtoMessage()    {}
func (*AddThemeCfgRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{30}
}
func (m *AddThemeCfgRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddThemeCfgRequest.Unmarshal(m, b)
}
func (m *AddThemeCfgRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddThemeCfgRequest.Marshal(b, m, deterministic)
}
func (dst *AddThemeCfgRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddThemeCfgRequest.Merge(dst, src)
}
func (m *AddThemeCfgRequest) XXX_Size() int {
	return xxx_messageInfo_AddThemeCfgRequest.Size(m)
}
func (m *AddThemeCfgRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AddThemeCfgRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AddThemeCfgRequest proto.InternalMessageInfo

func (m *AddThemeCfgRequest) GetThemeCfg() *ThemeCfg {
	if m != nil {
		return m.ThemeCfg
	}
	return nil
}

type AddThemeCfgResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddThemeCfgResponse) Reset()         { *m = AddThemeCfgResponse{} }
func (m *AddThemeCfgResponse) String() string { return proto.CompactTextString(m) }
func (*AddThemeCfgResponse) ProtoMessage()    {}
func (*AddThemeCfgResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{31}
}
func (m *AddThemeCfgResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddThemeCfgResponse.Unmarshal(m, b)
}
func (m *AddThemeCfgResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddThemeCfgResponse.Marshal(b, m, deterministic)
}
func (dst *AddThemeCfgResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddThemeCfgResponse.Merge(dst, src)
}
func (m *AddThemeCfgResponse) XXX_Size() int {
	return xxx_messageInfo_AddThemeCfgResponse.Size(m)
}
func (m *AddThemeCfgResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_AddThemeCfgResponse.DiscardUnknown(m)
}

var xxx_messageInfo_AddThemeCfgResponse proto.InternalMessageInfo

type UpdateThemeCfgRequest struct {
	ThemeCfg             *ThemeCfg `protobuf:"bytes,1,opt,name=theme_cfg,json=themeCfg,proto3" json:"theme_cfg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *UpdateThemeCfgRequest) Reset()         { *m = UpdateThemeCfgRequest{} }
func (m *UpdateThemeCfgRequest) String() string { return proto.CompactTextString(m) }
func (*UpdateThemeCfgRequest) ProtoMessage()    {}
func (*UpdateThemeCfgRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{32}
}
func (m *UpdateThemeCfgRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateThemeCfgRequest.Unmarshal(m, b)
}
func (m *UpdateThemeCfgRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateThemeCfgRequest.Marshal(b, m, deterministic)
}
func (dst *UpdateThemeCfgRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateThemeCfgRequest.Merge(dst, src)
}
func (m *UpdateThemeCfgRequest) XXX_Size() int {
	return xxx_messageInfo_UpdateThemeCfgRequest.Size(m)
}
func (m *UpdateThemeCfgRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateThemeCfgRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateThemeCfgRequest proto.InternalMessageInfo

func (m *UpdateThemeCfgRequest) GetThemeCfg() *ThemeCfg {
	if m != nil {
		return m.ThemeCfg
	}
	return nil
}

type UpdateThemeCfgResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateThemeCfgResponse) Reset()         { *m = UpdateThemeCfgResponse{} }
func (m *UpdateThemeCfgResponse) String() string { return proto.CompactTextString(m) }
func (*UpdateThemeCfgResponse) ProtoMessage()    {}
func (*UpdateThemeCfgResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{33}
}
func (m *UpdateThemeCfgResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateThemeCfgResponse.Unmarshal(m, b)
}
func (m *UpdateThemeCfgResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateThemeCfgResponse.Marshal(b, m, deterministic)
}
func (dst *UpdateThemeCfgResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateThemeCfgResponse.Merge(dst, src)
}
func (m *UpdateThemeCfgResponse) XXX_Size() int {
	return xxx_messageInfo_UpdateThemeCfgResponse.Size(m)
}
func (m *UpdateThemeCfgResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateThemeCfgResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateThemeCfgResponse proto.InternalMessageInfo

type DeleteThemeCfgRequest struct {
	ThemeId              uint32   `protobuf:"varint,1,opt,name=theme_id,json=themeId,proto3" json:"theme_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteThemeCfgRequest) Reset()         { *m = DeleteThemeCfgRequest{} }
func (m *DeleteThemeCfgRequest) String() string { return proto.CompactTextString(m) }
func (*DeleteThemeCfgRequest) ProtoMessage()    {}
func (*DeleteThemeCfgRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{34}
}
func (m *DeleteThemeCfgRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteThemeCfgRequest.Unmarshal(m, b)
}
func (m *DeleteThemeCfgRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteThemeCfgRequest.Marshal(b, m, deterministic)
}
func (dst *DeleteThemeCfgRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteThemeCfgRequest.Merge(dst, src)
}
func (m *DeleteThemeCfgRequest) XXX_Size() int {
	return xxx_messageInfo_DeleteThemeCfgRequest.Size(m)
}
func (m *DeleteThemeCfgRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteThemeCfgRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteThemeCfgRequest proto.InternalMessageInfo

func (m *DeleteThemeCfgRequest) GetThemeId() uint32 {
	if m != nil {
		return m.ThemeId
	}
	return 0
}

type DeleteThemeCfgResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteThemeCfgResponse) Reset()         { *m = DeleteThemeCfgResponse{} }
func (m *DeleteThemeCfgResponse) String() string { return proto.CompactTextString(m) }
func (*DeleteThemeCfgResponse) ProtoMessage()    {}
func (*DeleteThemeCfgResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{35}
}
func (m *DeleteThemeCfgResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteThemeCfgResponse.Unmarshal(m, b)
}
func (m *DeleteThemeCfgResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteThemeCfgResponse.Marshal(b, m, deterministic)
}
func (dst *DeleteThemeCfgResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteThemeCfgResponse.Merge(dst, src)
}
func (m *DeleteThemeCfgResponse) XXX_Size() int {
	return xxx_messageInfo_DeleteThemeCfgResponse.Size(m)
}
func (m *DeleteThemeCfgResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteThemeCfgResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteThemeCfgResponse proto.InternalMessageInfo

// 动画资源
type WeddingAnimationInfo struct {
	AnimationType        uint32   `protobuf:"varint,1,opt,name=animation_type,json=animationType,proto3" json:"animation_type,omitempty"`
	AnimationResource    string   `protobuf:"bytes,2,opt,name=animation_resource,json=animationResource,proto3" json:"animation_resource,omitempty"`
	AnimationResourceMd5 string   `protobuf:"bytes,3,opt,name=animation_resource_md5,json=animationResourceMd5,proto3" json:"animation_resource_md5,omitempty"`
	AnimationPng         string   `protobuf:"bytes,4,opt,name=animation_png,json=animationPng,proto3" json:"animation_png,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WeddingAnimationInfo) Reset()         { *m = WeddingAnimationInfo{} }
func (m *WeddingAnimationInfo) String() string { return proto.CompactTextString(m) }
func (*WeddingAnimationInfo) ProtoMessage()    {}
func (*WeddingAnimationInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{36}
}
func (m *WeddingAnimationInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingAnimationInfo.Unmarshal(m, b)
}
func (m *WeddingAnimationInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingAnimationInfo.Marshal(b, m, deterministic)
}
func (dst *WeddingAnimationInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingAnimationInfo.Merge(dst, src)
}
func (m *WeddingAnimationInfo) XXX_Size() int {
	return xxx_messageInfo_WeddingAnimationInfo.Size(m)
}
func (m *WeddingAnimationInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingAnimationInfo.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingAnimationInfo proto.InternalMessageInfo

func (m *WeddingAnimationInfo) GetAnimationType() uint32 {
	if m != nil {
		return m.AnimationType
	}
	return 0
}

func (m *WeddingAnimationInfo) GetAnimationResource() string {
	if m != nil {
		return m.AnimationResource
	}
	return ""
}

func (m *WeddingAnimationInfo) GetAnimationResourceMd5() string {
	if m != nil {
		return m.AnimationResourceMd5
	}
	return ""
}

func (m *WeddingAnimationInfo) GetAnimationPng() string {
	if m != nil {
		return m.AnimationPng
	}
	return ""
}

type WeddingScenePreview struct {
	Level                uint32                `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"`
	SceneAnimation       *WeddingAnimationInfo `protobuf:"bytes,2,opt,name=scene_animation,json=sceneAnimation,proto3" json:"scene_animation,omitempty"`
	SmallIcon            string                `protobuf:"bytes,3,opt,name=small_icon,json=smallIcon,proto3" json:"small_icon,omitempty"`
	ZoomAnimation        *WeddingAnimationInfo `protobuf:"bytes,4,opt,name=zoom_animation,json=zoomAnimation,proto3" json:"zoom_animation,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *WeddingScenePreview) Reset()         { *m = WeddingScenePreview{} }
func (m *WeddingScenePreview) String() string { return proto.CompactTextString(m) }
func (*WeddingScenePreview) ProtoMessage()    {}
func (*WeddingScenePreview) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{37}
}
func (m *WeddingScenePreview) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingScenePreview.Unmarshal(m, b)
}
func (m *WeddingScenePreview) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingScenePreview.Marshal(b, m, deterministic)
}
func (dst *WeddingScenePreview) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingScenePreview.Merge(dst, src)
}
func (m *WeddingScenePreview) XXX_Size() int {
	return xxx_messageInfo_WeddingScenePreview.Size(m)
}
func (m *WeddingScenePreview) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingScenePreview.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingScenePreview proto.InternalMessageInfo

func (m *WeddingScenePreview) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *WeddingScenePreview) GetSceneAnimation() *WeddingAnimationInfo {
	if m != nil {
		return m.SceneAnimation
	}
	return nil
}

func (m *WeddingScenePreview) GetSmallIcon() string {
	if m != nil {
		return m.SmallIcon
	}
	return ""
}

func (m *WeddingScenePreview) GetZoomAnimation() *WeddingAnimationInfo {
	if m != nil {
		return m.ZoomAnimation
	}
	return nil
}

type GetAllThemeCfgResponse struct {
	ThemeCfg             []*ThemeCfg `protobuf:"bytes,1,rep,name=theme_cfg,json=themeCfg,proto3" json:"theme_cfg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetAllThemeCfgResponse) Reset()         { *m = GetAllThemeCfgResponse{} }
func (m *GetAllThemeCfgResponse) String() string { return proto.CompactTextString(m) }
func (*GetAllThemeCfgResponse) ProtoMessage()    {}
func (*GetAllThemeCfgResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{38}
}
func (m *GetAllThemeCfgResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllThemeCfgResponse.Unmarshal(m, b)
}
func (m *GetAllThemeCfgResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllThemeCfgResponse.Marshal(b, m, deterministic)
}
func (dst *GetAllThemeCfgResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllThemeCfgResponse.Merge(dst, src)
}
func (m *GetAllThemeCfgResponse) XXX_Size() int {
	return xxx_messageInfo_GetAllThemeCfgResponse.Size(m)
}
func (m *GetAllThemeCfgResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllThemeCfgResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllThemeCfgResponse proto.InternalMessageInfo

func (m *GetAllThemeCfgResponse) GetThemeCfg() []*ThemeCfg {
	if m != nil {
		return m.ThemeCfg
	}
	return nil
}

type CancelWeddingRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	WeddingPlanId        uint32   `protobuf:"varint,2,opt,name=wedding_plan_id,json=weddingPlanId,proto3" json:"wedding_plan_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CancelWeddingRequest) Reset()         { *m = CancelWeddingRequest{} }
func (m *CancelWeddingRequest) String() string { return proto.CompactTextString(m) }
func (*CancelWeddingRequest) ProtoMessage()    {}
func (*CancelWeddingRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{39}
}
func (m *CancelWeddingRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelWeddingRequest.Unmarshal(m, b)
}
func (m *CancelWeddingRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelWeddingRequest.Marshal(b, m, deterministic)
}
func (dst *CancelWeddingRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelWeddingRequest.Merge(dst, src)
}
func (m *CancelWeddingRequest) XXX_Size() int {
	return xxx_messageInfo_CancelWeddingRequest.Size(m)
}
func (m *CancelWeddingRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelWeddingRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CancelWeddingRequest proto.InternalMessageInfo

func (m *CancelWeddingRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CancelWeddingRequest) GetWeddingPlanId() uint32 {
	if m != nil {
		return m.WeddingPlanId
	}
	return 0
}

type CancelWeddingResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CancelWeddingResponse) Reset()         { *m = CancelWeddingResponse{} }
func (m *CancelWeddingResponse) String() string { return proto.CompactTextString(m) }
func (*CancelWeddingResponse) ProtoMessage()    {}
func (*CancelWeddingResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{40}
}
func (m *CancelWeddingResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelWeddingResponse.Unmarshal(m, b)
}
func (m *CancelWeddingResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelWeddingResponse.Marshal(b, m, deterministic)
}
func (dst *CancelWeddingResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelWeddingResponse.Merge(dst, src)
}
func (m *CancelWeddingResponse) XXX_Size() int {
	return xxx_messageInfo_CancelWeddingResponse.Size(m)
}
func (m *CancelWeddingResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelWeddingResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CancelWeddingResponse proto.InternalMessageInfo

type AdminCancelWeddingRequest struct {
	WeddingPlanId        uint32   `protobuf:"varint,1,opt,name=wedding_plan_id,json=weddingPlanId,proto3" json:"wedding_plan_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AdminCancelWeddingRequest) Reset()         { *m = AdminCancelWeddingRequest{} }
func (m *AdminCancelWeddingRequest) String() string { return proto.CompactTextString(m) }
func (*AdminCancelWeddingRequest) ProtoMessage()    {}
func (*AdminCancelWeddingRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{41}
}
func (m *AdminCancelWeddingRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AdminCancelWeddingRequest.Unmarshal(m, b)
}
func (m *AdminCancelWeddingRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AdminCancelWeddingRequest.Marshal(b, m, deterministic)
}
func (dst *AdminCancelWeddingRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AdminCancelWeddingRequest.Merge(dst, src)
}
func (m *AdminCancelWeddingRequest) XXX_Size() int {
	return xxx_messageInfo_AdminCancelWeddingRequest.Size(m)
}
func (m *AdminCancelWeddingRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AdminCancelWeddingRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AdminCancelWeddingRequest proto.InternalMessageInfo

func (m *AdminCancelWeddingRequest) GetWeddingPlanId() uint32 {
	if m != nil {
		return m.WeddingPlanId
	}
	return 0
}

type AdminCancelWeddingResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AdminCancelWeddingResponse) Reset()         { *m = AdminCancelWeddingResponse{} }
func (m *AdminCancelWeddingResponse) String() string { return proto.CompactTextString(m) }
func (*AdminCancelWeddingResponse) ProtoMessage()    {}
func (*AdminCancelWeddingResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{42}
}
func (m *AdminCancelWeddingResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AdminCancelWeddingResponse.Unmarshal(m, b)
}
func (m *AdminCancelWeddingResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AdminCancelWeddingResponse.Marshal(b, m, deterministic)
}
func (dst *AdminCancelWeddingResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AdminCancelWeddingResponse.Merge(dst, src)
}
func (m *AdminCancelWeddingResponse) XXX_Size() int {
	return xxx_messageInfo_AdminCancelWeddingResponse.Size(m)
}
func (m *AdminCancelWeddingResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_AdminCancelWeddingResponse.DiscardUnknown(m)
}

var xxx_messageInfo_AdminCancelWeddingResponse proto.InternalMessageInfo

type GetMyWeddingInfoRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMyWeddingInfoRequest) Reset()         { *m = GetMyWeddingInfoRequest{} }
func (m *GetMyWeddingInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetMyWeddingInfoRequest) ProtoMessage()    {}
func (*GetMyWeddingInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{43}
}
func (m *GetMyWeddingInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMyWeddingInfoRequest.Unmarshal(m, b)
}
func (m *GetMyWeddingInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMyWeddingInfoRequest.Marshal(b, m, deterministic)
}
func (dst *GetMyWeddingInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMyWeddingInfoRequest.Merge(dst, src)
}
func (m *GetMyWeddingInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetMyWeddingInfoRequest.Size(m)
}
func (m *GetMyWeddingInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMyWeddingInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetMyWeddingInfoRequest proto.InternalMessageInfo

func (m *GetMyWeddingInfoRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type PartnerInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	IsAccept             bool     `protobuf:"varint,2,opt,name=is_accept,json=isAccept,proto3" json:"is_accept,omitempty"`
	TogetherTime         uint32   `protobuf:"varint,3,opt,name=together_time,json=togetherTime,proto3" json:"together_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PartnerInfo) Reset()         { *m = PartnerInfo{} }
func (m *PartnerInfo) String() string { return proto.CompactTextString(m) }
func (*PartnerInfo) ProtoMessage()    {}
func (*PartnerInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{44}
}
func (m *PartnerInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PartnerInfo.Unmarshal(m, b)
}
func (m *PartnerInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PartnerInfo.Marshal(b, m, deterministic)
}
func (dst *PartnerInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PartnerInfo.Merge(dst, src)
}
func (m *PartnerInfo) XXX_Size() int {
	return xxx_messageInfo_PartnerInfo.Size(m)
}
func (m *PartnerInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PartnerInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PartnerInfo proto.InternalMessageInfo

func (m *PartnerInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *PartnerInfo) GetIsAccept() bool {
	if m != nil {
		return m.IsAccept
	}
	return false
}

func (m *PartnerInfo) GetTogetherTime() uint32 {
	if m != nil {
		return m.TogetherTime
	}
	return 0
}

type GetMyWeddingInfoResponse struct {
	PartnerInfo          *PartnerInfo `protobuf:"bytes,1,opt,name=partner_info,json=partnerInfo,proto3" json:"partner_info,omitempty"`
	WeddingPlanId        uint32       `protobuf:"varint,2,opt,name=wedding_plan_id,json=weddingPlanId,proto3" json:"wedding_plan_id,omitempty"`
	ThemeId              uint32       `protobuf:"varint,3,opt,name=theme_id,json=themeId,proto3" json:"theme_id,omitempty"`
	BuyerUid             uint32       `protobuf:"varint,4,opt,name=buyer_uid,json=buyerUid,proto3" json:"buyer_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetMyWeddingInfoResponse) Reset()         { *m = GetMyWeddingInfoResponse{} }
func (m *GetMyWeddingInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetMyWeddingInfoResponse) ProtoMessage()    {}
func (*GetMyWeddingInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{45}
}
func (m *GetMyWeddingInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMyWeddingInfoResponse.Unmarshal(m, b)
}
func (m *GetMyWeddingInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMyWeddingInfoResponse.Marshal(b, m, deterministic)
}
func (dst *GetMyWeddingInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMyWeddingInfoResponse.Merge(dst, src)
}
func (m *GetMyWeddingInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetMyWeddingInfoResponse.Size(m)
}
func (m *GetMyWeddingInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMyWeddingInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetMyWeddingInfoResponse proto.InternalMessageInfo

func (m *GetMyWeddingInfoResponse) GetPartnerInfo() *PartnerInfo {
	if m != nil {
		return m.PartnerInfo
	}
	return nil
}

func (m *GetMyWeddingInfoResponse) GetWeddingPlanId() uint32 {
	if m != nil {
		return m.WeddingPlanId
	}
	return 0
}

func (m *GetMyWeddingInfoResponse) GetThemeId() uint32 {
	if m != nil {
		return m.ThemeId
	}
	return 0
}

func (m *GetMyWeddingInfoResponse) GetBuyerUid() uint32 {
	if m != nil {
		return m.BuyerUid
	}
	return 0
}

type WeddingPlanInfo struct {
	WeddingPlanId        uint32                  `protobuf:"varint,1,opt,name=wedding_plan_id,json=weddingPlanId,proto3" json:"wedding_plan_id,omitempty"`
	GroomUid             uint32                  `protobuf:"varint,2,opt,name=groom_uid,json=groomUid,proto3" json:"groom_uid,omitempty"`
	BrideUid             uint32                  `protobuf:"varint,3,opt,name=bride_uid,json=brideUid,proto3" json:"bride_uid,omitempty"`
	ThemeId              uint32                  `protobuf:"varint,4,opt,name=theme_id,json=themeId,proto3" json:"theme_id,omitempty"`
	IsThemeFree          bool                    `protobuf:"varint,5,opt,name=is_theme_free,json=isThemeFree,proto3" json:"is_theme_free,omitempty"`
	ReserveInfo          *WeddingPlanReserveInfo `protobuf:"bytes,6,opt,name=reserve_info,json=reserveInfo,proto3" json:"reserve_info,omitempty"`
	GroomsmanUidList     []uint32                `protobuf:"varint,7,rep,packed,name=groomsman_uid_list,json=groomsmanUidList,proto3" json:"groomsman_uid_list,omitempty"`
	BridesmaidUidList    []uint32                `protobuf:"varint,8,rep,packed,name=bridesmaid_uid_list,json=bridesmaidUidList,proto3" json:"bridesmaid_uid_list,omitempty"`
	HostUid              uint32                  `protobuf:"varint,9,opt,name=host_uid,json=hostUid,proto3" json:"host_uid,omitempty"`
	BigScreenList        []*BigScreenItem        `protobuf:"bytes,10,rep,name=big_screen_list,json=bigScreenList,proto3" json:"big_screen_list,omitempty"`
	Status               uint32                  `protobuf:"varint,11,opt,name=status,proto3" json:"status,omitempty"`
	IsHot                bool                    `protobuf:"varint,12,opt,name=is_hot,json=isHot,proto3" json:"is_hot,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *WeddingPlanInfo) Reset()         { *m = WeddingPlanInfo{} }
func (m *WeddingPlanInfo) String() string { return proto.CompactTextString(m) }
func (*WeddingPlanInfo) ProtoMessage()    {}
func (*WeddingPlanInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{46}
}
func (m *WeddingPlanInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingPlanInfo.Unmarshal(m, b)
}
func (m *WeddingPlanInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingPlanInfo.Marshal(b, m, deterministic)
}
func (dst *WeddingPlanInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingPlanInfo.Merge(dst, src)
}
func (m *WeddingPlanInfo) XXX_Size() int {
	return xxx_messageInfo_WeddingPlanInfo.Size(m)
}
func (m *WeddingPlanInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingPlanInfo.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingPlanInfo proto.InternalMessageInfo

func (m *WeddingPlanInfo) GetWeddingPlanId() uint32 {
	if m != nil {
		return m.WeddingPlanId
	}
	return 0
}

func (m *WeddingPlanInfo) GetGroomUid() uint32 {
	if m != nil {
		return m.GroomUid
	}
	return 0
}

func (m *WeddingPlanInfo) GetBrideUid() uint32 {
	if m != nil {
		return m.BrideUid
	}
	return 0
}

func (m *WeddingPlanInfo) GetThemeId() uint32 {
	if m != nil {
		return m.ThemeId
	}
	return 0
}

func (m *WeddingPlanInfo) GetIsThemeFree() bool {
	if m != nil {
		return m.IsThemeFree
	}
	return false
}

func (m *WeddingPlanInfo) GetReserveInfo() *WeddingPlanReserveInfo {
	if m != nil {
		return m.ReserveInfo
	}
	return nil
}

func (m *WeddingPlanInfo) GetGroomsmanUidList() []uint32 {
	if m != nil {
		return m.GroomsmanUidList
	}
	return nil
}

func (m *WeddingPlanInfo) GetBridesmaidUidList() []uint32 {
	if m != nil {
		return m.BridesmaidUidList
	}
	return nil
}

func (m *WeddingPlanInfo) GetHostUid() uint32 {
	if m != nil {
		return m.HostUid
	}
	return 0
}

func (m *WeddingPlanInfo) GetBigScreenList() []*BigScreenItem {
	if m != nil {
		return m.BigScreenList
	}
	return nil
}

func (m *WeddingPlanInfo) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *WeddingPlanInfo) GetIsHot() bool {
	if m != nil {
		return m.IsHot
	}
	return false
}

type WeddingPlanReserveInfo struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	StartTs              uint32   `protobuf:"varint,2,opt,name=start_ts,json=startTs,proto3" json:"start_ts,omitempty"`
	EndTs                uint32   `protobuf:"varint,3,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WeddingPlanReserveInfo) Reset()         { *m = WeddingPlanReserveInfo{} }
func (m *WeddingPlanReserveInfo) String() string { return proto.CompactTextString(m) }
func (*WeddingPlanReserveInfo) ProtoMessage()    {}
func (*WeddingPlanReserveInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{47}
}
func (m *WeddingPlanReserveInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingPlanReserveInfo.Unmarshal(m, b)
}
func (m *WeddingPlanReserveInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingPlanReserveInfo.Marshal(b, m, deterministic)
}
func (dst *WeddingPlanReserveInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingPlanReserveInfo.Merge(dst, src)
}
func (m *WeddingPlanReserveInfo) XXX_Size() int {
	return xxx_messageInfo_WeddingPlanReserveInfo.Size(m)
}
func (m *WeddingPlanReserveInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingPlanReserveInfo.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingPlanReserveInfo proto.InternalMessageInfo

func (m *WeddingPlanReserveInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *WeddingPlanReserveInfo) GetStartTs() uint32 {
	if m != nil {
		return m.StartTs
	}
	return 0
}

func (m *WeddingPlanReserveInfo) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

type BatGetWeddingInfoByIdRequest struct {
	WeddingPlanIdList    []uint32 `protobuf:"varint,1,rep,packed,name=wedding_plan_id_list,json=weddingPlanIdList,proto3" json:"wedding_plan_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatGetWeddingInfoByIdRequest) Reset()         { *m = BatGetWeddingInfoByIdRequest{} }
func (m *BatGetWeddingInfoByIdRequest) String() string { return proto.CompactTextString(m) }
func (*BatGetWeddingInfoByIdRequest) ProtoMessage()    {}
func (*BatGetWeddingInfoByIdRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{48}
}
func (m *BatGetWeddingInfoByIdRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetWeddingInfoByIdRequest.Unmarshal(m, b)
}
func (m *BatGetWeddingInfoByIdRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetWeddingInfoByIdRequest.Marshal(b, m, deterministic)
}
func (dst *BatGetWeddingInfoByIdRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetWeddingInfoByIdRequest.Merge(dst, src)
}
func (m *BatGetWeddingInfoByIdRequest) XXX_Size() int {
	return xxx_messageInfo_BatGetWeddingInfoByIdRequest.Size(m)
}
func (m *BatGetWeddingInfoByIdRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetWeddingInfoByIdRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetWeddingInfoByIdRequest proto.InternalMessageInfo

func (m *BatGetWeddingInfoByIdRequest) GetWeddingPlanIdList() []uint32 {
	if m != nil {
		return m.WeddingPlanIdList
	}
	return nil
}

type BatGetWeddingInfoByIdResponse struct {
	WeddingInfoMap       map[uint32]*WeddingPlanInfo `protobuf:"bytes,1,rep,name=wedding_info_map,json=weddingInfoMap,proto3" json:"wedding_info_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *BatGetWeddingInfoByIdResponse) Reset()         { *m = BatGetWeddingInfoByIdResponse{} }
func (m *BatGetWeddingInfoByIdResponse) String() string { return proto.CompactTextString(m) }
func (*BatGetWeddingInfoByIdResponse) ProtoMessage()    {}
func (*BatGetWeddingInfoByIdResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{49}
}
func (m *BatGetWeddingInfoByIdResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetWeddingInfoByIdResponse.Unmarshal(m, b)
}
func (m *BatGetWeddingInfoByIdResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetWeddingInfoByIdResponse.Marshal(b, m, deterministic)
}
func (dst *BatGetWeddingInfoByIdResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetWeddingInfoByIdResponse.Merge(dst, src)
}
func (m *BatGetWeddingInfoByIdResponse) XXX_Size() int {
	return xxx_messageInfo_BatGetWeddingInfoByIdResponse.Size(m)
}
func (m *BatGetWeddingInfoByIdResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetWeddingInfoByIdResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetWeddingInfoByIdResponse proto.InternalMessageInfo

func (m *BatGetWeddingInfoByIdResponse) GetWeddingInfoMap() map[uint32]*WeddingPlanInfo {
	if m != nil {
		return m.WeddingInfoMap
	}
	return nil
}

type PageGetComingWeddingListRequest struct {
	PageNum              uint32   `protobuf:"varint,1,opt,name=page_num,json=pageNum,proto3" json:"page_num,omitempty"`
	PageSize             uint32   `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	NowTs                int64    `protobuf:"varint,3,opt,name=now_ts,json=nowTs,proto3" json:"now_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PageGetComingWeddingListRequest) Reset()         { *m = PageGetComingWeddingListRequest{} }
func (m *PageGetComingWeddingListRequest) String() string { return proto.CompactTextString(m) }
func (*PageGetComingWeddingListRequest) ProtoMessage()    {}
func (*PageGetComingWeddingListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{50}
}
func (m *PageGetComingWeddingListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PageGetComingWeddingListRequest.Unmarshal(m, b)
}
func (m *PageGetComingWeddingListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PageGetComingWeddingListRequest.Marshal(b, m, deterministic)
}
func (dst *PageGetComingWeddingListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PageGetComingWeddingListRequest.Merge(dst, src)
}
func (m *PageGetComingWeddingListRequest) XXX_Size() int {
	return xxx_messageInfo_PageGetComingWeddingListRequest.Size(m)
}
func (m *PageGetComingWeddingListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PageGetComingWeddingListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PageGetComingWeddingListRequest proto.InternalMessageInfo

func (m *PageGetComingWeddingListRequest) GetPageNum() uint32 {
	if m != nil {
		return m.PageNum
	}
	return 0
}

func (m *PageGetComingWeddingListRequest) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *PageGetComingWeddingListRequest) GetNowTs() int64 {
	if m != nil {
		return m.NowTs
	}
	return 0
}

type PageGetComingWeddingListResponse struct {
	WeddingList          []*WeddingPlanInfo `protobuf:"bytes,1,rep,name=wedding_list,json=weddingList,proto3" json:"wedding_list,omitempty"`
	HasMore              bool               `protobuf:"varint,2,opt,name=has_more,json=hasMore,proto3" json:"has_more,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *PageGetComingWeddingListResponse) Reset()         { *m = PageGetComingWeddingListResponse{} }
func (m *PageGetComingWeddingListResponse) String() string { return proto.CompactTextString(m) }
func (*PageGetComingWeddingListResponse) ProtoMessage()    {}
func (*PageGetComingWeddingListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{51}
}
func (m *PageGetComingWeddingListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PageGetComingWeddingListResponse.Unmarshal(m, b)
}
func (m *PageGetComingWeddingListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PageGetComingWeddingListResponse.Marshal(b, m, deterministic)
}
func (dst *PageGetComingWeddingListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PageGetComingWeddingListResponse.Merge(dst, src)
}
func (m *PageGetComingWeddingListResponse) XXX_Size() int {
	return xxx_messageInfo_PageGetComingWeddingListResponse.Size(m)
}
func (m *PageGetComingWeddingListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PageGetComingWeddingListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PageGetComingWeddingListResponse proto.InternalMessageInfo

func (m *PageGetComingWeddingListResponse) GetWeddingList() []*WeddingPlanInfo {
	if m != nil {
		return m.WeddingList
	}
	return nil
}

func (m *PageGetComingWeddingListResponse) GetHasMore() bool {
	if m != nil {
		return m.HasMore
	}
	return false
}

type SubscribeWeddingRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	WeddingPlanId        uint32   `protobuf:"varint,2,opt,name=wedding_plan_id,json=weddingPlanId,proto3" json:"wedding_plan_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SubscribeWeddingRequest) Reset()         { *m = SubscribeWeddingRequest{} }
func (m *SubscribeWeddingRequest) String() string { return proto.CompactTextString(m) }
func (*SubscribeWeddingRequest) ProtoMessage()    {}
func (*SubscribeWeddingRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{52}
}
func (m *SubscribeWeddingRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SubscribeWeddingRequest.Unmarshal(m, b)
}
func (m *SubscribeWeddingRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SubscribeWeddingRequest.Marshal(b, m, deterministic)
}
func (dst *SubscribeWeddingRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SubscribeWeddingRequest.Merge(dst, src)
}
func (m *SubscribeWeddingRequest) XXX_Size() int {
	return xxx_messageInfo_SubscribeWeddingRequest.Size(m)
}
func (m *SubscribeWeddingRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SubscribeWeddingRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SubscribeWeddingRequest proto.InternalMessageInfo

func (m *SubscribeWeddingRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SubscribeWeddingRequest) GetWeddingPlanId() uint32 {
	if m != nil {
		return m.WeddingPlanId
	}
	return 0
}

type SubscribeWeddingResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SubscribeWeddingResponse) Reset()         { *m = SubscribeWeddingResponse{} }
func (m *SubscribeWeddingResponse) String() string { return proto.CompactTextString(m) }
func (*SubscribeWeddingResponse) ProtoMessage()    {}
func (*SubscribeWeddingResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{53}
}
func (m *SubscribeWeddingResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SubscribeWeddingResponse.Unmarshal(m, b)
}
func (m *SubscribeWeddingResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SubscribeWeddingResponse.Marshal(b, m, deterministic)
}
func (dst *SubscribeWeddingResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SubscribeWeddingResponse.Merge(dst, src)
}
func (m *SubscribeWeddingResponse) XXX_Size() int {
	return xxx_messageInfo_SubscribeWeddingResponse.Size(m)
}
func (m *SubscribeWeddingResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SubscribeWeddingResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SubscribeWeddingResponse proto.InternalMessageInfo

type BatGetWeddingSubscribeStatusRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	WeddingPlanIdList    []uint32 `protobuf:"varint,2,rep,packed,name=wedding_plan_id_list,json=weddingPlanIdList,proto3" json:"wedding_plan_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatGetWeddingSubscribeStatusRequest) Reset()         { *m = BatGetWeddingSubscribeStatusRequest{} }
func (m *BatGetWeddingSubscribeStatusRequest) String() string { return proto.CompactTextString(m) }
func (*BatGetWeddingSubscribeStatusRequest) ProtoMessage()    {}
func (*BatGetWeddingSubscribeStatusRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{54}
}
func (m *BatGetWeddingSubscribeStatusRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetWeddingSubscribeStatusRequest.Unmarshal(m, b)
}
func (m *BatGetWeddingSubscribeStatusRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetWeddingSubscribeStatusRequest.Marshal(b, m, deterministic)
}
func (dst *BatGetWeddingSubscribeStatusRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetWeddingSubscribeStatusRequest.Merge(dst, src)
}
func (m *BatGetWeddingSubscribeStatusRequest) XXX_Size() int {
	return xxx_messageInfo_BatGetWeddingSubscribeStatusRequest.Size(m)
}
func (m *BatGetWeddingSubscribeStatusRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetWeddingSubscribeStatusRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetWeddingSubscribeStatusRequest proto.InternalMessageInfo

func (m *BatGetWeddingSubscribeStatusRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *BatGetWeddingSubscribeStatusRequest) GetWeddingPlanIdList() []uint32 {
	if m != nil {
		return m.WeddingPlanIdList
	}
	return nil
}

type BatGetWeddingSubscribeStatusResponse struct {
	SubscribeStatusMap   map[uint32]bool `protobuf:"bytes,1,rep,name=subscribe_status_map,json=subscribeStatusMap,proto3" json:"subscribe_status_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *BatGetWeddingSubscribeStatusResponse) Reset()         { *m = BatGetWeddingSubscribeStatusResponse{} }
func (m *BatGetWeddingSubscribeStatusResponse) String() string { return proto.CompactTextString(m) }
func (*BatGetWeddingSubscribeStatusResponse) ProtoMessage()    {}
func (*BatGetWeddingSubscribeStatusResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{55}
}
func (m *BatGetWeddingSubscribeStatusResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetWeddingSubscribeStatusResponse.Unmarshal(m, b)
}
func (m *BatGetWeddingSubscribeStatusResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetWeddingSubscribeStatusResponse.Marshal(b, m, deterministic)
}
func (dst *BatGetWeddingSubscribeStatusResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetWeddingSubscribeStatusResponse.Merge(dst, src)
}
func (m *BatGetWeddingSubscribeStatusResponse) XXX_Size() int {
	return xxx_messageInfo_BatGetWeddingSubscribeStatusResponse.Size(m)
}
func (m *BatGetWeddingSubscribeStatusResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetWeddingSubscribeStatusResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetWeddingSubscribeStatusResponse proto.InternalMessageInfo

func (m *BatGetWeddingSubscribeStatusResponse) GetSubscribeStatusMap() map[uint32]bool {
	if m != nil {
		return m.SubscribeStatusMap
	}
	return nil
}

type BigScreenItem struct {
	ImgUrl               string       `protobuf:"bytes,1,opt,name=img_url,json=imgUrl,proto3" json:"img_url,omitempty"`
	ReviewStatus         ReviewStatus `protobuf:"varint,2,opt,name=review_status,json=reviewStatus,proto3,enum=channel_wedding_plan.ReviewStatus" json:"review_status,omitempty"`
	UploadByUid          uint32       `protobuf:"varint,3,opt,name=upload_by_uid,json=uploadByUid,proto3" json:"upload_by_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *BigScreenItem) Reset()         { *m = BigScreenItem{} }
func (m *BigScreenItem) String() string { return proto.CompactTextString(m) }
func (*BigScreenItem) ProtoMessage()    {}
func (*BigScreenItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{56}
}
func (m *BigScreenItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BigScreenItem.Unmarshal(m, b)
}
func (m *BigScreenItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BigScreenItem.Marshal(b, m, deterministic)
}
func (dst *BigScreenItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BigScreenItem.Merge(dst, src)
}
func (m *BigScreenItem) XXX_Size() int {
	return xxx_messageInfo_BigScreenItem.Size(m)
}
func (m *BigScreenItem) XXX_DiscardUnknown() {
	xxx_messageInfo_BigScreenItem.DiscardUnknown(m)
}

var xxx_messageInfo_BigScreenItem proto.InternalMessageInfo

func (m *BigScreenItem) GetImgUrl() string {
	if m != nil {
		return m.ImgUrl
	}
	return ""
}

func (m *BigScreenItem) GetReviewStatus() ReviewStatus {
	if m != nil {
		return m.ReviewStatus
	}
	return ReviewStatus_REVIEW_STATUS_REVIEWING
}

func (m *BigScreenItem) GetUploadByUid() uint32 {
	if m != nil {
		return m.UploadByUid
	}
	return 0
}

type GetWeddingBigScreenRequest struct {
	WeddingPlanId        uint32   `protobuf:"varint,1,opt,name=wedding_plan_id,json=weddingPlanId,proto3" json:"wedding_plan_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetWeddingBigScreenRequest) Reset()         { *m = GetWeddingBigScreenRequest{} }
func (m *GetWeddingBigScreenRequest) String() string { return proto.CompactTextString(m) }
func (*GetWeddingBigScreenRequest) ProtoMessage()    {}
func (*GetWeddingBigScreenRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{57}
}
func (m *GetWeddingBigScreenRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWeddingBigScreenRequest.Unmarshal(m, b)
}
func (m *GetWeddingBigScreenRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWeddingBigScreenRequest.Marshal(b, m, deterministic)
}
func (dst *GetWeddingBigScreenRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWeddingBigScreenRequest.Merge(dst, src)
}
func (m *GetWeddingBigScreenRequest) XXX_Size() int {
	return xxx_messageInfo_GetWeddingBigScreenRequest.Size(m)
}
func (m *GetWeddingBigScreenRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWeddingBigScreenRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetWeddingBigScreenRequest proto.InternalMessageInfo

func (m *GetWeddingBigScreenRequest) GetWeddingPlanId() uint32 {
	if m != nil {
		return m.WeddingPlanId
	}
	return 0
}

type GetWeddingBigScreenResponse struct {
	BigScreenList        []*BigScreenItem `protobuf:"bytes,1,rep,name=big_screen_list,json=bigScreenList,proto3" json:"big_screen_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetWeddingBigScreenResponse) Reset()         { *m = GetWeddingBigScreenResponse{} }
func (m *GetWeddingBigScreenResponse) String() string { return proto.CompactTextString(m) }
func (*GetWeddingBigScreenResponse) ProtoMessage()    {}
func (*GetWeddingBigScreenResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{58}
}
func (m *GetWeddingBigScreenResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWeddingBigScreenResponse.Unmarshal(m, b)
}
func (m *GetWeddingBigScreenResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWeddingBigScreenResponse.Marshal(b, m, deterministic)
}
func (dst *GetWeddingBigScreenResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWeddingBigScreenResponse.Merge(dst, src)
}
func (m *GetWeddingBigScreenResponse) XXX_Size() int {
	return xxx_messageInfo_GetWeddingBigScreenResponse.Size(m)
}
func (m *GetWeddingBigScreenResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWeddingBigScreenResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetWeddingBigScreenResponse proto.InternalMessageInfo

func (m *GetWeddingBigScreenResponse) GetBigScreenList() []*BigScreenItem {
	if m != nil {
		return m.BigScreenList
	}
	return nil
}

type SaveWeddingBigScreenRequest struct {
	WeddingPlanId        uint32           `protobuf:"varint,1,opt,name=wedding_plan_id,json=weddingPlanId,proto3" json:"wedding_plan_id,omitempty"`
	BigScreenList        []*BigScreenItem `protobuf:"bytes,2,rep,name=big_screen_list,json=bigScreenList,proto3" json:"big_screen_list,omitempty"` // Deprecated: Do not use.
	ImgUrl               string           `protobuf:"bytes,3,opt,name=img_url,json=imgUrl,proto3" json:"img_url,omitempty"`
	IsDel                bool             `protobuf:"varint,4,opt,name=is_del,json=isDel,proto3" json:"is_del,omitempty"`
	Uid                  uint32           `protobuf:"varint,5,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *SaveWeddingBigScreenRequest) Reset()         { *m = SaveWeddingBigScreenRequest{} }
func (m *SaveWeddingBigScreenRequest) String() string { return proto.CompactTextString(m) }
func (*SaveWeddingBigScreenRequest) ProtoMessage()    {}
func (*SaveWeddingBigScreenRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{59}
}
func (m *SaveWeddingBigScreenRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SaveWeddingBigScreenRequest.Unmarshal(m, b)
}
func (m *SaveWeddingBigScreenRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SaveWeddingBigScreenRequest.Marshal(b, m, deterministic)
}
func (dst *SaveWeddingBigScreenRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SaveWeddingBigScreenRequest.Merge(dst, src)
}
func (m *SaveWeddingBigScreenRequest) XXX_Size() int {
	return xxx_messageInfo_SaveWeddingBigScreenRequest.Size(m)
}
func (m *SaveWeddingBigScreenRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SaveWeddingBigScreenRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SaveWeddingBigScreenRequest proto.InternalMessageInfo

func (m *SaveWeddingBigScreenRequest) GetWeddingPlanId() uint32 {
	if m != nil {
		return m.WeddingPlanId
	}
	return 0
}

// Deprecated: Do not use.
func (m *SaveWeddingBigScreenRequest) GetBigScreenList() []*BigScreenItem {
	if m != nil {
		return m.BigScreenList
	}
	return nil
}

func (m *SaveWeddingBigScreenRequest) GetImgUrl() string {
	if m != nil {
		return m.ImgUrl
	}
	return ""
}

func (m *SaveWeddingBigScreenRequest) GetIsDel() bool {
	if m != nil {
		return m.IsDel
	}
	return false
}

func (m *SaveWeddingBigScreenRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type SaveWeddingBigScreenResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SaveWeddingBigScreenResponse) Reset()         { *m = SaveWeddingBigScreenResponse{} }
func (m *SaveWeddingBigScreenResponse) String() string { return proto.CompactTextString(m) }
func (*SaveWeddingBigScreenResponse) ProtoMessage()    {}
func (*SaveWeddingBigScreenResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{60}
}
func (m *SaveWeddingBigScreenResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SaveWeddingBigScreenResponse.Unmarshal(m, b)
}
func (m *SaveWeddingBigScreenResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SaveWeddingBigScreenResponse.Marshal(b, m, deterministic)
}
func (dst *SaveWeddingBigScreenResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SaveWeddingBigScreenResponse.Merge(dst, src)
}
func (m *SaveWeddingBigScreenResponse) XXX_Size() int {
	return xxx_messageInfo_SaveWeddingBigScreenResponse.Size(m)
}
func (m *SaveWeddingBigScreenResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SaveWeddingBigScreenResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SaveWeddingBigScreenResponse proto.InternalMessageInfo

type UpdateWeddingBigScreenReviewStatusRequest struct {
	WeddingPlanId        uint32       `protobuf:"varint,1,opt,name=wedding_plan_id,json=weddingPlanId,proto3" json:"wedding_plan_id,omitempty"`
	ImgUrl               string       `protobuf:"bytes,2,opt,name=img_url,json=imgUrl,proto3" json:"img_url,omitempty"`
	ReviewStatus         ReviewStatus `protobuf:"varint,3,opt,name=review_status,json=reviewStatus,proto3,enum=channel_wedding_plan.ReviewStatus" json:"review_status,omitempty"`
	Uid                  uint32       `protobuf:"varint,4,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *UpdateWeddingBigScreenReviewStatusRequest) Reset() {
	*m = UpdateWeddingBigScreenReviewStatusRequest{}
}
func (m *UpdateWeddingBigScreenReviewStatusRequest) String() string { return proto.CompactTextString(m) }
func (*UpdateWeddingBigScreenReviewStatusRequest) ProtoMessage()    {}
func (*UpdateWeddingBigScreenReviewStatusRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{61}
}
func (m *UpdateWeddingBigScreenReviewStatusRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateWeddingBigScreenReviewStatusRequest.Unmarshal(m, b)
}
func (m *UpdateWeddingBigScreenReviewStatusRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateWeddingBigScreenReviewStatusRequest.Marshal(b, m, deterministic)
}
func (dst *UpdateWeddingBigScreenReviewStatusRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateWeddingBigScreenReviewStatusRequest.Merge(dst, src)
}
func (m *UpdateWeddingBigScreenReviewStatusRequest) XXX_Size() int {
	return xxx_messageInfo_UpdateWeddingBigScreenReviewStatusRequest.Size(m)
}
func (m *UpdateWeddingBigScreenReviewStatusRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateWeddingBigScreenReviewStatusRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateWeddingBigScreenReviewStatusRequest proto.InternalMessageInfo

func (m *UpdateWeddingBigScreenReviewStatusRequest) GetWeddingPlanId() uint32 {
	if m != nil {
		return m.WeddingPlanId
	}
	return 0
}

func (m *UpdateWeddingBigScreenReviewStatusRequest) GetImgUrl() string {
	if m != nil {
		return m.ImgUrl
	}
	return ""
}

func (m *UpdateWeddingBigScreenReviewStatusRequest) GetReviewStatus() ReviewStatus {
	if m != nil {
		return m.ReviewStatus
	}
	return ReviewStatus_REVIEW_STATUS_REVIEWING
}

func (m *UpdateWeddingBigScreenReviewStatusRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type UpdateWeddingBigScreenReviewStatusResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateWeddingBigScreenReviewStatusResponse) Reset() {
	*m = UpdateWeddingBigScreenReviewStatusResponse{}
}
func (m *UpdateWeddingBigScreenReviewStatusResponse) String() string {
	return proto.CompactTextString(m)
}
func (*UpdateWeddingBigScreenReviewStatusResponse) ProtoMessage() {}
func (*UpdateWeddingBigScreenReviewStatusResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{62}
}
func (m *UpdateWeddingBigScreenReviewStatusResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateWeddingBigScreenReviewStatusResponse.Unmarshal(m, b)
}
func (m *UpdateWeddingBigScreenReviewStatusResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateWeddingBigScreenReviewStatusResponse.Marshal(b, m, deterministic)
}
func (dst *UpdateWeddingBigScreenReviewStatusResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateWeddingBigScreenReviewStatusResponse.Merge(dst, src)
}
func (m *UpdateWeddingBigScreenReviewStatusResponse) XXX_Size() int {
	return xxx_messageInfo_UpdateWeddingBigScreenReviewStatusResponse.Size(m)
}
func (m *UpdateWeddingBigScreenReviewStatusResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateWeddingBigScreenReviewStatusResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateWeddingBigScreenReviewStatusResponse proto.InternalMessageInfo

type ApplyEndWeddingRelationRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PartnerUid           uint32   `protobuf:"varint,2,opt,name=partner_uid,json=partnerUid,proto3" json:"partner_uid,omitempty"`
	Source               uint32   `protobuf:"varint,3,opt,name=source,proto3" json:"source,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ApplyEndWeddingRelationRequest) Reset()         { *m = ApplyEndWeddingRelationRequest{} }
func (m *ApplyEndWeddingRelationRequest) String() string { return proto.CompactTextString(m) }
func (*ApplyEndWeddingRelationRequest) ProtoMessage()    {}
func (*ApplyEndWeddingRelationRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{63}
}
func (m *ApplyEndWeddingRelationRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplyEndWeddingRelationRequest.Unmarshal(m, b)
}
func (m *ApplyEndWeddingRelationRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplyEndWeddingRelationRequest.Marshal(b, m, deterministic)
}
func (dst *ApplyEndWeddingRelationRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplyEndWeddingRelationRequest.Merge(dst, src)
}
func (m *ApplyEndWeddingRelationRequest) XXX_Size() int {
	return xxx_messageInfo_ApplyEndWeddingRelationRequest.Size(m)
}
func (m *ApplyEndWeddingRelationRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplyEndWeddingRelationRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ApplyEndWeddingRelationRequest proto.InternalMessageInfo

func (m *ApplyEndWeddingRelationRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ApplyEndWeddingRelationRequest) GetPartnerUid() uint32 {
	if m != nil {
		return m.PartnerUid
	}
	return 0
}

func (m *ApplyEndWeddingRelationRequest) GetSource() uint32 {
	if m != nil {
		return m.Source
	}
	return 0
}

type ApplyEndWeddingRelationResponse struct {
	EndRelationshipDeadline int64    `protobuf:"varint,1,opt,name=end_relationship_deadline,json=endRelationshipDeadline,proto3" json:"end_relationship_deadline,omitempty"`
	XXX_NoUnkeyedLiteral    struct{} `json:"-"`
	XXX_unrecognized        []byte   `json:"-"`
	XXX_sizecache           int32    `json:"-"`
}

func (m *ApplyEndWeddingRelationResponse) Reset()         { *m = ApplyEndWeddingRelationResponse{} }
func (m *ApplyEndWeddingRelationResponse) String() string { return proto.CompactTextString(m) }
func (*ApplyEndWeddingRelationResponse) ProtoMessage()    {}
func (*ApplyEndWeddingRelationResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{64}
}
func (m *ApplyEndWeddingRelationResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplyEndWeddingRelationResponse.Unmarshal(m, b)
}
func (m *ApplyEndWeddingRelationResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplyEndWeddingRelationResponse.Marshal(b, m, deterministic)
}
func (dst *ApplyEndWeddingRelationResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplyEndWeddingRelationResponse.Merge(dst, src)
}
func (m *ApplyEndWeddingRelationResponse) XXX_Size() int {
	return xxx_messageInfo_ApplyEndWeddingRelationResponse.Size(m)
}
func (m *ApplyEndWeddingRelationResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplyEndWeddingRelationResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ApplyEndWeddingRelationResponse proto.InternalMessageInfo

func (m *ApplyEndWeddingRelationResponse) GetEndRelationshipDeadline() int64 {
	if m != nil {
		return m.EndRelationshipDeadline
	}
	return 0
}

type CancelEndWeddingRelationRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PartnerUid           uint32   `protobuf:"varint,2,opt,name=partner_uid,json=partnerUid,proto3" json:"partner_uid,omitempty"`
	Source               uint32   `protobuf:"varint,3,opt,name=source,proto3" json:"source,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CancelEndWeddingRelationRequest) Reset()         { *m = CancelEndWeddingRelationRequest{} }
func (m *CancelEndWeddingRelationRequest) String() string { return proto.CompactTextString(m) }
func (*CancelEndWeddingRelationRequest) ProtoMessage()    {}
func (*CancelEndWeddingRelationRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{65}
}
func (m *CancelEndWeddingRelationRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelEndWeddingRelationRequest.Unmarshal(m, b)
}
func (m *CancelEndWeddingRelationRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelEndWeddingRelationRequest.Marshal(b, m, deterministic)
}
func (dst *CancelEndWeddingRelationRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelEndWeddingRelationRequest.Merge(dst, src)
}
func (m *CancelEndWeddingRelationRequest) XXX_Size() int {
	return xxx_messageInfo_CancelEndWeddingRelationRequest.Size(m)
}
func (m *CancelEndWeddingRelationRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelEndWeddingRelationRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CancelEndWeddingRelationRequest proto.InternalMessageInfo

func (m *CancelEndWeddingRelationRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CancelEndWeddingRelationRequest) GetPartnerUid() uint32 {
	if m != nil {
		return m.PartnerUid
	}
	return 0
}

func (m *CancelEndWeddingRelationRequest) GetSource() uint32 {
	if m != nil {
		return m.Source
	}
	return 0
}

type CancelEndWeddingRelationResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CancelEndWeddingRelationResponse) Reset()         { *m = CancelEndWeddingRelationResponse{} }
func (m *CancelEndWeddingRelationResponse) String() string { return proto.CompactTextString(m) }
func (*CancelEndWeddingRelationResponse) ProtoMessage()    {}
func (*CancelEndWeddingRelationResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{66}
}
func (m *CancelEndWeddingRelationResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelEndWeddingRelationResponse.Unmarshal(m, b)
}
func (m *CancelEndWeddingRelationResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelEndWeddingRelationResponse.Marshal(b, m, deterministic)
}
func (dst *CancelEndWeddingRelationResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelEndWeddingRelationResponse.Merge(dst, src)
}
func (m *CancelEndWeddingRelationResponse) XXX_Size() int {
	return xxx_messageInfo_CancelEndWeddingRelationResponse.Size(m)
}
func (m *CancelEndWeddingRelationResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelEndWeddingRelationResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CancelEndWeddingRelationResponse proto.InternalMessageInfo

type GetReservedWeddingRequest struct {
	ChannelId            []uint32 `protobuf:"varint,1,rep,packed,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	PageNum              uint32   `protobuf:"varint,2,opt,name=page_num,json=pageNum,proto3" json:"page_num,omitempty"`
	PageSize             uint32   `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	StartTime            uint32   `protobuf:"varint,4,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime              uint32   `protobuf:"varint,5,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	DataType             uint32   `protobuf:"varint,6,opt,name=data_type,json=dataType,proto3" json:"data_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetReservedWeddingRequest) Reset()         { *m = GetReservedWeddingRequest{} }
func (m *GetReservedWeddingRequest) String() string { return proto.CompactTextString(m) }
func (*GetReservedWeddingRequest) ProtoMessage()    {}
func (*GetReservedWeddingRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{67}
}
func (m *GetReservedWeddingRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetReservedWeddingRequest.Unmarshal(m, b)
}
func (m *GetReservedWeddingRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetReservedWeddingRequest.Marshal(b, m, deterministic)
}
func (dst *GetReservedWeddingRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetReservedWeddingRequest.Merge(dst, src)
}
func (m *GetReservedWeddingRequest) XXX_Size() int {
	return xxx_messageInfo_GetReservedWeddingRequest.Size(m)
}
func (m *GetReservedWeddingRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetReservedWeddingRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetReservedWeddingRequest proto.InternalMessageInfo

func (m *GetReservedWeddingRequest) GetChannelId() []uint32 {
	if m != nil {
		return m.ChannelId
	}
	return nil
}

func (m *GetReservedWeddingRequest) GetPageNum() uint32 {
	if m != nil {
		return m.PageNum
	}
	return 0
}

func (m *GetReservedWeddingRequest) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *GetReservedWeddingRequest) GetStartTime() uint32 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *GetReservedWeddingRequest) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GetReservedWeddingRequest) GetDataType() uint32 {
	if m != nil {
		return m.DataType
	}
	return 0
}

type GetReservedWeddingResponse struct {
	ReservedWeddingList  []*ReservedWeddingInfo `protobuf:"bytes,1,rep,name=reserved_wedding_list,json=reservedWeddingList,proto3" json:"reserved_wedding_list,omitempty"`
	Total                uint32                 `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetReservedWeddingResponse) Reset()         { *m = GetReservedWeddingResponse{} }
func (m *GetReservedWeddingResponse) String() string { return proto.CompactTextString(m) }
func (*GetReservedWeddingResponse) ProtoMessage()    {}
func (*GetReservedWeddingResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{68}
}
func (m *GetReservedWeddingResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetReservedWeddingResponse.Unmarshal(m, b)
}
func (m *GetReservedWeddingResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetReservedWeddingResponse.Marshal(b, m, deterministic)
}
func (dst *GetReservedWeddingResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetReservedWeddingResponse.Merge(dst, src)
}
func (m *GetReservedWeddingResponse) XXX_Size() int {
	return xxx_messageInfo_GetReservedWeddingResponse.Size(m)
}
func (m *GetReservedWeddingResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetReservedWeddingResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetReservedWeddingResponse proto.InternalMessageInfo

func (m *GetReservedWeddingResponse) GetReservedWeddingList() []*ReservedWeddingInfo {
	if m != nil {
		return m.ReservedWeddingList
	}
	return nil
}

func (m *GetReservedWeddingResponse) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type ReservedWeddingInfo struct {
	WeddingPlanId        uint32   `protobuf:"varint,1,opt,name=wedding_plan_id,json=weddingPlanId,proto3" json:"wedding_plan_id,omitempty"`
	CreateTime           uint32   `protobuf:"varint,2,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	ChannelId            uint32   `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ReserveStartTime     uint32   `protobuf:"varint,4,opt,name=reserve_start_time,json=reserveStartTime,proto3" json:"reserve_start_time,omitempty"`
	ReserveEndTime       uint32   `protobuf:"varint,5,opt,name=reserve_end_time,json=reserveEndTime,proto3" json:"reserve_end_time,omitempty"`
	GroomUid             uint32   `protobuf:"varint,6,opt,name=groom_uid,json=groomUid,proto3" json:"groom_uid,omitempty"`
	BrideUid             uint32   `protobuf:"varint,7,opt,name=bride_uid,json=brideUid,proto3" json:"bride_uid,omitempty"`
	HostUid              uint32   `protobuf:"varint,8,opt,name=host_uid,json=hostUid,proto3" json:"host_uid,omitempty"`
	ThemeName            string   `protobuf:"bytes,9,opt,name=theme_name,json=themeName,proto3" json:"theme_name,omitempty"`
	BuyerUid             uint32   `protobuf:"varint,10,opt,name=buyer_uid,json=buyerUid,proto3" json:"buyer_uid,omitempty"`
	ChannelManagerUid    uint32   `protobuf:"varint,11,opt,name=channel_manager_uid,json=channelManagerUid,proto3" json:"channel_manager_uid,omitempty"`
	BuyPrice             uint32   `protobuf:"varint,12,opt,name=buy_price,json=buyPrice,proto3" json:"buy_price,omitempty"`
	IsHot                bool     `protobuf:"varint,13,opt,name=is_hot,json=isHot,proto3" json:"is_hot,omitempty"`
	IsAdminCancel        bool     `protobuf:"varint,14,opt,name=is_admin_cancel,json=isAdminCancel,proto3" json:"is_admin_cancel,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReservedWeddingInfo) Reset()         { *m = ReservedWeddingInfo{} }
func (m *ReservedWeddingInfo) String() string { return proto.CompactTextString(m) }
func (*ReservedWeddingInfo) ProtoMessage()    {}
func (*ReservedWeddingInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{69}
}
func (m *ReservedWeddingInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReservedWeddingInfo.Unmarshal(m, b)
}
func (m *ReservedWeddingInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReservedWeddingInfo.Marshal(b, m, deterministic)
}
func (dst *ReservedWeddingInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReservedWeddingInfo.Merge(dst, src)
}
func (m *ReservedWeddingInfo) XXX_Size() int {
	return xxx_messageInfo_ReservedWeddingInfo.Size(m)
}
func (m *ReservedWeddingInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ReservedWeddingInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ReservedWeddingInfo proto.InternalMessageInfo

func (m *ReservedWeddingInfo) GetWeddingPlanId() uint32 {
	if m != nil {
		return m.WeddingPlanId
	}
	return 0
}

func (m *ReservedWeddingInfo) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *ReservedWeddingInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ReservedWeddingInfo) GetReserveStartTime() uint32 {
	if m != nil {
		return m.ReserveStartTime
	}
	return 0
}

func (m *ReservedWeddingInfo) GetReserveEndTime() uint32 {
	if m != nil {
		return m.ReserveEndTime
	}
	return 0
}

func (m *ReservedWeddingInfo) GetGroomUid() uint32 {
	if m != nil {
		return m.GroomUid
	}
	return 0
}

func (m *ReservedWeddingInfo) GetBrideUid() uint32 {
	if m != nil {
		return m.BrideUid
	}
	return 0
}

func (m *ReservedWeddingInfo) GetHostUid() uint32 {
	if m != nil {
		return m.HostUid
	}
	return 0
}

func (m *ReservedWeddingInfo) GetThemeName() string {
	if m != nil {
		return m.ThemeName
	}
	return ""
}

func (m *ReservedWeddingInfo) GetBuyerUid() uint32 {
	if m != nil {
		return m.BuyerUid
	}
	return 0
}

func (m *ReservedWeddingInfo) GetChannelManagerUid() uint32 {
	if m != nil {
		return m.ChannelManagerUid
	}
	return 0
}

func (m *ReservedWeddingInfo) GetBuyPrice() uint32 {
	if m != nil {
		return m.BuyPrice
	}
	return 0
}

func (m *ReservedWeddingInfo) GetIsHot() bool {
	if m != nil {
		return m.IsHot
	}
	return false
}

func (m *ReservedWeddingInfo) GetIsAdminCancel() bool {
	if m != nil {
		return m.IsAdminCancel
	}
	return false
}

type SetWeddingHostRequest struct {
	WeddingPlanId        uint32   `protobuf:"varint,1,opt,name=wedding_plan_id,json=weddingPlanId,proto3" json:"wedding_plan_id,omitempty"`
	HostUid              uint32   `protobuf:"varint,2,opt,name=host_uid,json=hostUid,proto3" json:"host_uid,omitempty"`
	OperatorUid          uint32   `protobuf:"varint,3,opt,name=operator_uid,json=operatorUid,proto3" json:"operator_uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,4,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetWeddingHostRequest) Reset()         { *m = SetWeddingHostRequest{} }
func (m *SetWeddingHostRequest) String() string { return proto.CompactTextString(m) }
func (*SetWeddingHostRequest) ProtoMessage()    {}
func (*SetWeddingHostRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{70}
}
func (m *SetWeddingHostRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetWeddingHostRequest.Unmarshal(m, b)
}
func (m *SetWeddingHostRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetWeddingHostRequest.Marshal(b, m, deterministic)
}
func (dst *SetWeddingHostRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetWeddingHostRequest.Merge(dst, src)
}
func (m *SetWeddingHostRequest) XXX_Size() int {
	return xxx_messageInfo_SetWeddingHostRequest.Size(m)
}
func (m *SetWeddingHostRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SetWeddingHostRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SetWeddingHostRequest proto.InternalMessageInfo

func (m *SetWeddingHostRequest) GetWeddingPlanId() uint32 {
	if m != nil {
		return m.WeddingPlanId
	}
	return 0
}

func (m *SetWeddingHostRequest) GetHostUid() uint32 {
	if m != nil {
		return m.HostUid
	}
	return 0
}

func (m *SetWeddingHostRequest) GetOperatorUid() uint32 {
	if m != nil {
		return m.OperatorUid
	}
	return 0
}

func (m *SetWeddingHostRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type SetWeddingHostResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetWeddingHostResponse) Reset()         { *m = SetWeddingHostResponse{} }
func (m *SetWeddingHostResponse) String() string { return proto.CompactTextString(m) }
func (*SetWeddingHostResponse) ProtoMessage()    {}
func (*SetWeddingHostResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{71}
}
func (m *SetWeddingHostResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetWeddingHostResponse.Unmarshal(m, b)
}
func (m *SetWeddingHostResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetWeddingHostResponse.Marshal(b, m, deterministic)
}
func (dst *SetWeddingHostResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetWeddingHostResponse.Merge(dst, src)
}
func (m *SetWeddingHostResponse) XXX_Size() int {
	return xxx_messageInfo_SetWeddingHostResponse.Size(m)
}
func (m *SetWeddingHostResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SetWeddingHostResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SetWeddingHostResponse proto.InternalMessageInfo

// 获取基础信息 url: /tt-revenue-http-logic/channel_wedding/get_wedding_plan_base_info
type GetWeddingPlanBaseInfoRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetWeddingPlanBaseInfoRequest) Reset()         { *m = GetWeddingPlanBaseInfoRequest{} }
func (m *GetWeddingPlanBaseInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetWeddingPlanBaseInfoRequest) ProtoMessage()    {}
func (*GetWeddingPlanBaseInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{72}
}
func (m *GetWeddingPlanBaseInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWeddingPlanBaseInfoRequest.Unmarshal(m, b)
}
func (m *GetWeddingPlanBaseInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWeddingPlanBaseInfoRequest.Marshal(b, m, deterministic)
}
func (dst *GetWeddingPlanBaseInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWeddingPlanBaseInfoRequest.Merge(dst, src)
}
func (m *GetWeddingPlanBaseInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetWeddingPlanBaseInfoRequest.Size(m)
}
func (m *GetWeddingPlanBaseInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWeddingPlanBaseInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetWeddingPlanBaseInfoRequest proto.InternalMessageInfo

type GetWeddingPlanBaseInfoResponse struct {
	HasMate              bool     `protobuf:"varint,1,opt,name=has_mate,json=hasMate,proto3" json:"has_mate,omitempty"`
	IsPay                bool     `protobuf:"varint,2,opt,name=is_pay,json=isPay,proto3" json:"is_pay,omitempty"`
	IsReserve            bool     `protobuf:"varint,3,opt,name=is_reserve,json=isReserve,proto3" json:"is_reserve,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetWeddingPlanBaseInfoResponse) Reset()         { *m = GetWeddingPlanBaseInfoResponse{} }
func (m *GetWeddingPlanBaseInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetWeddingPlanBaseInfoResponse) ProtoMessage()    {}
func (*GetWeddingPlanBaseInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{73}
}
func (m *GetWeddingPlanBaseInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWeddingPlanBaseInfoResponse.Unmarshal(m, b)
}
func (m *GetWeddingPlanBaseInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWeddingPlanBaseInfoResponse.Marshal(b, m, deterministic)
}
func (dst *GetWeddingPlanBaseInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWeddingPlanBaseInfoResponse.Merge(dst, src)
}
func (m *GetWeddingPlanBaseInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetWeddingPlanBaseInfoResponse.Size(m)
}
func (m *GetWeddingPlanBaseInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWeddingPlanBaseInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetWeddingPlanBaseInfoResponse proto.InternalMessageInfo

func (m *GetWeddingPlanBaseInfoResponse) GetHasMate() bool {
	if m != nil {
		return m.HasMate
	}
	return false
}

func (m *GetWeddingPlanBaseInfoResponse) GetIsPay() bool {
	if m != nil {
		return m.IsPay
	}
	return false
}

func (m *GetWeddingPlanBaseInfoResponse) GetIsReserve() bool {
	if m != nil {
		return m.IsReserve
	}
	return false
}

type GetThemeCfgByIdRequest struct {
	ThemeId              uint32   `protobuf:"varint,1,opt,name=theme_id,json=themeId,proto3" json:"theme_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetThemeCfgByIdRequest) Reset()         { *m = GetThemeCfgByIdRequest{} }
func (m *GetThemeCfgByIdRequest) String() string { return proto.CompactTextString(m) }
func (*GetThemeCfgByIdRequest) ProtoMessage()    {}
func (*GetThemeCfgByIdRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{74}
}
func (m *GetThemeCfgByIdRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetThemeCfgByIdRequest.Unmarshal(m, b)
}
func (m *GetThemeCfgByIdRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetThemeCfgByIdRequest.Marshal(b, m, deterministic)
}
func (dst *GetThemeCfgByIdRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetThemeCfgByIdRequest.Merge(dst, src)
}
func (m *GetThemeCfgByIdRequest) XXX_Size() int {
	return xxx_messageInfo_GetThemeCfgByIdRequest.Size(m)
}
func (m *GetThemeCfgByIdRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetThemeCfgByIdRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetThemeCfgByIdRequest proto.InternalMessageInfo

func (m *GetThemeCfgByIdRequest) GetThemeId() uint32 {
	if m != nil {
		return m.ThemeId
	}
	return 0
}

type GetThemeCfgByIdResponse struct {
	ThemeCfg             *ThemeCfg `protobuf:"bytes,1,opt,name=theme_cfg,json=themeCfg,proto3" json:"theme_cfg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GetThemeCfgByIdResponse) Reset()         { *m = GetThemeCfgByIdResponse{} }
func (m *GetThemeCfgByIdResponse) String() string { return proto.CompactTextString(m) }
func (*GetThemeCfgByIdResponse) ProtoMessage()    {}
func (*GetThemeCfgByIdResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{75}
}
func (m *GetThemeCfgByIdResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetThemeCfgByIdResponse.Unmarshal(m, b)
}
func (m *GetThemeCfgByIdResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetThemeCfgByIdResponse.Marshal(b, m, deterministic)
}
func (dst *GetThemeCfgByIdResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetThemeCfgByIdResponse.Merge(dst, src)
}
func (m *GetThemeCfgByIdResponse) XXX_Size() int {
	return xxx_messageInfo_GetThemeCfgByIdResponse.Size(m)
}
func (m *GetThemeCfgByIdResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetThemeCfgByIdResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetThemeCfgByIdResponse proto.InternalMessageInfo

func (m *GetThemeCfgByIdResponse) GetThemeCfg() *ThemeCfg {
	if m != nil {
		return m.ThemeCfg
	}
	return nil
}

// 获取求婚列表请求
type GetProposeListRequest struct {
	TargetUidList        []uint32 `protobuf:"varint,1,rep,packed,name=target_uid_list,json=targetUidList,proto3" json:"target_uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetProposeListRequest) Reset()         { *m = GetProposeListRequest{} }
func (m *GetProposeListRequest) String() string { return proto.CompactTextString(m) }
func (*GetProposeListRequest) ProtoMessage()    {}
func (*GetProposeListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{76}
}
func (m *GetProposeListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetProposeListRequest.Unmarshal(m, b)
}
func (m *GetProposeListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetProposeListRequest.Marshal(b, m, deterministic)
}
func (dst *GetProposeListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetProposeListRequest.Merge(dst, src)
}
func (m *GetProposeListRequest) XXX_Size() int {
	return xxx_messageInfo_GetProposeListRequest.Size(m)
}
func (m *GetProposeListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetProposeListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetProposeListRequest proto.InternalMessageInfo

func (m *GetProposeListRequest) GetTargetUidList() []uint32 {
	if m != nil {
		return m.TargetUidList
	}
	return nil
}

// 求婚对象信息
type ProposeUser struct {
	TargetUid            uint32   `protobuf:"varint,1,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	Status               uint32   `protobuf:"varint,2,opt,name=status,proto3" json:"status,omitempty"`
	FellowPoint          uint32   `protobuf:"varint,3,opt,name=fellow_point,json=fellowPoint,proto3" json:"fellow_point,omitempty"`
	ProposeId            string   `protobuf:"bytes,4,opt,name=propose_id,json=proposeId,proto3" json:"propose_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ProposeUser) Reset()         { *m = ProposeUser{} }
func (m *ProposeUser) String() string { return proto.CompactTextString(m) }
func (*ProposeUser) ProtoMessage()    {}
func (*ProposeUser) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{77}
}
func (m *ProposeUser) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProposeUser.Unmarshal(m, b)
}
func (m *ProposeUser) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProposeUser.Marshal(b, m, deterministic)
}
func (dst *ProposeUser) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProposeUser.Merge(dst, src)
}
func (m *ProposeUser) XXX_Size() int {
	return xxx_messageInfo_ProposeUser.Size(m)
}
func (m *ProposeUser) XXX_DiscardUnknown() {
	xxx_messageInfo_ProposeUser.DiscardUnknown(m)
}

var xxx_messageInfo_ProposeUser proto.InternalMessageInfo

func (m *ProposeUser) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *ProposeUser) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *ProposeUser) GetFellowPoint() uint32 {
	if m != nil {
		return m.FellowPoint
	}
	return 0
}

func (m *ProposeUser) GetProposeId() string {
	if m != nil {
		return m.ProposeId
	}
	return ""
}

// 获取求婚列表响应
type GetProposeListResponse struct {
	ProposeList          []*ProposeUser `protobuf:"bytes,1,rep,name=propose_list,json=proposeList,proto3" json:"propose_list,omitempty"`
	Tips                 []string       `protobuf:"bytes,2,rep,name=tips,proto3" json:"tips,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetProposeListResponse) Reset()         { *m = GetProposeListResponse{} }
func (m *GetProposeListResponse) String() string { return proto.CompactTextString(m) }
func (*GetProposeListResponse) ProtoMessage()    {}
func (*GetProposeListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{78}
}
func (m *GetProposeListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetProposeListResponse.Unmarshal(m, b)
}
func (m *GetProposeListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetProposeListResponse.Marshal(b, m, deterministic)
}
func (dst *GetProposeListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetProposeListResponse.Merge(dst, src)
}
func (m *GetProposeListResponse) XXX_Size() int {
	return xxx_messageInfo_GetProposeListResponse.Size(m)
}
func (m *GetProposeListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetProposeListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetProposeListResponse proto.InternalMessageInfo

func (m *GetProposeListResponse) GetProposeList() []*ProposeUser {
	if m != nil {
		return m.ProposeList
	}
	return nil
}

func (m *GetProposeListResponse) GetTips() []string {
	if m != nil {
		return m.Tips
	}
	return nil
}

type SendProposeRequest struct {
	TargetUid            uint32   `protobuf:"varint,1,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	Tips                 string   `protobuf:"bytes,2,opt,name=tips,proto3" json:"tips,omitempty"`
	FromUid              uint32   `protobuf:"varint,3,opt,name=from_uid,json=fromUid,proto3" json:"from_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendProposeRequest) Reset()         { *m = SendProposeRequest{} }
func (m *SendProposeRequest) String() string { return proto.CompactTextString(m) }
func (*SendProposeRequest) ProtoMessage()    {}
func (*SendProposeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{79}
}
func (m *SendProposeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendProposeRequest.Unmarshal(m, b)
}
func (m *SendProposeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendProposeRequest.Marshal(b, m, deterministic)
}
func (dst *SendProposeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendProposeRequest.Merge(dst, src)
}
func (m *SendProposeRequest) XXX_Size() int {
	return xxx_messageInfo_SendProposeRequest.Size(m)
}
func (m *SendProposeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SendProposeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SendProposeRequest proto.InternalMessageInfo

func (m *SendProposeRequest) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *SendProposeRequest) GetTips() string {
	if m != nil {
		return m.Tips
	}
	return ""
}

func (m *SendProposeRequest) GetFromUid() uint32 {
	if m != nil {
		return m.FromUid
	}
	return 0
}

type SendProposeResponse struct {
	ProposeId            string   `protobuf:"bytes,1,opt,name=propose_id,json=proposeId,proto3" json:"propose_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendProposeResponse) Reset()         { *m = SendProposeResponse{} }
func (m *SendProposeResponse) String() string { return proto.CompactTextString(m) }
func (*SendProposeResponse) ProtoMessage()    {}
func (*SendProposeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{80}
}
func (m *SendProposeResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendProposeResponse.Unmarshal(m, b)
}
func (m *SendProposeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendProposeResponse.Marshal(b, m, deterministic)
}
func (dst *SendProposeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendProposeResponse.Merge(dst, src)
}
func (m *SendProposeResponse) XXX_Size() int {
	return xxx_messageInfo_SendProposeResponse.Size(m)
}
func (m *SendProposeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SendProposeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SendProposeResponse proto.InternalMessageInfo

func (m *SendProposeResponse) GetProposeId() string {
	if m != nil {
		return m.ProposeId
	}
	return ""
}

// 处理求婚请求
type HandleProposeRequest struct {
	ProposeId            string   `protobuf:"bytes,1,opt,name=propose_id,json=proposeId,proto3" json:"propose_id,omitempty"`
	IsAccept             bool     `protobuf:"varint,2,opt,name=is_accept,json=isAccept,proto3" json:"is_accept,omitempty"`
	HandleUid            uint32   `protobuf:"varint,3,opt,name=handle_uid,json=handleUid,proto3" json:"handle_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HandleProposeRequest) Reset()         { *m = HandleProposeRequest{} }
func (m *HandleProposeRequest) String() string { return proto.CompactTextString(m) }
func (*HandleProposeRequest) ProtoMessage()    {}
func (*HandleProposeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{81}
}
func (m *HandleProposeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HandleProposeRequest.Unmarshal(m, b)
}
func (m *HandleProposeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HandleProposeRequest.Marshal(b, m, deterministic)
}
func (dst *HandleProposeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HandleProposeRequest.Merge(dst, src)
}
func (m *HandleProposeRequest) XXX_Size() int {
	return xxx_messageInfo_HandleProposeRequest.Size(m)
}
func (m *HandleProposeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_HandleProposeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_HandleProposeRequest proto.InternalMessageInfo

func (m *HandleProposeRequest) GetProposeId() string {
	if m != nil {
		return m.ProposeId
	}
	return ""
}

func (m *HandleProposeRequest) GetIsAccept() bool {
	if m != nil {
		return m.IsAccept
	}
	return false
}

func (m *HandleProposeRequest) GetHandleUid() uint32 {
	if m != nil {
		return m.HandleUid
	}
	return 0
}

type HandleProposeResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HandleProposeResponse) Reset()         { *m = HandleProposeResponse{} }
func (m *HandleProposeResponse) String() string { return proto.CompactTextString(m) }
func (*HandleProposeResponse) ProtoMessage()    {}
func (*HandleProposeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{82}
}
func (m *HandleProposeResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HandleProposeResponse.Unmarshal(m, b)
}
func (m *HandleProposeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HandleProposeResponse.Marshal(b, m, deterministic)
}
func (dst *HandleProposeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HandleProposeResponse.Merge(dst, src)
}
func (m *HandleProposeResponse) XXX_Size() int {
	return xxx_messageInfo_HandleProposeResponse.Size(m)
}
func (m *HandleProposeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_HandleProposeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_HandleProposeResponse proto.InternalMessageInfo

// 求婚函信息
type WeddingProposeInfo struct {
	ProposeId            string   `protobuf:"bytes,1,opt,name=propose_id,json=proposeId,proto3" json:"propose_id,omitempty"`
	FromUser             uint32   `protobuf:"varint,2,opt,name=from_user,json=fromUser,proto3" json:"from_user,omitempty"`
	TargetUser           uint32   `protobuf:"varint,3,opt,name=target_user,json=targetUser,proto3" json:"target_user,omitempty"`
	Status               uint32   `protobuf:"varint,4,opt,name=status,proto3" json:"status,omitempty"`
	CreateTime           uint32   `protobuf:"varint,5,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	EndTime              uint32   `protobuf:"varint,6,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	ExpireDay            uint32   `protobuf:"varint,7,opt,name=expire_day,json=expireDay,proto3" json:"expire_day,omitempty"`
	Tips                 string   `protobuf:"bytes,8,opt,name=tips,proto3" json:"tips,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WeddingProposeInfo) Reset()         { *m = WeddingProposeInfo{} }
func (m *WeddingProposeInfo) String() string { return proto.CompactTextString(m) }
func (*WeddingProposeInfo) ProtoMessage()    {}
func (*WeddingProposeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{83}
}
func (m *WeddingProposeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingProposeInfo.Unmarshal(m, b)
}
func (m *WeddingProposeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingProposeInfo.Marshal(b, m, deterministic)
}
func (dst *WeddingProposeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingProposeInfo.Merge(dst, src)
}
func (m *WeddingProposeInfo) XXX_Size() int {
	return xxx_messageInfo_WeddingProposeInfo.Size(m)
}
func (m *WeddingProposeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingProposeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingProposeInfo proto.InternalMessageInfo

func (m *WeddingProposeInfo) GetProposeId() string {
	if m != nil {
		return m.ProposeId
	}
	return ""
}

func (m *WeddingProposeInfo) GetFromUser() uint32 {
	if m != nil {
		return m.FromUser
	}
	return 0
}

func (m *WeddingProposeInfo) GetTargetUser() uint32 {
	if m != nil {
		return m.TargetUser
	}
	return 0
}

func (m *WeddingProposeInfo) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *WeddingProposeInfo) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *WeddingProposeInfo) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *WeddingProposeInfo) GetExpireDay() uint32 {
	if m != nil {
		return m.ExpireDay
	}
	return 0
}

func (m *WeddingProposeInfo) GetTips() string {
	if m != nil {
		return m.Tips
	}
	return ""
}

type GetProposeByIdRequest struct {
	ProposeId            string   `protobuf:"bytes,1,opt,name=propose_id,json=proposeId,proto3" json:"propose_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetProposeByIdRequest) Reset()         { *m = GetProposeByIdRequest{} }
func (m *GetProposeByIdRequest) String() string { return proto.CompactTextString(m) }
func (*GetProposeByIdRequest) ProtoMessage()    {}
func (*GetProposeByIdRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{84}
}
func (m *GetProposeByIdRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetProposeByIdRequest.Unmarshal(m, b)
}
func (m *GetProposeByIdRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetProposeByIdRequest.Marshal(b, m, deterministic)
}
func (dst *GetProposeByIdRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetProposeByIdRequest.Merge(dst, src)
}
func (m *GetProposeByIdRequest) XXX_Size() int {
	return xxx_messageInfo_GetProposeByIdRequest.Size(m)
}
func (m *GetProposeByIdRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetProposeByIdRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetProposeByIdRequest proto.InternalMessageInfo

func (m *GetProposeByIdRequest) GetProposeId() string {
	if m != nil {
		return m.ProposeId
	}
	return ""
}

type GetProposeByIdResponse struct {
	Propose              *WeddingProposeInfo `protobuf:"bytes,2,opt,name=propose,proto3" json:"propose,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetProposeByIdResponse) Reset()         { *m = GetProposeByIdResponse{} }
func (m *GetProposeByIdResponse) String() string { return proto.CompactTextString(m) }
func (*GetProposeByIdResponse) ProtoMessage()    {}
func (*GetProposeByIdResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{85}
}
func (m *GetProposeByIdResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetProposeByIdResponse.Unmarshal(m, b)
}
func (m *GetProposeByIdResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetProposeByIdResponse.Marshal(b, m, deterministic)
}
func (dst *GetProposeByIdResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetProposeByIdResponse.Merge(dst, src)
}
func (m *GetProposeByIdResponse) XXX_Size() int {
	return xxx_messageInfo_GetProposeByIdResponse.Size(m)
}
func (m *GetProposeByIdResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetProposeByIdResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetProposeByIdResponse proto.InternalMessageInfo

func (m *GetProposeByIdResponse) GetPropose() *WeddingProposeInfo {
	if m != nil {
		return m.Propose
	}
	return nil
}

// 我发出的求婚函
type GetSendProposeRequest struct {
	FromUid              uint32   `protobuf:"varint,1,opt,name=from_uid,json=fromUid,proto3" json:"from_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSendProposeRequest) Reset()         { *m = GetSendProposeRequest{} }
func (m *GetSendProposeRequest) String() string { return proto.CompactTextString(m) }
func (*GetSendProposeRequest) ProtoMessage()    {}
func (*GetSendProposeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{86}
}
func (m *GetSendProposeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSendProposeRequest.Unmarshal(m, b)
}
func (m *GetSendProposeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSendProposeRequest.Marshal(b, m, deterministic)
}
func (dst *GetSendProposeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSendProposeRequest.Merge(dst, src)
}
func (m *GetSendProposeRequest) XXX_Size() int {
	return xxx_messageInfo_GetSendProposeRequest.Size(m)
}
func (m *GetSendProposeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSendProposeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetSendProposeRequest proto.InternalMessageInfo

func (m *GetSendProposeRequest) GetFromUid() uint32 {
	if m != nil {
		return m.FromUid
	}
	return 0
}

type GetSendProposeResponse struct {
	Propose              *WeddingProposeInfo `protobuf:"bytes,1,opt,name=propose,proto3" json:"propose,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetSendProposeResponse) Reset()         { *m = GetSendProposeResponse{} }
func (m *GetSendProposeResponse) String() string { return proto.CompactTextString(m) }
func (*GetSendProposeResponse) ProtoMessage()    {}
func (*GetSendProposeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{87}
}
func (m *GetSendProposeResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSendProposeResponse.Unmarshal(m, b)
}
func (m *GetSendProposeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSendProposeResponse.Marshal(b, m, deterministic)
}
func (dst *GetSendProposeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSendProposeResponse.Merge(dst, src)
}
func (m *GetSendProposeResponse) XXX_Size() int {
	return xxx_messageInfo_GetSendProposeResponse.Size(m)
}
func (m *GetSendProposeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSendProposeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetSendProposeResponse proto.InternalMessageInfo

func (m *GetSendProposeResponse) GetPropose() *WeddingProposeInfo {
	if m != nil {
		return m.Propose
	}
	return nil
}

type BatchGetMarriageInfoRequest struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetMarriageInfoRequest) Reset()         { *m = BatchGetMarriageInfoRequest{} }
func (m *BatchGetMarriageInfoRequest) String() string { return proto.CompactTextString(m) }
func (*BatchGetMarriageInfoRequest) ProtoMessage()    {}
func (*BatchGetMarriageInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{88}
}
func (m *BatchGetMarriageInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetMarriageInfoRequest.Unmarshal(m, b)
}
func (m *BatchGetMarriageInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetMarriageInfoRequest.Marshal(b, m, deterministic)
}
func (dst *BatchGetMarriageInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetMarriageInfoRequest.Merge(dst, src)
}
func (m *BatchGetMarriageInfoRequest) XXX_Size() int {
	return xxx_messageInfo_BatchGetMarriageInfoRequest.Size(m)
}
func (m *BatchGetMarriageInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetMarriageInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetMarriageInfoRequest proto.InternalMessageInfo

func (m *BatchGetMarriageInfoRequest) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type BatchGetMarriageInfoResponse struct {
	MarriageInfoMap      map[uint32]uint32 `protobuf:"bytes,1,rep,name=marriage_info_map,json=marriageInfoMap,proto3" json:"marriage_info_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *BatchGetMarriageInfoResponse) Reset()         { *m = BatchGetMarriageInfoResponse{} }
func (m *BatchGetMarriageInfoResponse) String() string { return proto.CompactTextString(m) }
func (*BatchGetMarriageInfoResponse) ProtoMessage()    {}
func (*BatchGetMarriageInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{89}
}
func (m *BatchGetMarriageInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetMarriageInfoResponse.Unmarshal(m, b)
}
func (m *BatchGetMarriageInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetMarriageInfoResponse.Marshal(b, m, deterministic)
}
func (dst *BatchGetMarriageInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetMarriageInfoResponse.Merge(dst, src)
}
func (m *BatchGetMarriageInfoResponse) XXX_Size() int {
	return xxx_messageInfo_BatchGetMarriageInfoResponse.Size(m)
}
func (m *BatchGetMarriageInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetMarriageInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetMarriageInfoResponse proto.InternalMessageInfo

func (m *BatchGetMarriageInfoResponse) GetMarriageInfoMap() map[uint32]uint32 {
	if m != nil {
		return m.MarriageInfoMap
	}
	return nil
}

type GetSimpleWeddingPlanInfoRequest struct {
	WeddingPlanId        uint32   `protobuf:"varint,1,opt,name=wedding_plan_id,json=weddingPlanId,proto3" json:"wedding_plan_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSimpleWeddingPlanInfoRequest) Reset()         { *m = GetSimpleWeddingPlanInfoRequest{} }
func (m *GetSimpleWeddingPlanInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetSimpleWeddingPlanInfoRequest) ProtoMessage()    {}
func (*GetSimpleWeddingPlanInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{90}
}
func (m *GetSimpleWeddingPlanInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSimpleWeddingPlanInfoRequest.Unmarshal(m, b)
}
func (m *GetSimpleWeddingPlanInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSimpleWeddingPlanInfoRequest.Marshal(b, m, deterministic)
}
func (dst *GetSimpleWeddingPlanInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSimpleWeddingPlanInfoRequest.Merge(dst, src)
}
func (m *GetSimpleWeddingPlanInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetSimpleWeddingPlanInfoRequest.Size(m)
}
func (m *GetSimpleWeddingPlanInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSimpleWeddingPlanInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetSimpleWeddingPlanInfoRequest proto.InternalMessageInfo

func (m *GetSimpleWeddingPlanInfoRequest) GetWeddingPlanId() uint32 {
	if m != nil {
		return m.WeddingPlanId
	}
	return 0
}

type GetSimpleWeddingPlanInfoResponse struct {
	GroomUid             uint32   `protobuf:"varint,1,opt,name=groom_uid,json=groomUid,proto3" json:"groom_uid,omitempty"`
	BrideUid             uint32   `protobuf:"varint,2,opt,name=bride_uid,json=brideUid,proto3" json:"bride_uid,omitempty"`
	GroomsmanList        []uint32 `protobuf:"varint,3,rep,packed,name=groomsman_list,json=groomsmanList,proto3" json:"groomsman_list,omitempty"`
	BridesmaidList       []uint32 `protobuf:"varint,4,rep,packed,name=bridesmaid_list,json=bridesmaidList,proto3" json:"bridesmaid_list,omitempty"`
	BigScreenList        []string `protobuf:"bytes,5,rep,name=big_screen_list,json=bigScreenList,proto3" json:"big_screen_list,omitempty"`
	ReserveStartTime     uint32   `protobuf:"varint,6,opt,name=reserve_start_time,json=reserveStartTime,proto3" json:"reserve_start_time,omitempty"`
	ReserveEndTime       uint32   `protobuf:"varint,7,opt,name=reserve_end_time,json=reserveEndTime,proto3" json:"reserve_end_time,omitempty"`
	IsHot                bool     `protobuf:"varint,8,opt,name=is_hot,json=isHot,proto3" json:"is_hot,omitempty"`
	BuyerUid             uint32   `protobuf:"varint,9,opt,name=buyer_uid,json=buyerUid,proto3" json:"buyer_uid,omitempty"`
	GiftId               uint32   `protobuf:"varint,10,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSimpleWeddingPlanInfoResponse) Reset()         { *m = GetSimpleWeddingPlanInfoResponse{} }
func (m *GetSimpleWeddingPlanInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetSimpleWeddingPlanInfoResponse) ProtoMessage()    {}
func (*GetSimpleWeddingPlanInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{91}
}
func (m *GetSimpleWeddingPlanInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSimpleWeddingPlanInfoResponse.Unmarshal(m, b)
}
func (m *GetSimpleWeddingPlanInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSimpleWeddingPlanInfoResponse.Marshal(b, m, deterministic)
}
func (dst *GetSimpleWeddingPlanInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSimpleWeddingPlanInfoResponse.Merge(dst, src)
}
func (m *GetSimpleWeddingPlanInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetSimpleWeddingPlanInfoResponse.Size(m)
}
func (m *GetSimpleWeddingPlanInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSimpleWeddingPlanInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetSimpleWeddingPlanInfoResponse proto.InternalMessageInfo

func (m *GetSimpleWeddingPlanInfoResponse) GetGroomUid() uint32 {
	if m != nil {
		return m.GroomUid
	}
	return 0
}

func (m *GetSimpleWeddingPlanInfoResponse) GetBrideUid() uint32 {
	if m != nil {
		return m.BrideUid
	}
	return 0
}

func (m *GetSimpleWeddingPlanInfoResponse) GetGroomsmanList() []uint32 {
	if m != nil {
		return m.GroomsmanList
	}
	return nil
}

func (m *GetSimpleWeddingPlanInfoResponse) GetBridesmaidList() []uint32 {
	if m != nil {
		return m.BridesmaidList
	}
	return nil
}

func (m *GetSimpleWeddingPlanInfoResponse) GetBigScreenList() []string {
	if m != nil {
		return m.BigScreenList
	}
	return nil
}

func (m *GetSimpleWeddingPlanInfoResponse) GetReserveStartTime() uint32 {
	if m != nil {
		return m.ReserveStartTime
	}
	return 0
}

func (m *GetSimpleWeddingPlanInfoResponse) GetReserveEndTime() uint32 {
	if m != nil {
		return m.ReserveEndTime
	}
	return 0
}

func (m *GetSimpleWeddingPlanInfoResponse) GetIsHot() bool {
	if m != nil {
		return m.IsHot
	}
	return false
}

func (m *GetSimpleWeddingPlanInfoResponse) GetBuyerUid() uint32 {
	if m != nil {
		return m.BuyerUid
	}
	return 0
}

func (m *GetSimpleWeddingPlanInfoResponse) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

type BatchHotWeddingPlanRequest struct {
	WeddingPlanId        []uint32 `protobuf:"varint,1,rep,packed,name=wedding_plan_id,json=weddingPlanId,proto3" json:"wedding_plan_id,omitempty"`
	WithCache            bool     `protobuf:"varint,2,opt,name=with_cache,json=withCache,proto3" json:"with_cache,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchHotWeddingPlanRequest) Reset()         { *m = BatchHotWeddingPlanRequest{} }
func (m *BatchHotWeddingPlanRequest) String() string { return proto.CompactTextString(m) }
func (*BatchHotWeddingPlanRequest) ProtoMessage()    {}
func (*BatchHotWeddingPlanRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{92}
}
func (m *BatchHotWeddingPlanRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchHotWeddingPlanRequest.Unmarshal(m, b)
}
func (m *BatchHotWeddingPlanRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchHotWeddingPlanRequest.Marshal(b, m, deterministic)
}
func (dst *BatchHotWeddingPlanRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchHotWeddingPlanRequest.Merge(dst, src)
}
func (m *BatchHotWeddingPlanRequest) XXX_Size() int {
	return xxx_messageInfo_BatchHotWeddingPlanRequest.Size(m)
}
func (m *BatchHotWeddingPlanRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchHotWeddingPlanRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchHotWeddingPlanRequest proto.InternalMessageInfo

func (m *BatchHotWeddingPlanRequest) GetWeddingPlanId() []uint32 {
	if m != nil {
		return m.WeddingPlanId
	}
	return nil
}

func (m *BatchHotWeddingPlanRequest) GetWithCache() bool {
	if m != nil {
		return m.WithCache
	}
	return false
}

type BatchHotWeddingPlanResponse struct {
	HotWeddingMap        map[uint32]bool `protobuf:"bytes,1,rep,name=hot_wedding_map,json=hotWeddingMap,proto3" json:"hot_wedding_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *BatchHotWeddingPlanResponse) Reset()         { *m = BatchHotWeddingPlanResponse{} }
func (m *BatchHotWeddingPlanResponse) String() string { return proto.CompactTextString(m) }
func (*BatchHotWeddingPlanResponse) ProtoMessage()    {}
func (*BatchHotWeddingPlanResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{93}
}
func (m *BatchHotWeddingPlanResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchHotWeddingPlanResponse.Unmarshal(m, b)
}
func (m *BatchHotWeddingPlanResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchHotWeddingPlanResponse.Marshal(b, m, deterministic)
}
func (dst *BatchHotWeddingPlanResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchHotWeddingPlanResponse.Merge(dst, src)
}
func (m *BatchHotWeddingPlanResponse) XXX_Size() int {
	return xxx_messageInfo_BatchHotWeddingPlanResponse.Size(m)
}
func (m *BatchHotWeddingPlanResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchHotWeddingPlanResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchHotWeddingPlanResponse proto.InternalMessageInfo

func (m *BatchHotWeddingPlanResponse) GetHotWeddingMap() map[uint32]bool {
	if m != nil {
		return m.HotWeddingMap
	}
	return nil
}

type InviteCard struct {
	InviteId             uint32              `protobuf:"varint,2,opt,name=invite_id,json=inviteId,proto3" json:"invite_id,omitempty"`
	WeddingPlanId        uint32              `protobuf:"varint,3,opt,name=wedding_plan_id,json=weddingPlanId,proto3" json:"wedding_plan_id,omitempty"`
	ThemeId              uint32              `protobuf:"varint,4,opt,name=theme_id,json=themeId,proto3" json:"theme_id,omitempty"`
	GroomUid             uint32              `protobuf:"varint,5,opt,name=groom_uid,json=groomUid,proto3" json:"groom_uid,omitempty"`
	BrideUid             uint32              `protobuf:"varint,6,opt,name=bride_uid,json=brideUid,proto3" json:"bride_uid,omitempty"`
	WeddingDatetime      uint32              `protobuf:"varint,7,opt,name=wedding_datetime,json=weddingDatetime,proto3" json:"wedding_datetime,omitempty"`
	WeddingChannelId     uint32              `protobuf:"varint,8,opt,name=wedding_channel_id,json=weddingChannelId,proto3" json:"wedding_channel_id,omitempty"`
	GiftList             []*WeddingGuestGift `protobuf:"bytes,9,rep,name=gift_list,json=giftList,proto3" json:"gift_list,omitempty"`
	InviteCardBg         string              `protobuf:"bytes,10,opt,name=invite_card_bg,json=inviteCardBg,proto3" json:"invite_card_bg,omitempty"`
	WeddingInviteType    uint32              `protobuf:"varint,11,opt,name=wedding_invite_type,json=weddingInviteType,proto3" json:"wedding_invite_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *InviteCard) Reset()         { *m = InviteCard{} }
func (m *InviteCard) String() string { return proto.CompactTextString(m) }
func (*InviteCard) ProtoMessage()    {}
func (*InviteCard) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{94}
}
func (m *InviteCard) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InviteCard.Unmarshal(m, b)
}
func (m *InviteCard) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InviteCard.Marshal(b, m, deterministic)
}
func (dst *InviteCard) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InviteCard.Merge(dst, src)
}
func (m *InviteCard) XXX_Size() int {
	return xxx_messageInfo_InviteCard.Size(m)
}
func (m *InviteCard) XXX_DiscardUnknown() {
	xxx_messageInfo_InviteCard.DiscardUnknown(m)
}

var xxx_messageInfo_InviteCard proto.InternalMessageInfo

func (m *InviteCard) GetInviteId() uint32 {
	if m != nil {
		return m.InviteId
	}
	return 0
}

func (m *InviteCard) GetWeddingPlanId() uint32 {
	if m != nil {
		return m.WeddingPlanId
	}
	return 0
}

func (m *InviteCard) GetThemeId() uint32 {
	if m != nil {
		return m.ThemeId
	}
	return 0
}

func (m *InviteCard) GetGroomUid() uint32 {
	if m != nil {
		return m.GroomUid
	}
	return 0
}

func (m *InviteCard) GetBrideUid() uint32 {
	if m != nil {
		return m.BrideUid
	}
	return 0
}

func (m *InviteCard) GetWeddingDatetime() uint32 {
	if m != nil {
		return m.WeddingDatetime
	}
	return 0
}

func (m *InviteCard) GetWeddingChannelId() uint32 {
	if m != nil {
		return m.WeddingChannelId
	}
	return 0
}

func (m *InviteCard) GetGiftList() []*WeddingGuestGift {
	if m != nil {
		return m.GiftList
	}
	return nil
}

func (m *InviteCard) GetInviteCardBg() string {
	if m != nil {
		return m.InviteCardBg
	}
	return ""
}

func (m *InviteCard) GetWeddingInviteType() uint32 {
	if m != nil {
		return m.WeddingInviteType
	}
	return 0
}

type WeddingGuestGift struct {
	GiftUrl              string   `protobuf:"bytes,1,opt,name=gift_url,json=giftUrl,proto3" json:"gift_url,omitempty"`
	GiftDesc             string   `protobuf:"bytes,2,opt,name=gift_desc,json=giftDesc,proto3" json:"gift_desc,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WeddingGuestGift) Reset()         { *m = WeddingGuestGift{} }
func (m *WeddingGuestGift) String() string { return proto.CompactTextString(m) }
func (*WeddingGuestGift) ProtoMessage()    {}
func (*WeddingGuestGift) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{95}
}
func (m *WeddingGuestGift) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingGuestGift.Unmarshal(m, b)
}
func (m *WeddingGuestGift) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingGuestGift.Marshal(b, m, deterministic)
}
func (dst *WeddingGuestGift) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingGuestGift.Merge(dst, src)
}
func (m *WeddingGuestGift) XXX_Size() int {
	return xxx_messageInfo_WeddingGuestGift.Size(m)
}
func (m *WeddingGuestGift) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingGuestGift.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingGuestGift proto.InternalMessageInfo

func (m *WeddingGuestGift) GetGiftUrl() string {
	if m != nil {
		return m.GiftUrl
	}
	return ""
}

func (m *WeddingGuestGift) GetGiftDesc() string {
	if m != nil {
		return m.GiftDesc
	}
	return ""
}

// 获取邀请信息(点击im)
type GetWeddingInviteInfoRequest struct {
	WeddingPlanId        uint32   `protobuf:"varint,1,opt,name=wedding_plan_id,json=weddingPlanId,proto3" json:"wedding_plan_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	WeddingInviteType    uint32   `protobuf:"varint,3,opt,name=wedding_invite_type,json=weddingInviteType,proto3" json:"wedding_invite_type,omitempty"`
	InviteId             uint32   `protobuf:"varint,4,opt,name=invite_id,json=inviteId,proto3" json:"invite_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetWeddingInviteInfoRequest) Reset()         { *m = GetWeddingInviteInfoRequest{} }
func (m *GetWeddingInviteInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetWeddingInviteInfoRequest) ProtoMessage()    {}
func (*GetWeddingInviteInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{96}
}
func (m *GetWeddingInviteInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWeddingInviteInfoRequest.Unmarshal(m, b)
}
func (m *GetWeddingInviteInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWeddingInviteInfoRequest.Marshal(b, m, deterministic)
}
func (dst *GetWeddingInviteInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWeddingInviteInfoRequest.Merge(dst, src)
}
func (m *GetWeddingInviteInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetWeddingInviteInfoRequest.Size(m)
}
func (m *GetWeddingInviteInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWeddingInviteInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetWeddingInviteInfoRequest proto.InternalMessageInfo

func (m *GetWeddingInviteInfoRequest) GetWeddingPlanId() uint32 {
	if m != nil {
		return m.WeddingPlanId
	}
	return 0
}

func (m *GetWeddingInviteInfoRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetWeddingInviteInfoRequest) GetWeddingInviteType() uint32 {
	if m != nil {
		return m.WeddingInviteType
	}
	return 0
}

func (m *GetWeddingInviteInfoRequest) GetInviteId() uint32 {
	if m != nil {
		return m.InviteId
	}
	return 0
}

type GetWeddingInviteInfoResponse struct {
	InviteCard           *InviteCard `protobuf:"bytes,1,opt,name=invite_card,json=inviteCard,proto3" json:"invite_card,omitempty"`
	InviteStatus         uint32      `protobuf:"varint,2,opt,name=invite_status,json=inviteStatus,proto3" json:"invite_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetWeddingInviteInfoResponse) Reset()         { *m = GetWeddingInviteInfoResponse{} }
func (m *GetWeddingInviteInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetWeddingInviteInfoResponse) ProtoMessage()    {}
func (*GetWeddingInviteInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{97}
}
func (m *GetWeddingInviteInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWeddingInviteInfoResponse.Unmarshal(m, b)
}
func (m *GetWeddingInviteInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWeddingInviteInfoResponse.Marshal(b, m, deterministic)
}
func (dst *GetWeddingInviteInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWeddingInviteInfoResponse.Merge(dst, src)
}
func (m *GetWeddingInviteInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetWeddingInviteInfoResponse.Size(m)
}
func (m *GetWeddingInviteInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWeddingInviteInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetWeddingInviteInfoResponse proto.InternalMessageInfo

func (m *GetWeddingInviteInfoResponse) GetInviteCard() *InviteCard {
	if m != nil {
		return m.InviteCard
	}
	return nil
}

func (m *GetWeddingInviteInfoResponse) GetInviteStatus() uint32 {
	if m != nil {
		return m.InviteStatus
	}
	return 0
}

type SetUserRelationHideStatusRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	OpType               uint32   `protobuf:"varint,2,opt,name=op_type,json=opType,proto3" json:"op_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetUserRelationHideStatusRequest) Reset()         { *m = SetUserRelationHideStatusRequest{} }
func (m *SetUserRelationHideStatusRequest) String() string { return proto.CompactTextString(m) }
func (*SetUserRelationHideStatusRequest) ProtoMessage()    {}
func (*SetUserRelationHideStatusRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{98}
}
func (m *SetUserRelationHideStatusRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserRelationHideStatusRequest.Unmarshal(m, b)
}
func (m *SetUserRelationHideStatusRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserRelationHideStatusRequest.Marshal(b, m, deterministic)
}
func (dst *SetUserRelationHideStatusRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserRelationHideStatusRequest.Merge(dst, src)
}
func (m *SetUserRelationHideStatusRequest) XXX_Size() int {
	return xxx_messageInfo_SetUserRelationHideStatusRequest.Size(m)
}
func (m *SetUserRelationHideStatusRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserRelationHideStatusRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserRelationHideStatusRequest proto.InternalMessageInfo

func (m *SetUserRelationHideStatusRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetUserRelationHideStatusRequest) GetOpType() uint32 {
	if m != nil {
		return m.OpType
	}
	return 0
}

type SetUserRelationHideStatusResponse struct {
	HideStatus           uint32   `protobuf:"varint,1,opt,name=hide_status,json=hideStatus,proto3" json:"hide_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetUserRelationHideStatusResponse) Reset()         { *m = SetUserRelationHideStatusResponse{} }
func (m *SetUserRelationHideStatusResponse) String() string { return proto.CompactTextString(m) }
func (*SetUserRelationHideStatusResponse) ProtoMessage()    {}
func (*SetUserRelationHideStatusResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{99}
}
func (m *SetUserRelationHideStatusResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserRelationHideStatusResponse.Unmarshal(m, b)
}
func (m *SetUserRelationHideStatusResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserRelationHideStatusResponse.Marshal(b, m, deterministic)
}
func (dst *SetUserRelationHideStatusResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserRelationHideStatusResponse.Merge(dst, src)
}
func (m *SetUserRelationHideStatusResponse) XXX_Size() int {
	return xxx_messageInfo_SetUserRelationHideStatusResponse.Size(m)
}
func (m *SetUserRelationHideStatusResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserRelationHideStatusResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserRelationHideStatusResponse proto.InternalMessageInfo

func (m *SetUserRelationHideStatusResponse) GetHideStatus() uint32 {
	if m != nil {
		return m.HideStatus
	}
	return 0
}

type DivorceRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PartnerUid           uint32   `protobuf:"varint,2,opt,name=partner_uid,json=partnerUid,proto3" json:"partner_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DivorceRequest) Reset()         { *m = DivorceRequest{} }
func (m *DivorceRequest) String() string { return proto.CompactTextString(m) }
func (*DivorceRequest) ProtoMessage()    {}
func (*DivorceRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{100}
}
func (m *DivorceRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DivorceRequest.Unmarshal(m, b)
}
func (m *DivorceRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DivorceRequest.Marshal(b, m, deterministic)
}
func (dst *DivorceRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DivorceRequest.Merge(dst, src)
}
func (m *DivorceRequest) XXX_Size() int {
	return xxx_messageInfo_DivorceRequest.Size(m)
}
func (m *DivorceRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DivorceRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DivorceRequest proto.InternalMessageInfo

func (m *DivorceRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DivorceRequest) GetPartnerUid() uint32 {
	if m != nil {
		return m.PartnerUid
	}
	return 0
}

type DivorceResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DivorceResponse) Reset()         { *m = DivorceResponse{} }
func (m *DivorceResponse) String() string { return proto.CompactTextString(m) }
func (*DivorceResponse) ProtoMessage()    {}
func (*DivorceResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{101}
}
func (m *DivorceResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DivorceResponse.Unmarshal(m, b)
}
func (m *DivorceResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DivorceResponse.Marshal(b, m, deterministic)
}
func (dst *DivorceResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DivorceResponse.Merge(dst, src)
}
func (m *DivorceResponse) XXX_Size() int {
	return xxx_messageInfo_DivorceResponse.Size(m)
}
func (m *DivorceResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DivorceResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DivorceResponse proto.InternalMessageInfo

type GetUserDivorceStatusRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserDivorceStatusRequest) Reset()         { *m = GetUserDivorceStatusRequest{} }
func (m *GetUserDivorceStatusRequest) String() string { return proto.CompactTextString(m) }
func (*GetUserDivorceStatusRequest) ProtoMessage()    {}
func (*GetUserDivorceStatusRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{102}
}
func (m *GetUserDivorceStatusRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserDivorceStatusRequest.Unmarshal(m, b)
}
func (m *GetUserDivorceStatusRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserDivorceStatusRequest.Marshal(b, m, deterministic)
}
func (dst *GetUserDivorceStatusRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserDivorceStatusRequest.Merge(dst, src)
}
func (m *GetUserDivorceStatusRequest) XXX_Size() int {
	return xxx_messageInfo_GetUserDivorceStatusRequest.Size(m)
}
func (m *GetUserDivorceStatusRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserDivorceStatusRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserDivorceStatusRequest proto.InternalMessageInfo

func (m *GetUserDivorceStatusRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserDivorceStatusResponse struct {
	DivorceDeadline      int64    `protobuf:"varint,1,opt,name=divorce_deadline,json=divorceDeadline,proto3" json:"divorce_deadline,omitempty"`
	AutoDivorceDay       int64    `protobuf:"varint,2,opt,name=auto_divorce_day,json=autoDivorceDay,proto3" json:"auto_divorce_day,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserDivorceStatusResponse) Reset()         { *m = GetUserDivorceStatusResponse{} }
func (m *GetUserDivorceStatusResponse) String() string { return proto.CompactTextString(m) }
func (*GetUserDivorceStatusResponse) ProtoMessage()    {}
func (*GetUserDivorceStatusResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{103}
}
func (m *GetUserDivorceStatusResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserDivorceStatusResponse.Unmarshal(m, b)
}
func (m *GetUserDivorceStatusResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserDivorceStatusResponse.Marshal(b, m, deterministic)
}
func (dst *GetUserDivorceStatusResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserDivorceStatusResponse.Merge(dst, src)
}
func (m *GetUserDivorceStatusResponse) XXX_Size() int {
	return xxx_messageInfo_GetUserDivorceStatusResponse.Size(m)
}
func (m *GetUserDivorceStatusResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserDivorceStatusResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserDivorceStatusResponse proto.InternalMessageInfo

func (m *GetUserDivorceStatusResponse) GetDivorceDeadline() int64 {
	if m != nil {
		return m.DivorceDeadline
	}
	return 0
}

func (m *GetUserDivorceStatusResponse) GetAutoDivorceDay() int64 {
	if m != nil {
		return m.AutoDivorceDay
	}
	return 0
}

type MarriageRelationInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PartnerUid           uint32   `protobuf:"varint,2,opt,name=partner_uid,json=partnerUid,proto3" json:"partner_uid,omitempty"`
	RelationCtime        int64    `protobuf:"varint,3,opt,name=relation_ctime,json=relationCtime,proto3" json:"relation_ctime,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MarriageRelationInfo) Reset()         { *m = MarriageRelationInfo{} }
func (m *MarriageRelationInfo) String() string { return proto.CompactTextString(m) }
func (*MarriageRelationInfo) ProtoMessage()    {}
func (*MarriageRelationInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{104}
}
func (m *MarriageRelationInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarriageRelationInfo.Unmarshal(m, b)
}
func (m *MarriageRelationInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarriageRelationInfo.Marshal(b, m, deterministic)
}
func (dst *MarriageRelationInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarriageRelationInfo.Merge(dst, src)
}
func (m *MarriageRelationInfo) XXX_Size() int {
	return xxx_messageInfo_MarriageRelationInfo.Size(m)
}
func (m *MarriageRelationInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MarriageRelationInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MarriageRelationInfo proto.InternalMessageInfo

func (m *MarriageRelationInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MarriageRelationInfo) GetPartnerUid() uint32 {
	if m != nil {
		return m.PartnerUid
	}
	return 0
}

func (m *MarriageRelationInfo) GetRelationCtime() int64 {
	if m != nil {
		return m.RelationCtime
	}
	return 0
}

type GetMarriageStatusRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMarriageStatusRequest) Reset()         { *m = GetMarriageStatusRequest{} }
func (m *GetMarriageStatusRequest) String() string { return proto.CompactTextString(m) }
func (*GetMarriageStatusRequest) ProtoMessage()    {}
func (*GetMarriageStatusRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{105}
}
func (m *GetMarriageStatusRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMarriageStatusRequest.Unmarshal(m, b)
}
func (m *GetMarriageStatusRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMarriageStatusRequest.Marshal(b, m, deterministic)
}
func (dst *GetMarriageStatusRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMarriageStatusRequest.Merge(dst, src)
}
func (m *GetMarriageStatusRequest) XXX_Size() int {
	return xxx_messageInfo_GetMarriageStatusRequest.Size(m)
}
func (m *GetMarriageStatusRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMarriageStatusRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetMarriageStatusRequest proto.InternalMessageInfo

func (m *GetMarriageStatusRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetMarriageStatusResponse struct {
	RelationInfo         *MarriageRelationInfo `protobuf:"bytes,1,opt,name=relation_info,json=relationInfo,proto3" json:"relation_info,omitempty"`
	DivorceDeadline      int64                 `protobuf:"varint,2,opt,name=divorce_deadline,json=divorceDeadline,proto3" json:"divorce_deadline,omitempty"`
	AutoDivorceDay       int64                 `protobuf:"varint,3,opt,name=auto_divorce_day,json=autoDivorceDay,proto3" json:"auto_divorce_day,omitempty"`
	IsHide               bool                  `protobuf:"varint,4,opt,name=is_hide,json=isHide,proto3" json:"is_hide,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetMarriageStatusResponse) Reset()         { *m = GetMarriageStatusResponse{} }
func (m *GetMarriageStatusResponse) String() string { return proto.CompactTextString(m) }
func (*GetMarriageStatusResponse) ProtoMessage()    {}
func (*GetMarriageStatusResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{106}
}
func (m *GetMarriageStatusResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMarriageStatusResponse.Unmarshal(m, b)
}
func (m *GetMarriageStatusResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMarriageStatusResponse.Marshal(b, m, deterministic)
}
func (dst *GetMarriageStatusResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMarriageStatusResponse.Merge(dst, src)
}
func (m *GetMarriageStatusResponse) XXX_Size() int {
	return xxx_messageInfo_GetMarriageStatusResponse.Size(m)
}
func (m *GetMarriageStatusResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMarriageStatusResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetMarriageStatusResponse proto.InternalMessageInfo

func (m *GetMarriageStatusResponse) GetRelationInfo() *MarriageRelationInfo {
	if m != nil {
		return m.RelationInfo
	}
	return nil
}

func (m *GetMarriageStatusResponse) GetDivorceDeadline() int64 {
	if m != nil {
		return m.DivorceDeadline
	}
	return 0
}

func (m *GetMarriageStatusResponse) GetAutoDivorceDay() int64 {
	if m != nil {
		return m.AutoDivorceDay
	}
	return 0
}

func (m *GetMarriageStatusResponse) GetIsHide() bool {
	if m != nil {
		return m.IsHide
	}
	return false
}

type ManualNotifyWeddingStartRequest struct {
	WeddingPlanId        uint32   `protobuf:"varint,1,opt,name=wedding_plan_id,json=weddingPlanId,proto3" json:"wedding_plan_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ManualNotifyWeddingStartRequest) Reset()         { *m = ManualNotifyWeddingStartRequest{} }
func (m *ManualNotifyWeddingStartRequest) String() string { return proto.CompactTextString(m) }
func (*ManualNotifyWeddingStartRequest) ProtoMessage()    {}
func (*ManualNotifyWeddingStartRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{107}
}
func (m *ManualNotifyWeddingStartRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ManualNotifyWeddingStartRequest.Unmarshal(m, b)
}
func (m *ManualNotifyWeddingStartRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ManualNotifyWeddingStartRequest.Marshal(b, m, deterministic)
}
func (dst *ManualNotifyWeddingStartRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ManualNotifyWeddingStartRequest.Merge(dst, src)
}
func (m *ManualNotifyWeddingStartRequest) XXX_Size() int {
	return xxx_messageInfo_ManualNotifyWeddingStartRequest.Size(m)
}
func (m *ManualNotifyWeddingStartRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ManualNotifyWeddingStartRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ManualNotifyWeddingStartRequest proto.InternalMessageInfo

func (m *ManualNotifyWeddingStartRequest) GetWeddingPlanId() uint32 {
	if m != nil {
		return m.WeddingPlanId
	}
	return 0
}

type ManualNotifyWeddingStartResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ManualNotifyWeddingStartResponse) Reset()         { *m = ManualNotifyWeddingStartResponse{} }
func (m *ManualNotifyWeddingStartResponse) String() string { return proto.CompactTextString(m) }
func (*ManualNotifyWeddingStartResponse) ProtoMessage()    {}
func (*ManualNotifyWeddingStartResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{108}
}
func (m *ManualNotifyWeddingStartResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ManualNotifyWeddingStartResponse.Unmarshal(m, b)
}
func (m *ManualNotifyWeddingStartResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ManualNotifyWeddingStartResponse.Marshal(b, m, deterministic)
}
func (dst *ManualNotifyWeddingStartResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ManualNotifyWeddingStartResponse.Merge(dst, src)
}
func (m *ManualNotifyWeddingStartResponse) XXX_Size() int {
	return xxx_messageInfo_ManualNotifyWeddingStartResponse.Size(m)
}
func (m *ManualNotifyWeddingStartResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ManualNotifyWeddingStartResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ManualNotifyWeddingStartResponse proto.InternalMessageInfo

type UpdateWeddingPlanStatusRequest struct {
	WeddingPlanId        uint32   `protobuf:"varint,1,opt,name=wedding_plan_id,json=weddingPlanId,proto3" json:"wedding_plan_id,omitempty"`
	IsForce              bool     `protobuf:"varint,2,opt,name=is_force,json=isForce,proto3" json:"is_force,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateWeddingPlanStatusRequest) Reset()         { *m = UpdateWeddingPlanStatusRequest{} }
func (m *UpdateWeddingPlanStatusRequest) String() string { return proto.CompactTextString(m) }
func (*UpdateWeddingPlanStatusRequest) ProtoMessage()    {}
func (*UpdateWeddingPlanStatusRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{109}
}
func (m *UpdateWeddingPlanStatusRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateWeddingPlanStatusRequest.Unmarshal(m, b)
}
func (m *UpdateWeddingPlanStatusRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateWeddingPlanStatusRequest.Marshal(b, m, deterministic)
}
func (dst *UpdateWeddingPlanStatusRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateWeddingPlanStatusRequest.Merge(dst, src)
}
func (m *UpdateWeddingPlanStatusRequest) XXX_Size() int {
	return xxx_messageInfo_UpdateWeddingPlanStatusRequest.Size(m)
}
func (m *UpdateWeddingPlanStatusRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateWeddingPlanStatusRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateWeddingPlanStatusRequest proto.InternalMessageInfo

func (m *UpdateWeddingPlanStatusRequest) GetWeddingPlanId() uint32 {
	if m != nil {
		return m.WeddingPlanId
	}
	return 0
}

func (m *UpdateWeddingPlanStatusRequest) GetIsForce() bool {
	if m != nil {
		return m.IsForce
	}
	return false
}

type UpdateWeddingPlanStatusResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateWeddingPlanStatusResponse) Reset()         { *m = UpdateWeddingPlanStatusResponse{} }
func (m *UpdateWeddingPlanStatusResponse) String() string { return proto.CompactTextString(m) }
func (*UpdateWeddingPlanStatusResponse) ProtoMessage()    {}
func (*UpdateWeddingPlanStatusResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{110}
}
func (m *UpdateWeddingPlanStatusResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateWeddingPlanStatusResponse.Unmarshal(m, b)
}
func (m *UpdateWeddingPlanStatusResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateWeddingPlanStatusResponse.Marshal(b, m, deterministic)
}
func (dst *UpdateWeddingPlanStatusResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateWeddingPlanStatusResponse.Merge(dst, src)
}
func (m *UpdateWeddingPlanStatusResponse) XXX_Size() int {
	return xxx_messageInfo_UpdateWeddingPlanStatusResponse.Size(m)
}
func (m *UpdateWeddingPlanStatusResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateWeddingPlanStatusResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateWeddingPlanStatusResponse proto.InternalMessageInfo

type TestWeddingAnniversaryPopupRequest struct {
	TargetTime           int64                                     `protobuf:"varint,1,opt,name=target_time,json=targetTime,proto3" json:"target_time,omitempty"`
	OpType               TestWeddingAnniversaryPopupRequest_OpType `protobuf:"varint,2,opt,name=op_type,json=opType,proto3,enum=channel_wedding_plan.TestWeddingAnniversaryPopupRequest_OpType" json:"op_type,omitempty"`
	Uid                  uint32                                    `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	Day                  uint32                                    `protobuf:"varint,4,opt,name=day,proto3" json:"day,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                  `json:"-"`
	XXX_unrecognized     []byte                                    `json:"-"`
	XXX_sizecache        int32                                     `json:"-"`
}

func (m *TestWeddingAnniversaryPopupRequest) Reset()         { *m = TestWeddingAnniversaryPopupRequest{} }
func (m *TestWeddingAnniversaryPopupRequest) String() string { return proto.CompactTextString(m) }
func (*TestWeddingAnniversaryPopupRequest) ProtoMessage()    {}
func (*TestWeddingAnniversaryPopupRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{111}
}
func (m *TestWeddingAnniversaryPopupRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TestWeddingAnniversaryPopupRequest.Unmarshal(m, b)
}
func (m *TestWeddingAnniversaryPopupRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TestWeddingAnniversaryPopupRequest.Marshal(b, m, deterministic)
}
func (dst *TestWeddingAnniversaryPopupRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TestWeddingAnniversaryPopupRequest.Merge(dst, src)
}
func (m *TestWeddingAnniversaryPopupRequest) XXX_Size() int {
	return xxx_messageInfo_TestWeddingAnniversaryPopupRequest.Size(m)
}
func (m *TestWeddingAnniversaryPopupRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_TestWeddingAnniversaryPopupRequest.DiscardUnknown(m)
}

var xxx_messageInfo_TestWeddingAnniversaryPopupRequest proto.InternalMessageInfo

func (m *TestWeddingAnniversaryPopupRequest) GetTargetTime() int64 {
	if m != nil {
		return m.TargetTime
	}
	return 0
}

func (m *TestWeddingAnniversaryPopupRequest) GetOpType() TestWeddingAnniversaryPopupRequest_OpType {
	if m != nil {
		return m.OpType
	}
	return TestWeddingAnniversaryPopupRequest_OP_TYPE_UNEXPECTED
}

func (m *TestWeddingAnniversaryPopupRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *TestWeddingAnniversaryPopupRequest) GetDay() uint32 {
	if m != nil {
		return m.Day
	}
	return 0
}

type TestWeddingAnniversaryPopupResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TestWeddingAnniversaryPopupResponse) Reset()         { *m = TestWeddingAnniversaryPopupResponse{} }
func (m *TestWeddingAnniversaryPopupResponse) String() string { return proto.CompactTextString(m) }
func (*TestWeddingAnniversaryPopupResponse) ProtoMessage()    {}
func (*TestWeddingAnniversaryPopupResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{112}
}
func (m *TestWeddingAnniversaryPopupResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TestWeddingAnniversaryPopupResponse.Unmarshal(m, b)
}
func (m *TestWeddingAnniversaryPopupResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TestWeddingAnniversaryPopupResponse.Marshal(b, m, deterministic)
}
func (dst *TestWeddingAnniversaryPopupResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TestWeddingAnniversaryPopupResponse.Merge(dst, src)
}
func (m *TestWeddingAnniversaryPopupResponse) XXX_Size() int {
	return xxx_messageInfo_TestWeddingAnniversaryPopupResponse.Size(m)
}
func (m *TestWeddingAnniversaryPopupResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_TestWeddingAnniversaryPopupResponse.DiscardUnknown(m)
}

var xxx_messageInfo_TestWeddingAnniversaryPopupResponse proto.InternalMessageInfo

type GetMyWeddingRoleRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMyWeddingRoleRequest) Reset()         { *m = GetMyWeddingRoleRequest{} }
func (m *GetMyWeddingRoleRequest) String() string { return proto.CompactTextString(m) }
func (*GetMyWeddingRoleRequest) ProtoMessage()    {}
func (*GetMyWeddingRoleRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{113}
}
func (m *GetMyWeddingRoleRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMyWeddingRoleRequest.Unmarshal(m, b)
}
func (m *GetMyWeddingRoleRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMyWeddingRoleRequest.Marshal(b, m, deterministic)
}
func (dst *GetMyWeddingRoleRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMyWeddingRoleRequest.Merge(dst, src)
}
func (m *GetMyWeddingRoleRequest) XXX_Size() int {
	return xxx_messageInfo_GetMyWeddingRoleRequest.Size(m)
}
func (m *GetMyWeddingRoleRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMyWeddingRoleRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetMyWeddingRoleRequest proto.InternalMessageInfo

func (m *GetMyWeddingRoleRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetMyWeddingRoleResponse struct {
	WeddingRole          uint32   `protobuf:"varint,1,opt,name=wedding_role,json=weddingRole,proto3" json:"wedding_role,omitempty"`
	InChangeTime         bool     `protobuf:"varint,2,opt,name=in_change_time,json=inChangeTime,proto3" json:"in_change_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMyWeddingRoleResponse) Reset()         { *m = GetMyWeddingRoleResponse{} }
func (m *GetMyWeddingRoleResponse) String() string { return proto.CompactTextString(m) }
func (*GetMyWeddingRoleResponse) ProtoMessage()    {}
func (*GetMyWeddingRoleResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{114}
}
func (m *GetMyWeddingRoleResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMyWeddingRoleResponse.Unmarshal(m, b)
}
func (m *GetMyWeddingRoleResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMyWeddingRoleResponse.Marshal(b, m, deterministic)
}
func (dst *GetMyWeddingRoleResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMyWeddingRoleResponse.Merge(dst, src)
}
func (m *GetMyWeddingRoleResponse) XXX_Size() int {
	return xxx_messageInfo_GetMyWeddingRoleResponse.Size(m)
}
func (m *GetMyWeddingRoleResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMyWeddingRoleResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetMyWeddingRoleResponse proto.InternalMessageInfo

func (m *GetMyWeddingRoleResponse) GetWeddingRole() uint32 {
	if m != nil {
		return m.WeddingRole
	}
	return 0
}

func (m *GetMyWeddingRoleResponse) GetInChangeTime() bool {
	if m != nil {
		return m.InChangeTime
	}
	return false
}

type BatchGetWeddingRoleRequest struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	PlanId               uint32   `protobuf:"varint,2,opt,name=plan_id,json=planId,proto3" json:"plan_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetWeddingRoleRequest) Reset()         { *m = BatchGetWeddingRoleRequest{} }
func (m *BatchGetWeddingRoleRequest) String() string { return proto.CompactTextString(m) }
func (*BatchGetWeddingRoleRequest) ProtoMessage()    {}
func (*BatchGetWeddingRoleRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{115}
}
func (m *BatchGetWeddingRoleRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetWeddingRoleRequest.Unmarshal(m, b)
}
func (m *BatchGetWeddingRoleRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetWeddingRoleRequest.Marshal(b, m, deterministic)
}
func (dst *BatchGetWeddingRoleRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetWeddingRoleRequest.Merge(dst, src)
}
func (m *BatchGetWeddingRoleRequest) XXX_Size() int {
	return xxx_messageInfo_BatchGetWeddingRoleRequest.Size(m)
}
func (m *BatchGetWeddingRoleRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetWeddingRoleRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetWeddingRoleRequest proto.InternalMessageInfo

func (m *BatchGetWeddingRoleRequest) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *BatchGetWeddingRoleRequest) GetPlanId() uint32 {
	if m != nil {
		return m.PlanId
	}
	return 0
}

type BatchGetWeddingRoleResponse struct {
	WeddingRoleMap       map[uint32]uint32 `protobuf:"bytes,1,rep,name=wedding_role_map,json=weddingRoleMap,proto3" json:"wedding_role_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *BatchGetWeddingRoleResponse) Reset()         { *m = BatchGetWeddingRoleResponse{} }
func (m *BatchGetWeddingRoleResponse) String() string { return proto.CompactTextString(m) }
func (*BatchGetWeddingRoleResponse) ProtoMessage()    {}
func (*BatchGetWeddingRoleResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{116}
}
func (m *BatchGetWeddingRoleResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetWeddingRoleResponse.Unmarshal(m, b)
}
func (m *BatchGetWeddingRoleResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetWeddingRoleResponse.Marshal(b, m, deterministic)
}
func (dst *BatchGetWeddingRoleResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetWeddingRoleResponse.Merge(dst, src)
}
func (m *BatchGetWeddingRoleResponse) XXX_Size() int {
	return xxx_messageInfo_BatchGetWeddingRoleResponse.Size(m)
}
func (m *BatchGetWeddingRoleResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetWeddingRoleResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetWeddingRoleResponse proto.InternalMessageInfo

func (m *BatchGetWeddingRoleResponse) GetWeddingRoleMap() map[uint32]uint32 {
	if m != nil {
		return m.WeddingRoleMap
	}
	return nil
}

// 获取可预约信息 url: /tt-revenue-http-logic/channel_wedding/get_channel_reserved_info
type GetChannelReservedInfoRequest struct {
	ReserveDate          uint32   `protobuf:"varint,1,opt,name=reserve_date,json=reserveDate,proto3" json:"reserve_date,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ThemeType            uint32   `protobuf:"varint,3,opt,name=theme_type,json=themeType,proto3" json:"theme_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelReservedInfoRequest) Reset()         { *m = GetChannelReservedInfoRequest{} }
func (m *GetChannelReservedInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetChannelReservedInfoRequest) ProtoMessage()    {}
func (*GetChannelReservedInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{117}
}
func (m *GetChannelReservedInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelReservedInfoRequest.Unmarshal(m, b)
}
func (m *GetChannelReservedInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelReservedInfoRequest.Marshal(b, m, deterministic)
}
func (dst *GetChannelReservedInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelReservedInfoRequest.Merge(dst, src)
}
func (m *GetChannelReservedInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetChannelReservedInfoRequest.Size(m)
}
func (m *GetChannelReservedInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelReservedInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelReservedInfoRequest proto.InternalMessageInfo

func (m *GetChannelReservedInfoRequest) GetReserveDate() uint32 {
	if m != nil {
		return m.ReserveDate
	}
	return 0
}

func (m *GetChannelReservedInfoRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetChannelReservedInfoRequest) GetThemeType() uint32 {
	if m != nil {
		return m.ThemeType
	}
	return 0
}

type GetChannelReservedInfoResponse struct {
	ReserveTimeInfoList  []*ChannelReserveTimeInfo `protobuf:"bytes,1,rep,name=reserve_time_info_list,json=reserveTimeInfoList,proto3" json:"reserve_time_info_list,omitempty"`
	MinReserveDate       uint32                    `protobuf:"varint,2,opt,name=min_reserve_date,json=minReserveDate,proto3" json:"min_reserve_date,omitempty"`
	MaxReserveDate       uint32                    `protobuf:"varint,3,opt,name=max_reserve_date,json=maxReserveDate,proto3" json:"max_reserve_date,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetChannelReservedInfoResponse) Reset()         { *m = GetChannelReservedInfoResponse{} }
func (m *GetChannelReservedInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetChannelReservedInfoResponse) ProtoMessage()    {}
func (*GetChannelReservedInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{118}
}
func (m *GetChannelReservedInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelReservedInfoResponse.Unmarshal(m, b)
}
func (m *GetChannelReservedInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelReservedInfoResponse.Marshal(b, m, deterministic)
}
func (dst *GetChannelReservedInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelReservedInfoResponse.Merge(dst, src)
}
func (m *GetChannelReservedInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetChannelReservedInfoResponse.Size(m)
}
func (m *GetChannelReservedInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelReservedInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelReservedInfoResponse proto.InternalMessageInfo

func (m *GetChannelReservedInfoResponse) GetReserveTimeInfoList() []*ChannelReserveTimeInfo {
	if m != nil {
		return m.ReserveTimeInfoList
	}
	return nil
}

func (m *GetChannelReservedInfoResponse) GetMinReserveDate() uint32 {
	if m != nil {
		return m.MinReserveDate
	}
	return 0
}

func (m *GetChannelReservedInfoResponse) GetMaxReserveDate() uint32 {
	if m != nil {
		return m.MaxReserveDate
	}
	return 0
}

// 预约时段信息
type ChannelReserveTimeInfo struct {
	ReserveTime          string   `protobuf:"bytes,1,opt,name=reserve_time,json=reserveTime,proto3" json:"reserve_time,omitempty"`
	GroomUid             uint32   `protobuf:"varint,2,opt,name=groom_uid,json=groomUid,proto3" json:"groom_uid,omitempty"`
	BrideUid             uint32   `protobuf:"varint,3,opt,name=bride_uid,json=brideUid,proto3" json:"bride_uid,omitempty"`
	IsHot                bool     `protobuf:"varint,4,opt,name=is_hot,json=isHot,proto3" json:"is_hot,omitempty"`
	ThemeId              uint32   `protobuf:"varint,5,opt,name=theme_id,json=themeId,proto3" json:"theme_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelReserveTimeInfo) Reset()         { *m = ChannelReserveTimeInfo{} }
func (m *ChannelReserveTimeInfo) String() string { return proto.CompactTextString(m) }
func (*ChannelReserveTimeInfo) ProtoMessage()    {}
func (*ChannelReserveTimeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{119}
}
func (m *ChannelReserveTimeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelReserveTimeInfo.Unmarshal(m, b)
}
func (m *ChannelReserveTimeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelReserveTimeInfo.Marshal(b, m, deterministic)
}
func (dst *ChannelReserveTimeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelReserveTimeInfo.Merge(dst, src)
}
func (m *ChannelReserveTimeInfo) XXX_Size() int {
	return xxx_messageInfo_ChannelReserveTimeInfo.Size(m)
}
func (m *ChannelReserveTimeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelReserveTimeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelReserveTimeInfo proto.InternalMessageInfo

func (m *ChannelReserveTimeInfo) GetReserveTime() string {
	if m != nil {
		return m.ReserveTime
	}
	return ""
}

func (m *ChannelReserveTimeInfo) GetGroomUid() uint32 {
	if m != nil {
		return m.GroomUid
	}
	return 0
}

func (m *ChannelReserveTimeInfo) GetBrideUid() uint32 {
	if m != nil {
		return m.BrideUid
	}
	return 0
}

func (m *ChannelReserveTimeInfo) GetIsHot() bool {
	if m != nil {
		return m.IsHot
	}
	return false
}

func (m *ChannelReserveTimeInfo) GetThemeId() uint32 {
	if m != nil {
		return m.ThemeId
	}
	return 0
}

// 咨询婚礼预约
type ConsultWeddingReserveRequest struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ReserveDate          uint32   `protobuf:"varint,2,opt,name=reserve_date,json=reserveDate,proto3" json:"reserve_date,omitempty"`
	ReserveTime          []string `protobuf:"bytes,3,rep,name=reserve_time,json=reserveTime,proto3" json:"reserve_time,omitempty"`
	ThemeId              uint32   `protobuf:"varint,4,opt,name=theme_id,json=themeId,proto3" json:"theme_id,omitempty"`
	ManagerUid           uint32   `protobuf:"varint,5,opt,name=manager_uid,json=managerUid,proto3" json:"manager_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConsultWeddingReserveRequest) Reset()         { *m = ConsultWeddingReserveRequest{} }
func (m *ConsultWeddingReserveRequest) String() string { return proto.CompactTextString(m) }
func (*ConsultWeddingReserveRequest) ProtoMessage()    {}
func (*ConsultWeddingReserveRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{120}
}
func (m *ConsultWeddingReserveRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConsultWeddingReserveRequest.Unmarshal(m, b)
}
func (m *ConsultWeddingReserveRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConsultWeddingReserveRequest.Marshal(b, m, deterministic)
}
func (dst *ConsultWeddingReserveRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConsultWeddingReserveRequest.Merge(dst, src)
}
func (m *ConsultWeddingReserveRequest) XXX_Size() int {
	return xxx_messageInfo_ConsultWeddingReserveRequest.Size(m)
}
func (m *ConsultWeddingReserveRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ConsultWeddingReserveRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ConsultWeddingReserveRequest proto.InternalMessageInfo

func (m *ConsultWeddingReserveRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ConsultWeddingReserveRequest) GetReserveDate() uint32 {
	if m != nil {
		return m.ReserveDate
	}
	return 0
}

func (m *ConsultWeddingReserveRequest) GetReserveTime() []string {
	if m != nil {
		return m.ReserveTime
	}
	return nil
}

func (m *ConsultWeddingReserveRequest) GetThemeId() uint32 {
	if m != nil {
		return m.ThemeId
	}
	return 0
}

func (m *ConsultWeddingReserveRequest) GetManagerUid() uint32 {
	if m != nil {
		return m.ManagerUid
	}
	return 0
}

type ConsultWeddingReserveResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConsultWeddingReserveResponse) Reset()         { *m = ConsultWeddingReserveResponse{} }
func (m *ConsultWeddingReserveResponse) String() string { return proto.CompactTextString(m) }
func (*ConsultWeddingReserveResponse) ProtoMessage()    {}
func (*ConsultWeddingReserveResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{121}
}
func (m *ConsultWeddingReserveResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConsultWeddingReserveResponse.Unmarshal(m, b)
}
func (m *ConsultWeddingReserveResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConsultWeddingReserveResponse.Marshal(b, m, deterministic)
}
func (dst *ConsultWeddingReserveResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConsultWeddingReserveResponse.Merge(dst, src)
}
func (m *ConsultWeddingReserveResponse) XXX_Size() int {
	return xxx_messageInfo_ConsultWeddingReserveResponse.Size(m)
}
func (m *ConsultWeddingReserveResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ConsultWeddingReserveResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ConsultWeddingReserveResponse proto.InternalMessageInfo

type ArrangeWeddingReserveRequest struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ReserveDate          uint32   `protobuf:"varint,5,opt,name=reserve_date,json=reserveDate,proto3" json:"reserve_date,omitempty"`
	ReserveTime          []string `protobuf:"bytes,2,rep,name=reserve_time,json=reserveTime,proto3" json:"reserve_time,omitempty"`
	IsHot                bool     `protobuf:"varint,3,opt,name=is_hot,json=isHot,proto3" json:"is_hot,omitempty"`
	GiftId               uint32   `protobuf:"varint,4,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	SourceMsgId          uint32   `protobuf:"varint,6,opt,name=source_msg_id,json=sourceMsgId,proto3" json:"source_msg_id,omitempty"`
	ThemeId              uint32   `protobuf:"varint,7,opt,name=theme_id,json=themeId,proto3" json:"theme_id,omitempty"`
	TargetUid            uint32   `protobuf:"varint,8,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ArrangeWeddingReserveRequest) Reset()         { *m = ArrangeWeddingReserveRequest{} }
func (m *ArrangeWeddingReserveRequest) String() string { return proto.CompactTextString(m) }
func (*ArrangeWeddingReserveRequest) ProtoMessage()    {}
func (*ArrangeWeddingReserveRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{122}
}
func (m *ArrangeWeddingReserveRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ArrangeWeddingReserveRequest.Unmarshal(m, b)
}
func (m *ArrangeWeddingReserveRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ArrangeWeddingReserveRequest.Marshal(b, m, deterministic)
}
func (dst *ArrangeWeddingReserveRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ArrangeWeddingReserveRequest.Merge(dst, src)
}
func (m *ArrangeWeddingReserveRequest) XXX_Size() int {
	return xxx_messageInfo_ArrangeWeddingReserveRequest.Size(m)
}
func (m *ArrangeWeddingReserveRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ArrangeWeddingReserveRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ArrangeWeddingReserveRequest proto.InternalMessageInfo

func (m *ArrangeWeddingReserveRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ArrangeWeddingReserveRequest) GetReserveDate() uint32 {
	if m != nil {
		return m.ReserveDate
	}
	return 0
}

func (m *ArrangeWeddingReserveRequest) GetReserveTime() []string {
	if m != nil {
		return m.ReserveTime
	}
	return nil
}

func (m *ArrangeWeddingReserveRequest) GetIsHot() bool {
	if m != nil {
		return m.IsHot
	}
	return false
}

func (m *ArrangeWeddingReserveRequest) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *ArrangeWeddingReserveRequest) GetSourceMsgId() uint32 {
	if m != nil {
		return m.SourceMsgId
	}
	return 0
}

func (m *ArrangeWeddingReserveRequest) GetThemeId() uint32 {
	if m != nil {
		return m.ThemeId
	}
	return 0
}

func (m *ArrangeWeddingReserveRequest) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

type ArrangeWeddingReserveResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ArrangeWeddingReserveResponse) Reset()         { *m = ArrangeWeddingReserveResponse{} }
func (m *ArrangeWeddingReserveResponse) String() string { return proto.CompactTextString(m) }
func (*ArrangeWeddingReserveResponse) ProtoMessage()    {}
func (*ArrangeWeddingReserveResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{123}
}
func (m *ArrangeWeddingReserveResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ArrangeWeddingReserveResponse.Unmarshal(m, b)
}
func (m *ArrangeWeddingReserveResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ArrangeWeddingReserveResponse.Marshal(b, m, deterministic)
}
func (dst *ArrangeWeddingReserveResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ArrangeWeddingReserveResponse.Merge(dst, src)
}
func (m *ArrangeWeddingReserveResponse) XXX_Size() int {
	return xxx_messageInfo_ArrangeWeddingReserveResponse.Size(m)
}
func (m *ArrangeWeddingReserveResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ArrangeWeddingReserveResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ArrangeWeddingReserveResponse proto.InternalMessageInfo

type RevokeProposeRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RevokeProposeRequest) Reset()         { *m = RevokeProposeRequest{} }
func (m *RevokeProposeRequest) String() string { return proto.CompactTextString(m) }
func (*RevokeProposeRequest) ProtoMessage()    {}
func (*RevokeProposeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{124}
}
func (m *RevokeProposeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RevokeProposeRequest.Unmarshal(m, b)
}
func (m *RevokeProposeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RevokeProposeRequest.Marshal(b, m, deterministic)
}
func (dst *RevokeProposeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RevokeProposeRequest.Merge(dst, src)
}
func (m *RevokeProposeRequest) XXX_Size() int {
	return xxx_messageInfo_RevokeProposeRequest.Size(m)
}
func (m *RevokeProposeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_RevokeProposeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_RevokeProposeRequest proto.InternalMessageInfo

func (m *RevokeProposeRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type RevokeProposeResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RevokeProposeResponse) Reset()         { *m = RevokeProposeResponse{} }
func (m *RevokeProposeResponse) String() string { return proto.CompactTextString(m) }
func (*RevokeProposeResponse) ProtoMessage()    {}
func (*RevokeProposeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{125}
}
func (m *RevokeProposeResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RevokeProposeResponse.Unmarshal(m, b)
}
func (m *RevokeProposeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RevokeProposeResponse.Marshal(b, m, deterministic)
}
func (dst *RevokeProposeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RevokeProposeResponse.Merge(dst, src)
}
func (m *RevokeProposeResponse) XXX_Size() int {
	return xxx_messageInfo_RevokeProposeResponse.Size(m)
}
func (m *RevokeProposeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_RevokeProposeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_RevokeProposeResponse proto.InternalMessageInfo

type GetChannelReserveTimeSectionConfRequest struct {
	Date                 uint32   `protobuf:"varint,1,opt,name=date,proto3" json:"date,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelReserveTimeSectionConfRequest) Reset() {
	*m = GetChannelReserveTimeSectionConfRequest{}
}
func (m *GetChannelReserveTimeSectionConfRequest) String() string { return proto.CompactTextString(m) }
func (*GetChannelReserveTimeSectionConfRequest) ProtoMessage()    {}
func (*GetChannelReserveTimeSectionConfRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{126}
}
func (m *GetChannelReserveTimeSectionConfRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelReserveTimeSectionConfRequest.Unmarshal(m, b)
}
func (m *GetChannelReserveTimeSectionConfRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelReserveTimeSectionConfRequest.Marshal(b, m, deterministic)
}
func (dst *GetChannelReserveTimeSectionConfRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelReserveTimeSectionConfRequest.Merge(dst, src)
}
func (m *GetChannelReserveTimeSectionConfRequest) XXX_Size() int {
	return xxx_messageInfo_GetChannelReserveTimeSectionConfRequest.Size(m)
}
func (m *GetChannelReserveTimeSectionConfRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelReserveTimeSectionConfRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelReserveTimeSectionConfRequest proto.InternalMessageInfo

func (m *GetChannelReserveTimeSectionConfRequest) GetDate() uint32 {
	if m != nil {
		return m.Date
	}
	return 0
}

func (m *GetChannelReserveTimeSectionConfRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetChannelReserveTimeSectionConfResponse struct {
	ReserveTimeSectionInfoList []*AdminChannelReserveTimeSectionInfo `protobuf:"bytes,1,rep,name=reserve_time_section_info_list,json=reserveTimeSectionInfoList,proto3" json:"reserve_time_section_info_list,omitempty"`
	XXX_NoUnkeyedLiteral       struct{}                              `json:"-"`
	XXX_unrecognized           []byte                                `json:"-"`
	XXX_sizecache              int32                                 `json:"-"`
}

func (m *GetChannelReserveTimeSectionConfResponse) Reset() {
	*m = GetChannelReserveTimeSectionConfResponse{}
}
func (m *GetChannelReserveTimeSectionConfResponse) String() string { return proto.CompactTextString(m) }
func (*GetChannelReserveTimeSectionConfResponse) ProtoMessage()    {}
func (*GetChannelReserveTimeSectionConfResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{127}
}
func (m *GetChannelReserveTimeSectionConfResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelReserveTimeSectionConfResponse.Unmarshal(m, b)
}
func (m *GetChannelReserveTimeSectionConfResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelReserveTimeSectionConfResponse.Marshal(b, m, deterministic)
}
func (dst *GetChannelReserveTimeSectionConfResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelReserveTimeSectionConfResponse.Merge(dst, src)
}
func (m *GetChannelReserveTimeSectionConfResponse) XXX_Size() int {
	return xxx_messageInfo_GetChannelReserveTimeSectionConfResponse.Size(m)
}
func (m *GetChannelReserveTimeSectionConfResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelReserveTimeSectionConfResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelReserveTimeSectionConfResponse proto.InternalMessageInfo

func (m *GetChannelReserveTimeSectionConfResponse) GetReserveTimeSectionInfoList() []*AdminChannelReserveTimeSectionInfo {
	if m != nil {
		return m.ReserveTimeSectionInfoList
	}
	return nil
}

type AdminChannelReserveTimeSectionInfo struct {
	ReserveSt            uint32   `protobuf:"varint,1,opt,name=reserve_st,json=reserveSt,proto3" json:"reserve_st,omitempty"`
	ReserveEt            uint32   `protobuf:"varint,2,opt,name=reserve_et,json=reserveEt,proto3" json:"reserve_et,omitempty"`
	ReserveUid           uint32   `protobuf:"varint,3,opt,name=reserve_uid,json=reserveUid,proto3" json:"reserve_uid,omitempty"`
	ReservableSwitch     uint32   `protobuf:"varint,4,opt,name=reservable_switch,json=reservableSwitch,proto3" json:"reservable_switch,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AdminChannelReserveTimeSectionInfo) Reset()         { *m = AdminChannelReserveTimeSectionInfo{} }
func (m *AdminChannelReserveTimeSectionInfo) String() string { return proto.CompactTextString(m) }
func (*AdminChannelReserveTimeSectionInfo) ProtoMessage()    {}
func (*AdminChannelReserveTimeSectionInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{128}
}
func (m *AdminChannelReserveTimeSectionInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AdminChannelReserveTimeSectionInfo.Unmarshal(m, b)
}
func (m *AdminChannelReserveTimeSectionInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AdminChannelReserveTimeSectionInfo.Marshal(b, m, deterministic)
}
func (dst *AdminChannelReserveTimeSectionInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AdminChannelReserveTimeSectionInfo.Merge(dst, src)
}
func (m *AdminChannelReserveTimeSectionInfo) XXX_Size() int {
	return xxx_messageInfo_AdminChannelReserveTimeSectionInfo.Size(m)
}
func (m *AdminChannelReserveTimeSectionInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AdminChannelReserveTimeSectionInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AdminChannelReserveTimeSectionInfo proto.InternalMessageInfo

func (m *AdminChannelReserveTimeSectionInfo) GetReserveSt() uint32 {
	if m != nil {
		return m.ReserveSt
	}
	return 0
}

func (m *AdminChannelReserveTimeSectionInfo) GetReserveEt() uint32 {
	if m != nil {
		return m.ReserveEt
	}
	return 0
}

func (m *AdminChannelReserveTimeSectionInfo) GetReserveUid() uint32 {
	if m != nil {
		return m.ReserveUid
	}
	return 0
}

func (m *AdminChannelReserveTimeSectionInfo) GetReservableSwitch() uint32 {
	if m != nil {
		return m.ReservableSwitch
	}
	return 0
}

type SetAdminChannelReserveTimeSectionSwitchRequest struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ReserveSt            uint32   `protobuf:"varint,2,opt,name=reserve_st,json=reserveSt,proto3" json:"reserve_st,omitempty"`
	ReserveEt            uint32   `protobuf:"varint,3,opt,name=reserve_et,json=reserveEt,proto3" json:"reserve_et,omitempty"`
	ReservableSwitch     uint32   `protobuf:"varint,4,opt,name=reservable_switch,json=reservableSwitch,proto3" json:"reservable_switch,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetAdminChannelReserveTimeSectionSwitchRequest) Reset() {
	*m = SetAdminChannelReserveTimeSectionSwitchRequest{}
}
func (m *SetAdminChannelReserveTimeSectionSwitchRequest) String() string {
	return proto.CompactTextString(m)
}
func (*SetAdminChannelReserveTimeSectionSwitchRequest) ProtoMessage() {}
func (*SetAdminChannelReserveTimeSectionSwitchRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{129}
}
func (m *SetAdminChannelReserveTimeSectionSwitchRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetAdminChannelReserveTimeSectionSwitchRequest.Unmarshal(m, b)
}
func (m *SetAdminChannelReserveTimeSectionSwitchRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetAdminChannelReserveTimeSectionSwitchRequest.Marshal(b, m, deterministic)
}
func (dst *SetAdminChannelReserveTimeSectionSwitchRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetAdminChannelReserveTimeSectionSwitchRequest.Merge(dst, src)
}
func (m *SetAdminChannelReserveTimeSectionSwitchRequest) XXX_Size() int {
	return xxx_messageInfo_SetAdminChannelReserveTimeSectionSwitchRequest.Size(m)
}
func (m *SetAdminChannelReserveTimeSectionSwitchRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SetAdminChannelReserveTimeSectionSwitchRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SetAdminChannelReserveTimeSectionSwitchRequest proto.InternalMessageInfo

func (m *SetAdminChannelReserveTimeSectionSwitchRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetAdminChannelReserveTimeSectionSwitchRequest) GetReserveSt() uint32 {
	if m != nil {
		return m.ReserveSt
	}
	return 0
}

func (m *SetAdminChannelReserveTimeSectionSwitchRequest) GetReserveEt() uint32 {
	if m != nil {
		return m.ReserveEt
	}
	return 0
}

func (m *SetAdminChannelReserveTimeSectionSwitchRequest) GetReservableSwitch() uint32 {
	if m != nil {
		return m.ReservableSwitch
	}
	return 0
}

type SetAdminChannelReserveTimeSectionSwitchResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetAdminChannelReserveTimeSectionSwitchResponse) Reset() {
	*m = SetAdminChannelReserveTimeSectionSwitchResponse{}
}
func (m *SetAdminChannelReserveTimeSectionSwitchResponse) String() string {
	return proto.CompactTextString(m)
}
func (*SetAdminChannelReserveTimeSectionSwitchResponse) ProtoMessage() {}
func (*SetAdminChannelReserveTimeSectionSwitchResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_plan_b56569e2f2e6a981, []int{130}
}
func (m *SetAdminChannelReserveTimeSectionSwitchResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetAdminChannelReserveTimeSectionSwitchResponse.Unmarshal(m, b)
}
func (m *SetAdminChannelReserveTimeSectionSwitchResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetAdminChannelReserveTimeSectionSwitchResponse.Marshal(b, m, deterministic)
}
func (dst *SetAdminChannelReserveTimeSectionSwitchResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetAdminChannelReserveTimeSectionSwitchResponse.Merge(dst, src)
}
func (m *SetAdminChannelReserveTimeSectionSwitchResponse) XXX_Size() int {
	return xxx_messageInfo_SetAdminChannelReserveTimeSectionSwitchResponse.Size(m)
}
func (m *SetAdminChannelReserveTimeSectionSwitchResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SetAdminChannelReserveTimeSectionSwitchResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SetAdminChannelReserveTimeSectionSwitchResponse proto.InternalMessageInfo

func init() {
	proto.RegisterType((*BuyWeddingRequest)(nil), "channel_wedding_plan.BuyWeddingRequest")
	proto.RegisterType((*BuyReserveInfo)(nil), "channel_wedding_plan.BuyReserveInfo")
	proto.RegisterType((*BuyWeddingResponse)(nil), "channel_wedding_plan.BuyWeddingResponse")
	proto.RegisterType((*GetMyWeddingReserveInfoRequest)(nil), "channel_wedding_plan.GetMyWeddingReserveInfoRequest")
	proto.RegisterType((*GetMyWeddingReserveInfoResponse)(nil), "channel_wedding_plan.GetMyWeddingReserveInfoResponse")
	proto.RegisterType((*ChannelInfo)(nil), "channel_wedding_plan.ChannelInfo")
	proto.RegisterType((*GetWeddingReserveInfoRequest)(nil), "channel_wedding_plan.GetWeddingReserveInfoRequest")
	proto.RegisterType((*GetWeddingReserveInfoResponse)(nil), "channel_wedding_plan.GetWeddingReserveInfoResponse")
	proto.RegisterType((*SaveWeddingReserveRequest)(nil), "channel_wedding_plan.SaveWeddingReserveRequest")
	proto.RegisterType((*SaveWeddingReserveInfoResponse)(nil), "channel_wedding_plan.SaveWeddingReserveInfoResponse")
	proto.RegisterType((*ChangeWeddingReserveRequest)(nil), "channel_wedding_plan.ChangeWeddingReserveRequest")
	proto.RegisterType((*ChangeWeddingReserveInfoResponse)(nil), "channel_wedding_plan.ChangeWeddingReserveInfoResponse")
	proto.RegisterType((*WeddingGuestInfo)(nil), "channel_wedding_plan.WeddingGuestInfo")
	proto.RegisterType((*GetGroomsmanAndBridesmaidInfoRequest)(nil), "channel_wedding_plan.GetGroomsmanAndBridesmaidInfoRequest")
	proto.RegisterType((*GetGroomsmanAndBridesmaidInfoResponse)(nil), "channel_wedding_plan.GetGroomsmanAndBridesmaidInfoResponse")
	proto.RegisterType((*GetWeddingFriendInfoRequest)(nil), "channel_wedding_plan.GetWeddingFriendInfoRequest")
	proto.RegisterType((*GetWeddingFriendInfoResponse)(nil), "channel_wedding_plan.GetWeddingFriendInfoResponse")
	proto.RegisterType((*InviteWeddingGuestRequest)(nil), "channel_wedding_plan.InviteWeddingGuestRequest")
	proto.RegisterType((*InviteWeddingGuestResponse)(nil), "channel_wedding_plan.InviteWeddingGuestResponse")
	proto.RegisterType((*HandleWeddingInviteRequest)(nil), "channel_wedding_plan.HandleWeddingInviteRequest")
	proto.RegisterType((*HandleWeddingInviteResponse)(nil), "channel_wedding_plan.HandleWeddingInviteResponse")
	proto.RegisterType((*DelWeddingGuestRequest)(nil), "channel_wedding_plan.DelWeddingGuestRequest")
	proto.RegisterType((*DelWeddingGuestResponse)(nil), "channel_wedding_plan.DelWeddingGuestResponse")
	proto.RegisterType((*ReserveTimeInfo)(nil), "channel_wedding_plan.ReserveTimeInfo")
	proto.RegisterType((*GetAllThemeCfgRequest)(nil), "channel_wedding_plan.GetAllThemeCfgRequest")
	proto.RegisterType((*WeddingDressPreview)(nil), "channel_wedding_plan.WeddingDressPreview")
	proto.RegisterType((*WeddingDressPreviewList)(nil), "channel_wedding_plan.WeddingDressPreviewList")
	proto.RegisterType((*FinishWeddingAward)(nil), "channel_wedding_plan.FinishWeddingAward")
	proto.RegisterType((*WeddingPriceInfo)(nil), "channel_wedding_plan.WeddingPriceInfo")
	proto.RegisterType((*ThemeCfg)(nil), "channel_wedding_plan.ThemeCfg")
	proto.RegisterMapType((map[uint32]*WeddingDressPreviewList)(nil), "channel_wedding_plan.ThemeCfg.DressPreviewMapEntry")
	proto.RegisterType((*AddThemeCfgRequest)(nil), "channel_wedding_plan.AddThemeCfgRequest")
	proto.RegisterType((*AddThemeCfgResponse)(nil), "channel_wedding_plan.AddThemeCfgResponse")
	proto.RegisterType((*UpdateThemeCfgRequest)(nil), "channel_wedding_plan.UpdateThemeCfgRequest")
	proto.RegisterType((*UpdateThemeCfgResponse)(nil), "channel_wedding_plan.UpdateThemeCfgResponse")
	proto.RegisterType((*DeleteThemeCfgRequest)(nil), "channel_wedding_plan.DeleteThemeCfgRequest")
	proto.RegisterType((*DeleteThemeCfgResponse)(nil), "channel_wedding_plan.DeleteThemeCfgResponse")
	proto.RegisterType((*WeddingAnimationInfo)(nil), "channel_wedding_plan.WeddingAnimationInfo")
	proto.RegisterType((*WeddingScenePreview)(nil), "channel_wedding_plan.WeddingScenePreview")
	proto.RegisterType((*GetAllThemeCfgResponse)(nil), "channel_wedding_plan.GetAllThemeCfgResponse")
	proto.RegisterType((*CancelWeddingRequest)(nil), "channel_wedding_plan.CancelWeddingRequest")
	proto.RegisterType((*CancelWeddingResponse)(nil), "channel_wedding_plan.CancelWeddingResponse")
	proto.RegisterType((*AdminCancelWeddingRequest)(nil), "channel_wedding_plan.AdminCancelWeddingRequest")
	proto.RegisterType((*AdminCancelWeddingResponse)(nil), "channel_wedding_plan.AdminCancelWeddingResponse")
	proto.RegisterType((*GetMyWeddingInfoRequest)(nil), "channel_wedding_plan.GetMyWeddingInfoRequest")
	proto.RegisterType((*PartnerInfo)(nil), "channel_wedding_plan.PartnerInfo")
	proto.RegisterType((*GetMyWeddingInfoResponse)(nil), "channel_wedding_plan.GetMyWeddingInfoResponse")
	proto.RegisterType((*WeddingPlanInfo)(nil), "channel_wedding_plan.WeddingPlanInfo")
	proto.RegisterType((*WeddingPlanReserveInfo)(nil), "channel_wedding_plan.WeddingPlanReserveInfo")
	proto.RegisterType((*BatGetWeddingInfoByIdRequest)(nil), "channel_wedding_plan.BatGetWeddingInfoByIdRequest")
	proto.RegisterType((*BatGetWeddingInfoByIdResponse)(nil), "channel_wedding_plan.BatGetWeddingInfoByIdResponse")
	proto.RegisterMapType((map[uint32]*WeddingPlanInfo)(nil), "channel_wedding_plan.BatGetWeddingInfoByIdResponse.WeddingInfoMapEntry")
	proto.RegisterType((*PageGetComingWeddingListRequest)(nil), "channel_wedding_plan.PageGetComingWeddingListRequest")
	proto.RegisterType((*PageGetComingWeddingListResponse)(nil), "channel_wedding_plan.PageGetComingWeddingListResponse")
	proto.RegisterType((*SubscribeWeddingRequest)(nil), "channel_wedding_plan.SubscribeWeddingRequest")
	proto.RegisterType((*SubscribeWeddingResponse)(nil), "channel_wedding_plan.SubscribeWeddingResponse")
	proto.RegisterType((*BatGetWeddingSubscribeStatusRequest)(nil), "channel_wedding_plan.BatGetWeddingSubscribeStatusRequest")
	proto.RegisterType((*BatGetWeddingSubscribeStatusResponse)(nil), "channel_wedding_plan.BatGetWeddingSubscribeStatusResponse")
	proto.RegisterMapType((map[uint32]bool)(nil), "channel_wedding_plan.BatGetWeddingSubscribeStatusResponse.SubscribeStatusMapEntry")
	proto.RegisterType((*BigScreenItem)(nil), "channel_wedding_plan.BigScreenItem")
	proto.RegisterType((*GetWeddingBigScreenRequest)(nil), "channel_wedding_plan.GetWeddingBigScreenRequest")
	proto.RegisterType((*GetWeddingBigScreenResponse)(nil), "channel_wedding_plan.GetWeddingBigScreenResponse")
	proto.RegisterType((*SaveWeddingBigScreenRequest)(nil), "channel_wedding_plan.SaveWeddingBigScreenRequest")
	proto.RegisterType((*SaveWeddingBigScreenResponse)(nil), "channel_wedding_plan.SaveWeddingBigScreenResponse")
	proto.RegisterType((*UpdateWeddingBigScreenReviewStatusRequest)(nil), "channel_wedding_plan.UpdateWeddingBigScreenReviewStatusRequest")
	proto.RegisterType((*UpdateWeddingBigScreenReviewStatusResponse)(nil), "channel_wedding_plan.UpdateWeddingBigScreenReviewStatusResponse")
	proto.RegisterType((*ApplyEndWeddingRelationRequest)(nil), "channel_wedding_plan.ApplyEndWeddingRelationRequest")
	proto.RegisterType((*ApplyEndWeddingRelationResponse)(nil), "channel_wedding_plan.ApplyEndWeddingRelationResponse")
	proto.RegisterType((*CancelEndWeddingRelationRequest)(nil), "channel_wedding_plan.CancelEndWeddingRelationRequest")
	proto.RegisterType((*CancelEndWeddingRelationResponse)(nil), "channel_wedding_plan.CancelEndWeddingRelationResponse")
	proto.RegisterType((*GetReservedWeddingRequest)(nil), "channel_wedding_plan.GetReservedWeddingRequest")
	proto.RegisterType((*GetReservedWeddingResponse)(nil), "channel_wedding_plan.GetReservedWeddingResponse")
	proto.RegisterType((*ReservedWeddingInfo)(nil), "channel_wedding_plan.ReservedWeddingInfo")
	proto.RegisterType((*SetWeddingHostRequest)(nil), "channel_wedding_plan.SetWeddingHostRequest")
	proto.RegisterType((*SetWeddingHostResponse)(nil), "channel_wedding_plan.SetWeddingHostResponse")
	proto.RegisterType((*GetWeddingPlanBaseInfoRequest)(nil), "channel_wedding_plan.GetWeddingPlanBaseInfoRequest")
	proto.RegisterType((*GetWeddingPlanBaseInfoResponse)(nil), "channel_wedding_plan.GetWeddingPlanBaseInfoResponse")
	proto.RegisterType((*GetThemeCfgByIdRequest)(nil), "channel_wedding_plan.GetThemeCfgByIdRequest")
	proto.RegisterType((*GetThemeCfgByIdResponse)(nil), "channel_wedding_plan.GetThemeCfgByIdResponse")
	proto.RegisterType((*GetProposeListRequest)(nil), "channel_wedding_plan.GetProposeListRequest")
	proto.RegisterType((*ProposeUser)(nil), "channel_wedding_plan.ProposeUser")
	proto.RegisterType((*GetProposeListResponse)(nil), "channel_wedding_plan.GetProposeListResponse")
	proto.RegisterType((*SendProposeRequest)(nil), "channel_wedding_plan.SendProposeRequest")
	proto.RegisterType((*SendProposeResponse)(nil), "channel_wedding_plan.SendProposeResponse")
	proto.RegisterType((*HandleProposeRequest)(nil), "channel_wedding_plan.HandleProposeRequest")
	proto.RegisterType((*HandleProposeResponse)(nil), "channel_wedding_plan.HandleProposeResponse")
	proto.RegisterType((*WeddingProposeInfo)(nil), "channel_wedding_plan.WeddingProposeInfo")
	proto.RegisterType((*GetProposeByIdRequest)(nil), "channel_wedding_plan.GetProposeByIdRequest")
	proto.RegisterType((*GetProposeByIdResponse)(nil), "channel_wedding_plan.GetProposeByIdResponse")
	proto.RegisterType((*GetSendProposeRequest)(nil), "channel_wedding_plan.GetSendProposeRequest")
	proto.RegisterType((*GetSendProposeResponse)(nil), "channel_wedding_plan.GetSendProposeResponse")
	proto.RegisterType((*BatchGetMarriageInfoRequest)(nil), "channel_wedding_plan.BatchGetMarriageInfoRequest")
	proto.RegisterType((*BatchGetMarriageInfoResponse)(nil), "channel_wedding_plan.BatchGetMarriageInfoResponse")
	proto.RegisterMapType((map[uint32]uint32)(nil), "channel_wedding_plan.BatchGetMarriageInfoResponse.MarriageInfoMapEntry")
	proto.RegisterType((*GetSimpleWeddingPlanInfoRequest)(nil), "channel_wedding_plan.GetSimpleWeddingPlanInfoRequest")
	proto.RegisterType((*GetSimpleWeddingPlanInfoResponse)(nil), "channel_wedding_plan.GetSimpleWeddingPlanInfoResponse")
	proto.RegisterType((*BatchHotWeddingPlanRequest)(nil), "channel_wedding_plan.BatchHotWeddingPlanRequest")
	proto.RegisterType((*BatchHotWeddingPlanResponse)(nil), "channel_wedding_plan.BatchHotWeddingPlanResponse")
	proto.RegisterMapType((map[uint32]bool)(nil), "channel_wedding_plan.BatchHotWeddingPlanResponse.HotWeddingMapEntry")
	proto.RegisterType((*InviteCard)(nil), "channel_wedding_plan.InviteCard")
	proto.RegisterType((*WeddingGuestGift)(nil), "channel_wedding_plan.WeddingGuestGift")
	proto.RegisterType((*GetWeddingInviteInfoRequest)(nil), "channel_wedding_plan.GetWeddingInviteInfoRequest")
	proto.RegisterType((*GetWeddingInviteInfoResponse)(nil), "channel_wedding_plan.GetWeddingInviteInfoResponse")
	proto.RegisterType((*SetUserRelationHideStatusRequest)(nil), "channel_wedding_plan.SetUserRelationHideStatusRequest")
	proto.RegisterType((*SetUserRelationHideStatusResponse)(nil), "channel_wedding_plan.SetUserRelationHideStatusResponse")
	proto.RegisterType((*DivorceRequest)(nil), "channel_wedding_plan.DivorceRequest")
	proto.RegisterType((*DivorceResponse)(nil), "channel_wedding_plan.DivorceResponse")
	proto.RegisterType((*GetUserDivorceStatusRequest)(nil), "channel_wedding_plan.GetUserDivorceStatusRequest")
	proto.RegisterType((*GetUserDivorceStatusResponse)(nil), "channel_wedding_plan.GetUserDivorceStatusResponse")
	proto.RegisterType((*MarriageRelationInfo)(nil), "channel_wedding_plan.MarriageRelationInfo")
	proto.RegisterType((*GetMarriageStatusRequest)(nil), "channel_wedding_plan.GetMarriageStatusRequest")
	proto.RegisterType((*GetMarriageStatusResponse)(nil), "channel_wedding_plan.GetMarriageStatusResponse")
	proto.RegisterType((*ManualNotifyWeddingStartRequest)(nil), "channel_wedding_plan.ManualNotifyWeddingStartRequest")
	proto.RegisterType((*ManualNotifyWeddingStartResponse)(nil), "channel_wedding_plan.ManualNotifyWeddingStartResponse")
	proto.RegisterType((*UpdateWeddingPlanStatusRequest)(nil), "channel_wedding_plan.UpdateWeddingPlanStatusRequest")
	proto.RegisterType((*UpdateWeddingPlanStatusResponse)(nil), "channel_wedding_plan.UpdateWeddingPlanStatusResponse")
	proto.RegisterType((*TestWeddingAnniversaryPopupRequest)(nil), "channel_wedding_plan.TestWeddingAnniversaryPopupRequest")
	proto.RegisterType((*TestWeddingAnniversaryPopupResponse)(nil), "channel_wedding_plan.TestWeddingAnniversaryPopupResponse")
	proto.RegisterType((*GetMyWeddingRoleRequest)(nil), "channel_wedding_plan.GetMyWeddingRoleRequest")
	proto.RegisterType((*GetMyWeddingRoleResponse)(nil), "channel_wedding_plan.GetMyWeddingRoleResponse")
	proto.RegisterType((*BatchGetWeddingRoleRequest)(nil), "channel_wedding_plan.BatchGetWeddingRoleRequest")
	proto.RegisterType((*BatchGetWeddingRoleResponse)(nil), "channel_wedding_plan.BatchGetWeddingRoleResponse")
	proto.RegisterMapType((map[uint32]uint32)(nil), "channel_wedding_plan.BatchGetWeddingRoleResponse.WeddingRoleMapEntry")
	proto.RegisterType((*GetChannelReservedInfoRequest)(nil), "channel_wedding_plan.GetChannelReservedInfoRequest")
	proto.RegisterType((*GetChannelReservedInfoResponse)(nil), "channel_wedding_plan.GetChannelReservedInfoResponse")
	proto.RegisterType((*ChannelReserveTimeInfo)(nil), "channel_wedding_plan.ChannelReserveTimeInfo")
	proto.RegisterType((*ConsultWeddingReserveRequest)(nil), "channel_wedding_plan.ConsultWeddingReserveRequest")
	proto.RegisterType((*ConsultWeddingReserveResponse)(nil), "channel_wedding_plan.ConsultWeddingReserveResponse")
	proto.RegisterType((*ArrangeWeddingReserveRequest)(nil), "channel_wedding_plan.ArrangeWeddingReserveRequest")
	proto.RegisterType((*ArrangeWeddingReserveResponse)(nil), "channel_wedding_plan.ArrangeWeddingReserveResponse")
	proto.RegisterType((*RevokeProposeRequest)(nil), "channel_wedding_plan.RevokeProposeRequest")
	proto.RegisterType((*RevokeProposeResponse)(nil), "channel_wedding_plan.RevokeProposeResponse")
	proto.RegisterType((*GetChannelReserveTimeSectionConfRequest)(nil), "channel_wedding_plan.GetChannelReserveTimeSectionConfRequest")
	proto.RegisterType((*GetChannelReserveTimeSectionConfResponse)(nil), "channel_wedding_plan.GetChannelReserveTimeSectionConfResponse")
	proto.RegisterType((*AdminChannelReserveTimeSectionInfo)(nil), "channel_wedding_plan.AdminChannelReserveTimeSectionInfo")
	proto.RegisterType((*SetAdminChannelReserveTimeSectionSwitchRequest)(nil), "channel_wedding_plan.SetAdminChannelReserveTimeSectionSwitchRequest")
	proto.RegisterType((*SetAdminChannelReserveTimeSectionSwitchResponse)(nil), "channel_wedding_plan.SetAdminChannelReserveTimeSectionSwitchResponse")
	proto.RegisterEnum("channel_wedding_plan.ThemeType", ThemeType_name, ThemeType_value)
	proto.RegisterEnum("channel_wedding_plan.WeddingInviteStatus", WeddingInviteStatus_name, WeddingInviteStatus_value)
	proto.RegisterEnum("channel_wedding_plan.WeddingPlanStatus", WeddingPlanStatus_name, WeddingPlanStatus_value)
	proto.RegisterEnum("channel_wedding_plan.ReviewStatus", ReviewStatus_name, ReviewStatus_value)
	proto.RegisterEnum("channel_wedding_plan.WeddingReserveDataType", WeddingReserveDataType_name, WeddingReserveDataType_value)
	proto.RegisterEnum("channel_wedding_plan.HideOpType", HideOpType_name, HideOpType_value)
	proto.RegisterEnum("channel_wedding_plan.AdminReservableSwitch", AdminReservableSwitch_name, AdminReservableSwitch_value)
	proto.RegisterEnum("channel_wedding_plan.TestWeddingAnniversaryPopupRequest_OpType", TestWeddingAnniversaryPopupRequest_OpType_name, TestWeddingAnniversaryPopupRequest_OpType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ChannelWeddingPlanClient is the client API for ChannelWeddingPlan service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChannelWeddingPlanClient interface {
	// 购买婚礼
	BuyWedding(ctx context.Context, in *BuyWeddingRequest, opts ...grpc.CallOption) (*BuyWeddingResponse, error)
	// 取消婚礼
	CancelWedding(ctx context.Context, in *CancelWeddingRequest, opts ...grpc.CallOption) (*CancelWeddingResponse, error)
	// 获取我的婚礼信息
	GetMyWeddingInfo(ctx context.Context, in *GetMyWeddingInfoRequest, opts ...grpc.CallOption) (*GetMyWeddingInfoResponse, error)
	// 获取所有主题配置
	GetAllThemeCfg(ctx context.Context, in *GetAllThemeCfgRequest, opts ...grpc.CallOption) (*GetAllThemeCfgResponse, error)
	// 添加主题配置
	AddThemeCfg(ctx context.Context, in *AddThemeCfgRequest, opts ...grpc.CallOption) (*AddThemeCfgResponse, error)
	// 更新主题配置
	UpdateThemeCfg(ctx context.Context, in *UpdateThemeCfgRequest, opts ...grpc.CallOption) (*UpdateThemeCfgResponse, error)
	// 删除主题配置
	DeleteThemeCfg(ctx context.Context, in *DeleteThemeCfgRequest, opts ...grpc.CallOption) (*DeleteThemeCfgResponse, error)
	// 根据themeId获取Cfg
	GetThemeCfgById(ctx context.Context, in *GetThemeCfgByIdRequest, opts ...grpc.CallOption) (*GetThemeCfgByIdResponse, error)
	// 获取我的预约信息
	GetMyWeddingReserveInfo(ctx context.Context, in *GetMyWeddingReserveInfoRequest, opts ...grpc.CallOption) (*GetMyWeddingReserveInfoResponse, error)
	// 获取选中日期/房间的预约信息
	GetWeddingReserveInfo(ctx context.Context, in *GetWeddingReserveInfoRequest, opts ...grpc.CallOption) (*GetWeddingReserveInfoResponse, error)
	// 保存预约信息
	SaveWeddingReserveInfo(ctx context.Context, in *SaveWeddingReserveRequest, opts ...grpc.CallOption) (*SaveWeddingReserveInfoResponse, error)
	// 修改预约信息
	ChangeWeddingReserveInfo(ctx context.Context, in *ChangeWeddingReserveRequest, opts ...grpc.CallOption) (*ChangeWeddingReserveInfoResponse, error)
	// 获取伴郎伴娘信息
	GetGroomsmanAndBridesmaidInfo(ctx context.Context, in *GetGroomsmanAndBridesmaidInfoRequest, opts ...grpc.CallOption) (*GetGroomsmanAndBridesmaidInfoResponse, error)
	// 获取亲友团信息
	GetWeddingFriendInfo(ctx context.Context, in *GetWeddingFriendInfoRequest, opts ...grpc.CallOption) (*GetWeddingFriendInfoResponse, error)
	// 邀请嘉宾
	InviteWeddingGuest(ctx context.Context, in *InviteWeddingGuestRequest, opts ...grpc.CallOption) (*InviteWeddingGuestResponse, error)
	// 处理婚礼邀请
	HandleWeddingInvite(ctx context.Context, in *HandleWeddingInviteRequest, opts ...grpc.CallOption) (*HandleWeddingInviteResponse, error)
	// 删除嘉宾
	DelWeddingGuest(ctx context.Context, in *DelWeddingGuestRequest, opts ...grpc.CallOption) (*DelWeddingGuestResponse, error)
	// 查询预约列表
	GetReservedWedding(ctx context.Context, in *GetReservedWeddingRequest, opts ...grpc.CallOption) (*GetReservedWeddingResponse, error)
	// 设置主持人
	SetWeddingHost(ctx context.Context, in *SetWeddingHostRequest, opts ...grpc.CallOption) (*SetWeddingHostResponse, error)
	// 获取婚礼基础信息
	GetWeddingPlanBaseInfo(ctx context.Context, in *GetWeddingPlanBaseInfoRequest, opts ...grpc.CallOption) (*GetWeddingPlanBaseInfoResponse, error)
	// 获取简单婚礼信息
	GetSimpleWeddingPlanInfo(ctx context.Context, in *GetSimpleWeddingPlanInfoRequest, opts ...grpc.CallOption) (*GetSimpleWeddingPlanInfoResponse, error)
	// 获取求婚函信息
	GetWeddingInviteInfo(ctx context.Context, in *GetWeddingInviteInfoRequest, opts ...grpc.CallOption) (*GetWeddingInviteInfoResponse, error)
	// 批量获取婚礼信息
	BatGetWeddingInfoById(ctx context.Context, in *BatGetWeddingInfoByIdRequest, opts ...grpc.CallOption) (*BatGetWeddingInfoByIdResponse, error)
	// 分页获取未开始的婚礼列表
	PageGetComingWeddingList(ctx context.Context, in *PageGetComingWeddingListRequest, opts ...grpc.CallOption) (*PageGetComingWeddingListResponse, error)
	// 订阅婚礼
	SubscribeWedding(ctx context.Context, in *SubscribeWeddingRequest, opts ...grpc.CallOption) (*SubscribeWeddingResponse, error)
	// 批量获取婚礼订阅状态
	BatGetWeddingSubscribeStatus(ctx context.Context, in *BatGetWeddingSubscribeStatusRequest, opts ...grpc.CallOption) (*BatGetWeddingSubscribeStatusResponse, error)
	// 获取婚礼大屏
	GetWeddingBigScreen(ctx context.Context, in *GetWeddingBigScreenRequest, opts ...grpc.CallOption) (*GetWeddingBigScreenResponse, error)
	// 保存婚礼大屏
	SaveWeddingBigScreen(ctx context.Context, in *SaveWeddingBigScreenRequest, opts ...grpc.CallOption) (*SaveWeddingBigScreenResponse, error)
	// 更新婚礼大屏审核状态
	UpdateWeddingBigScreenReviewStatus(ctx context.Context, in *UpdateWeddingBigScreenReviewStatusRequest, opts ...grpc.CallOption) (*UpdateWeddingBigScreenReviewStatusResponse, error)
	// 申请结束婚礼关系
	ApplyEndWeddingRelation(ctx context.Context, in *ApplyEndWeddingRelationRequest, opts ...grpc.CallOption) (*ApplyEndWeddingRelationResponse, error)
	// 取消结束婚礼关系
	CancelEndWeddingRelation(ctx context.Context, in *CancelEndWeddingRelationRequest, opts ...grpc.CallOption) (*CancelEndWeddingRelationResponse, error)
	// 获取求婚列表
	GetProposeList(ctx context.Context, in *GetProposeListRequest, opts ...grpc.CallOption) (*GetProposeListResponse, error)
	// 发送求婚
	SendPropose(ctx context.Context, in *SendProposeRequest, opts ...grpc.CallOption) (*SendProposeResponse, error)
	// RevokePropose 撤销求婚
	RevokePropose(ctx context.Context, in *RevokeProposeRequest, opts ...grpc.CallOption) (*RevokeProposeResponse, error)
	// 处理求婚请求
	HandlePropose(ctx context.Context, in *HandleProposeRequest, opts ...grpc.CallOption) (*HandleProposeResponse, error)
	// 获取求婚函信息
	GetProposeById(ctx context.Context, in *GetProposeByIdRequest, opts ...grpc.CallOption) (*GetProposeByIdResponse, error)
	// 获取我发出的求婚函
	GetSendPropose(ctx context.Context, in *GetSendProposeRequest, opts ...grpc.CallOption) (*GetSendProposeResponse, error)
	// 批量获取结婚状态
	BatchGetMarriageInfo(ctx context.Context, in *BatchGetMarriageInfoRequest, opts ...grpc.CallOption) (*BatchGetMarriageInfoResponse, error)
	// 设置用户关系隐藏开关状态
	SetUserRelationHideStatus(ctx context.Context, in *SetUserRelationHideStatusRequest, opts ...grpc.CallOption) (*SetUserRelationHideStatusResponse, error)
	// 离婚
	Divorce(ctx context.Context, in *DivorceRequest, opts ...grpc.CallOption) (*DivorceResponse, error)
	// 获取离婚状态
	GetUserDivorceStatus(ctx context.Context, in *GetUserDivorceStatusRequest, opts ...grpc.CallOption) (*GetUserDivorceStatusResponse, error)
	// 获取婚姻关系状态
	GetMarriageStatus(ctx context.Context, in *GetMarriageStatusRequest, opts ...grpc.CallOption) (*GetMarriageStatusResponse, error)
	// 手动通知婚礼开始
	ManualNotifyWeddingStart(ctx context.Context, in *ManualNotifyWeddingStartRequest, opts ...grpc.CallOption) (*ManualNotifyWeddingStartResponse, error)
	// 更新婚礼计划状态
	UpdateWeddingPlanStatus(ctx context.Context, in *UpdateWeddingPlanStatusRequest, opts ...grpc.CallOption) (*UpdateWeddingPlanStatusResponse, error)
	// 手动测试婚礼纪念弹窗
	TestWeddingAnniversaryPopup(ctx context.Context, in *TestWeddingAnniversaryPopupRequest, opts ...grpc.CallOption) (*TestWeddingAnniversaryPopupResponse, error)
	// 获取婚礼角色
	GetMyWeddingRole(ctx context.Context, in *GetMyWeddingRoleRequest, opts ...grpc.CallOption) (*GetMyWeddingRoleResponse, error)
	//  批量获取婚礼角色
	BatchGetWeddingRole(ctx context.Context, in *BatchGetWeddingRoleRequest, opts ...grpc.CallOption) (*BatchGetWeddingRoleResponse, error)
	// 获取房间内预约信息
	GetChannelReservedInfo(ctx context.Context, in *GetChannelReservedInfoRequest, opts ...grpc.CallOption) (*GetChannelReservedInfoResponse, error)
	// 咨询预约婚礼
	ConsultWeddingReserve(ctx context.Context, in *ConsultWeddingReserveRequest, opts ...grpc.CallOption) (*ConsultWeddingReserveResponse, error)
	// 客服安排婚礼预约
	ArrangeWeddingReserve(ctx context.Context, in *ArrangeWeddingReserveRequest, opts ...grpc.CallOption) (*ArrangeWeddingReserveResponse, error)
	// 获取热门婚礼
	BatchHotWeddingPlan(ctx context.Context, in *BatchHotWeddingPlanRequest, opts ...grpc.CallOption) (*BatchHotWeddingPlanResponse, error)
	// T豆对账
	GetTBeanTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error)
	GetTBeanOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error)
	AdminCancelWedding(ctx context.Context, in *AdminCancelWeddingRequest, opts ...grpc.CallOption) (*AdminCancelWeddingResponse, error)
	// 获取公会后台房间预约时段配置
	GetChannelReserveTimeSectionConf(ctx context.Context, in *GetChannelReserveTimeSectionConfRequest, opts ...grpc.CallOption) (*GetChannelReserveTimeSectionConfResponse, error)
	// 设置公会后台房间预约时段开关
	SetAdminChannelReserveTimeSectionSwitch(ctx context.Context, in *SetAdminChannelReserveTimeSectionSwitchRequest, opts ...grpc.CallOption) (*SetAdminChannelReserveTimeSectionSwitchResponse, error)
}

type channelWeddingPlanClient struct {
	cc *grpc.ClientConn
}

func NewChannelWeddingPlanClient(cc *grpc.ClientConn) ChannelWeddingPlanClient {
	return &channelWeddingPlanClient{cc}
}

func (c *channelWeddingPlanClient) BuyWedding(ctx context.Context, in *BuyWeddingRequest, opts ...grpc.CallOption) (*BuyWeddingResponse, error) {
	out := new(BuyWeddingResponse)
	err := c.cc.Invoke(ctx, "/channel_wedding_plan.ChannelWeddingPlan/BuyWedding", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingPlanClient) CancelWedding(ctx context.Context, in *CancelWeddingRequest, opts ...grpc.CallOption) (*CancelWeddingResponse, error) {
	out := new(CancelWeddingResponse)
	err := c.cc.Invoke(ctx, "/channel_wedding_plan.ChannelWeddingPlan/CancelWedding", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingPlanClient) GetMyWeddingInfo(ctx context.Context, in *GetMyWeddingInfoRequest, opts ...grpc.CallOption) (*GetMyWeddingInfoResponse, error) {
	out := new(GetMyWeddingInfoResponse)
	err := c.cc.Invoke(ctx, "/channel_wedding_plan.ChannelWeddingPlan/GetMyWeddingInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingPlanClient) GetAllThemeCfg(ctx context.Context, in *GetAllThemeCfgRequest, opts ...grpc.CallOption) (*GetAllThemeCfgResponse, error) {
	out := new(GetAllThemeCfgResponse)
	err := c.cc.Invoke(ctx, "/channel_wedding_plan.ChannelWeddingPlan/GetAllThemeCfg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingPlanClient) AddThemeCfg(ctx context.Context, in *AddThemeCfgRequest, opts ...grpc.CallOption) (*AddThemeCfgResponse, error) {
	out := new(AddThemeCfgResponse)
	err := c.cc.Invoke(ctx, "/channel_wedding_plan.ChannelWeddingPlan/AddThemeCfg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingPlanClient) UpdateThemeCfg(ctx context.Context, in *UpdateThemeCfgRequest, opts ...grpc.CallOption) (*UpdateThemeCfgResponse, error) {
	out := new(UpdateThemeCfgResponse)
	err := c.cc.Invoke(ctx, "/channel_wedding_plan.ChannelWeddingPlan/UpdateThemeCfg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingPlanClient) DeleteThemeCfg(ctx context.Context, in *DeleteThemeCfgRequest, opts ...grpc.CallOption) (*DeleteThemeCfgResponse, error) {
	out := new(DeleteThemeCfgResponse)
	err := c.cc.Invoke(ctx, "/channel_wedding_plan.ChannelWeddingPlan/DeleteThemeCfg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingPlanClient) GetThemeCfgById(ctx context.Context, in *GetThemeCfgByIdRequest, opts ...grpc.CallOption) (*GetThemeCfgByIdResponse, error) {
	out := new(GetThemeCfgByIdResponse)
	err := c.cc.Invoke(ctx, "/channel_wedding_plan.ChannelWeddingPlan/GetThemeCfgById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingPlanClient) GetMyWeddingReserveInfo(ctx context.Context, in *GetMyWeddingReserveInfoRequest, opts ...grpc.CallOption) (*GetMyWeddingReserveInfoResponse, error) {
	out := new(GetMyWeddingReserveInfoResponse)
	err := c.cc.Invoke(ctx, "/channel_wedding_plan.ChannelWeddingPlan/GetMyWeddingReserveInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingPlanClient) GetWeddingReserveInfo(ctx context.Context, in *GetWeddingReserveInfoRequest, opts ...grpc.CallOption) (*GetWeddingReserveInfoResponse, error) {
	out := new(GetWeddingReserveInfoResponse)
	err := c.cc.Invoke(ctx, "/channel_wedding_plan.ChannelWeddingPlan/GetWeddingReserveInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingPlanClient) SaveWeddingReserveInfo(ctx context.Context, in *SaveWeddingReserveRequest, opts ...grpc.CallOption) (*SaveWeddingReserveInfoResponse, error) {
	out := new(SaveWeddingReserveInfoResponse)
	err := c.cc.Invoke(ctx, "/channel_wedding_plan.ChannelWeddingPlan/SaveWeddingReserveInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingPlanClient) ChangeWeddingReserveInfo(ctx context.Context, in *ChangeWeddingReserveRequest, opts ...grpc.CallOption) (*ChangeWeddingReserveInfoResponse, error) {
	out := new(ChangeWeddingReserveInfoResponse)
	err := c.cc.Invoke(ctx, "/channel_wedding_plan.ChannelWeddingPlan/ChangeWeddingReserveInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingPlanClient) GetGroomsmanAndBridesmaidInfo(ctx context.Context, in *GetGroomsmanAndBridesmaidInfoRequest, opts ...grpc.CallOption) (*GetGroomsmanAndBridesmaidInfoResponse, error) {
	out := new(GetGroomsmanAndBridesmaidInfoResponse)
	err := c.cc.Invoke(ctx, "/channel_wedding_plan.ChannelWeddingPlan/GetGroomsmanAndBridesmaidInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingPlanClient) GetWeddingFriendInfo(ctx context.Context, in *GetWeddingFriendInfoRequest, opts ...grpc.CallOption) (*GetWeddingFriendInfoResponse, error) {
	out := new(GetWeddingFriendInfoResponse)
	err := c.cc.Invoke(ctx, "/channel_wedding_plan.ChannelWeddingPlan/GetWeddingFriendInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingPlanClient) InviteWeddingGuest(ctx context.Context, in *InviteWeddingGuestRequest, opts ...grpc.CallOption) (*InviteWeddingGuestResponse, error) {
	out := new(InviteWeddingGuestResponse)
	err := c.cc.Invoke(ctx, "/channel_wedding_plan.ChannelWeddingPlan/InviteWeddingGuest", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingPlanClient) HandleWeddingInvite(ctx context.Context, in *HandleWeddingInviteRequest, opts ...grpc.CallOption) (*HandleWeddingInviteResponse, error) {
	out := new(HandleWeddingInviteResponse)
	err := c.cc.Invoke(ctx, "/channel_wedding_plan.ChannelWeddingPlan/HandleWeddingInvite", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingPlanClient) DelWeddingGuest(ctx context.Context, in *DelWeddingGuestRequest, opts ...grpc.CallOption) (*DelWeddingGuestResponse, error) {
	out := new(DelWeddingGuestResponse)
	err := c.cc.Invoke(ctx, "/channel_wedding_plan.ChannelWeddingPlan/DelWeddingGuest", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingPlanClient) GetReservedWedding(ctx context.Context, in *GetReservedWeddingRequest, opts ...grpc.CallOption) (*GetReservedWeddingResponse, error) {
	out := new(GetReservedWeddingResponse)
	err := c.cc.Invoke(ctx, "/channel_wedding_plan.ChannelWeddingPlan/GetReservedWedding", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingPlanClient) SetWeddingHost(ctx context.Context, in *SetWeddingHostRequest, opts ...grpc.CallOption) (*SetWeddingHostResponse, error) {
	out := new(SetWeddingHostResponse)
	err := c.cc.Invoke(ctx, "/channel_wedding_plan.ChannelWeddingPlan/SetWeddingHost", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingPlanClient) GetWeddingPlanBaseInfo(ctx context.Context, in *GetWeddingPlanBaseInfoRequest, opts ...grpc.CallOption) (*GetWeddingPlanBaseInfoResponse, error) {
	out := new(GetWeddingPlanBaseInfoResponse)
	err := c.cc.Invoke(ctx, "/channel_wedding_plan.ChannelWeddingPlan/GetWeddingPlanBaseInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingPlanClient) GetSimpleWeddingPlanInfo(ctx context.Context, in *GetSimpleWeddingPlanInfoRequest, opts ...grpc.CallOption) (*GetSimpleWeddingPlanInfoResponse, error) {
	out := new(GetSimpleWeddingPlanInfoResponse)
	err := c.cc.Invoke(ctx, "/channel_wedding_plan.ChannelWeddingPlan/GetSimpleWeddingPlanInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingPlanClient) GetWeddingInviteInfo(ctx context.Context, in *GetWeddingInviteInfoRequest, opts ...grpc.CallOption) (*GetWeddingInviteInfoResponse, error) {
	out := new(GetWeddingInviteInfoResponse)
	err := c.cc.Invoke(ctx, "/channel_wedding_plan.ChannelWeddingPlan/GetWeddingInviteInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingPlanClient) BatGetWeddingInfoById(ctx context.Context, in *BatGetWeddingInfoByIdRequest, opts ...grpc.CallOption) (*BatGetWeddingInfoByIdResponse, error) {
	out := new(BatGetWeddingInfoByIdResponse)
	err := c.cc.Invoke(ctx, "/channel_wedding_plan.ChannelWeddingPlan/BatGetWeddingInfoById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingPlanClient) PageGetComingWeddingList(ctx context.Context, in *PageGetComingWeddingListRequest, opts ...grpc.CallOption) (*PageGetComingWeddingListResponse, error) {
	out := new(PageGetComingWeddingListResponse)
	err := c.cc.Invoke(ctx, "/channel_wedding_plan.ChannelWeddingPlan/PageGetComingWeddingList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingPlanClient) SubscribeWedding(ctx context.Context, in *SubscribeWeddingRequest, opts ...grpc.CallOption) (*SubscribeWeddingResponse, error) {
	out := new(SubscribeWeddingResponse)
	err := c.cc.Invoke(ctx, "/channel_wedding_plan.ChannelWeddingPlan/SubscribeWedding", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingPlanClient) BatGetWeddingSubscribeStatus(ctx context.Context, in *BatGetWeddingSubscribeStatusRequest, opts ...grpc.CallOption) (*BatGetWeddingSubscribeStatusResponse, error) {
	out := new(BatGetWeddingSubscribeStatusResponse)
	err := c.cc.Invoke(ctx, "/channel_wedding_plan.ChannelWeddingPlan/BatGetWeddingSubscribeStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingPlanClient) GetWeddingBigScreen(ctx context.Context, in *GetWeddingBigScreenRequest, opts ...grpc.CallOption) (*GetWeddingBigScreenResponse, error) {
	out := new(GetWeddingBigScreenResponse)
	err := c.cc.Invoke(ctx, "/channel_wedding_plan.ChannelWeddingPlan/GetWeddingBigScreen", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingPlanClient) SaveWeddingBigScreen(ctx context.Context, in *SaveWeddingBigScreenRequest, opts ...grpc.CallOption) (*SaveWeddingBigScreenResponse, error) {
	out := new(SaveWeddingBigScreenResponse)
	err := c.cc.Invoke(ctx, "/channel_wedding_plan.ChannelWeddingPlan/SaveWeddingBigScreen", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingPlanClient) UpdateWeddingBigScreenReviewStatus(ctx context.Context, in *UpdateWeddingBigScreenReviewStatusRequest, opts ...grpc.CallOption) (*UpdateWeddingBigScreenReviewStatusResponse, error) {
	out := new(UpdateWeddingBigScreenReviewStatusResponse)
	err := c.cc.Invoke(ctx, "/channel_wedding_plan.ChannelWeddingPlan/UpdateWeddingBigScreenReviewStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingPlanClient) ApplyEndWeddingRelation(ctx context.Context, in *ApplyEndWeddingRelationRequest, opts ...grpc.CallOption) (*ApplyEndWeddingRelationResponse, error) {
	out := new(ApplyEndWeddingRelationResponse)
	err := c.cc.Invoke(ctx, "/channel_wedding_plan.ChannelWeddingPlan/ApplyEndWeddingRelation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingPlanClient) CancelEndWeddingRelation(ctx context.Context, in *CancelEndWeddingRelationRequest, opts ...grpc.CallOption) (*CancelEndWeddingRelationResponse, error) {
	out := new(CancelEndWeddingRelationResponse)
	err := c.cc.Invoke(ctx, "/channel_wedding_plan.ChannelWeddingPlan/CancelEndWeddingRelation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingPlanClient) GetProposeList(ctx context.Context, in *GetProposeListRequest, opts ...grpc.CallOption) (*GetProposeListResponse, error) {
	out := new(GetProposeListResponse)
	err := c.cc.Invoke(ctx, "/channel_wedding_plan.ChannelWeddingPlan/GetProposeList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingPlanClient) SendPropose(ctx context.Context, in *SendProposeRequest, opts ...grpc.CallOption) (*SendProposeResponse, error) {
	out := new(SendProposeResponse)
	err := c.cc.Invoke(ctx, "/channel_wedding_plan.ChannelWeddingPlan/SendPropose", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingPlanClient) RevokePropose(ctx context.Context, in *RevokeProposeRequest, opts ...grpc.CallOption) (*RevokeProposeResponse, error) {
	out := new(RevokeProposeResponse)
	err := c.cc.Invoke(ctx, "/channel_wedding_plan.ChannelWeddingPlan/RevokePropose", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingPlanClient) HandlePropose(ctx context.Context, in *HandleProposeRequest, opts ...grpc.CallOption) (*HandleProposeResponse, error) {
	out := new(HandleProposeResponse)
	err := c.cc.Invoke(ctx, "/channel_wedding_plan.ChannelWeddingPlan/HandlePropose", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingPlanClient) GetProposeById(ctx context.Context, in *GetProposeByIdRequest, opts ...grpc.CallOption) (*GetProposeByIdResponse, error) {
	out := new(GetProposeByIdResponse)
	err := c.cc.Invoke(ctx, "/channel_wedding_plan.ChannelWeddingPlan/GetProposeById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingPlanClient) GetSendPropose(ctx context.Context, in *GetSendProposeRequest, opts ...grpc.CallOption) (*GetSendProposeResponse, error) {
	out := new(GetSendProposeResponse)
	err := c.cc.Invoke(ctx, "/channel_wedding_plan.ChannelWeddingPlan/GetSendPropose", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingPlanClient) BatchGetMarriageInfo(ctx context.Context, in *BatchGetMarriageInfoRequest, opts ...grpc.CallOption) (*BatchGetMarriageInfoResponse, error) {
	out := new(BatchGetMarriageInfoResponse)
	err := c.cc.Invoke(ctx, "/channel_wedding_plan.ChannelWeddingPlan/BatchGetMarriageInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingPlanClient) SetUserRelationHideStatus(ctx context.Context, in *SetUserRelationHideStatusRequest, opts ...grpc.CallOption) (*SetUserRelationHideStatusResponse, error) {
	out := new(SetUserRelationHideStatusResponse)
	err := c.cc.Invoke(ctx, "/channel_wedding_plan.ChannelWeddingPlan/SetUserRelationHideStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingPlanClient) Divorce(ctx context.Context, in *DivorceRequest, opts ...grpc.CallOption) (*DivorceResponse, error) {
	out := new(DivorceResponse)
	err := c.cc.Invoke(ctx, "/channel_wedding_plan.ChannelWeddingPlan/Divorce", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingPlanClient) GetUserDivorceStatus(ctx context.Context, in *GetUserDivorceStatusRequest, opts ...grpc.CallOption) (*GetUserDivorceStatusResponse, error) {
	out := new(GetUserDivorceStatusResponse)
	err := c.cc.Invoke(ctx, "/channel_wedding_plan.ChannelWeddingPlan/GetUserDivorceStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingPlanClient) GetMarriageStatus(ctx context.Context, in *GetMarriageStatusRequest, opts ...grpc.CallOption) (*GetMarriageStatusResponse, error) {
	out := new(GetMarriageStatusResponse)
	err := c.cc.Invoke(ctx, "/channel_wedding_plan.ChannelWeddingPlan/GetMarriageStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingPlanClient) ManualNotifyWeddingStart(ctx context.Context, in *ManualNotifyWeddingStartRequest, opts ...grpc.CallOption) (*ManualNotifyWeddingStartResponse, error) {
	out := new(ManualNotifyWeddingStartResponse)
	err := c.cc.Invoke(ctx, "/channel_wedding_plan.ChannelWeddingPlan/ManualNotifyWeddingStart", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingPlanClient) UpdateWeddingPlanStatus(ctx context.Context, in *UpdateWeddingPlanStatusRequest, opts ...grpc.CallOption) (*UpdateWeddingPlanStatusResponse, error) {
	out := new(UpdateWeddingPlanStatusResponse)
	err := c.cc.Invoke(ctx, "/channel_wedding_plan.ChannelWeddingPlan/UpdateWeddingPlanStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingPlanClient) TestWeddingAnniversaryPopup(ctx context.Context, in *TestWeddingAnniversaryPopupRequest, opts ...grpc.CallOption) (*TestWeddingAnniversaryPopupResponse, error) {
	out := new(TestWeddingAnniversaryPopupResponse)
	err := c.cc.Invoke(ctx, "/channel_wedding_plan.ChannelWeddingPlan/TestWeddingAnniversaryPopup", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingPlanClient) GetMyWeddingRole(ctx context.Context, in *GetMyWeddingRoleRequest, opts ...grpc.CallOption) (*GetMyWeddingRoleResponse, error) {
	out := new(GetMyWeddingRoleResponse)
	err := c.cc.Invoke(ctx, "/channel_wedding_plan.ChannelWeddingPlan/GetMyWeddingRole", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingPlanClient) BatchGetWeddingRole(ctx context.Context, in *BatchGetWeddingRoleRequest, opts ...grpc.CallOption) (*BatchGetWeddingRoleResponse, error) {
	out := new(BatchGetWeddingRoleResponse)
	err := c.cc.Invoke(ctx, "/channel_wedding_plan.ChannelWeddingPlan/BatchGetWeddingRole", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingPlanClient) GetChannelReservedInfo(ctx context.Context, in *GetChannelReservedInfoRequest, opts ...grpc.CallOption) (*GetChannelReservedInfoResponse, error) {
	out := new(GetChannelReservedInfoResponse)
	err := c.cc.Invoke(ctx, "/channel_wedding_plan.ChannelWeddingPlan/GetChannelReservedInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingPlanClient) ConsultWeddingReserve(ctx context.Context, in *ConsultWeddingReserveRequest, opts ...grpc.CallOption) (*ConsultWeddingReserveResponse, error) {
	out := new(ConsultWeddingReserveResponse)
	err := c.cc.Invoke(ctx, "/channel_wedding_plan.ChannelWeddingPlan/ConsultWeddingReserve", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingPlanClient) ArrangeWeddingReserve(ctx context.Context, in *ArrangeWeddingReserveRequest, opts ...grpc.CallOption) (*ArrangeWeddingReserveResponse, error) {
	out := new(ArrangeWeddingReserveResponse)
	err := c.cc.Invoke(ctx, "/channel_wedding_plan.ChannelWeddingPlan/ArrangeWeddingReserve", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingPlanClient) BatchHotWeddingPlan(ctx context.Context, in *BatchHotWeddingPlanRequest, opts ...grpc.CallOption) (*BatchHotWeddingPlanResponse, error) {
	out := new(BatchHotWeddingPlanResponse)
	err := c.cc.Invoke(ctx, "/channel_wedding_plan.ChannelWeddingPlan/BatchHotWeddingPlan", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingPlanClient) GetTBeanTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	out := new(reconcile_v2.CountResp)
	err := c.cc.Invoke(ctx, "/channel_wedding_plan.ChannelWeddingPlan/GetTBeanTotalCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingPlanClient) GetTBeanOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	out := new(reconcile_v2.OrderIdsResp)
	err := c.cc.Invoke(ctx, "/channel_wedding_plan.ChannelWeddingPlan/GetTBeanOrderIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingPlanClient) AdminCancelWedding(ctx context.Context, in *AdminCancelWeddingRequest, opts ...grpc.CallOption) (*AdminCancelWeddingResponse, error) {
	out := new(AdminCancelWeddingResponse)
	err := c.cc.Invoke(ctx, "/channel_wedding_plan.ChannelWeddingPlan/AdminCancelWedding", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingPlanClient) GetChannelReserveTimeSectionConf(ctx context.Context, in *GetChannelReserveTimeSectionConfRequest, opts ...grpc.CallOption) (*GetChannelReserveTimeSectionConfResponse, error) {
	out := new(GetChannelReserveTimeSectionConfResponse)
	err := c.cc.Invoke(ctx, "/channel_wedding_plan.ChannelWeddingPlan/GetChannelReserveTimeSectionConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingPlanClient) SetAdminChannelReserveTimeSectionSwitch(ctx context.Context, in *SetAdminChannelReserveTimeSectionSwitchRequest, opts ...grpc.CallOption) (*SetAdminChannelReserveTimeSectionSwitchResponse, error) {
	out := new(SetAdminChannelReserveTimeSectionSwitchResponse)
	err := c.cc.Invoke(ctx, "/channel_wedding_plan.ChannelWeddingPlan/SetAdminChannelReserveTimeSectionSwitch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChannelWeddingPlanServer is the server API for ChannelWeddingPlan service.
type ChannelWeddingPlanServer interface {
	// 购买婚礼
	BuyWedding(context.Context, *BuyWeddingRequest) (*BuyWeddingResponse, error)
	// 取消婚礼
	CancelWedding(context.Context, *CancelWeddingRequest) (*CancelWeddingResponse, error)
	// 获取我的婚礼信息
	GetMyWeddingInfo(context.Context, *GetMyWeddingInfoRequest) (*GetMyWeddingInfoResponse, error)
	// 获取所有主题配置
	GetAllThemeCfg(context.Context, *GetAllThemeCfgRequest) (*GetAllThemeCfgResponse, error)
	// 添加主题配置
	AddThemeCfg(context.Context, *AddThemeCfgRequest) (*AddThemeCfgResponse, error)
	// 更新主题配置
	UpdateThemeCfg(context.Context, *UpdateThemeCfgRequest) (*UpdateThemeCfgResponse, error)
	// 删除主题配置
	DeleteThemeCfg(context.Context, *DeleteThemeCfgRequest) (*DeleteThemeCfgResponse, error)
	// 根据themeId获取Cfg
	GetThemeCfgById(context.Context, *GetThemeCfgByIdRequest) (*GetThemeCfgByIdResponse, error)
	// 获取我的预约信息
	GetMyWeddingReserveInfo(context.Context, *GetMyWeddingReserveInfoRequest) (*GetMyWeddingReserveInfoResponse, error)
	// 获取选中日期/房间的预约信息
	GetWeddingReserveInfo(context.Context, *GetWeddingReserveInfoRequest) (*GetWeddingReserveInfoResponse, error)
	// 保存预约信息
	SaveWeddingReserveInfo(context.Context, *SaveWeddingReserveRequest) (*SaveWeddingReserveInfoResponse, error)
	// 修改预约信息
	ChangeWeddingReserveInfo(context.Context, *ChangeWeddingReserveRequest) (*ChangeWeddingReserveInfoResponse, error)
	// 获取伴郎伴娘信息
	GetGroomsmanAndBridesmaidInfo(context.Context, *GetGroomsmanAndBridesmaidInfoRequest) (*GetGroomsmanAndBridesmaidInfoResponse, error)
	// 获取亲友团信息
	GetWeddingFriendInfo(context.Context, *GetWeddingFriendInfoRequest) (*GetWeddingFriendInfoResponse, error)
	// 邀请嘉宾
	InviteWeddingGuest(context.Context, *InviteWeddingGuestRequest) (*InviteWeddingGuestResponse, error)
	// 处理婚礼邀请
	HandleWeddingInvite(context.Context, *HandleWeddingInviteRequest) (*HandleWeddingInviteResponse, error)
	// 删除嘉宾
	DelWeddingGuest(context.Context, *DelWeddingGuestRequest) (*DelWeddingGuestResponse, error)
	// 查询预约列表
	GetReservedWedding(context.Context, *GetReservedWeddingRequest) (*GetReservedWeddingResponse, error)
	// 设置主持人
	SetWeddingHost(context.Context, *SetWeddingHostRequest) (*SetWeddingHostResponse, error)
	// 获取婚礼基础信息
	GetWeddingPlanBaseInfo(context.Context, *GetWeddingPlanBaseInfoRequest) (*GetWeddingPlanBaseInfoResponse, error)
	// 获取简单婚礼信息
	GetSimpleWeddingPlanInfo(context.Context, *GetSimpleWeddingPlanInfoRequest) (*GetSimpleWeddingPlanInfoResponse, error)
	// 获取求婚函信息
	GetWeddingInviteInfo(context.Context, *GetWeddingInviteInfoRequest) (*GetWeddingInviteInfoResponse, error)
	// 批量获取婚礼信息
	BatGetWeddingInfoById(context.Context, *BatGetWeddingInfoByIdRequest) (*BatGetWeddingInfoByIdResponse, error)
	// 分页获取未开始的婚礼列表
	PageGetComingWeddingList(context.Context, *PageGetComingWeddingListRequest) (*PageGetComingWeddingListResponse, error)
	// 订阅婚礼
	SubscribeWedding(context.Context, *SubscribeWeddingRequest) (*SubscribeWeddingResponse, error)
	// 批量获取婚礼订阅状态
	BatGetWeddingSubscribeStatus(context.Context, *BatGetWeddingSubscribeStatusRequest) (*BatGetWeddingSubscribeStatusResponse, error)
	// 获取婚礼大屏
	GetWeddingBigScreen(context.Context, *GetWeddingBigScreenRequest) (*GetWeddingBigScreenResponse, error)
	// 保存婚礼大屏
	SaveWeddingBigScreen(context.Context, *SaveWeddingBigScreenRequest) (*SaveWeddingBigScreenResponse, error)
	// 更新婚礼大屏审核状态
	UpdateWeddingBigScreenReviewStatus(context.Context, *UpdateWeddingBigScreenReviewStatusRequest) (*UpdateWeddingBigScreenReviewStatusResponse, error)
	// 申请结束婚礼关系
	ApplyEndWeddingRelation(context.Context, *ApplyEndWeddingRelationRequest) (*ApplyEndWeddingRelationResponse, error)
	// 取消结束婚礼关系
	CancelEndWeddingRelation(context.Context, *CancelEndWeddingRelationRequest) (*CancelEndWeddingRelationResponse, error)
	// 获取求婚列表
	GetProposeList(context.Context, *GetProposeListRequest) (*GetProposeListResponse, error)
	// 发送求婚
	SendPropose(context.Context, *SendProposeRequest) (*SendProposeResponse, error)
	// RevokePropose 撤销求婚
	RevokePropose(context.Context, *RevokeProposeRequest) (*RevokeProposeResponse, error)
	// 处理求婚请求
	HandlePropose(context.Context, *HandleProposeRequest) (*HandleProposeResponse, error)
	// 获取求婚函信息
	GetProposeById(context.Context, *GetProposeByIdRequest) (*GetProposeByIdResponse, error)
	// 获取我发出的求婚函
	GetSendPropose(context.Context, *GetSendProposeRequest) (*GetSendProposeResponse, error)
	// 批量获取结婚状态
	BatchGetMarriageInfo(context.Context, *BatchGetMarriageInfoRequest) (*BatchGetMarriageInfoResponse, error)
	// 设置用户关系隐藏开关状态
	SetUserRelationHideStatus(context.Context, *SetUserRelationHideStatusRequest) (*SetUserRelationHideStatusResponse, error)
	// 离婚
	Divorce(context.Context, *DivorceRequest) (*DivorceResponse, error)
	// 获取离婚状态
	GetUserDivorceStatus(context.Context, *GetUserDivorceStatusRequest) (*GetUserDivorceStatusResponse, error)
	// 获取婚姻关系状态
	GetMarriageStatus(context.Context, *GetMarriageStatusRequest) (*GetMarriageStatusResponse, error)
	// 手动通知婚礼开始
	ManualNotifyWeddingStart(context.Context, *ManualNotifyWeddingStartRequest) (*ManualNotifyWeddingStartResponse, error)
	// 更新婚礼计划状态
	UpdateWeddingPlanStatus(context.Context, *UpdateWeddingPlanStatusRequest) (*UpdateWeddingPlanStatusResponse, error)
	// 手动测试婚礼纪念弹窗
	TestWeddingAnniversaryPopup(context.Context, *TestWeddingAnniversaryPopupRequest) (*TestWeddingAnniversaryPopupResponse, error)
	// 获取婚礼角色
	GetMyWeddingRole(context.Context, *GetMyWeddingRoleRequest) (*GetMyWeddingRoleResponse, error)
	//  批量获取婚礼角色
	BatchGetWeddingRole(context.Context, *BatchGetWeddingRoleRequest) (*BatchGetWeddingRoleResponse, error)
	// 获取房间内预约信息
	GetChannelReservedInfo(context.Context, *GetChannelReservedInfoRequest) (*GetChannelReservedInfoResponse, error)
	// 咨询预约婚礼
	ConsultWeddingReserve(context.Context, *ConsultWeddingReserveRequest) (*ConsultWeddingReserveResponse, error)
	// 客服安排婚礼预约
	ArrangeWeddingReserve(context.Context, *ArrangeWeddingReserveRequest) (*ArrangeWeddingReserveResponse, error)
	// 获取热门婚礼
	BatchHotWeddingPlan(context.Context, *BatchHotWeddingPlanRequest) (*BatchHotWeddingPlanResponse, error)
	// T豆对账
	GetTBeanTotalCount(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error)
	GetTBeanOrderIds(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error)
	AdminCancelWedding(context.Context, *AdminCancelWeddingRequest) (*AdminCancelWeddingResponse, error)
	// 获取公会后台房间预约时段配置
	GetChannelReserveTimeSectionConf(context.Context, *GetChannelReserveTimeSectionConfRequest) (*GetChannelReserveTimeSectionConfResponse, error)
	// 设置公会后台房间预约时段开关
	SetAdminChannelReserveTimeSectionSwitch(context.Context, *SetAdminChannelReserveTimeSectionSwitchRequest) (*SetAdminChannelReserveTimeSectionSwitchResponse, error)
}

func RegisterChannelWeddingPlanServer(s *grpc.Server, srv ChannelWeddingPlanServer) {
	s.RegisterService(&_ChannelWeddingPlan_serviceDesc, srv)
}

func _ChannelWeddingPlan_BuyWedding_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BuyWeddingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingPlanServer).BuyWedding(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding_plan.ChannelWeddingPlan/BuyWedding",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingPlanServer).BuyWedding(ctx, req.(*BuyWeddingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingPlan_CancelWedding_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CancelWeddingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingPlanServer).CancelWedding(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding_plan.ChannelWeddingPlan/CancelWedding",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingPlanServer).CancelWedding(ctx, req.(*CancelWeddingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingPlan_GetMyWeddingInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMyWeddingInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingPlanServer).GetMyWeddingInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding_plan.ChannelWeddingPlan/GetMyWeddingInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingPlanServer).GetMyWeddingInfo(ctx, req.(*GetMyWeddingInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingPlan_GetAllThemeCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllThemeCfgRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingPlanServer).GetAllThemeCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding_plan.ChannelWeddingPlan/GetAllThemeCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingPlanServer).GetAllThemeCfg(ctx, req.(*GetAllThemeCfgRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingPlan_AddThemeCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddThemeCfgRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingPlanServer).AddThemeCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding_plan.ChannelWeddingPlan/AddThemeCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingPlanServer).AddThemeCfg(ctx, req.(*AddThemeCfgRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingPlan_UpdateThemeCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateThemeCfgRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingPlanServer).UpdateThemeCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding_plan.ChannelWeddingPlan/UpdateThemeCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingPlanServer).UpdateThemeCfg(ctx, req.(*UpdateThemeCfgRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingPlan_DeleteThemeCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteThemeCfgRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingPlanServer).DeleteThemeCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding_plan.ChannelWeddingPlan/DeleteThemeCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingPlanServer).DeleteThemeCfg(ctx, req.(*DeleteThemeCfgRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingPlan_GetThemeCfgById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetThemeCfgByIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingPlanServer).GetThemeCfgById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding_plan.ChannelWeddingPlan/GetThemeCfgById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingPlanServer).GetThemeCfgById(ctx, req.(*GetThemeCfgByIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingPlan_GetMyWeddingReserveInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMyWeddingReserveInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingPlanServer).GetMyWeddingReserveInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding_plan.ChannelWeddingPlan/GetMyWeddingReserveInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingPlanServer).GetMyWeddingReserveInfo(ctx, req.(*GetMyWeddingReserveInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingPlan_GetWeddingReserveInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWeddingReserveInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingPlanServer).GetWeddingReserveInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding_plan.ChannelWeddingPlan/GetWeddingReserveInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingPlanServer).GetWeddingReserveInfo(ctx, req.(*GetWeddingReserveInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingPlan_SaveWeddingReserveInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SaveWeddingReserveRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingPlanServer).SaveWeddingReserveInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding_plan.ChannelWeddingPlan/SaveWeddingReserveInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingPlanServer).SaveWeddingReserveInfo(ctx, req.(*SaveWeddingReserveRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingPlan_ChangeWeddingReserveInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChangeWeddingReserveRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingPlanServer).ChangeWeddingReserveInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding_plan.ChannelWeddingPlan/ChangeWeddingReserveInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingPlanServer).ChangeWeddingReserveInfo(ctx, req.(*ChangeWeddingReserveRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingPlan_GetGroomsmanAndBridesmaidInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGroomsmanAndBridesmaidInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingPlanServer).GetGroomsmanAndBridesmaidInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding_plan.ChannelWeddingPlan/GetGroomsmanAndBridesmaidInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingPlanServer).GetGroomsmanAndBridesmaidInfo(ctx, req.(*GetGroomsmanAndBridesmaidInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingPlan_GetWeddingFriendInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWeddingFriendInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingPlanServer).GetWeddingFriendInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding_plan.ChannelWeddingPlan/GetWeddingFriendInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingPlanServer).GetWeddingFriendInfo(ctx, req.(*GetWeddingFriendInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingPlan_InviteWeddingGuest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InviteWeddingGuestRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingPlanServer).InviteWeddingGuest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding_plan.ChannelWeddingPlan/InviteWeddingGuest",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingPlanServer).InviteWeddingGuest(ctx, req.(*InviteWeddingGuestRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingPlan_HandleWeddingInvite_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HandleWeddingInviteRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingPlanServer).HandleWeddingInvite(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding_plan.ChannelWeddingPlan/HandleWeddingInvite",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingPlanServer).HandleWeddingInvite(ctx, req.(*HandleWeddingInviteRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingPlan_DelWeddingGuest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelWeddingGuestRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingPlanServer).DelWeddingGuest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding_plan.ChannelWeddingPlan/DelWeddingGuest",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingPlanServer).DelWeddingGuest(ctx, req.(*DelWeddingGuestRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingPlan_GetReservedWedding_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetReservedWeddingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingPlanServer).GetReservedWedding(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding_plan.ChannelWeddingPlan/GetReservedWedding",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingPlanServer).GetReservedWedding(ctx, req.(*GetReservedWeddingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingPlan_SetWeddingHost_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetWeddingHostRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingPlanServer).SetWeddingHost(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding_plan.ChannelWeddingPlan/SetWeddingHost",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingPlanServer).SetWeddingHost(ctx, req.(*SetWeddingHostRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingPlan_GetWeddingPlanBaseInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWeddingPlanBaseInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingPlanServer).GetWeddingPlanBaseInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding_plan.ChannelWeddingPlan/GetWeddingPlanBaseInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingPlanServer).GetWeddingPlanBaseInfo(ctx, req.(*GetWeddingPlanBaseInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingPlan_GetSimpleWeddingPlanInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSimpleWeddingPlanInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingPlanServer).GetSimpleWeddingPlanInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding_plan.ChannelWeddingPlan/GetSimpleWeddingPlanInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingPlanServer).GetSimpleWeddingPlanInfo(ctx, req.(*GetSimpleWeddingPlanInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingPlan_GetWeddingInviteInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWeddingInviteInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingPlanServer).GetWeddingInviteInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding_plan.ChannelWeddingPlan/GetWeddingInviteInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingPlanServer).GetWeddingInviteInfo(ctx, req.(*GetWeddingInviteInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingPlan_BatGetWeddingInfoById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatGetWeddingInfoByIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingPlanServer).BatGetWeddingInfoById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding_plan.ChannelWeddingPlan/BatGetWeddingInfoById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingPlanServer).BatGetWeddingInfoById(ctx, req.(*BatGetWeddingInfoByIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingPlan_PageGetComingWeddingList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PageGetComingWeddingListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingPlanServer).PageGetComingWeddingList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding_plan.ChannelWeddingPlan/PageGetComingWeddingList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingPlanServer).PageGetComingWeddingList(ctx, req.(*PageGetComingWeddingListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingPlan_SubscribeWedding_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SubscribeWeddingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingPlanServer).SubscribeWedding(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding_plan.ChannelWeddingPlan/SubscribeWedding",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingPlanServer).SubscribeWedding(ctx, req.(*SubscribeWeddingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingPlan_BatGetWeddingSubscribeStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatGetWeddingSubscribeStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingPlanServer).BatGetWeddingSubscribeStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding_plan.ChannelWeddingPlan/BatGetWeddingSubscribeStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingPlanServer).BatGetWeddingSubscribeStatus(ctx, req.(*BatGetWeddingSubscribeStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingPlan_GetWeddingBigScreen_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWeddingBigScreenRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingPlanServer).GetWeddingBigScreen(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding_plan.ChannelWeddingPlan/GetWeddingBigScreen",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingPlanServer).GetWeddingBigScreen(ctx, req.(*GetWeddingBigScreenRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingPlan_SaveWeddingBigScreen_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SaveWeddingBigScreenRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingPlanServer).SaveWeddingBigScreen(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding_plan.ChannelWeddingPlan/SaveWeddingBigScreen",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingPlanServer).SaveWeddingBigScreen(ctx, req.(*SaveWeddingBigScreenRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingPlan_UpdateWeddingBigScreenReviewStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateWeddingBigScreenReviewStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingPlanServer).UpdateWeddingBigScreenReviewStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding_plan.ChannelWeddingPlan/UpdateWeddingBigScreenReviewStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingPlanServer).UpdateWeddingBigScreenReviewStatus(ctx, req.(*UpdateWeddingBigScreenReviewStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingPlan_ApplyEndWeddingRelation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ApplyEndWeddingRelationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingPlanServer).ApplyEndWeddingRelation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding_plan.ChannelWeddingPlan/ApplyEndWeddingRelation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingPlanServer).ApplyEndWeddingRelation(ctx, req.(*ApplyEndWeddingRelationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingPlan_CancelEndWeddingRelation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CancelEndWeddingRelationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingPlanServer).CancelEndWeddingRelation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding_plan.ChannelWeddingPlan/CancelEndWeddingRelation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingPlanServer).CancelEndWeddingRelation(ctx, req.(*CancelEndWeddingRelationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingPlan_GetProposeList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetProposeListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingPlanServer).GetProposeList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding_plan.ChannelWeddingPlan/GetProposeList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingPlanServer).GetProposeList(ctx, req.(*GetProposeListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingPlan_SendPropose_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendProposeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingPlanServer).SendPropose(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding_plan.ChannelWeddingPlan/SendPropose",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingPlanServer).SendPropose(ctx, req.(*SendProposeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingPlan_RevokePropose_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RevokeProposeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingPlanServer).RevokePropose(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding_plan.ChannelWeddingPlan/RevokePropose",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingPlanServer).RevokePropose(ctx, req.(*RevokeProposeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingPlan_HandlePropose_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HandleProposeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingPlanServer).HandlePropose(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding_plan.ChannelWeddingPlan/HandlePropose",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingPlanServer).HandlePropose(ctx, req.(*HandleProposeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingPlan_GetProposeById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetProposeByIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingPlanServer).GetProposeById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding_plan.ChannelWeddingPlan/GetProposeById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingPlanServer).GetProposeById(ctx, req.(*GetProposeByIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingPlan_GetSendPropose_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSendProposeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingPlanServer).GetSendPropose(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding_plan.ChannelWeddingPlan/GetSendPropose",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingPlanServer).GetSendPropose(ctx, req.(*GetSendProposeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingPlan_BatchGetMarriageInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetMarriageInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingPlanServer).BatchGetMarriageInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding_plan.ChannelWeddingPlan/BatchGetMarriageInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingPlanServer).BatchGetMarriageInfo(ctx, req.(*BatchGetMarriageInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingPlan_SetUserRelationHideStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetUserRelationHideStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingPlanServer).SetUserRelationHideStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding_plan.ChannelWeddingPlan/SetUserRelationHideStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingPlanServer).SetUserRelationHideStatus(ctx, req.(*SetUserRelationHideStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingPlan_Divorce_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DivorceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingPlanServer).Divorce(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding_plan.ChannelWeddingPlan/Divorce",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingPlanServer).Divorce(ctx, req.(*DivorceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingPlan_GetUserDivorceStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserDivorceStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingPlanServer).GetUserDivorceStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding_plan.ChannelWeddingPlan/GetUserDivorceStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingPlanServer).GetUserDivorceStatus(ctx, req.(*GetUserDivorceStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingPlan_GetMarriageStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMarriageStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingPlanServer).GetMarriageStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding_plan.ChannelWeddingPlan/GetMarriageStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingPlanServer).GetMarriageStatus(ctx, req.(*GetMarriageStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingPlan_ManualNotifyWeddingStart_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ManualNotifyWeddingStartRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingPlanServer).ManualNotifyWeddingStart(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding_plan.ChannelWeddingPlan/ManualNotifyWeddingStart",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingPlanServer).ManualNotifyWeddingStart(ctx, req.(*ManualNotifyWeddingStartRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingPlan_UpdateWeddingPlanStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateWeddingPlanStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingPlanServer).UpdateWeddingPlanStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding_plan.ChannelWeddingPlan/UpdateWeddingPlanStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingPlanServer).UpdateWeddingPlanStatus(ctx, req.(*UpdateWeddingPlanStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingPlan_TestWeddingAnniversaryPopup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TestWeddingAnniversaryPopupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingPlanServer).TestWeddingAnniversaryPopup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding_plan.ChannelWeddingPlan/TestWeddingAnniversaryPopup",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingPlanServer).TestWeddingAnniversaryPopup(ctx, req.(*TestWeddingAnniversaryPopupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingPlan_GetMyWeddingRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMyWeddingRoleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingPlanServer).GetMyWeddingRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding_plan.ChannelWeddingPlan/GetMyWeddingRole",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingPlanServer).GetMyWeddingRole(ctx, req.(*GetMyWeddingRoleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingPlan_BatchGetWeddingRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetWeddingRoleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingPlanServer).BatchGetWeddingRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding_plan.ChannelWeddingPlan/BatchGetWeddingRole",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingPlanServer).BatchGetWeddingRole(ctx, req.(*BatchGetWeddingRoleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingPlan_GetChannelReservedInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelReservedInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingPlanServer).GetChannelReservedInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding_plan.ChannelWeddingPlan/GetChannelReservedInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingPlanServer).GetChannelReservedInfo(ctx, req.(*GetChannelReservedInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingPlan_ConsultWeddingReserve_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConsultWeddingReserveRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingPlanServer).ConsultWeddingReserve(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding_plan.ChannelWeddingPlan/ConsultWeddingReserve",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingPlanServer).ConsultWeddingReserve(ctx, req.(*ConsultWeddingReserveRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingPlan_ArrangeWeddingReserve_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ArrangeWeddingReserveRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingPlanServer).ArrangeWeddingReserve(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding_plan.ChannelWeddingPlan/ArrangeWeddingReserve",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingPlanServer).ArrangeWeddingReserve(ctx, req.(*ArrangeWeddingReserveRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingPlan_BatchHotWeddingPlan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchHotWeddingPlanRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingPlanServer).BatchHotWeddingPlan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding_plan.ChannelWeddingPlan/BatchHotWeddingPlan",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingPlanServer).BatchHotWeddingPlan(ctx, req.(*BatchHotWeddingPlanRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingPlan_GetTBeanTotalCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingPlanServer).GetTBeanTotalCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding_plan.ChannelWeddingPlan/GetTBeanTotalCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingPlanServer).GetTBeanTotalCount(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingPlan_GetTBeanOrderIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingPlanServer).GetTBeanOrderIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding_plan.ChannelWeddingPlan/GetTBeanOrderIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingPlanServer).GetTBeanOrderIds(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingPlan_AdminCancelWedding_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AdminCancelWeddingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingPlanServer).AdminCancelWedding(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding_plan.ChannelWeddingPlan/AdminCancelWedding",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingPlanServer).AdminCancelWedding(ctx, req.(*AdminCancelWeddingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingPlan_GetChannelReserveTimeSectionConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelReserveTimeSectionConfRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingPlanServer).GetChannelReserveTimeSectionConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding_plan.ChannelWeddingPlan/GetChannelReserveTimeSectionConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingPlanServer).GetChannelReserveTimeSectionConf(ctx, req.(*GetChannelReserveTimeSectionConfRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingPlan_SetAdminChannelReserveTimeSectionSwitch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetAdminChannelReserveTimeSectionSwitchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingPlanServer).SetAdminChannelReserveTimeSectionSwitch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding_plan.ChannelWeddingPlan/SetAdminChannelReserveTimeSectionSwitch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingPlanServer).SetAdminChannelReserveTimeSectionSwitch(ctx, req.(*SetAdminChannelReserveTimeSectionSwitchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelWeddingPlan_serviceDesc = grpc.ServiceDesc{
	ServiceName: "channel_wedding_plan.ChannelWeddingPlan",
	HandlerType: (*ChannelWeddingPlanServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "BuyWedding",
			Handler:    _ChannelWeddingPlan_BuyWedding_Handler,
		},
		{
			MethodName: "CancelWedding",
			Handler:    _ChannelWeddingPlan_CancelWedding_Handler,
		},
		{
			MethodName: "GetMyWeddingInfo",
			Handler:    _ChannelWeddingPlan_GetMyWeddingInfo_Handler,
		},
		{
			MethodName: "GetAllThemeCfg",
			Handler:    _ChannelWeddingPlan_GetAllThemeCfg_Handler,
		},
		{
			MethodName: "AddThemeCfg",
			Handler:    _ChannelWeddingPlan_AddThemeCfg_Handler,
		},
		{
			MethodName: "UpdateThemeCfg",
			Handler:    _ChannelWeddingPlan_UpdateThemeCfg_Handler,
		},
		{
			MethodName: "DeleteThemeCfg",
			Handler:    _ChannelWeddingPlan_DeleteThemeCfg_Handler,
		},
		{
			MethodName: "GetThemeCfgById",
			Handler:    _ChannelWeddingPlan_GetThemeCfgById_Handler,
		},
		{
			MethodName: "GetMyWeddingReserveInfo",
			Handler:    _ChannelWeddingPlan_GetMyWeddingReserveInfo_Handler,
		},
		{
			MethodName: "GetWeddingReserveInfo",
			Handler:    _ChannelWeddingPlan_GetWeddingReserveInfo_Handler,
		},
		{
			MethodName: "SaveWeddingReserveInfo",
			Handler:    _ChannelWeddingPlan_SaveWeddingReserveInfo_Handler,
		},
		{
			MethodName: "ChangeWeddingReserveInfo",
			Handler:    _ChannelWeddingPlan_ChangeWeddingReserveInfo_Handler,
		},
		{
			MethodName: "GetGroomsmanAndBridesmaidInfo",
			Handler:    _ChannelWeddingPlan_GetGroomsmanAndBridesmaidInfo_Handler,
		},
		{
			MethodName: "GetWeddingFriendInfo",
			Handler:    _ChannelWeddingPlan_GetWeddingFriendInfo_Handler,
		},
		{
			MethodName: "InviteWeddingGuest",
			Handler:    _ChannelWeddingPlan_InviteWeddingGuest_Handler,
		},
		{
			MethodName: "HandleWeddingInvite",
			Handler:    _ChannelWeddingPlan_HandleWeddingInvite_Handler,
		},
		{
			MethodName: "DelWeddingGuest",
			Handler:    _ChannelWeddingPlan_DelWeddingGuest_Handler,
		},
		{
			MethodName: "GetReservedWedding",
			Handler:    _ChannelWeddingPlan_GetReservedWedding_Handler,
		},
		{
			MethodName: "SetWeddingHost",
			Handler:    _ChannelWeddingPlan_SetWeddingHost_Handler,
		},
		{
			MethodName: "GetWeddingPlanBaseInfo",
			Handler:    _ChannelWeddingPlan_GetWeddingPlanBaseInfo_Handler,
		},
		{
			MethodName: "GetSimpleWeddingPlanInfo",
			Handler:    _ChannelWeddingPlan_GetSimpleWeddingPlanInfo_Handler,
		},
		{
			MethodName: "GetWeddingInviteInfo",
			Handler:    _ChannelWeddingPlan_GetWeddingInviteInfo_Handler,
		},
		{
			MethodName: "BatGetWeddingInfoById",
			Handler:    _ChannelWeddingPlan_BatGetWeddingInfoById_Handler,
		},
		{
			MethodName: "PageGetComingWeddingList",
			Handler:    _ChannelWeddingPlan_PageGetComingWeddingList_Handler,
		},
		{
			MethodName: "SubscribeWedding",
			Handler:    _ChannelWeddingPlan_SubscribeWedding_Handler,
		},
		{
			MethodName: "BatGetWeddingSubscribeStatus",
			Handler:    _ChannelWeddingPlan_BatGetWeddingSubscribeStatus_Handler,
		},
		{
			MethodName: "GetWeddingBigScreen",
			Handler:    _ChannelWeddingPlan_GetWeddingBigScreen_Handler,
		},
		{
			MethodName: "SaveWeddingBigScreen",
			Handler:    _ChannelWeddingPlan_SaveWeddingBigScreen_Handler,
		},
		{
			MethodName: "UpdateWeddingBigScreenReviewStatus",
			Handler:    _ChannelWeddingPlan_UpdateWeddingBigScreenReviewStatus_Handler,
		},
		{
			MethodName: "ApplyEndWeddingRelation",
			Handler:    _ChannelWeddingPlan_ApplyEndWeddingRelation_Handler,
		},
		{
			MethodName: "CancelEndWeddingRelation",
			Handler:    _ChannelWeddingPlan_CancelEndWeddingRelation_Handler,
		},
		{
			MethodName: "GetProposeList",
			Handler:    _ChannelWeddingPlan_GetProposeList_Handler,
		},
		{
			MethodName: "SendPropose",
			Handler:    _ChannelWeddingPlan_SendPropose_Handler,
		},
		{
			MethodName: "RevokePropose",
			Handler:    _ChannelWeddingPlan_RevokePropose_Handler,
		},
		{
			MethodName: "HandlePropose",
			Handler:    _ChannelWeddingPlan_HandlePropose_Handler,
		},
		{
			MethodName: "GetProposeById",
			Handler:    _ChannelWeddingPlan_GetProposeById_Handler,
		},
		{
			MethodName: "GetSendPropose",
			Handler:    _ChannelWeddingPlan_GetSendPropose_Handler,
		},
		{
			MethodName: "BatchGetMarriageInfo",
			Handler:    _ChannelWeddingPlan_BatchGetMarriageInfo_Handler,
		},
		{
			MethodName: "SetUserRelationHideStatus",
			Handler:    _ChannelWeddingPlan_SetUserRelationHideStatus_Handler,
		},
		{
			MethodName: "Divorce",
			Handler:    _ChannelWeddingPlan_Divorce_Handler,
		},
		{
			MethodName: "GetUserDivorceStatus",
			Handler:    _ChannelWeddingPlan_GetUserDivorceStatus_Handler,
		},
		{
			MethodName: "GetMarriageStatus",
			Handler:    _ChannelWeddingPlan_GetMarriageStatus_Handler,
		},
		{
			MethodName: "ManualNotifyWeddingStart",
			Handler:    _ChannelWeddingPlan_ManualNotifyWeddingStart_Handler,
		},
		{
			MethodName: "UpdateWeddingPlanStatus",
			Handler:    _ChannelWeddingPlan_UpdateWeddingPlanStatus_Handler,
		},
		{
			MethodName: "TestWeddingAnniversaryPopup",
			Handler:    _ChannelWeddingPlan_TestWeddingAnniversaryPopup_Handler,
		},
		{
			MethodName: "GetMyWeddingRole",
			Handler:    _ChannelWeddingPlan_GetMyWeddingRole_Handler,
		},
		{
			MethodName: "BatchGetWeddingRole",
			Handler:    _ChannelWeddingPlan_BatchGetWeddingRole_Handler,
		},
		{
			MethodName: "GetChannelReservedInfo",
			Handler:    _ChannelWeddingPlan_GetChannelReservedInfo_Handler,
		},
		{
			MethodName: "ConsultWeddingReserve",
			Handler:    _ChannelWeddingPlan_ConsultWeddingReserve_Handler,
		},
		{
			MethodName: "ArrangeWeddingReserve",
			Handler:    _ChannelWeddingPlan_ArrangeWeddingReserve_Handler,
		},
		{
			MethodName: "BatchHotWeddingPlan",
			Handler:    _ChannelWeddingPlan_BatchHotWeddingPlan_Handler,
		},
		{
			MethodName: "GetTBeanTotalCount",
			Handler:    _ChannelWeddingPlan_GetTBeanTotalCount_Handler,
		},
		{
			MethodName: "GetTBeanOrderIds",
			Handler:    _ChannelWeddingPlan_GetTBeanOrderIds_Handler,
		},
		{
			MethodName: "AdminCancelWedding",
			Handler:    _ChannelWeddingPlan_AdminCancelWedding_Handler,
		},
		{
			MethodName: "GetChannelReserveTimeSectionConf",
			Handler:    _ChannelWeddingPlan_GetChannelReserveTimeSectionConf_Handler,
		},
		{
			MethodName: "SetAdminChannelReserveTimeSectionSwitch",
			Handler:    _ChannelWeddingPlan_SetAdminChannelReserveTimeSectionSwitch_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/channel-wedding-plan/channel-wedding-plan.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/channel-wedding-plan/channel-wedding-plan.proto", fileDescriptor_channel_wedding_plan_b56569e2f2e6a981)
}

var fileDescriptor_channel_wedding_plan_b56569e2f2e6a981 = []byte{
	// 6145 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xd4, 0x7d, 0xdd, 0x6f, 0x1b, 0xcb,
	0x75, 0xb8, 0x96, 0xd4, 0x07, 0x79, 0xa8, 0x0f, 0x6a, 0x2d, 0xeb, 0x83, 0xb2, 0x2d, 0x79, 0x7d,
	0x7d, 0xaf, 0xed, 0x6b, 0xcb, 0xb9, 0xba, 0x1f, 0xb9, 0xb9, 0xf9, 0xfd, 0x92, 0x48, 0x24, 0x2d,
	0x33, 0xb1, 0x25, 0x66, 0x49, 0x5d, 0x27, 0x45, 0x82, 0xc5, 0x8a, 0x3b, 0xa2, 0x36, 0x26, 0x77,
	0xe9, 0xdd, 0xa5, 0x64, 0x05, 0x01, 0x92, 0x16, 0x6d, 0x53, 0xa0, 0x40, 0x81, 0x14, 0x2d, 0x0a,
	0x04, 0x7d, 0x28, 0xfa, 0x99, 0xf6, 0xa9, 0x6f, 0x2d, 0xf2, 0xd0, 0xe4, 0xa1, 0x40, 0x51, 0xa0,
	0x7d, 0xe8, 0x53, 0x8b, 0xa2, 0x7f, 0x42, 0x81, 0x02, 0x41, 0x1f, 0xd2, 0xa2, 0x0f, 0xc5, 0x7c,
	0xec, 0xee, 0xec, 0xc7, 0x2c, 0x49, 0xdd, 0x8b, 0x16, 0x7d, 0xb2, 0xf6, 0xcc, 0xcc, 0x99, 0x33,
	0x67, 0xce, 0x9c, 0x39, 0xe7, 0xcc, 0x39, 0x34, 0x7c, 0xc1, 0xf3, 0x1e, 0xbf, 0x1a, 0x9a, 0x9d,
	0x97, 0xae, 0xd9, 0x3b, 0x47, 0xce, 0xe3, 0xce, 0x99, 0x6e, 0x59, 0xa8, 0xf7, 0xe8, 0x02, 0x19,
	0x86, 0x69, 0x75, 0x1f, 0x0d, 0x7a, 0xba, 0x95, 0x0a, 0xdc, 0x19, 0x38, 0xb6, 0x67, 0xcb, 0x2b,
	0xac, 0x4d, 0x63, 0x6d, 0x1a, 0x6e, 0xab, 0xec, 0xc4, 0xb0, 0xa2, 0xd7, 0x1e, 0xb2, 0x5c, 0xd3,
	0xb6, 0x1e, 0xdb, 0x03, 0xcf, 0xb4, 0x2d, 0xd7, 0xff, 0x97, 0x62, 0x49, 0xf4, 0x77, 0x50, 0xc7,
	0xb6, 0x3a, 0x66, 0x0f, 0x3d, 0x3a, 0xdf, 0x8d, 0x7c, 0xd0, 0xfe, 0xca, 0x4f, 0x24, 0x58, 0xde,
	0x1f, 0x5e, 0xbe, 0xa0, 0x73, 0xaa, 0xe8, 0xd5, 0x10, 0xb9, 0x9e, 0xbc, 0x01, 0x05, 0xef, 0x0c,
	0xf5, 0x91, 0x66, 0x1a, 0xeb, 0xd2, 0xb6, 0x74, 0x6f, 0x41, 0x9d, 0x23, 0xdf, 0x0d, 0x43, 0xde,
	0x84, 0xe2, 0xc9, 0xf0, 0x12, 0x39, 0xda, 0xd0, 0x34, 0xd6, 0x73, 0xa4, 0xad, 0x40, 0x00, 0xc7,
	0xa6, 0x21, 0x1f, 0x42, 0xf9, 0x64, 0x78, 0xa9, 0x39, 0xc8, 0x45, 0xce, 0x39, 0xd2, 0x4c, 0xeb,
	0xd4, 0x5e, 0xcf, 0x6f, 0x4b, 0xf7, 0x4a, 0xbb, 0x6f, 0xec, 0xa4, 0x2d, 0x6f, 0x67, 0x7f, 0x78,
	0xa9, 0xd2, 0xce, 0x0d, 0xeb, 0xd4, 0x56, 0x17, 0x4f, 0x22, 0xdf, 0xb2, 0x02, 0x0b, 0xae, 0x3d,
	0x74, 0x3a, 0x48, 0xeb, 0xbb, 0x5d, 0x4c, 0xcc, 0x34, 0x99, 0xb0, 0x44, 0x81, 0xcf, 0xdd, 0x6e,
	0xc3, 0x50, 0xfe, 0x48, 0x82, 0xc5, 0x28, 0x1a, 0xf9, 0x36, 0xcc, 0xfb, 0x24, 0x18, 0xba, 0x87,
	0xd8, 0x12, 0x4a, 0x0c, 0x56, 0xd3, 0x3d, 0x24, 0xdf, 0x04, 0xf0, 0x09, 0x0a, 0xd6, 0x51, 0x64,
	0x90, 0x86, 0xc1, 0x63, 0xf0, 0xcc, 0x3e, 0x5a, 0xcf, 0x6f, 0xe7, 0xef, 0x15, 0x03, 0x0c, 0x6d,
	0xb3, 0x8f, 0xe4, 0xeb, 0x30, 0x6b, 0xba, 0xda, 0x99, 0xed, 0x11, 0xa2, 0x0a, 0xea, 0x8c, 0xe9,
	0x3e, 0xb5, 0x3d, 0x79, 0x0d, 0xe6, 0xba, 0xe6, 0xa9, 0x87, 0xb1, 0xce, 0x10, 0xac, 0xb3, 0xf8,
	0xb3, 0x61, 0x28, 0xff, 0x0f, 0x64, 0x9e, 0xd1, 0xee, 0xc0, 0xb6, 0x5c, 0x24, 0xbf, 0x09, 0x4b,
	0x3c, 0x43, 0x42, 0x86, 0x2f, 0x30, 0x70, 0xb3, 0xa7, 0x5b, 0x0d, 0x43, 0x79, 0x0a, 0xb7, 0x0e,
	0x90, 0xf7, 0x9c, 0x1b, 0x1f, 0x30, 0x8d, 0xed, 0xd9, 0xb8, 0x98, 0xfe, 0x2d, 0x07, 0x5b, 0x42,
	0x54, 0x8c, 0xaa, 0x4f, 0xce, 0xc0, 0xcf, 0xc0, 0x0a, 0xcf, 0x40, 0xcd, 0x45, 0x1d, 0x2c, 0xa6,
	0x8c, 0x91, 0x32, 0xc7, 0xc8, 0x16, 0x6d, 0x91, 0x77, 0xe0, 0x9a, 0x83, 0xfa, 0xba, 0x69, 0x69,
	0x18, 0x4b, 0x97, 0x8e, 0x73, 0xd9, 0x8e, 0x2f, 0xd3, 0xa6, 0x2a, 0x69, 0xc1, 0xa3, 0x5c, 0xf9,
	0x01, 0x2c, 0xb3, 0x8e, 0x3d, 0xb3, 0x6f, 0x7a, 0x74, 0x9f, 0x28, 0xcb, 0x97, 0x68, 0xc3, 0x33,
	0x0c, 0x27, 0x7b, 0xf5, 0x06, 0x2c, 0x46, 0xf1, 0xae, 0xcf, 0x92, 0x3d, 0x9b, 0xe7, 0x51, 0xca,
	0x0f, 0xc1, 0xa7, 0x4b, 0x73, 0x3d, 0xdd, 0x61, 0x28, 0xe7, 0x08, 0xca, 0x32, 0x6b, 0x69, 0xe1,
	0x06, 0xd2, 0xfb, 0x1e, 0xf8, 0x30, 0x0d, 0x59, 0x06, 0xed, 0x5b, 0x20, 0x7d, 0x17, 0x19, 0xbc,
	0x6e, 0x19, 0xb8, 0xa7, 0xf2, 0x1c, 0x4a, 0x55, 0xc6, 0x18, 0x2c, 0x9d, 0x51, 0xce, 0x49, 0x71,
	0xce, 0x6d, 0x41, 0xa9, 0xaf, 0x5b, 0x7a, 0x37, 0x72, 0xc4, 0x80, 0x81, 0x8e, 0x4d, 0x43, 0xf9,
	0x2e, 0xdc, 0x38, 0x40, 0x9e, 0x58, 0x10, 0x6e, 0x02, 0xd0, 0xc3, 0xeb, 0x5d, 0x0e, 0xfc, 0xad,
	0x2b, 0x12, 0x48, 0xfb, 0x72, 0x90, 0xdc, 0xdb, 0xdc, 0xa8, 0xbd, 0xcd, 0xc7, 0x28, 0x54, 0xfe,
	0x25, 0x07, 0x37, 0x05, 0x14, 0x30, 0xf9, 0x79, 0x03, 0x16, 0x3b, 0x43, 0x47, 0x4b, 0x2c, 0x73,
	0xbe, 0x33, 0x74, 0xaa, 0xc1, 0x4a, 0xef, 0x41, 0x19, 0xf7, 0x4a, 0xa1, 0x06, 0x8f, 0x56, 0x39,
	0x82, 0x6a, 0x30, 0x1f, 0xe0, 0xa2, 0x3a, 0x25, 0x7f, 0xaf, 0xb4, 0x7b, 0x3b, 0x5d, 0xa7, 0x70,
	0xbc, 0x56, 0x4b, 0x1d, 0x8e, 0xf1, 0x5f, 0x85, 0xe5, 0x88, 0x4c, 0x12, 0x54, 0xd3, 0x04, 0xd5,
	0xdd, 0x74, 0x54, 0x6a, 0x28, 0xa6, 0x04, 0xdd, 0x92, 0x13, 0x05, 0xe0, 0x25, 0xf4, 0x4d, 0x2b,
	0xba, 0x04, 0x2a, 0x83, 0x8b, 0x7d, 0xd3, 0xe2, 0x97, 0x80, 0x7b, 0xea, 0xaf, 0xa3, 0x3d, 0x67,
	0x59, 0x4f, 0xfd, 0x35, 0xd7, 0x53, 0xf9, 0x91, 0x04, 0x1b, 0x2d, 0xfd, 0x1c, 0x45, 0xf9, 0x3b,
	0xe1, 0x31, 0xff, 0xe4, 0xdb, 0x9c, 0xd0, 0x81, 0xd3, 0x09, 0x1d, 0xa8, 0x6c, 0xc3, 0xad, 0x24,
	0xa5, 0xbc, 0x24, 0x28, 0x7f, 0x26, 0xc1, 0x26, 0x3d, 0x62, 0xff, 0x07, 0x96, 0xa3, 0xc0, 0x76,
	0x1a, 0xad, 0x91, 0x05, 0x19, 0x50, 0x66, 0xad, 0x07, 0x78, 0x01, 0x44, 0x0a, 0xca, 0x90, 0x1f,
	0x06, 0x84, 0xe3, 0x3f, 0x31, 0x2d, 0xa6, 0x75, 0x6e, 0x7a, 0x88, 0x3b, 0xc3, 0x45, 0x0a, 0xc1,
	0xf7, 0xe4, 0x16, 0x94, 0x3a, 0x0e, 0xd2, 0xbd, 0xe0, 0x76, 0x21, 0x67, 0x9c, 0x82, 0x08, 0x25,
	0x87, 0xf0, 0xc6, 0x01, 0xf2, 0x0e, 0x1c, 0xdb, 0xee, 0xbb, 0x7d, 0xdd, 0xda, 0xb3, 0x8c, 0x7d,
	0xc7, 0x34, 0x90, 0xdb, 0xd7, 0x4d, 0xe3, 0x2a, 0x4a, 0xff, 0x8f, 0xf3, 0x70, 0x77, 0x04, 0x42,
	0x76, 0x74, 0x8f, 0x60, 0xe9, 0x24, 0x68, 0xd1, 0x7a, 0xa6, 0xeb, 0xad, 0x4b, 0xe4, 0x88, 0xbc,
	0x99, 0x7e, 0x44, 0xe2, 0xcc, 0x50, 0x17, 0xc3, 0xe1, 0xcf, 0x4c, 0xd7, 0x93, 0x9f, 0xc3, 0x62,
	0xd7, 0x9f, 0x96, 0xe2, 0xcb, 0x4d, 0x84, 0x6f, 0x21, 0x18, 0x4d, 0xd0, 0x35, 0x60, 0x9e, 0xf2,
	0x91, 0x11, 0x97, 0x9f, 0x08, 0x59, 0x89, 0x8d, 0x25, 0xa8, 0x0e, 0xa0, 0xa4, 0x77, 0x1d, 0xe4,
	0x63, 0x9a, 0x9e, 0x08, 0x13, 0xd0, 0xa1, 0x3e, 0x4d, 0x0e, 0x3a, 0x1d, 0xba, 0x3e, 0xa6, 0x99,
	0xc9, 0x68, 0x62, 0x63, 0x31, 0x2a, 0xa5, 0x0e, 0x9b, 0xa1, 0x6a, 0x7d, 0xe2, 0x98, 0xc8, 0xba,
	0xd2, 0x7e, 0xff, 0x6b, 0x8e, 0xbf, 0x24, 0x78, 0x3c, 0x6c, 0x9b, 0x0f, 0xa0, 0x74, 0x4a, 0xa0,
	0x57, 0xd9, 0x62, 0xa0, 0x43, 0x53, 0xf7, 0x23, 0xf7, 0xa9, 0xed, 0x47, 0xfe, 0x53, 0xdb, 0x8f,
	0xe9, 0x2b, 0xef, 0x07, 0xbe, 0xc9, 0xb0, 0xda, 0x66, 0xbc, 0xb2, 0x86, 0x7d, 0xa6, 0xde, 0xe7,
	0xfb, 0xfa, 0x6b, 0xca, 0xd6, 0xc3, 0x61, 0x5f, 0xf9, 0x3d, 0x09, 0x36, 0x1a, 0x64, 0x25, 0x3c,
	0xb6, 0x49, 0x75, 0xdc, 0x43, 0x90, 0xfd, 0x7e, 0x5d, 0x3c, 0x90, 0x5e, 0xe0, 0x54, 0x79, 0x94,
	0x2f, 0x38, 0xc4, 0xe4, 0x1e, 0x67, 0x4a, 0x27, 0x1f, 0x2a, 0x9d, 0x4d, 0x28, 0x9a, 0xae, 0xd6,
	0xd1, 0xad, 0x0e, 0xea, 0x31, 0xa3, 0xb4, 0x60, 0xba, 0x55, 0xf2, 0xad, 0xdc, 0x80, 0x4a, 0x1a,
	0x85, 0x4c, 0xab, 0x0d, 0xa0, 0xf2, 0x54, 0xb7, 0x8c, 0x9e, 0xdf, 0x4a, 0xbb, 0xfa, 0x0b, 0xc0,
	0x88, 0xa9, 0x36, 0x0b, 0x48, 0x2f, 0x50, 0x40, 0xc3, 0x90, 0xef, 0xc0, 0xc2, 0x19, 0x19, 0x8a,
	0x8d, 0x26, 0x6f, 0xe8, 0x32, 0x82, 0xe7, 0x29, 0xb0, 0x45, 0x60, 0x49, 0x62, 0x95, 0x9b, 0xb0,
	0x99, 0x3a, 0x23, 0x23, 0xe8, 0x7b, 0x12, 0xac, 0xd6, 0x50, 0xef, 0x7f, 0x91, 0x9d, 0xca, 0x06,
	0xac, 0x25, 0x28, 0x60, 0xd4, 0xfd, 0x54, 0x82, 0xa5, 0x98, 0x6d, 0x20, 0xb4, 0x78, 0xf1, 0x84,
	0xe9, 0x16, 0xef, 0x03, 0x58, 0x36, 0x5d, 0xed, 0x74, 0xd8, 0xeb, 0x05, 0x2e, 0x13, 0x25, 0xa0,
	0xa0, 0x2e, 0x99, 0xee, 0x13, 0x0c, 0x67, 0x93, 0x90, 0xbd, 0x25, 0x7a, 0x90, 0xdc, 0x27, 0xd4,
	0x26, 0x2e, 0x10, 0xc0, 0x31, 0xdd, 0x78, 0xa2, 0x74, 0x49, 0xe3, 0x0c, 0xf3, 0xc9, 0x30, 0x00,
	0x37, 0x86, 0x7e, 0xca, 0x2c, 0xe7, 0xa7, 0x28, 0x6b, 0x70, 0xfd, 0x00, 0x79, 0x7b, 0xbd, 0x5e,
	0x1b, 0x5b, 0x86, 0xd5, 0x53, 0xdf, 0xf7, 0x53, 0x7e, 0x2c, 0xc1, 0x35, 0xb6, 0xe8, 0x9a, 0x83,
	0x5c, 0xb7, 0xe9, 0xa0, 0x73, 0x13, 0x5d, 0xc8, 0x2b, 0x30, 0xd3, 0x43, 0xe7, 0xa8, 0xc7, 0x98,
	0x4d, 0x3f, 0xf0, 0x45, 0x87, 0x99, 0xeb, 0xf2, 0xcc, 0x2d, 0x12, 0x08, 0xe1, 0xea, 0x11, 0xf8,
	0x9b, 0xa2, 0x19, 0x18, 0x19, 0xf3, 0x06, 0x1f, 0x64, 0x1e, 0xc5, 0x3d, 0xcb, 0xec, 0xeb, 0x98,
	0x43, 0xe4, 0x38, 0xce, 0x5f, 0x70, 0xc4, 0xe0, 0xf9, 0x08, 0x22, 0xcd, 0x43, 0xaf, 0xa9, 0xe7,
	0x55, 0x54, 0x8b, 0x04, 0xd2, 0x46, 0xaf, 0x3d, 0xc5, 0x81, 0xb5, 0x14, 0xda, 0xc9, 0x49, 0x7e,
	0x01, 0x32, 0x1d, 0x39, 0xa0, 0x40, 0x5e, 0xf1, 0xdd, 0xcf, 0xa4, 0x87, 0x47, 0xa5, 0x96, 0x8d,
	0x18, 0x62, 0xec, 0x80, 0xca, 0x4f, 0x4c, 0xcb, 0x74, 0xcf, 0x7c, 0xfa, 0x2f, 0x74, 0xc7, 0x90,
	0x5b, 0xb0, 0xa4, 0xe3, 0x3f, 0x34, 0xdd, 0x5f, 0x0e, 0xe1, 0xdc, 0x64, 0x8b, 0x5f, 0x24, 0x28,
	0x02, 0x18, 0x71, 0xcc, 0xed, 0x01, 0x5d, 0x3c, 0x15, 0xac, 0x39, 0xcf, 0x1e, 0xe0, 0xa5, 0x63,
	0x9b, 0xe2, 0xc4, 0xf6, 0x3c, 0xbb, 0x4f, 0x5b, 0xf3, 0xa4, 0x15, 0x28, 0x88, 0xf0, 0xe6, 0x20,
	0xb0, 0x5c, 0x9a, 0x8e, 0xd9, 0xa1, 0x42, 0xbb, 0x02, 0x33, 0x03, 0xfc, 0xe1, 0x6f, 0x2a, 0xf9,
	0xc0, 0x4c, 0x26, 0x7f, 0x44, 0x36, 0x95, 0x40, 0xf0, 0xa6, 0x2a, 0x7f, 0x3d, 0x0b, 0x05, 0x5f,
	0x6a, 0xb2, 0x42, 0x05, 0x81, 0x23, 0x62, 0xe9, 0x7d, 0xc4, 0xc8, 0xa5, 0x8e, 0xc8, 0xa1, 0xde,
	0x47, 0x72, 0xdd, 0x9f, 0x85, 0x0b, 0x13, 0x64, 0xeb, 0xe8, 0x80, 0x6e, 0x46, 0x0d, 0x59, 0xc2,
	0x0b, 0x90, 0xdd, 0x0e, 0xb2, 0x50, 0x74, 0x5f, 0xa7, 0xc7, 0xd8, 0xd7, 0x16, 0x1e, 0x16, 0xec,
	0xab, 0xcb, 0x7d, 0x11, 0x81, 0xd1, 0x60, 0x39, 0x2a, 0x30, 0x7d, 0x7d, 0xc0, 0xae, 0xf6, 0x77,
	0xd3, 0xf1, 0xfa, 0x4c, 0xd9, 0xe1, 0x25, 0xe6, 0xb9, 0x3e, 0xa8, 0x5b, 0x9e, 0x73, 0xa9, 0x2e,
	0x19, 0x51, 0xa8, 0xfc, 0x2e, 0xac, 0xe2, 0xbb, 0xc5, 0x3d, 0xb3, 0x2f, 0xb4, 0xd0, 0x44, 0xc2,
	0x77, 0x0c, 0x75, 0x0c, 0xae, 0xf5, 0xf5, 0xd7, 0xad, 0x33, 0xfb, 0x22, 0xb0, 0xda, 0x0e, 0x87,
	0x7d, 0xf9, 0x11, 0x5c, 0x0b, 0x06, 0x9d, 0xea, 0x7d, 0xb3, 0x77, 0x49, 0x46, 0x30, 0x2f, 0x95,
	0x8d, 0x78, 0x42, 0x1a, 0x70, 0xf7, 0x3b, 0xb0, 0xe0, 0xa2, 0x1e, 0xea, 0xe0, 0xfb, 0xd9, 0xec,
	0xd8, 0x16, 0x71, 0x51, 0x8b, 0xea, 0xbc, 0x0f, 0x6c, 0x74, 0x6c, 0x4b, 0x7e, 0x0b, 0x96, 0x86,
	0x56, 0xb4, 0x5b, 0x91, 0x74, 0x5b, 0x0c, 0xc1, 0xa4, 0xa3, 0x8a, 0x7d, 0x5e, 0x22, 0xd4, 0x78,
	0xcf, 0x28, 0xa7, 0x81, 0x70, 0xe4, 0x5e, 0x3a, 0x47, 0x92, 0xe7, 0x02, 0x7b, 0xc7, 0xf8, 0x5f,
	0xbc, 0x77, 0x84, 0xcd, 0x6f, 0x83, 0x8c, 0x17, 0x74, 0x62, 0x76, 0x35, 0xb7, 0xe3, 0x20, 0x44,
	0x39, 0x50, 0xa2, 0x8e, 0x7c, 0x5f, 0x7f, 0xbd, 0x6f, 0x76, 0x5b, 0x04, 0x8e, 0x97, 0x73, 0x1f,
	0xca, 0x54, 0xa4, 0x4e, 0xf4, 0xce, 0xcb, 0xae, 0x63, 0x0f, 0x2d, 0x63, 0x7d, 0x9e, 0x90, 0xba,
	0x44, 0xe0, 0xfb, 0x01, 0x18, 0xab, 0x7f, 0xda, 0xd5, 0xdf, 0x3e, 0x72, 0x2c, 0x16, 0x48, 0x67,
	0x8a, 0x84, 0x6d, 0x05, 0x3e, 0x1c, 0x95, 0x57, 0xb0, 0x92, 0xb6, 0x69, 0xf8, 0x5a, 0x78, 0x89,
	0x2e, 0x7d, 0xd3, 0xfe, 0x25, 0xba, 0x94, 0xab, 0x30, 0x73, 0xae, 0xf7, 0x86, 0x54, 0xa0, 0x4b,
	0xbb, 0x8f, 0xc6, 0x56, 0x1d, 0x78, 0xb5, 0x2a, 0x1d, 0xfb, 0x51, 0xee, 0x43, 0x49, 0xf9, 0x2a,
	0xc8, 0x7b, 0x86, 0x11, 0x53, 0xbf, 0xf2, 0xe7, 0x81, 0x1e, 0x11, 0xad, 0x73, 0xda, 0x65, 0x0a,
	0xe3, 0x56, 0xb6, 0xb4, 0xa9, 0xf4, 0x00, 0x56, 0x4f, 0xbb, 0xca, 0x75, 0xb8, 0x16, 0x41, 0xc9,
	0xae, 0xab, 0x36, 0x5c, 0x3f, 0x1e, 0x60, 0xb7, 0xe9, 0x53, 0x9d, 0x6c, 0x1d, 0x56, 0xe3, 0x58,
	0xd9, 0x7c, 0xbb, 0x70, 0xbd, 0x86, 0x7a, 0x28, 0x39, 0x9f, 0x58, 0x59, 0x60, 0x6c, 0xf1, 0x31,
	0x0c, 0xdb, 0xdf, 0x48, 0xb0, 0x92, 0xa6, 0x1c, 0xe5, 0xbb, 0xb0, 0x18, 0xe8, 0x56, 0x3e, 0xd8,
	0xb1, 0x10, 0x40, 0xc9, 0x1d, 0xf4, 0x08, 0xe4, 0xb0, 0x9b, 0x83, 0x68, 0xec, 0x90, 0xa9, 0xa3,
	0xe5, 0xa0, 0x45, 0x65, 0x0d, 0xf2, 0x7b, 0xb0, 0x9a, 0xec, 0xae, 0xf5, 0x8d, 0xf7, 0x99, 0x4a,
	0x5d, 0x49, 0x0c, 0x79, 0x6e, 0xbc, 0x8f, 0xcf, 0x59, 0x38, 0x6a, 0x60, 0x75, 0xd9, 0xd5, 0x34,
	0x1f, 0x00, 0x9b, 0x56, 0x57, 0xf9, 0x59, 0x78, 0xb5, 0xf2, 0xba, 0x47, 0x70, 0xb5, 0xb6, 0x60,
	0x89, 0x2a, 0xb6, 0xf0, 0x02, 0xc9, 0x4d, 0x7e, 0x81, 0x10, 0x14, 0xe1, 0x05, 0x72, 0x13, 0xc0,
	0xed, 0xeb, 0xbd, 0x1e, 0x3d, 0xe5, 0x74, 0x45, 0x45, 0x02, 0x21, 0x07, 0xfc, 0xab, 0xb0, 0xf8,
	0x6d, 0x6c, 0x65, 0x84, 0x53, 0x4e, 0x4f, 0x3c, 0xe5, 0x02, 0xc6, 0x10, 0x80, 0x94, 0x63, 0x58,
	0x8d, 0x1b, 0x1a, 0xcc, 0x07, 0x89, 0x49, 0x5f, 0x7e, 0x22, 0xe9, 0x6b, 0xc2, 0x0a, 0xb5, 0x6c,
	0x63, 0xa1, 0xeb, 0xa4, 0x2f, 0x9e, 0x62, 0x2f, 0xe6, 0xd2, 0x7c, 0xa6, 0x35, 0xb8, 0x1e, 0xc3,
	0xc8, 0x04, 0xb0, 0x0a, 0x1b, 0x7b, 0x46, 0xdf, 0xb4, 0x52, 0xe7, 0x1b, 0xd7, 0x23, 0xbb, 0x01,
	0x95, 0x34, 0x24, 0x6c, 0x8a, 0xb7, 0x61, 0x8d, 0x8f, 0xc9, 0xf2, 0x2e, 0x5f, 0x62, 0x41, 0x8a,
	0x0e, 0xa5, 0xa6, 0xee, 0x78, 0x16, 0x72, 0x04, 0xd1, 0x07, 0xea, 0x08, 0xe8, 0x9d, 0x0e, 0x1a,
	0x50, 0x33, 0x81, 0x38, 0x02, 0x7b, 0xe4, 0x1b, 0x4b, 0xaa, 0x67, 0x77, 0x91, 0x77, 0x86, 0x1c,
	0x3e, 0xfa, 0x30, 0xef, 0x03, 0x49, 0xfc, 0xe1, 0xa7, 0x12, 0xac, 0x27, 0x09, 0x62, 0xfb, 0x56,
	0x83, 0xf9, 0x01, 0x9d, 0x9f, 0x5e, 0xdd, 0x54, 0x71, 0x08, 0xa2, 0x71, 0x1c, 0xa5, 0x6a, 0x69,
	0xc0, 0x91, 0x3d, 0xe6, 0xb6, 0x44, 0x74, 0x46, 0x3e, 0xe3, 0x2d, 0x62, 0x3a, 0xfa, 0x16, 0xa1,
	0xfc, 0x3c, 0x0f, 0x4b, 0x2f, 0x38, 0x4c, 0x82, 0x39, 0x53, 0x5d, 0x87, 0x88, 0xb5, 0x9d, 0xcb,
	0xb2, 0xb6, 0xf3, 0x31, 0x6b, 0x9b, 0xa7, 0x76, 0x3a, 0x4a, 0xad, 0x02, 0x0b, 0xa6, 0xab, 0xd1,
	0xd6, 0x53, 0x07, 0xd1, 0x40, 0x61, 0x41, 0x2d, 0x99, 0x2e, 0x11, 0xef, 0x27, 0x0e, 0xc2, 0xf6,
	0xf2, 0x7c, 0xe4, 0xf1, 0x64, 0x96, 0xb0, 0xf6, 0x61, 0xb6, 0x55, 0xd4, 0xd3, 0x2d, 0x3e, 0x52,
	0xe5, 0x87, 0xb4, 0xc8, 0x8a, 0x1f, 0x82, 0x1c, 0x9a, 0x16, 0x43, 0x3f, 0xa2, 0x33, 0xb7, 0x9d,
	0xc7, 0xd6, 0x42, 0xd0, 0x72, 0xcc, 0x62, 0x35, 0x3b, 0x70, 0x8d, 0x0b, 0xfe, 0x04, 0xdd, 0x0b,
	0xa4, 0xfb, 0x72, 0xd8, 0xe4, 0xf7, 0xdf, 0x80, 0xc2, 0x99, 0xed, 0x7a, 0x84, 0x13, 0x45, 0xba,
	0x5a, 0xfc, 0x8d, 0x19, 0xf1, 0x15, 0x58, 0xe2, 0xae, 0x74, 0xce, 0x52, 0xb8, 0x23, 0x78, 0x09,
	0xf2, 0xaf, 0xf9, 0x86, 0x87, 0xfa, 0xea, 0xc2, 0x89, 0xff, 0x49, 0xe6, 0x59, 0x85, 0x59, 0xe6,
	0x5c, 0x52, 0xbb, 0x80, 0x7d, 0x71, 0xbe, 0xcd, 0x3c, 0xef, 0xdb, 0x98, 0xb0, 0x9a, 0xce, 0x9b,
	0x51, 0xb1, 0xf7, 0x0d, 0x28, 0xb0, 0xc8, 0xbf, 0xef, 0xc6, 0xce, 0x91, 0xef, 0x36, 0x99, 0x8a,
	0x84, 0xf9, 0x5d, 0xb6, 0xe5, 0x33, 0xc8, 0x32, 0xda, 0xae, 0x72, 0x04, 0x37, 0xf6, 0x75, 0x2f,
	0x0c, 0xb5, 0xe0, 0x59, 0xf6, 0x2f, 0x1b, 0x86, 0x7f, 0x7a, 0x1f, 0xc3, 0x4a, 0x4c, 0xe2, 0x42,
	0xbf, 0x63, 0x41, 0x5d, 0x8e, 0x88, 0x1d, 0xf1, 0x26, 0xfe, 0x4b, 0x82, 0x9b, 0x02, 0x8c, 0xec,
	0xf8, 0xbd, 0x02, 0xdf, 0x7b, 0xa5, 0x56, 0x18, 0x36, 0x4b, 0xa9, 0xf6, 0x3c, 0x10, 0xb0, 0x36,
	0x0b, 0xdd, 0x0e, 0x07, 0x0f, 0x4c, 0xd5, 0xc5, 0x8b, 0x08, 0xb0, 0x72, 0x16, 0xdc, 0x5b, 0x7c,
	0xb7, 0x14, 0xe3, 0xe8, 0xf3, 0x51, 0xe3, 0xe8, 0xee, 0x48, 0xc1, 0x25, 0x12, 0xcb, 0x19, 0x45,
	0x03, 0xd8, 0x6a, 0xea, 0x5d, 0x74, 0x80, 0xbc, 0xaa, 0xdd, 0x37, 0xad, 0x2e, 0xeb, 0x4a, 0x6c,
	0xa7, 0xd0, 0x88, 0x18, 0xe8, 0x5d, 0x44, 0xcc, 0x44, 0x66, 0x44, 0xe0, 0x6f, 0x6c, 0x1e, 0x6e,
	0x42, 0x91, 0x34, 0xb9, 0xe6, 0xb7, 0x7d, 0xbf, 0x85, 0xf4, 0x6d, 0x99, 0xdf, 0x26, 0x0f, 0x76,
	0x96, 0x7d, 0xe1, 0xef, 0x60, 0x5e, 0x9d, 0xb1, 0xec, 0x8b, 0xb6, 0xab, 0x7c, 0x5f, 0x82, 0x6d,
	0xf1, 0x94, 0x8c, 0xe7, 0x4f, 0xc1, 0x77, 0x43, 0x79, 0xb7, 0x71, 0xcc, 0xe5, 0x95, 0x2e, 0x42,
	0x8c, 0xe4, 0xc8, 0xe8, 0xae, 0xd6, 0xb7, 0x1d, 0xc4, 0x54, 0xf3, 0xdc, 0x99, 0xee, 0x3e, 0xb7,
	0x1d, 0xa4, 0xb4, 0x60, 0xad, 0x35, 0x3c, 0x71, 0x3b, 0x8e, 0x79, 0x82, 0x3e, 0xb5, 0x5b, 0xad,
	0x02, 0xeb, 0x49, 0xa4, 0xec, 0xd6, 0x39, 0x83, 0x3b, 0x11, 0xd9, 0x08, 0x3a, 0xd2, 0xa8, 0x8d,
	0x78, 0x72, 0x91, 0x54, 0xe7, 0x44, 0x52, 0xfd, 0xef, 0x12, 0xbc, 0x91, 0x3d, 0x15, 0x63, 0xf4,
	0x2f, 0x4b, 0xb0, 0xe2, 0xfa, 0x6d, 0x2c, 0xa2, 0xc4, 0x49, 0xb8, 0x3a, 0x86, 0x84, 0x0b, 0x50,
	0xef, 0xc4, 0xe0, 0x81, 0xb0, 0xcb, 0x6e, 0xa2, 0xa1, 0x52, 0xe7, 0xb6, 0x22, 0xda, 0x3d, 0x45,
	0xe8, 0x57, 0x78, 0xa1, 0x2f, 0xf0, 0xd2, 0xfc, 0xdb, 0x12, 0x2c, 0x44, 0x14, 0x9b, 0xbc, 0x06,
	0x73, 0x66, 0xbf, 0xab, 0x0d, 0x1d, 0x6a, 0xec, 0x15, 0xd5, 0x59, 0xb3, 0xdf, 0x3d, 0x76, 0x7a,
	0xf2, 0x01, 0x2c, 0x30, 0x3f, 0x85, 0x0b, 0xa3, 0x2d, 0xee, 0x2a, 0xa2, 0x87, 0x29, 0xdc, 0x95,
	0x2d, 0x70, 0xde, 0xe1, 0xbe, 0xf0, 0x35, 0x33, 0x1c, 0xf4, 0x6c, 0xdd, 0xd0, 0x4e, 0x2e, 0xb9,
	0x2b, 0xaa, 0x44, 0x81, 0xfb, 0x97, 0xf8, 0x6e, 0xac, 0x41, 0x25, 0xe4, 0x57, 0x40, 0xe0, 0xa4,
	0x26, 0xcd, 0xb7, 0xf8, 0x58, 0x35, 0x87, 0x85, 0x6d, 0x65, 0xca, 0x0d, 0x20, 0x5d, 0xf5, 0x06,
	0x50, 0xfe, 0x49, 0x82, 0x4d, 0xee, 0xa9, 0xe9, 0xaa, 0x34, 0x93, 0xe7, 0x8d, 0x18, 0x51, 0xb9,
	0xb1, 0x89, 0xda, 0xcf, 0xad, 0x4b, 0xf1, 0xab, 0x89, 0xdb, 0xd0, 0x7c, 0x64, 0x43, 0xe9, 0xdd,
	0x64, 0x04, 0xa1, 0xd8, 0x19, 0xd3, 0xad, 0xa1, 0x9e, 0x7f, 0x98, 0x66, 0x42, 0x73, 0xee, 0x16,
	0xdc, 0x48, 0x5f, 0x59, 0xe8, 0xff, 0xdc, 0xa7, 0x8e, 0x56, 0xb2, 0x0b, 0x27, 0x05, 0x13, 0x32,
	0x82, 0xa3, 0x3b, 0x97, 0x2d, 0x88, 0xf9, 0x2b, 0x0a, 0x22, 0x5b, 0xe9, 0x74, 0xb8, 0xd2, 0x87,
	0xf0, 0x60, 0x9c, 0x85, 0xb0, 0x75, 0xbf, 0x84, 0x5b, 0x7b, 0x83, 0x41, 0xef, 0xb2, 0x6e, 0x19,
	0x81, 0xe2, 0xea, 0x31, 0xc7, 0x4b, 0xa4, 0x98, 0xb6, 0xc0, 0xb7, 0x31, 0xf9, 0xc7, 0x73, 0x06,
	0xc2, 0x66, 0x09, 0xb6, 0x24, 0xa8, 0x03, 0x98, 0x67, 0x96, 0x04, 0xf9, 0x52, 0xbe, 0x09, 0x5b,
	0xc2, 0xc9, 0x98, 0x3c, 0x7f, 0x04, 0x1b, 0xd8, 0x02, 0x70, 0x18, 0xdc, 0x3d, 0x33, 0x07, 0x9a,
	0x81, 0x74, 0xa3, 0x67, 0x5a, 0xd4, 0xf3, 0xcc, 0xab, 0x6b, 0xc8, 0x32, 0x54, 0xae, 0xbd, 0xc6,
	0x9a, 0x95, 0x1e, 0x6c, 0x51, 0xc3, 0xff, 0x7f, 0x64, 0x31, 0x0a, 0x6c, 0x8b, 0x67, 0x63, 0xdc,
	0xfd, 0x5b, 0x09, 0x36, 0x0e, 0x90, 0xe7, 0x07, 0x98, 0x63, 0xf7, 0x4d, 0xdc, 0x4e, 0xca, 0x27,
	0xec, 0xa4, 0xe0, 0x0a, 0xce, 0x65, 0x5c, 0xc1, 0xf9, 0xd8, 0x15, 0x8c, 0xbd, 0xcf, 0x30, 0xb3,
	0x82, 0x4a, 0x46, 0xd1, 0x0d, 0x52, 0x2a, 0x36, 0xa0, 0x10, 0xa4, 0x52, 0xd0, 0x03, 0x32, 0x87,
	0x68, 0x0e, 0x05, 0x46, 0x6b, 0xe8, 0x9e, 0x4e, 0xdd, 0x7c, 0x1a, 0x1e, 0x2b, 0x60, 0x00, 0x09,
	0x48, 0xfe, 0x40, 0x22, 0xfa, 0x2c, 0xb1, 0x16, 0xb6, 0x71, 0xdf, 0x84, 0xeb, 0x7e, 0x78, 0x5d,
	0x4b, 0xb9, 0xc5, 0xef, 0x67, 0xbe, 0xfd, 0x1b, 0xbc, 0x07, 0x74, 0xcd, 0x89, 0x02, 0x89, 0x06,
	0x58, 0x81, 0x19, 0xcf, 0xf6, 0xf4, 0x1e, 0xe3, 0x04, 0xfd, 0x50, 0x7e, 0x96, 0x87, 0x6b, 0x29,
	0x28, 0xc6, 0x3e, 0x9f, 0xb1, 0x27, 0xe2, 0x5c, 0xfc, 0x89, 0x78, 0xd4, 0x73, 0x77, 0x7a, 0x32,
	0xcb, 0xf4, 0x04, 0xc9, 0x2c, 0x33, 0x69, 0xc9, 0x2c, 0x51, 0xd7, 0x68, 0x36, 0xcb, 0x35, 0x9a,
	0x4b, 0xba, 0x46, 0x81, 0xb3, 0x50, 0x88, 0x3a, 0x0b, 0xd1, 0x48, 0x71, 0x31, 0x1e, 0x29, 0x8e,
	0xf8, 0x79, 0x10, 0xcb, 0x39, 0xdb, 0x81, 0x6b, 0x3e, 0x1f, 0xf8, 0xbc, 0x19, 0xea, 0x28, 0x2c,
	0xb3, 0xa6, 0xe7, 0x41, 0xfa, 0x0c, 0x43, 0xa6, 0xd1, 0xb0, 0xf7, 0x7c, 0x80, 0x8c, 0xc4, 0x96,
	0x39, 0x87, 0x62, 0x81, 0x4f, 0xea, 0x7a, 0x13, 0x96, 0xb0, 0x43, 0x8d, 0xfd, 0x77, 0xff, 0x7d,
	0x6d, 0x91, 0xb4, 0x2f, 0x98, 0x2e, 0xe7, 0xd5, 0x2b, 0xbf, 0x2b, 0xc1, 0xf5, 0x56, 0x70, 0x25,
	0x3e, 0xb5, 0x27, 0x7f, 0xb4, 0xe2, 0x99, 0x94, 0x8b, 0x32, 0xe9, 0x36, 0xcc, 0xdb, 0x03, 0xe4,
	0xe8, 0x9e, 0xed, 0xf0, 0xf7, 0xba, 0x0f, 0x63, 0x7c, 0xe4, 0x64, 0x62, 0x3a, 0x9e, 0xb8, 0xb3,
	0x0e, 0xab, 0x71, 0xea, 0x98, 0x36, 0xd8, 0xe2, 0x33, 0x7a, 0x30, 0x29, 0xfb, 0xba, 0xcb, 0x27,
	0x15, 0x29, 0xaf, 0x48, 0xfe, 0x59, 0x6a, 0x07, 0x76, 0xca, 0x7c, 0xc3, 0xd6, 0xcf, 0x17, 0x63,
	0x86, 0xad, 0xee, 0xf9, 0x5c, 0x1d, 0xe8, 0x97, 0xbe, 0x85, 0x64, 0xba, 0x4d, 0xfd, 0x92, 0x24,
	0x49, 0xb8, 0xfe, 0xcb, 0x17, 0x7b, 0xf8, 0x2a, 0x9a, 0x2e, 0x3b, 0x35, 0xca, 0xbb, 0x24, 0x70,
	0xe4, 0x87, 0x7e, 0x78, 0xa7, 0x2a, 0x23, 0x8c, 0xf8, 0x31, 0x09, 0xa4, 0x44, 0x07, 0xa5, 0x87,
	0x9b, 0x26, 0x0b, 0x76, 0x7e, 0x91, 0x3c, 0x97, 0x35, 0x1d, 0x7b, 0x60, 0xbb, 0x88, 0xf7, 0x46,
	0xde, 0x84, 0x25, 0x4f, 0x77, 0xba, 0xc8, 0x0b, 0xdd, 0x65, 0xaa, 0x2e, 0x17, 0x28, 0x98, 0xb9,
	0xca, 0xca, 0xaf, 0x4a, 0x50, 0x62, 0xc3, 0x8f, 0x5d, 0xe4, 0x10, 0x91, 0x0f, 0xc6, 0x05, 0x59,
	0x5a, 0xfe, 0x10, 0xce, 0xe3, 0xcd, 0x45, 0x3c, 0xde, 0xdb, 0x30, 0x7f, 0x8a, 0x7a, 0x3d, 0xfb,
	0x42, 0x1b, 0xd8, 0xa6, 0xe5, 0xf9, 0x42, 0x40, 0x61, 0x4d, 0x0c, 0xa2, 0xaf, 0x37, 0x64, 0x22,
	0x5f, 0x08, 0x8a, 0x6a, 0x91, 0x41, 0x1a, 0x86, 0xe2, 0x10, 0xb6, 0x46, 0x56, 0xc2, 0xc5, 0x75,
	0xd8, 0x40, 0x4e, 0x3d, 0x8a, 0xe2, 0x3a, 0xe1, 0x5a, 0xd4, 0xd2, 0x20, 0xc4, 0x26, 0xcb, 0x30,
	0xed, 0x99, 0x03, 0x97, 0x98, 0x55, 0x45, 0x95, 0xfc, 0xad, 0x9c, 0x80, 0xdc, 0x42, 0x96, 0xc1,
	0xc6, 0xf0, 0x89, 0x6a, 0x19, 0x2c, 0x08, 0x11, 0x49, 0x3e, 0x22, 0xbc, 0xf3, 0xa7, 0x0e, 0x53,
	0x3e, 0x2c, 0x18, 0x84, 0xbf, 0xb1, 0x4d, 0xfb, 0x1e, 0x5c, 0x8b, 0xcc, 0xc1, 0x16, 0x15, 0xe5,
	0x86, 0x14, 0xe7, 0xc6, 0x2b, 0x58, 0xa1, 0xcf, 0xd0, 0x49, 0xda, 0x32, 0x86, 0x65, 0x47, 0xd8,
	0x6e, 0x02, 0xb0, 0x17, 0xf1, 0x90, 0xcc, 0x22, 0x85, 0x60, 0x42, 0xd7, 0xe0, 0x7a, 0x6c, 0x4a,
	0x76, 0x08, 0x7f, 0x2e, 0x81, 0x1c, 0xbc, 0x74, 0xd1, 0x99, 0x58, 0xcc, 0x62, 0x04, 0x29, 0x94,
	0x25, 0x2e, 0x72, 0x7c, 0x9f, 0x97, 0xf0, 0x04, 0x4b, 0xd9, 0x16, 0x94, 0x7c, 0x16, 0xe3, 0x66,
	0x96, 0x68, 0xc4, 0x78, 0x8c, 0x3b, 0x84, 0x72, 0x36, 0x1d, 0x91, 0xb3, 0xd8, 0xf5, 0x33, 0x93,
	0xb8, 0x7e, 0xf8, 0xbb, 0x7a, 0x36, 0x7a, 0x57, 0xdf, 0x04, 0x40, 0xaf, 0x07, 0xa6, 0x83, 0x34,
	0x43, 0xbf, 0x64, 0xd7, 0x40, 0x91, 0x42, 0x6a, 0xfa, 0x65, 0xb0, 0xaf, 0x85, 0x70, 0x5f, 0x95,
	0x0f, 0xf8, 0xe3, 0xc5, 0x1f, 0xf5, 0x11, 0xdb, 0xf7, 0x0d, 0x5e, 0x98, 0x23, 0xa7, 0x7d, 0x1f,
	0xe6, 0x58, 0x37, 0x16, 0x8b, 0xb8, 0x37, 0xe2, 0x69, 0x31, 0x60, 0xb8, 0xea, 0x0f, 0x54, 0x76,
	0x09, 0x55, 0x29, 0x92, 0xcb, 0x8b, 0xa1, 0x14, 0x15, 0x43, 0x4a, 0x51, 0x9a, 0x24, 0x72, 0x14,
	0x49, 0x57, 0xa5, 0xe8, 0x43, 0xd8, 0xdc, 0xd7, 0xbd, 0xce, 0xd9, 0x01, 0xf2, 0x9e, 0xeb, 0x8e,
	0x63, 0xea, 0xdd, 0x48, 0xea, 0xe7, 0x06, 0x14, 0x62, 0x5a, 0x68, 0x6e, 0xc8, 0xf4, 0xcf, 0x3f,
	0x4a, 0x24, 0x52, 0x95, 0x32, 0x94, 0x91, 0xe7, 0xc2, 0x72, 0x9f, 0xc1, 0xc7, 0x8f, 0x2b, 0x09,
	0xd1, 0xed, 0xf0, 0xc0, 0xf0, 0x09, 0xb4, 0x1f, 0x85, 0x56, 0xf6, 0x61, 0x25, 0xad, 0xe3, 0x28,
	0x27, 0x7b, 0x81, 0x77, 0xb2, 0x1b, 0x24, 0x9f, 0xb9, 0x65, 0xf6, 0x07, 0x41, 0x32, 0x49, 0x10,
	0x7a, 0x99, 0xd0, 0xa3, 0xfd, 0x8f, 0x1c, 0x6c, 0x8b, 0x71, 0x31, 0x46, 0x45, 0x2c, 0x20, 0x29,
	0xcb, 0x02, 0xca, 0xc5, 0x2c, 0xa0, 0xbb, 0x89, 0x54, 0xb8, 0x3c, 0xbd, 0x2a, 0xa2, 0x29, 0x6e,
	0x6f, 0x25, 0x53, 0xf0, 0xa6, 0x49, 0xbf, 0x78, 0x6a, 0xdd, 0x9b, 0x49, 0x67, 0x76, 0x86, 0x68,
	0xdd, 0x98, 0x8f, 0x9a, 0x6e, 0x0b, 0xce, 0x4e, 0x60, 0x0b, 0xce, 0xa5, 0xda, 0x82, 0xa1, 0xb5,
	0x54, 0xe0, 0xad, 0xa5, 0x88, 0xb9, 0x56, 0x8c, 0x99, 0x6b, 0x5c, 0x7e, 0x3c, 0x44, 0xf2, 0xe3,
	0x3b, 0x50, 0x21, 0x02, 0xf5, 0xd4, 0xf6, 0x22, 0xc1, 0xdb, 0x8c, 0x1d, 0xcc, 0x27, 0xed, 0xa7,
	0x9b, 0x00, 0x17, 0xa6, 0x77, 0xa6, 0x75, 0xf4, 0xce, 0x99, 0x1f, 0x90, 0x29, 0x62, 0x48, 0x15,
	0x03, 0x94, 0xbf, 0x97, 0xd8, 0x01, 0x8a, 0xcf, 0xc2, 0xf6, 0xb6, 0x07, 0x4b, 0x67, 0xb6, 0x17,
	0x88, 0x79, 0x78, 0x04, 0x6a, 0x19, 0x47, 0x20, 0x1d, 0xd7, 0x4e, 0x08, 0x0e, 0xe4, 0x7f, 0xe1,
	0x8c, 0x87, 0x55, 0xbe, 0x04, 0x72, 0xb2, 0xd3, 0x44, 0x01, 0xa6, 0x1f, 0xe6, 0x01, 0x68, 0xe6,
	0x54, 0x55, 0x77, 0x8c, 0x68, 0xa2, 0x56, 0x2e, 0x96, 0xa8, 0x95, 0xc2, 0xc2, 0xfc, 0xa8, 0x07,
	0x97, 0xe9, 0xc4, 0x83, 0x4b, 0x28, 0xfa, 0x33, 0x59, 0xa2, 0x3f, 0x1b, 0x13, 0xfd, 0xfb, 0x61,
	0xd0, 0xda, 0xd0, 0x3d, 0xc4, 0x09, 0x95, 0x4f, 0x54, 0x8d, 0x81, 0xf9, 0xbc, 0x2d, 0xce, 0x98,
	0x2d, 0x44, 0xf2, 0xb6, 0xc2, 0x24, 0xf2, 0x2a, 0x14, 0x89, 0x3c, 0x11, 0xe9, 0x2f, 0x8e, 0x9b,
	0xe8, 0x77, 0x60, 0x9e, 0x7a, 0x6a, 0x01, 0x0f, 0xf4, 0xb3, 0xfc, 0x18, 0xdf, 0x3a, 0xba, 0x63,
	0x68, 0x27, 0x5d, 0x22, 0x9b, 0x45, 0x95, 0xa5, 0x36, 0x62, 0xde, 0xee, 0x77, 0xb1, 0xa7, 0x11,
	0x06, 0xde, 0x49, 0x6f, 0xe2, 0x8d, 0x32, 0x4f, 0xe3, 0x82, 0xcf, 0x63, 0x23, 0x6e, 0xe9, 0x97,
	0xa3, 0xa9, 0xc2, 0x78, 0x4e, 0xcc, 0x5c, 0x42, 0x6e, 0x18, 0x00, 0x24, 0xc7, 0xe1, 0xd8, 0xe9,
	0x11, 0xe6, 0xe2, 0x26, 0x03, 0xb9, 0x1d, 0x66, 0xf4, 0x90, 0xbe, 0x35, 0xe4, 0x76, 0x94, 0x3f,
	0x94, 0xf8, 0x60, 0x1b, 0x9d, 0xe4, 0x0a, 0x1a, 0xce, 0x8f, 0x32, 0xe4, 0xc2, 0x28, 0x83, 0x60,
	0x55, 0x79, 0xc1, 0xaa, 0xa2, 0x32, 0x36, 0x1d, 0x95, 0x31, 0x6c, 0xe5, 0xde, 0x48, 0x27, 0x93,
	0x1d, 0xb0, 0x3d, 0x28, 0x71, 0x9c, 0x66, 0x17, 0xe1, 0x76, 0xfa, 0x86, 0x85, 0x82, 0xad, 0x42,
	0xb8, 0x11, 0xf2, 0x1d, 0x58, 0x60, 0x28, 0xa2, 0x09, 0x87, 0x14, 0x48, 0x83, 0x48, 0xca, 0x73,
	0xd8, 0x6e, 0x51, 0x13, 0xc7, 0x8f, 0x7c, 0x3c, 0x35, 0x8d, 0x91, 0x71, 0xed, 0x35, 0x98, 0xb3,
	0x07, 0x7c, 0xd6, 0xd3, 0xac, 0x3d, 0x20, 0x5b, 0x59, 0x83, 0xdb, 0x19, 0xe8, 0xd8, 0xda, 0xb6,
	0xa0, 0x74, 0x86, 0xe5, 0x9f, 0x91, 0x45, 0xf1, 0xc2, 0x59, 0xd0, 0x51, 0xa9, 0xc2, 0x62, 0xcd,
	0x3c, 0xb7, 0x9d, 0x0e, 0xba, 0x7a, 0xd0, 0x47, 0x59, 0x86, 0xa5, 0x00, 0x09, 0x33, 0x1c, 0x1f,
	0x13, 0xd9, 0xc0, 0xd4, 0xb1, 0x96, 0x11, 0xeb, 0x54, 0x5c, 0xb2, 0x4b, 0x29, 0x03, 0xd8, 0x4a,
	0xee, 0x43, 0xd9, 0xa0, 0x0d, 0xf1, 0x08, 0xd7, 0x12, 0x83, 0xfb, 0x91, 0x2d, 0x7c, 0x5b, 0xe8,
	0x43, 0xcf, 0xd6, 0x82, 0xfe, 0xcc, 0xcb, 0xcb, 0xab, 0x8b, 0x18, 0xce, 0xf0, 0xd7, 0xf4, 0x4b,
	0x65, 0x10, 0xde, 0xf5, 0x3e, 0x13, 0x05, 0xef, 0xd7, 0x23, 0x03, 0x5f, 0x77, 0x61, 0xd1, 0x0f,
	0xc3, 0x69, 0x9d, 0xe0, 0x11, 0x3b, 0xaf, 0x2e, 0xf8, 0xd0, 0x2a, 0x06, 0x2a, 0x0f, 0xe9, 0x23,
	0x36, 0x9b, 0x74, 0x14, 0x53, 0xfe, 0x99, 0x46, 0xc4, 0xe2, 0xdd, 0x83, 0xbc, 0xf8, 0x00, 0x39,
	0xff, 0xea, 0x2d, 0x48, 0x8c, 0x48, 0x5b, 0xa8, 0x3a, 0xef, 0xf0, 0xcb, 0x4e, 0xe3, 0x71, 0x6e,
	0x7c, 0x1e, 0xe7, 0xd3, 0x78, 0x4c, 0xa2, 0xba, 0xae, 0x86, 0x45, 0x8e, 0x45, 0x9d, 0x67, 0x4d,
	0x17, 0x4b, 0x2a, 0x36, 0x92, 0x9e, 0xeb, 0xd6, 0x50, 0xef, 0x1d, 0xda, 0x9e, 0x79, 0xea, 0x3f,
	0xeb, 0x93, 0x4b, 0x7f, 0x52, 0x23, 0x49, 0x81, 0x6d, 0x31, 0x2a, 0x26, 0x91, 0x1d, 0xb8, 0x15,
	0x89, 0xf4, 0xe2, 0xa1, 0x57, 0x8b, 0x53, 0x6f, 0x40, 0xc1, 0x74, 0xb5, 0x53, 0xdb, 0xcf, 0xd9,
	0x29, 0xa8, 0x73, 0xa6, 0xfb, 0x04, 0x7f, 0x2a, 0xb7, 0x61, 0x4b, 0x38, 0x09, 0xa3, 0xe3, 0x47,
	0x39, 0x50, 0xda, 0xc8, 0xf5, 0x82, 0x44, 0x15, 0xcb, 0x3c, 0x47, 0x8e, 0xab, 0x3b, 0x97, 0x4d,
	0x7b, 0x30, 0x1c, 0xf8, 0xc4, 0x84, 0x6e, 0x12, 0x11, 0x26, 0x2a, 0xea, 0xcc, 0x4d, 0x22, 0x96,
	0xce, 0xd7, 0xa2, 0x8a, 0x61, 0x71, 0xf7, 0x8b, 0x82, 0xc8, 0xc1, 0xc8, 0xb9, 0x76, 0x8e, 0x88,
	0x46, 0xf1, 0x35, 0x4b, 0x4a, 0x1a, 0x77, 0x19, 0xf2, 0x78, 0x83, 0x59, 0xdc, 0xdc, 0xd0, 0x2f,
	0x95, 0x0e, 0xcc, 0xd2, 0x51, 0xf2, 0x2a, 0xc8, 0x47, 0x4d, 0xad, 0xfd, 0xf5, 0x66, 0x5d, 0x3b,
	0x3e, 0xac, 0x7f, 0xad, 0x59, 0xaf, 0xb6, 0xeb, 0xb5, 0xf2, 0x94, 0x7c, 0x1d, 0x96, 0x7d, 0xf8,
	0x41, 0xfd, 0x50, 0x6b, 0x1e, 0x35, 0x8f, 0x9b, 0x65, 0x49, 0x5e, 0x87, 0x15, 0x1f, 0x7c, 0x74,
	0xf8, 0xac, 0x71, 0x58, 0xd7, 0xea, 0x1f, 0xd7, 0x0f, 0xdb, 0xe5, 0x9c, 0x5c, 0x86, 0x79, 0xbf,
	0xa5, 0x79, 0xdc, 0x7a, 0x5a, 0xce, 0x2b, 0x77, 0xe1, 0x4e, 0x26, 0xf5, 0xe9, 0x99, 0x2a, 0xaa,
	0xdd, 0x13, 0x2b, 0x33, 0xa5, 0x13, 0xcd, 0x22, 0xa1, 0x9d, 0xc3, 0x1a, 0x43, 0x9f, 0x75, 0x8e,
	0xdd, 0x0b, 0x6a, 0x0c, 0x2f, 0xc2, 0xae, 0x29, 0x65, 0x7b, 0xb9, 0x64, 0xd9, 0x9e, 0xd2, 0x64,
	0x86, 0x23, 0x57, 0x92, 0xc6, 0x11, 0x25, 0x76, 0x89, 0xf0, 0x61, 0x89, 0x3e, 0x9d, 0xce, 0x0e,
	0xa8, 0x84, 0xff, 0x83, 0x14, 0xba, 0x59, 0x69, 0xa4, 0xdb, 0xa1, 0x31, 0x83, 0x49, 0xe7, 0xcc,
	0xc4, 0x7a, 0xb6, 0xa7, 0x94, 0x82, 0x6c, 0x87, 0x83, 0x25, 0xde, 0xdf, 0x19, 0xb0, 0xb2, 0x17,
	0xbc, 0xbf, 0xf3, 0xdd, 0x26, 0xf2, 0x92, 0xbe, 0x27, 0x91, 0x10, 0x1f, 0x33, 0x9c, 0xfc, 0xc8,
	0x34, 0x6f, 0x42, 0x7c, 0xf2, 0xa2, 0xcf, 0x68, 0xe5, 0x61, 0x3e, 0x56, 0x79, 0x88, 0x5d, 0xd0,
	0x5b, 0x22, 0x12, 0x18, 0x67, 0x75, 0x58, 0x4d, 0x94, 0xe8, 0xf1, 0xc1, 0xa8, 0x87, 0x99, 0x25,
	0x7f, 0xf1, 0x72, 0xbd, 0x6b, 0xb1, 0x72, 0x3d, 0xb2, 0xeb, 0x69, 0x25, 0x7b, 0xb9, 0xb1, 0x4b,
	0xf6, 0xf2, 0xa9, 0x25, 0x7b, 0x7f, 0x22, 0xc1, 0x6a, 0x3a, 0x0d, 0x89, 0xb2, 0x33, 0x6a, 0xf4,
	0x45, 0x2a, 0x89, 0xaf, 0x9e, 0x6d, 0x24, 0xa8, 0x41, 0xe6, 0x2d, 0xf8, 0x99, 0x68, 0x7c, 0xf4,
	0x27, 0x12, 0xdc, 0xa8, 0xda, 0x96, 0x3b, 0xec, 0x79, 0xe9, 0x05, 0x79, 0x23, 0x32, 0x64, 0xc6,
	0xa8, 0xc3, 0x1b, 0xa3, 0x76, 0x3a, 0xc3, 0xc5, 0x88, 0x95, 0xbf, 0xce, 0x24, 0xca, 0x5f, 0xb7,
	0xe0, 0xa6, 0x60, 0x01, 0x4c, 0x43, 0xfd, 0x20, 0x07, 0x37, 0xf6, 0x1c, 0x47, 0x5c, 0x73, 0x38,
	0xe1, 0x12, 0x67, 0x46, 0x2f, 0x31, 0x97, 0x55, 0x1e, 0x9e, 0x17, 0x94, 0x87, 0x4f, 0xf3, 0xee,
	0x6f, 0xb2, 0xd4, 0x7d, 0x36, 0x51, 0xea, 0x1e, 0x61, 0xdb, 0x5c, 0x32, 0xd7, 0x3e, 0x8c, 0xa5,
	0x16, 0x62, 0xb1, 0x54, 0xcc, 0x34, 0x01, 0x4b, 0x18, 0xd3, 0xee, 0xc1, 0x8a, 0x8a, 0xce, 0xed,
	0x97, 0xf1, 0x38, 0x68, 0x52, 0xa7, 0xaf, 0xc1, 0xf5, 0x58, 0x4f, 0x86, 0xe2, 0x1b, 0xf0, 0x56,
	0xe2, 0x74, 0x73, 0xe5, 0x2e, 0x55, 0xdb, 0x3a, 0xf5, 0xb1, 0xca, 0x30, 0xcd, 0xa9, 0x18, 0xf2,
	0xf7, 0x08, 0xdd, 0xa2, 0xfc, 0x48, 0x82, 0x7b, 0xa3, 0xd1, 0x33, 0x35, 0xf2, 0x1d, 0xb8, 0x95,
	0x56, 0x8b, 0x93, 0x50, 0x27, 0x1f, 0xa6, 0xab, 0x13, 0xfa, 0xa4, 0x23, 0x9a, 0x89, 0xa8, 0x96,
	0x8a, 0x93, 0x0a, 0x27, 0xa1, 0xb6, 0x3f, 0x97, 0x40, 0x19, 0x8d, 0x02, 0x2f, 0x38, 0x8c, 0xca,
	0xf8, 0x62, 0x18, 0x44, 0x63, 0xf8, 0x66, 0xe4, 0xf9, 0xfc, 0xf0, 0x03, 0x30, 0xc4, 0x64, 0xf1,
	0x9b, 0x43, 0xcd, 0xe0, 0x8f, 0xc0, 0xba, 0xe1, 0x6d, 0xbf, 0xda, 0x59, 0x3f, 0xe9, 0x21, 0xcd,
	0xbd, 0x30, 0xbd, 0xce, 0x59, 0xf4, 0xfd, 0x0f, 0x37, 0xb4, 0x08, 0x5c, 0xf9, 0xb1, 0x04, 0x3b,
	0x2d, 0xe4, 0x65, 0x53, 0x4d, 0xfb, 0x8e, 0x79, 0x8a, 0xa2, 0xab, 0xcb, 0x65, 0xaf, 0x2e, 0x1f,
	0x5f, 0xdd, 0x44, 0xc4, 0xbf, 0x03, 0x8f, 0xc7, 0xa6, 0x9d, 0x0a, 0xc8, 0x83, 0xb7, 0xa1, 0xd8,
	0x0e, 0x2a, 0xe2, 0x4b, 0x30, 0x77, 0x7c, 0xf8, 0x95, 0xc3, 0xa3, 0x17, 0x87, 0xe5, 0x29, 0xb9,
	0x00, 0xd3, 0x4f, 0xd4, 0x7a, 0xbd, 0x2c, 0xc9, 0x73, 0x90, 0x6f, 0xee, 0x7d, 0xbd, 0x9c, 0x7b,
	0xf0, 0xfb, 0x12, 0x97, 0xfe, 0x16, 0xfa, 0x98, 0xf2, 0x6d, 0xb8, 0xf9, 0xa2, 0x5e, 0xab, 0x35,
	0x0e, 0x0f, 0xb4, 0xc6, 0xe1, 0xc7, 0x8d, 0x76, 0x5d, 0x6b, 0xb5, 0xf7, 0xda, 0xc7, 0x2d, 0x2d,
	0xc4, 0x26, 0xec, 0x42, 0xbf, 0x6a, 0x65, 0x49, 0x56, 0xe0, 0x56, 0x7a, 0x97, 0xbd, 0x6a, 0xb5,
	0xde, 0xc4, 0x7d, 0x72, 0x62, 0x34, 0x6a, 0xfd, 0xc9, 0x71, 0xab, 0x5e, 0x2b, 0xe7, 0x1f, 0xfc,
	0xa6, 0x04, 0xcb, 0x09, 0x3b, 0x58, 0xbe, 0x01, 0xeb, 0xfe, 0xc0, 0xe6, 0xb3, 0xbd, 0xc3, 0x70,
	0xf6, 0x46, 0xbb, 0x3c, 0x25, 0x6f, 0xc1, 0x66, 0x5a, 0x6b, 0xf3, 0xd9, 0xde, 0xd7, 0x1b, 0x87,
	0x07, 0x65, 0x49, 0xbe, 0x05, 0x95, 0xb4, 0x0e, 0x4f, 0x1a, 0x87, 0x8d, 0xd6, 0xd3, 0x72, 0x4e,
	0xd4, 0x5e, 0xdd, 0x3b, 0xac, 0xd6, 0x9f, 0x95, 0xf3, 0x0f, 0xbe, 0x09, 0xf3, 0x7c, 0x6a, 0x87,
	0xbc, 0x09, 0x6b, 0x6a, 0xfd, 0xe3, 0x46, 0xfd, 0x45, 0x48, 0x3f, 0xfe, 0xc2, 0x93, 0x4d, 0x61,
	0xdb, 0x36, 0xda, 0xd8, 0xdc, 0x6b, 0xb5, 0xa8, 0x11, 0x1b, 0x1f, 0xf4, 0xe5, 0x7a, 0xb5, 0x5d,
	0xce, 0x3d, 0xf8, 0xbe, 0x14, 0x24, 0x7a, 0x86, 0xb7, 0xb1, 0xce, 0x7e, 0xe5, 0x20, 0xe0, 0x98,
	0x5a, 0x6f, 0xd5, 0xd5, 0x8f, 0xeb, 0x5a, 0x6d, 0xaf, 0xbd, 0x47, 0x2d, 0xde, 0xbd, 0x67, 0xcf,
	0xca, 0x53, 0xf2, 0x5b, 0x70, 0x47, 0xdc, 0xe5, 0xf0, 0xa8, 0x8d, 0xa7, 0x53, 0xdb, 0x65, 0x29,
	0x1b, 0x57, 0xfd, 0xb0, 0x56, 0xce, 0x3d, 0x78, 0x01, 0x80, 0xdd, 0x2c, 0x66, 0xa5, 0x6f, 0xc2,
	0xda, 0xd3, 0x46, 0xad, 0xae, 0x89, 0x4c, 0xf5, 0x48, 0x23, 0xfe, 0x28, 0x4b, 0x09, 0x70, 0xeb,
	0xe9, 0xd1, 0x8b, 0x72, 0xee, 0x81, 0x03, 0xd7, 0x89, 0x60, 0xab, 0x31, 0xa1, 0x97, 0xb7, 0xe1,
	0x06, 0x25, 0x66, 0x6f, 0xff, 0x59, 0x5d, 0x6b, 0xbd, 0x68, 0xb4, 0xab, 0x4f, 0xa3, 0x13, 0x55,
	0x60, 0x35, 0xd9, 0xe3, 0xa8, 0x59, 0x3f, 0x2c, 0x4b, 0x74, 0x23, 0xe2, 0x6d, 0xd5, 0x67, 0x47,
	0xad, 0x7a, 0x39, 0xb7, 0xfb, 0x57, 0x9f, 0x05, 0x99, 0x1d, 0x24, 0x4e, 0xa2, 0x64, 0x1d, 0x20,
	0xfc, 0x01, 0x13, 0xf9, 0x2d, 0xe1, 0x0f, 0xba, 0x44, 0x53, 0x49, 0x2a, 0xf7, 0x46, 0x77, 0x64,
	0x17, 0xc8, 0x94, 0xfc, 0x2d, 0x58, 0x88, 0xe4, 0xc7, 0xcb, 0x02, 0xf7, 0x3a, 0x2d, 0x13, 0xbf,
	0xf2, 0xf6, 0x58, 0x7d, 0x83, 0xb9, 0x5c, 0x28, 0xc7, 0x33, 0xdc, 0x65, 0x41, 0x31, 0x8f, 0x20,
	0x35, 0xbf, 0xb2, 0x33, 0x6e, 0xf7, 0x60, 0xd2, 0x3e, 0x2c, 0x46, 0x8b, 0x21, 0xe4, 0xb7, 0x85,
	0x38, 0x92, 0xb5, 0x99, 0x95, 0x87, 0xe3, 0x75, 0x0e, 0xa6, 0x33, 0xa0, 0xc4, 0xd5, 0x03, 0xc9,
	0xf7, 0x44, 0xd7, 0x5d, 0xbc, 0x0a, 0xa9, 0x72, 0x7f, 0x8c, 0x9e, 0xfc, 0xa2, 0xa2, 0x85, 0x40,
	0xa2, 0x45, 0xa5, 0x16, 0x21, 0x89, 0x16, 0x25, 0xa8, 0x2d, 0x22, 0xd3, 0x45, 0x2b, 0x85, 0x44,
	0xd3, 0xa5, 0xd6, 0x20, 0x89, 0xa6, 0x13, 0x14, 0x1f, 0x4d, 0xc9, 0x03, 0x58, 0x8a, 0x65, 0x14,
	0xc8, 0xe2, 0x6d, 0x48, 0xc9, 0x56, 0xa8, 0x3c, 0x1a, 0xb3, 0x77, 0x30, 0xe3, 0xaf, 0x49, 0x31,
	0x1f, 0x9b, 0x4b, 0x60, 0x7f, 0x6f, 0xb4, 0xc8, 0x25, 0x7f, 0x12, 0xa6, 0xf2, 0xfe, 0x84, 0xa3,
	0x02, 0x52, 0x7e, 0x49, 0x22, 0x4f, 0xa0, 0x29, 0x84, 0xec, 0x0a, 0x51, 0x8a, 0xc9, 0x78, 0x77,
	0xa2, 0x31, 0x01, 0x11, 0xbf, 0x28, 0xc1, 0x6a, 0xfa, 0xcf, 0x8c, 0xc8, 0x8f, 0xd3, 0x31, 0x0a,
	0x7f, 0x3e, 0xa5, 0xf2, 0xde, 0xb8, 0x03, 0x62, 0x34, 0x7c, 0x5f, 0x82, 0x75, 0xd1, 0x6f, 0x83,
	0xc8, 0xef, 0x88, 0xbd, 0x52, 0x81, 0x0f, 0x52, 0xf9, 0x60, 0xfc, 0x21, 0x31, 0x4a, 0x7e, 0x48,
	0x1d, 0x79, 0xf1, 0x4f, 0x79, 0xc8, 0x1f, 0x09, 0xd9, 0x3c, 0xf2, 0x07, 0x45, 0x2a, 0x9f, 0xbf,
	0xd2, 0xd8, 0x80, 0xb8, 0xef, 0xc2, 0x4a, 0xda, 0xcf, 0x4e, 0x88, 0x38, 0x94, 0xf1, 0x53, 0x17,
	0x95, 0xdd, 0x49, 0x86, 0x04, 0x04, 0x5c, 0x82, 0x9c, 0xfc, 0x99, 0x03, 0x91, 0x98, 0x08, 0x7f,
	0xb2, 0xa1, 0xf2, 0x99, 0xf1, 0x07, 0x04, 0x53, 0x7f, 0x07, 0xae, 0xa5, 0xfc, 0xa2, 0x81, 0x2c,
	0x40, 0x25, 0xfe, 0xb9, 0x85, 0xca, 0x3b, 0x13, 0x8c, 0xe0, 0xd5, 0x54, 0xec, 0xd7, 0x0a, 0x64,
	0xb1, 0xa6, 0x4b, 0x5b, 0xf2, 0xa3, 0x31, 0x7b, 0xf3, 0xac, 0x4e, 0x26, 0x5d, 0x8a, 0x58, 0x2d,
	0x4c, 0x35, 0x15, 0xb1, 0x5a, 0x9c, 0xcf, 0x49, 0xaf, 0x80, 0x68, 0x22, 0x9b, 0xe8, 0x0a, 0x48,
	0x4d, 0xc6, 0x13, 0x5d, 0x01, 0x82, 0xdc, 0xb8, 0x29, 0xf9, 0x57, 0x24, 0x92, 0xd4, 0x91, 0x92,
	0xfd, 0x26, 0x8f, 0x54, 0x69, 0x29, 0xc9, 0x74, 0x95, 0xf7, 0x26, 0x1b, 0x14, 0xd0, 0xf1, 0xeb,
	0xb4, 0x2a, 0x2f, 0x35, 0x3d, 0x41, 0x16, 0xeb, 0xf8, 0xac, 0xd4, 0x08, 0x91, 0x22, 0x1a, 0x95,
	0x05, 0x11, 0x3f, 0xeb, 0xe1, 0x53, 0xdf, 0xe8, 0xb3, 0x9e, 0x78, 0xbd, 0x1c, 0x7d, 0xd6, 0x93,
	0x2f, 0x89, 0xec, 0x72, 0x4a, 0xad, 0x6d, 0x12, 0x5d, 0x4e, 0x59, 0x95, 0x5a, 0xa2, 0xcb, 0x29,
	0xb3, 0x78, 0x8a, 0xed, 0x89, 0xa8, 0x7c, 0x48, 0xb4, 0x27, 0x23, 0x2a, 0x9c, 0x44, 0x7b, 0x32,
	0xaa, 0x4a, 0x89, 0x1a, 0xb5, 0xf1, 0x6a, 0x1f, 0x91, 0x51, 0x2b, 0x28, 0x35, 0x12, 0x19, 0xb5,
	0xc2, 0x22, 0xa2, 0x29, 0xf9, 0x77, 0xa4, 0x58, 0x11, 0x5c, 0xac, 0x74, 0x46, 0xfe, 0xdc, 0x55,
	0xaa, 0x76, 0x28, 0x35, 0x1f, 0x5d, 0xbd, 0xe0, 0x87, 0xaa, 0xe4, 0x94, 0x0a, 0x15, 0xf9, 0x33,
	0xa3, 0xc4, 0x2d, 0x5e, 0x5e, 0x52, 0x79, 0x67, 0x82, 0x11, 0xfc, 0x01, 0x49, 0x2b, 0xec, 0x10,
	0x1d, 0x90, 0x8c, 0xf2, 0x16, 0xd1, 0x01, 0xc9, 0xac, 0x1b, 0x99, 0x92, 0xff, 0x54, 0x02, 0x65,
	0x74, 0xc1, 0x85, 0xfc, 0xc5, 0x2c, 0x03, 0x7c, 0x8c, 0x9a, 0x93, 0xca, 0x97, 0xae, 0x8e, 0x20,
	0x62, 0xf4, 0x0a, 0x2a, 0x30, 0x44, 0x46, 0x6f, 0x76, 0x75, 0x88, 0xc8, 0xe8, 0x1d, 0x51, 0xe6,
	0xc1, 0x8e, 0xb4, 0xa8, 0x7e, 0x42, 0x74, 0xa4, 0x47, 0x54, 0x77, 0x08, 0xed, 0xbd, 0x51, 0x65,
	0x1a, 0xbe, 0xcb, 0xc8, 0xe5, 0xeb, 0x66, 0xb8, 0x8c, 0xc9, 0xfc, 0xe4, 0x0c, 0x97, 0x31, 0x25,
	0x05, 0x98, 0xba, 0x8c, 0x5c, 0xf2, 0xa2, 0xc8, 0x65, 0x4c, 0xe6, 0x44, 0x8a, 0x5c, 0xc6, 0x94,
	0x4c, 0x48, 0xea, 0xe8, 0x47, 0x82, 0xc8, 0x22, 0x47, 0x3f, 0x2d, 0x26, 0x2d, 0x72, 0xf4, 0xd3,
	0xa3, 0xd2, 0x64, 0xae, 0x48, 0xbe, 0xad, 0x68, 0xae, 0xb4, 0x3c, 0x60, 0xd1, 0x5c, 0xe9, 0x09,
	0xbc, 0xb1, 0xcd, 0x22, 0x57, 0xd1, 0xc8, 0xcd, 0xe2, 0xef, 0xa0, 0x87, 0xe3, 0x75, 0x8e, 0x4d,
	0xc7, 0xef, 0x97, 0x78, 0xba, 0x94, 0x2d, 0x7b, 0x38, 0x5e, 0x67, 0x5e, 0xa1, 0xa5, 0xe5, 0x7c,
	0x8a, 0x14, 0x5a, 0x46, 0xa6, 0x6a, 0x65, 0x77, 0xf2, 0x94, 0x52, 0x65, 0x4a, 0xfe, 0x0d, 0x09,
	0x36, 0x84, 0x79, 0x38, 0xf2, 0x07, 0x42, 0xb3, 0x2e, 0x33, 0x0f, 0xa8, 0xf2, 0xd9, 0x89, 0xc7,
	0x05, 0x04, 0x7d, 0x0d, 0xe6, 0x58, 0xf6, 0x85, 0x2c, 0xf8, 0x85, 0xe3, 0x68, 0xc2, 0x4f, 0xe5,
	0xee, 0x88, 0x5e, 0x31, 0xeb, 0x2a, 0x91, 0xa2, 0x93, 0x61, 0x5d, 0x89, 0xf2, 0x7f, 0x32, 0xac,
	0x2b, 0x61, 0x06, 0x90, 0x32, 0x25, 0x9f, 0xc3, 0x72, 0x22, 0x1b, 0x46, 0xce, 0x88, 0x78, 0xa5,
	0x65, 0xd9, 0x54, 0x1e, 0x8f, 0xdd, 0x3f, 0xa2, 0x7d, 0x45, 0xf9, 0x25, 0x22, 0xed, 0x3b, 0x22,
	0xb5, 0x45, 0xa4, 0x7d, 0x47, 0xa6, 0xb1, 0xd0, 0x6b, 0x49, 0x90, 0x64, 0x22, 0xba, 0x96, 0xb2,
	0x13, 0x5f, 0x44, 0xd7, 0xd2, 0xa8, 0x4c, 0x96, 0x29, 0xf9, 0xb7, 0x24, 0xd8, 0xcc, 0xc8, 0xd0,
	0x90, 0x3f, 0xbc, 0x6a, 0x4a, 0x4a, 0xe5, 0x73, 0x57, 0x18, 0x29, 0x8a, 0xa3, 0x92, 0xc4, 0x8d,
	0x31, 0xe2, 0xa8, 0x5c, 0x8e, 0xc6, 0x38, 0x71, 0x54, 0x3e, 0x65, 0x82, 0x1a, 0x76, 0x29, 0x39,
	0x15, 0x22, 0xc3, 0x4e, 0x9c, 0x1e, 0x52, 0x79, 0x67, 0xe2, 0x84, 0x8d, 0xd0, 0x1f, 0x4c, 0x49,
	0x64, 0xc8, 0xf0, 0x07, 0xc5, 0x99, 0x17, 0x19, 0xfe, 0x60, 0x46, 0xae, 0x04, 0x73, 0x80, 0x52,
	0xdf, 0xc2, 0x45, 0x0e, 0x50, 0xd6, 0xcb, 0xbf, 0xc8, 0x01, 0xca, 0x7e, 0x6c, 0xa7, 0x44, 0xa4,
	0xbe, 0x2d, 0x8b, 0x88, 0xc8, 0x7a, 0x9b, 0x17, 0x11, 0x91, 0xfd, 0x78, 0x1d, 0xca, 0x43, 0x34,
	0x15, 0x3b, 0x53, 0x1e, 0x52, 0xf3, 0xcc, 0x33, 0xe5, 0x21, 0x3d, 0xcf, 0x5b, 0x99, 0x92, 0x1b,
	0x24, 0x12, 0xd2, 0xde, 0x47, 0xba, 0xd5, 0xb6, 0x3d, 0xbd, 0x57, 0xb5, 0x87, 0x96, 0x27, 0x6f,
	0xec, 0xa8, 0xfe, 0xef, 0xed, 0x7f, 0xbc, 0xbb, 0xd3, 0x36, 0xfb, 0x48, 0xc5, 0x6b, 0x50, 0xd1,
	0xab, 0xca, 0x6a, 0xa4, 0x89, 0x74, 0xc7, 0xe8, 0x94, 0x29, 0xf9, 0xcb, 0xe4, 0x34, 0x11, 0x54,
	0x47, 0x8e, 0x81, 0x9c, 0x86, 0xe1, 0x66, 0x21, 0x8a, 0x36, 0xf9, 0x23, 0x18, 0xae, 0x4b, 0x90,
	0x93, 0x3f, 0x39, 0x24, 0x0a, 0xd0, 0x08, 0x7f, 0xe1, 0x48, 0x14, 0xa0, 0xc9, 0xf8, 0x35, 0xa3,
	0x29, 0xf9, 0x0f, 0x24, 0x52, 0x48, 0x91, 0xf9, 0x5a, 0x2f, 0xff, 0xff, 0x31, 0xc5, 0x3e, 0x3d,
	0x89, 0xa0, 0xf2, 0x85, 0xab, 0x0e, 0x0f, 0xa8, 0xfc, 0x4b, 0x09, 0xde, 0x1a, 0xf3, 0xe5, 0x58,
	0xae, 0x09, 0x8d, 0x84, 0x09, 0x1e, 0xcd, 0x2b, 0xf5, 0x4f, 0x88, 0xc5, 0x27, 0xbd, 0xb2, 0xf9,
	0x9f, 0x7f, 0xf1, 0x77, 0xed, 0x55, 0x58, 0x49, 0xfb, 0x0f, 0x25, 0xf6, 0x3f, 0xfc, 0x85, 0x0f,
	0xba, 0x76, 0x4f, 0xb7, 0xba, 0x3b, 0xef, 0xef, 0x7a, 0xde, 0x4e, 0xc7, 0xee, 0x3f, 0x26, 0xff,
	0xdb, 0x43, 0xc7, 0xee, 0x3d, 0xc6, 0x98, 0xcd, 0x0e, 0x72, 0x53, 0xff, 0x2b, 0x8a, 0x93, 0x59,
	0xd2, 0xef, 0xdd, 0xff, 0x0e, 0x00, 0x00, 0xff, 0xff, 0xc7, 0x98, 0x4f, 0x25, 0xcd, 0x62, 0x00,
	0x00,
}
