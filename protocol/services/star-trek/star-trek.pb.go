// Code generated by protoc-gen-go. DO NOT EDIT.
// source: star-trek/star-trek.proto

package star_trek // import "golang.52tt.com/protocol/services/star-trek"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/services/extension/options"
import reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type TrekResultType int32

const (
	TrekResultType_AllType TrekResultType = 0
	TrekResultType_Success TrekResultType = 1
	TrekResultType_Failed  TrekResultType = 2
)

var TrekResultType_name = map[int32]string{
	0: "AllType",
	1: "Success",
	2: "Failed",
}
var TrekResultType_value = map[string]int32{
	"AllType": 0,
	"Success": 1,
	"Failed":  2,
}

func (x TrekResultType) String() string {
	return proto.EnumName(TrekResultType_name, int32(x))
}
func (TrekResultType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_ee1e21e98b8bab9e, []int{0}
}

// ************* 运营后台 ******************
type StarTrekWeekDay int32

const (
	StarTrekWeekDay_Sunday    StarTrekWeekDay = 0
	StarTrekWeekDay_Monday    StarTrekWeekDay = 1
	StarTrekWeekDay_Tuesday   StarTrekWeekDay = 2
	StarTrekWeekDay_Wednesday StarTrekWeekDay = 3
	StarTrekWeekDay_Thursday  StarTrekWeekDay = 4
	StarTrekWeekDay_Friday    StarTrekWeekDay = 5
	StarTrekWeekDay_Saturday  StarTrekWeekDay = 6
	StarTrekWeekDay_UnkownDay StarTrekWeekDay = 7
)

var StarTrekWeekDay_name = map[int32]string{
	0: "Sunday",
	1: "Monday",
	2: "Tuesday",
	3: "Wednesday",
	4: "Thursday",
	5: "Friday",
	6: "Saturday",
	7: "UnkownDay",
}
var StarTrekWeekDay_value = map[string]int32{
	"Sunday":    0,
	"Monday":    1,
	"Tuesday":   2,
	"Wednesday": 3,
	"Thursday":  4,
	"Friday":    5,
	"Saturday":  6,
	"UnkownDay": 7,
}

func (x StarTrekWeekDay) String() string {
	return proto.EnumName(StarTrekWeekDay_name, int32(x))
}
func (StarTrekWeekDay) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_ee1e21e98b8bab9e, []int{1}
}

// 活动状态
type StarTrekStatus int32

const (
	StarTrekStatus_AllStatus StarTrekStatus = 0
	StarTrekStatus_Using     StarTrekStatus = 1
	StarTrekStatus_Unused    StarTrekStatus = 2
	StarTrekStatus_Finished  StarTrekStatus = 3
)

var StarTrekStatus_name = map[int32]string{
	0: "AllStatus",
	1: "Using",
	2: "Unused",
	3: "Finished",
}
var StarTrekStatus_value = map[string]int32{
	"AllStatus": 0,
	"Using":     1,
	"Unused":    2,
	"Finished":  3,
}

func (x StarTrekStatus) String() string {
	return proto.EnumName(StarTrekStatus_name, int32(x))
}
func (StarTrekStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_ee1e21e98b8bab9e, []int{2}
}

type PackageItemType int32

const (
	PackageItemType_UNKNOW_ITEM_TYPE          PackageItemType = 0
	PackageItemType_BACKPACK_PRESENT          PackageItemType = 1
	PackageItemType_BACKPACK_CARD_RICH_EXP    PackageItemType = 2
	PackageItemType_BACKPACK_CARD_CHARM_EXP   PackageItemType = 3
	PackageItemType_BACKPACK_LOTTERY_FRAGMENT PackageItemType = 4
)

var PackageItemType_name = map[int32]string{
	0: "UNKNOW_ITEM_TYPE",
	1: "BACKPACK_PRESENT",
	2: "BACKPACK_CARD_RICH_EXP",
	3: "BACKPACK_CARD_CHARM_EXP",
	4: "BACKPACK_LOTTERY_FRAGMENT",
}
var PackageItemType_value = map[string]int32{
	"UNKNOW_ITEM_TYPE":          0,
	"BACKPACK_PRESENT":          1,
	"BACKPACK_CARD_RICH_EXP":    2,
	"BACKPACK_CARD_CHARM_EXP":   3,
	"BACKPACK_LOTTERY_FRAGMENT": 4,
}

func (x PackageItemType) String() string {
	return proto.EnumName(PackageItemType_name, int32(x))
}
func (PackageItemType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_ee1e21e98b8bab9e, []int{3}
}

// 奖励包裹信息
type StarTAwardInfo struct {
	PackId               uint32   `protobuf:"varint,1,opt,name=pack_id,json=packId,proto3" json:"pack_id,omitempty"`
	PackName             string   `protobuf:"bytes,2,opt,name=pack_name,json=packName,proto3" json:"pack_name,omitempty"`
	PackPic              string   `protobuf:"bytes,3,opt,name=pack_pic,json=packPic,proto3" json:"pack_pic,omitempty"`
	PackAmount           uint32   `protobuf:"varint,4,opt,name=pack_amount,json=packAmount,proto3" json:"pack_amount,omitempty"`
	UnitPrice            uint32   `protobuf:"varint,5,opt,name=unit_price,json=unitPrice,proto3" json:"unit_price,omitempty"`
	FinTime              uint32   `protobuf:"varint,6,opt,name=fin_time,json=finTime,proto3" json:"fin_time,omitempty"`
	GiftId               uint32   `protobuf:"varint,7,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StarTAwardInfo) Reset()         { *m = StarTAwardInfo{} }
func (m *StarTAwardInfo) String() string { return proto.CompactTextString(m) }
func (*StarTAwardInfo) ProtoMessage()    {}
func (*StarTAwardInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_ee1e21e98b8bab9e, []int{0}
}
func (m *StarTAwardInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StarTAwardInfo.Unmarshal(m, b)
}
func (m *StarTAwardInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StarTAwardInfo.Marshal(b, m, deterministic)
}
func (dst *StarTAwardInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StarTAwardInfo.Merge(dst, src)
}
func (m *StarTAwardInfo) XXX_Size() int {
	return xxx_messageInfo_StarTAwardInfo.Size(m)
}
func (m *StarTAwardInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_StarTAwardInfo.DiscardUnknown(m)
}

var xxx_messageInfo_StarTAwardInfo proto.InternalMessageInfo

func (m *StarTAwardInfo) GetPackId() uint32 {
	if m != nil {
		return m.PackId
	}
	return 0
}

func (m *StarTAwardInfo) GetPackName() string {
	if m != nil {
		return m.PackName
	}
	return ""
}

func (m *StarTAwardInfo) GetPackPic() string {
	if m != nil {
		return m.PackPic
	}
	return ""
}

func (m *StarTAwardInfo) GetPackAmount() uint32 {
	if m != nil {
		return m.PackAmount
	}
	return 0
}

func (m *StarTAwardInfo) GetUnitPrice() uint32 {
	if m != nil {
		return m.UnitPrice
	}
	return 0
}

func (m *StarTAwardInfo) GetFinTime() uint32 {
	if m != nil {
		return m.FinTime
	}
	return 0
}

func (m *StarTAwardInfo) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

type StarTUserInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StarTUserInfo) Reset()         { *m = StarTUserInfo{} }
func (m *StarTUserInfo) String() string { return proto.CompactTextString(m) }
func (*StarTUserInfo) ProtoMessage()    {}
func (*StarTUserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_ee1e21e98b8bab9e, []int{1}
}
func (m *StarTUserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StarTUserInfo.Unmarshal(m, b)
}
func (m *StarTUserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StarTUserInfo.Marshal(b, m, deterministic)
}
func (dst *StarTUserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StarTUserInfo.Merge(dst, src)
}
func (m *StarTUserInfo) XXX_Size() int {
	return xxx_messageInfo_StarTUserInfo.Size(m)
}
func (m *StarTUserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_StarTUserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_StarTUserInfo proto.InternalMessageInfo

func (m *StarTUserInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

// 上期回顾
type LastRoundReview struct {
	RoundId              uint32          `protobuf:"varint,1,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
	RoundTime            uint32          `protobuf:"varint,2,opt,name=round_time,json=roundTime,proto3" json:"round_time,omitempty"`
	TrekResult           bool            `protobuf:"varint,3,opt,name=trek_result,json=trekResult,proto3" json:"trek_result,omitempty"`
	AwardPack            *StarTAwardInfo `protobuf:"bytes,4,opt,name=award_pack,json=awardPack,proto3" json:"award_pack,omitempty"`
	UserInfo             *StarTUserInfo  `protobuf:"bytes,5,opt,name=user_info,json=userInfo,proto3" json:"user_info,omitempty"`
	UserList             []uint32        `protobuf:"varint,6,rep,packed,name=user_list,json=userList,proto3" json:"user_list,omitempty"`
	BingoUserNum         uint32          `protobuf:"varint,7,opt,name=bingo_user_num,json=bingoUserNum,proto3" json:"bingo_user_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *LastRoundReview) Reset()         { *m = LastRoundReview{} }
func (m *LastRoundReview) String() string { return proto.CompactTextString(m) }
func (*LastRoundReview) ProtoMessage()    {}
func (*LastRoundReview) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_ee1e21e98b8bab9e, []int{2}
}
func (m *LastRoundReview) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LastRoundReview.Unmarshal(m, b)
}
func (m *LastRoundReview) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LastRoundReview.Marshal(b, m, deterministic)
}
func (dst *LastRoundReview) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LastRoundReview.Merge(dst, src)
}
func (m *LastRoundReview) XXX_Size() int {
	return xxx_messageInfo_LastRoundReview.Size(m)
}
func (m *LastRoundReview) XXX_DiscardUnknown() {
	xxx_messageInfo_LastRoundReview.DiscardUnknown(m)
}

var xxx_messageInfo_LastRoundReview proto.InternalMessageInfo

func (m *LastRoundReview) GetRoundId() uint32 {
	if m != nil {
		return m.RoundId
	}
	return 0
}

func (m *LastRoundReview) GetRoundTime() uint32 {
	if m != nil {
		return m.RoundTime
	}
	return 0
}

func (m *LastRoundReview) GetTrekResult() bool {
	if m != nil {
		return m.TrekResult
	}
	return false
}

func (m *LastRoundReview) GetAwardPack() *StarTAwardInfo {
	if m != nil {
		return m.AwardPack
	}
	return nil
}

func (m *LastRoundReview) GetUserInfo() *StarTUserInfo {
	if m != nil {
		return m.UserInfo
	}
	return nil
}

func (m *LastRoundReview) GetUserList() []uint32 {
	if m != nil {
		return m.UserList
	}
	return nil
}

func (m *LastRoundReview) GetBingoUserNum() uint32 {
	if m != nil {
		return m.BingoUserNum
	}
	return 0
}

// 下期预告
type NextRoundForecast struct {
	RoundId              uint32          `protobuf:"varint,1,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
	AwardPack            *StarTAwardInfo `protobuf:"bytes,2,opt,name=award_pack,json=awardPack,proto3" json:"award_pack,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *NextRoundForecast) Reset()         { *m = NextRoundForecast{} }
func (m *NextRoundForecast) String() string { return proto.CompactTextString(m) }
func (*NextRoundForecast) ProtoMessage()    {}
func (*NextRoundForecast) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_ee1e21e98b8bab9e, []int{3}
}
func (m *NextRoundForecast) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NextRoundForecast.Unmarshal(m, b)
}
func (m *NextRoundForecast) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NextRoundForecast.Marshal(b, m, deterministic)
}
func (dst *NextRoundForecast) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NextRoundForecast.Merge(dst, src)
}
func (m *NextRoundForecast) XXX_Size() int {
	return xxx_messageInfo_NextRoundForecast.Size(m)
}
func (m *NextRoundForecast) XXX_DiscardUnknown() {
	xxx_messageInfo_NextRoundForecast.DiscardUnknown(m)
}

var xxx_messageInfo_NextRoundForecast proto.InternalMessageInfo

func (m *NextRoundForecast) GetRoundId() uint32 {
	if m != nil {
		return m.RoundId
	}
	return 0
}

func (m *NextRoundForecast) GetAwardPack() *StarTAwardInfo {
	if m != nil {
		return m.AwardPack
	}
	return nil
}

// 中奖轮播信息
type RollingAwardInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Nickname             string   `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"`
	PackName             string   `protobuf:"bytes,3,opt,name=pack_name,json=packName,proto3" json:"pack_name,omitempty"`
	PackPic              string   `protobuf:"bytes,4,opt,name=pack_pic,json=packPic,proto3" json:"pack_pic,omitempty"`
	PackAmount           uint32   `protobuf:"varint,5,opt,name=pack_amount,json=packAmount,proto3" json:"pack_amount,omitempty"`
	BingoUserNum         uint32   `protobuf:"varint,6,opt,name=bingo_user_num,json=bingoUserNum,proto3" json:"bingo_user_num,omitempty"`
	RoundId              uint32   `protobuf:"varint,7,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RollingAwardInfo) Reset()         { *m = RollingAwardInfo{} }
func (m *RollingAwardInfo) String() string { return proto.CompactTextString(m) }
func (*RollingAwardInfo) ProtoMessage()    {}
func (*RollingAwardInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_ee1e21e98b8bab9e, []int{4}
}
func (m *RollingAwardInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RollingAwardInfo.Unmarshal(m, b)
}
func (m *RollingAwardInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RollingAwardInfo.Marshal(b, m, deterministic)
}
func (dst *RollingAwardInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RollingAwardInfo.Merge(dst, src)
}
func (m *RollingAwardInfo) XXX_Size() int {
	return xxx_messageInfo_RollingAwardInfo.Size(m)
}
func (m *RollingAwardInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_RollingAwardInfo.DiscardUnknown(m)
}

var xxx_messageInfo_RollingAwardInfo proto.InternalMessageInfo

func (m *RollingAwardInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RollingAwardInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *RollingAwardInfo) GetPackName() string {
	if m != nil {
		return m.PackName
	}
	return ""
}

func (m *RollingAwardInfo) GetPackPic() string {
	if m != nil {
		return m.PackPic
	}
	return ""
}

func (m *RollingAwardInfo) GetPackAmount() uint32 {
	if m != nil {
		return m.PackAmount
	}
	return 0
}

func (m *RollingAwardInfo) GetBingoUserNum() uint32 {
	if m != nil {
		return m.BingoUserNum
	}
	return 0
}

func (m *RollingAwardInfo) GetRoundId() uint32 {
	if m != nil {
		return m.RoundId
	}
	return 0
}

// 获取星际巡航信息
type GetStatTrekInfoReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetStatTrekInfoReq) Reset()         { *m = GetStatTrekInfoReq{} }
func (m *GetStatTrekInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetStatTrekInfoReq) ProtoMessage()    {}
func (*GetStatTrekInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_ee1e21e98b8bab9e, []int{5}
}
func (m *GetStatTrekInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStatTrekInfoReq.Unmarshal(m, b)
}
func (m *GetStatTrekInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStatTrekInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetStatTrekInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStatTrekInfoReq.Merge(dst, src)
}
func (m *GetStatTrekInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetStatTrekInfoReq.Size(m)
}
func (m *GetStatTrekInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStatTrekInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetStatTrekInfoReq proto.InternalMessageInfo

func (m *GetStatTrekInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetStatTrekInfoResp struct {
	RoundId              uint32              `protobuf:"varint,1,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
	BeginTime            uint32              `protobuf:"varint,2,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint32              `protobuf:"varint,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	AwardPack            *StarTAwardInfo     `protobuf:"bytes,4,opt,name=award_pack,json=awardPack,proto3" json:"award_pack,omitempty"`
	InvestProgress       uint32              `protobuf:"varint,5,opt,name=invest_progress,json=investProgress,proto3" json:"invest_progress,omitempty"`
	UserCount            uint32              `protobuf:"varint,6,opt,name=user_count,json=userCount,proto3" json:"user_count,omitempty"`
	LastS                *LastRoundReview    `protobuf:"bytes,7,opt,name=last_s,json=lastS,proto3" json:"last_s,omitempty"`
	NextS                *NextRoundForecast  `protobuf:"bytes,8,opt,name=next_s,json=nextS,proto3" json:"next_s,omitempty"`
	Records              []*RollingAwardInfo `protobuf:"bytes,9,rep,name=records,proto3" json:"records,omitempty"`
	LatestPartitions     []*StarTUserInfo    `protobuf:"bytes,10,rep,name=latest_partitions,json=latestPartitions,proto3" json:"latest_partitions,omitempty"`
	MySupply             uint32              `protobuf:"varint,11,opt,name=my_supply,json=mySupply,proto3" json:"my_supply,omitempty"`
	RoundSupplyLimit     uint32              `protobuf:"varint,12,opt,name=round_supply_limit,json=roundSupplyLimit,proto3" json:"round_supply_limit,omitempty"`
	MinInvest            uint32              `protobuf:"varint,13,opt,name=min_invest,json=minInvest,proto3" json:"min_invest,omitempty"`
	UserDailyInvest      uint32              `protobuf:"varint,14,opt,name=user_daily_invest,json=userDailyInvest,proto3" json:"user_daily_invest,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetStatTrekInfoResp) Reset()         { *m = GetStatTrekInfoResp{} }
func (m *GetStatTrekInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetStatTrekInfoResp) ProtoMessage()    {}
func (*GetStatTrekInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_ee1e21e98b8bab9e, []int{6}
}
func (m *GetStatTrekInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStatTrekInfoResp.Unmarshal(m, b)
}
func (m *GetStatTrekInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStatTrekInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetStatTrekInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStatTrekInfoResp.Merge(dst, src)
}
func (m *GetStatTrekInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetStatTrekInfoResp.Size(m)
}
func (m *GetStatTrekInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStatTrekInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetStatTrekInfoResp proto.InternalMessageInfo

func (m *GetStatTrekInfoResp) GetRoundId() uint32 {
	if m != nil {
		return m.RoundId
	}
	return 0
}

func (m *GetStatTrekInfoResp) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetStatTrekInfoResp) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GetStatTrekInfoResp) GetAwardPack() *StarTAwardInfo {
	if m != nil {
		return m.AwardPack
	}
	return nil
}

func (m *GetStatTrekInfoResp) GetInvestProgress() uint32 {
	if m != nil {
		return m.InvestProgress
	}
	return 0
}

func (m *GetStatTrekInfoResp) GetUserCount() uint32 {
	if m != nil {
		return m.UserCount
	}
	return 0
}

func (m *GetStatTrekInfoResp) GetLastS() *LastRoundReview {
	if m != nil {
		return m.LastS
	}
	return nil
}

func (m *GetStatTrekInfoResp) GetNextS() *NextRoundForecast {
	if m != nil {
		return m.NextS
	}
	return nil
}

func (m *GetStatTrekInfoResp) GetRecords() []*RollingAwardInfo {
	if m != nil {
		return m.Records
	}
	return nil
}

func (m *GetStatTrekInfoResp) GetLatestPartitions() []*StarTUserInfo {
	if m != nil {
		return m.LatestPartitions
	}
	return nil
}

func (m *GetStatTrekInfoResp) GetMySupply() uint32 {
	if m != nil {
		return m.MySupply
	}
	return 0
}

func (m *GetStatTrekInfoResp) GetRoundSupplyLimit() uint32 {
	if m != nil {
		return m.RoundSupplyLimit
	}
	return 0
}

func (m *GetStatTrekInfoResp) GetMinInvest() uint32 {
	if m != nil {
		return m.MinInvest
	}
	return 0
}

func (m *GetStatTrekInfoResp) GetUserDailyInvest() uint32 {
	if m != nil {
		return m.UserDailyInvest
	}
	return 0
}

type GetCurAtcTimeAndRoundTimeReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCurAtcTimeAndRoundTimeReq) Reset()         { *m = GetCurAtcTimeAndRoundTimeReq{} }
func (m *GetCurAtcTimeAndRoundTimeReq) String() string { return proto.CompactTextString(m) }
func (*GetCurAtcTimeAndRoundTimeReq) ProtoMessage()    {}
func (*GetCurAtcTimeAndRoundTimeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_ee1e21e98b8bab9e, []int{7}
}
func (m *GetCurAtcTimeAndRoundTimeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCurAtcTimeAndRoundTimeReq.Unmarshal(m, b)
}
func (m *GetCurAtcTimeAndRoundTimeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCurAtcTimeAndRoundTimeReq.Marshal(b, m, deterministic)
}
func (dst *GetCurAtcTimeAndRoundTimeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCurAtcTimeAndRoundTimeReq.Merge(dst, src)
}
func (m *GetCurAtcTimeAndRoundTimeReq) XXX_Size() int {
	return xxx_messageInfo_GetCurAtcTimeAndRoundTimeReq.Size(m)
}
func (m *GetCurAtcTimeAndRoundTimeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCurAtcTimeAndRoundTimeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetCurAtcTimeAndRoundTimeReq proto.InternalMessageInfo

func (m *GetCurAtcTimeAndRoundTimeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetCurAtcTimeAndRoundTimeResp struct {
	ActId                uint32   `protobuf:"varint,1,opt,name=act_id,json=actId,proto3" json:"act_id,omitempty"`
	DayBeing             uint32   `protobuf:"varint,2,opt,name=day_being,json=dayBeing,proto3" json:"day_being,omitempty"`
	DayEnd               uint32   `protobuf:"varint,3,opt,name=day_end,json=dayEnd,proto3" json:"day_end,omitempty"`
	RoundId              uint32   `protobuf:"varint,4,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
	RoundBegin           uint32   `protobuf:"varint,5,opt,name=round_begin,json=roundBegin,proto3" json:"round_begin,omitempty"`
	RoundEnd             uint32   `protobuf:"varint,6,opt,name=round_end,json=roundEnd,proto3" json:"round_end,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCurAtcTimeAndRoundTimeResp) Reset()         { *m = GetCurAtcTimeAndRoundTimeResp{} }
func (m *GetCurAtcTimeAndRoundTimeResp) String() string { return proto.CompactTextString(m) }
func (*GetCurAtcTimeAndRoundTimeResp) ProtoMessage()    {}
func (*GetCurAtcTimeAndRoundTimeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_ee1e21e98b8bab9e, []int{8}
}
func (m *GetCurAtcTimeAndRoundTimeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCurAtcTimeAndRoundTimeResp.Unmarshal(m, b)
}
func (m *GetCurAtcTimeAndRoundTimeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCurAtcTimeAndRoundTimeResp.Marshal(b, m, deterministic)
}
func (dst *GetCurAtcTimeAndRoundTimeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCurAtcTimeAndRoundTimeResp.Merge(dst, src)
}
func (m *GetCurAtcTimeAndRoundTimeResp) XXX_Size() int {
	return xxx_messageInfo_GetCurAtcTimeAndRoundTimeResp.Size(m)
}
func (m *GetCurAtcTimeAndRoundTimeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCurAtcTimeAndRoundTimeResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetCurAtcTimeAndRoundTimeResp proto.InternalMessageInfo

func (m *GetCurAtcTimeAndRoundTimeResp) GetActId() uint32 {
	if m != nil {
		return m.ActId
	}
	return 0
}

func (m *GetCurAtcTimeAndRoundTimeResp) GetDayBeing() uint32 {
	if m != nil {
		return m.DayBeing
	}
	return 0
}

func (m *GetCurAtcTimeAndRoundTimeResp) GetDayEnd() uint32 {
	if m != nil {
		return m.DayEnd
	}
	return 0
}

func (m *GetCurAtcTimeAndRoundTimeResp) GetRoundId() uint32 {
	if m != nil {
		return m.RoundId
	}
	return 0
}

func (m *GetCurAtcTimeAndRoundTimeResp) GetRoundBegin() uint32 {
	if m != nil {
		return m.RoundBegin
	}
	return 0
}

func (m *GetCurAtcTimeAndRoundTimeResp) GetRoundEnd() uint32 {
	if m != nil {
		return m.RoundEnd
	}
	return 0
}

type GetSupplyValueChangeReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSupplyValueChangeReq) Reset()         { *m = GetSupplyValueChangeReq{} }
func (m *GetSupplyValueChangeReq) String() string { return proto.CompactTextString(m) }
func (*GetSupplyValueChangeReq) ProtoMessage()    {}
func (*GetSupplyValueChangeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_ee1e21e98b8bab9e, []int{9}
}
func (m *GetSupplyValueChangeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSupplyValueChangeReq.Unmarshal(m, b)
}
func (m *GetSupplyValueChangeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSupplyValueChangeReq.Marshal(b, m, deterministic)
}
func (dst *GetSupplyValueChangeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSupplyValueChangeReq.Merge(dst, src)
}
func (m *GetSupplyValueChangeReq) XXX_Size() int {
	return xxx_messageInfo_GetSupplyValueChangeReq.Size(m)
}
func (m *GetSupplyValueChangeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSupplyValueChangeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSupplyValueChangeReq proto.InternalMessageInfo

func (m *GetSupplyValueChangeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetSupplyValueChangeResp struct {
	ServerSupply         uint32   `protobuf:"varint,1,opt,name=server_supply,json=serverSupply,proto3" json:"server_supply,omitempty"`
	MySupply             uint32   `protobuf:"varint,2,opt,name=my_supply,json=mySupply,proto3" json:"my_supply,omitempty"`
	MinInvest            uint32   `protobuf:"varint,3,opt,name=min_invest,json=minInvest,proto3" json:"min_invest,omitempty"`
	RoundSupplyLimit     uint32   `protobuf:"varint,4,opt,name=round_supply_limit,json=roundSupplyLimit,proto3" json:"round_supply_limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSupplyValueChangeResp) Reset()         { *m = GetSupplyValueChangeResp{} }
func (m *GetSupplyValueChangeResp) String() string { return proto.CompactTextString(m) }
func (*GetSupplyValueChangeResp) ProtoMessage()    {}
func (*GetSupplyValueChangeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_ee1e21e98b8bab9e, []int{10}
}
func (m *GetSupplyValueChangeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSupplyValueChangeResp.Unmarshal(m, b)
}
func (m *GetSupplyValueChangeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSupplyValueChangeResp.Marshal(b, m, deterministic)
}
func (dst *GetSupplyValueChangeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSupplyValueChangeResp.Merge(dst, src)
}
func (m *GetSupplyValueChangeResp) XXX_Size() int {
	return xxx_messageInfo_GetSupplyValueChangeResp.Size(m)
}
func (m *GetSupplyValueChangeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSupplyValueChangeResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSupplyValueChangeResp proto.InternalMessageInfo

func (m *GetSupplyValueChangeResp) GetServerSupply() uint32 {
	if m != nil {
		return m.ServerSupply
	}
	return 0
}

func (m *GetSupplyValueChangeResp) GetMySupply() uint32 {
	if m != nil {
		return m.MySupply
	}
	return 0
}

func (m *GetSupplyValueChangeResp) GetMinInvest() uint32 {
	if m != nil {
		return m.MinInvest
	}
	return 0
}

func (m *GetSupplyValueChangeResp) GetRoundSupplyLimit() uint32 {
	if m != nil {
		return m.RoundSupplyLimit
	}
	return 0
}

type CostSupplyInfo struct {
	UserItemId           uint32   `protobuf:"varint,1,opt,name=user_item_id,json=userItemId,proto3" json:"user_item_id,omitempty"`
	GiftId               uint32   `protobuf:"varint,2,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	GiftType             uint32   `protobuf:"varint,3,opt,name=gift_type,json=giftType,proto3" json:"gift_type,omitempty"`
	GiftNum              uint32   `protobuf:"varint,4,opt,name=gift_num,json=giftNum,proto3" json:"gift_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CostSupplyInfo) Reset()         { *m = CostSupplyInfo{} }
func (m *CostSupplyInfo) String() string { return proto.CompactTextString(m) }
func (*CostSupplyInfo) ProtoMessage()    {}
func (*CostSupplyInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_ee1e21e98b8bab9e, []int{11}
}
func (m *CostSupplyInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CostSupplyInfo.Unmarshal(m, b)
}
func (m *CostSupplyInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CostSupplyInfo.Marshal(b, m, deterministic)
}
func (dst *CostSupplyInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CostSupplyInfo.Merge(dst, src)
}
func (m *CostSupplyInfo) XXX_Size() int {
	return xxx_messageInfo_CostSupplyInfo.Size(m)
}
func (m *CostSupplyInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_CostSupplyInfo.DiscardUnknown(m)
}

var xxx_messageInfo_CostSupplyInfo proto.InternalMessageInfo

func (m *CostSupplyInfo) GetUserItemId() uint32 {
	if m != nil {
		return m.UserItemId
	}
	return 0
}

func (m *CostSupplyInfo) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *CostSupplyInfo) GetGiftType() uint32 {
	if m != nil {
		return m.GiftType
	}
	return 0
}

func (m *CostSupplyInfo) GetGiftNum() uint32 {
	if m != nil {
		return m.GiftNum
	}
	return 0
}

// 去探险
type DoInvestReq struct {
	Uid                  uint32            `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32            `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	RoundId              uint32            `protobuf:"varint,3,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
	SupplyList           []*CostSupplyInfo `protobuf:"bytes,4,rep,name=supply_list,json=supplyList,proto3" json:"supply_list,omitempty"`
	Cost                 uint32            `protobuf:"varint,5,opt,name=cost,proto3" json:"cost,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *DoInvestReq) Reset()         { *m = DoInvestReq{} }
func (m *DoInvestReq) String() string { return proto.CompactTextString(m) }
func (*DoInvestReq) ProtoMessage()    {}
func (*DoInvestReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_ee1e21e98b8bab9e, []int{12}
}
func (m *DoInvestReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DoInvestReq.Unmarshal(m, b)
}
func (m *DoInvestReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DoInvestReq.Marshal(b, m, deterministic)
}
func (dst *DoInvestReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DoInvestReq.Merge(dst, src)
}
func (m *DoInvestReq) XXX_Size() int {
	return xxx_messageInfo_DoInvestReq.Size(m)
}
func (m *DoInvestReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DoInvestReq.DiscardUnknown(m)
}

var xxx_messageInfo_DoInvestReq proto.InternalMessageInfo

func (m *DoInvestReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DoInvestReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *DoInvestReq) GetRoundId() uint32 {
	if m != nil {
		return m.RoundId
	}
	return 0
}

func (m *DoInvestReq) GetSupplyList() []*CostSupplyInfo {
	if m != nil {
		return m.SupplyList
	}
	return nil
}

func (m *DoInvestReq) GetCost() uint32 {
	if m != nil {
		return m.Cost
	}
	return 0
}

type DoInvestResp struct {
	RoundId              uint32   `protobuf:"varint,1,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
	MySupply             uint32   `protobuf:"varint,2,opt,name=my_supply,json=mySupply,proto3" json:"my_supply,omitempty"`
	RoundSupplyLimit     uint32   `protobuf:"varint,3,opt,name=round_supply_limit,json=roundSupplyLimit,proto3" json:"round_supply_limit,omitempty"`
	ServerSupply         uint32   `protobuf:"varint,4,opt,name=server_supply,json=serverSupply,proto3" json:"server_supply,omitempty"`
	DailyInvest          uint32   `protobuf:"varint,5,opt,name=daily_invest,json=dailyInvest,proto3" json:"daily_invest,omitempty"`
	GoalServerSupply     uint32   `protobuf:"varint,6,opt,name=goal_server_supply,json=goalServerSupply,proto3" json:"goal_server_supply,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DoInvestResp) Reset()         { *m = DoInvestResp{} }
func (m *DoInvestResp) String() string { return proto.CompactTextString(m) }
func (*DoInvestResp) ProtoMessage()    {}
func (*DoInvestResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_ee1e21e98b8bab9e, []int{13}
}
func (m *DoInvestResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DoInvestResp.Unmarshal(m, b)
}
func (m *DoInvestResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DoInvestResp.Marshal(b, m, deterministic)
}
func (dst *DoInvestResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DoInvestResp.Merge(dst, src)
}
func (m *DoInvestResp) XXX_Size() int {
	return xxx_messageInfo_DoInvestResp.Size(m)
}
func (m *DoInvestResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DoInvestResp.DiscardUnknown(m)
}

var xxx_messageInfo_DoInvestResp proto.InternalMessageInfo

func (m *DoInvestResp) GetRoundId() uint32 {
	if m != nil {
		return m.RoundId
	}
	return 0
}

func (m *DoInvestResp) GetMySupply() uint32 {
	if m != nil {
		return m.MySupply
	}
	return 0
}

func (m *DoInvestResp) GetRoundSupplyLimit() uint32 {
	if m != nil {
		return m.RoundSupplyLimit
	}
	return 0
}

func (m *DoInvestResp) GetServerSupply() uint32 {
	if m != nil {
		return m.ServerSupply
	}
	return 0
}

func (m *DoInvestResp) GetDailyInvest() uint32 {
	if m != nil {
		return m.DailyInvest
	}
	return 0
}

func (m *DoInvestResp) GetGoalServerSupply() uint32 {
	if m != nil {
		return m.GoalServerSupply
	}
	return 0
}

// 获取用户巡航记录
type GetMyTrekRecordReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TrekResult           uint32   `protobuf:"varint,2,opt,name=trek_result,json=trekResult,proto3" json:"trek_result,omitempty"`
	LastPageFinalId      string   `protobuf:"bytes,3,opt,name=last_page_final_id,json=lastPageFinalId,proto3" json:"last_page_final_id,omitempty"`
	Limit                uint32   `protobuf:"varint,4,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMyTrekRecordReq) Reset()         { *m = GetMyTrekRecordReq{} }
func (m *GetMyTrekRecordReq) String() string { return proto.CompactTextString(m) }
func (*GetMyTrekRecordReq) ProtoMessage()    {}
func (*GetMyTrekRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_ee1e21e98b8bab9e, []int{14}
}
func (m *GetMyTrekRecordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMyTrekRecordReq.Unmarshal(m, b)
}
func (m *GetMyTrekRecordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMyTrekRecordReq.Marshal(b, m, deterministic)
}
func (dst *GetMyTrekRecordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMyTrekRecordReq.Merge(dst, src)
}
func (m *GetMyTrekRecordReq) XXX_Size() int {
	return xxx_messageInfo_GetMyTrekRecordReq.Size(m)
}
func (m *GetMyTrekRecordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMyTrekRecordReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMyTrekRecordReq proto.InternalMessageInfo

func (m *GetMyTrekRecordReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetMyTrekRecordReq) GetTrekResult() uint32 {
	if m != nil {
		return m.TrekResult
	}
	return 0
}

func (m *GetMyTrekRecordReq) GetLastPageFinalId() string {
	if m != nil {
		return m.LastPageFinalId
	}
	return ""
}

func (m *GetMyTrekRecordReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type MyTrekRecord struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	RoundId              uint32   `protobuf:"varint,3,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
	RoundTime            uint32   `protobuf:"varint,4,opt,name=round_time,json=roundTime,proto3" json:"round_time,omitempty"`
	TrekResult           bool     `protobuf:"varint,5,opt,name=trek_result,json=trekResult,proto3" json:"trek_result,omitempty"`
	PrizeName            string   `protobuf:"bytes,6,opt,name=prize_name,json=prizeName,proto3" json:"prize_name,omitempty"`
	PrizePic             string   `protobuf:"bytes,7,opt,name=Prize_pic,json=PrizePic,proto3" json:"Prize_pic,omitempty"`
	LuckyUid             uint32   `protobuf:"varint,8,opt,name=lucky_uid,json=luckyUid,proto3" json:"lucky_uid,omitempty"`
	MyInvest             uint32   `protobuf:"varint,9,opt,name=my_invest,json=myInvest,proto3" json:"my_invest,omitempty"`
	MySupplyDec          string   `protobuf:"bytes,10,opt,name=my_supply_dec,json=mySupplyDec,proto3" json:"my_supply_dec,omitempty"`
	MyAwardDec           string   `protobuf:"bytes,11,opt,name=my_award_dec,json=myAwardDec,proto3" json:"my_award_dec,omitempty"`
	LuckyUserAmount      uint32   `protobuf:"varint,12,opt,name=lucky_user_amount,json=luckyUserAmount,proto3" json:"lucky_user_amount,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MyTrekRecord) Reset()         { *m = MyTrekRecord{} }
func (m *MyTrekRecord) String() string { return proto.CompactTextString(m) }
func (*MyTrekRecord) ProtoMessage()    {}
func (*MyTrekRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_ee1e21e98b8bab9e, []int{15}
}
func (m *MyTrekRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MyTrekRecord.Unmarshal(m, b)
}
func (m *MyTrekRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MyTrekRecord.Marshal(b, m, deterministic)
}
func (dst *MyTrekRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MyTrekRecord.Merge(dst, src)
}
func (m *MyTrekRecord) XXX_Size() int {
	return xxx_messageInfo_MyTrekRecord.Size(m)
}
func (m *MyTrekRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_MyTrekRecord.DiscardUnknown(m)
}

var xxx_messageInfo_MyTrekRecord proto.InternalMessageInfo

func (m *MyTrekRecord) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *MyTrekRecord) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MyTrekRecord) GetRoundId() uint32 {
	if m != nil {
		return m.RoundId
	}
	return 0
}

func (m *MyTrekRecord) GetRoundTime() uint32 {
	if m != nil {
		return m.RoundTime
	}
	return 0
}

func (m *MyTrekRecord) GetTrekResult() bool {
	if m != nil {
		return m.TrekResult
	}
	return false
}

func (m *MyTrekRecord) GetPrizeName() string {
	if m != nil {
		return m.PrizeName
	}
	return ""
}

func (m *MyTrekRecord) GetPrizePic() string {
	if m != nil {
		return m.PrizePic
	}
	return ""
}

func (m *MyTrekRecord) GetLuckyUid() uint32 {
	if m != nil {
		return m.LuckyUid
	}
	return 0
}

func (m *MyTrekRecord) GetMyInvest() uint32 {
	if m != nil {
		return m.MyInvest
	}
	return 0
}

func (m *MyTrekRecord) GetMySupplyDec() string {
	if m != nil {
		return m.MySupplyDec
	}
	return ""
}

func (m *MyTrekRecord) GetMyAwardDec() string {
	if m != nil {
		return m.MyAwardDec
	}
	return ""
}

func (m *MyTrekRecord) GetLuckyUserAmount() uint32 {
	if m != nil {
		return m.LuckyUserAmount
	}
	return 0
}

type GetMyTrekRecordResp struct {
	MyRecordList         []*MyTrekRecord `protobuf:"bytes,1,rep,name=my_record_list,json=myRecordList,proto3" json:"my_record_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetMyTrekRecordResp) Reset()         { *m = GetMyTrekRecordResp{} }
func (m *GetMyTrekRecordResp) String() string { return proto.CompactTextString(m) }
func (*GetMyTrekRecordResp) ProtoMessage()    {}
func (*GetMyTrekRecordResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_ee1e21e98b8bab9e, []int{16}
}
func (m *GetMyTrekRecordResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMyTrekRecordResp.Unmarshal(m, b)
}
func (m *GetMyTrekRecordResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMyTrekRecordResp.Marshal(b, m, deterministic)
}
func (dst *GetMyTrekRecordResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMyTrekRecordResp.Merge(dst, src)
}
func (m *GetMyTrekRecordResp) XXX_Size() int {
	return xxx_messageInfo_GetMyTrekRecordResp.Size(m)
}
func (m *GetMyTrekRecordResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMyTrekRecordResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMyTrekRecordResp proto.InternalMessageInfo

func (m *GetMyTrekRecordResp) GetMyRecordList() []*MyTrekRecord {
	if m != nil {
		return m.MyRecordList
	}
	return nil
}

// 往期回顾
type GetAllTrekHistoryReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Offset               uint32   `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllTrekHistoryReq) Reset()         { *m = GetAllTrekHistoryReq{} }
func (m *GetAllTrekHistoryReq) String() string { return proto.CompactTextString(m) }
func (*GetAllTrekHistoryReq) ProtoMessage()    {}
func (*GetAllTrekHistoryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_ee1e21e98b8bab9e, []int{17}
}
func (m *GetAllTrekHistoryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllTrekHistoryReq.Unmarshal(m, b)
}
func (m *GetAllTrekHistoryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllTrekHistoryReq.Marshal(b, m, deterministic)
}
func (dst *GetAllTrekHistoryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllTrekHistoryReq.Merge(dst, src)
}
func (m *GetAllTrekHistoryReq) XXX_Size() int {
	return xxx_messageInfo_GetAllTrekHistoryReq.Size(m)
}
func (m *GetAllTrekHistoryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllTrekHistoryReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllTrekHistoryReq proto.InternalMessageInfo

func (m *GetAllTrekHistoryReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetAllTrekHistoryReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetAllTrekHistoryReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetAllTrekHistoryResp struct {
	ReviewList           []*LastRoundReview `protobuf:"bytes,1,rep,name=review_list,json=reviewList,proto3" json:"review_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetAllTrekHistoryResp) Reset()         { *m = GetAllTrekHistoryResp{} }
func (m *GetAllTrekHistoryResp) String() string { return proto.CompactTextString(m) }
func (*GetAllTrekHistoryResp) ProtoMessage()    {}
func (*GetAllTrekHistoryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_ee1e21e98b8bab9e, []int{18}
}
func (m *GetAllTrekHistoryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllTrekHistoryResp.Unmarshal(m, b)
}
func (m *GetAllTrekHistoryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllTrekHistoryResp.Marshal(b, m, deterministic)
}
func (dst *GetAllTrekHistoryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllTrekHistoryResp.Merge(dst, src)
}
func (m *GetAllTrekHistoryResp) XXX_Size() int {
	return xxx_messageInfo_GetAllTrekHistoryResp.Size(m)
}
func (m *GetAllTrekHistoryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllTrekHistoryResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllTrekHistoryResp proto.InternalMessageInfo

func (m *GetAllTrekHistoryResp) GetReviewList() []*LastRoundReview {
	if m != nil {
		return m.ReviewList
	}
	return nil
}

// 星际礼物信息
type StarTrekPrizeInfo struct {
	BgId                 uint32   `protobuf:"varint,1,opt,name=bg_id,json=bgId,proto3" json:"bg_id,omitempty"`
	Weight               uint32   `protobuf:"varint,2,opt,name=weight,proto3" json:"weight,omitempty"`
	GiftId               uint32   `protobuf:"varint,3,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	GiftPrice            uint32   `protobuf:"varint,4,opt,name=gift_price,json=giftPrice,proto3" json:"gift_price,omitempty"`
	GiftName             string   `protobuf:"bytes,5,opt,name=gift_name,json=giftName,proto3" json:"gift_name,omitempty"`
	GiftPic              string   `protobuf:"bytes,6,opt,name=gift_pic,json=giftPic,proto3" json:"gift_pic,omitempty"`
	GiftNum              uint32   `protobuf:"varint,7,opt,name=gift_num,json=giftNum,proto3" json:"gift_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StarTrekPrizeInfo) Reset()         { *m = StarTrekPrizeInfo{} }
func (m *StarTrekPrizeInfo) String() string { return proto.CompactTextString(m) }
func (*StarTrekPrizeInfo) ProtoMessage()    {}
func (*StarTrekPrizeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_ee1e21e98b8bab9e, []int{19}
}
func (m *StarTrekPrizeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StarTrekPrizeInfo.Unmarshal(m, b)
}
func (m *StarTrekPrizeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StarTrekPrizeInfo.Marshal(b, m, deterministic)
}
func (dst *StarTrekPrizeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StarTrekPrizeInfo.Merge(dst, src)
}
func (m *StarTrekPrizeInfo) XXX_Size() int {
	return xxx_messageInfo_StarTrekPrizeInfo.Size(m)
}
func (m *StarTrekPrizeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_StarTrekPrizeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_StarTrekPrizeInfo proto.InternalMessageInfo

func (m *StarTrekPrizeInfo) GetBgId() uint32 {
	if m != nil {
		return m.BgId
	}
	return 0
}

func (m *StarTrekPrizeInfo) GetWeight() uint32 {
	if m != nil {
		return m.Weight
	}
	return 0
}

func (m *StarTrekPrizeInfo) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *StarTrekPrizeInfo) GetGiftPrice() uint32 {
	if m != nil {
		return m.GiftPrice
	}
	return 0
}

func (m *StarTrekPrizeInfo) GetGiftName() string {
	if m != nil {
		return m.GiftName
	}
	return ""
}

func (m *StarTrekPrizeInfo) GetGiftPic() string {
	if m != nil {
		return m.GiftPic
	}
	return ""
}

func (m *StarTrekPrizeInfo) GetGiftNum() uint32 {
	if m != nil {
		return m.GiftNum
	}
	return 0
}

// 开启时间段配置
type DayTimeRangeInfo struct {
	Selected             bool     `protobuf:"varint,1,opt,name=selected,proto3" json:"selected,omitempty"`
	DayBegin             uint32   `protobuf:"varint,5,opt,name=day_begin,json=dayBegin,proto3" json:"day_begin,omitempty"`
	DayEnd               uint32   `protobuf:"varint,6,opt,name=day_end,json=dayEnd,proto3" json:"day_end,omitempty"`
	WDayList             []uint32 `protobuf:"varint,7,rep,packed,name=w_day_list,json=wDayList,proto3" json:"w_day_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DayTimeRangeInfo) Reset()         { *m = DayTimeRangeInfo{} }
func (m *DayTimeRangeInfo) String() string { return proto.CompactTextString(m) }
func (*DayTimeRangeInfo) ProtoMessage()    {}
func (*DayTimeRangeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_ee1e21e98b8bab9e, []int{20}
}
func (m *DayTimeRangeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DayTimeRangeInfo.Unmarshal(m, b)
}
func (m *DayTimeRangeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DayTimeRangeInfo.Marshal(b, m, deterministic)
}
func (dst *DayTimeRangeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DayTimeRangeInfo.Merge(dst, src)
}
func (m *DayTimeRangeInfo) XXX_Size() int {
	return xxx_messageInfo_DayTimeRangeInfo.Size(m)
}
func (m *DayTimeRangeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_DayTimeRangeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_DayTimeRangeInfo proto.InternalMessageInfo

func (m *DayTimeRangeInfo) GetSelected() bool {
	if m != nil {
		return m.Selected
	}
	return false
}

func (m *DayTimeRangeInfo) GetDayBegin() uint32 {
	if m != nil {
		return m.DayBegin
	}
	return 0
}

func (m *DayTimeRangeInfo) GetDayEnd() uint32 {
	if m != nil {
		return m.DayEnd
	}
	return 0
}

func (m *DayTimeRangeInfo) GetWDayList() []uint32 {
	if m != nil {
		return m.WDayList
	}
	return nil
}

// 活动配置
type StarTrekConf struct {
	ConfId               uint32               `protobuf:"varint,1,opt,name=conf_id,json=confId,proto3" json:"conf_id,omitempty"`
	ActivityName         string               `protobuf:"bytes,2,opt,name=activity_name,json=activityName,proto3" json:"activity_name,omitempty"`
	ActivityBegin        uint32               `protobuf:"varint,3,opt,name=activity_begin,json=activityBegin,proto3" json:"activity_begin,omitempty"`
	ActivityEnd          uint32               `protobuf:"varint,4,opt,name=activity_end,json=activityEnd,proto3" json:"activity_end,omitempty"`
	DayBegin             uint32               `protobuf:"varint,5,opt,name=day_begin,json=dayBegin,proto3" json:"day_begin,omitempty"`
	DayEnd               uint32               `protobuf:"varint,6,opt,name=day_end,json=dayEnd,proto3" json:"day_end,omitempty"`
	WDayList             []uint32             `protobuf:"varint,7,rep,packed,name=w_day_list,json=wDayList,proto3" json:"w_day_list,omitempty"`
	PrizeList            []*StarTrekPrizeInfo `protobuf:"bytes,8,rep,name=prize_list,json=prizeList,proto3" json:"prize_list,omitempty"`
	TimeList             []*DayTimeRangeInfo  `protobuf:"bytes,9,rep,name=time_list,json=timeList,proto3" json:"time_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *StarTrekConf) Reset()         { *m = StarTrekConf{} }
func (m *StarTrekConf) String() string { return proto.CompactTextString(m) }
func (*StarTrekConf) ProtoMessage()    {}
func (*StarTrekConf) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_ee1e21e98b8bab9e, []int{21}
}
func (m *StarTrekConf) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StarTrekConf.Unmarshal(m, b)
}
func (m *StarTrekConf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StarTrekConf.Marshal(b, m, deterministic)
}
func (dst *StarTrekConf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StarTrekConf.Merge(dst, src)
}
func (m *StarTrekConf) XXX_Size() int {
	return xxx_messageInfo_StarTrekConf.Size(m)
}
func (m *StarTrekConf) XXX_DiscardUnknown() {
	xxx_messageInfo_StarTrekConf.DiscardUnknown(m)
}

var xxx_messageInfo_StarTrekConf proto.InternalMessageInfo

func (m *StarTrekConf) GetConfId() uint32 {
	if m != nil {
		return m.ConfId
	}
	return 0
}

func (m *StarTrekConf) GetActivityName() string {
	if m != nil {
		return m.ActivityName
	}
	return ""
}

func (m *StarTrekConf) GetActivityBegin() uint32 {
	if m != nil {
		return m.ActivityBegin
	}
	return 0
}

func (m *StarTrekConf) GetActivityEnd() uint32 {
	if m != nil {
		return m.ActivityEnd
	}
	return 0
}

func (m *StarTrekConf) GetDayBegin() uint32 {
	if m != nil {
		return m.DayBegin
	}
	return 0
}

func (m *StarTrekConf) GetDayEnd() uint32 {
	if m != nil {
		return m.DayEnd
	}
	return 0
}

func (m *StarTrekConf) GetWDayList() []uint32 {
	if m != nil {
		return m.WDayList
	}
	return nil
}

func (m *StarTrekConf) GetPrizeList() []*StarTrekPrizeInfo {
	if m != nil {
		return m.PrizeList
	}
	return nil
}

func (m *StarTrekConf) GetTimeList() []*DayTimeRangeInfo {
	if m != nil {
		return m.TimeList
	}
	return nil
}

type SetStarTrekConfReq struct {
	Conf                 *StarTrekConf `protobuf:"bytes,1,opt,name=conf,proto3" json:"conf,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetStarTrekConfReq) Reset()         { *m = SetStarTrekConfReq{} }
func (m *SetStarTrekConfReq) String() string { return proto.CompactTextString(m) }
func (*SetStarTrekConfReq) ProtoMessage()    {}
func (*SetStarTrekConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_ee1e21e98b8bab9e, []int{22}
}
func (m *SetStarTrekConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetStarTrekConfReq.Unmarshal(m, b)
}
func (m *SetStarTrekConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetStarTrekConfReq.Marshal(b, m, deterministic)
}
func (dst *SetStarTrekConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetStarTrekConfReq.Merge(dst, src)
}
func (m *SetStarTrekConfReq) XXX_Size() int {
	return xxx_messageInfo_SetStarTrekConfReq.Size(m)
}
func (m *SetStarTrekConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetStarTrekConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetStarTrekConfReq proto.InternalMessageInfo

func (m *SetStarTrekConfReq) GetConf() *StarTrekConf {
	if m != nil {
		return m.Conf
	}
	return nil
}

type SetStarTrekConfResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetStarTrekConfResp) Reset()         { *m = SetStarTrekConfResp{} }
func (m *SetStarTrekConfResp) String() string { return proto.CompactTextString(m) }
func (*SetStarTrekConfResp) ProtoMessage()    {}
func (*SetStarTrekConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_ee1e21e98b8bab9e, []int{23}
}
func (m *SetStarTrekConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetStarTrekConfResp.Unmarshal(m, b)
}
func (m *SetStarTrekConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetStarTrekConfResp.Marshal(b, m, deterministic)
}
func (dst *SetStarTrekConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetStarTrekConfResp.Merge(dst, src)
}
func (m *SetStarTrekConfResp) XXX_Size() int {
	return xxx_messageInfo_SetStarTrekConfResp.Size(m)
}
func (m *SetStarTrekConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetStarTrekConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetStarTrekConfResp proto.InternalMessageInfo

type GetStarTrekConfReq struct {
	Status               StarTrekStatus `protobuf:"varint,1,opt,name=status,proto3,enum=star_trek.StarTrekStatus" json:"status,omitempty"`
	Limit                uint32         `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	Offset               uint32         `protobuf:"varint,3,opt,name=offset,proto3" json:"offset,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetStarTrekConfReq) Reset()         { *m = GetStarTrekConfReq{} }
func (m *GetStarTrekConfReq) String() string { return proto.CompactTextString(m) }
func (*GetStarTrekConfReq) ProtoMessage()    {}
func (*GetStarTrekConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_ee1e21e98b8bab9e, []int{24}
}
func (m *GetStarTrekConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStarTrekConfReq.Unmarshal(m, b)
}
func (m *GetStarTrekConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStarTrekConfReq.Marshal(b, m, deterministic)
}
func (dst *GetStarTrekConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStarTrekConfReq.Merge(dst, src)
}
func (m *GetStarTrekConfReq) XXX_Size() int {
	return xxx_messageInfo_GetStarTrekConfReq.Size(m)
}
func (m *GetStarTrekConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStarTrekConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetStarTrekConfReq proto.InternalMessageInfo

func (m *GetStarTrekConfReq) GetStatus() StarTrekStatus {
	if m != nil {
		return m.Status
	}
	return StarTrekStatus_AllStatus
}

func (m *GetStarTrekConfReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetStarTrekConfReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

type GetStarTrekConfResp struct {
	ConfList             []*StarTrekConf `protobuf:"bytes,1,rep,name=conf_list,json=confList,proto3" json:"conf_list,omitempty"`
	Total                uint32          `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetStarTrekConfResp) Reset()         { *m = GetStarTrekConfResp{} }
func (m *GetStarTrekConfResp) String() string { return proto.CompactTextString(m) }
func (*GetStarTrekConfResp) ProtoMessage()    {}
func (*GetStarTrekConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_ee1e21e98b8bab9e, []int{25}
}
func (m *GetStarTrekConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStarTrekConfResp.Unmarshal(m, b)
}
func (m *GetStarTrekConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStarTrekConfResp.Marshal(b, m, deterministic)
}
func (dst *GetStarTrekConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStarTrekConfResp.Merge(dst, src)
}
func (m *GetStarTrekConfResp) XXX_Size() int {
	return xxx_messageInfo_GetStarTrekConfResp.Size(m)
}
func (m *GetStarTrekConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStarTrekConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetStarTrekConfResp proto.InternalMessageInfo

func (m *GetStarTrekConfResp) GetConfList() []*StarTrekConf {
	if m != nil {
		return m.ConfList
	}
	return nil
}

func (m *GetStarTrekConfResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type GetStarTrekConfByIdReq struct {
	ConfId               []uint32 `protobuf:"varint,1,rep,packed,name=conf_id,json=confId,proto3" json:"conf_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetStarTrekConfByIdReq) Reset()         { *m = GetStarTrekConfByIdReq{} }
func (m *GetStarTrekConfByIdReq) String() string { return proto.CompactTextString(m) }
func (*GetStarTrekConfByIdReq) ProtoMessage()    {}
func (*GetStarTrekConfByIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_ee1e21e98b8bab9e, []int{26}
}
func (m *GetStarTrekConfByIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStarTrekConfByIdReq.Unmarshal(m, b)
}
func (m *GetStarTrekConfByIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStarTrekConfByIdReq.Marshal(b, m, deterministic)
}
func (dst *GetStarTrekConfByIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStarTrekConfByIdReq.Merge(dst, src)
}
func (m *GetStarTrekConfByIdReq) XXX_Size() int {
	return xxx_messageInfo_GetStarTrekConfByIdReq.Size(m)
}
func (m *GetStarTrekConfByIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStarTrekConfByIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetStarTrekConfByIdReq proto.InternalMessageInfo

func (m *GetStarTrekConfByIdReq) GetConfId() []uint32 {
	if m != nil {
		return m.ConfId
	}
	return nil
}

type GetStarTrekConfByIdResp struct {
	ConfList             []*StarTrekConf `protobuf:"bytes,1,rep,name=conf_list,json=confList,proto3" json:"conf_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetStarTrekConfByIdResp) Reset()         { *m = GetStarTrekConfByIdResp{} }
func (m *GetStarTrekConfByIdResp) String() string { return proto.CompactTextString(m) }
func (*GetStarTrekConfByIdResp) ProtoMessage()    {}
func (*GetStarTrekConfByIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_ee1e21e98b8bab9e, []int{27}
}
func (m *GetStarTrekConfByIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStarTrekConfByIdResp.Unmarshal(m, b)
}
func (m *GetStarTrekConfByIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStarTrekConfByIdResp.Marshal(b, m, deterministic)
}
func (dst *GetStarTrekConfByIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStarTrekConfByIdResp.Merge(dst, src)
}
func (m *GetStarTrekConfByIdResp) XXX_Size() int {
	return xxx_messageInfo_GetStarTrekConfByIdResp.Size(m)
}
func (m *GetStarTrekConfByIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStarTrekConfByIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetStarTrekConfByIdResp proto.InternalMessageInfo

func (m *GetStarTrekConfByIdResp) GetConfList() []*StarTrekConf {
	if m != nil {
		return m.ConfList
	}
	return nil
}

type UpdateStarTrekConfReq struct {
	ConfId               uint32        `protobuf:"varint,1,opt,name=conf_id,json=confId,proto3" json:"conf_id,omitempty"`
	Conf                 *StarTrekConf `protobuf:"bytes,2,opt,name=conf,proto3" json:"conf,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *UpdateStarTrekConfReq) Reset()         { *m = UpdateStarTrekConfReq{} }
func (m *UpdateStarTrekConfReq) String() string { return proto.CompactTextString(m) }
func (*UpdateStarTrekConfReq) ProtoMessage()    {}
func (*UpdateStarTrekConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_ee1e21e98b8bab9e, []int{28}
}
func (m *UpdateStarTrekConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateStarTrekConfReq.Unmarshal(m, b)
}
func (m *UpdateStarTrekConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateStarTrekConfReq.Marshal(b, m, deterministic)
}
func (dst *UpdateStarTrekConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateStarTrekConfReq.Merge(dst, src)
}
func (m *UpdateStarTrekConfReq) XXX_Size() int {
	return xxx_messageInfo_UpdateStarTrekConfReq.Size(m)
}
func (m *UpdateStarTrekConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateStarTrekConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateStarTrekConfReq proto.InternalMessageInfo

func (m *UpdateStarTrekConfReq) GetConfId() uint32 {
	if m != nil {
		return m.ConfId
	}
	return 0
}

func (m *UpdateStarTrekConfReq) GetConf() *StarTrekConf {
	if m != nil {
		return m.Conf
	}
	return nil
}

type UpdateStarTrekConfResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateStarTrekConfResp) Reset()         { *m = UpdateStarTrekConfResp{} }
func (m *UpdateStarTrekConfResp) String() string { return proto.CompactTextString(m) }
func (*UpdateStarTrekConfResp) ProtoMessage()    {}
func (*UpdateStarTrekConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_ee1e21e98b8bab9e, []int{29}
}
func (m *UpdateStarTrekConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateStarTrekConfResp.Unmarshal(m, b)
}
func (m *UpdateStarTrekConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateStarTrekConfResp.Marshal(b, m, deterministic)
}
func (dst *UpdateStarTrekConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateStarTrekConfResp.Merge(dst, src)
}
func (m *UpdateStarTrekConfResp) XXX_Size() int {
	return xxx_messageInfo_UpdateStarTrekConfResp.Size(m)
}
func (m *UpdateStarTrekConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateStarTrekConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateStarTrekConfResp proto.InternalMessageInfo

type DelStarTrekConfReq struct {
	ConfId               uint32   `protobuf:"varint,1,opt,name=conf_id,json=confId,proto3" json:"conf_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelStarTrekConfReq) Reset()         { *m = DelStarTrekConfReq{} }
func (m *DelStarTrekConfReq) String() string { return proto.CompactTextString(m) }
func (*DelStarTrekConfReq) ProtoMessage()    {}
func (*DelStarTrekConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_ee1e21e98b8bab9e, []int{30}
}
func (m *DelStarTrekConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelStarTrekConfReq.Unmarshal(m, b)
}
func (m *DelStarTrekConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelStarTrekConfReq.Marshal(b, m, deterministic)
}
func (dst *DelStarTrekConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelStarTrekConfReq.Merge(dst, src)
}
func (m *DelStarTrekConfReq) XXX_Size() int {
	return xxx_messageInfo_DelStarTrekConfReq.Size(m)
}
func (m *DelStarTrekConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelStarTrekConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelStarTrekConfReq proto.InternalMessageInfo

func (m *DelStarTrekConfReq) GetConfId() uint32 {
	if m != nil {
		return m.ConfId
	}
	return 0
}

type DelStarTrekConfResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelStarTrekConfResp) Reset()         { *m = DelStarTrekConfResp{} }
func (m *DelStarTrekConfResp) String() string { return proto.CompactTextString(m) }
func (*DelStarTrekConfResp) ProtoMessage()    {}
func (*DelStarTrekConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_ee1e21e98b8bab9e, []int{31}
}
func (m *DelStarTrekConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelStarTrekConfResp.Unmarshal(m, b)
}
func (m *DelStarTrekConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelStarTrekConfResp.Marshal(b, m, deterministic)
}
func (dst *DelStarTrekConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelStarTrekConfResp.Merge(dst, src)
}
func (m *DelStarTrekConfResp) XXX_Size() int {
	return xxx_messageInfo_DelStarTrekConfResp.Size(m)
}
func (m *DelStarTrekConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelStarTrekConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelStarTrekConfResp proto.InternalMessageInfo

type SupplyConf struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	GiftId               uint32   `protobuf:"varint,2,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	GiftType             uint32   `protobuf:"varint,3,opt,name=gift_type,json=giftType,proto3" json:"gift_type,omitempty"`
	GiftPrice            uint32   `protobuf:"varint,4,opt,name=gift_price,json=giftPrice,proto3" json:"gift_price,omitempty"`
	GiftName             string   `protobuf:"bytes,5,opt,name=gift_name,json=giftName,proto3" json:"gift_name,omitempty"`
	GiftPic              string   `protobuf:"bytes,6,opt,name=gift_pic,json=giftPic,proto3" json:"gift_pic,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SupplyConf) Reset()         { *m = SupplyConf{} }
func (m *SupplyConf) String() string { return proto.CompactTextString(m) }
func (*SupplyConf) ProtoMessage()    {}
func (*SupplyConf) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_ee1e21e98b8bab9e, []int{32}
}
func (m *SupplyConf) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SupplyConf.Unmarshal(m, b)
}
func (m *SupplyConf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SupplyConf.Marshal(b, m, deterministic)
}
func (dst *SupplyConf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SupplyConf.Merge(dst, src)
}
func (m *SupplyConf) XXX_Size() int {
	return xxx_messageInfo_SupplyConf.Size(m)
}
func (m *SupplyConf) XXX_DiscardUnknown() {
	xxx_messageInfo_SupplyConf.DiscardUnknown(m)
}

var xxx_messageInfo_SupplyConf proto.InternalMessageInfo

func (m *SupplyConf) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *SupplyConf) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *SupplyConf) GetGiftType() uint32 {
	if m != nil {
		return m.GiftType
	}
	return 0
}

func (m *SupplyConf) GetGiftPrice() uint32 {
	if m != nil {
		return m.GiftPrice
	}
	return 0
}

func (m *SupplyConf) GetGiftName() string {
	if m != nil {
		return m.GiftName
	}
	return ""
}

func (m *SupplyConf) GetGiftPic() string {
	if m != nil {
		return m.GiftPic
	}
	return ""
}

// 设置补给配置
type SetStarTrekSupplyReq struct {
	SupplyList           []*SupplyConf `protobuf:"bytes,1,rep,name=supply_list,json=supplyList,proto3" json:"supply_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetStarTrekSupplyReq) Reset()         { *m = SetStarTrekSupplyReq{} }
func (m *SetStarTrekSupplyReq) String() string { return proto.CompactTextString(m) }
func (*SetStarTrekSupplyReq) ProtoMessage()    {}
func (*SetStarTrekSupplyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_ee1e21e98b8bab9e, []int{33}
}
func (m *SetStarTrekSupplyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetStarTrekSupplyReq.Unmarshal(m, b)
}
func (m *SetStarTrekSupplyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetStarTrekSupplyReq.Marshal(b, m, deterministic)
}
func (dst *SetStarTrekSupplyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetStarTrekSupplyReq.Merge(dst, src)
}
func (m *SetStarTrekSupplyReq) XXX_Size() int {
	return xxx_messageInfo_SetStarTrekSupplyReq.Size(m)
}
func (m *SetStarTrekSupplyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetStarTrekSupplyReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetStarTrekSupplyReq proto.InternalMessageInfo

func (m *SetStarTrekSupplyReq) GetSupplyList() []*SupplyConf {
	if m != nil {
		return m.SupplyList
	}
	return nil
}

type SetStarTrekSupplyResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetStarTrekSupplyResp) Reset()         { *m = SetStarTrekSupplyResp{} }
func (m *SetStarTrekSupplyResp) String() string { return proto.CompactTextString(m) }
func (*SetStarTrekSupplyResp) ProtoMessage()    {}
func (*SetStarTrekSupplyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_ee1e21e98b8bab9e, []int{34}
}
func (m *SetStarTrekSupplyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetStarTrekSupplyResp.Unmarshal(m, b)
}
func (m *SetStarTrekSupplyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetStarTrekSupplyResp.Marshal(b, m, deterministic)
}
func (dst *SetStarTrekSupplyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetStarTrekSupplyResp.Merge(dst, src)
}
func (m *SetStarTrekSupplyResp) XXX_Size() int {
	return xxx_messageInfo_SetStarTrekSupplyResp.Size(m)
}
func (m *SetStarTrekSupplyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetStarTrekSupplyResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetStarTrekSupplyResp proto.InternalMessageInfo

type GetStarTrekSupplyReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetStarTrekSupplyReq) Reset()         { *m = GetStarTrekSupplyReq{} }
func (m *GetStarTrekSupplyReq) String() string { return proto.CompactTextString(m) }
func (*GetStarTrekSupplyReq) ProtoMessage()    {}
func (*GetStarTrekSupplyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_ee1e21e98b8bab9e, []int{35}
}
func (m *GetStarTrekSupplyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStarTrekSupplyReq.Unmarshal(m, b)
}
func (m *GetStarTrekSupplyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStarTrekSupplyReq.Marshal(b, m, deterministic)
}
func (dst *GetStarTrekSupplyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStarTrekSupplyReq.Merge(dst, src)
}
func (m *GetStarTrekSupplyReq) XXX_Size() int {
	return xxx_messageInfo_GetStarTrekSupplyReq.Size(m)
}
func (m *GetStarTrekSupplyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStarTrekSupplyReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetStarTrekSupplyReq proto.InternalMessageInfo

type GetStarTrekSupplyResp struct {
	SupplyList           []*SupplyConf `protobuf:"bytes,1,rep,name=supply_list,json=supplyList,proto3" json:"supply_list,omitempty"`
	DailyLimit           uint32        `protobuf:"varint,2,opt,name=daily_limit,json=dailyLimit,proto3" json:"daily_limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetStarTrekSupplyResp) Reset()         { *m = GetStarTrekSupplyResp{} }
func (m *GetStarTrekSupplyResp) String() string { return proto.CompactTextString(m) }
func (*GetStarTrekSupplyResp) ProtoMessage()    {}
func (*GetStarTrekSupplyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_ee1e21e98b8bab9e, []int{36}
}
func (m *GetStarTrekSupplyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStarTrekSupplyResp.Unmarshal(m, b)
}
func (m *GetStarTrekSupplyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStarTrekSupplyResp.Marshal(b, m, deterministic)
}
func (dst *GetStarTrekSupplyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStarTrekSupplyResp.Merge(dst, src)
}
func (m *GetStarTrekSupplyResp) XXX_Size() int {
	return xxx_messageInfo_GetStarTrekSupplyResp.Size(m)
}
func (m *GetStarTrekSupplyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStarTrekSupplyResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetStarTrekSupplyResp proto.InternalMessageInfo

func (m *GetStarTrekSupplyResp) GetSupplyList() []*SupplyConf {
	if m != nil {
		return m.SupplyList
	}
	return nil
}

func (m *GetStarTrekSupplyResp) GetDailyLimit() uint32 {
	if m != nil {
		return m.DailyLimit
	}
	return 0
}

type DelStarTrekSupplyReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelStarTrekSupplyReq) Reset()         { *m = DelStarTrekSupplyReq{} }
func (m *DelStarTrekSupplyReq) String() string { return proto.CompactTextString(m) }
func (*DelStarTrekSupplyReq) ProtoMessage()    {}
func (*DelStarTrekSupplyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_ee1e21e98b8bab9e, []int{37}
}
func (m *DelStarTrekSupplyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelStarTrekSupplyReq.Unmarshal(m, b)
}
func (m *DelStarTrekSupplyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelStarTrekSupplyReq.Marshal(b, m, deterministic)
}
func (dst *DelStarTrekSupplyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelStarTrekSupplyReq.Merge(dst, src)
}
func (m *DelStarTrekSupplyReq) XXX_Size() int {
	return xxx_messageInfo_DelStarTrekSupplyReq.Size(m)
}
func (m *DelStarTrekSupplyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelStarTrekSupplyReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelStarTrekSupplyReq proto.InternalMessageInfo

func (m *DelStarTrekSupplyReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type DelStarTrekSupplyResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelStarTrekSupplyResp) Reset()         { *m = DelStarTrekSupplyResp{} }
func (m *DelStarTrekSupplyResp) String() string { return proto.CompactTextString(m) }
func (*DelStarTrekSupplyResp) ProtoMessage()    {}
func (*DelStarTrekSupplyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_ee1e21e98b8bab9e, []int{38}
}
func (m *DelStarTrekSupplyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelStarTrekSupplyResp.Unmarshal(m, b)
}
func (m *DelStarTrekSupplyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelStarTrekSupplyResp.Marshal(b, m, deterministic)
}
func (dst *DelStarTrekSupplyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelStarTrekSupplyResp.Merge(dst, src)
}
func (m *DelStarTrekSupplyResp) XXX_Size() int {
	return xxx_messageInfo_DelStarTrekSupplyResp.Size(m)
}
func (m *DelStarTrekSupplyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelStarTrekSupplyResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelStarTrekSupplyResp proto.InternalMessageInfo

type SetUserDailyInvestLimitReq struct {
	DailyLimit           uint32   `protobuf:"varint,1,opt,name=daily_limit,json=dailyLimit,proto3" json:"daily_limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetUserDailyInvestLimitReq) Reset()         { *m = SetUserDailyInvestLimitReq{} }
func (m *SetUserDailyInvestLimitReq) String() string { return proto.CompactTextString(m) }
func (*SetUserDailyInvestLimitReq) ProtoMessage()    {}
func (*SetUserDailyInvestLimitReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_ee1e21e98b8bab9e, []int{39}
}
func (m *SetUserDailyInvestLimitReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserDailyInvestLimitReq.Unmarshal(m, b)
}
func (m *SetUserDailyInvestLimitReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserDailyInvestLimitReq.Marshal(b, m, deterministic)
}
func (dst *SetUserDailyInvestLimitReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserDailyInvestLimitReq.Merge(dst, src)
}
func (m *SetUserDailyInvestLimitReq) XXX_Size() int {
	return xxx_messageInfo_SetUserDailyInvestLimitReq.Size(m)
}
func (m *SetUserDailyInvestLimitReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserDailyInvestLimitReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserDailyInvestLimitReq proto.InternalMessageInfo

func (m *SetUserDailyInvestLimitReq) GetDailyLimit() uint32 {
	if m != nil {
		return m.DailyLimit
	}
	return 0
}

type SetUserDailyInvestLimitResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetUserDailyInvestLimitResp) Reset()         { *m = SetUserDailyInvestLimitResp{} }
func (m *SetUserDailyInvestLimitResp) String() string { return proto.CompactTextString(m) }
func (*SetUserDailyInvestLimitResp) ProtoMessage()    {}
func (*SetUserDailyInvestLimitResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_ee1e21e98b8bab9e, []int{40}
}
func (m *SetUserDailyInvestLimitResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserDailyInvestLimitResp.Unmarshal(m, b)
}
func (m *SetUserDailyInvestLimitResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserDailyInvestLimitResp.Marshal(b, m, deterministic)
}
func (dst *SetUserDailyInvestLimitResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserDailyInvestLimitResp.Merge(dst, src)
}
func (m *SetUserDailyInvestLimitResp) XXX_Size() int {
	return xxx_messageInfo_SetUserDailyInvestLimitResp.Size(m)
}
func (m *SetUserDailyInvestLimitResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserDailyInvestLimitResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserDailyInvestLimitResp proto.InternalMessageInfo

type GetUserDailyInvestLimitReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserDailyInvestLimitReq) Reset()         { *m = GetUserDailyInvestLimitReq{} }
func (m *GetUserDailyInvestLimitReq) String() string { return proto.CompactTextString(m) }
func (*GetUserDailyInvestLimitReq) ProtoMessage()    {}
func (*GetUserDailyInvestLimitReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_ee1e21e98b8bab9e, []int{41}
}
func (m *GetUserDailyInvestLimitReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserDailyInvestLimitReq.Unmarshal(m, b)
}
func (m *GetUserDailyInvestLimitReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserDailyInvestLimitReq.Marshal(b, m, deterministic)
}
func (dst *GetUserDailyInvestLimitReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserDailyInvestLimitReq.Merge(dst, src)
}
func (m *GetUserDailyInvestLimitReq) XXX_Size() int {
	return xxx_messageInfo_GetUserDailyInvestLimitReq.Size(m)
}
func (m *GetUserDailyInvestLimitReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserDailyInvestLimitReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserDailyInvestLimitReq proto.InternalMessageInfo

type GetUserDailyInvestLimitResp struct {
	DailyLimit           uint32   `protobuf:"varint,1,opt,name=daily_limit,json=dailyLimit,proto3" json:"daily_limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserDailyInvestLimitResp) Reset()         { *m = GetUserDailyInvestLimitResp{} }
func (m *GetUserDailyInvestLimitResp) String() string { return proto.CompactTextString(m) }
func (*GetUserDailyInvestLimitResp) ProtoMessage()    {}
func (*GetUserDailyInvestLimitResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_ee1e21e98b8bab9e, []int{42}
}
func (m *GetUserDailyInvestLimitResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserDailyInvestLimitResp.Unmarshal(m, b)
}
func (m *GetUserDailyInvestLimitResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserDailyInvestLimitResp.Marshal(b, m, deterministic)
}
func (dst *GetUserDailyInvestLimitResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserDailyInvestLimitResp.Merge(dst, src)
}
func (m *GetUserDailyInvestLimitResp) XXX_Size() int {
	return xxx_messageInfo_GetUserDailyInvestLimitResp.Size(m)
}
func (m *GetUserDailyInvestLimitResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserDailyInvestLimitResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserDailyInvestLimitResp proto.InternalMessageInfo

func (m *GetUserDailyInvestLimitResp) GetDailyLimit() uint32 {
	if m != nil {
		return m.DailyLimit
	}
	return 0
}

type GetStarTrekExemptValueReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetStarTrekExemptValueReq) Reset()         { *m = GetStarTrekExemptValueReq{} }
func (m *GetStarTrekExemptValueReq) String() string { return proto.CompactTextString(m) }
func (*GetStarTrekExemptValueReq) ProtoMessage()    {}
func (*GetStarTrekExemptValueReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_ee1e21e98b8bab9e, []int{43}
}
func (m *GetStarTrekExemptValueReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStarTrekExemptValueReq.Unmarshal(m, b)
}
func (m *GetStarTrekExemptValueReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStarTrekExemptValueReq.Marshal(b, m, deterministic)
}
func (dst *GetStarTrekExemptValueReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStarTrekExemptValueReq.Merge(dst, src)
}
func (m *GetStarTrekExemptValueReq) XXX_Size() int {
	return xxx_messageInfo_GetStarTrekExemptValueReq.Size(m)
}
func (m *GetStarTrekExemptValueReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStarTrekExemptValueReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetStarTrekExemptValueReq proto.InternalMessageInfo

func (m *GetStarTrekExemptValueReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetStarTrekExemptValueResp struct {
	LastInvestFlag       bool     `protobuf:"varint,1,opt,name=last_invest_flag,json=lastInvestFlag,proto3" json:"last_invest_flag,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetStarTrekExemptValueResp) Reset()         { *m = GetStarTrekExemptValueResp{} }
func (m *GetStarTrekExemptValueResp) String() string { return proto.CompactTextString(m) }
func (*GetStarTrekExemptValueResp) ProtoMessage()    {}
func (*GetStarTrekExemptValueResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_trek_ee1e21e98b8bab9e, []int{44}
}
func (m *GetStarTrekExemptValueResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStarTrekExemptValueResp.Unmarshal(m, b)
}
func (m *GetStarTrekExemptValueResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStarTrekExemptValueResp.Marshal(b, m, deterministic)
}
func (dst *GetStarTrekExemptValueResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStarTrekExemptValueResp.Merge(dst, src)
}
func (m *GetStarTrekExemptValueResp) XXX_Size() int {
	return xxx_messageInfo_GetStarTrekExemptValueResp.Size(m)
}
func (m *GetStarTrekExemptValueResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStarTrekExemptValueResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetStarTrekExemptValueResp proto.InternalMessageInfo

func (m *GetStarTrekExemptValueResp) GetLastInvestFlag() bool {
	if m != nil {
		return m.LastInvestFlag
	}
	return false
}

func init() {
	proto.RegisterType((*StarTAwardInfo)(nil), "star_trek.StarTAwardInfo")
	proto.RegisterType((*StarTUserInfo)(nil), "star_trek.StarTUserInfo")
	proto.RegisterType((*LastRoundReview)(nil), "star_trek.LastRoundReview")
	proto.RegisterType((*NextRoundForecast)(nil), "star_trek.NextRoundForecast")
	proto.RegisterType((*RollingAwardInfo)(nil), "star_trek.RollingAwardInfo")
	proto.RegisterType((*GetStatTrekInfoReq)(nil), "star_trek.GetStatTrekInfoReq")
	proto.RegisterType((*GetStatTrekInfoResp)(nil), "star_trek.GetStatTrekInfoResp")
	proto.RegisterType((*GetCurAtcTimeAndRoundTimeReq)(nil), "star_trek.GetCurAtcTimeAndRoundTimeReq")
	proto.RegisterType((*GetCurAtcTimeAndRoundTimeResp)(nil), "star_trek.GetCurAtcTimeAndRoundTimeResp")
	proto.RegisterType((*GetSupplyValueChangeReq)(nil), "star_trek.GetSupplyValueChangeReq")
	proto.RegisterType((*GetSupplyValueChangeResp)(nil), "star_trek.GetSupplyValueChangeResp")
	proto.RegisterType((*CostSupplyInfo)(nil), "star_trek.CostSupplyInfo")
	proto.RegisterType((*DoInvestReq)(nil), "star_trek.DoInvestReq")
	proto.RegisterType((*DoInvestResp)(nil), "star_trek.DoInvestResp")
	proto.RegisterType((*GetMyTrekRecordReq)(nil), "star_trek.GetMyTrekRecordReq")
	proto.RegisterType((*MyTrekRecord)(nil), "star_trek.MyTrekRecord")
	proto.RegisterType((*GetMyTrekRecordResp)(nil), "star_trek.GetMyTrekRecordResp")
	proto.RegisterType((*GetAllTrekHistoryReq)(nil), "star_trek.GetAllTrekHistoryReq")
	proto.RegisterType((*GetAllTrekHistoryResp)(nil), "star_trek.GetAllTrekHistoryResp")
	proto.RegisterType((*StarTrekPrizeInfo)(nil), "star_trek.StarTrekPrizeInfo")
	proto.RegisterType((*DayTimeRangeInfo)(nil), "star_trek.DayTimeRangeInfo")
	proto.RegisterType((*StarTrekConf)(nil), "star_trek.StarTrekConf")
	proto.RegisterType((*SetStarTrekConfReq)(nil), "star_trek.SetStarTrekConfReq")
	proto.RegisterType((*SetStarTrekConfResp)(nil), "star_trek.SetStarTrekConfResp")
	proto.RegisterType((*GetStarTrekConfReq)(nil), "star_trek.GetStarTrekConfReq")
	proto.RegisterType((*GetStarTrekConfResp)(nil), "star_trek.GetStarTrekConfResp")
	proto.RegisterType((*GetStarTrekConfByIdReq)(nil), "star_trek.GetStarTrekConfByIdReq")
	proto.RegisterType((*GetStarTrekConfByIdResp)(nil), "star_trek.GetStarTrekConfByIdResp")
	proto.RegisterType((*UpdateStarTrekConfReq)(nil), "star_trek.UpdateStarTrekConfReq")
	proto.RegisterType((*UpdateStarTrekConfResp)(nil), "star_trek.UpdateStarTrekConfResp")
	proto.RegisterType((*DelStarTrekConfReq)(nil), "star_trek.DelStarTrekConfReq")
	proto.RegisterType((*DelStarTrekConfResp)(nil), "star_trek.DelStarTrekConfResp")
	proto.RegisterType((*SupplyConf)(nil), "star_trek.SupplyConf")
	proto.RegisterType((*SetStarTrekSupplyReq)(nil), "star_trek.SetStarTrekSupplyReq")
	proto.RegisterType((*SetStarTrekSupplyResp)(nil), "star_trek.SetStarTrekSupplyResp")
	proto.RegisterType((*GetStarTrekSupplyReq)(nil), "star_trek.GetStarTrekSupplyReq")
	proto.RegisterType((*GetStarTrekSupplyResp)(nil), "star_trek.GetStarTrekSupplyResp")
	proto.RegisterType((*DelStarTrekSupplyReq)(nil), "star_trek.DelStarTrekSupplyReq")
	proto.RegisterType((*DelStarTrekSupplyResp)(nil), "star_trek.DelStarTrekSupplyResp")
	proto.RegisterType((*SetUserDailyInvestLimitReq)(nil), "star_trek.SetUserDailyInvestLimitReq")
	proto.RegisterType((*SetUserDailyInvestLimitResp)(nil), "star_trek.SetUserDailyInvestLimitResp")
	proto.RegisterType((*GetUserDailyInvestLimitReq)(nil), "star_trek.GetUserDailyInvestLimitReq")
	proto.RegisterType((*GetUserDailyInvestLimitResp)(nil), "star_trek.GetUserDailyInvestLimitResp")
	proto.RegisterType((*GetStarTrekExemptValueReq)(nil), "star_trek.GetStarTrekExemptValueReq")
	proto.RegisterType((*GetStarTrekExemptValueResp)(nil), "star_trek.GetStarTrekExemptValueResp")
	proto.RegisterEnum("star_trek.TrekResultType", TrekResultType_name, TrekResultType_value)
	proto.RegisterEnum("star_trek.StarTrekWeekDay", StarTrekWeekDay_name, StarTrekWeekDay_value)
	proto.RegisterEnum("star_trek.StarTrekStatus", StarTrekStatus_name, StarTrekStatus_value)
	proto.RegisterEnum("star_trek.PackageItemType", PackageItemType_name, PackageItemType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// StarTrekClient is the client API for StarTrek service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type StarTrekClient interface {
	// 获取星际巡航信息
	GetStatTrekInfo(ctx context.Context, in *GetStatTrekInfoReq, opts ...grpc.CallOption) (*GetStatTrekInfoResp, error)
	// 获取全站、用户补给值变化
	GetSupplyValueChange(ctx context.Context, in *GetSupplyValueChangeReq, opts ...grpc.CallOption) (*GetSupplyValueChangeResp, error)
	// 去探险
	DoInvest(ctx context.Context, in *DoInvestReq, opts ...grpc.CallOption) (*DoInvestResp, error)
	// 获取用户巡航记录
	GetMyTrekRecord(ctx context.Context, in *GetMyTrekRecordReq, opts ...grpc.CallOption) (*GetMyTrekRecordResp, error)
	// 往期回顾
	GetAllTrekHistory(ctx context.Context, in *GetAllTrekHistoryReq, opts ...grpc.CallOption) (*GetAllTrekHistoryResp, error)
	GetCurAtcTimeAndRoundTime(ctx context.Context, in *GetCurAtcTimeAndRoundTimeReq, opts ...grpc.CallOption) (*GetCurAtcTimeAndRoundTimeResp, error)
	// 活动配置（包括时间、奖池）
	SetStarTrekConf(ctx context.Context, in *SetStarTrekConfReq, opts ...grpc.CallOption) (*SetStarTrekConfResp, error)
	// 获取活动配置
	GetStarTrekConf(ctx context.Context, in *GetStarTrekConfReq, opts ...grpc.CallOption) (*GetStarTrekConfResp, error)
	GetStarTrekConfById(ctx context.Context, in *GetStarTrekConfByIdReq, opts ...grpc.CallOption) (*GetStarTrekConfByIdResp, error)
	// 修改活动配置
	UpdateStarTrekConf(ctx context.Context, in *UpdateStarTrekConfReq, opts ...grpc.CallOption) (*UpdateStarTrekConfResp, error)
	// 删除活动配置
	DelStarTrekConf(ctx context.Context, in *DelStarTrekConfReq, opts ...grpc.CallOption) (*DelStarTrekConfResp, error)
	// 补给配置
	SetStarTrekSupply(ctx context.Context, in *SetStarTrekSupplyReq, opts ...grpc.CallOption) (*SetStarTrekSupplyResp, error)
	// 获取补给配置
	GetStarTrekSupply(ctx context.Context, in *GetStarTrekSupplyReq, opts ...grpc.CallOption) (*GetStarTrekSupplyResp, error)
	// 删除补给配置
	DelStarTrekSupply(ctx context.Context, in *DelStarTrekSupplyReq, opts ...grpc.CallOption) (*DelStarTrekSupplyResp, error)
	// 设置每日最大投入值
	SetUserDailyInvestLimit(ctx context.Context, in *SetUserDailyInvestLimitReq, opts ...grpc.CallOption) (*SetUserDailyInvestLimitResp, error)
	GetUserDailyInvestLimit(ctx context.Context, in *GetUserDailyInvestLimitReq, opts ...grpc.CallOption) (*GetUserDailyInvestLimitResp, error)
	// 豁免条件值
	GetStarTrekExemptValue(ctx context.Context, in *GetStarTrekExemptValueReq, opts ...grpc.CallOption) (*GetStarTrekExemptValueResp, error)
	// 发放包裹数据对账
	GetAwardTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error)
	GetAwardOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error)
	// 扣除背包物品数据对账
	GetCostTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error)
	GetCostOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error)
	// 尝试回滚对不上的订单
	RollbackLostOrder(ctx context.Context, in *reconcile_v2.ReplaceOrderReq, opts ...grpc.CallOption) (*reconcile_v2.EmptyResp, error)
}

type starTrekClient struct {
	cc *grpc.ClientConn
}

func NewStarTrekClient(cc *grpc.ClientConn) StarTrekClient {
	return &starTrekClient{cc}
}

func (c *starTrekClient) GetStatTrekInfo(ctx context.Context, in *GetStatTrekInfoReq, opts ...grpc.CallOption) (*GetStatTrekInfoResp, error) {
	out := new(GetStatTrekInfoResp)
	err := c.cc.Invoke(ctx, "/star_trek.StarTrek/GetStatTrekInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starTrekClient) GetSupplyValueChange(ctx context.Context, in *GetSupplyValueChangeReq, opts ...grpc.CallOption) (*GetSupplyValueChangeResp, error) {
	out := new(GetSupplyValueChangeResp)
	err := c.cc.Invoke(ctx, "/star_trek.StarTrek/GetSupplyValueChange", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starTrekClient) DoInvest(ctx context.Context, in *DoInvestReq, opts ...grpc.CallOption) (*DoInvestResp, error) {
	out := new(DoInvestResp)
	err := c.cc.Invoke(ctx, "/star_trek.StarTrek/DoInvest", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starTrekClient) GetMyTrekRecord(ctx context.Context, in *GetMyTrekRecordReq, opts ...grpc.CallOption) (*GetMyTrekRecordResp, error) {
	out := new(GetMyTrekRecordResp)
	err := c.cc.Invoke(ctx, "/star_trek.StarTrek/GetMyTrekRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starTrekClient) GetAllTrekHistory(ctx context.Context, in *GetAllTrekHistoryReq, opts ...grpc.CallOption) (*GetAllTrekHistoryResp, error) {
	out := new(GetAllTrekHistoryResp)
	err := c.cc.Invoke(ctx, "/star_trek.StarTrek/GetAllTrekHistory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starTrekClient) GetCurAtcTimeAndRoundTime(ctx context.Context, in *GetCurAtcTimeAndRoundTimeReq, opts ...grpc.CallOption) (*GetCurAtcTimeAndRoundTimeResp, error) {
	out := new(GetCurAtcTimeAndRoundTimeResp)
	err := c.cc.Invoke(ctx, "/star_trek.StarTrek/GetCurAtcTimeAndRoundTime", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starTrekClient) SetStarTrekConf(ctx context.Context, in *SetStarTrekConfReq, opts ...grpc.CallOption) (*SetStarTrekConfResp, error) {
	out := new(SetStarTrekConfResp)
	err := c.cc.Invoke(ctx, "/star_trek.StarTrek/SetStarTrekConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starTrekClient) GetStarTrekConf(ctx context.Context, in *GetStarTrekConfReq, opts ...grpc.CallOption) (*GetStarTrekConfResp, error) {
	out := new(GetStarTrekConfResp)
	err := c.cc.Invoke(ctx, "/star_trek.StarTrek/GetStarTrekConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starTrekClient) GetStarTrekConfById(ctx context.Context, in *GetStarTrekConfByIdReq, opts ...grpc.CallOption) (*GetStarTrekConfByIdResp, error) {
	out := new(GetStarTrekConfByIdResp)
	err := c.cc.Invoke(ctx, "/star_trek.StarTrek/GetStarTrekConfById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starTrekClient) UpdateStarTrekConf(ctx context.Context, in *UpdateStarTrekConfReq, opts ...grpc.CallOption) (*UpdateStarTrekConfResp, error) {
	out := new(UpdateStarTrekConfResp)
	err := c.cc.Invoke(ctx, "/star_trek.StarTrek/UpdateStarTrekConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starTrekClient) DelStarTrekConf(ctx context.Context, in *DelStarTrekConfReq, opts ...grpc.CallOption) (*DelStarTrekConfResp, error) {
	out := new(DelStarTrekConfResp)
	err := c.cc.Invoke(ctx, "/star_trek.StarTrek/DelStarTrekConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starTrekClient) SetStarTrekSupply(ctx context.Context, in *SetStarTrekSupplyReq, opts ...grpc.CallOption) (*SetStarTrekSupplyResp, error) {
	out := new(SetStarTrekSupplyResp)
	err := c.cc.Invoke(ctx, "/star_trek.StarTrek/SetStarTrekSupply", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starTrekClient) GetStarTrekSupply(ctx context.Context, in *GetStarTrekSupplyReq, opts ...grpc.CallOption) (*GetStarTrekSupplyResp, error) {
	out := new(GetStarTrekSupplyResp)
	err := c.cc.Invoke(ctx, "/star_trek.StarTrek/GetStarTrekSupply", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starTrekClient) DelStarTrekSupply(ctx context.Context, in *DelStarTrekSupplyReq, opts ...grpc.CallOption) (*DelStarTrekSupplyResp, error) {
	out := new(DelStarTrekSupplyResp)
	err := c.cc.Invoke(ctx, "/star_trek.StarTrek/DelStarTrekSupply", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starTrekClient) SetUserDailyInvestLimit(ctx context.Context, in *SetUserDailyInvestLimitReq, opts ...grpc.CallOption) (*SetUserDailyInvestLimitResp, error) {
	out := new(SetUserDailyInvestLimitResp)
	err := c.cc.Invoke(ctx, "/star_trek.StarTrek/SetUserDailyInvestLimit", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starTrekClient) GetUserDailyInvestLimit(ctx context.Context, in *GetUserDailyInvestLimitReq, opts ...grpc.CallOption) (*GetUserDailyInvestLimitResp, error) {
	out := new(GetUserDailyInvestLimitResp)
	err := c.cc.Invoke(ctx, "/star_trek.StarTrek/GetUserDailyInvestLimit", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starTrekClient) GetStarTrekExemptValue(ctx context.Context, in *GetStarTrekExemptValueReq, opts ...grpc.CallOption) (*GetStarTrekExemptValueResp, error) {
	out := new(GetStarTrekExemptValueResp)
	err := c.cc.Invoke(ctx, "/star_trek.StarTrek/GetStarTrekExemptValue", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starTrekClient) GetAwardTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	out := new(reconcile_v2.CountResp)
	err := c.cc.Invoke(ctx, "/star_trek.StarTrek/GetAwardTotalCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starTrekClient) GetAwardOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	out := new(reconcile_v2.OrderIdsResp)
	err := c.cc.Invoke(ctx, "/star_trek.StarTrek/GetAwardOrderIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starTrekClient) GetCostTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	out := new(reconcile_v2.CountResp)
	err := c.cc.Invoke(ctx, "/star_trek.StarTrek/GetCostTotalCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starTrekClient) GetCostOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	out := new(reconcile_v2.OrderIdsResp)
	err := c.cc.Invoke(ctx, "/star_trek.StarTrek/GetCostOrderIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starTrekClient) RollbackLostOrder(ctx context.Context, in *reconcile_v2.ReplaceOrderReq, opts ...grpc.CallOption) (*reconcile_v2.EmptyResp, error) {
	out := new(reconcile_v2.EmptyResp)
	err := c.cc.Invoke(ctx, "/star_trek.StarTrek/RollbackLostOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// StarTrekServer is the server API for StarTrek service.
type StarTrekServer interface {
	// 获取星际巡航信息
	GetStatTrekInfo(context.Context, *GetStatTrekInfoReq) (*GetStatTrekInfoResp, error)
	// 获取全站、用户补给值变化
	GetSupplyValueChange(context.Context, *GetSupplyValueChangeReq) (*GetSupplyValueChangeResp, error)
	// 去探险
	DoInvest(context.Context, *DoInvestReq) (*DoInvestResp, error)
	// 获取用户巡航记录
	GetMyTrekRecord(context.Context, *GetMyTrekRecordReq) (*GetMyTrekRecordResp, error)
	// 往期回顾
	GetAllTrekHistory(context.Context, *GetAllTrekHistoryReq) (*GetAllTrekHistoryResp, error)
	GetCurAtcTimeAndRoundTime(context.Context, *GetCurAtcTimeAndRoundTimeReq) (*GetCurAtcTimeAndRoundTimeResp, error)
	// 活动配置（包括时间、奖池）
	SetStarTrekConf(context.Context, *SetStarTrekConfReq) (*SetStarTrekConfResp, error)
	// 获取活动配置
	GetStarTrekConf(context.Context, *GetStarTrekConfReq) (*GetStarTrekConfResp, error)
	GetStarTrekConfById(context.Context, *GetStarTrekConfByIdReq) (*GetStarTrekConfByIdResp, error)
	// 修改活动配置
	UpdateStarTrekConf(context.Context, *UpdateStarTrekConfReq) (*UpdateStarTrekConfResp, error)
	// 删除活动配置
	DelStarTrekConf(context.Context, *DelStarTrekConfReq) (*DelStarTrekConfResp, error)
	// 补给配置
	SetStarTrekSupply(context.Context, *SetStarTrekSupplyReq) (*SetStarTrekSupplyResp, error)
	// 获取补给配置
	GetStarTrekSupply(context.Context, *GetStarTrekSupplyReq) (*GetStarTrekSupplyResp, error)
	// 删除补给配置
	DelStarTrekSupply(context.Context, *DelStarTrekSupplyReq) (*DelStarTrekSupplyResp, error)
	// 设置每日最大投入值
	SetUserDailyInvestLimit(context.Context, *SetUserDailyInvestLimitReq) (*SetUserDailyInvestLimitResp, error)
	GetUserDailyInvestLimit(context.Context, *GetUserDailyInvestLimitReq) (*GetUserDailyInvestLimitResp, error)
	// 豁免条件值
	GetStarTrekExemptValue(context.Context, *GetStarTrekExemptValueReq) (*GetStarTrekExemptValueResp, error)
	// 发放包裹数据对账
	GetAwardTotalCount(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error)
	GetAwardOrderIds(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error)
	// 扣除背包物品数据对账
	GetCostTotalCount(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error)
	GetCostOrderIds(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error)
	// 尝试回滚对不上的订单
	RollbackLostOrder(context.Context, *reconcile_v2.ReplaceOrderReq) (*reconcile_v2.EmptyResp, error)
}

func RegisterStarTrekServer(s *grpc.Server, srv StarTrekServer) {
	s.RegisterService(&_StarTrek_serviceDesc, srv)
}

func _StarTrek_GetStatTrekInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStatTrekInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarTrekServer).GetStatTrekInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/star_trek.StarTrek/GetStatTrekInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarTrekServer).GetStatTrekInfo(ctx, req.(*GetStatTrekInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarTrek_GetSupplyValueChange_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSupplyValueChangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarTrekServer).GetSupplyValueChange(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/star_trek.StarTrek/GetSupplyValueChange",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarTrekServer).GetSupplyValueChange(ctx, req.(*GetSupplyValueChangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarTrek_DoInvest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DoInvestReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarTrekServer).DoInvest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/star_trek.StarTrek/DoInvest",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarTrekServer).DoInvest(ctx, req.(*DoInvestReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarTrek_GetMyTrekRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMyTrekRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarTrekServer).GetMyTrekRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/star_trek.StarTrek/GetMyTrekRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarTrekServer).GetMyTrekRecord(ctx, req.(*GetMyTrekRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarTrek_GetAllTrekHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllTrekHistoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarTrekServer).GetAllTrekHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/star_trek.StarTrek/GetAllTrekHistory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarTrekServer).GetAllTrekHistory(ctx, req.(*GetAllTrekHistoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarTrek_GetCurAtcTimeAndRoundTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCurAtcTimeAndRoundTimeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarTrekServer).GetCurAtcTimeAndRoundTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/star_trek.StarTrek/GetCurAtcTimeAndRoundTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarTrekServer).GetCurAtcTimeAndRoundTime(ctx, req.(*GetCurAtcTimeAndRoundTimeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarTrek_SetStarTrekConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetStarTrekConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarTrekServer).SetStarTrekConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/star_trek.StarTrek/SetStarTrekConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarTrekServer).SetStarTrekConf(ctx, req.(*SetStarTrekConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarTrek_GetStarTrekConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStarTrekConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarTrekServer).GetStarTrekConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/star_trek.StarTrek/GetStarTrekConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarTrekServer).GetStarTrekConf(ctx, req.(*GetStarTrekConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarTrek_GetStarTrekConfById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStarTrekConfByIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarTrekServer).GetStarTrekConfById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/star_trek.StarTrek/GetStarTrekConfById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarTrekServer).GetStarTrekConfById(ctx, req.(*GetStarTrekConfByIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarTrek_UpdateStarTrekConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateStarTrekConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarTrekServer).UpdateStarTrekConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/star_trek.StarTrek/UpdateStarTrekConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarTrekServer).UpdateStarTrekConf(ctx, req.(*UpdateStarTrekConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarTrek_DelStarTrekConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelStarTrekConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarTrekServer).DelStarTrekConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/star_trek.StarTrek/DelStarTrekConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarTrekServer).DelStarTrekConf(ctx, req.(*DelStarTrekConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarTrek_SetStarTrekSupply_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetStarTrekSupplyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarTrekServer).SetStarTrekSupply(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/star_trek.StarTrek/SetStarTrekSupply",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarTrekServer).SetStarTrekSupply(ctx, req.(*SetStarTrekSupplyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarTrek_GetStarTrekSupply_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStarTrekSupplyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarTrekServer).GetStarTrekSupply(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/star_trek.StarTrek/GetStarTrekSupply",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarTrekServer).GetStarTrekSupply(ctx, req.(*GetStarTrekSupplyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarTrek_DelStarTrekSupply_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelStarTrekSupplyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarTrekServer).DelStarTrekSupply(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/star_trek.StarTrek/DelStarTrekSupply",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarTrekServer).DelStarTrekSupply(ctx, req.(*DelStarTrekSupplyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarTrek_SetUserDailyInvestLimit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetUserDailyInvestLimitReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarTrekServer).SetUserDailyInvestLimit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/star_trek.StarTrek/SetUserDailyInvestLimit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarTrekServer).SetUserDailyInvestLimit(ctx, req.(*SetUserDailyInvestLimitReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarTrek_GetUserDailyInvestLimit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserDailyInvestLimitReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarTrekServer).GetUserDailyInvestLimit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/star_trek.StarTrek/GetUserDailyInvestLimit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarTrekServer).GetUserDailyInvestLimit(ctx, req.(*GetUserDailyInvestLimitReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarTrek_GetStarTrekExemptValue_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStarTrekExemptValueReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarTrekServer).GetStarTrekExemptValue(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/star_trek.StarTrek/GetStarTrekExemptValue",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarTrekServer).GetStarTrekExemptValue(ctx, req.(*GetStarTrekExemptValueReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarTrek_GetAwardTotalCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarTrekServer).GetAwardTotalCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/star_trek.StarTrek/GetAwardTotalCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarTrekServer).GetAwardTotalCount(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarTrek_GetAwardOrderIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarTrekServer).GetAwardOrderIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/star_trek.StarTrek/GetAwardOrderIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarTrekServer).GetAwardOrderIds(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarTrek_GetCostTotalCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarTrekServer).GetCostTotalCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/star_trek.StarTrek/GetCostTotalCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarTrekServer).GetCostTotalCount(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarTrek_GetCostOrderIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarTrekServer).GetCostOrderIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/star_trek.StarTrek/GetCostOrderIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarTrekServer).GetCostOrderIds(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarTrek_RollbackLostOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.ReplaceOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarTrekServer).RollbackLostOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/star_trek.StarTrek/RollbackLostOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarTrekServer).RollbackLostOrder(ctx, req.(*reconcile_v2.ReplaceOrderReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _StarTrek_serviceDesc = grpc.ServiceDesc{
	ServiceName: "star_trek.StarTrek",
	HandlerType: (*StarTrekServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetStatTrekInfo",
			Handler:    _StarTrek_GetStatTrekInfo_Handler,
		},
		{
			MethodName: "GetSupplyValueChange",
			Handler:    _StarTrek_GetSupplyValueChange_Handler,
		},
		{
			MethodName: "DoInvest",
			Handler:    _StarTrek_DoInvest_Handler,
		},
		{
			MethodName: "GetMyTrekRecord",
			Handler:    _StarTrek_GetMyTrekRecord_Handler,
		},
		{
			MethodName: "GetAllTrekHistory",
			Handler:    _StarTrek_GetAllTrekHistory_Handler,
		},
		{
			MethodName: "GetCurAtcTimeAndRoundTime",
			Handler:    _StarTrek_GetCurAtcTimeAndRoundTime_Handler,
		},
		{
			MethodName: "SetStarTrekConf",
			Handler:    _StarTrek_SetStarTrekConf_Handler,
		},
		{
			MethodName: "GetStarTrekConf",
			Handler:    _StarTrek_GetStarTrekConf_Handler,
		},
		{
			MethodName: "GetStarTrekConfById",
			Handler:    _StarTrek_GetStarTrekConfById_Handler,
		},
		{
			MethodName: "UpdateStarTrekConf",
			Handler:    _StarTrek_UpdateStarTrekConf_Handler,
		},
		{
			MethodName: "DelStarTrekConf",
			Handler:    _StarTrek_DelStarTrekConf_Handler,
		},
		{
			MethodName: "SetStarTrekSupply",
			Handler:    _StarTrek_SetStarTrekSupply_Handler,
		},
		{
			MethodName: "GetStarTrekSupply",
			Handler:    _StarTrek_GetStarTrekSupply_Handler,
		},
		{
			MethodName: "DelStarTrekSupply",
			Handler:    _StarTrek_DelStarTrekSupply_Handler,
		},
		{
			MethodName: "SetUserDailyInvestLimit",
			Handler:    _StarTrek_SetUserDailyInvestLimit_Handler,
		},
		{
			MethodName: "GetUserDailyInvestLimit",
			Handler:    _StarTrek_GetUserDailyInvestLimit_Handler,
		},
		{
			MethodName: "GetStarTrekExemptValue",
			Handler:    _StarTrek_GetStarTrekExemptValue_Handler,
		},
		{
			MethodName: "GetAwardTotalCount",
			Handler:    _StarTrek_GetAwardTotalCount_Handler,
		},
		{
			MethodName: "GetAwardOrderIds",
			Handler:    _StarTrek_GetAwardOrderIds_Handler,
		},
		{
			MethodName: "GetCostTotalCount",
			Handler:    _StarTrek_GetCostTotalCount_Handler,
		},
		{
			MethodName: "GetCostOrderIds",
			Handler:    _StarTrek_GetCostOrderIds_Handler,
		},
		{
			MethodName: "RollbackLostOrder",
			Handler:    _StarTrek_RollbackLostOrder_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "star-trek/star-trek.proto",
}

func init() {
	proto.RegisterFile("star-trek/star-trek.proto", fileDescriptor_star_trek_ee1e21e98b8bab9e)
}

var fileDescriptor_star_trek_ee1e21e98b8bab9e = []byte{
	// 2622 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x59, 0xcb, 0x73, 0xdb, 0xc8,
	0xd1, 0x17, 0x49, 0x89, 0x22, 0x9b, 0x7a, 0x50, 0xb3, 0xb6, 0x44, 0x51, 0xd6, 0x67, 0x1b, 0xfb,
	0x72, 0xd9, 0x9f, 0xe5, 0xac, 0x36, 0xbb, 0xb5, 0x95, 0xad, 0x4d, 0x95, 0xac, 0x07, 0xcd, 0xd8,
	0x96, 0x55, 0x20, 0xb5, 0x8f, 0xa4, 0x12, 0xd4, 0x08, 0x18, 0xd1, 0x08, 0x41, 0x00, 0xc1, 0x80,
	0xb6, 0x99, 0xca, 0x29, 0xb9, 0xe5, 0x9e, 0x7b, 0xae, 0x7b, 0xce, 0x21, 0x7f, 0x41, 0x4e, 0xc9,
	0x35, 0x97, 0xdc, 0x72, 0x4b, 0xe5, 0x9a, 0x5b, 0x4e, 0xa9, 0xee, 0x01, 0x48, 0x80, 0x00, 0x29,
	0x95, 0xb7, 0xf6, 0x24, 0x4e, 0x77, 0x4f, 0x4f, 0x4f, 0xf7, 0xaf, 0x1f, 0x03, 0xc1, 0xb6, 0x0c,
	0x79, 0xf0, 0x30, 0x0c, 0x44, 0xff, 0xd1, 0xf8, 0xd7, 0x9e, 0x1f, 0x78, 0xa1, 0xc7, 0xaa, 0x48,
	0x30, 0x90, 0xd0, 0xbc, 0x2d, 0xde, 0x84, 0xc2, 0x95, 0xb6, 0xe7, 0x3e, 0xf2, 0xfc, 0xd0, 0xf6,
	0x5c, 0x19, 0xff, 0x55, 0xb2, 0xcd, 0xdb, 0x81, 0x30, 0x3d, 0xd7, 0xb4, 0x1d, 0xf1, 0xf0, 0xd5,
	0xfe, 0xa3, 0xe4, 0x42, 0x09, 0x68, 0x7f, 0x2f, 0xc0, 0x5a, 0x27, 0xe4, 0x41, 0xf7, 0xe0, 0x35,
	0x0f, 0xac, 0xb6, 0x7b, 0xe9, 0xb1, 0x2d, 0x58, 0xf6, 0xb9, 0xd9, 0x37, 0x6c, 0xab, 0x51, 0xb8,
	0x53, 0xb8, 0xb7, 0xaa, 0x97, 0x71, 0xd9, 0xb6, 0xd8, 0x0e, 0x54, 0x89, 0xe1, 0xf2, 0x81, 0x68,
	0x14, 0xef, 0x14, 0xee, 0x55, 0xf5, 0x0a, 0x12, 0x4e, 0xf9, 0x40, 0xb0, 0x6d, 0xa0, 0xdf, 0x86,
	0x6f, 0x9b, 0x8d, 0x12, 0xf1, 0x48, 0xcb, 0x99, 0x6d, 0xb2, 0xdb, 0x50, 0x23, 0x16, 0x1f, 0x78,
	0x43, 0x37, 0x6c, 0x2c, 0x92, 0x52, 0x40, 0xd2, 0x01, 0x51, 0xd8, 0x2e, 0xc0, 0xd0, 0xb5, 0x43,
	0xc3, 0x0f, 0x6c, 0x53, 0x34, 0x96, 0x88, 0x5f, 0x45, 0xca, 0x19, 0x12, 0x50, 0xf5, 0xa5, 0xed,
	0x1a, 0xa1, 0x3d, 0x10, 0x8d, 0x32, 0x31, 0x97, 0x2f, 0x6d, 0xb7, 0x6b, 0x0f, 0x04, 0xda, 0xda,
	0xb3, 0x2f, 0x43, 0xb4, 0x75, 0x59, 0xd9, 0x8a, 0xcb, 0xb6, 0xa5, 0xdd, 0x85, 0x55, 0xba, 0xd6,
	0xb9, 0x14, 0x01, 0xdd, 0xaa, 0x0e, 0xa5, 0xe1, 0xf8, 0x46, 0xf8, 0x53, 0xfb, 0x63, 0x11, 0xd6,
	0x9f, 0x71, 0x19, 0xea, 0xde, 0xd0, 0xb5, 0x74, 0xf1, 0xca, 0x16, 0xaf, 0xf1, 0xa8, 0x00, 0x97,
	0x93, 0xcb, 0x2f, 0xd3, 0xba, 0x6d, 0xa1, 0x91, 0x8a, 0x45, 0x76, 0x14, 0x95, 0x91, 0x44, 0x21,
	0x4b, 0x6e, 0x43, 0x0d, 0x43, 0x62, 0x04, 0x42, 0x0e, 0x9d, 0x90, 0x5c, 0x50, 0xd1, 0x01, 0x49,
	0x3a, 0x51, 0xd8, 0x67, 0x00, 0x1c, 0x7d, 0x6c, 0xe0, 0xc5, 0xc9, 0x09, 0xb5, 0xfd, 0xed, 0xbd,
	0x71, 0x2c, 0xf7, 0xd2, 0x51, 0xd0, 0xab, 0x24, 0x7c, 0xc6, 0xcd, 0x3e, 0xfb, 0x04, 0xaa, 0x43,
	0x29, 0x02, 0xc3, 0x76, 0x2f, 0x3d, 0xf2, 0x4e, 0x6d, 0xbf, 0x31, 0xbd, 0x31, 0xbe, 0xa7, 0x5e,
	0x19, 0xc6, 0x37, 0xde, 0x89, 0xb6, 0x39, 0xb6, 0x0c, 0x1b, 0xe5, 0x3b, 0xa5, 0x7b, 0xab, 0x8a,
	0xf9, 0xcc, 0x96, 0x21, 0x7b, 0x0f, 0xd6, 0x2e, 0x6c, 0xb7, 0xe7, 0x19, 0x24, 0xe2, 0x0e, 0x07,
	0x91, 0xff, 0x56, 0x88, 0x8a, 0xda, 0x4e, 0x87, 0x03, 0xed, 0x25, 0x6c, 0x9c, 0x8a, 0x37, 0xca,
	0x43, 0x27, 0x5e, 0x20, 0x4c, 0x2e, 0xc3, 0x79, 0x3e, 0x4a, 0xdf, 0xb1, 0x78, 0xfd, 0x3b, 0x6a,
	0xff, 0x28, 0x40, 0x5d, 0xf7, 0x1c, 0xc7, 0x76, 0x7b, 0x13, 0x24, 0x66, 0x62, 0xc6, 0x9a, 0x50,
	0x71, 0x6d, 0xb3, 0x9f, 0x44, 0x60, 0xbc, 0x4e, 0xc3, 0xb3, 0x34, 0x07, 0x9e, 0x8b, 0x73, 0xe1,
	0xb9, 0x94, 0x81, 0x67, 0xd6, 0x57, 0xe5, 0xac, 0xaf, 0x52, 0x6e, 0x59, 0x4e, 0xb9, 0x45, 0xfb,
	0x00, 0x58, 0x4b, 0x84, 0x9d, 0x90, 0x87, 0xdd, 0x40, 0xf4, 0xe9, 0xea, 0xe2, 0x57, 0x39, 0x88,
	0xfc, 0xd7, 0x22, 0xbc, 0x93, 0x11, 0x94, 0xfe, 0x15, 0xa8, 0xbc, 0x10, 0xbd, 0x38, 0x3b, 0x22,
	0x54, 0x12, 0x85, 0x50, 0xb9, 0x0d, 0x15, 0x11, 0x43, 0xb6, 0xa4, 0x76, 0x8a, 0x08, 0xb0, 0x6f,
	0x8f, 0xc7, 0x0f, 0x61, 0xdd, 0x76, 0x5f, 0x09, 0x89, 0x09, 0xeb, 0xf5, 0x02, 0x21, 0x65, 0xe4,
	0xb4, 0x35, 0x45, 0x3e, 0x8b, 0xa8, 0x94, 0xd7, 0xe8, 0x32, 0x93, 0x1c, 0x5b, 0x8e, 0xf2, 0x5a,
	0x8a, 0xe0, 0x90, 0xfc, 0xfa, 0x11, 0x94, 0x1d, 0x2e, 0x43, 0x43, 0x92, 0xbf, 0x6a, 0xfb, 0xcd,
	0xc4, 0xe9, 0x53, 0x89, 0xa9, 0x2f, 0xa1, 0x64, 0x87, 0x7d, 0x0c, 0x65, 0x57, 0xbc, 0xc1, 0x2d,
	0x15, 0xda, 0x72, 0x2b, 0xb1, 0x25, 0x83, 0x54, 0x7d, 0x09, 0x65, 0x3b, 0xec, 0x13, 0x58, 0xc6,
	0xca, 0x17, 0x58, 0xb2, 0x51, 0xbd, 0x53, 0xba, 0x57, 0xdb, 0xdf, 0x49, 0xec, 0x9a, 0x06, 0x9d,
	0x1e, 0xcb, 0xb2, 0x63, 0xd8, 0x70, 0x78, 0x48, 0xd7, 0xe4, 0x41, 0x68, 0x53, 0x59, 0x6d, 0x00,
	0x29, 0x98, 0x9d, 0x7e, 0x75, 0xb5, 0xe5, 0x6c, 0xbc, 0x03, 0x61, 0x39, 0x18, 0x19, 0x72, 0xe8,
	0xfb, 0xce, 0xa8, 0x51, 0x23, 0x1f, 0x54, 0x06, 0xa3, 0x0e, 0xad, 0xd9, 0xff, 0x03, 0x53, 0x91,
	0x55, 0x7c, 0xc3, 0xb1, 0x07, 0x76, 0xd8, 0x58, 0x21, 0xa9, 0x3a, 0x71, 0x94, 0xe0, 0x33, 0xa4,
	0xa3, 0x3f, 0x07, 0xb6, 0x6b, 0x28, 0x2f, 0x37, 0x56, 0x95, 0x3f, 0x07, 0xb6, 0xdb, 0x26, 0x02,
	0xbb, 0x0f, 0x1b, 0xe4, 0x6e, 0x8b, 0xdb, 0xce, 0x28, 0x96, 0x5a, 0x23, 0xa9, 0x75, 0x64, 0x1c,
	0x21, 0x5d, 0xc9, 0x6a, 0x3f, 0x80, 0x5b, 0x2d, 0x11, 0x1e, 0x0e, 0x83, 0x83, 0xd0, 0x44, 0x38,
	0x1c, 0xb8, 0x96, 0x1e, 0xd7, 0xb2, 0x7c, 0x70, 0xfe, 0xa5, 0x00, 0xbb, 0x73, 0xb6, 0x48, 0x9f,
	0xdd, 0x84, 0x32, 0x37, 0xc3, 0x09, 0x48, 0x97, 0xb8, 0x19, 0xaa, 0xb6, 0x61, 0xf1, 0x91, 0x71,
	0x21, 0x6c, 0xb7, 0x17, 0x21, 0xb4, 0x62, 0xf1, 0xd1, 0x63, 0x5c, 0x63, 0x01, 0x47, 0xa6, 0x70,
	0xad, 0x08, 0x9f, 0x65, 0x8b, 0x8f, 0x8e, 0x5d, 0x2b, 0x85, 0xf9, 0xc5, 0x34, 0xe6, 0x6f, 0x43,
	0x4d, 0xb1, 0x08, 0xe7, 0x71, 0xc2, 0x12, 0xe9, 0x31, 0x52, 0xf0, 0x44, 0x25, 0x80, 0x6a, 0x15,
	0xec, 0x94, 0xb2, 0x63, 0xd7, 0xd2, 0x1e, 0xc0, 0x16, 0xe6, 0x18, 0xb9, 0xf5, 0x4b, 0xee, 0x0c,
	0xc5, 0xe1, 0x4b, 0xee, 0xf6, 0x66, 0x5c, 0xfa, 0xdb, 0x02, 0x34, 0xf2, 0xa5, 0xa5, 0xcf, 0xde,
	0x85, 0x55, 0x29, 0x82, 0x57, 0x22, 0x88, 0xa3, 0xab, 0x36, 0xae, 0x28, 0x62, 0x14, 0xe1, 0x54,
	0xf8, 0x8b, 0x53, 0xe1, 0x4f, 0x07, 0xb4, 0x34, 0x1d, 0xd0, 0x7c, 0x74, 0x2c, 0xe6, 0xa3, 0x43,
	0xfb, 0x5d, 0x01, 0xd6, 0x0e, 0x3d, 0x19, 0x19, 0x4b, 0x05, 0xf4, 0x0e, 0xac, 0xa8, 0xce, 0x11,
	0x8a, 0xc1, 0x24, 0x2e, 0x94, 0x94, 0xed, 0x50, 0x0c, 0xda, 0x56, 0xb2, 0x81, 0x16, 0x93, 0x0d,
	0x14, 0xed, 0x26, 0x46, 0x38, 0xf2, 0xe3, 0xd2, 0x51, 0x41, 0x42, 0x77, 0xe4, 0x53, 0x59, 0x21,
	0x26, 0xd6, 0xc2, 0x28, 0x38, 0xb8, 0xc6, 0x96, 0xf1, 0x6d, 0x01, 0x6a, 0x47, 0x9e, 0xba, 0x40,
	0xae, 0x4f, 0xf1, 0xd2, 0xe6, 0x4b, 0xee, 0xba, 0xc2, 0x99, 0x9c, 0x5a, 0x8d, 0x28, 0xed, 0x74,
	0xe0, 0x4b, 0xe9, 0xc0, 0xff, 0x08, 0x6a, 0x63, 0x4f, 0x48, 0x74, 0x44, 0x69, 0xaa, 0x66, 0xa5,
	0xaf, 0xaf, 0x83, 0x8c, 0xdc, 0x23, 0x43, 0xc6, 0x60, 0xd1, 0xf4, 0x64, 0x5c, 0xde, 0xe9, 0xb7,
	0xf6, 0xef, 0x02, 0xac, 0x4c, 0x6c, 0x9d, 0x5f, 0x68, 0xe7, 0xc6, 0x31, 0x3f, 0x50, 0xa5, 0x19,
	0x69, 0x9c, 0xc1, 0xcd, 0x62, 0x0e, 0x6e, 0xee, 0xc2, 0x4a, 0x2a, 0x8f, 0x95, 0xdd, 0x35, 0x6b,
	0x92, 0xc3, 0x78, 0x6a, 0xcf, 0xe3, 0x8e, 0x91, 0x56, 0xa6, 0xf0, 0x5e, 0x47, 0x4e, 0x27, 0xa1,
	0x50, 0xfb, 0x7d, 0x81, 0xba, 0xd0, 0xf3, 0x51, 0x97, 0x66, 0x12, 0xac, 0x71, 0xf9, 0xf1, 0x99,
	0x9a, 0x64, 0xd4, 0x5d, 0x93, 0x93, 0xcc, 0x03, 0x60, 0x54, 0xb7, 0x7d, 0xde, 0x13, 0xc6, 0xa5,
	0xed, 0x72, 0x27, 0x8e, 0x55, 0x55, 0x5f, 0x47, 0xce, 0x19, 0xef, 0x89, 0x13, 0xa4, 0xb7, 0x2d,
	0x76, 0x03, 0x96, 0x92, 0xb0, 0x55, 0x0b, 0xed, 0x3f, 0x45, 0x58, 0x49, 0x5a, 0xc2, 0xd6, 0xa0,
	0x18, 0x59, 0x51, 0xd5, 0x8b, 0xb6, 0x15, 0x9b, 0x55, 0x9c, 0x98, 0x35, 0x07, 0x17, 0xe9, 0xd1,
	0x6c, 0xf1, 0x8a, 0xd1, 0x6c, 0x29, 0x33, 0x9a, 0xed, 0x02, 0xf8, 0x81, 0xfd, 0x6b, 0xa1, 0x46,
	0x87, 0x32, 0x19, 0x51, 0x25, 0xca, 0x69, 0x34, 0x58, 0x9c, 0x11, 0x1b, 0x87, 0x87, 0x65, 0x35,
	0x58, 0x10, 0x01, 0xa7, 0x87, 0x1d, 0xa8, 0x3a, 0x43, 0xb3, 0x3f, 0x32, 0xd0, 0xdc, 0x8a, 0xc2,
	0x05, 0x11, 0xce, 0xed, 0x18, 0x34, 0x51, 0x04, 0xab, 0x31, 0x68, 0xa2, 0xf0, 0x69, 0xb0, 0x3a,
	0x46, 0x94, 0x61, 0x09, 0xb3, 0x01, 0xa4, 0xba, 0x16, 0xa3, 0xea, 0x48, 0x98, 0x98, 0xc0, 0x83,
	0x91, 0xa1, 0xfa, 0x34, 0x8a, 0xd4, 0x48, 0x04, 0x06, 0x23, 0xea, 0x57, 0x28, 0x71, 0x1f, 0x36,
	0xa2, 0xf3, 0x31, 0xd1, 0xa3, 0x19, 0x46, 0x35, 0x90, 0x75, 0x65, 0x87, 0x14, 0x81, 0x1a, 0x64,
	0xb4, 0x2e, 0x8d, 0x17, 0x69, 0x04, 0x48, 0x9f, 0x7d, 0x01, 0x6b, 0x83, 0x91, 0xa1, 0xda, 0x9e,
	0xca, 0xac, 0x02, 0x65, 0xd6, 0x56, 0x22, 0xb3, 0x52, 0x9b, 0x56, 0x06, 0x23, 0xf5, 0x0b, 0x33,
	0x4b, 0xfb, 0x12, 0x6e, 0xb4, 0x44, 0x78, 0xe0, 0x38, 0x28, 0xf1, 0xc4, 0x96, 0xa1, 0x17, 0x8c,
	0xf2, 0x91, 0xb5, 0x09, 0x65, 0xef, 0xf2, 0x52, 0x8a, 0x18, 0x54, 0xd1, 0x6a, 0x82, 0x91, 0x52,
	0x12, 0x23, 0x5d, 0xb8, 0x99, 0xa3, 0x57, 0xfa, 0xec, 0x73, 0xa8, 0x05, 0x34, 0x15, 0x24, 0x8d,
	0x9d, 0x37, 0x3c, 0x80, 0x12, 0x27, 0x6b, 0xff, 0x5a, 0x80, 0x0d, 0x6a, 0xd9, 0x81, 0xe8, 0x53,
	0x10, 0xa9, 0x50, 0xbe, 0x03, 0x4b, 0x17, 0xbd, 0x49, 0xd6, 0x2f, 0x5e, 0xf4, 0xda, 0x64, 0xee,
	0x6b, 0x61, 0xf7, 0x5e, 0x8e, 0xcd, 0x55, 0xab, 0x64, 0xcd, 0x2c, 0xa5, 0x6a, 0xe6, 0x2e, 0x00,
	0x31, 0xd4, 0x3b, 0x26, 0xc2, 0x21, 0x52, 0xd4, 0x3b, 0x26, 0x2e, 0xa9, 0x84, 0xb2, 0x25, 0x85,
	0x23, 0x2a, 0x9b, 0xd1, 0x80, 0xaa, 0xf6, 0xda, 0x66, 0x84, 0x40, 0x3a, 0x04, 0x21, 0x96, 0xac,
	0xb6, 0xcb, 0xe9, 0x6a, 0xfb, 0xdb, 0x02, 0xd4, 0x8f, 0xf8, 0x88, 0x5a, 0x30, 0xf6, 0x25, 0xba,
	0x4c, 0x13, 0x2a, 0x52, 0x38, 0xc2, 0x0c, 0x85, 0xba, 0x4f, 0x45, 0x1f, 0xaf, 0x27, 0xcd, 0x78,
	0xd2, 0x39, 0x55, 0x33, 0xc6, 0xbe, 0x99, 0x68, 0xc6, 0xe5, 0x54, 0x33, 0xbe, 0x05, 0xf0, 0xda,
	0x40, 0x16, 0x39, 0x7c, 0x59, 0xbd, 0x25, 0x5e, 0x1f, 0x71, 0x2a, 0xad, 0xda, 0x3f, 0x8b, 0xb0,
	0x12, 0xbb, 0xf4, 0xd0, 0x73, 0x2f, 0x51, 0x8f, 0xe9, 0xb9, 0x97, 0x89, 0x17, 0x24, 0x2e, 0xdb,
	0x16, 0x56, 0x3e, 0x6e, 0x86, 0xf6, 0x2b, 0x3b, 0x1c, 0x25, 0x5f, 0x91, 0x2b, 0x31, 0x91, 0x3c,
	0xf1, 0x3e, 0xac, 0x8d, 0x85, 0x94, 0x9d, 0xca, 0xcb, 0xe3, 0xad, 0xca, 0xd8, 0xbb, 0x30, 0xde,
	0x46, 0x16, 0x2b, 0x77, 0xd7, 0x62, 0x1a, 0x9a, 0xfd, 0x3d, 0x5c, 0x96, 0x7d, 0x1e, 0xd7, 0x0a,
	0xe2, 0x56, 0x08, 0x7b, 0xb7, 0xa6, 0xc7, 0xc1, 0x24, 0xb6, 0xa2, 0x4a, 0x42, 0x9b, 0x3f, 0x83,
	0x2a, 0x96, 0x28, 0xb5, 0x37, 0x3b, 0x8b, 0x4e, 0x47, 0x52, 0xaf, 0xa0, 0x34, 0xf9, 0xf8, 0x00,
	0x58, 0x87, 0x5e, 0x06, 0x63, 0x2f, 0x63, 0x8a, 0x3d, 0xc0, 0xa6, 0xe6, 0x5e, 0x92, 0x97, 0xd3,
	0xf9, 0x9a, 0x92, 0x24, 0x21, 0xed, 0x26, 0xbc, 0x93, 0x51, 0x21, 0x7d, 0x6d, 0x18, 0x3f, 0x4e,
	0x52, 0x9a, 0x3f, 0x82, 0xb2, 0x0c, 0x79, 0x38, 0x94, 0xa4, 0x7b, 0x2d, 0xfb, 0x32, 0x08, 0x44,
	0xbf, 0x43, 0x02, 0x7a, 0x24, 0x38, 0xc9, 0xe2, 0x62, 0x22, 0x8b, 0x13, 0x39, 0x5f, 0x4a, 0xe6,
	0xbc, 0xc6, 0xe3, 0xa7, 0x4e, 0xca, 0x1a, 0xf6, 0x43, 0xa8, 0x12, 0x74, 0x66, 0x94, 0xa1, 0x94,
	0x7c, 0x05, 0x25, 0xc9, 0xaf, 0x37, 0x60, 0x29, 0xf4, 0x42, 0xee, 0xc4, 0x47, 0xd3, 0x42, 0xfb,
	0x08, 0x36, 0xa7, 0x8e, 0x78, 0x3c, 0x6a, 0x53, 0xd3, 0x4b, 0x01, 0xb4, 0x34, 0x01, 0xa8, 0xf6,
	0x42, 0x0d, 0x87, 0x99, 0x2d, 0x6f, 0x6b, 0x99, 0xf6, 0x73, 0xb8, 0x79, 0xee, 0x5b, 0x3c, 0x14,
	0xd3, 0x0e, 0x9e, 0x99, 0x23, 0x71, 0x4c, 0x8b, 0xd7, 0x89, 0x69, 0x03, 0x36, 0xf3, 0xd4, 0x4b,
	0x5f, 0x7b, 0x08, 0xec, 0x48, 0x38, 0xd7, 0x3d, 0x15, 0xc1, 0x91, 0x11, 0x97, 0x3e, 0x4e, 0x73,
	0xa0, 0xba, 0x11, 0x25, 0xf6, 0xa4, 0x4b, 0xaf, 0x52, 0x97, 0x7e, 0xbb, 0xe9, 0xf1, 0xfb, 0x29,
	0x93, 0xda, 0x29, 0xdc, 0x48, 0xe0, 0x5b, 0x59, 0x8d, 0x77, 0xfe, 0x34, 0x3d, 0x35, 0xaa, 0xd0,
	0xdd, 0x4c, 0xfa, 0x75, 0x7c, 0xc1, 0xe4, 0xc4, 0xa8, 0x6d, 0xc1, 0xcd, 0x1c, 0x7d, 0xd2, 0xd7,
	0x36, 0xa9, 0xe1, 0x65, 0x0e, 0xd2, 0x7c, 0x6a, 0x58, 0xd9, 0x0d, 0x6f, 0x6b, 0x01, 0x0e, 0x2e,
	0x6a, 0x06, 0x4c, 0xe6, 0x15, 0x10, 0x49, 0x8d, 0xfc, 0x1f, 0xc0, 0x8d, 0x44, 0xd4, 0x26, 0x57,
	0x9e, 0x8a, 0x13, 0x5e, 0x25, 0x47, 0x4e, 0xfa, 0xda, 0x17, 0xd0, 0xec, 0x88, 0xf0, 0x3c, 0xfd,
	0x38, 0x24, 0xdd, 0xa8, 0x66, 0xea, 0xfc, 0x42, 0xe6, 0xfc, 0x5d, 0xd8, 0x99, 0xb9, 0x5d, 0xfa,
	0xda, 0x2d, 0x68, 0xb6, 0x66, 0x6a, 0xd7, 0x7e, 0x0c, 0x3b, 0xad, 0xd9, 0x9b, 0xaf, 0x3e, 0xfc,
	0x21, 0x6c, 0x27, 0xdc, 0x7d, 0xfc, 0x46, 0x0c, 0xfc, 0x90, 0x9e, 0x68, 0xf9, 0x4f, 0xb9, 0x13,
	0x32, 0x26, 0x57, 0x5c, 0xfa, 0xec, 0x1e, 0xd4, 0x69, 0xa6, 0x8d, 0x3e, 0x6c, 0x5c, 0x3a, 0xbc,
	0x17, 0xf5, 0xce, 0x35, 0xa4, 0x2b, 0xe3, 0x4e, 0x1c, 0xde, 0xbb, 0xff, 0x29, 0xac, 0x75, 0xc7,
	0xa3, 0x23, 0xe1, 0xb9, 0x06, 0xcb, 0x38, 0xa5, 0x8c, 0x7c, 0x51, 0x5f, 0xc0, 0x45, 0x67, 0x68,
	0x9a, 0x42, 0xca, 0x7a, 0x81, 0x01, 0x94, 0x4f, 0xb8, 0xed, 0x08, 0xab, 0x5e, 0xbc, 0xff, 0x1b,
	0x58, 0x8f, 0x0f, 0xff, 0x4a, 0x88, 0xfe, 0x11, 0x1f, 0x21, 0xbb, 0x33, 0x74, 0x2d, 0x3e, 0xaa,
	0x2f, 0xe0, 0xef, 0xe7, 0x1e, 0xfd, 0x2e, 0xa0, 0x8e, 0xee, 0x50, 0x48, 0x5c, 0x14, 0xd9, 0x2a,
	0x54, 0xbf, 0x12, 0x96, 0xab, 0x96, 0x25, 0xb6, 0x02, 0x95, 0xee, 0xcb, 0x61, 0x40, 0xab, 0x45,
	0x3a, 0x20, 0xb0, 0xf1, 0xf7, 0x12, 0x72, 0x3a, 0x3c, 0x1c, 0x06, 0xb8, 0x2a, 0xe3, 0xb6, 0x73,
	0xb7, 0xef, 0xbd, 0x76, 0x8f, 0xf8, 0xa8, 0xbe, 0x7c, 0xff, 0x28, 0xfa, 0xcc, 0x3b, 0x2e, 0xdb,
	0x28, 0x70, 0xe0, 0x38, 0x6a, 0x51, 0x5f, 0x60, 0x55, 0x58, 0x3a, 0x97, 0xb6, 0xdb, 0x53, 0x56,
	0x9f, 0xbb, 0x43, 0x89, 0x56, 0xa3, 0xd2, 0x13, 0xdb, 0xb5, 0xe5, 0x4b, 0x61, 0xd5, 0x4b, 0xf7,
	0xff, 0x50, 0x80, 0xf5, 0x33, 0x6e, 0xf6, 0x79, 0x4f, 0xe0, 0xfb, 0x91, 0x6e, 0x7f, 0x03, 0xea,
	0xe7, 0xa7, 0x4f, 0x4f, 0x5f, 0x7c, 0x65, 0xb4, 0xbb, 0xc7, 0xcf, 0x8d, 0xee, 0x37, 0x67, 0xc7,
	0xf5, 0x05, 0xa4, 0x3e, 0x3e, 0x38, 0x7c, 0x7a, 0x76, 0x70, 0xf8, 0xd4, 0x38, 0xd3, 0x8f, 0x3b,
	0xc7, 0xa7, 0xdd, 0x7a, 0x81, 0x35, 0x61, 0x73, 0x4c, 0x3d, 0x3c, 0xd0, 0x8f, 0x0c, 0xbd, 0x7d,
	0xf8, 0xc4, 0x38, 0xfe, 0xfa, 0xac, 0x5e, 0x64, 0x3b, 0xb0, 0x95, 0xe6, 0x1d, 0x3e, 0x39, 0xd0,
	0x9f, 0x13, 0xb3, 0xc4, 0x76, 0x61, 0x7b, 0xcc, 0x7c, 0xf6, 0xa2, 0xdb, 0x3d, 0xd6, 0xbf, 0x31,
	0x4e, 0xf4, 0x83, 0xd6, 0x73, 0xd4, 0xbb, 0xb8, 0xff, 0xa7, 0x75, 0xa8, 0xc4, 0xd7, 0x63, 0x3a,
	0xac, 0x4f, 0x7d, 0x44, 0x63, 0xbb, 0x89, 0x5c, 0xcb, 0x7e, 0x89, 0x6b, 0xfe, 0xdf, 0x3c, 0xb6,
	0xf4, 0xb5, 0x05, 0xc6, 0x55, 0xca, 0x4f, 0x7f, 0x06, 0x60, 0xda, 0xd4, 0xce, 0x9c, 0xaf, 0x0a,
	0xcd, 0x77, 0xaf, 0x94, 0xa1, 0x23, 0xbe, 0x80, 0x4a, 0xfc, 0x16, 0x65, 0x9b, 0xc9, 0xa1, 0x60,
	0xf2, 0x98, 0x6e, 0x6e, 0xe5, 0xd2, 0x69, 0xbb, 0xba, 0x75, 0xea, 0x4d, 0x35, 0x75, 0xeb, 0xa9,
	0x97, 0xdf, 0xf4, 0xad, 0xa7, 0x9f, 0x05, 0xda, 0x02, 0xfb, 0x1a, 0x36, 0x32, 0x13, 0x38, 0xbb,
	0x9d, 0xde, 0x96, 0x99, 0xfb, 0x9b, 0x77, 0xe6, 0x0b, 0x90, 0x66, 0x9f, 0x72, 0x37, 0xff, 0x5b,
	0x12, 0xfb, 0x30, 0xad, 0x60, 0xe6, 0x47, 0xaa, 0xe6, 0xbd, 0xeb, 0x09, 0xc6, 0xfe, 0x99, 0x9a,
	0x7e, 0x52, 0xfe, 0xc9, 0x0e, 0x57, 0x29, 0xff, 0xe4, 0x0d, 0x4e, 0x0b, 0x13, 0xa4, 0xe5, 0xeb,
	0x6c, 0xcd, 0xd7, 0xd9, 0xca, 0xd5, 0xf9, 0x8b, 0xcc, 0x5c, 0x84, 0x13, 0x08, 0xbb, 0x3b, 0x7b,
	0x63, 0x34, 0xd4, 0x34, 0xb5, 0xab, 0x44, 0x48, 0xff, 0xcf, 0x80, 0x65, 0x27, 0x06, 0x96, 0x8c,
	0x59, 0xee, 0xbc, 0xd2, 0xbc, 0x7b, 0x85, 0x44, 0xec, 0x90, 0xa9, 0x29, 0x22, 0xe5, 0x90, 0xec,
	0x40, 0x92, 0x72, 0x48, 0xde, 0x00, 0x42, 0x20, 0xcc, 0xb4, 0xe1, 0x14, 0x08, 0xf3, 0x9a, 0x7e,
	0x0a, 0x84, 0xf9, 0x5d, 0x3c, 0x86, 0xf7, 0x1c, 0xcd, 0xad, 0xab, 0x34, 0xb7, 0x66, 0x6b, 0xce,
	0xf4, 0xdb, 0x94, 0xe6, 0xbc, 0xae, 0x9d, 0xd2, 0x9c, 0xdf, 0xae, 0x17, 0xd8, 0x2f, 0x61, 0x6b,
	0x46, 0xc7, 0x65, 0xef, 0xa7, 0xaf, 0x3c, 0xa3, 0xed, 0x36, 0x3f, 0xb8, 0x8e, 0x58, 0x7c, 0x56,
	0xeb, 0x1a, 0x67, 0xb5, 0xae, 0x77, 0x56, 0x6b, 0xee, 0x59, 0xbd, 0xd4, 0xac, 0x9e, 0xe8, 0xce,
	0xec, 0xbd, 0x7c, 0x7f, 0xa7, 0xfb, 0x7d, 0xf3, 0xfd, 0x6b, 0x48, 0xd1, 0x41, 0x6d, 0x7a, 0xee,
	0xd0, 0xe7, 0x93, 0x2e, 0xbe, 0x12, 0xd4, 0xbf, 0x22, 0xb6, 0xf7, 0xf4, 0xf8, 0x7f, 0xa3, 0x5f,
	0xee, 0xef, 0x8d, 0x1f, 0x61, 0xa8, 0x79, 0x33, 0xc5, 0x22, 0xf1, 0x48, 0xd5, 0x4f, 0xa0, 0x1e,
	0xab, 0x7a, 0x11, 0x58, 0x22, 0x68, 0x5b, 0x72, 0x9e, 0xa2, 0x34, 0x2b, 0xde, 0x11, 0xe9, 0x7a,
	0x42, 0x58, 0x3c, 0xf4, 0x64, 0xf8, 0x5d, 0xad, 0x6a, 0x53, 0x51, 0x42, 0x4d, 0xdf, 0xd9, 0xa8,
	0xa7, 0xb0, 0xa1, 0x7b, 0x8e, 0x73, 0xc1, 0xcd, 0xfe, 0xb3, 0x58, 0x1f, 0xbb, 0x95, 0xda, 0xa1,
	0x0b, 0xdf, 0xe1, 0xa6, 0x20, 0x56, 0xd6, 0xae, 0xe3, 0x81, 0x1f, 0x46, 0xc8, 0x6d, 0xd6, 0xff,
	0xfb, 0xe7, 0xbf, 0x75, 0x6b, 0x50, 0x1d, 0xff, 0x3b, 0xfb, 0xf1, 0xc3, 0x9f, 0x3e, 0xe8, 0x79,
	0x0e, 0x77, 0x7b, 0x7b, 0x9f, 0xec, 0x87, 0xe1, 0x9e, 0xe9, 0x0d, 0x1e, 0xd1, 0x3f, 0xa5, 0x4d,
	0xcf, 0x79, 0x24, 0x45, 0xf0, 0xca, 0x36, 0x85, 0x9c, 0xfc, 0xf7, 0xfb, 0xa2, 0x4c, 0xcc, 0x8f,
	0xff, 0x17, 0x00, 0x00, 0xff, 0xff, 0x79, 0x36, 0xf2, 0x97, 0x1b, 0x1f, 0x00, 0x00,
}
