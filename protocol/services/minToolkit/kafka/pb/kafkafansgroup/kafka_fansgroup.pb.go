// Code generated by protoc-gen-go. DO NOT EDIT.
// source: src/minToolkit/kafka/pb/kafkafansgroup/kafka_fansgroup.proto

package kafka_fans_event

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type EnumAnchorEventType int32

const (
	EnumAnchorEventType_E_NEW_FANS          EnumAnchorEventType = 0
	EnumAnchorEventType_E_POPULARITY_CHANGE EnumAnchorEventType = 1
	EnumAnchorEventType_E_LEAVE_FANS        EnumAnchorEventType = 2
)

var EnumAnchorEventType_name = map[int32]string{
	0: "E_NEW_FANS",
	1: "E_POPULARITY_CHANGE",
	2: "E_LEAVE_FANS",
}
var EnumAnchorEventType_value = map[string]int32{
	"E_NEW_FANS":          0,
	"E_POPULARITY_CHANGE": 1,
	"E_LEAVE_FANS":        2,
}

func (x EnumAnchorEventType) String() string {
	return proto.EnumName(EnumAnchorEventType_name, int32(x))
}
func (EnumAnchorEventType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_kafka_fansgroup_f9da106d326e7a3e, []int{0}
}

// 主播事件
type AnchorInfoEvent struct {
	EventType            uint32   `protobuf:"varint,1,opt,name=event_type,json=eventType,proto3" json:"event_type,omitempty"`
	FansUid              uint32   `protobuf:"varint,2,opt,name=fans_uid,json=fansUid,proto3" json:"fans_uid,omitempty"`
	AnchorUid            uint32   `protobuf:"varint,3,opt,name=anchor_uid,json=anchorUid,proto3" json:"anchor_uid,omitempty"`
	TagId                uint32   `protobuf:"varint,4,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	JoinAt               uint32   `protobuf:"varint,5,opt,name=join_at,json=joinAt,proto3" json:"join_at,omitempty"`
	ChannelId            uint32   `protobuf:"varint,6,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AnchorInfoEvent) Reset()         { *m = AnchorInfoEvent{} }
func (m *AnchorInfoEvent) String() string { return proto.CompactTextString(m) }
func (*AnchorInfoEvent) ProtoMessage()    {}
func (*AnchorInfoEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_kafka_fansgroup_f9da106d326e7a3e, []int{0}
}
func (m *AnchorInfoEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AnchorInfoEvent.Unmarshal(m, b)
}
func (m *AnchorInfoEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AnchorInfoEvent.Marshal(b, m, deterministic)
}
func (dst *AnchorInfoEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AnchorInfoEvent.Merge(dst, src)
}
func (m *AnchorInfoEvent) XXX_Size() int {
	return xxx_messageInfo_AnchorInfoEvent.Size(m)
}
func (m *AnchorInfoEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_AnchorInfoEvent.DiscardUnknown(m)
}

var xxx_messageInfo_AnchorInfoEvent proto.InternalMessageInfo

func (m *AnchorInfoEvent) GetEventType() uint32 {
	if m != nil {
		return m.EventType
	}
	return 0
}

func (m *AnchorInfoEvent) GetFansUid() uint32 {
	if m != nil {
		return m.FansUid
	}
	return 0
}

func (m *AnchorInfoEvent) GetAnchorUid() uint32 {
	if m != nil {
		return m.AnchorUid
	}
	return 0
}

func (m *AnchorInfoEvent) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *AnchorInfoEvent) GetJoinAt() uint32 {
	if m != nil {
		return m.JoinAt
	}
	return 0
}

func (m *AnchorInfoEvent) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

// 亲密值变化事件
type FansLoveValueChange struct {
	FansUid              uint32   `protobuf:"varint,1,opt,name=fans_uid,json=fansUid,proto3" json:"fans_uid,omitempty"`
	AnchorUid            uint32   `protobuf:"varint,2,opt,name=anchor_uid,json=anchorUid,proto3" json:"anchor_uid,omitempty"`
	LoveValue            uint32   `protobuf:"varint,3,opt,name=love_value,json=loveValue,proto3" json:"love_value,omitempty"`
	ChannelId            uint32   `protobuf:"varint,4,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	UpdateTs             uint32   `protobuf:"varint,5,opt,name=update_ts,json=updateTs,proto3" json:"update_ts,omitempty"`
	MissionId            uint32   `protobuf:"varint,6,opt,name=mission_id,json=missionId,proto3" json:"mission_id,omitempty"`
	NewLevel             uint32   `protobuf:"varint,7,opt,name=new_level,json=newLevel,proto3" json:"new_level,omitempty"`
	IsLightPlate         bool     `protobuf:"varint,8,opt,name=is_light_plate,json=isLightPlate,proto3" json:"is_light_plate,omitempty"`
	OldLevel             uint32   `protobuf:"varint,9,opt,name=old_level,json=oldLevel,proto3" json:"old_level,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FansLoveValueChange) Reset()         { *m = FansLoveValueChange{} }
func (m *FansLoveValueChange) String() string { return proto.CompactTextString(m) }
func (*FansLoveValueChange) ProtoMessage()    {}
func (*FansLoveValueChange) Descriptor() ([]byte, []int) {
	return fileDescriptor_kafka_fansgroup_f9da106d326e7a3e, []int{1}
}
func (m *FansLoveValueChange) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FansLoveValueChange.Unmarshal(m, b)
}
func (m *FansLoveValueChange) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FansLoveValueChange.Marshal(b, m, deterministic)
}
func (dst *FansLoveValueChange) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FansLoveValueChange.Merge(dst, src)
}
func (m *FansLoveValueChange) XXX_Size() int {
	return xxx_messageInfo_FansLoveValueChange.Size(m)
}
func (m *FansLoveValueChange) XXX_DiscardUnknown() {
	xxx_messageInfo_FansLoveValueChange.DiscardUnknown(m)
}

var xxx_messageInfo_FansLoveValueChange proto.InternalMessageInfo

func (m *FansLoveValueChange) GetFansUid() uint32 {
	if m != nil {
		return m.FansUid
	}
	return 0
}

func (m *FansLoveValueChange) GetAnchorUid() uint32 {
	if m != nil {
		return m.AnchorUid
	}
	return 0
}

func (m *FansLoveValueChange) GetLoveValue() uint32 {
	if m != nil {
		return m.LoveValue
	}
	return 0
}

func (m *FansLoveValueChange) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *FansLoveValueChange) GetUpdateTs() uint32 {
	if m != nil {
		return m.UpdateTs
	}
	return 0
}

func (m *FansLoveValueChange) GetMissionId() uint32 {
	if m != nil {
		return m.MissionId
	}
	return 0
}

func (m *FansLoveValueChange) GetNewLevel() uint32 {
	if m != nil {
		return m.NewLevel
	}
	return 0
}

func (m *FansLoveValueChange) GetIsLightPlate() bool {
	if m != nil {
		return m.IsLightPlate
	}
	return false
}

func (m *FansLoveValueChange) GetOldLevel() uint32 {
	if m != nil {
		return m.OldLevel
	}
	return 0
}

func init() {
	proto.RegisterType((*AnchorInfoEvent)(nil), "kafka_fans_event.AnchorInfoEvent")
	proto.RegisterType((*FansLoveValueChange)(nil), "kafka_fans_event.FansLoveValueChange")
	proto.RegisterEnum("kafka_fans_event.EnumAnchorEventType", EnumAnchorEventType_name, EnumAnchorEventType_value)
}

func init() {
	proto.RegisterFile("src/minToolkit/kafka/pb/kafkafansgroup/kafka_fansgroup.proto", fileDescriptor_kafka_fansgroup_f9da106d326e7a3e)
}

var fileDescriptor_kafka_fansgroup_f9da106d326e7a3e = []byte{
	// 409 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x7c, 0x92, 0xcf, 0x6f, 0xd3, 0x30,
	0x1c, 0xc5, 0x49, 0xd9, 0xfa, 0xe3, 0xab, 0x31, 0x22, 0x57, 0x68, 0x41, 0x68, 0xd2, 0x34, 0x71,
	0x98, 0x38, 0xd0, 0x03, 0x57, 0x2e, 0xd1, 0xe4, 0x41, 0xa4, 0xa8, 0x44, 0x25, 0x1b, 0xe2, 0x64,
	0x79, 0xb5, 0x9b, 0x98, 0xba, 0x76, 0x14, 0x3b, 0x99, 0xf6, 0x27, 0xf1, 0x27, 0xf0, 0xdf, 0x21,
	0xdb, 0x59, 0x81, 0x1e, 0xb8, 0xe5, 0x7d, 0x5e, 0xde, 0xd3, 0xb3, 0x6c, 0xf8, 0x68, 0xda, 0xf5,
	0x62, 0x27, 0x54, 0xa9, 0xb5, 0xdc, 0x0a, 0xbb, 0xd8, 0xd2, 0xcd, 0x96, 0x2e, 0x9a, 0xfb, 0xf0,
	0xb1, 0xa1, 0xca, 0x54, 0xad, 0xee, 0x9a, 0x20, 0xc9, 0x5e, 0xbf, 0x6f, 0x5a, 0x6d, 0x35, 0x8a,
	0xff, 0x60, 0xc2, 0x7b, 0xae, 0xec, 0xe5, 0xaf, 0x08, 0x5e, 0xa6, 0x6a, 0x5d, 0xeb, 0x36, 0x53,
	0x1b, 0x8d, 0x1d, 0x43, 0xe7, 0x00, 0xde, 0x24, 0xf6, 0xb1, 0xe1, 0x49, 0x74, 0x11, 0x5d, 0xbd,
	0x58, 0xcd, 0x3c, 0x29, 0x1f, 0x1b, 0x8e, 0x5e, 0xc3, 0xd4, 0x17, 0x74, 0x82, 0x25, 0x23, 0x6f,
	0x4e, 0x9c, 0xbe, 0x15, 0xcc, 0x25, 0xa9, 0x2f, 0xf3, 0xe6, 0xf3, 0x90, 0x0c, 0xc4, 0xd9, 0xaf,
	0x60, 0x6c, 0x69, 0x45, 0x04, 0x4b, 0x8e, 0xbc, 0x75, 0x6c, 0x69, 0x95, 0x31, 0x74, 0x06, 0x93,
	0x1f, 0x5a, 0x28, 0x42, 0x6d, 0x72, 0xec, 0xf9, 0xd8, 0xc9, 0xd4, 0x0f, 0x59, 0xd7, 0x54, 0x29,
	0x2e, 0x5d, 0x66, 0x1c, 0xea, 0x06, 0x92, 0xb1, 0xcb, 0x9f, 0x23, 0x98, 0xdf, 0x50, 0x65, 0x72,
	0xdd, 0xf3, 0x3b, 0x2a, 0x3b, 0x7e, 0x5d, 0x53, 0x55, 0xfd, 0x3b, 0x30, 0xfa, 0xdf, 0xc0, 0xd1,
	0xe1, 0xc0, 0x73, 0x00, 0xa9, 0x7b, 0x4e, 0x7a, 0xd7, 0xf6, 0xb4, 0x5f, 0x3e, 0xd5, 0x1f, 0xec,
	0x39, 0x3a, 0xd8, 0x83, 0xde, 0xc0, 0xac, 0x6b, 0x18, 0xb5, 0x9c, 0x58, 0x33, 0x9c, 0x64, 0x1a,
	0x40, 0x69, 0x5c, 0x76, 0x27, 0x8c, 0x11, 0x5a, 0xfd, 0x75, 0x96, 0x81, 0x84, 0xac, 0xe2, 0x0f,
	0x44, 0xf2, 0x9e, 0xcb, 0x64, 0x12, 0xb2, 0x8a, 0x3f, 0xe4, 0x4e, 0xa3, 0xb7, 0x70, 0x2a, 0x0c,
	0x91, 0xa2, 0xaa, 0x2d, 0x69, 0x24, 0xb5, 0x3c, 0x99, 0x5e, 0x44, 0x57, 0xd3, 0xd5, 0x89, 0x30,
	0xb9, 0x83, 0x85, 0x63, 0xae, 0x42, 0x4b, 0x36, 0x54, 0xcc, 0x42, 0x85, 0x96, 0xcc, 0x57, 0xbc,
	0x2b, 0x60, 0x8e, 0x55, 0xb7, 0x0b, 0x57, 0x8d, 0xf7, 0x77, 0x79, 0x0a, 0x80, 0xc9, 0x12, 0x7f,
	0x23, 0x37, 0xe9, 0xf2, 0x6b, 0xfc, 0x0c, 0x9d, 0xc1, 0x1c, 0x93, 0xe2, 0x4b, 0x71, 0x9b, 0xa7,
	0xab, 0xac, 0xfc, 0x4e, 0xae, 0x3f, 0xa7, 0xcb, 0x4f, 0x38, 0x8e, 0x50, 0x0c, 0x27, 0x98, 0xe4,
	0x38, 0xbd, 0xc3, 0xe1, 0xd7, 0xd1, 0xfd, 0xd8, 0x3f, 0xa9, 0x0f, 0xbf, 0x03, 0x00, 0x00, 0xff,
	0xff, 0x2c, 0xfa, 0xf8, 0x3b, 0x92, 0x02, 0x00, 0x00,
}
