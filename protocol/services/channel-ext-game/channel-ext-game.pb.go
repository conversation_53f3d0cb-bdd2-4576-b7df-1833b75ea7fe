// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/channel-ext-game/channel-ext-game.proto

package channel_ext_game // import "golang.52tt.com/protocol/services/channel-ext-game"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/services/extension/options"
import reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type ExtGameAwardReq_RewardType int32

const (
	ExtGameAwardReq_REWARD_TYPE_UNSPECIFIED     ExtGameAwardReq_RewardType = 0
	ExtGameAwardReq_REWARD_TYPE_TBEAN           ExtGameAwardReq_RewardType = 1
	ExtGameAwardReq_REWARD_TYPE_RED_DIAMONDD    ExtGameAwardReq_RewardType = 2
	ExtGameAwardReq_REWARD_TYPE_HEADWEAR        ExtGameAwardReq_RewardType = 3
	ExtGameAwardReq_REWARD_TYPE_NAMEPLATE       ExtGameAwardReq_RewardType = 4
	ExtGameAwardReq_REWARD_TYPE_HORSE           ExtGameAwardReq_RewardType = 5
	ExtGameAwardReq_REWARD_TYPE_OFFICIAL_CERT   ExtGameAwardReq_RewardType = 6
	ExtGameAwardReq_REWARD_TYPE_USER_DECORATION ExtGameAwardReq_RewardType = 7
	ExtGameAwardReq_REWARD_TYPE_MEDAL           ExtGameAwardReq_RewardType = 8
)

var ExtGameAwardReq_RewardType_name = map[int32]string{
	0: "REWARD_TYPE_UNSPECIFIED",
	1: "REWARD_TYPE_TBEAN",
	2: "REWARD_TYPE_RED_DIAMONDD",
	3: "REWARD_TYPE_HEADWEAR",
	4: "REWARD_TYPE_NAMEPLATE",
	5: "REWARD_TYPE_HORSE",
	6: "REWARD_TYPE_OFFICIAL_CERT",
	7: "REWARD_TYPE_USER_DECORATION",
	8: "REWARD_TYPE_MEDAL",
}
var ExtGameAwardReq_RewardType_value = map[string]int32{
	"REWARD_TYPE_UNSPECIFIED":     0,
	"REWARD_TYPE_TBEAN":           1,
	"REWARD_TYPE_RED_DIAMONDD":    2,
	"REWARD_TYPE_HEADWEAR":        3,
	"REWARD_TYPE_NAMEPLATE":       4,
	"REWARD_TYPE_HORSE":           5,
	"REWARD_TYPE_OFFICIAL_CERT":   6,
	"REWARD_TYPE_USER_DECORATION": 7,
	"REWARD_TYPE_MEDAL":           8,
}

func (x ExtGameAwardReq_RewardType) String() string {
	return proto.EnumName(ExtGameAwardReq_RewardType_name, int32(x))
}
func (ExtGameAwardReq_RewardType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_ext_game_f146a90a6a2035cb, []int{6, 0}
}

type GetUserExtGameJsCodeReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"` // Deprecated: Do not use.
	GameId               uint32   `protobuf:"varint,3,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`          // Deprecated: Do not use.
	ChannelViewId        string   `protobuf:"bytes,4,opt,name=channel_view_id,json=channelViewId,proto3" json:"channel_view_id,omitempty"`
	AppId                string   `protobuf:"bytes,5,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserExtGameJsCodeReq) Reset()         { *m = GetUserExtGameJsCodeReq{} }
func (m *GetUserExtGameJsCodeReq) String() string { return proto.CompactTextString(m) }
func (*GetUserExtGameJsCodeReq) ProtoMessage()    {}
func (*GetUserExtGameJsCodeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ext_game_f146a90a6a2035cb, []int{0}
}
func (m *GetUserExtGameJsCodeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserExtGameJsCodeReq.Unmarshal(m, b)
}
func (m *GetUserExtGameJsCodeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserExtGameJsCodeReq.Marshal(b, m, deterministic)
}
func (dst *GetUserExtGameJsCodeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserExtGameJsCodeReq.Merge(dst, src)
}
func (m *GetUserExtGameJsCodeReq) XXX_Size() int {
	return xxx_messageInfo_GetUserExtGameJsCodeReq.Size(m)
}
func (m *GetUserExtGameJsCodeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserExtGameJsCodeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserExtGameJsCodeReq proto.InternalMessageInfo

func (m *GetUserExtGameJsCodeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

// Deprecated: Do not use.
func (m *GetUserExtGameJsCodeReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

// Deprecated: Do not use.
func (m *GetUserExtGameJsCodeReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *GetUserExtGameJsCodeReq) GetChannelViewId() string {
	if m != nil {
		return m.ChannelViewId
	}
	return ""
}

func (m *GetUserExtGameJsCodeReq) GetAppId() string {
	if m != nil {
		return m.AppId
	}
	return ""
}

type GetUserExtGameJsCodeResp struct {
	JsCode               string   `protobuf:"bytes,1,opt,name=js_code,json=jsCode,proto3" json:"js_code,omitempty"`
	Openid               string   `protobuf:"bytes,2,opt,name=openid,proto3" json:"openid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserExtGameJsCodeResp) Reset()         { *m = GetUserExtGameJsCodeResp{} }
func (m *GetUserExtGameJsCodeResp) String() string { return proto.CompactTextString(m) }
func (*GetUserExtGameJsCodeResp) ProtoMessage()    {}
func (*GetUserExtGameJsCodeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ext_game_f146a90a6a2035cb, []int{1}
}
func (m *GetUserExtGameJsCodeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserExtGameJsCodeResp.Unmarshal(m, b)
}
func (m *GetUserExtGameJsCodeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserExtGameJsCodeResp.Marshal(b, m, deterministic)
}
func (dst *GetUserExtGameJsCodeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserExtGameJsCodeResp.Merge(dst, src)
}
func (m *GetUserExtGameJsCodeResp) XXX_Size() int {
	return xxx_messageInfo_GetUserExtGameJsCodeResp.Size(m)
}
func (m *GetUserExtGameJsCodeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserExtGameJsCodeResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserExtGameJsCodeResp proto.InternalMessageInfo

func (m *GetUserExtGameJsCodeResp) GetJsCode() string {
	if m != nil {
		return m.JsCode
	}
	return ""
}

func (m *GetUserExtGameJsCodeResp) GetOpenid() string {
	if m != nil {
		return m.Openid
	}
	return ""
}

type GetUserExtGameOpenidReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserExtGameOpenidReq) Reset()         { *m = GetUserExtGameOpenidReq{} }
func (m *GetUserExtGameOpenidReq) String() string { return proto.CompactTextString(m) }
func (*GetUserExtGameOpenidReq) ProtoMessage()    {}
func (*GetUserExtGameOpenidReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ext_game_f146a90a6a2035cb, []int{2}
}
func (m *GetUserExtGameOpenidReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserExtGameOpenidReq.Unmarshal(m, b)
}
func (m *GetUserExtGameOpenidReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserExtGameOpenidReq.Marshal(b, m, deterministic)
}
func (dst *GetUserExtGameOpenidReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserExtGameOpenidReq.Merge(dst, src)
}
func (m *GetUserExtGameOpenidReq) XXX_Size() int {
	return xxx_messageInfo_GetUserExtGameOpenidReq.Size(m)
}
func (m *GetUserExtGameOpenidReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserExtGameOpenidReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserExtGameOpenidReq proto.InternalMessageInfo

func (m *GetUserExtGameOpenidReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserExtGameOpenidResp struct {
	Openid               string   `protobuf:"bytes,1,opt,name=openid,proto3" json:"openid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserExtGameOpenidResp) Reset()         { *m = GetUserExtGameOpenidResp{} }
func (m *GetUserExtGameOpenidResp) String() string { return proto.CompactTextString(m) }
func (*GetUserExtGameOpenidResp) ProtoMessage()    {}
func (*GetUserExtGameOpenidResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ext_game_f146a90a6a2035cb, []int{3}
}
func (m *GetUserExtGameOpenidResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserExtGameOpenidResp.Unmarshal(m, b)
}
func (m *GetUserExtGameOpenidResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserExtGameOpenidResp.Marshal(b, m, deterministic)
}
func (dst *GetUserExtGameOpenidResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserExtGameOpenidResp.Merge(dst, src)
}
func (m *GetUserExtGameOpenidResp) XXX_Size() int {
	return xxx_messageInfo_GetUserExtGameOpenidResp.Size(m)
}
func (m *GetUserExtGameOpenidResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserExtGameOpenidResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserExtGameOpenidResp proto.InternalMessageInfo

func (m *GetUserExtGameOpenidResp) GetOpenid() string {
	if m != nil {
		return m.Openid
	}
	return ""
}

type ExtGameConsumeReq struct {
	Openid               string   `protobuf:"bytes,1,opt,name=openid,proto3" json:"openid,omitempty"`
	Appid                string   `protobuf:"bytes,2,opt,name=appid,proto3" json:"appid,omitempty"`
	OrderId              string   `protobuf:"bytes,3,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	Amount               uint32   `protobuf:"varint,4,opt,name=amount,proto3" json:"amount,omitempty"`
	OutsideTs            int64    `protobuf:"varint,5,opt,name=outside_ts,json=outsideTs,proto3" json:"outside_ts,omitempty"`
	Uid                  uint32   `protobuf:"varint,6,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ExtGameConsumeReq) Reset()         { *m = ExtGameConsumeReq{} }
func (m *ExtGameConsumeReq) String() string { return proto.CompactTextString(m) }
func (*ExtGameConsumeReq) ProtoMessage()    {}
func (*ExtGameConsumeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ext_game_f146a90a6a2035cb, []int{4}
}
func (m *ExtGameConsumeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExtGameConsumeReq.Unmarshal(m, b)
}
func (m *ExtGameConsumeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExtGameConsumeReq.Marshal(b, m, deterministic)
}
func (dst *ExtGameConsumeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExtGameConsumeReq.Merge(dst, src)
}
func (m *ExtGameConsumeReq) XXX_Size() int {
	return xxx_messageInfo_ExtGameConsumeReq.Size(m)
}
func (m *ExtGameConsumeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ExtGameConsumeReq.DiscardUnknown(m)
}

var xxx_messageInfo_ExtGameConsumeReq proto.InternalMessageInfo

func (m *ExtGameConsumeReq) GetOpenid() string {
	if m != nil {
		return m.Openid
	}
	return ""
}

func (m *ExtGameConsumeReq) GetAppid() string {
	if m != nil {
		return m.Appid
	}
	return ""
}

func (m *ExtGameConsumeReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *ExtGameConsumeReq) GetAmount() uint32 {
	if m != nil {
		return m.Amount
	}
	return 0
}

func (m *ExtGameConsumeReq) GetOutsideTs() int64 {
	if m != nil {
		return m.OutsideTs
	}
	return 0
}

func (m *ExtGameConsumeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type ExtGameConsumeResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ExtGameConsumeResp) Reset()         { *m = ExtGameConsumeResp{} }
func (m *ExtGameConsumeResp) String() string { return proto.CompactTextString(m) }
func (*ExtGameConsumeResp) ProtoMessage()    {}
func (*ExtGameConsumeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ext_game_f146a90a6a2035cb, []int{5}
}
func (m *ExtGameConsumeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExtGameConsumeResp.Unmarshal(m, b)
}
func (m *ExtGameConsumeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExtGameConsumeResp.Marshal(b, m, deterministic)
}
func (dst *ExtGameConsumeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExtGameConsumeResp.Merge(dst, src)
}
func (m *ExtGameConsumeResp) XXX_Size() int {
	return xxx_messageInfo_ExtGameConsumeResp.Size(m)
}
func (m *ExtGameConsumeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ExtGameConsumeResp.DiscardUnknown(m)
}

var xxx_messageInfo_ExtGameConsumeResp proto.InternalMessageInfo

type ExtGameAwardReq struct {
	Uid                  string   `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"` // Deprecated: Do not use.
	Openid               string   `protobuf:"bytes,2,opt,name=openid,proto3" json:"openid,omitempty"`
	Appid                string   `protobuf:"bytes,3,opt,name=appid,proto3" json:"appid,omitempty"`
	OrderId              string   `protobuf:"bytes,4,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	RewardId             string   `protobuf:"bytes,5,opt,name=reward_id,json=rewardId,proto3" json:"reward_id,omitempty"`
	RewardType           uint32   `protobuf:"varint,6,opt,name=reward_type,json=rewardType,proto3" json:"reward_type,omitempty"`
	Amount               uint32   `protobuf:"varint,7,opt,name=amount,proto3" json:"amount,omitempty"`
	UserId               uint32   `protobuf:"varint,8,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ExtGameAwardReq) Reset()         { *m = ExtGameAwardReq{} }
func (m *ExtGameAwardReq) String() string { return proto.CompactTextString(m) }
func (*ExtGameAwardReq) ProtoMessage()    {}
func (*ExtGameAwardReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ext_game_f146a90a6a2035cb, []int{6}
}
func (m *ExtGameAwardReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExtGameAwardReq.Unmarshal(m, b)
}
func (m *ExtGameAwardReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExtGameAwardReq.Marshal(b, m, deterministic)
}
func (dst *ExtGameAwardReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExtGameAwardReq.Merge(dst, src)
}
func (m *ExtGameAwardReq) XXX_Size() int {
	return xxx_messageInfo_ExtGameAwardReq.Size(m)
}
func (m *ExtGameAwardReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ExtGameAwardReq.DiscardUnknown(m)
}

var xxx_messageInfo_ExtGameAwardReq proto.InternalMessageInfo

// Deprecated: Do not use.
func (m *ExtGameAwardReq) GetUid() string {
	if m != nil {
		return m.Uid
	}
	return ""
}

func (m *ExtGameAwardReq) GetOpenid() string {
	if m != nil {
		return m.Openid
	}
	return ""
}

func (m *ExtGameAwardReq) GetAppid() string {
	if m != nil {
		return m.Appid
	}
	return ""
}

func (m *ExtGameAwardReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *ExtGameAwardReq) GetRewardId() string {
	if m != nil {
		return m.RewardId
	}
	return ""
}

func (m *ExtGameAwardReq) GetRewardType() uint32 {
	if m != nil {
		return m.RewardType
	}
	return 0
}

func (m *ExtGameAwardReq) GetAmount() uint32 {
	if m != nil {
		return m.Amount
	}
	return 0
}

func (m *ExtGameAwardReq) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

type ExtGameAwardResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ExtGameAwardResp) Reset()         { *m = ExtGameAwardResp{} }
func (m *ExtGameAwardResp) String() string { return proto.CompactTextString(m) }
func (*ExtGameAwardResp) ProtoMessage()    {}
func (*ExtGameAwardResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ext_game_f146a90a6a2035cb, []int{7}
}
func (m *ExtGameAwardResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExtGameAwardResp.Unmarshal(m, b)
}
func (m *ExtGameAwardResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExtGameAwardResp.Marshal(b, m, deterministic)
}
func (dst *ExtGameAwardResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExtGameAwardResp.Merge(dst, src)
}
func (m *ExtGameAwardResp) XXX_Size() int {
	return xxx_messageInfo_ExtGameAwardResp.Size(m)
}
func (m *ExtGameAwardResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ExtGameAwardResp.DiscardUnknown(m)
}

var xxx_messageInfo_ExtGameAwardResp proto.InternalMessageInfo

type CancelUserExtGameJsCodeReq struct {
	JsCode               string   `protobuf:"bytes,1,opt,name=js_code,json=jsCode,proto3" json:"js_code,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CancelUserExtGameJsCodeReq) Reset()         { *m = CancelUserExtGameJsCodeReq{} }
func (m *CancelUserExtGameJsCodeReq) String() string { return proto.CompactTextString(m) }
func (*CancelUserExtGameJsCodeReq) ProtoMessage()    {}
func (*CancelUserExtGameJsCodeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ext_game_f146a90a6a2035cb, []int{8}
}
func (m *CancelUserExtGameJsCodeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelUserExtGameJsCodeReq.Unmarshal(m, b)
}
func (m *CancelUserExtGameJsCodeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelUserExtGameJsCodeReq.Marshal(b, m, deterministic)
}
func (dst *CancelUserExtGameJsCodeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelUserExtGameJsCodeReq.Merge(dst, src)
}
func (m *CancelUserExtGameJsCodeReq) XXX_Size() int {
	return xxx_messageInfo_CancelUserExtGameJsCodeReq.Size(m)
}
func (m *CancelUserExtGameJsCodeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelUserExtGameJsCodeReq.DiscardUnknown(m)
}

var xxx_messageInfo_CancelUserExtGameJsCodeReq proto.InternalMessageInfo

func (m *CancelUserExtGameJsCodeReq) GetJsCode() string {
	if m != nil {
		return m.JsCode
	}
	return ""
}

type CancelUserExtGameJsCodeResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CancelUserExtGameJsCodeResp) Reset()         { *m = CancelUserExtGameJsCodeResp{} }
func (m *CancelUserExtGameJsCodeResp) String() string { return proto.CompactTextString(m) }
func (*CancelUserExtGameJsCodeResp) ProtoMessage()    {}
func (*CancelUserExtGameJsCodeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ext_game_f146a90a6a2035cb, []int{9}
}
func (m *CancelUserExtGameJsCodeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelUserExtGameJsCodeResp.Unmarshal(m, b)
}
func (m *CancelUserExtGameJsCodeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelUserExtGameJsCodeResp.Marshal(b, m, deterministic)
}
func (dst *CancelUserExtGameJsCodeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelUserExtGameJsCodeResp.Merge(dst, src)
}
func (m *CancelUserExtGameJsCodeResp) XXX_Size() int {
	return xxx_messageInfo_CancelUserExtGameJsCodeResp.Size(m)
}
func (m *CancelUserExtGameJsCodeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelUserExtGameJsCodeResp.DiscardUnknown(m)
}

var xxx_messageInfo_CancelUserExtGameJsCodeResp proto.InternalMessageInfo

type GetUidByOpenidReq struct {
	Openid               string   `protobuf:"bytes,1,opt,name=openid,proto3" json:"openid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUidByOpenidReq) Reset()         { *m = GetUidByOpenidReq{} }
func (m *GetUidByOpenidReq) String() string { return proto.CompactTextString(m) }
func (*GetUidByOpenidReq) ProtoMessage()    {}
func (*GetUidByOpenidReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ext_game_f146a90a6a2035cb, []int{10}
}
func (m *GetUidByOpenidReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUidByOpenidReq.Unmarshal(m, b)
}
func (m *GetUidByOpenidReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUidByOpenidReq.Marshal(b, m, deterministic)
}
func (dst *GetUidByOpenidReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUidByOpenidReq.Merge(dst, src)
}
func (m *GetUidByOpenidReq) XXX_Size() int {
	return xxx_messageInfo_GetUidByOpenidReq.Size(m)
}
func (m *GetUidByOpenidReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUidByOpenidReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUidByOpenidReq proto.InternalMessageInfo

func (m *GetUidByOpenidReq) GetOpenid() string {
	if m != nil {
		return m.Openid
	}
	return ""
}

type GetUidByOpenidResp struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUidByOpenidResp) Reset()         { *m = GetUidByOpenidResp{} }
func (m *GetUidByOpenidResp) String() string { return proto.CompactTextString(m) }
func (*GetUidByOpenidResp) ProtoMessage()    {}
func (*GetUidByOpenidResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ext_game_f146a90a6a2035cb, []int{11}
}
func (m *GetUidByOpenidResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUidByOpenidResp.Unmarshal(m, b)
}
func (m *GetUidByOpenidResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUidByOpenidResp.Marshal(b, m, deterministic)
}
func (dst *GetUidByOpenidResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUidByOpenidResp.Merge(dst, src)
}
func (m *GetUidByOpenidResp) XXX_Size() int {
	return xxx_messageInfo_GetUidByOpenidResp.Size(m)
}
func (m *GetUidByOpenidResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUidByOpenidResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUidByOpenidResp proto.InternalMessageInfo

func (m *GetUidByOpenidResp) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type CheckUserGameAccessReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GameId               uint32   `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"` // Deprecated: Do not use.
	ChannelId            uint32   `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	AppId                string   `protobuf:"bytes,4,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckUserGameAccessReq) Reset()         { *m = CheckUserGameAccessReq{} }
func (m *CheckUserGameAccessReq) String() string { return proto.CompactTextString(m) }
func (*CheckUserGameAccessReq) ProtoMessage()    {}
func (*CheckUserGameAccessReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ext_game_f146a90a6a2035cb, []int{12}
}
func (m *CheckUserGameAccessReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckUserGameAccessReq.Unmarshal(m, b)
}
func (m *CheckUserGameAccessReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckUserGameAccessReq.Marshal(b, m, deterministic)
}
func (dst *CheckUserGameAccessReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckUserGameAccessReq.Merge(dst, src)
}
func (m *CheckUserGameAccessReq) XXX_Size() int {
	return xxx_messageInfo_CheckUserGameAccessReq.Size(m)
}
func (m *CheckUserGameAccessReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckUserGameAccessReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckUserGameAccessReq proto.InternalMessageInfo

func (m *CheckUserGameAccessReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

// Deprecated: Do not use.
func (m *CheckUserGameAccessReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *CheckUserGameAccessReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *CheckUserGameAccessReq) GetAppId() string {
	if m != nil {
		return m.AppId
	}
	return ""
}

type CheckUserGameAccessResp struct {
	Access               bool     `protobuf:"varint,1,opt,name=access,proto3" json:"access,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckUserGameAccessResp) Reset()         { *m = CheckUserGameAccessResp{} }
func (m *CheckUserGameAccessResp) String() string { return proto.CompactTextString(m) }
func (*CheckUserGameAccessResp) ProtoMessage()    {}
func (*CheckUserGameAccessResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ext_game_f146a90a6a2035cb, []int{13}
}
func (m *CheckUserGameAccessResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckUserGameAccessResp.Unmarshal(m, b)
}
func (m *CheckUserGameAccessResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckUserGameAccessResp.Marshal(b, m, deterministic)
}
func (dst *CheckUserGameAccessResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckUserGameAccessResp.Merge(dst, src)
}
func (m *CheckUserGameAccessResp) XXX_Size() int {
	return xxx_messageInfo_CheckUserGameAccessResp.Size(m)
}
func (m *CheckUserGameAccessResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckUserGameAccessResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckUserGameAccessResp proto.InternalMessageInfo

func (m *CheckUserGameAccessResp) GetAccess() bool {
	if m != nil {
		return m.Access
	}
	return false
}

type CheckChannelBlackListReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckChannelBlackListReq) Reset()         { *m = CheckChannelBlackListReq{} }
func (m *CheckChannelBlackListReq) String() string { return proto.CompactTextString(m) }
func (*CheckChannelBlackListReq) ProtoMessage()    {}
func (*CheckChannelBlackListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ext_game_f146a90a6a2035cb, []int{14}
}
func (m *CheckChannelBlackListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckChannelBlackListReq.Unmarshal(m, b)
}
func (m *CheckChannelBlackListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckChannelBlackListReq.Marshal(b, m, deterministic)
}
func (dst *CheckChannelBlackListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckChannelBlackListReq.Merge(dst, src)
}
func (m *CheckChannelBlackListReq) XXX_Size() int {
	return xxx_messageInfo_CheckChannelBlackListReq.Size(m)
}
func (m *CheckChannelBlackListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckChannelBlackListReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckChannelBlackListReq proto.InternalMessageInfo

func (m *CheckChannelBlackListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type CheckChannelBlackListResp struct {
	IsBlack              bool     `protobuf:"varint,1,opt,name=is_black,json=isBlack,proto3" json:"is_black,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckChannelBlackListResp) Reset()         { *m = CheckChannelBlackListResp{} }
func (m *CheckChannelBlackListResp) String() string { return proto.CompactTextString(m) }
func (*CheckChannelBlackListResp) ProtoMessage()    {}
func (*CheckChannelBlackListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ext_game_f146a90a6a2035cb, []int{15}
}
func (m *CheckChannelBlackListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckChannelBlackListResp.Unmarshal(m, b)
}
func (m *CheckChannelBlackListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckChannelBlackListResp.Marshal(b, m, deterministic)
}
func (dst *CheckChannelBlackListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckChannelBlackListResp.Merge(dst, src)
}
func (m *CheckChannelBlackListResp) XXX_Size() int {
	return xxx_messageInfo_CheckChannelBlackListResp.Size(m)
}
func (m *CheckChannelBlackListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckChannelBlackListResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckChannelBlackListResp proto.InternalMessageInfo

func (m *CheckChannelBlackListResp) GetIsBlack() bool {
	if m != nil {
		return m.IsBlack
	}
	return false
}

type GetWhiteChannelRandomlyReq struct {
	Count                uint32   `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetWhiteChannelRandomlyReq) Reset()         { *m = GetWhiteChannelRandomlyReq{} }
func (m *GetWhiteChannelRandomlyReq) String() string { return proto.CompactTextString(m) }
func (*GetWhiteChannelRandomlyReq) ProtoMessage()    {}
func (*GetWhiteChannelRandomlyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ext_game_f146a90a6a2035cb, []int{16}
}
func (m *GetWhiteChannelRandomlyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWhiteChannelRandomlyReq.Unmarshal(m, b)
}
func (m *GetWhiteChannelRandomlyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWhiteChannelRandomlyReq.Marshal(b, m, deterministic)
}
func (dst *GetWhiteChannelRandomlyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWhiteChannelRandomlyReq.Merge(dst, src)
}
func (m *GetWhiteChannelRandomlyReq) XXX_Size() int {
	return xxx_messageInfo_GetWhiteChannelRandomlyReq.Size(m)
}
func (m *GetWhiteChannelRandomlyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWhiteChannelRandomlyReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetWhiteChannelRandomlyReq proto.InternalMessageInfo

func (m *GetWhiteChannelRandomlyReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type GetWhiteChannelRandomlyResp struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"` // Deprecated: Do not use.
	CidList              []uint32 `protobuf:"varint,2,rep,packed,name=cid_list,json=cidList,proto3" json:"cid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetWhiteChannelRandomlyResp) Reset()         { *m = GetWhiteChannelRandomlyResp{} }
func (m *GetWhiteChannelRandomlyResp) String() string { return proto.CompactTextString(m) }
func (*GetWhiteChannelRandomlyResp) ProtoMessage()    {}
func (*GetWhiteChannelRandomlyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ext_game_f146a90a6a2035cb, []int{17}
}
func (m *GetWhiteChannelRandomlyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWhiteChannelRandomlyResp.Unmarshal(m, b)
}
func (m *GetWhiteChannelRandomlyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWhiteChannelRandomlyResp.Marshal(b, m, deterministic)
}
func (dst *GetWhiteChannelRandomlyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWhiteChannelRandomlyResp.Merge(dst, src)
}
func (m *GetWhiteChannelRandomlyResp) XXX_Size() int {
	return xxx_messageInfo_GetWhiteChannelRandomlyResp.Size(m)
}
func (m *GetWhiteChannelRandomlyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWhiteChannelRandomlyResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetWhiteChannelRandomlyResp proto.InternalMessageInfo

// Deprecated: Do not use.
func (m *GetWhiteChannelRandomlyResp) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetWhiteChannelRandomlyResp) GetCidList() []uint32 {
	if m != nil {
		return m.CidList
	}
	return nil
}

type GetAuthInfoByJsCodeReq struct {
	JsCode               string   `protobuf:"bytes,1,opt,name=js_code,json=jsCode,proto3" json:"js_code,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAuthInfoByJsCodeReq) Reset()         { *m = GetAuthInfoByJsCodeReq{} }
func (m *GetAuthInfoByJsCodeReq) String() string { return proto.CompactTextString(m) }
func (*GetAuthInfoByJsCodeReq) ProtoMessage()    {}
func (*GetAuthInfoByJsCodeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ext_game_f146a90a6a2035cb, []int{18}
}
func (m *GetAuthInfoByJsCodeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAuthInfoByJsCodeReq.Unmarshal(m, b)
}
func (m *GetAuthInfoByJsCodeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAuthInfoByJsCodeReq.Marshal(b, m, deterministic)
}
func (dst *GetAuthInfoByJsCodeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAuthInfoByJsCodeReq.Merge(dst, src)
}
func (m *GetAuthInfoByJsCodeReq) XXX_Size() int {
	return xxx_messageInfo_GetAuthInfoByJsCodeReq.Size(m)
}
func (m *GetAuthInfoByJsCodeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAuthInfoByJsCodeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAuthInfoByJsCodeReq proto.InternalMessageInfo

func (m *GetAuthInfoByJsCodeReq) GetJsCode() string {
	if m != nil {
		return m.JsCode
	}
	return ""
}

type GetAuthInfoByJsCodeResp struct {
	Openid               string   `protobuf:"bytes,1,opt,name=openid,proto3" json:"openid,omitempty"`
	Uid                  string   `protobuf:"bytes,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Appid                string   `protobuf:"bytes,3,opt,name=appid,proto3" json:"appid,omitempty"`
	ChannelDisplayId     uint32   `protobuf:"varint,4,opt,name=channel_display_id,json=channelDisplayId,proto3" json:"channel_display_id,omitempty"` // Deprecated: Do not use.
	ChannelViewId        string   `protobuf:"bytes,5,opt,name=channel_view_id,json=channelViewId,proto3" json:"channel_view_id,omitempty"`
	ClientType           uint32   `protobuf:"varint,6,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	MarketId             uint32   `protobuf:"varint,7,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAuthInfoByJsCodeResp) Reset()         { *m = GetAuthInfoByJsCodeResp{} }
func (m *GetAuthInfoByJsCodeResp) String() string { return proto.CompactTextString(m) }
func (*GetAuthInfoByJsCodeResp) ProtoMessage()    {}
func (*GetAuthInfoByJsCodeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ext_game_f146a90a6a2035cb, []int{19}
}
func (m *GetAuthInfoByJsCodeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAuthInfoByJsCodeResp.Unmarshal(m, b)
}
func (m *GetAuthInfoByJsCodeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAuthInfoByJsCodeResp.Marshal(b, m, deterministic)
}
func (dst *GetAuthInfoByJsCodeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAuthInfoByJsCodeResp.Merge(dst, src)
}
func (m *GetAuthInfoByJsCodeResp) XXX_Size() int {
	return xxx_messageInfo_GetAuthInfoByJsCodeResp.Size(m)
}
func (m *GetAuthInfoByJsCodeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAuthInfoByJsCodeResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAuthInfoByJsCodeResp proto.InternalMessageInfo

func (m *GetAuthInfoByJsCodeResp) GetOpenid() string {
	if m != nil {
		return m.Openid
	}
	return ""
}

func (m *GetAuthInfoByJsCodeResp) GetUid() string {
	if m != nil {
		return m.Uid
	}
	return ""
}

func (m *GetAuthInfoByJsCodeResp) GetAppid() string {
	if m != nil {
		return m.Appid
	}
	return ""
}

// Deprecated: Do not use.
func (m *GetAuthInfoByJsCodeResp) GetChannelDisplayId() uint32 {
	if m != nil {
		return m.ChannelDisplayId
	}
	return 0
}

func (m *GetAuthInfoByJsCodeResp) GetChannelViewId() string {
	if m != nil {
		return m.ChannelViewId
	}
	return ""
}

func (m *GetAuthInfoByJsCodeResp) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

func (m *GetAuthInfoByJsCodeResp) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

// 获取游戏信息列表
type GetExtGameInfoListReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetExtGameInfoListReq) Reset()         { *m = GetExtGameInfoListReq{} }
func (m *GetExtGameInfoListReq) String() string { return proto.CompactTextString(m) }
func (*GetExtGameInfoListReq) ProtoMessage()    {}
func (*GetExtGameInfoListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ext_game_f146a90a6a2035cb, []int{20}
}
func (m *GetExtGameInfoListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetExtGameInfoListReq.Unmarshal(m, b)
}
func (m *GetExtGameInfoListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetExtGameInfoListReq.Marshal(b, m, deterministic)
}
func (dst *GetExtGameInfoListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetExtGameInfoListReq.Merge(dst, src)
}
func (m *GetExtGameInfoListReq) XXX_Size() int {
	return xxx_messageInfo_GetExtGameInfoListReq.Size(m)
}
func (m *GetExtGameInfoListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetExtGameInfoListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetExtGameInfoListReq proto.InternalMessageInfo

// 获取游戏信息列表
type GetExtGameInfoListResp struct {
	GameInfo             []*ExtGameInfo `protobuf:"bytes,1,rep,name=game_info,json=gameInfo,proto3" json:"game_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetExtGameInfoListResp) Reset()         { *m = GetExtGameInfoListResp{} }
func (m *GetExtGameInfoListResp) String() string { return proto.CompactTextString(m) }
func (*GetExtGameInfoListResp) ProtoMessage()    {}
func (*GetExtGameInfoListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ext_game_f146a90a6a2035cb, []int{21}
}
func (m *GetExtGameInfoListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetExtGameInfoListResp.Unmarshal(m, b)
}
func (m *GetExtGameInfoListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetExtGameInfoListResp.Marshal(b, m, deterministic)
}
func (dst *GetExtGameInfoListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetExtGameInfoListResp.Merge(dst, src)
}
func (m *GetExtGameInfoListResp) XXX_Size() int {
	return xxx_messageInfo_GetExtGameInfoListResp.Size(m)
}
func (m *GetExtGameInfoListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetExtGameInfoListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetExtGameInfoListResp proto.InternalMessageInfo

func (m *GetExtGameInfoListResp) GetGameInfo() []*ExtGameInfo {
	if m != nil {
		return m.GameInfo
	}
	return nil
}

// 游戏包信息
type ExtGameInfo struct {
	GameId               uint32   `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"` // Deprecated: Do not use.
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Content              string   `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"`
	Version              string   `protobuf:"bytes,4,opt,name=version,proto3" json:"version,omitempty"`
	Build                uint32   `protobuf:"varint,5,opt,name=build,proto3" json:"build,omitempty"`
	Zip                  string   `protobuf:"bytes,6,opt,name=zip,proto3" json:"zip,omitempty"`
	H5Url                string   `protobuf:"bytes,7,opt,name=h5_url,json=h5Url,proto3" json:"h5_url,omitempty"`
	FullUrl              string   `protobuf:"bytes,8,opt,name=full_url,json=fullUrl,proto3" json:"full_url,omitempty"`
	Md5                  string   `protobuf:"bytes,9,opt,name=md5,proto3" json:"md5,omitempty"`
	Size                 uint32   `protobuf:"varint,10,opt,name=size,proto3" json:"size,omitempty"`
	AppId                string   `protobuf:"bytes,11,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ExtGameInfo) Reset()         { *m = ExtGameInfo{} }
func (m *ExtGameInfo) String() string { return proto.CompactTextString(m) }
func (*ExtGameInfo) ProtoMessage()    {}
func (*ExtGameInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ext_game_f146a90a6a2035cb, []int{22}
}
func (m *ExtGameInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExtGameInfo.Unmarshal(m, b)
}
func (m *ExtGameInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExtGameInfo.Marshal(b, m, deterministic)
}
func (dst *ExtGameInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExtGameInfo.Merge(dst, src)
}
func (m *ExtGameInfo) XXX_Size() int {
	return xxx_messageInfo_ExtGameInfo.Size(m)
}
func (m *ExtGameInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ExtGameInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ExtGameInfo proto.InternalMessageInfo

// Deprecated: Do not use.
func (m *ExtGameInfo) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *ExtGameInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *ExtGameInfo) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *ExtGameInfo) GetVersion() string {
	if m != nil {
		return m.Version
	}
	return ""
}

func (m *ExtGameInfo) GetBuild() uint32 {
	if m != nil {
		return m.Build
	}
	return 0
}

func (m *ExtGameInfo) GetZip() string {
	if m != nil {
		return m.Zip
	}
	return ""
}

func (m *ExtGameInfo) GetH5Url() string {
	if m != nil {
		return m.H5Url
	}
	return ""
}

func (m *ExtGameInfo) GetFullUrl() string {
	if m != nil {
		return m.FullUrl
	}
	return ""
}

func (m *ExtGameInfo) GetMd5() string {
	if m != nil {
		return m.Md5
	}
	return ""
}

func (m *ExtGameInfo) GetSize() uint32 {
	if m != nil {
		return m.Size
	}
	return 0
}

func (m *ExtGameInfo) GetAppId() string {
	if m != nil {
		return m.AppId
	}
	return ""
}

type BatchGetUidByOpenIdsReq struct {
	OpenIdList           []string `protobuf:"bytes,1,rep,name=open_id_list,json=openIdList,proto3" json:"open_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetUidByOpenIdsReq) Reset()         { *m = BatchGetUidByOpenIdsReq{} }
func (m *BatchGetUidByOpenIdsReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetUidByOpenIdsReq) ProtoMessage()    {}
func (*BatchGetUidByOpenIdsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ext_game_f146a90a6a2035cb, []int{23}
}
func (m *BatchGetUidByOpenIdsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUidByOpenIdsReq.Unmarshal(m, b)
}
func (m *BatchGetUidByOpenIdsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUidByOpenIdsReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetUidByOpenIdsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUidByOpenIdsReq.Merge(dst, src)
}
func (m *BatchGetUidByOpenIdsReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetUidByOpenIdsReq.Size(m)
}
func (m *BatchGetUidByOpenIdsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUidByOpenIdsReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUidByOpenIdsReq proto.InternalMessageInfo

func (m *BatchGetUidByOpenIdsReq) GetOpenIdList() []string {
	if m != nil {
		return m.OpenIdList
	}
	return nil
}

type BatchGetUidByOpenIdsResp struct {
	UidMap               map[string]uint32 `protobuf:"bytes,1,rep,name=uid_map,json=uidMap,proto3" json:"uid_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *BatchGetUidByOpenIdsResp) Reset()         { *m = BatchGetUidByOpenIdsResp{} }
func (m *BatchGetUidByOpenIdsResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetUidByOpenIdsResp) ProtoMessage()    {}
func (*BatchGetUidByOpenIdsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ext_game_f146a90a6a2035cb, []int{24}
}
func (m *BatchGetUidByOpenIdsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUidByOpenIdsResp.Unmarshal(m, b)
}
func (m *BatchGetUidByOpenIdsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUidByOpenIdsResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetUidByOpenIdsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUidByOpenIdsResp.Merge(dst, src)
}
func (m *BatchGetUidByOpenIdsResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetUidByOpenIdsResp.Size(m)
}
func (m *BatchGetUidByOpenIdsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUidByOpenIdsResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUidByOpenIdsResp proto.InternalMessageInfo

func (m *BatchGetUidByOpenIdsResp) GetUidMap() map[string]uint32 {
	if m != nil {
		return m.UidMap
	}
	return nil
}

type SendPlatformMsgReq struct {
	AppId                string   `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	OpenIdList           []string `protobuf:"bytes,2,rep,name=open_id_list,json=openIdList,proto3" json:"open_id_list,omitempty"`
	TemplateId           uint32   `protobuf:"varint,3,opt,name=template_id,json=templateId,proto3" json:"template_id,omitempty"`
	SendType             uint32   `protobuf:"varint,4,opt,name=send_type,json=sendType,proto3" json:"send_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendPlatformMsgReq) Reset()         { *m = SendPlatformMsgReq{} }
func (m *SendPlatformMsgReq) String() string { return proto.CompactTextString(m) }
func (*SendPlatformMsgReq) ProtoMessage()    {}
func (*SendPlatformMsgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ext_game_f146a90a6a2035cb, []int{25}
}
func (m *SendPlatformMsgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendPlatformMsgReq.Unmarshal(m, b)
}
func (m *SendPlatformMsgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendPlatformMsgReq.Marshal(b, m, deterministic)
}
func (dst *SendPlatformMsgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendPlatformMsgReq.Merge(dst, src)
}
func (m *SendPlatformMsgReq) XXX_Size() int {
	return xxx_messageInfo_SendPlatformMsgReq.Size(m)
}
func (m *SendPlatformMsgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SendPlatformMsgReq.DiscardUnknown(m)
}

var xxx_messageInfo_SendPlatformMsgReq proto.InternalMessageInfo

func (m *SendPlatformMsgReq) GetAppId() string {
	if m != nil {
		return m.AppId
	}
	return ""
}

func (m *SendPlatformMsgReq) GetOpenIdList() []string {
	if m != nil {
		return m.OpenIdList
	}
	return nil
}

func (m *SendPlatformMsgReq) GetTemplateId() uint32 {
	if m != nil {
		return m.TemplateId
	}
	return 0
}

func (m *SendPlatformMsgReq) GetSendType() uint32 {
	if m != nil {
		return m.SendType
	}
	return 0
}

type SendPlatformMsgResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendPlatformMsgResp) Reset()         { *m = SendPlatformMsgResp{} }
func (m *SendPlatformMsgResp) String() string { return proto.CompactTextString(m) }
func (*SendPlatformMsgResp) ProtoMessage()    {}
func (*SendPlatformMsgResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ext_game_f146a90a6a2035cb, []int{26}
}
func (m *SendPlatformMsgResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendPlatformMsgResp.Unmarshal(m, b)
}
func (m *SendPlatformMsgResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendPlatformMsgResp.Marshal(b, m, deterministic)
}
func (dst *SendPlatformMsgResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendPlatformMsgResp.Merge(dst, src)
}
func (m *SendPlatformMsgResp) XXX_Size() int {
	return xxx_messageInfo_SendPlatformMsgResp.Size(m)
}
func (m *SendPlatformMsgResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SendPlatformMsgResp.DiscardUnknown(m)
}

var xxx_messageInfo_SendPlatformMsgResp proto.InternalMessageInfo

type RemoveWhiteListReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RemoveWhiteListReq) Reset()         { *m = RemoveWhiteListReq{} }
func (m *RemoveWhiteListReq) String() string { return proto.CompactTextString(m) }
func (*RemoveWhiteListReq) ProtoMessage()    {}
func (*RemoveWhiteListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ext_game_f146a90a6a2035cb, []int{27}
}
func (m *RemoveWhiteListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RemoveWhiteListReq.Unmarshal(m, b)
}
func (m *RemoveWhiteListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RemoveWhiteListReq.Marshal(b, m, deterministic)
}
func (dst *RemoveWhiteListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RemoveWhiteListReq.Merge(dst, src)
}
func (m *RemoveWhiteListReq) XXX_Size() int {
	return xxx_messageInfo_RemoveWhiteListReq.Size(m)
}
func (m *RemoveWhiteListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RemoveWhiteListReq.DiscardUnknown(m)
}

var xxx_messageInfo_RemoveWhiteListReq proto.InternalMessageInfo

func (m *RemoveWhiteListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *RemoveWhiteListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type RemoveWhiteListResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RemoveWhiteListResp) Reset()         { *m = RemoveWhiteListResp{} }
func (m *RemoveWhiteListResp) String() string { return proto.CompactTextString(m) }
func (*RemoveWhiteListResp) ProtoMessage()    {}
func (*RemoveWhiteListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ext_game_f146a90a6a2035cb, []int{28}
}
func (m *RemoveWhiteListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RemoveWhiteListResp.Unmarshal(m, b)
}
func (m *RemoveWhiteListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RemoveWhiteListResp.Marshal(b, m, deterministic)
}
func (dst *RemoveWhiteListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RemoveWhiteListResp.Merge(dst, src)
}
func (m *RemoveWhiteListResp) XXX_Size() int {
	return xxx_messageInfo_RemoveWhiteListResp.Size(m)
}
func (m *RemoveWhiteListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RemoveWhiteListResp.DiscardUnknown(m)
}

var xxx_messageInfo_RemoveWhiteListResp proto.InternalMessageInfo

type AddWhiteListReq struct {
	CidList              []uint32 `protobuf:"varint,1,rep,packed,name=cid_list,json=cidList,proto3" json:"cid_list,omitempty"`
	UidList              []uint32 `protobuf:"varint,2,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddWhiteListReq) Reset()         { *m = AddWhiteListReq{} }
func (m *AddWhiteListReq) String() string { return proto.CompactTextString(m) }
func (*AddWhiteListReq) ProtoMessage()    {}
func (*AddWhiteListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ext_game_f146a90a6a2035cb, []int{29}
}
func (m *AddWhiteListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddWhiteListReq.Unmarshal(m, b)
}
func (m *AddWhiteListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddWhiteListReq.Marshal(b, m, deterministic)
}
func (dst *AddWhiteListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddWhiteListReq.Merge(dst, src)
}
func (m *AddWhiteListReq) XXX_Size() int {
	return xxx_messageInfo_AddWhiteListReq.Size(m)
}
func (m *AddWhiteListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddWhiteListReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddWhiteListReq proto.InternalMessageInfo

func (m *AddWhiteListReq) GetCidList() []uint32 {
	if m != nil {
		return m.CidList
	}
	return nil
}

func (m *AddWhiteListReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type AddWhiteListResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddWhiteListResp) Reset()         { *m = AddWhiteListResp{} }
func (m *AddWhiteListResp) String() string { return proto.CompactTextString(m) }
func (*AddWhiteListResp) ProtoMessage()    {}
func (*AddWhiteListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ext_game_f146a90a6a2035cb, []int{30}
}
func (m *AddWhiteListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddWhiteListResp.Unmarshal(m, b)
}
func (m *AddWhiteListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddWhiteListResp.Marshal(b, m, deterministic)
}
func (dst *AddWhiteListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddWhiteListResp.Merge(dst, src)
}
func (m *AddWhiteListResp) XXX_Size() int {
	return xxx_messageInfo_AddWhiteListResp.Size(m)
}
func (m *AddWhiteListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddWhiteListResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddWhiteListResp proto.InternalMessageInfo

type CheckWhiteListReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckWhiteListReq) Reset()         { *m = CheckWhiteListReq{} }
func (m *CheckWhiteListReq) String() string { return proto.CompactTextString(m) }
func (*CheckWhiteListReq) ProtoMessage()    {}
func (*CheckWhiteListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ext_game_f146a90a6a2035cb, []int{31}
}
func (m *CheckWhiteListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckWhiteListReq.Unmarshal(m, b)
}
func (m *CheckWhiteListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckWhiteListReq.Marshal(b, m, deterministic)
}
func (dst *CheckWhiteListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckWhiteListReq.Merge(dst, src)
}
func (m *CheckWhiteListReq) XXX_Size() int {
	return xxx_messageInfo_CheckWhiteListReq.Size(m)
}
func (m *CheckWhiteListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckWhiteListReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckWhiteListReq proto.InternalMessageInfo

func (m *CheckWhiteListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *CheckWhiteListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type CheckWhiteListResp struct {
	CidWhite             bool     `protobuf:"varint,1,opt,name=cid_white,json=cidWhite,proto3" json:"cid_white,omitempty"`
	UidWhite             bool     `protobuf:"varint,2,opt,name=uid_white,json=uidWhite,proto3" json:"uid_white,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckWhiteListResp) Reset()         { *m = CheckWhiteListResp{} }
func (m *CheckWhiteListResp) String() string { return proto.CompactTextString(m) }
func (*CheckWhiteListResp) ProtoMessage()    {}
func (*CheckWhiteListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ext_game_f146a90a6a2035cb, []int{32}
}
func (m *CheckWhiteListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckWhiteListResp.Unmarshal(m, b)
}
func (m *CheckWhiteListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckWhiteListResp.Marshal(b, m, deterministic)
}
func (dst *CheckWhiteListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckWhiteListResp.Merge(dst, src)
}
func (m *CheckWhiteListResp) XXX_Size() int {
	return xxx_messageInfo_CheckWhiteListResp.Size(m)
}
func (m *CheckWhiteListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckWhiteListResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckWhiteListResp proto.InternalMessageInfo

func (m *CheckWhiteListResp) GetCidWhite() bool {
	if m != nil {
		return m.CidWhite
	}
	return false
}

func (m *CheckWhiteListResp) GetUidWhite() bool {
	if m != nil {
		return m.UidWhite
	}
	return false
}

type TestChannelCommonHighLightImReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Cid                  uint32   `protobuf:"varint,2,opt,name=cid,proto3" json:"cid,omitempty"`
	Content              string   `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"`
	HighLightContent     string   `protobuf:"bytes,4,opt,name=high_light_content,json=highLightContent,proto3" json:"high_light_content,omitempty"`
	JumpUrl              string   `protobuf:"bytes,5,opt,name=jump_url,json=jumpUrl,proto3" json:"jump_url,omitempty"`
	FontColor            string   `protobuf:"bytes,6,opt,name=font_color,json=fontColor,proto3" json:"font_color,omitempty"`
	BorderColor          string   `protobuf:"bytes,7,opt,name=border_color,json=borderColor,proto3" json:"border_color,omitempty"`
	HighLightColor       string   `protobuf:"bytes,8,opt,name=high_light_color,json=highLightColor,proto3" json:"high_light_color,omitempty"`
	BackgroundColor      string   `protobuf:"bytes,9,opt,name=background_color,json=backgroundColor,proto3" json:"background_color,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TestChannelCommonHighLightImReq) Reset()         { *m = TestChannelCommonHighLightImReq{} }
func (m *TestChannelCommonHighLightImReq) String() string { return proto.CompactTextString(m) }
func (*TestChannelCommonHighLightImReq) ProtoMessage()    {}
func (*TestChannelCommonHighLightImReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ext_game_f146a90a6a2035cb, []int{33}
}
func (m *TestChannelCommonHighLightImReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TestChannelCommonHighLightImReq.Unmarshal(m, b)
}
func (m *TestChannelCommonHighLightImReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TestChannelCommonHighLightImReq.Marshal(b, m, deterministic)
}
func (dst *TestChannelCommonHighLightImReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TestChannelCommonHighLightImReq.Merge(dst, src)
}
func (m *TestChannelCommonHighLightImReq) XXX_Size() int {
	return xxx_messageInfo_TestChannelCommonHighLightImReq.Size(m)
}
func (m *TestChannelCommonHighLightImReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TestChannelCommonHighLightImReq.DiscardUnknown(m)
}

var xxx_messageInfo_TestChannelCommonHighLightImReq proto.InternalMessageInfo

func (m *TestChannelCommonHighLightImReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *TestChannelCommonHighLightImReq) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *TestChannelCommonHighLightImReq) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *TestChannelCommonHighLightImReq) GetHighLightContent() string {
	if m != nil {
		return m.HighLightContent
	}
	return ""
}

func (m *TestChannelCommonHighLightImReq) GetJumpUrl() string {
	if m != nil {
		return m.JumpUrl
	}
	return ""
}

func (m *TestChannelCommonHighLightImReq) GetFontColor() string {
	if m != nil {
		return m.FontColor
	}
	return ""
}

func (m *TestChannelCommonHighLightImReq) GetBorderColor() string {
	if m != nil {
		return m.BorderColor
	}
	return ""
}

func (m *TestChannelCommonHighLightImReq) GetHighLightColor() string {
	if m != nil {
		return m.HighLightColor
	}
	return ""
}

func (m *TestChannelCommonHighLightImReq) GetBackgroundColor() string {
	if m != nil {
		return m.BackgroundColor
	}
	return ""
}

type TestChannelCommonHighLightImResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TestChannelCommonHighLightImResp) Reset()         { *m = TestChannelCommonHighLightImResp{} }
func (m *TestChannelCommonHighLightImResp) String() string { return proto.CompactTextString(m) }
func (*TestChannelCommonHighLightImResp) ProtoMessage()    {}
func (*TestChannelCommonHighLightImResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ext_game_f146a90a6a2035cb, []int{34}
}
func (m *TestChannelCommonHighLightImResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TestChannelCommonHighLightImResp.Unmarshal(m, b)
}
func (m *TestChannelCommonHighLightImResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TestChannelCommonHighLightImResp.Marshal(b, m, deterministic)
}
func (dst *TestChannelCommonHighLightImResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TestChannelCommonHighLightImResp.Merge(dst, src)
}
func (m *TestChannelCommonHighLightImResp) XXX_Size() int {
	return xxx_messageInfo_TestChannelCommonHighLightImResp.Size(m)
}
func (m *TestChannelCommonHighLightImResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TestChannelCommonHighLightImResp.DiscardUnknown(m)
}

var xxx_messageInfo_TestChannelCommonHighLightImResp proto.InternalMessageInfo

type DailyConsumeTotalCntPushReq struct {
	BeginTs              int64    `protobuf:"varint,1,opt,name=begin_ts,json=beginTs,proto3" json:"begin_ts,omitempty"`
	EndTs                int64    `protobuf:"varint,2,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	PushLark             bool     `protobuf:"varint,3,opt,name=push_lark,json=pushLark,proto3" json:"push_lark,omitempty"` // Deprecated: Do not use.
	Title                string   `protobuf:"bytes,4,opt,name=title,proto3" json:"title,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DailyConsumeTotalCntPushReq) Reset()         { *m = DailyConsumeTotalCntPushReq{} }
func (m *DailyConsumeTotalCntPushReq) String() string { return proto.CompactTextString(m) }
func (*DailyConsumeTotalCntPushReq) ProtoMessage()    {}
func (*DailyConsumeTotalCntPushReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ext_game_f146a90a6a2035cb, []int{35}
}
func (m *DailyConsumeTotalCntPushReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DailyConsumeTotalCntPushReq.Unmarshal(m, b)
}
func (m *DailyConsumeTotalCntPushReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DailyConsumeTotalCntPushReq.Marshal(b, m, deterministic)
}
func (dst *DailyConsumeTotalCntPushReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DailyConsumeTotalCntPushReq.Merge(dst, src)
}
func (m *DailyConsumeTotalCntPushReq) XXX_Size() int {
	return xxx_messageInfo_DailyConsumeTotalCntPushReq.Size(m)
}
func (m *DailyConsumeTotalCntPushReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DailyConsumeTotalCntPushReq.DiscardUnknown(m)
}

var xxx_messageInfo_DailyConsumeTotalCntPushReq proto.InternalMessageInfo

func (m *DailyConsumeTotalCntPushReq) GetBeginTs() int64 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *DailyConsumeTotalCntPushReq) GetEndTs() int64 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

// Deprecated: Do not use.
func (m *DailyConsumeTotalCntPushReq) GetPushLark() bool {
	if m != nil {
		return m.PushLark
	}
	return false
}

func (m *DailyConsumeTotalCntPushReq) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

type DailyConsumeTotalCntPushResp struct {
	TotalCnt             uint32   `protobuf:"varint,1,opt,name=total_cnt,json=totalCnt,proto3" json:"total_cnt,omitempty"`
	Value                uint32   `protobuf:"varint,2,opt,name=value,proto3" json:"value,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DailyConsumeTotalCntPushResp) Reset()         { *m = DailyConsumeTotalCntPushResp{} }
func (m *DailyConsumeTotalCntPushResp) String() string { return proto.CompactTextString(m) }
func (*DailyConsumeTotalCntPushResp) ProtoMessage()    {}
func (*DailyConsumeTotalCntPushResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ext_game_f146a90a6a2035cb, []int{36}
}
func (m *DailyConsumeTotalCntPushResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DailyConsumeTotalCntPushResp.Unmarshal(m, b)
}
func (m *DailyConsumeTotalCntPushResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DailyConsumeTotalCntPushResp.Marshal(b, m, deterministic)
}
func (dst *DailyConsumeTotalCntPushResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DailyConsumeTotalCntPushResp.Merge(dst, src)
}
func (m *DailyConsumeTotalCntPushResp) XXX_Size() int {
	return xxx_messageInfo_DailyConsumeTotalCntPushResp.Size(m)
}
func (m *DailyConsumeTotalCntPushResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DailyConsumeTotalCntPushResp.DiscardUnknown(m)
}

var xxx_messageInfo_DailyConsumeTotalCntPushResp proto.InternalMessageInfo

func (m *DailyConsumeTotalCntPushResp) GetTotalCnt() uint32 {
	if m != nil {
		return m.TotalCnt
	}
	return 0
}

func (m *DailyConsumeTotalCntPushResp) GetValue() uint32 {
	if m != nil {
		return m.Value
	}
	return 0
}

// 只查询，不创建openId
type SimpleQueryOpenidReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Ttid                 string   `protobuf:"bytes,2,opt,name=ttid,proto3" json:"ttid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SimpleQueryOpenidReq) Reset()         { *m = SimpleQueryOpenidReq{} }
func (m *SimpleQueryOpenidReq) String() string { return proto.CompactTextString(m) }
func (*SimpleQueryOpenidReq) ProtoMessage()    {}
func (*SimpleQueryOpenidReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ext_game_f146a90a6a2035cb, []int{37}
}
func (m *SimpleQueryOpenidReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SimpleQueryOpenidReq.Unmarshal(m, b)
}
func (m *SimpleQueryOpenidReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SimpleQueryOpenidReq.Marshal(b, m, deterministic)
}
func (dst *SimpleQueryOpenidReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SimpleQueryOpenidReq.Merge(dst, src)
}
func (m *SimpleQueryOpenidReq) XXX_Size() int {
	return xxx_messageInfo_SimpleQueryOpenidReq.Size(m)
}
func (m *SimpleQueryOpenidReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SimpleQueryOpenidReq.DiscardUnknown(m)
}

var xxx_messageInfo_SimpleQueryOpenidReq proto.InternalMessageInfo

func (m *SimpleQueryOpenidReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SimpleQueryOpenidReq) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

type SimpleQueryOpenidResp struct {
	Openid               string   `protobuf:"bytes,1,opt,name=openid,proto3" json:"openid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SimpleQueryOpenidResp) Reset()         { *m = SimpleQueryOpenidResp{} }
func (m *SimpleQueryOpenidResp) String() string { return proto.CompactTextString(m) }
func (*SimpleQueryOpenidResp) ProtoMessage()    {}
func (*SimpleQueryOpenidResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ext_game_f146a90a6a2035cb, []int{38}
}
func (m *SimpleQueryOpenidResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SimpleQueryOpenidResp.Unmarshal(m, b)
}
func (m *SimpleQueryOpenidResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SimpleQueryOpenidResp.Marshal(b, m, deterministic)
}
func (dst *SimpleQueryOpenidResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SimpleQueryOpenidResp.Merge(dst, src)
}
func (m *SimpleQueryOpenidResp) XXX_Size() int {
	return xxx_messageInfo_SimpleQueryOpenidResp.Size(m)
}
func (m *SimpleQueryOpenidResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SimpleQueryOpenidResp.DiscardUnknown(m)
}

var xxx_messageInfo_SimpleQueryOpenidResp proto.InternalMessageInfo

func (m *SimpleQueryOpenidResp) GetOpenid() string {
	if m != nil {
		return m.Openid
	}
	return ""
}

func init() {
	proto.RegisterType((*GetUserExtGameJsCodeReq)(nil), "channel_ext_game.GetUserExtGameJsCodeReq")
	proto.RegisterType((*GetUserExtGameJsCodeResp)(nil), "channel_ext_game.GetUserExtGameJsCodeResp")
	proto.RegisterType((*GetUserExtGameOpenidReq)(nil), "channel_ext_game.GetUserExtGameOpenidReq")
	proto.RegisterType((*GetUserExtGameOpenidResp)(nil), "channel_ext_game.GetUserExtGameOpenidResp")
	proto.RegisterType((*ExtGameConsumeReq)(nil), "channel_ext_game.ExtGameConsumeReq")
	proto.RegisterType((*ExtGameConsumeResp)(nil), "channel_ext_game.ExtGameConsumeResp")
	proto.RegisterType((*ExtGameAwardReq)(nil), "channel_ext_game.ExtGameAwardReq")
	proto.RegisterType((*ExtGameAwardResp)(nil), "channel_ext_game.ExtGameAwardResp")
	proto.RegisterType((*CancelUserExtGameJsCodeReq)(nil), "channel_ext_game.CancelUserExtGameJsCodeReq")
	proto.RegisterType((*CancelUserExtGameJsCodeResp)(nil), "channel_ext_game.CancelUserExtGameJsCodeResp")
	proto.RegisterType((*GetUidByOpenidReq)(nil), "channel_ext_game.GetUidByOpenidReq")
	proto.RegisterType((*GetUidByOpenidResp)(nil), "channel_ext_game.GetUidByOpenidResp")
	proto.RegisterType((*CheckUserGameAccessReq)(nil), "channel_ext_game.CheckUserGameAccessReq")
	proto.RegisterType((*CheckUserGameAccessResp)(nil), "channel_ext_game.CheckUserGameAccessResp")
	proto.RegisterType((*CheckChannelBlackListReq)(nil), "channel_ext_game.CheckChannelBlackListReq")
	proto.RegisterType((*CheckChannelBlackListResp)(nil), "channel_ext_game.CheckChannelBlackListResp")
	proto.RegisterType((*GetWhiteChannelRandomlyReq)(nil), "channel_ext_game.GetWhiteChannelRandomlyReq")
	proto.RegisterType((*GetWhiteChannelRandomlyResp)(nil), "channel_ext_game.GetWhiteChannelRandomlyResp")
	proto.RegisterType((*GetAuthInfoByJsCodeReq)(nil), "channel_ext_game.GetAuthInfoByJsCodeReq")
	proto.RegisterType((*GetAuthInfoByJsCodeResp)(nil), "channel_ext_game.GetAuthInfoByJsCodeResp")
	proto.RegisterType((*GetExtGameInfoListReq)(nil), "channel_ext_game.GetExtGameInfoListReq")
	proto.RegisterType((*GetExtGameInfoListResp)(nil), "channel_ext_game.GetExtGameInfoListResp")
	proto.RegisterType((*ExtGameInfo)(nil), "channel_ext_game.ExtGameInfo")
	proto.RegisterType((*BatchGetUidByOpenIdsReq)(nil), "channel_ext_game.BatchGetUidByOpenIdsReq")
	proto.RegisterType((*BatchGetUidByOpenIdsResp)(nil), "channel_ext_game.BatchGetUidByOpenIdsResp")
	proto.RegisterMapType((map[string]uint32)(nil), "channel_ext_game.BatchGetUidByOpenIdsResp.UidMapEntry")
	proto.RegisterType((*SendPlatformMsgReq)(nil), "channel_ext_game.SendPlatformMsgReq")
	proto.RegisterType((*SendPlatformMsgResp)(nil), "channel_ext_game.SendPlatformMsgResp")
	proto.RegisterType((*RemoveWhiteListReq)(nil), "channel_ext_game.RemoveWhiteListReq")
	proto.RegisterType((*RemoveWhiteListResp)(nil), "channel_ext_game.RemoveWhiteListResp")
	proto.RegisterType((*AddWhiteListReq)(nil), "channel_ext_game.AddWhiteListReq")
	proto.RegisterType((*AddWhiteListResp)(nil), "channel_ext_game.AddWhiteListResp")
	proto.RegisterType((*CheckWhiteListReq)(nil), "channel_ext_game.CheckWhiteListReq")
	proto.RegisterType((*CheckWhiteListResp)(nil), "channel_ext_game.CheckWhiteListResp")
	proto.RegisterType((*TestChannelCommonHighLightImReq)(nil), "channel_ext_game.TestChannelCommonHighLightImReq")
	proto.RegisterType((*TestChannelCommonHighLightImResp)(nil), "channel_ext_game.TestChannelCommonHighLightImResp")
	proto.RegisterType((*DailyConsumeTotalCntPushReq)(nil), "channel_ext_game.DailyConsumeTotalCntPushReq")
	proto.RegisterType((*DailyConsumeTotalCntPushResp)(nil), "channel_ext_game.DailyConsumeTotalCntPushResp")
	proto.RegisterType((*SimpleQueryOpenidReq)(nil), "channel_ext_game.SimpleQueryOpenidReq")
	proto.RegisterType((*SimpleQueryOpenidResp)(nil), "channel_ext_game.SimpleQueryOpenidResp")
	proto.RegisterEnum("channel_ext_game.ExtGameAwardReq_RewardType", ExtGameAwardReq_RewardType_name, ExtGameAwardReq_RewardType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ChannelExtGameClient is the client API for ChannelExtGame service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChannelExtGameClient interface {
	// 获取用户登陆jsCode
	GetUserExtGameJsCode(ctx context.Context, in *GetUserExtGameJsCodeReq, opts ...grpc.CallOption) (*GetUserExtGameJsCodeResp, error)
	// 注销jsCode
	CancelUserExtGameJsCode(ctx context.Context, in *CancelUserExtGameJsCodeReq, opts ...grpc.CallOption) (*CancelUserExtGameJsCodeResp, error)
	// 获取用户openid
	GetUserExtGameOpenid(ctx context.Context, in *GetUserExtGameOpenidReq, opts ...grpc.CallOption) (*GetUserExtGameOpenidResp, error)
	// simple query openId
	SimpleQueryOpenid(ctx context.Context, in *SimpleQueryOpenidReq, opts ...grpc.CallOption) (*SimpleQueryOpenidResp, error)
	// 第三方游戏消费货币
	ExtGameConsume(ctx context.Context, in *ExtGameConsumeReq, opts ...grpc.CallOption) (*ExtGameConsumeResp, error)
	// 第三方游戏发放奖励
	ExtGameAward(ctx context.Context, in *ExtGameAwardReq, opts ...grpc.CallOption) (*ExtGameAwardResp, error)
	// 根据openid获取uid
	GetUidByOpenid(ctx context.Context, in *GetUidByOpenidReq, opts ...grpc.CallOption) (*GetUidByOpenidResp, error)
	// 批量获取uid
	BatchGetUidByOpenIds(ctx context.Context, in *BatchGetUidByOpenIdsReq, opts ...grpc.CallOption) (*BatchGetUidByOpenIdsResp, error)
	// 判断用户游戏入口权限
	CheckUserGameAccess(ctx context.Context, in *CheckUserGameAccessReq, opts ...grpc.CallOption) (*CheckUserGameAccessResp, error)
	// 判断房间是否黑名单房间
	CheckChannelBlackList(ctx context.Context, in *CheckChannelBlackListReq, opts ...grpc.CallOption) (*CheckChannelBlackListResp, error)
	// 随机获取一个白名单房间
	GetWhiteChannelRandomly(ctx context.Context, in *GetWhiteChannelRandomlyReq, opts ...grpc.CallOption) (*GetWhiteChannelRandomlyResp, error)
	// 通过JsCode获取信息
	GetAuthInfoByJsCode(ctx context.Context, in *GetAuthInfoByJsCodeReq, opts ...grpc.CallOption) (*GetAuthInfoByJsCodeResp, error)
	// 获取游戏信息列表
	GetExtGameInfoList(ctx context.Context, in *GetExtGameInfoListReq, opts ...grpc.CallOption) (*GetExtGameInfoListResp, error)
	// 发放平台系统消息、房间公屏
	SendPlatformMsg(ctx context.Context, in *SendPlatformMsgReq, opts ...grpc.CallOption) (*SendPlatformMsgResp, error)
	// 移除房间白名单
	RemoveWhiteList(ctx context.Context, in *RemoveWhiteListReq, opts ...grpc.CallOption) (*RemoveWhiteListResp, error)
	// 批量新增房间白名单
	AddWhiteList(ctx context.Context, in *AddWhiteListReq, opts ...grpc.CallOption) (*AddWhiteListResp, error)
	// 检查房间是否在白名单中
	CheckWhiteList(ctx context.Context, in *CheckWhiteListReq, opts ...grpc.CallOption) (*CheckWhiteListResp, error)
	// TestChannelCommonHighLightIm
	TestChannelCommonHighLightIm(ctx context.Context, in *TestChannelCommonHighLightImReq, opts ...grpc.CallOption) (*TestChannelCommonHighLightImResp, error)
	// DailyConsumeTotalCntPush
	DailyConsumeTotalCntPush(ctx context.Context, in *DailyConsumeTotalCntPushReq, opts ...grpc.CallOption) (*DailyConsumeTotalCntPushResp, error)
	// T豆消费数据对账
	GetConsumeTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error)
	GetConsumeOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error)
	// 发放包裹数据对账
	GetAwardTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error)
	GetAwardOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error)
}

type channelExtGameClient struct {
	cc *grpc.ClientConn
}

func NewChannelExtGameClient(cc *grpc.ClientConn) ChannelExtGameClient {
	return &channelExtGameClient{cc}
}

func (c *channelExtGameClient) GetUserExtGameJsCode(ctx context.Context, in *GetUserExtGameJsCodeReq, opts ...grpc.CallOption) (*GetUserExtGameJsCodeResp, error) {
	out := new(GetUserExtGameJsCodeResp)
	err := c.cc.Invoke(ctx, "/channel_ext_game.ChannelExtGame/GetUserExtGameJsCode", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelExtGameClient) CancelUserExtGameJsCode(ctx context.Context, in *CancelUserExtGameJsCodeReq, opts ...grpc.CallOption) (*CancelUserExtGameJsCodeResp, error) {
	out := new(CancelUserExtGameJsCodeResp)
	err := c.cc.Invoke(ctx, "/channel_ext_game.ChannelExtGame/CancelUserExtGameJsCode", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelExtGameClient) GetUserExtGameOpenid(ctx context.Context, in *GetUserExtGameOpenidReq, opts ...grpc.CallOption) (*GetUserExtGameOpenidResp, error) {
	out := new(GetUserExtGameOpenidResp)
	err := c.cc.Invoke(ctx, "/channel_ext_game.ChannelExtGame/GetUserExtGameOpenid", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelExtGameClient) SimpleQueryOpenid(ctx context.Context, in *SimpleQueryOpenidReq, opts ...grpc.CallOption) (*SimpleQueryOpenidResp, error) {
	out := new(SimpleQueryOpenidResp)
	err := c.cc.Invoke(ctx, "/channel_ext_game.ChannelExtGame/SimpleQueryOpenid", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelExtGameClient) ExtGameConsume(ctx context.Context, in *ExtGameConsumeReq, opts ...grpc.CallOption) (*ExtGameConsumeResp, error) {
	out := new(ExtGameConsumeResp)
	err := c.cc.Invoke(ctx, "/channel_ext_game.ChannelExtGame/ExtGameConsume", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelExtGameClient) ExtGameAward(ctx context.Context, in *ExtGameAwardReq, opts ...grpc.CallOption) (*ExtGameAwardResp, error) {
	out := new(ExtGameAwardResp)
	err := c.cc.Invoke(ctx, "/channel_ext_game.ChannelExtGame/ExtGameAward", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelExtGameClient) GetUidByOpenid(ctx context.Context, in *GetUidByOpenidReq, opts ...grpc.CallOption) (*GetUidByOpenidResp, error) {
	out := new(GetUidByOpenidResp)
	err := c.cc.Invoke(ctx, "/channel_ext_game.ChannelExtGame/GetUidByOpenid", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelExtGameClient) BatchGetUidByOpenIds(ctx context.Context, in *BatchGetUidByOpenIdsReq, opts ...grpc.CallOption) (*BatchGetUidByOpenIdsResp, error) {
	out := new(BatchGetUidByOpenIdsResp)
	err := c.cc.Invoke(ctx, "/channel_ext_game.ChannelExtGame/BatchGetUidByOpenIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelExtGameClient) CheckUserGameAccess(ctx context.Context, in *CheckUserGameAccessReq, opts ...grpc.CallOption) (*CheckUserGameAccessResp, error) {
	out := new(CheckUserGameAccessResp)
	err := c.cc.Invoke(ctx, "/channel_ext_game.ChannelExtGame/CheckUserGameAccess", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelExtGameClient) CheckChannelBlackList(ctx context.Context, in *CheckChannelBlackListReq, opts ...grpc.CallOption) (*CheckChannelBlackListResp, error) {
	out := new(CheckChannelBlackListResp)
	err := c.cc.Invoke(ctx, "/channel_ext_game.ChannelExtGame/CheckChannelBlackList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelExtGameClient) GetWhiteChannelRandomly(ctx context.Context, in *GetWhiteChannelRandomlyReq, opts ...grpc.CallOption) (*GetWhiteChannelRandomlyResp, error) {
	out := new(GetWhiteChannelRandomlyResp)
	err := c.cc.Invoke(ctx, "/channel_ext_game.ChannelExtGame/GetWhiteChannelRandomly", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelExtGameClient) GetAuthInfoByJsCode(ctx context.Context, in *GetAuthInfoByJsCodeReq, opts ...grpc.CallOption) (*GetAuthInfoByJsCodeResp, error) {
	out := new(GetAuthInfoByJsCodeResp)
	err := c.cc.Invoke(ctx, "/channel_ext_game.ChannelExtGame/GetAuthInfoByJsCode", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelExtGameClient) GetExtGameInfoList(ctx context.Context, in *GetExtGameInfoListReq, opts ...grpc.CallOption) (*GetExtGameInfoListResp, error) {
	out := new(GetExtGameInfoListResp)
	err := c.cc.Invoke(ctx, "/channel_ext_game.ChannelExtGame/GetExtGameInfoList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelExtGameClient) SendPlatformMsg(ctx context.Context, in *SendPlatformMsgReq, opts ...grpc.CallOption) (*SendPlatformMsgResp, error) {
	out := new(SendPlatformMsgResp)
	err := c.cc.Invoke(ctx, "/channel_ext_game.ChannelExtGame/SendPlatformMsg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelExtGameClient) RemoveWhiteList(ctx context.Context, in *RemoveWhiteListReq, opts ...grpc.CallOption) (*RemoveWhiteListResp, error) {
	out := new(RemoveWhiteListResp)
	err := c.cc.Invoke(ctx, "/channel_ext_game.ChannelExtGame/RemoveWhiteList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelExtGameClient) AddWhiteList(ctx context.Context, in *AddWhiteListReq, opts ...grpc.CallOption) (*AddWhiteListResp, error) {
	out := new(AddWhiteListResp)
	err := c.cc.Invoke(ctx, "/channel_ext_game.ChannelExtGame/AddWhiteList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelExtGameClient) CheckWhiteList(ctx context.Context, in *CheckWhiteListReq, opts ...grpc.CallOption) (*CheckWhiteListResp, error) {
	out := new(CheckWhiteListResp)
	err := c.cc.Invoke(ctx, "/channel_ext_game.ChannelExtGame/CheckWhiteList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelExtGameClient) TestChannelCommonHighLightIm(ctx context.Context, in *TestChannelCommonHighLightImReq, opts ...grpc.CallOption) (*TestChannelCommonHighLightImResp, error) {
	out := new(TestChannelCommonHighLightImResp)
	err := c.cc.Invoke(ctx, "/channel_ext_game.ChannelExtGame/TestChannelCommonHighLightIm", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelExtGameClient) DailyConsumeTotalCntPush(ctx context.Context, in *DailyConsumeTotalCntPushReq, opts ...grpc.CallOption) (*DailyConsumeTotalCntPushResp, error) {
	out := new(DailyConsumeTotalCntPushResp)
	err := c.cc.Invoke(ctx, "/channel_ext_game.ChannelExtGame/DailyConsumeTotalCntPush", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelExtGameClient) GetConsumeTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	out := new(reconcile_v2.CountResp)
	err := c.cc.Invoke(ctx, "/channel_ext_game.ChannelExtGame/GetConsumeTotalCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelExtGameClient) GetConsumeOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	out := new(reconcile_v2.OrderIdsResp)
	err := c.cc.Invoke(ctx, "/channel_ext_game.ChannelExtGame/GetConsumeOrderIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelExtGameClient) GetAwardTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	out := new(reconcile_v2.CountResp)
	err := c.cc.Invoke(ctx, "/channel_ext_game.ChannelExtGame/GetAwardTotalCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelExtGameClient) GetAwardOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	out := new(reconcile_v2.OrderIdsResp)
	err := c.cc.Invoke(ctx, "/channel_ext_game.ChannelExtGame/GetAwardOrderIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChannelExtGameServer is the server API for ChannelExtGame service.
type ChannelExtGameServer interface {
	// 获取用户登陆jsCode
	GetUserExtGameJsCode(context.Context, *GetUserExtGameJsCodeReq) (*GetUserExtGameJsCodeResp, error)
	// 注销jsCode
	CancelUserExtGameJsCode(context.Context, *CancelUserExtGameJsCodeReq) (*CancelUserExtGameJsCodeResp, error)
	// 获取用户openid
	GetUserExtGameOpenid(context.Context, *GetUserExtGameOpenidReq) (*GetUserExtGameOpenidResp, error)
	// simple query openId
	SimpleQueryOpenid(context.Context, *SimpleQueryOpenidReq) (*SimpleQueryOpenidResp, error)
	// 第三方游戏消费货币
	ExtGameConsume(context.Context, *ExtGameConsumeReq) (*ExtGameConsumeResp, error)
	// 第三方游戏发放奖励
	ExtGameAward(context.Context, *ExtGameAwardReq) (*ExtGameAwardResp, error)
	// 根据openid获取uid
	GetUidByOpenid(context.Context, *GetUidByOpenidReq) (*GetUidByOpenidResp, error)
	// 批量获取uid
	BatchGetUidByOpenIds(context.Context, *BatchGetUidByOpenIdsReq) (*BatchGetUidByOpenIdsResp, error)
	// 判断用户游戏入口权限
	CheckUserGameAccess(context.Context, *CheckUserGameAccessReq) (*CheckUserGameAccessResp, error)
	// 判断房间是否黑名单房间
	CheckChannelBlackList(context.Context, *CheckChannelBlackListReq) (*CheckChannelBlackListResp, error)
	// 随机获取一个白名单房间
	GetWhiteChannelRandomly(context.Context, *GetWhiteChannelRandomlyReq) (*GetWhiteChannelRandomlyResp, error)
	// 通过JsCode获取信息
	GetAuthInfoByJsCode(context.Context, *GetAuthInfoByJsCodeReq) (*GetAuthInfoByJsCodeResp, error)
	// 获取游戏信息列表
	GetExtGameInfoList(context.Context, *GetExtGameInfoListReq) (*GetExtGameInfoListResp, error)
	// 发放平台系统消息、房间公屏
	SendPlatformMsg(context.Context, *SendPlatformMsgReq) (*SendPlatformMsgResp, error)
	// 移除房间白名单
	RemoveWhiteList(context.Context, *RemoveWhiteListReq) (*RemoveWhiteListResp, error)
	// 批量新增房间白名单
	AddWhiteList(context.Context, *AddWhiteListReq) (*AddWhiteListResp, error)
	// 检查房间是否在白名单中
	CheckWhiteList(context.Context, *CheckWhiteListReq) (*CheckWhiteListResp, error)
	// TestChannelCommonHighLightIm
	TestChannelCommonHighLightIm(context.Context, *TestChannelCommonHighLightImReq) (*TestChannelCommonHighLightImResp, error)
	// DailyConsumeTotalCntPush
	DailyConsumeTotalCntPush(context.Context, *DailyConsumeTotalCntPushReq) (*DailyConsumeTotalCntPushResp, error)
	// T豆消费数据对账
	GetConsumeTotalCount(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error)
	GetConsumeOrderIds(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error)
	// 发放包裹数据对账
	GetAwardTotalCount(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error)
	GetAwardOrderIds(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error)
}

func RegisterChannelExtGameServer(s *grpc.Server, srv ChannelExtGameServer) {
	s.RegisterService(&_ChannelExtGame_serviceDesc, srv)
}

func _ChannelExtGame_GetUserExtGameJsCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserExtGameJsCodeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelExtGameServer).GetUserExtGameJsCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_ext_game.ChannelExtGame/GetUserExtGameJsCode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelExtGameServer).GetUserExtGameJsCode(ctx, req.(*GetUserExtGameJsCodeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelExtGame_CancelUserExtGameJsCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CancelUserExtGameJsCodeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelExtGameServer).CancelUserExtGameJsCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_ext_game.ChannelExtGame/CancelUserExtGameJsCode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelExtGameServer).CancelUserExtGameJsCode(ctx, req.(*CancelUserExtGameJsCodeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelExtGame_GetUserExtGameOpenid_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserExtGameOpenidReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelExtGameServer).GetUserExtGameOpenid(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_ext_game.ChannelExtGame/GetUserExtGameOpenid",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelExtGameServer).GetUserExtGameOpenid(ctx, req.(*GetUserExtGameOpenidReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelExtGame_SimpleQueryOpenid_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SimpleQueryOpenidReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelExtGameServer).SimpleQueryOpenid(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_ext_game.ChannelExtGame/SimpleQueryOpenid",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelExtGameServer).SimpleQueryOpenid(ctx, req.(*SimpleQueryOpenidReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelExtGame_ExtGameConsume_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExtGameConsumeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelExtGameServer).ExtGameConsume(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_ext_game.ChannelExtGame/ExtGameConsume",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelExtGameServer).ExtGameConsume(ctx, req.(*ExtGameConsumeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelExtGame_ExtGameAward_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExtGameAwardReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelExtGameServer).ExtGameAward(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_ext_game.ChannelExtGame/ExtGameAward",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelExtGameServer).ExtGameAward(ctx, req.(*ExtGameAwardReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelExtGame_GetUidByOpenid_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUidByOpenidReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelExtGameServer).GetUidByOpenid(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_ext_game.ChannelExtGame/GetUidByOpenid",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelExtGameServer).GetUidByOpenid(ctx, req.(*GetUidByOpenidReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelExtGame_BatchGetUidByOpenIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetUidByOpenIdsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelExtGameServer).BatchGetUidByOpenIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_ext_game.ChannelExtGame/BatchGetUidByOpenIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelExtGameServer).BatchGetUidByOpenIds(ctx, req.(*BatchGetUidByOpenIdsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelExtGame_CheckUserGameAccess_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckUserGameAccessReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelExtGameServer).CheckUserGameAccess(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_ext_game.ChannelExtGame/CheckUserGameAccess",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelExtGameServer).CheckUserGameAccess(ctx, req.(*CheckUserGameAccessReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelExtGame_CheckChannelBlackList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckChannelBlackListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelExtGameServer).CheckChannelBlackList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_ext_game.ChannelExtGame/CheckChannelBlackList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelExtGameServer).CheckChannelBlackList(ctx, req.(*CheckChannelBlackListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelExtGame_GetWhiteChannelRandomly_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWhiteChannelRandomlyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelExtGameServer).GetWhiteChannelRandomly(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_ext_game.ChannelExtGame/GetWhiteChannelRandomly",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelExtGameServer).GetWhiteChannelRandomly(ctx, req.(*GetWhiteChannelRandomlyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelExtGame_GetAuthInfoByJsCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAuthInfoByJsCodeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelExtGameServer).GetAuthInfoByJsCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_ext_game.ChannelExtGame/GetAuthInfoByJsCode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelExtGameServer).GetAuthInfoByJsCode(ctx, req.(*GetAuthInfoByJsCodeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelExtGame_GetExtGameInfoList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetExtGameInfoListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelExtGameServer).GetExtGameInfoList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_ext_game.ChannelExtGame/GetExtGameInfoList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelExtGameServer).GetExtGameInfoList(ctx, req.(*GetExtGameInfoListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelExtGame_SendPlatformMsg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendPlatformMsgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelExtGameServer).SendPlatformMsg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_ext_game.ChannelExtGame/SendPlatformMsg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelExtGameServer).SendPlatformMsg(ctx, req.(*SendPlatformMsgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelExtGame_RemoveWhiteList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveWhiteListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelExtGameServer).RemoveWhiteList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_ext_game.ChannelExtGame/RemoveWhiteList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelExtGameServer).RemoveWhiteList(ctx, req.(*RemoveWhiteListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelExtGame_AddWhiteList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddWhiteListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelExtGameServer).AddWhiteList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_ext_game.ChannelExtGame/AddWhiteList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelExtGameServer).AddWhiteList(ctx, req.(*AddWhiteListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelExtGame_CheckWhiteList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckWhiteListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelExtGameServer).CheckWhiteList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_ext_game.ChannelExtGame/CheckWhiteList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelExtGameServer).CheckWhiteList(ctx, req.(*CheckWhiteListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelExtGame_TestChannelCommonHighLightIm_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TestChannelCommonHighLightImReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelExtGameServer).TestChannelCommonHighLightIm(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_ext_game.ChannelExtGame/TestChannelCommonHighLightIm",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelExtGameServer).TestChannelCommonHighLightIm(ctx, req.(*TestChannelCommonHighLightImReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelExtGame_DailyConsumeTotalCntPush_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DailyConsumeTotalCntPushReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelExtGameServer).DailyConsumeTotalCntPush(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_ext_game.ChannelExtGame/DailyConsumeTotalCntPush",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelExtGameServer).DailyConsumeTotalCntPush(ctx, req.(*DailyConsumeTotalCntPushReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelExtGame_GetConsumeTotalCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelExtGameServer).GetConsumeTotalCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_ext_game.ChannelExtGame/GetConsumeTotalCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelExtGameServer).GetConsumeTotalCount(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelExtGame_GetConsumeOrderIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelExtGameServer).GetConsumeOrderIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_ext_game.ChannelExtGame/GetConsumeOrderIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelExtGameServer).GetConsumeOrderIds(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelExtGame_GetAwardTotalCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelExtGameServer).GetAwardTotalCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_ext_game.ChannelExtGame/GetAwardTotalCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelExtGameServer).GetAwardTotalCount(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelExtGame_GetAwardOrderIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelExtGameServer).GetAwardOrderIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_ext_game.ChannelExtGame/GetAwardOrderIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelExtGameServer).GetAwardOrderIds(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelExtGame_serviceDesc = grpc.ServiceDesc{
	ServiceName: "channel_ext_game.ChannelExtGame",
	HandlerType: (*ChannelExtGameServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetUserExtGameJsCode",
			Handler:    _ChannelExtGame_GetUserExtGameJsCode_Handler,
		},
		{
			MethodName: "CancelUserExtGameJsCode",
			Handler:    _ChannelExtGame_CancelUserExtGameJsCode_Handler,
		},
		{
			MethodName: "GetUserExtGameOpenid",
			Handler:    _ChannelExtGame_GetUserExtGameOpenid_Handler,
		},
		{
			MethodName: "SimpleQueryOpenid",
			Handler:    _ChannelExtGame_SimpleQueryOpenid_Handler,
		},
		{
			MethodName: "ExtGameConsume",
			Handler:    _ChannelExtGame_ExtGameConsume_Handler,
		},
		{
			MethodName: "ExtGameAward",
			Handler:    _ChannelExtGame_ExtGameAward_Handler,
		},
		{
			MethodName: "GetUidByOpenid",
			Handler:    _ChannelExtGame_GetUidByOpenid_Handler,
		},
		{
			MethodName: "BatchGetUidByOpenIds",
			Handler:    _ChannelExtGame_BatchGetUidByOpenIds_Handler,
		},
		{
			MethodName: "CheckUserGameAccess",
			Handler:    _ChannelExtGame_CheckUserGameAccess_Handler,
		},
		{
			MethodName: "CheckChannelBlackList",
			Handler:    _ChannelExtGame_CheckChannelBlackList_Handler,
		},
		{
			MethodName: "GetWhiteChannelRandomly",
			Handler:    _ChannelExtGame_GetWhiteChannelRandomly_Handler,
		},
		{
			MethodName: "GetAuthInfoByJsCode",
			Handler:    _ChannelExtGame_GetAuthInfoByJsCode_Handler,
		},
		{
			MethodName: "GetExtGameInfoList",
			Handler:    _ChannelExtGame_GetExtGameInfoList_Handler,
		},
		{
			MethodName: "SendPlatformMsg",
			Handler:    _ChannelExtGame_SendPlatformMsg_Handler,
		},
		{
			MethodName: "RemoveWhiteList",
			Handler:    _ChannelExtGame_RemoveWhiteList_Handler,
		},
		{
			MethodName: "AddWhiteList",
			Handler:    _ChannelExtGame_AddWhiteList_Handler,
		},
		{
			MethodName: "CheckWhiteList",
			Handler:    _ChannelExtGame_CheckWhiteList_Handler,
		},
		{
			MethodName: "TestChannelCommonHighLightIm",
			Handler:    _ChannelExtGame_TestChannelCommonHighLightIm_Handler,
		},
		{
			MethodName: "DailyConsumeTotalCntPush",
			Handler:    _ChannelExtGame_DailyConsumeTotalCntPush_Handler,
		},
		{
			MethodName: "GetConsumeTotalCount",
			Handler:    _ChannelExtGame_GetConsumeTotalCount_Handler,
		},
		{
			MethodName: "GetConsumeOrderIds",
			Handler:    _ChannelExtGame_GetConsumeOrderIds_Handler,
		},
		{
			MethodName: "GetAwardTotalCount",
			Handler:    _ChannelExtGame_GetAwardTotalCount_Handler,
		},
		{
			MethodName: "GetAwardOrderIds",
			Handler:    _ChannelExtGame_GetAwardOrderIds_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/channel-ext-game/channel-ext-game.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/channel-ext-game/channel-ext-game.proto", fileDescriptor_channel_ext_game_f146a90a6a2035cb)
}

var fileDescriptor_channel_ext_game_f146a90a6a2035cb = []byte{
	// 2053 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xa4, 0x59, 0xcd, 0x72, 0xdb, 0xc8,
	0x11, 0x36, 0x48, 0x4b, 0x24, 0x5b, 0x2b, 0x1b, 0x1e, 0x4b, 0x16, 0x45, 0x59, 0x65, 0x19, 0x49,
	0xbc, 0xf2, 0x7a, 0x4d, 0xc5, 0x4a, 0xe4, 0xca, 0x6e, 0x72, 0xa1, 0x48, 0x5a, 0xe6, 0xae, 0x2c,
	0x79, 0x21, 0x7a, 0x5d, 0x49, 0x2a, 0xc5, 0x40, 0xc0, 0x88, 0x84, 0x85, 0xbf, 0xc5, 0x0c, 0x64,
	0x71, 0x53, 0x39, 0xe4, 0x92, 0x54, 0x2a, 0xcf, 0x91, 0x1c, 0xf2, 0x02, 0x39, 0xe6, 0x05, 0xf2,
	0x0e, 0x39, 0xe4, 0x09, 0x72, 0xcd, 0x29, 0x35, 0x3f, 0x10, 0x41, 0x62, 0x20, 0xc9, 0xe5, 0x93,
	0x30, 0xdd, 0x3d, 0xfd, 0x37, 0xd3, 0xdf, 0x74, 0x8b, 0xf0, 0x9c, 0xd2, 0xad, 0xef, 0x12, 0xd7,
	0x3e, 0x25, 0xae, 0x77, 0x86, 0xe3, 0x2d, 0x7b, 0x64, 0x05, 0x01, 0xf6, 0x9e, 0xe2, 0x73, 0xfa,
	0x74, 0x68, 0xf9, 0x38, 0x47, 0x68, 0x46, 0x71, 0x48, 0x43, 0xa4, 0x4b, 0xfa, 0x00, 0x9f, 0xd3,
	0x01, 0xa3, 0x37, 0x9a, 0x33, 0x9a, 0xf0, 0x39, 0xc5, 0x01, 0x71, 0xc3, 0x60, 0x2b, 0x8c, 0xa8,
	0x1b, 0x06, 0x24, 0xfd, 0x2b, 0x34, 0xe4, 0xe4, 0x63, 0x6c, 0x87, 0x81, 0xed, 0x7a, 0xf8, 0xe9,
	0xd9, 0xf6, 0xd4, 0x42, 0xc8, 0x1b, 0x7f, 0xd7, 0x60, 0x65, 0x0f, 0xd3, 0x37, 0x04, 0xc7, 0xdd,
	0x73, 0xba, 0x67, 0xf9, 0xf8, 0x2b, 0xd2, 0x0e, 0x1d, 0x6c, 0xe2, 0xef, 0x90, 0x0e, 0xe5, 0xc4,
	0x75, 0xea, 0xda, 0x86, 0xb6, 0xb9, 0x68, 0xb2, 0x4f, 0xf4, 0x10, 0x20, 0xf5, 0xd0, 0x75, 0xea,
	0x25, 0xc6, 0xd8, 0x2d, 0xd5, 0x35, 0xb3, 0x26, 0xa9, 0x3d, 0x07, 0xad, 0x41, 0x85, 0x39, 0xce,
	0xf8, 0xe5, 0x0b, 0xfe, 0x3c, 0x23, 0xf5, 0x1c, 0xf4, 0x08, 0x6e, 0xa7, 0xfb, 0xcf, 0x5c, 0xfc,
	0x9e, 0x09, 0xdd, 0xdc, 0xd0, 0x36, 0x6b, 0xe6, 0xa2, 0x24, 0x7f, 0xeb, 0xe2, 0xf7, 0x3d, 0x07,
	0x2d, 0xc3, 0xbc, 0x15, 0x45, 0x8c, 0x3d, 0xc7, 0xd9, 0x73, 0x56, 0x14, 0xf5, 0x1c, 0xe3, 0x6b,
	0xa8, 0xab, 0x7d, 0x25, 0x11, 0x5a, 0x81, 0xca, 0x3b, 0x32, 0xb0, 0x43, 0x07, 0x73, 0x87, 0x6b,
	0xe6, 0xfc, 0x3b, 0xce, 0x44, 0xf7, 0x60, 0x3e, 0x8c, 0x70, 0x20, 0xfd, 0xad, 0x99, 0x72, 0x65,
	0x3c, 0x99, 0x0d, 0xfc, 0x90, 0xd3, 0x95, 0x81, 0x1b, 0xdb, 0xb3, 0x96, 0x53, 0x61, 0x12, 0x65,
	0x0c, 0x68, 0x53, 0x06, 0xfe, 0xaa, 0xc1, 0x1d, 0x29, 0xdd, 0x0e, 0x03, 0x92, 0xf8, 0x3c, 0xa9,
	0x05, 0xd2, 0x68, 0x09, 0x58, 0x90, 0x17, 0x5e, 0x8a, 0x05, 0x5a, 0x85, 0x6a, 0x18, 0x3b, 0x38,
	0x4e, 0xd3, 0x59, 0x33, 0x2b, 0x7c, 0xdd, 0x73, 0x98, 0x22, 0xcb, 0x0f, 0x93, 0x80, 0xf2, 0x14,
	0x2e, 0x9a, 0x72, 0x85, 0xd6, 0x01, 0xc2, 0x84, 0x12, 0xd7, 0xc1, 0x03, 0x4a, 0x78, 0xfe, 0xca,
	0x66, 0x4d, 0x52, 0xfa, 0x24, 0x8d, 0x6d, 0x7e, 0x12, 0xdb, 0x12, 0xa0, 0x59, 0x37, 0x49, 0x64,
	0xfc, 0xa7, 0x0c, 0xb7, 0x25, 0xb9, 0xf5, 0xde, 0x8a, 0x79, 0x5e, 0x96, 0x26, 0x79, 0xa9, 0xf1,
	0x73, 0xe5, 0x97, 0xa2, 0x20, 0xc1, 0x93, 0x88, 0xca, 0x45, 0x11, 0xdd, 0x9c, 0x8e, 0x68, 0x0d,
	0x6a, 0x31, 0x66, 0xb6, 0x26, 0x07, 0x5f, 0x15, 0x84, 0x9e, 0x83, 0x1e, 0xc0, 0x82, 0x64, 0xd2,
	0x71, 0x84, 0xa5, 0xff, 0x20, 0x48, 0xfd, 0x71, 0x84, 0x33, 0xf9, 0xa8, 0x4c, 0xe5, 0x63, 0x05,
	0x2a, 0x09, 0x11, 0xf6, 0xaa, 0x82, 0xc1, 0x96, 0x3d, 0xc7, 0xf8, 0x43, 0x09, 0xc0, 0x9c, 0xec,
	0x5f, 0x83, 0x15, 0xb3, 0xfb, 0xb6, 0x65, 0x76, 0x06, 0xfd, 0x5f, 0xbe, 0xee, 0x0e, 0xde, 0x1c,
	0x1c, 0xbd, 0xee, 0xb6, 0x7b, 0x2f, 0x7a, 0xdd, 0x8e, 0x7e, 0x03, 0x2d, 0xc3, 0x9d, 0x2c, 0xb3,
	0xbf, 0xdb, 0x6d, 0x1d, 0xe8, 0x1a, 0xba, 0x0f, 0xf5, 0x2c, 0xd9, 0xec, 0x76, 0x06, 0x9d, 0x5e,
	0xeb, 0xd5, 0xe1, 0x41, 0xa7, 0xa3, 0x97, 0x50, 0x1d, 0x96, 0xb2, 0xdc, 0x97, 0xdd, 0x56, 0xe7,
	0x6d, 0xb7, 0x65, 0xea, 0x65, 0xb4, 0x0a, 0xcb, 0x59, 0xce, 0x41, 0xeb, 0x55, 0xf7, 0xf5, 0x7e,
	0xab, 0xdf, 0xd5, 0x6f, 0xce, 0x5a, 0x7a, 0x79, 0x68, 0x1e, 0x75, 0xf5, 0x39, 0xb4, 0x0e, 0xab,
	0x59, 0xf2, 0xe1, 0x8b, 0x17, 0xbd, 0x76, 0xaf, 0xb5, 0x3f, 0x68, 0x77, 0xcd, 0xbe, 0x3e, 0x8f,
	0x1e, 0xc0, 0xda, 0x94, 0xf3, 0x47, 0x5d, 0x73, 0xd0, 0xe9, 0xb6, 0x0f, 0xcd, 0x56, 0xbf, 0x77,
	0x78, 0xa0, 0x57, 0x66, 0xd5, 0xbe, 0xea, 0x76, 0x5a, 0xfb, 0x7a, 0xd5, 0x40, 0xa0, 0x4f, 0x1f,
	0x32, 0x89, 0x8c, 0x1d, 0x68, 0xb4, 0xad, 0xc0, 0xc6, 0x9e, 0x12, 0x14, 0x8a, 0xea, 0xcc, 0x58,
	0x87, 0xb5, 0xc2, 0x6d, 0x24, 0x32, 0x9e, 0xc0, 0x1d, 0x56, 0x41, 0xae, 0xb3, 0x3b, 0x9e, 0x14,
	0x5a, 0x51, 0xe9, 0x3c, 0x02, 0x34, 0x2b, 0x4c, 0x22, 0x45, 0x59, 0xfe, 0x1e, 0xee, 0xb5, 0x47,
	0xd8, 0x3e, 0x65, 0x26, 0x79, 0x10, 0xb6, 0x8d, 0x09, 0x51, 0x63, 0x57, 0x06, 0x98, 0x4a, 0x39,
	0x60, 0x5a, 0x9f, 0x02, 0x36, 0x0e, 0x5c, 0x59, 0x50, 0x9b, 0xe0, 0xd1, 0xcd, 0x2c, 0x1e, 0x3d,
	0x83, 0x15, 0xa5, 0x79, 0x01, 0x0a, 0x16, 0x5f, 0x71, 0x17, 0xaa, 0xa6, 0x5c, 0x19, 0x5f, 0x40,
	0x9d, 0x6f, 0x69, 0x0b, 0xdd, 0xbb, 0x9e, 0x65, 0x9f, 0xee, 0xbb, 0x84, 0x32, 0x9f, 0xa7, 0x9d,
	0xd0, 0x66, 0x9c, 0x30, 0x9e, 0xc3, 0x6a, 0xc1, 0x56, 0x12, 0xb1, 0xb2, 0x72, 0xc9, 0xe0, 0x98,
	0xd1, 0xa4, 0xc5, 0x8a, 0x4b, 0xb8, 0x88, 0xb1, 0x0d, 0x8d, 0x3d, 0x4c, 0xdf, 0x8e, 0x5c, 0x8a,
	0xe5, 0x56, 0xd3, 0x0a, 0x9c, 0xd0, 0xf7, 0xc6, 0xa2, 0xa6, 0xe7, 0x6c, 0x5e, 0x35, 0xc2, 0x9e,
	0x58, 0x18, 0xbf, 0x86, 0xb5, 0xc2, 0x3d, 0x24, 0x9a, 0x79, 0x07, 0x34, 0xd5, 0x3b, 0xb0, 0x0a,
	0x55, 0xdb, 0x75, 0x06, 0x9e, 0x4b, 0x68, 0xbd, 0xb4, 0x51, 0xde, 0x5c, 0x34, 0x2b, 0xb6, 0xeb,
	0x30, 0x7f, 0x8d, 0x67, 0x70, 0x6f, 0x0f, 0xd3, 0x56, 0x42, 0x47, 0xbd, 0xe0, 0x24, 0xdc, 0x1d,
	0x5f, 0xe3, 0x72, 0xfd, 0x57, 0x3c, 0x53, 0xf9, 0x3d, 0xc5, 0xf8, 0x9b, 0x5e, 0x01, 0x01, 0x4a,
	0xfc, 0x0a, 0xa8, 0x11, 0xe9, 0xc7, 0x80, 0xd2, 0x60, 0x1c, 0x97, 0x44, 0x9e, 0x35, 0x4e, 0x0f,
	0x5a, 0x04, 0x95, 0x3e, 0xca, 0x1d, 0xc1, 0x54, 0x3f, 0x63, 0x73, 0xaa, 0x67, 0xec, 0x01, 0x2c,
	0xd8, 0x9e, 0x8b, 0x03, 0x3a, 0x85, 0x59, 0x82, 0x24, 0x31, 0xa7, 0xe6, 0x5b, 0xf1, 0x29, 0xa6,
	0x4c, 0x85, 0x80, 0xad, 0xaa, 0x20, 0xf4, 0x1c, 0x63, 0x05, 0x96, 0xf7, 0x30, 0x95, 0x95, 0xc4,
	0xa2, 0x96, 0xf7, 0xc4, 0xe8, 0xf3, 0xfc, 0xe5, 0x18, 0x24, 0x42, 0x5f, 0x42, 0x4d, 0xdc, 0xf1,
	0xe0, 0x24, 0xac, 0x6b, 0x1b, 0xe5, 0xcd, 0x85, 0xed, 0xf5, 0xe6, 0x6c, 0x4f, 0xd1, 0xcc, 0xec,
	0x34, 0xab, 0x43, 0xf9, 0x65, 0xfc, 0xb9, 0x04, 0x0b, 0x19, 0x4e, 0xb6, 0x5e, 0xb4, 0x5c, 0xbd,
	0x20, 0xb8, 0x19, 0x58, 0x3e, 0x96, 0xc9, 0xe5, 0xdf, 0xa8, 0x0e, 0x15, 0x3b, 0x0c, 0x28, 0x0e,
	0x68, 0xfa, 0x54, 0xc9, 0x25, 0xe3, 0x9c, 0xe1, 0x98, 0x75, 0x2d, 0x29, 0xe4, 0xcb, 0x25, 0x3b,
	0x91, 0xe3, 0xc4, 0xf5, 0x44, 0xfe, 0x16, 0x4d, 0xb1, 0x60, 0x27, 0xf7, 0xbd, 0x1b, 0xf1, 0x7c,
	0xd5, 0x4c, 0xf6, 0xc9, 0x0a, 0x70, 0xb4, 0x33, 0x48, 0x62, 0x8f, 0x67, 0xa9, 0x66, 0xce, 0x8d,
	0x76, 0xde, 0xc4, 0x1e, 0xbb, 0x64, 0x27, 0x89, 0xe7, 0x71, 0x46, 0x55, 0x68, 0x66, 0x6b, 0xc6,
	0xd2, 0xa1, 0xec, 0x3b, 0x3b, 0xf5, 0x9a, 0xd0, 0xe1, 0x3b, 0x3b, 0xcc, 0x67, 0xe2, 0x7e, 0x8f,
	0xeb, 0xc0, 0x4d, 0xf1, 0xef, 0x4c, 0x61, 0x2f, 0x64, 0x0b, 0xfb, 0xe7, 0xb0, 0xb2, 0x6b, 0x51,
	0x7b, 0x94, 0x05, 0xa1, 0x9e, 0xc3, 0x81, 0x65, 0x03, 0x3e, 0x61, 0xf7, 0x6b, 0x90, 0xde, 0x6d,
	0x96, 0xe5, 0x9a, 0x09, 0x21, 0x97, 0xe0, 0xd7, 0xfb, 0x6f, 0x1a, 0xd4, 0xd5, 0xbb, 0x49, 0x84,
	0x0e, 0xa1, 0x92, 0xb8, 0xce, 0xc0, 0xb7, 0x22, 0x79, 0x3e, 0xcf, 0xf3, 0xe7, 0x53, 0xb4, 0xb9,
	0xf9, 0xc6, 0x75, 0x5e, 0x59, 0x51, 0x37, 0xa0, 0xf1, 0xd8, 0x9c, 0x4f, 0xf8, 0xa2, 0xf1, 0x05,
	0x2c, 0x64, 0xc8, 0x2c, 0xec, 0x53, 0x3c, 0x96, 0x95, 0xc0, 0x3e, 0x59, 0x8a, 0xcf, 0x2c, 0x2f,
	0x11, 0x67, 0xb5, 0x68, 0x8a, 0xc5, 0x97, 0xa5, 0x9f, 0x69, 0xc6, 0x5f, 0x34, 0x40, 0x47, 0x38,
	0x70, 0x5e, 0x7b, 0x16, 0x3d, 0x09, 0x63, 0xff, 0x15, 0x19, 0xb2, 0x08, 0x27, 0x39, 0xd1, 0x32,
	0x39, 0xc9, 0x05, 0x5e, 0x9a, 0x0d, 0x9c, 0x5d, 0x77, 0x8a, 0xfd, 0xc8, 0xb3, 0xe8, 0xa4, 0xfd,
	0x33, 0x21, 0x25, 0x89, 0x07, 0x9e, 0xe0, 0x40, 0xbe, 0xe0, 0xa2, 0x6b, 0xa9, 0x32, 0x02, 0xab,
	0x05, 0x63, 0x19, 0xee, 0xe6, 0x9c, 0x21, 0x91, 0xd1, 0x05, 0x64, 0x62, 0x3f, 0x3c, 0xc3, 0x1c,
	0x8c, 0xae, 0x07, 0x95, 0xd9, 0xd2, 0x97, 0x2f, 0xc5, 0x32, 0xdc, 0xcd, 0xa9, 0x21, 0x91, 0xb1,
	0x07, 0xb7, 0x5b, 0x8e, 0x33, 0xa5, 0x3a, 0x0b, 0x5c, 0xda, 0x14, 0x70, 0x31, 0x56, 0x32, 0x83,
	0x69, 0x89, 0xc4, 0x34, 0x04, 0xfa, 0xb4, 0x22, 0x12, 0x19, 0x1d, 0xb8, 0xc3, 0x01, 0xfb, 0xe3,
	0x3c, 0x3f, 0x00, 0x34, 0xab, 0x85, 0x44, 0x2c, 0x95, 0xcc, 0xcb, 0xf7, 0x8c, 0x28, 0x01, 0x9f,
	0xb9, 0xcd, 0x85, 0x18, 0x33, 0xb9, 0x60, 0x96, 0x04, 0x33, 0x91, 0x4c, 0xe3, 0x9f, 0x25, 0x78,
	0xd0, 0xc7, 0x84, 0x4a, 0x5c, 0x6f, 0x87, 0xbe, 0x1f, 0x06, 0x2f, 0xdd, 0xe1, 0x68, 0xdf, 0x1d,
	0x8e, 0x68, 0xcf, 0x57, 0xbf, 0x9e, 0x3a, 0x94, 0xed, 0x89, 0x5f, 0xb6, 0xeb, 0x5c, 0x52, 0xee,
	0x9f, 0x03, 0x1a, 0xb9, 0xc3, 0xd1, 0xc0, 0x63, 0x0a, 0x07, 0xa9, 0x90, 0xa8, 0x7c, 0x7d, 0x94,
	0x5a, 0x6a, 0x4b, 0xe9, 0x55, 0xa8, 0xbe, 0x4b, 0xfc, 0x88, 0xd7, 0xb0, 0x40, 0xd1, 0x0a, 0x5b,
	0xb3, 0x1a, 0x5e, 0x07, 0x38, 0x09, 0x03, 0xa6, 0xc2, 0x0b, 0x63, 0x09, 0x07, 0x35, 0x46, 0x69,
	0x33, 0x02, 0x7a, 0x08, 0x9f, 0x1c, 0x8b, 0x5e, 0x52, 0x08, 0x08, 0x68, 0x58, 0x10, 0x34, 0x21,
	0xb2, 0x09, 0xfa, 0x94, 0x2b, 0x4c, 0x4c, 0x00, 0xc5, 0xad, 0x8c, 0x23, 0x4c, 0xf2, 0x31, 0xe8,
	0xc7, 0x96, 0x7d, 0x3a, 0x8c, 0xc3, 0x24, 0x70, 0xa4, 0xa4, 0x00, 0x8f, 0xdb, 0x13, 0x3a, 0x17,
	0x35, 0x0c, 0xd8, 0xb8, 0x3c, 0x81, 0x24, 0x32, 0xfe, 0xa8, 0xc1, 0x5a, 0xc7, 0x72, 0xbd, 0xb1,
	0xec, 0xa9, 0xfb, 0x21, 0xb5, 0xbc, 0x76, 0x40, 0x5f, 0x27, 0x64, 0x24, 0x6f, 0xd9, 0x31, 0x1e,
	0xba, 0x01, 0xeb, 0xd1, 0x35, 0xde, 0xa3, 0x57, 0xf8, 0xba, 0x4f, 0x58, 0xfd, 0xf1, 0x22, 0x21,
	0x3c, 0xdb, 0x65, 0x73, 0x8e, 0x55, 0x08, 0x41, 0x0f, 0xa0, 0x16, 0x25, 0x64, 0x34, 0xf0, 0xac,
	0xf8, 0x94, 0x67, 0xbc, 0xca, 0x11, 0xb9, 0xca, 0x88, 0xfb, 0x56, 0x7c, 0xca, 0x0a, 0x9d, 0xba,
	0xd4, 0xc3, 0x69, 0x8f, 0xc2, 0x17, 0xc6, 0x37, 0x70, 0xbf, 0xd8, 0x0f, 0x71, 0x91, 0x28, 0xa3,
	0x0d, 0xec, 0x8b, 0x1e, 0xa0, 0x4a, 0xa5, 0x90, 0x1a, 0x3b, 0x8c, 0x5f, 0xc0, 0xd2, 0x91, 0xeb,
	0x47, 0x1e, 0xfe, 0x26, 0xc1, 0xf1, 0xf8, 0x92, 0xb1, 0x89, 0x41, 0x2e, 0xa5, 0x17, 0x6f, 0x30,
	0xff, 0x36, 0xb6, 0x60, 0x59, 0xb1, 0xbb, 0xf8, 0x1d, 0xdf, 0xfe, 0x37, 0x82, 0x5b, 0x32, 0xd7,
	0xf2, 0x7d, 0x42, 0x21, 0x2c, 0xa9, 0x06, 0x41, 0xf4, 0x38, 0x0f, 0xa6, 0x05, 0xc3, 0x6d, 0xe3,
	0xb3, 0xeb, 0x8a, 0x92, 0xc8, 0xb8, 0x81, 0xce, 0x61, 0xa5, 0xa0, 0xb9, 0x45, 0x9f, 0xe7, 0x15,
	0x15, 0xb7, 0xcf, 0x8d, 0xa7, 0x1f, 0x20, 0xcd, 0x2d, 0xe7, 0x42, 0x15, 0x19, 0xbb, 0x3a, 0xd4,
	0x8b, 0x73, 0xb9, 0x3a, 0xd4, 0xc9, 0x21, 0x18, 0x37, 0xd0, 0x09, 0xdc, 0xc9, 0x9d, 0x0f, 0x7a,
	0x94, 0x57, 0xa1, 0xba, 0x02, 0x8d, 0x4f, 0xaf, 0x25, 0xc7, 0xed, 0xfc, 0x06, 0x6e, 0x4d, 0x8f,
	0x9d, 0xe8, 0x07, 0x85, 0xad, 0xca, 0x64, 0x7e, 0x6e, 0xfc, 0xf0, 0x6a, 0x21, 0xae, 0xfe, 0x2d,
	0x7c, 0x92, 0x9d, 0x6c, 0xd0, 0xc3, 0xc2, 0x7d, 0xe9, 0x78, 0xdb, 0x30, 0xae, 0x12, 0x49, 0xfd,
	0x9e, 0x9e, 0x4d, 0x54, 0x7e, 0xe7, 0x46, 0x1d, 0x95, 0xdf, 0xf9, 0x11, 0x47, 0x9c, 0xb7, 0xea,
	0xfd, 0x57, 0x9d, 0x77, 0x41, 0x8b, 0xa2, 0x3a, 0xef, 0xa2, 0x96, 0xc2, 0xb8, 0x81, 0x3c, 0xb8,
	0xab, 0x18, 0x62, 0xd0, 0xa6, 0xe2, 0xa2, 0x2a, 0x47, 0xad, 0xc6, 0xe3, 0x6b, 0x4a, 0x72, 0x6b,
	0x31, 0x2c, 0x2b, 0x87, 0x18, 0xf4, 0x59, 0x81, 0x16, 0xc5, 0xa0, 0xd4, 0x78, 0x72, 0x6d, 0xd9,
	0xb4, 0x78, 0x0b, 0x86, 0x19, 0x55, 0xf1, 0x16, 0xcf, 0x4a, 0xaa, 0xe2, 0xbd, 0x64, 0x4a, 0x12,
	0xb9, 0x55, 0x4c, 0x2d, 0xaa, 0xdc, 0xaa, 0x07, 0xa2, 0xc6, 0xe3, 0x6b, 0x4a, 0x72, 0x6b, 0x2e,
	0x9f, 0x9a, 0x67, 0xe6, 0x02, 0xf4, 0xa9, 0x52, 0x45, 0x7e, 0xac, 0x68, 0x6c, 0x5e, 0x4f, 0x90,
	0x9b, 0xfa, 0x2d, 0xdc, 0x9e, 0x69, 0xd6, 0x90, 0xe2, 0x82, 0xe7, 0x9b, 0xcb, 0xc6, 0x8f, 0xae,
	0x21, 0x95, 0x5a, 0x98, 0x69, 0xd8, 0x54, 0x16, 0xf2, 0xad, 0xa1, 0xca, 0x82, 0xaa, 0xf3, 0xe3,
	0x08, 0x91, 0x6d, 0xd9, 0x54, 0x08, 0x31, 0xd3, 0x1b, 0xaa, 0x10, 0x22, 0xd7, 0xf5, 0x71, 0x84,
	0x98, 0xee, 0xd8, 0x54, 0x08, 0x91, 0xeb, 0x0c, 0x55, 0x08, 0x91, 0x6f, 0xfc, 0x8c, 0x1b, 0xe8,
	0x4f, 0x1a, 0xdc, 0xbf, 0xac, 0xff, 0x40, 0xcf, 0xf2, 0x8a, 0xae, 0x68, 0xf8, 0x1a, 0xdb, 0x1f,
	0xba, 0x85, 0x7b, 0xf2, 0x3b, 0xa8, 0x17, 0xf5, 0x16, 0x48, 0x51, 0x2b, 0x97, 0xf4, 0x43, 0x8d,
	0xe6, 0x87, 0x88, 0x73, 0xe3, 0x5f, 0xf3, 0x87, 0x71, 0x8a, 0xcf, 0xff, 0xdf, 0xb7, 0xda, 0x34,
	0xd3, 0x7f, 0x73, 0x7f, 0xbb, 0xdd, 0xec, 0xbb, 0x3e, 0x36, 0xad, 0x60, 0xc8, 0xab, 0xe9, 0xde,
	0x14, 0x8b, 0x8b, 0x4b, 0x65, 0xfb, 0xbc, 0x74, 0xa4, 0xb2, 0x43, 0xf1, 0xff, 0x48, 0x72, 0x99,
	0xaa, 0x69, 0x56, 0xba, 0x43, 0x6a, 0xeb, 0x71, 0x6d, 0xfc, 0xd1, 0xf8, 0x58, 0xc7, 0xbe, 0x02,
	0x3d, 0x55, 0xf5, 0xb1, 0x6e, 0x35, 0x56, 0xfe, 0xf7, 0x8f, 0x7f, 0xf5, 0x11, 0xe8, 0xb3, 0x3f,
	0x3e, 0xec, 0xfe, 0xf4, 0x57, 0xdb, 0xc3, 0xd0, 0xb3, 0x82, 0x61, 0x73, 0x67, 0x9b, 0xd2, 0xa6,
	0x1d, 0xfa, 0x5b, 0xfc, 0xd7, 0x01, 0x3b, 0xf4, 0xb6, 0x08, 0x8e, 0xcf, 0x5c, 0x1b, 0x93, 0xdc,
	0x4f, 0x16, 0xc7, 0xf3, 0x5c, 0xe6, 0x27, 0xff, 0x0f, 0x00, 0x00, 0xff, 0xff, 0x2b, 0x33, 0x94,
	0x21, 0xed, 0x18, 0x00, 0x00,
}
