// Code generated by protoc-gen-go. DO NOT EDIT.
// source: logicsvr-go/channel-scheme-logic/channel-scheme-logic.proto

package channel_scheme_logic // import "golang.52tt.com/protocol/services/logicsvr-go/channel-scheme-logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import channel_scheme "golang.52tt.com/protocol/app/channel-scheme"
import _ "golang.52tt.com/protocol/services/logicsvr-go/gateway/options"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ChannelSchemeLogicClient is the client API for ChannelSchemeLogic service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChannelSchemeLogicClient interface {
	GetChannelSchemeInfo(ctx context.Context, in *channel_scheme.GetChannelSchemeInfoReq, opts ...grpc.CallOption) (*channel_scheme.GetChannelSchemeInfoResp, error)
	CheckExitAfterSwitchScheme(ctx context.Context, in *channel_scheme.CheckExitAfterSwitchSchemeReq, opts ...grpc.CallOption) (*channel_scheme.CheckExitAfterSwitchSchemeResp, error)
	GetPgcChannelSchemeList(ctx context.Context, in *channel_scheme.GetPgcChannelSchemeListReq, opts ...grpc.CallOption) (*channel_scheme.GetPgcChannelSchemeListResp, error)
	SwitchChannelScheme(ctx context.Context, in *channel_scheme.SwitchChannelSchemeReq, opts ...grpc.CallOption) (*channel_scheme.SwitchChannelSchemeResp, error)
}

type channelSchemeLogicClient struct {
	cc *grpc.ClientConn
}

func NewChannelSchemeLogicClient(cc *grpc.ClientConn) ChannelSchemeLogicClient {
	return &channelSchemeLogicClient{cc}
}

func (c *channelSchemeLogicClient) GetChannelSchemeInfo(ctx context.Context, in *channel_scheme.GetChannelSchemeInfoReq, opts ...grpc.CallOption) (*channel_scheme.GetChannelSchemeInfoResp, error) {
	out := new(channel_scheme.GetChannelSchemeInfoResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelSchemeLogic/GetChannelSchemeInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelSchemeLogicClient) CheckExitAfterSwitchScheme(ctx context.Context, in *channel_scheme.CheckExitAfterSwitchSchemeReq, opts ...grpc.CallOption) (*channel_scheme.CheckExitAfterSwitchSchemeResp, error) {
	out := new(channel_scheme.CheckExitAfterSwitchSchemeResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelSchemeLogic/CheckExitAfterSwitchScheme", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelSchemeLogicClient) GetPgcChannelSchemeList(ctx context.Context, in *channel_scheme.GetPgcChannelSchemeListReq, opts ...grpc.CallOption) (*channel_scheme.GetPgcChannelSchemeListResp, error) {
	out := new(channel_scheme.GetPgcChannelSchemeListResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelSchemeLogic/GetPgcChannelSchemeList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelSchemeLogicClient) SwitchChannelScheme(ctx context.Context, in *channel_scheme.SwitchChannelSchemeReq, opts ...grpc.CallOption) (*channel_scheme.SwitchChannelSchemeResp, error) {
	out := new(channel_scheme.SwitchChannelSchemeResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelSchemeLogic/SwitchChannelScheme", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChannelSchemeLogicServer is the server API for ChannelSchemeLogic service.
type ChannelSchemeLogicServer interface {
	GetChannelSchemeInfo(context.Context, *channel_scheme.GetChannelSchemeInfoReq) (*channel_scheme.GetChannelSchemeInfoResp, error)
	CheckExitAfterSwitchScheme(context.Context, *channel_scheme.CheckExitAfterSwitchSchemeReq) (*channel_scheme.CheckExitAfterSwitchSchemeResp, error)
	GetPgcChannelSchemeList(context.Context, *channel_scheme.GetPgcChannelSchemeListReq) (*channel_scheme.GetPgcChannelSchemeListResp, error)
	SwitchChannelScheme(context.Context, *channel_scheme.SwitchChannelSchemeReq) (*channel_scheme.SwitchChannelSchemeResp, error)
}

func RegisterChannelSchemeLogicServer(s *grpc.Server, srv ChannelSchemeLogicServer) {
	s.RegisterService(&_ChannelSchemeLogic_serviceDesc, srv)
}

func _ChannelSchemeLogic_GetChannelSchemeInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_scheme.GetChannelSchemeInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelSchemeLogicServer).GetChannelSchemeInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelSchemeLogic/GetChannelSchemeInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelSchemeLogicServer).GetChannelSchemeInfo(ctx, req.(*channel_scheme.GetChannelSchemeInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelSchemeLogic_CheckExitAfterSwitchScheme_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_scheme.CheckExitAfterSwitchSchemeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelSchemeLogicServer).CheckExitAfterSwitchScheme(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelSchemeLogic/CheckExitAfterSwitchScheme",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelSchemeLogicServer).CheckExitAfterSwitchScheme(ctx, req.(*channel_scheme.CheckExitAfterSwitchSchemeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelSchemeLogic_GetPgcChannelSchemeList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_scheme.GetPgcChannelSchemeListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelSchemeLogicServer).GetPgcChannelSchemeList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelSchemeLogic/GetPgcChannelSchemeList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelSchemeLogicServer).GetPgcChannelSchemeList(ctx, req.(*channel_scheme.GetPgcChannelSchemeListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelSchemeLogic_SwitchChannelScheme_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_scheme.SwitchChannelSchemeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelSchemeLogicServer).SwitchChannelScheme(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelSchemeLogic/SwitchChannelScheme",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelSchemeLogicServer).SwitchChannelScheme(ctx, req.(*channel_scheme.SwitchChannelSchemeReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelSchemeLogic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "logic.ChannelSchemeLogic",
	HandlerType: (*ChannelSchemeLogicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetChannelSchemeInfo",
			Handler:    _ChannelSchemeLogic_GetChannelSchemeInfo_Handler,
		},
		{
			MethodName: "CheckExitAfterSwitchScheme",
			Handler:    _ChannelSchemeLogic_CheckExitAfterSwitchScheme_Handler,
		},
		{
			MethodName: "GetPgcChannelSchemeList",
			Handler:    _ChannelSchemeLogic_GetPgcChannelSchemeList_Handler,
		},
		{
			MethodName: "SwitchChannelScheme",
			Handler:    _ChannelSchemeLogic_SwitchChannelScheme_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "logicsvr-go/channel-scheme-logic/channel-scheme-logic.proto",
}

func init() {
	proto.RegisterFile("logicsvr-go/channel-scheme-logic/channel-scheme-logic.proto", fileDescriptor_channel_scheme_logic_3bf2560a1b4cc962)
}

var fileDescriptor_channel_scheme_logic_3bf2560a1b4cc962 = []byte{
	// 308 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x84, 0x92, 0xbf, 0x4b, 0xc4, 0x30,
	0x14, 0xc7, 0xa1, 0xfe, 0x40, 0x33, 0xc6, 0x43, 0x31, 0x15, 0xc5, 0xdb, 0x1c, 0x9a, 0xc2, 0x89,
	0x93, 0x93, 0x77, 0xca, 0x21, 0x38, 0x88, 0x37, 0x29, 0x82, 0xc4, 0xf0, 0x2e, 0x0d, 0xf6, 0x92,
	0xda, 0x84, 0x3b, 0x5d, 0xfb, 0x5f, 0xdd, 0xe8, 0xae, 0xf6, 0x7f, 0x71, 0x74, 0x92, 0xb4, 0x4a,
	0xad, 0xb4, 0xde, 0x54, 0x9a, 0xcf, 0x7b, 0x9f, 0xef, 0x0b, 0x79, 0xe8, 0x38, 0xd6, 0x42, 0x72,
	0x33, 0x4d, 0x03, 0xa1, 0x43, 0x1e, 0x31, 0xa5, 0x20, 0x0e, 0x0c, 0x8f, 0x60, 0x02, 0x41, 0x81,
	0x1a, 0x0f, 0x69, 0x92, 0x6a, 0xab, 0xf1, 0x4a, 0xf1, 0x43, 0x0e, 0x7e, 0x3b, 0x04, 0xb3, 0x30,
	0x63, 0xcf, 0xa1, 0x4e, 0xac, 0xd4, 0xca, 0xfc, 0x7c, 0xcb, 0x0e, 0xb2, 0xcd, 0x92, 0xe4, 0x8f,
	0xf1, 0xae, 0x44, 0xbd, 0x97, 0x25, 0x84, 0x07, 0x25, 0x19, 0x15, 0xe0, 0xc2, 0x59, 0xf1, 0x2d,
	0xea, 0x0c, 0xc1, 0xd6, 0xc0, 0xb9, 0x1a, 0x6b, 0xec, 0x53, 0xc1, 0x68, 0x13, 0xb9, 0x82, 0x47,
	0xb2, 0xd3, 0x0e, 0x4d, 0xd2, 0x5d, 0xff, 0xc8, 0xe7, 0x74, 0x79, 0xed, 0x35, 0xf3, 0xb0, 0x42,
	0x64, 0x10, 0x01, 0x7f, 0x38, 0x7b, 0x92, 0xf6, 0x64, 0x6c, 0x21, 0x1d, 0xcd, 0xa4, 0xe5, 0x51,
	0xd9, 0x80, 0xf7, 0x9d, 0xa6, 0x9d, 0xbb, 0xa4, 0xee, 0xa2, 0x92, 0x2a, 0xef, 0x2d, 0xf3, 0x30,
	0xa0, 0xad, 0x21, 0xd8, 0x4b, 0xc1, 0xeb, 0x37, 0x95, 0xc6, 0xe2, 0xdd, 0xef, 0x99, 0x9b, 0xa0,
	0x4b, 0xda, 0xfb, 0x97, 0x57, 0x31, 0xef, 0x99, 0x87, 0xaf, 0xd1, 0x46, 0x39, 0x45, 0xad, 0x12,
	0x13, 0xa7, 0x68, 0x00, 0x4e, 0xef, 0xb7, 0xb2, 0x4a, 0x9d, 0x67, 0x1e, 0xf1, 0x3f, 0xf3, 0x39,
	0xdd, 0x44, 0x9d, 0xa6, 0xb5, 0xe8, 0x9f, 0xde, 0xf4, 0x85, 0x8e, 0x99, 0x12, 0xf4, 0xa8, 0x67,
	0x2d, 0xe5, 0x7a, 0x12, 0x16, 0x8f, 0xcb, 0x75, 0x1c, 0x1a, 0x48, 0xa7, 0x92, 0x83, 0x09, 0x17,
	0x6d, 0xdc, 0xfd, 0x6a, 0xd1, 0x73, 0xf8, 0x15, 0x00, 0x00, 0xff, 0xff, 0xa5, 0x01, 0xbe, 0xd3,
	0x9c, 0x02, 0x00, 0x00,
}
