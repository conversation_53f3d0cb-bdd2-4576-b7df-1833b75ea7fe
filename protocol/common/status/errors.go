// Generated by gen_error tool (go plugin).
// If you make any local change, they will be lost.
// source: errors.def

package status

// status codes
const (

	//

	// 切记!切记!切记: 不要轻易使用1000 以内的错误码

	// common 使用的 appsvr/common/core/iErrno.h

	//
	Success        = 0 // 成功
	ErrKeyNotFound = 1

	// Common Error
	ErrSys                    = -2 // 系统错误
	ErrDatabaseOperation      = -3
	ErrSystemBusy             = -4  // 系统繁忙
	ErrRequestTimeout         = -5  // 处理超时
	ErrBadRequest             = -6  // 无效的请求
	ErrTrafficAdmin           = -8  // 流量管制
	ErrTrafficFrequently      = -14 // 请求太频繁
	ErrGrpcNotFound           = -15 // 实例找不到
	ErrGrpcFailedPrecondition = -16 // 前置条件未就绪
	ErrGrpcUnimplemented      = -17 // 接口未实现
	ErrGrpcInternal           = -18 // 服务内部错误
	ErrGrpcUnavailable        = -19 // 服务不可用
	ErrGrpcCancelled          = -20 // 请求被取消
	ErrPbParseError           = -21 // 协议解析出错
	ErrWasmCircuitBreak       = -22 // 请求被熔断

	// 客户端错误码 从－100000开始 begin ---------------
	ErrTimeout                                     = -100001
	ErrPbParse                                     = -100003
	ErrParam                                       = -100004 // 参数错误
	ErrNetwork                                     = -100005
	ErrDbError                                     = -100006
	ErrNetservice                                  = -100007
	ErrNotLogin                                    = -100008
	ErrFileNotFound                                = -100009
	ErrStillWaitingResponse                        = -100010
	ErrImageDecodeError                            = -100011
	ErrImageDownloadError                          = -100012
	ErrImagePathEmptyError                         = -100013
	ErrDummyErrorCode                              = -100020 // // 该错误不应该显示给用户
	ErrNoMoreDataErrorCode                         = -100021
	ErrPureErrorCode                               = -100022 // // 该错误码不应该显示给用户
	ErrCancelled                                   = -100023 // 取消
	ErrUnknown                                     = -100030 // 未知错误
	ErrFaceBigDownloading                          = -100100
	ErrImUploadAttachmentFailed                    = -100201
	ErrImDownloadAttachmentFailed                  = -100202
	ErrGameNoLocalPackage                          = -100600
	ErrGameDownloadHttpError                       = -100601
	ErrGameDownloadApkError                        = -100602
	ErrGameCircleSendTopicCompressImgFailed        = -100700
	ErrGameCircleSendTopicUploadImgFailed          = -100701
	ErrDeviceNotSupportTeamVoice                   = -100801
	ErrNotInChannel                                = -100901
	ErrPluginLoad                                  = -101001
	ErrPluginNotExists                             = -101002
	ErrPluginFileNotExists                         = -101003
	ErrPluginDisable                               = -101004
	ErrPluginInterfaceNotSupport                   = -101005
	ErrChannelNeedsQuitCurrentChannel              = -101101
	ErrChannelNeedsRegister                        = -101102
	ErrShareFailedWechatNotInstalled               = -101201
	ErrShareFailedIllegalParameter                 = -101202
	ErrShareFailedNotSupportShareType              = -101203
	ErrSendPresentSelectNothingToSend              = -101301
	ErrSendPresentFailToGetTargetUserInfo          = -101302
	ErrSendPresentFailToGetCurrentChannelInfo      = -101303
	ErrSendPresentFailToMyself                     = -101304 // 不可以送礼给自己喔
	ErrCaptchaErrParameter                         = -101401
	ErrCaptchaNotReady                             = -101402
	ErrCaptchaValidateErr                          = -101403
	ErrCaptchaOtherErr                             = -101404
	ErrNormalVerifyTooOftenErr                     = -101501 // 获取短信验证码太频繁
	ErrChannelInGameNotSupportChangeMic            = -101601 // 房间在游戏中，不支持上下麦
	ErrOperateTooOften                             = -101701 // 操作太频繁，频率限制
	ErrTgameNoButtonAction                         = -101801 // 不支持游戏中该按钮的点击回调
	ErrTgameForbidImChatWithMicMate                = -101802 // 该游戏不支持跟麦上成员进行im聊天
	ErrTgameForbidSendPresentWhenGameStart         = -101803 // 游戏已开始，为了保证游戏公平性，暂时不能给Ta送礼哦
	ErrTgameForbidSendMagicExpressionWhenGameStart = -101804 // 游戏已开始，为了保证游戏公平性，暂时不能发送魔法表情哦
	ErrPlayTogetherAnotherStillExist               = -101901 // 与该用户存在另外一个 一起玩的邀请未失效
	ErrPlayTogetherQuitChannelError                = -101902 // 退出当前房间失败
	ErrPlayTogetherQuitChannelCancel               = -101903 // 拒绝退出当前房间
	ErrFrescoRequestFail                           = -102001 // //FRESCO下载图片失败
	ErrFrescoRequestCancel                         = -102002 // //FRESCO下载图片取
	ErrDownloadResourceFail                        = -102003 // //下载资源失败
	ErrNoAvailableMic                              = -102004 // //没有空麦位可以上
	ErrNotifySendReqNotLogin                       = -102005 // //请求由于未登录被拦截
	ErrPresentReturnParamsError                    = -102101 // //礼物请求返回参数有误

	// 客户端错误码 end --------------
	ErrBaseAccount = -100
	ErrBaseSendMsg = -200
	ErrBaseSyncMsg = -300
	ErrBaseFriend  = -400
	ErrSystemError = -10000

	// ACCOUNT -100
	ErrAccountPhoneExist                        = -101 // 该手机号码已被注册
	ErrAccountUsernameExist                     = -102 // 该用户名已存在
	ErrAccountNotExist                          = -103 // 账号不存在
	ErrAccountPasswordWrong                     = -104 // 密码或用户名错误
	ErrAccountAliasModified                     = -105 // 账号已经修改过了
	ErrAccountCompleted                         = -106 // 用户资料已经完善过了
	ErrAccountNoHeadImage                       = -107 // 用户没有上传头像
	ErrAccountVerifyCodeWrong                   = -108 // 验证码输入有误，请重新输入
	ErrAccountPasswordSame                      = -109 // 新密码不能与旧密码相同
	ErrAccountPhoneFormatWrong                  = -110 // 手机号码格式不对
	ErrAccountUsernameFormatWrong               = -111 // 用户名不能包含特殊字符
	ErrAccountPermissionDenied                  = -112 // 权限不足
	ErrAccountHasNoGuild                        = -113 // 用户未加入公会
	ErrAccountVerifyTooFreq                     = -114 // 验证短信一段时间内只能下发一条， 请稍后再试
	ErrAccountExist                             = -115 // 账号已存在
	ErrAccountVerifyInvalidUsage                = -116
	ErrAccountMibaoNotSet                       = -117 // 用户未设置密保
	ErrAccountMibaoAnswerErr                    = -118 // 密保答案错误
	ErrAccountMibaoQuestionErr                  = -119 // 密保问题错误
	ErrAccountNicknameSensitive                 = -120 // 你输入的昵称包含敏感词，修改失败
	ErrAccountSignatureSensitive                = -121 // 你输入的签名包含敏感词，修改失败
	ErrAccountMibaoAlreadyInit                  = -122 // 用户已经设置过密保
	ErrAccountResetClose                        = -123 // 注册失败，同一个手机号只能注册一个帐号
	ErrAccountBanned                            = -124 // 该帐号由于安全问题已被暂时冻结，请联系客服
	ErrAccountRegFrequenceLimit                 = -125 // 同设备不能注册多个
	ErrAccountOpenidExist                       = -126 // Openid已存在
	ErrAccountPasswordNotEnough                 = -127
	ErrAccountInvalidQueryType                  = -128
	ErrAccountNoLoginHistory                    = -129
	ErrAccountOperationNotAllowed               = -130
	ErrAccountUserPhoneAlreadyBinded            = -131 // 该手机号已经绑定到你的账号，无需再次绑定
	ErrAccountUserBindedAnotherPhone            = -132 // 你当前已经绑定到了另一个手机号，无法绑定
	ErrAccountPhoneBindedToAnother              = -133 // 该手机号已被绑定过，请更新到最新版本中绑定，在【我】-【关于TT语音】-【检查更新】中更新版本
	ErrAccountBindedPhoheNotMatch               = -134
	ErrAccountRequireNewPassword                = -135 // 需要设置一个密码
	ErrAccountSetTaillightMedalidInvalid        = -136 // 你没有该勋章或已过期 不能将其设置为昵称后显示
	ErrAccountMedalNotallowSettaillight         = -137 // 昵称后不能显示该类型的勋章
	ErrAccountWatcherPermissionLimit            = -138 // 权限不足
	ErrAccountToLoginCaptcha                    = -139 // 需要登录验证
	ErrAccountVerifyLoginCaptcha                = -140 // 登录验证码错误
	ErrAccountNoNeedCaptcha                     = -141 // 不需要图片验证码
	ErrAccountNoWechatUnionId                   = -142
	ErrAccountUnionIdExist                      = -143
	ErrAccountBanRegFrequenceLimit              = -144 // 该帐号由于安全问题已被限制注册，请联系客服
	ErrAccountBanAuthFrequenceLimit             = -145 // 该帐号由于安全问题已被限制登录，请联系客服
	ErrAccountInvalid                           = -146 // 账号异常
	ErrAccountRegIpFrequenceLimit               = -147 // 注册帐号IP频率限制
	ErrAccountSearchNewRegContactFrequenceLimit = -148 // 由于安全问题已被限制操作，请联系客服
	ErrAccountSmsThreshholdExceed               = -149 // 发送短信过多，请明天再试
	ErrAccountNoBindedPhoneToUser               = -150 // 该账号未绑定任何手机号
	ErrAccountRebindVerifiedPhoneInvalid        = -151 // 已绑定的手机验证失败
	ErrAccountThirdpartyDetachDisabled          = -152 // 该功能已经下线
	ErrAccountPhoneFormatBanned                 = -153 // 手机号码格式不对
	ErrAccountRegLimitedByClientVersion         = -154 // 请升级客户端至最新版
	ErrAccountThirdpartyIdNotDetachable         = -155 // 该账号无法进行解绑
	ErrAccountDeviceFormatBanned                = -156 // 设备异常
	ErrAccountLoginNeedBindPhone                = -157 // 该账号需要绑定手机号才能登陆，请升级客户端版本至最新
	ErrAccountModifyPwdNeedVerifycode           = -158 // 修改密码需要进行手机验证，请升级客户端版本至最新
	ErrAccountDeviceUnusual                     = -159 // 您的版本过旧，为确保账号安全，需更新为最新版才可继续使用
	ErrAccountDiffAccTryPwdTooMuch              = -160 // 由于该设备登陆密码错误次数过多，该设备5小时内不能登陆
	ErrAccountSameAccTryPwdTooMuch              = -161 // 由于之前一段时间该账户密码错误次数超过5次，该账号5小时内不能登陆
	ErrAccountPwdNullErr                        = -162 // 未设置密码
	ErrAccountBannedForever                     = -163 // 该帐号已被冻结
	ErrAccountLoginNxLimit                      = -164 // 请勿尝试同时登录多设备
	ErrAccountRegNeedBindPhone                  = -165 // 需要绑定手机号才能注册，请升级客户端版本至最新
	ErrAccountLoginNeedBindRealnameauthPhone    = -166 // 根据国家相关政策要求,登录需要填写手机号，请升级客户端版本至最新
	ErrAccountUnregister                        = -167 // 用户已注销
	ErrAccountLoginBindRealnameauthPhoneLimit   = -168 // 此手机号已认证十个账号，请使用其它手机号进行认证
	ErrAccountBindLoginPhoneIsExpected          = -169 // 此手机号未绑定至任何账户，请重试并绑定为登录手机
	ErrAccountBindSecurePhoneIsExpected         = -170 // 此手机号已经绑定至其他账户，请重试并绑定为安全手机
	ErrAccountBindSecurePhoneLimit              = -171 // 此手机号已被10个账号绑定，请使用其它手机号操作
	ErrAccountRegBannedByPolicy                 = -172 // 您的手机号码或IP所属区域暂不支持注册
	ErrAccountBannedWithInfo                    = -173 // 该帐号由于安全问题已被暂时冻结，请联系客服
	ErrAccountBannedWithNotLoggedIn             = -174 // 由于您过长时间未登录，该账号已被冻结封禁
	ErrAccountSameAccountTryPwdTooMuch          = -175 // 由于之前一段时间该账户密码或用户名错误超过10次, 该账号当天以内不能登陆
	ErrAccountLoginFail                         = -176 // 账号或密码错误，如忘记密码请尝试使用手机验证码登录
	ErrAccountAbnormalFunctionInvalid           = -177 // 您被判定为异常账号，无法使用该功能
	ErrAccountAbnormalFunctionInvalid2          = -178 // 该功能仅对部分用户开放，敬请谅解
	ErrAccountThirdPartyApiFail                 = -179 // 第三方接口调用失败
	ErrAccountRebindPhoneNotAllowInGameSdk      = -180 // 如需换绑手机号，请回到TT语音App内操作
	ErrAccountWaitUnregister                    = -181 // 账号处于注销流程中
	ErrAccountBlackSample                       = -182 // 您的账号存在异常，请联系客服进行处理
	ErrAccountBannedBlack                       = -183 // 该帐号由于安全问题已被暂时冻结，请联系客服
	ErrAccountSuspectFraud                      = -184 // 您操作的次数太多啦，请稍后再试
	ErrAccountPcAuthNeedApply                   = -185 // 您的账号在尝试电脑登录，请在APP进行此操作
	ErrAccountPcAuthNeedApplyExpired            = -186 // 登录已超时
	ErrAccountPcAuthNeedApplyReject             = -187 // 登录验证失败，请重试
	ErrAccountNeedStealingVerifyCode            = -188 // 登录失败，请通过验证码登录
	ErrAccountStealingVerifyOption              = -189 // 登录失败，请通过验证
	ErrAccountStealingVerifyNeedFace            = -190 // 登录失败，需要进行人脸认证解除限制
	ErrAccountPhoneIsRisk                       = -191 // 当前手机号存在风险，请更换再试
	ErrAccountIdcardIsRisk                      = -192 // 当前身份证号存在风险，请更换再试
	ErrAccountRebindPhoneFraud                  = -193 // 检测近期改绑频繁，请过几天再尝试
	ErrAccountNotLogin                          = -194 // 账号未登录

	// IM_MSG -200
	ErrSendMsgTargetNotExist                 = -201
	ErrSendMsgTargetNotFriend                = -202
	ErrSendMsgTimelineSvrFailed              = -203
	ErrSendMsgUploadAttachmentSvrFailed      = -204
	ErrSendMsgDownlAttachmentSvrFailed       = -205
	ErrSendMsgDownlAttachmentExceed          = -206 // 附件已失效
	ErrSendMsgAttachmentInvalid              = -207
	ErrSendMsgAttachmentInvalidUploadAlready = -208
	ErrSendMsgAttachmentGenSmallImgFailed    = -209
	ErrSendMsgNotGroupMember                 = -210 // 你不在该群，发送失败
	ErrSendMsgUpdateGroupMsgFailed           = -211
	ErrSendMsgParasErr                       = -212 // 发消息参数不对
	ErrSendMsgMute                           = -213 // 你被禁言了，发送失败
	ErrSendMsgAllMute                        = -214 // 全员禁言中，只有管理员可以发言
	ErrSendMsgBuildSystemMsgErr              = -215
	ErrSendMsgGuildLimit                     = -216 // 客官你今天公会内的自由聊天人数已经用完，想要继续搭讪请先添加玩伴吧~
	ErrSendMsgQuitGuildFar                   = -217 // 对不起，已超出挽回聊天有效时间，发起会话失败。
	ErrSendMsgUpdatePulibMsgFailed           = -218
	ErrSendMsgGenerateAttachmentKeyFailed    = -219
	ErrSendMsgAtEveryoneNotAllow             = -220 // 该用户没有发送@全体消息的权限
	ErrSendMsgAtEveryoneTimesToomuch         = -221 // 该用户发送@全体消息的次数超过限制
	ErrSendMsgStrangerTimesToomuch           = -222 // 对方未回复前只能发送5条消息哦~
	ErrSendMsgStrangerTargetsToomuch         = -223 // 你今天发起的聊天有点多哦~先和刚认识的小伙伴聊聊吧
	ErrSendMsgStrangerUnusualAccount         = -224 // 你的账号异常，无法和陌生人打招呼呢~
	ErrSendMsgStrangerIpTargetsToomuch       = -225 // 你的IP已达到陌生人打招呼次数上限
	ErrSendMsgCantTalkWithGmHelper           = -226 // 你不是会长，不能和会长小秘聊啦~
	ErrSendMsgIsBlockByTarget                = -227 // 发送失败，对方已拒收
	ErrSendMsgBlockTarget                    = -228 // 你已拉黑对方，对方无法接收你的信息
	ErrSendMsgSensitive                      = -229 // 发送失败，不符合平台管理规则
	ErrUserAntispamAccountBannedPost         = -230 // 您的账号目前处于禁言期，请稍后再尝试
	ErrSendMsgStrangerTimesToomuchTimeLimit  = -231 // 发送失败，你最近发起聊天过于频繁，请稍后再试
	ErrSendMsgStrangerTargetRevToomuch       = -232 // 发送失败~今天有很多人找Ta聊天啦~
	ErrSendMsgStrangerReportedToomany        = -233 // 由于你被频繁举报，账号存在异常，60分钟内不能发起私聊，如有疑问，请联系客服
	ErrSendMsgTooManyUsersChat               = -234 // 发送失败，今天有很多用户找ta聊天啦
	ErrSendMsgCanNotRcvMsg                   = -251 // 对方暂不接收私聊信息喔

	// 师徒关系相关
	ErrSendMsgMasterapprenticeYoualreadybound     = -235 // 你已经和对方绑定啦～
	ErrSendMsgMasterapprenticeUpperlimit          = -236 // 发送失败，你绑定的徒弟数已达到上限啦
	ErrSendMsgMasterapprenticeAccountabnormal     = -237 // 对方账号异常，不能绑定为徒弟哟
	ErrSendMsgMasterapprenticeAccountnotsatisfied = -238 // 对方不满足收徒条件，不能绑定为徒弟，您可到<徒弟大厅>中收徒
	ErrSendMsgMasterapprenticeTargetalreadybound  = -239 // 绑定失败，对方已被绑定其他师父啦，您可到<徒弟大厅>中收徒
	ErrSendMsgMasterapprenticeIndev               = -240 // 此功能维护中，敬请期待
	ErrSendMsgMasterapprenticeAlreadyfollow       = -241 // 你已经关注Ta啦
	ErrSendMsgMasterapprenticeEntranceclosed      = -242 // 入口已关闭
	ErrSendMsgMasterapprenticeAlreadyinvited      = -243 // 你刚刚已邀请过Ta啦
	ErrSendMsgMasterapprenticeAlreadyapprentice   = -244 // 已成功收Ta为徒啦，快去完成每日互动任务吧
	ErrSendMsgMasterapprenticeOver                = -245 // 五日师徒任务已完成，Ta已经出师啦，不能重复收徒哦
	ErrSendMsgMasterapprenticeTerminal            = -246 // 你们没有连续完成每日互动任务，师徒关系已解绑，不能再次收Ta为徒哦
	ErrSendMsgMasterapprenticeNotnewdevice        = -247 // 对方不是新设备，不能绑定为徒弟哦
	ErrUserAntispamAccountBannedPostV2            = -248 // 您的账号目前处于禁言期，请稍后再尝试
	ErrUserAntispamAccountTakeHoldMicBanned       = -249 // 你抱的用户由于违规被禁言
	ErrUserAntispamAccountBannedPostV3            = -250 // 您的账号目前处于禁言期，请稍后再尝试
	ErrUserAntispamUnbanAllFailed                 = -255 // 一级封禁，解封失败
	ErrUserAntispamUnbanUserFailed                = -256 // 一级封禁，账号解封失败
	ErrUserAntispamUnbanDeviceFailed              = -257 // 一级封禁，设备解封失败

	// SYNC_MSG -300
	ErrSyncMsgTypeErr       = -301
	ErrSyncMsgEmptyMessages = -302

	// FRIEND -400
	ErrFriendAlreadyFriend        = -401
	ErrFriendTargetNotFriend      = -402 // 对方还不是你的好友
	ErrFriendVerifyFriendFailed   = -403
	ErrFriendVerifyMsgExceed      = -404
	ErrFriendDelNotSupported      = -405
	ErrFriendCannotAddYoursefl    = -406
	ErrFriendUpdateRemarkFailed   = -407
	ErrFriendLimit                = -408 // 客官今天发出了很多添加玩伴的请求了，添加玩伴的体力用完了哦~
	ErrFriendKefuLimit            = -409 // 客服帐号不能加好友
	ErrFriendGonghuiKefuLimit     = -410 // 公会客服帐号不能加好友
	ErrFriendBaned                = -411 // 由于对方的设置，你不能添加对方为玩伴
	ErrFriendTotalSizeLimit       = -412 // 你的好友数量已达到500人上限
	ErrFriendTargetTotalSizeLimit = -413 // 对方的好友数量已达到500人上限
	ErrFriendIpLimit              = -414 // 客官你的IP今天发出了很多添加玩伴的请求了，添加玩伴的体力用完了哦~

	// GUILD -500
	ErrGuildNameExist                   = -500 // 会长大人，该名字已经被抢注，赶紧换个更霸气的名字吧。
	ErrGuildNotExist                    = -501 // 公会不存在
	ErrGuildShortIdSet                  = -502 // 该公会已经设置过短号，不能重新设置
	ErrGuildShortIdExist                = -503 // 该短号已被使用
	ErrGuildUserHave                    = -504 // 用户已加入公会了
	ErrGuildNoPermission                = -505 // 无权限操作
	ErrGuildApplyExist                  = -506 // 用户已发起加入公会的申请
	ErrGuildApplyHaveReviewed           = -507 // 已被其他管理员审核通过
	ErrGuildApplyHaveConfirmed          = -508 // 此申请已经确认通过了
	ErrGuildMemberNotExist              = -509 // 非公会成员
	ErrGuildApplyNotExist               = -510 // 用户未发起对加入公会的申请
	ErrGroupMemberNotExist              = -511 // 不是群成员
	ErrGroupMemberExist                 = -512 // 已经是群成员
	ErrGroupNotExist                    = -513 // 群不存在
	ErrGroupOwnerCannotBeRemoved        = -514 // 群主不能退群
	ErrGuildOwnerCannotBeRemoved        = -515 // 会长不能退出公会
	ErrGroupCannotJoin                  = -516 // 此群禁止任何人加入
	ErrGroupApplyExist                  = -517 // 用户已发起加入群的申请
	ErrGroupApplyNotExist               = -518 // 用户未发起对加入群的申请
	ErrGroupApplyHaveReviewed           = -519 // 已被其他管理员审核通过
	ErrGroupUserHaveBeenOwner           = -520 // 用户已经是群主
	ErrGuildAddGameExist                = -521 // 该游戏已经是群游戏了
	ErrGroupDeleteMemberSelf            = -522 // 踢出群用户列表中包含自己
	ErrGuildDeleteMemberSelf            = -523 // 踢出公会用户列表中包含自己
	ErrGroupMuteMemberSelf              = -524 // 禁言用户列表不能包含自己
	ErrGroupUnmuteMemberSelf            = -525 // 恢复发言用户列表不能包含自己
	ErrGroupCannotExitGuildMainGroup    = -526 // 不能退出公会总群
	ErrGroupParamErr                    = -527 // 参数错误
	ErrGuildGameNotExist                = -528 // 非公会主玩游戏
	ErrGuildNoticeDuplicateDelete       = -529
	ErrGroupUserNotOwner                = -530 // 此用户不是群主
	ErrGuildCantDismissMemberToMuch     = -531 // 解散公会，必须公会成员总数少于10人
	ErrGuildAlreadyJoin                 = -532 // 用户已经加入公会
	ErrGuildSetGameUrlInvalid           = -533 // 设置游戏链接，URL不正确
	ErrGuildIdNotMatch                  = -534 // 公会id不正确
	ErrGuildSetGameUrlCantReach         = -535 // 已设置成功，但您设置的链接可能无法访问，请确认哦。
	ErrGuildSetGameUrlNotApk            = -536 // 已设置成功，但该链接可能不是安卓游戏下载链接，请确认哦。
	ErrGuildApplyUserJoinOtherGuild     = -537 // 申请已失效，或用户已加入其它公会
	ErrGuildApplyPartedSendFailed       = -538
	ErrGuildMainGroupNoOwner            = -539 // 总群不能设置群主
	ErrGuildPunishPinNoPermission       = -540 // 无权限发布图钉广播
	ErrGuildNameSensitive               = -541 // 你输入的公会名包含敏感词，修改失败
	ErrGuildGroupNameSensitive          = -542 // 你输入的群名称包含敏感词，修改失败
	ErrGuildDescriptionSensitive        = -543 // 你输入的群简介包含敏感词，修改失败
	ErrGuildMemberCardSensitive         = -544 // 你输入的群名片包含敏感词，修改失败
	ErrGuildNameContainsGameName        = -545 // 你输入的公会名称中包含游戏名，无法创建
	ErrGuildNameAllDigit                = -546 // 无法使用纯数字作为公会名
	ErrGuildHadOnceJoinGuild            = -547 // 快速加入失败
	ErrGuildAddGameExceedLimit          = -548 // 公会游戏已达上限
	ErrGuildNameNotMatch                = -549 // 公会名称不对
	ErrGuildNameMapErr                  = -550 // 公会名称映射错误
	ErrGroupMemberCountLimit            = -551 // 临时群已满员
	ErrGuildNameInvalid                 = -552 // 公会名称含有非法字符
	ErrGuildUserHavebeenHighadminlevel  = -553 // 目标用户已经被设置为更高权限
	ErrGroupMemberCardOffline           = -554 // 修改群名片失败
	ErrGroupAddmemberLimit              = -555 // 每次拉人加群不能超过50人上限
	ErrGroupApplyExceed                 = -556 // 该入群申请已过期
	ErrGuildCreateFrequenceLimit        = -557 // 当天不能创建多个公会
	ErrGuildNotCheckin                  = -558 // 今天未签到
	ErrGuildCheckinSupplement           = -559 // 没有补签天数
	ErrGroupDismissed                   = -560 // 该群已被解散
	ErrGuildRefuseQuitJointimeShort     = -561 // 加入公会满30分钟后，才可以退出公会哦~~ 在公会里多玩一会儿吧，小伙伴们舍不得你走呢
	ErrGuildAlreadyDonate               = -562 // 今天已捐献
	ErrGuildExtraGameExceedLimit        = -563 // 额外扩充游戏数量已达上限，公会等级提升后可扩充更多数量
	ErrGuildContributionNotEnough       = -564 // 公会可用贡献值不足
	ErrGuildUserIsOfficial              = -565 // 用户已经是官员
	ErrGuildUserNotOfficial             = -566 // 用户不是官员
	ErrGuildOfficialNotExist            = -567 // 职位不存在
	ErrGuildMemberListRankTypeErr       = -568
	ErrGuildStoragePNotShelve           = -569 // 商品还没上架
	ErrGuildStoragePSoldout             = -570 // 商品已经卖完
	ErrGuildStoragePHadModify           = -571 // 商品价格已发生变化，请刷新再试
	ErrGuildStoragePTotalError          = -572 // 商品的总数与礼物列表不匹配
	ErrGuildStorageHasNotExamineRecord  = -573 // 找不到审查记录
	ErrGuildStorageAddPError            = -574 // 添加商品失败
	ErrGuildDeductMemContributionFail   = -575 // 扣除个人贡献失败
	ErrGuildReturnMemContributionFail   = -576 // 返回个人贡献失败
	ErrGuildStorageAddGiftBoxFail       = -577 // 放入个人宝箱失败
	ErrGuildStoragePNotEnough           = -578 // 商品数量不足，快去【礼包中心】补货吧~
	ErrGuildStoragePUserCanNotGain      = -579 // 你还没有领取资格
	ErrGuildAlreadyCheckIn              = -580 // 今天已签到
	ErrGuildStoragePAllotInvalid        = -581 // 分配操作无效
	ErrGuildStoragePPassDu              = -582 // 领取失败，礼包领取尚未开始或者已经过期
	ErrGuildMemberCardGuildLimit        = -583 // 该成员已退出公会
	ErrGuildPrefixIsTooLong             = -584 // 马甲长度超过限制
	ErrGuildMemberOptCdUid              = -585 // 用户已达到当天操作次数上限
	ErrGuildMemberOptCdIp               = -586 // IP已达到本时段内操作次数上限
	ErrGuildMemberOptCdDevice           = -587 // 设备已达到当天操作次数上限
	ErrGuildOfficialExist               = -588 // 职位已经存在
	ErrGuildSpiltGiftCardValueOverLimit = -589 // 切分的礼品卡单张面额超过了限制
	ErrGuildStorageVoucherPermission    = -590 // 此商品只有会长才能操作
	ErrGuildStoragePNotExist            = -591 // 商品不存在
	ErrGuildStoragePDeleteConditionFail = -592 // 只能删除已过期的商品
	ErrGuildGiftCardBalanceNotEnough    = -593 // 礼品卡余额不足
	ErrGuildGiftCardCantNotUnshelve     = -594 // 本版本不支持代金券下架操作，请先检查升级
	ErrGuildGiftCardFoundNot            = -595 // 找不到礼品卡
	ErrGuildGiftCardStatus              = -596 // 礼品卡状态异常
	ErrGuildGiftCardRemoveFail          = -597 // 只能删除过期和状态异常的礼品卡
	ErrGuildGiftCardRepeat              = -598 // 该代金券每个用户只能领取一次
	ErrGuildManifestoSensitive          = -599

	//  Game [-600, -649]
	ErrGameNotExist              = -600 // 游戏不存在
	ErrGameFuzzyMatchNotReady    = -601
	ErrGameServerNotExist        = -602
	ErrGameTopgameNotExist       = -603
	ErrGameTopgameExist          = -604
	ErrGameZoneNotExist          = -605
	ErrGameBindedOfficialTgroup  = -606
	ErrGameZoneSetNotEnoughParam = -607
	ErrGameTagNumLimit           = -608

	//  Guild [-650, -699]
	ErrGuildOfficialMemberReachLimit = -650 // 您只能设置50个公会管理
	ErrGuildMemberReachLimit         = -651 // 公会成员已经达上限，无法加入
	ErrGuildModifyPrefixLimit        = -652 // 30分钟内只能修改一次成员马甲，请稍候再试。
	ErrGuildSensitive                = -653 // 输入的内容包含敏感词，修改失败

	// ACCOUNT -700
	ErrAuthAutoLoginAlreadyOnline = -700 // 该帐号已在其它设备登录，请手动登录
	ErrUploadFaceAccountTypeErr   = -701 // 上传头像，帐号类型错误
	ErrAuthAutoLoginFailed        = -702 // 自动登录失败，请手动登录
	ErrThirdPartyAccessTokenErr   = -703 // 第三方账号token验证失败
	ErrImageInvalidErr            = -704 // 图片涉嫌违规
	ErrChinaMobileAccessTokenErr  = -705 // 中国移动一键登录token验证失败
	ErrChinaMobileNotfindPhone    = -706 // 中国移动一键登录没有匹配到手机号
	ErrChinaUnicomAccessTokenErr  = -707 // 中国联通一键登录token验证失败
	ErrChinaUnicomNotfindPhone    = -708 // 中国联通一键登录没有匹配到手机号
	ErrChuangLanAccessTokenErr    = -709 // 创蓝token验证失败
	ErrChuangLanNotfindPhone      = -710 // 创蓝没有匹配到手机号
	ErrAppleAuthCodeFail          = -711 // AppleID验证失败

	// album -800
	ErrAlbumNotExist               = -800 // 相册不存在
	ErrPhotoNotExist               = -801 // 相片不存在
	ErrAlbumDefaultAlbumCantDelete = -802 // 默认相册不允许删除
	ErrNoPermissionDeleteAlbum     = -803 // 你没有权限删除该相册
	ErrNoPermissionDeletePhoto     = -804 // 你没有权限删除该照片
	ErrAlbumNoPermission           = -805 // 没权限修改相册

	// 动态头像 -880
	ErrHeadDynamicImageNoPermission = -881 // 没有动态头像权限

	// giftpkg -900
	ErrGiftpkgGuildPkgNotEnough      = -900 // 公会礼包不足
	ErrGiftpkgPkgNotFound            = -901 // 找不到这种礼包
	ErrGiftpkgAlreadyApplying        = -902 // 这种礼包公会已经在申请中
	ErrGiftpkgApplyidDoneOrNotExist  = -903 // 该申请已经处理过，或者不存在
	ErrGiftpkgApplyidNotExist        = -904 // 该申请不存在
	ErrGiftpkgApplyidAlreadyDone     = -905 // 该申请已处理过
	ErrGiftpkgPkgNotEnough           = -906 // 后台礼包不足
	ErrGiftpkgApplyNoPermission      = -907 // 无权限申请礼包
	ErrGiftpkgUserHadFetedThisPkg    = -908 // 已经领取过这种礼包
	ErrGiftpkgTaohaoNoUsableId       = -909 // 淘号，已经没有可用的序列号了
	ErrGiftpkgRedPkgEmpty            = -910 // 红包已经被领完了
	ErrGiftpkgStorageSerialNotEnough = -911 // 仓库序列号不足
	ErrGiftpkgApplyHasNoThisGame     = -912 // 公会没有这款游戏，不能申请该礼包
	ErrGiftpkgNotFoundRedPkg         = -913 // 找不到该红包
	ErrGiftpkgRedPkgTargetErr        = -914 // 红包只能发到公会群或者公会好友
	ErrGiftpkgRedPkgGuildErr         = -915 // 你不是该公会的成员，不能领取该公会的红包
	ErrGiftpkgAlreadyFetchRedpkg     = -916 // 已经领取过这个红包
	ErrGiftpkgNotGuildOwner          = -917 // 用户不是会长
	ErrGiftpkgPkgNotUsed             = -918 // 该礼包已经下架
	ErrGiftpkgApplyMemberNotEnough   = -919 // 该款礼包需要公会人数达到5人以上才能申请，你的公会人数不够，赶紧邀请更多的小伙伴加入你的公会吧!
	ErrGiftpkgUserHadFetchedToday    = -920 // 你今天领礼包次数已达到上限，请明天签到后再来吧
	ErrGiftpkgUserNotCheckinToday    = -921 // 公会签到后才可以领礼包哦
	ErrGiftpkgDeviceHadFetchedToday  = -922 // 你今天领礼包次数已达到上限，请明天签到后再来吧
	ErrGiftpkgApplyCdNotReady        = -923 // 申请过于频繁，请稍后再试
	ErrGiftpkgNotMatchPkgSource      = -924
	ErrGiftpkgNotBelongToTheGuild    = -925
	ErrGiftpkgUnableToDelete         = -926
	ErrGiftpkgAlreadyDeleted         = -927
	ErrGiftpkgExceedFetchFailed      = -928 // 领取失败，该礼包已过期
	ErrGiftpkgHadFetedThisSerial     = -929
	ErrGiftpkgOrderidNotFound        = -930
	ErrGiftpkgOrderidDuplicate       = -931
	ErrGiftpkgPriceUpdated           = -932 // 礼包价格已更新
	ErrGiftpkgPriceRangeErr          = -933
	ErrGiftpkgDeviceHadFetched       = -934
	ErrGiftpkgCollectionNotExist     = -935

	// verifycode
	ErrVerifycodeForSensitiveOp   = -936 // 敏感操作需要输入短信验证码，请更新至最新版本进行操作
	ErrNoPhoneBind                = -937 // 该操作需要绑定手机
	ErrVerifycodeSessionChanged   = -938 // 登录信息变化,请重新获取验证码
	ErrVerifycodeNoCode           = -939 // 验证码未生成,请重新获取验证码
	ErrVerifycodeWrongCode        = -940 // 验证码不正确,请重新输入
	ErrGiftpkgDevHadFetedThisPkg  = -941 // 设备已经领取过这种礼包
	ErrVerifycodeParamErr         = -942 // 验证码操作的请求参数有误
	ErrNeedPhoneBind              = -943 // 该操作需要绑定手机,请到【我】-【隐私与安全】中绑定手机或者升级客户端
	ErrVerifycodeWrongCodeToomuch = -944 // 验证码错误过多, 请重新获取验证码
	ErrVerifycodeExceedLimit      = -945 // 今日接收验证码次数已达上限

	//	circle
	ErrCircleNotFound                       = -1000 // 游戏圈不存在
	ErrCircleJoinAlreadyJoined              = -1001 // 已经加入了游戏圈
	ErrCircleTopicDeleted                   = -1002 // 该主题已被删除
	ErrCircleTopicNotExists                 = -1003 // 该主题不存在
	ErrCircleCommentDeleted                 = -1004 // 该评论已被删除
	ErrCircleCommentNotExists               = -1005 // 该评论不存在
	ErrCircleLikeAlreadyLiked               = -1006 // 你已经点过赞了
	ErrCircleLikeNotExists                  = -1007 // 还未赞过
	ErrCircleNotCreator                     = -1008 // 该操作只允许作者进行
	ErrCircleTopicTitleSensitive            = -1009 // 你输入的标题包含敏感词，发布失败
	ErrCircleTopicContentSensitive          = -1010 // 你输入的内容包含敏感词，发布失败
	ErrCircleCommentSensitive               = -1011 // 你输入的内容包含敏感词，发布失败
	ErrCircleNoPermissionMute               = -1012 // 无权限禁言
	ErrCirclePostTopicCoolingdown           = -1013 // 你的操作过于频繁, 请稍后再试
	ErrCirclePostCommentCoolingdown         = -1014 // 你的操作过于频繁, 请稍后再试
	ErrCirclePostSimilarTopic               = -1015 // 你的操作过于频繁, 请稍后再试
	ErrCircleHaveNotJoinCircle              = -1016 // 未加入圈子
	ErrCircleCanNotQuitActivity             = -1017 // 无法退出活动圈
	ErrCircleNoPermissionHightlight         = -1018 // 无权限加精
	ErrCircleNoPermissionCancleHightlight   = -1019 // 无权限删精
	ErrCircleNoPermissionDelTopicComment    = -1020 // 无权限删评论
	ErrCircleUserMuteComment                = -1021 // 你已被禁言，暂时无法发送评论
	ErrCircleMarkReadedFailed               = -1022
	ErrCircleTopicContentEmpty              = -1023
	ErrCircleCommentContentEmpty            = -1024
	ErrCircleUserMuteTopic                  = -1025 // 你已被禁言，暂时无法发送主题
	ErrCircleContentLenOverlimit            = -1026 // 你发的内容太多了，伦家受不了呐
	ErrCircleTitleLenOverlimit              = -1027 // 你标题都这么长，吓死我了，嘤嘤嘤~~~
	ErrCircleAnnouncementSendTopic          = -1028 // 公告圈禁止发主题
	ErrCircleAnnouncementQuit               = -1029 // 禁止退出公告圈
	ErrCircleTopicPictureSensitive          = -1030 // 主题图片包含敏感信息
	ErrCircleAnnouncementJoin               = -1031 // 禁止关注公告圈
	ErrCircleAnnouncementVersion            = -1032 // 当前版本不支持该操作，请更新版本
	ErrCircleTopicNotDeleted                = -1033 // 该主题未被删除
	ErrCircleIsNowReadonly                  = -1034 // T^T游戏圈帖子只支持查看，不支持发帖、点赞、评论等操作哟
	ErrRecruitNotExist                      = -1035 // 招募不存在或者已经结束
	ErrRecruitPostContentSensitive          = -1036 // 招募内容包含敏感词汇
	ErrRecruitPostReddiamonNotEnough        = -1037 // 红钻不够 不能招募
	ErrRecruitPostGuildAlreadyPost          = -1038 // 该公会在这个游戏发过招募还没有结束
	ErrRecruitGuildNotUsercurrent           = -1039 // 您已经不在创建该招募的公会了
	ErrRecruitSupportAlreadyDo              = -1040 // 每天只能为自己公会顶一次
	ErrRecruitOpNoPermission                = -1041 // 没有操作权限
	ErrRecruitPostGuildcontrbutionNotEnough = -1042 // 贡献值不够 不能招募

	//Grow
	ErrGrowMissionNotExists               = -1100
	ErrGrowMissionNotBelongToUser         = -1101
	ErrGrowUserMissionNotExists           = -1102
	ErrGrowUserMissionNotFinished         = -1103
	ErrGrowUserMissionCollected           = -1104 // 奖励已领取
	ErrGrowExpAdded                       = -1105
	ErrGrowCurrencyAdded                  = -1106 // 用户红钻已经添加，不能重复添加
	ErrGrowCurrencyNotEnough              = -1107 // 你的红钻数量不足，赶紧去做任务领红钻吧～
	ErrGrowExpZero                        = -1108
	ErrGrowCurrencyZero                   = -1109
	ErrGrowUserMissionExpired             = -1110
	ErrGrowLevelNotEnough                 = -1111
	ErrGrowMissionNotTimeLimit            = -1112
	ErrGrowMissionAccepted                = -1113 // 任务已接受
	ErrGrowMissionUnacceptable            = -1114 // 任务不可接受
	ErrGrowMissionNotAccepted             = -1115
	ErrGrowMissionNotEffect               = -1116
	ErrGrowMissionEventNoOneCare          = -1117
	ErrGrowMissionSameDayOper             = -1118
	ErrReddiamondRewardAboveLimit         = -1119
	ErrReddiamondNotEnoughInStock         = -1120 // 红钻池红钻不足
	ErrReddiamondFreezeDuplicateOrder     = -1121
	ErrReddiamondFreezeNoSuchOrder        = -1122
	ErrReddiamondFreezeConfirmNotAllowed  = -1123
	ErrReddiamondFreezeConfirmAlreadyDone = -1124
	ErrTimeLimitMissionConfigNotExist     = -1171 // 限时任务不存在
	ErrTimeLimitMissionEventNotExist      = -1172 // 限时任务类型不存在
	ErrTimeLimitMissionConfigParam        = -1173 // 限时任务配置参数异常
	ErrTimeLimitMissionAcceptLimit        = -1174 // 该任务的领取名额已满
	ErrTimeLimitUserMissionNotExist       = -1175 // 用户限时任务不存在
	ErrTimeLimitUserMissionAccepted       = -1176 // 用户限时任务已接受

	//	medal
	ErrMedalConfitNotExists      = -1200
	ErrMedalUserMedalNotExists   = -1201
	ErrMedalUserMedalOrderExists = -1202 // 发勋章订单重复

	// push_message
	ErrPushMessageNotExists         = -1300 // 推送信息不存在
	ErrPushMessageReviewed          = -1301 // 消息之前已经审核过了
	ErrPushMessageRefused           = -1302 // 消息已经被拒绝
	ErrPushMessageDeleted           = -1303 // 消息已被删除
	ErrPushMessageSent              = -1304
	ErrPushMessageOperationUndefine = -1305
	ErrPushParamError               = -1306 // 推送参数错误
	ErrPushWithoutBroadcast         = -1320 // 无推送筛选条件，如需广播请指定广播模式

	// public account
	ErrPublicAccountNotExists         = -1400
	ErrPublicAccountInvalidBizType    = -1401
	ErrPublicNoConfig                 = -1402
	ErrPublicCanNotUnsubSystemAccount = -1403 // 无法取消关注该公众号

	// session (room)
	ErrSessionRoomNotExists        = -1500 // 房间不存在
	ErrSessionRoomUserInOtherRoom  = -1501 // 用户已经在其它房间
	ErrSessionRoomUserNotInRoom    = -1502 // 用户不在此房间内
	ErrSessionRoomUserNotInAnyRoom = -1503 // 用户不在任何房间
	ErrSession1v1NotFriend         = -1504 // 和对方不是好友
	ErrSessionCallinWaitCoolDown   = -1505 // 召集令冷却中
	ErrSessionGroupNoCallin        = -1506 // 这个群当前没有召集令
	ErrSessionAlreadyAcceptCallin  = -1507 // 用户已经接受了召集了
	ErrSessionCallinNotEnd         = -1508 // 召集令尚未结束
	ErrSessionCallinTooFrequency   = -1509 // 发起召集令过于频繁
	ErrSessionRoomUserExceed       = -1510 // 当前开黑人数已满

	// token
	ErrTokenUnsupportedBiz      = -1600
	ErrTokenBadToken            = -1601 // token无效或者已经过期, 请重新登录
	ErrTokenNoValidSeed         = -1602
	ErrTokenIsRequired          = -1603 // token无效, 请重新登录
	ErrTokenWasExpired          = -1604 // token已过期, 请重新登录
	ErrTokenInvalidRefreshToken = -1605 // 无效的refresh token

	// tgroup
	ErrTgroupCreateLimitExceed      = -1700 // 用户创建的兴趣群数量已经超过上限
	ErrTgroupInvalidTgroupAccount   = -1701 // 无效的群帐号
	ErrTgroupNoPermission           = -1702 // 无权限操作
	ErrTgroupOwnerCanntQuit         = -1703 // 群主不允许退群
	ErrTgroupCreateNeedInviteFriend = -1704 // 需要邀请一个好友才能创建群组
	ErrTgroupTargetInvalid          = -1705 // 操作对象错误
	ErrTgroupDisplayIdNotExist      = -1706 // 群不存在
	ErrTgroupJoinGroupLimit         = -1707 // 用户加入的兴趣群过多
	ErrTroupUserBeenKicked          = -1708
	ErrTgroupInvalidInvite          = -1709 // 非法的邀请
	ErrTgroupSensitive              = -1710 // 输入的内容包含敏感词，修改失败

	// presence
	ErrPresExist               = -1800
	ErrSessionNotExist         = -1801 // 会话不存在
	ErrSessionExpired          = -1802 // 会话过期
	ErrPresInvalidProxyAddress = -1803 // 错误的proxy地址

	//websession  -1850 -> -1899
	ErrWebsessionServerError  = -1851 // 系统错误
	ErrWebsessionInvalidCode  = -1852 // Invalid auth code
	ErrWebsessionInvalidToken = -1853 // Invalid token
	ErrWebsessionInvalidScope = -1854 // Invalid scope

	// UserSetting
	ErrUserSetting   = -1900
	ErrSettingEmpty  = -1901
	ErrResultUidDiff = -1902
	ErrUnknowType    = -1903

	// Mall
	ErrMall                                  = -2000
	ErrMallNoProduct                         = -2001 // 找不到商品
	ErrMallItemTypeMismatch                  = -2002 // 物品类型不匹配
	ErrMallInvalidCategory                   = -2003 // 无效的Category
	ErrMallInvalidItemType                   = -2004 // 无效的物品类型
	ErrMallNoEnoughStorage                   = -2005 // 库存不足
	ErrMallActivityTimeOverlappedWithAnother = -2006 // 活动与另一个活动冲突
	ErrMallNoSuchActivity                    = -2007 // 找不到相应的活动
	ErrMallActivityCanNotBeUpdated           = -2008 // 活动当前无法被修改
	ErrMallInvalidConfig                     = -2009 // 活动配置有问题
	ErrMallCurrentActivityNone               = -2010 // 当前没有活动
	ErrMallNotCurrentActivity                = -2011 // 非当前活动
	ErrMallActivityNotOpen                   = -2012 // 活动还未开始
	ErrMallActivityClosed                    = -2013 // 活动已经结束
	ErrMallNoSuchProductItem                 = -2014 // 找不到相应的物品
	ErrMallCurrentActivityInProgress         = -2015 // 当前有活动在进行
	ErrMallFailedToParseProductItem          = -2016 // 无法解析物品数据
	ErrMallFailedToParseOrderId              = -2017
	ErrMallUserHadMedal                      = -2018 // 你已经拥有该勋章
	ErrMallNoSuchOrderId                     = -2019
	ErrMallNoLotteryChance                   = -2020
	ErrMallUserHaveAlreadyPurchasedProduct   = -2021 // 你已经在本次活动中购买过该商品了

	// Channel
	ErrChannel                              = -2100
	ErrChannelNameLenError                  = -2101 // 房间名称长度不正确
	ErrChannelBindtypeInvalid               = -2102 // 创建的房间类型不匹配
	ErrChannelNotExist                      = -2103 // 房间已经被删除
	ErrChannelBindinfoNothaveChannel        = -2104
	ErrChannelCacheErr                      = -2105
	ErrChannelMicrophoneOverlimit           = -2106 // 上麦人数达到上限
	ErrChannelNameSensitive                 = -2107 // 不能使用敏感词作为房间名
	ErrChannelNameAllDigit                  = -2108 // 无法使用纯数字作为房间名
	ErrChannelBindGuildIdErr                = -2109
	ErrChannelBindGuildNotMember            = -2110 // 用户不是该房间所属公会成员
	ErrChannelNoPermission                  = -2111 // 权限不足
	ErrChannelMemberOverlimit               = -2112 // 房间内成员数目达到上限
	ErrChannelGuildChannelSizeOverlimit     = -2113 // 公会内开黑房间总数达到上限
	ErrChannelRegRelationFailed             = -2114
	ErrChannelUserAlreadySetMuted           = -2115 // 用户已经被禁言
	ErrChannelUserAlreadyHoldMicrophone     = -2116 // 用户已经在麦上
	ErrChannelUserChannelMuted              = -2117 // 你已经被禁言
	ErrChannelUserNotJoinAnyChannel         = -2118 // 用户没有加入任何房间
	ErrChannelUserNotJoinThisChannel        = -2119 // 用户不是该房间成员,请尝试退出房间后重新进入
	ErrChannelMicrophoneEntryAlreadyDisable = -2120 // 麦位已经被关闭
	ErrChannelMicrophoneEntryAlreadyEnabled = -2121 // 麦位已经被启用
	ErrChannelInLockScreenStat              = -2122 // 房间公屏处于锁定状态 不允许发公屏消息
	ErrChannelUserNotHoldMicrophone         = -2123 // 用户没有上麦
	ErrChannelMicmodeNotsupportOper         = -2124 // 房间当前模式不支持该操作
	ErrChannelTopicinfoSensitive            = -2125 // 房间话题包含敏感词
	ErrChannelMicrophoneDisable             = -2126 // 麦位已经被管理员禁用
	ErrChannelUserMicrophoneKickPunishTime  = -2127 // 你被管理员设为旁听 暂时无法上麦
	ErrChannelUserChannelKickPunishTime     = -2128 // 你被管理员踢出房间 暂时无法进入
	ErrChannelMuteTargetHaveduty            = -2129 // 用户是公会管理员 不能被禁言
	ErrChannelMicrophoneMute                = -2130 // 你已经被禁言 暂时无法上麦
	ErrChannelUnmuteTargetHaveduty          = -2131 // 普通管理员没有权限对其他管理员解除禁言
	ErrChannelPwdInvaild                    = -2132

	//密码错: android 使用CHANNEL_PWD_WRONG, ios使用CHANNEL_HAVE_ALREAD_SET_PWD
	ErrChannelPwdWrong                    = -2133 // 该房间已经上锁，当前版本不支持房间密码输入，请更新APP
	ErrChannelHaveAlreadSetPwd            = -2134
	ErrChannelMicModeNotSupport           = -2135 // 该房间处于当前版本不支持的模式下,请将应用升级到最新版本
	ErrChannelMusicNotHoldMic             = -2136 // 上麦后才能播放背景音乐哦
	ErrChannelSearchTimeOverlimit         = -2137 // 哎呀您慢点啊~~ 手速这么快~~伦家受不了的哟
	ErrChannelMicModeAlreadySet           = -2138 // 您已经处于该模式下了哦
	ErrChannelBindtypeClientNotSupport    = -2139 // 您当前版本不支持进入该类型房间,请升级到最新版本
	ErrChannelOperPermissonUnderTarget    = -2140 // 这里的人等级都好高～伦家打不过啦
	ErrChannelMusicKuwoApiErr             = -2141 // 战歌服务正在努力修复中
	ErrChannelPwdInvalid                  = -2142 // 输入的房间密码无效
	ErrChannelMsgAttachmentExpire         = -2143 // 数据已经过期
	ErrChannelMsgAttachmentFormatInvalid  = -2144 // 房间不支持发送该格式数据或者数据大小超过限制
	ErrChannelConveneCdLmt                = -2145 // 房间召集CD中
	ErrChannelConvening                   = -2146 // 房间正在召集
	ErrChannelNotConvening                = -2147 // 当前房间没有召集哦
	ErrChannelMemberConfirmStatus         = -2148 // 响应召集失败
	ErrNoUserChannelConvene               = -2149 // 没有在召集的房间哦
	ErrChannelMicrSpaceIdInvalid          = -2150 // 麦位信息有误
	ErrChannelMicrSpaceInfoEmpty          = -2151 // 找不到麦位数据
	ErrChannelMicrSpaceNotallowHold       = -2152 // 该麦位被关闭或麦上有人
	ErrChannelMicModeChangeFromFun        = -2153 // 娱乐房人数超过500人时，不能切换为开黑房哦
	ErrChannelPersonalAdminCntLimit       = -2154 // 管理员数量已经达到上限
	ErrChannelTagIdIsReserved             = -2155 // 该标签指暂不允许设置
	ErrChannelCollectNumOverLimit         = -2156 // 你收藏的房间太多了
	ErrChannelTrafficAdmin                = -2158 // 房间当前人数过多，请稍后再试
	ErrChannelSetBackgroundFail           = -2159 // 房间背景设置失败
	ErrChannelSetBackgroundBgExpire       = -2160 // 背景已失效
	ErrChannelSetBackgroundBgSubtypeLimit = -2161 // 专属背景不能在此玩法使用哦
	ErrChannelCollectGuildOwner           = -2168 // 你是会长，不能取消收藏哦
	ErrChannelCollectManager              = -2169 // 你是房间管理，不能取消收藏哦
	ErrChannelMusicListEmpty              = -2170 // 歌单为空
	ErrChannelMusicCountLimit             = -2171 // 已经达到歌单上限
	ErrChannelMusicCannotShare            = -2172 // 房间当前禁止分享音乐
	ErrChannelMusicHoldNomic              = -2173 // 分享失败,你当前不在麦上
	ErrChannelMusicMusicNotExist          = -2174 // 歌曲不存在,可能已经被删除
	ErrChannelRemoveCollectFailed         = -2175 // 取消收藏失败
	ErrChannelFungameAlreadyStart         = -2180 // 房间已经在进行小游戏
	ErrChannelInFunMicMode                = -2181 // 房间处于娱乐模式,请升级到最新版本才可以操作
	ErrChannelMicrophoneEntryAlreadyMute  = -2182 // 麦位已经被闭麦
	ErrChannelInvalidTagId                = -2183 // 不存在此标签
	ErrChannelRefreshCd                   = -2184 // 刷新太快，请稍后
	ErrChannelTmpAllocPoolEmpty           = -2185 // 临时房间不足
	ErrChannelTmpAllocTypeInvalid         = -2186 // 不能分配该类型的临时房间
	ErrChannelTmpAllocNotExist            = -2187 // 临时房间不存在或者已经解散
	ErrChannelMicModeVersionNotSupport    = -2188 // 房间处于新版布局中,请升级到最新版本才可以操作
	ErrChannelSwitchAttachmentMsg         = -2189 // 该房间暂时不允许发图片哦
	ErrChannelSwitchLevelLmt              = -2190 // 该房间暂时不允许新人发言哦
	ErrChannelSwitchLiveConnectMicLmt     = -2191 // 当前主播没有开启连麦哦
	ErrChannelLiveConnectMicApplyCountLmt = -2192 // 当前申请连麦人数已经达到上限，不能继续申请哦
	ErrChannelLiveConnectMicCountLmt      = -2193 // 当前连麦人数已经达到上限，不能继续连麦哦
	ErrChannelLiveNotStarting             = -2194 // 别急嘛,主播还没有开播哦
	ErrChannelHcNoHoldmic                 = -2195 // HC频道不支持上麦操作
	ErrChannelInDatinggameMicMode         = -2196 // 房间处于相亲模式,请升级到最新版本才可以操作
	ErrChannelIsRecommendOpLimit          = -2197 // 房间被加入推荐库中，不能执行该操作
	ErrChannelOpCdOverlimit               = -2198 // 哎呀您慢点啊~~ 手速这么快~~伦家受不了的哟
	ErrChannelCollectCd                   = -2199 // 操作太快啦，一会儿再来试试

	// SDK
	ErrSdkConfigNotValid = -2200
	ErrSdkConfigNotFound = -2201

	//
	ErrChannelQueueUpMicSwitchLmt               = -2250 // 房间排麦功能没有开启哦
	ErrChannelQueueUpMicApplyCountLmt           = -2251 // 当前申请排麦的人数已经达到上限，不能继续申请哦
	ErrChannelQueueUpMicApplyAlready            = -2252 // 你已经申请过排麦了，请等待主持人的处理
	ErrChannelLockedConveneUnavailable          = -2253 // 锁房状态下不可召集
	ErrChannelMiniGameUnavailable               = -2254 // 当前版本不支持小游戏玩法，请更新到最新版本体验酷炫好玩的房间小游戏
	ErrChannelSwitchMiniGameUnavailable         = -2255 // 切换小游戏失败，请更新至最新版本体验酷炫好玩的房间小游戏哟
	ErrChannelSwitchWerewolvesGameUnavailable   = -2256 // 切换失败，请更新至最新版本体验酷炫好玩的狼人杀小游戏哟
	ErrChannelPublicWerewolvesGameUnavailable   = -2257 // 发布失败，请更新至最新版本体验酷炫好玩的狼人杀小游戏哟
	ErrChannelHourRankUnavaiable                = -2258 // 蒙面PK赛时段暂不开启小时榜展示，小时榜奖励正常发放
	ErrChannelMicModeLockedInPk                 = -2259 // 参加蒙面PK赛期间无法切换麦位模式
	ErrChannelIsCollected                       = -2260 // 已收藏该房间
	ErrChannelModifyNameTextRejectRisk          = -2261 // 对不起，您的房间名称未通过审核
	ErrChannelModifyNameSpecialSchemeWordsLimit = -2262 // 房间名称字数限制
	ErrChannelTypeNotSupport                    = -2263 // 不支持该类型的房间
	ErrChannelMusicOpLimit                      = -2264 // 操作太快啦，一会儿再来试试
	ErrChannelMusicListTypeNotSupport           = -2265 // 该歌曲来源于当前版本不支持的模式，请升级版本后再进行操作
	ErrChannelOpenGamePlayingLimit              = -2266 // 游戏过程中无法踢出用户/切换房间类型

	// Activity 活动
	ErrActivityErrcodeBase        = -2300
	ErrActivitySvrFalied          = -2301
	ErrActivityNameEmpty          = -2302
	ErrActivityTimeError          = -2303
	ErrActivityDberror            = -2304
	ErrActivityRecordNotexist     = -2305
	ErrActivityParameterErr       = -2306
	ErrActivityNotEnoughPrizeTale = -2307 // 没有足够的奖励可领取

	// apns
	ErrApnsInvalidRegisterRequest = -2400
	ErrApnsNoDeviceToken          = -2401
	ErrApnsInvalidDeviceToken     = -2402
	ErrApnsNotificationExpired    = -2403

	// first voucher activity
	ErrGameFirstVoucherNotExist        = -2500
	ErrGameFirstVoucherUpdateNoParam   = -2501
	ErrGameFirstVoucherProductHaveItem = -2502
	ErrGameFirstVoucherProductPastDue  = -2503
	ErrGameFirstVoucherItemNotEnough   = -2504
	ErrGameFirstVoucherUserNoBuyAuth   = -2505

	// Redpacket
	ErrRedpacketAlreadyMassguildbuff        = -2600
	ErrRedpacketStageNotFound               = -2601
	ErrRedpacketExchangeGiftAccountNotExist = -2603
	ErrRedpacketNotAllowed                  = -2604
	ErrRedpacketActivityNotFound            = -2605
	ErrRedpacketGuildbuffNotFound           = -2606
	ErrRedpacketStagetimeOverlay            = -2607 // 修改的阶段时间和已有配置重合
	ErrRedpacketStagetimeTooLate            = -2608 // 修改的阶段时间必须在当前时间前2小时

	// 活动
	ErrCeremony2018StageError              = -2650 // 年度盛典当前阶段不能执行该操作
	ErrCeremony2018SignupSexErr            = -2651 // 您的性别不符合报名要求，请修改
	ErrCeremony2018SignupAlready           = -2652 // 您已经报过名了 不能重复报名
	ErrCeremony2018JoinTeamAlready         = -2653 // 您已经加入了一个多人队伍
	ErrCeremony2018NotTeamMember           = -2654 // 您不是该队伍成员
	ErrCeremony2018NotTeamCreater          = -2655 // 您不是该队伍的创建者，没有权限
	ErrCeremony2018TeamNotExist            = -2656 // 该队伍不存在或者已经解散
	ErrCeremony2018TeamMemberSizeOverlimit = -2657 // 该队伍已经满员,无法加入
	ErrNewyearBeat2019GameNotStart         = -2658 // 游戏尚未开始
	ErrNewyearBeat2019GameAlreadyPlay      = -2659 // 本轮游戏您已经参与过了
	ErrNewyearBeat2019GameAlreadyEnd       = -2660 // 本轮游戏已结束
	ErrNewyearBeat2019LotteryError         = -2661 // 游戏抽奖错误

	// Commission
	ErrCommissionMoneyNotEnough100   = -2670 // 金额小于100，不可提现
	ErrCommissionAccountIsFrost      = -2671 // 你的账户已被冻结，不可提现。如有问题，请联系客服
	ErrCommissionGuildLvTooLow       = -2672 // 公会星级达到Lv.2以上才能提现
	ErrCommissionGetMoneyTimeErr     = -2673 // 每月的4日~6日才可以申请提现
	ErrCommissionUserInfoIdCardErr   = -2674 // 身份证填写格式有误, 请核对后重试
	ErrCommissionUserInfoBankIdErr   = -2675 // 开户行填写格式有误，请核对后重试
	ErrCommissionUserInfoPhoneErr    = -2676 // 手机号码填写格式有误, 请核对后重试
	ErrCommissionUserInfoBankCardErr = -2677 // 银行卡号填写格式有误，请核对后重试
	ErrCommissionMoneyNotEnough      = -2678 // 提现金额超过余额，请重新申请
	ErrCommissionUserInfoErr         = -2679
	ErrCommissionApiTimeout          = -2680 // 佣金系统接口超时
	ErrCommissionApiRespError        = -2681 // 佣金系统操作不成功
	ErrCommissionUserBankInfoError   = -2682 // 上次提现银行卡账户信息不正确，请重新设置银行卡

	// Gold Diamond
	ErrGoldDiamondOrderIdExist      = -2700
	ErrGoldDiamondDetailEmpty       = -2701
	ErrGoldDiamondSqlSumFailed      = -2702
	ErrGoldDiamondIncomeNotEqualSum = -2703
	ErrGoldDiamondTimeError         = -2704

	//numericsvr 2750- 2799
	ErrNumericsUnknownRankType             = -2750 // 未知的排行榜类型
	ErrNumericsPresentRecordNotsupportType = -2751 // 财富魅力值接口不支持该类型礼物
	ErrNumericsUserNoContract              = -2752 // 用户未签约
	ErrNumericsUserNotFound                = -2753 // 用戶不存在
	ErrNumericsDb                          = -2754 // 数据库错误
	ErrNumericsRepeatOp                    = -2755 // 重复操作
	ErrNumericsNotEnough                   = -2756 // 该账号财富值不足
	ErrNumericsRichCardLock                = -2757 // 你已锁定财富值，无法使用财富卡
	ErrNumericsCommonErr                   = -2758 // 财富值业务错误
	ErrNumericsVipPackGetErr               = -2759 // 暂无法领取礼包，请联系官方客服
	ErrNumericsVipPackNoCondition          = -2760 // 未达到领取条件，暂无法领取礼包
	ErrNumericsVipPackPackErr              = -2761 // 包裹错误，暂无法领取礼包
	ErrNumericsVipPackGiftErr              = -2762 // 礼物已下架，请选择其他礼物
	ErrNumericsVipPackFrequencyErr         = -2763 // 领取频繁，请稍后再试
	ErrNumericsVipPackTriggerRisk          = -2764 // 暂无法领取礼包，请联系官方客服

	//
	ErrLbsJsonParseErr      = -2800 // json解析失败
	ErrLbsSsdbOperErr       = -2801 // ssdb操作失败
	ErrLbsIpNotFound        = -2802 // IP地址无效
	ErrLbsLocationNotFound  = -2803 // 经纬度无效
	ErrLbsLocationOutOfDate = -2804 // 经纬度数据过期

	//Guild storage [2900 - 2999]
	ErrGuildJoinTimeLimit = -2900 // 加入公会时间不足

	// 开放平台 oauth2:   [ -3000 --- -4999 ]
	ErrOauth2Base                    = -3000
	ErrOauth2InvalidRequest          = -3001
	ErrOauth2UnauthorizedClient      = -3002
	ErrOauth2AccessDenied            = -3003
	ErrOauth2UnsupportedResponseType = -3004
	ErrOauth2InvalidScope            = -3005
	ErrOauth2ServerError             = -3006
	ErrOauth2TemprarilyUnavailable   = -3007
	ErrOauth2InvalidClient           = -3008
	ErrOauth2InvalidGrant            = -3009
	ErrOauth2UnsupportedGrantType    = -3010
	ErrOauth2InvalidUser             = -3011
	ErrOauth2InvalidCode             = -3012
	ErrOauth2InvalidAccessToken      = -3013
	ErrOauth2InvalidRefreshToken     = -3014
	ErrOauth2InvalidRedirectUri      = -3015
	ErrOauth2NoPermissionToAccess    = -3016
	ErrOauth2TokenSignatureMismatch  = -3040
	ErrOauth2InvalidTokenFormat      = -3041

	// Team svr
	ErrTeamSvrUserAlreadyHaveTeam = -3101 // 用户已有战队
	ErrTeamSvrTeamNotExist        = -3102 // 战队不存在
	ErrTeamSvrUserNoVoteChance    = -3103 // 每天只能给出一个支持哦~
	ErrTeamSvrApproved            = -3104 // 战队已批准，禁止再修改
	ErrTeamNameAlreadyUse         = -3105
	ErrTeamMemberSignupRepeat     = -3106
	ErrTeamNotFollow              = -3107

	// league svr
	ErrLeagueSvrUploadImg = -3201 // 当前阶段不可上传截图

	// game preorder
	ErrGamePreorderNotExist = -3250 // 预约不存在

	//userrecommendsvr
	ErrUserReportPhonelistInvalid = -3260 // 上报手机列表出错

	//cityrecommendsvr
	ErrCityRecommendAddrNotExist = -3265 // 用户地址不存在

	// recommend card
	ErrGameRecommendCardTagExist              = -3301
	ErrGameRecommendCardToplistNameExist      = -3302
	ErrGameRecommendCardToplistTagExist       = -3303
	ErrGameRecommendCardTagNotExist           = -3304
	ErrGameRecommendCardNotExist              = -3305
	ErrGameRecommendCardActivityBindGameExist = -3306
	ErrGameRecommendCardActivityExpire        = -3307
	ErrGameRecommendCardTagCardExist          = -3308

	// 安全中心 Security -3401 ~ -3499
	ErrSecurityOk                            = -3401
	ErrSecurityAccountWrong                  = -3402
	ErrSecuritySystemErr                     = -3403
	ErrSecurityAccountNotExist               = -3404
	ErrSecurityAuthRequired                  = -3405
	ErrSecurityAccessTokenExpired            = -3406
	ErrSecurityPhoneFormatWrong              = -3407
	ErrSecurityWrongMissingInput             = -3408
	ErrSecurityPhoneBindedByAnother          = -3409
	ErrSecuritySmsVerfiycodeValidateFail     = -3410
	ErrSecuritySessionRequired               = -3411
	ErrSecuritySessionExpired                = -3412
	ErrSecuritySessionError                  = -3413
	ErrSecurityDberror                       = -3414
	ErrSecurityAnswerNotMatch                = -3415
	ErrSecurityAnswerWrongFrequently         = -3416
	ErrSecurityWrongOldPassword              = -3417
	ErrSecuritySmsVerfiycodeFailExeccedDaily = -3418
	ErrSecuritySmsSendTooFreq                = -3419
	ErrSecuritySmsSendExeccedDaily           = -3420
	ErrSecurityAlreadyBindPhone              = -3421 // 你已经绑定了手机
	ErrSecurityAccountBlack                  = -3422 // 该帐号禁止操作

	// gamelotto svr
	ErrGamelottoSvrNoEnoughChance                = -3501 // 抱歉 剩余抽奖次数为0
	ErrGamelottoSvrReachLimit                    = -3502 // 已达到当日抽奖次数限制
	ErrGamelottoSvrNeedReddiamond                = -3503 // 继续抽奖需要消耗红钻
	ErrGamelottoRechargeOrderidDuplicate         = -3504
	ErrGamelottoRechargeParamErrMingiftOverlimit = -3505
	ErrGamelottoGameChannelUnkown                = -3506
	ErrGamelottoGameChannelNotValid              = -3507
	ErrGamelottoRechargeInvalid                  = -3508
	ErrGamelottoRechargeParamErrRebateInvalid    = -3509
	ErrGamelottoRechargeParamLack                = -3510
	ErrGamelottoRechargeNeverRecharge            = -3511
	ErrGamelottoRechargeFixPoolLack              = -3512
	ErrGamelottoRechargeAdditionPoolLack         = -3513
	ErrGamelottoRechargeNoGiftRange              = -3514
	ErrGamelottoRechargeParamErr                 = -3515
	ErrGamelottoUserGameCpNotExist               = -3540
	ErrGamelottoNoEnoughReddiamond               = -3550 // 红钻不足
	ErrGamelottoPoolEmpty                        = -3551 // 库存已空
	ErrGamelottoConsumeReddiamondFailed          = -3552 // 红钻扣除失败
	ErrGamelottFirstLottoHasDone                 = -3553 // 首抽已经领取
	ErrGamelottoLoginToGetFreeChance             = -3554 // 红钻抽奖次数已到达上线,登录获取免费抽奖次数
	ErrGamelottoNoSuchOrder                      = -3555 // 无效的订单号
	ErrGamelottoFragmentNotEnough                = -3556 // 碎片不足

	// audit svr 3600 ~ 3699
	ErrAuditOk               = -3600
	ErrAuditGeneralErr       = -3601
	ErrAuditMongodbException = -3602
	ErrAuditDbErr            = -3603
	ErrAuditQueryParseErr    = -3604

	// guildmemberlv svr
	ErrGuildmemberlvSupplementParam     = -3701
	ErrGuildmemberlvInvalidDonateOption = -3702 // 捐献值错误
	ErrGuildmemberlvContributionLack    = -3703 // 贡献值不足
	ErrGuildmemberlvAddContributionZero = -3704
	ErrGuildmemberlvOperType            = -3705
	ErrGuildmemberlvOrderExist          = -3706
	ErrGuildmemberlvOrderEmpty          = -3708
	ErrGuildmemberlvNoUidList           = -3709

	// guildstorge svr
	ErrGuildStorgeProductOverLimit     = -3740 // 公会商品数量超过限制
	ErrGuildStorgeProductItemOverLimit = -3741 // 商品兑换码超过限制svn

	// guildcircle svr
	ErrGuildcircleTopTopicCountLimitExceeded = -3750 // 置顶帖数量超过限制
	ErrGuildcircleTitleIsEmpty               = -3751 // 标题不能为空

	// ttgiftcenter svr
	ErrTtGiftCenterNotRankSubType   = -3801 // 改子礼包无法排序
	ErrTtGiftCenterNotEnoughStorage = -3802 // 库存不足
	ErrTtGiftNotExistGift           = -3803 // 礼包不存在
	ErrTtGiftNotEnoughCurrency      = -3804 // 货币不足
	ErrTtGiftSpendCurrencyFailed    = -3805 // 扣费失败
	ErrTtGiftNotAllowToPick         = -3806 // 未开放淘号
	ErrTtGiftNotValidCount          = -3809
	ErrTtGiftNotForSdk              = -3810
	ErrTtGiftNotthingCanClaim       = -3811 // 没有可领取的礼包
	ErrTtGiftLimitRegisterAt        = -3812 // 礼包限定了可领取用户的注册时间

	//findfriend_match
	ErrFindFriendMatchingNull           = -3830 // 没有匹配的用户
	ErrFindFriendExamIdNotExist         = -3831 // 该测试项不存在~
	ErrFindFriendMatchCntOverLimit      = -3832 // 今天召唤了很多个小可爱啦，明天再试试吧
	ErrUserPresentConfigNotExist        = -3851 // 礼物不存在
	ErrUserPresentLmtEffectedBegin      = -3852 // 礼物未上架
	ErrUserPresentLmtEffectedEnd        = -3853 // 礼物已下架
	ErrUserPresentConfigOverdue         = -3854 // 礼物配置已过期
	ErrUserPresentConfigParam           = -3855 // 礼物配置参数异常
	ErrUserPresentBuyFailed             = -3857 // 购买礼物失败
	ErrUserPresentInvalidItemCount      = -3858 // 礼物数量异常
	ErrUserPresentOrderNotExist         = -3859 // 礼物订单不存在
	ErrUserPresentTagChannelDating      = -3860 // 此房间不可送出相亲房礼物
	ErrUserPresentInvalidTargetUserSize = -3861 // 送礼失败
	ErrUserPresentUnableSendUserPresent = -3862 // 无法给该用户送礼
	ErrUserPresentBackpackRiskControl   = -3863 // 礼物赠送失败，不符合平台规则，有疑问请联系客服80040或80513
	ErrUserPresentChannelGameOnly       = -3864 // 送礼失败！小互动礼物只能在游戏中送出哦
	ErrUserPresentFlowConfigNotExist    = -3865 // 礼物流光配置不存在
	ErrUserPresentSpendOverLimit        = -3866 // 单笔赠送金额不能超过2万元
	ErrUserPresentBatchNotSupport       = -3867 // 该礼物不支持全麦赠送
	ErrUserPresentBoxInvalid            = -3868 // 开盒参数错误
	ErrUserPresentServiceInfoInvalid    = -3869 // 参数错误
	ErrScenePresentOrderExist           = -3870 // 送礼场景的礼物订单号已存在
	ErrUserScoreConfigNotExist          = -3871 // 积分配置不存在
	ErrUserScoreNotEnough               = -3872 // 积分不足
	ErrUserScoreOrderExist              = -3873 // 积分订单号已存在
	ErrUserScoreChangeReasonInvalid     = -3874 // 非法积分变化原因
	ErrUserScoreOrderRollback           = -3875 // 积分已回滚
	ErrUserScoreInvalidDealtoken        = -3876 // 校验不通过

	// 3880-3889 还是礼物，前面的用完了
	ErrUserPresentAccountRiskControl = -3880 // 账号异常，请稍后再试~

	// 3890-3889 体验券相关
	ErrUserPresentInvalidTargetUserTicket = -3890 // 只能对麦上的签约用户使用喔
	ErrUserPresentInvalidChannelTicket    = -3891 // 免费体验券只能在对应房间使用喔，快去使用吧~
	ErrUserPresentChannelTicketMainMic    = -3892 // 券不能送给主持喔，送给其他麦位嘉宾吧~

	// anti 3900 - 3949
	ErrAntiOk                 = -3900
	ErrAntiDbErr              = -3901 // 访问DB错误
	ErrAntiNotExist           = -3902 // 记录不存在
	ErrAntiRedisErr           = -3903 // 访问Redis错误
	ErrAntiCmdOpBanForbid     = -3904 // 当前处于系统维护升级中，该功能暂不可用
	ErrAntiCmdOpUserBanForbid = -3905 // 系统升级中，该功能暂不可用

	// cooldown 3950 - 3999
	ErrCooldownOk       = -3950
	ErrCooldownRedisErr = -3951
	ErrCooldownNotExist = -3952

	// channelvotepk
	ErrChannelVotePkGetLockFail    = -3310 // 获取锁失败
	ErrChannelVotePkRedisFail      = -3311 // 系统错误
	ErrChannelVotePkExist          = -3312 // 发起投票失败，该房间正在投票中
	ErrChannelVotePkArgsFail       = -3313 // 参数错误
	ErrChannelVotePkNotExist       = -3314 // PK已经结束
	ErrChannelVotePkNotMember      = -3315 // 不是PK成员
	ErrChannelVotePkNotAdmin       = -3316 // 需要管理员权限
	ErrChannelVotePkCommonFail     = -3317 // 投票失败
	ErrChannelVotePkNotLeftVote    = -3318 // 你的票数已经用完啦，感谢你的支持哟
	ErrChannelVotePkNameInvalid    = -3319 // 投票话题中包含敏感词，请重新输入
	ErrChannelVotePkNotChannelMode = -3320 // 只有娱乐模式才能开启投票哟

	//channeldrawgame
	ErrChannelDrawGameStatusOff    = -3321 // 涂鸦小画板已经关闭，请联系房管打开画板功能
	ErrChannelDrawGameLineCntMax   = -3322 // 当前画板操作次数过多，请清屏后再画吧
	ErrChannelDrawGameNotAdmin     = -3323 // 需要管理员权限
	ErrChannelDrawGameLineNotExist = -3324 // 线不存在
	ErrChannelDraeGamePointCntMax  = -3325 // 笔画太长啦，松手再继续吧~
	ErrChannelDrawGameUnavailable  = -3326 // 系统升级暂不可用

	//领取礼物碎片
	ErrGetFriendDebrisGiftTimeOut        = -3330 // 消息已过期
	ErrGetFriendDebrisGiftRepeat         = -3331 // 礼物已经领取
	ErrSendFriendDebrisDayCntLimit       = -3332 // 今日送礼次数达到上限
	ErrReceiveFriendDebrisDayCntLimit    = -3333 // 对方今日收礼次数达到上限
	ErrSendFriendTargetUidErr            = -3334 // 不能领取不是送给自己的礼物
	ErrYouReceiveFriendDebrisDayCntLimit = -3335 // 今日收礼次数已达上限，明天再试吧

	// httplogic 4000 - 4099
	ErrHttplogicSmsThreshold = -4001

	// misssion 4100 - 4199
	ErrUpdateMissionStatus = -4101

	// friendonline 4200 - 4220
	ErrOnlineUnknownStatus            = -4201
	ErrOnlineChannelFollowAuthClose   = -4202 // 您的玩伴未开启“跟随进房”功能，快去让他开启吧
	ErrOnlineChannelFollowAuthNotOpen = -4203 // 用户跟随进房间设置未打开
	ErrOnlineChannelFollowNotEnter    = -4204 // 您跟随的玩伴现在没有在房间内

	// user tag 4221 - 4299
	ErrUsertagConfNotExist            = -4221 // 标签不存在
	ErrUsertagConfDel                 = -4222 // 该标签已经被删除 不可设置
	ErrUsertagGameOptConfDel          = -4223 // 游戏标签的该选项已经被删除 不可设置
	ErrUsertagSetCountOverlimit       = -4224 // 设置的标签数量超过限制
	ErrUsertagSetGametagNickSensitive = -4225 // 游戏卡片中游戏昵称包含敏感词
	ErrUsertagSetBirthdayFormatErr    = -4226 // 无效的生日标签格3904式
	ErrGamecardDuplicateCreate        = -4228 // 游戏卡重复创建
	ErrGameTabNotExistErr             = -4229 // 游戏暂不存在
	ErrGamecardSetParamErr            = -4299 // 游戏卡参数错误

	// seach 4300 - 4349
	ErrSearchOk           = -4300
	ErrSearchParameterErr = -4301
	ErrSearchSphinxErr    = -4302 // Sphinx错误

	// clientversion 4350 - 4399
	ErrClientversionRedisErr        = -4351
	ErrClientversionNoVersionCfg    = -4352
	ErrClientversionNoValidVersion  = -4353
	ErrClientversionUserVerNotExist = -4354

	// kefu 4400 - 4450
	ErrUserAlreadyBindKefu = -4400 // 该用户当前由其他客服绑定服务
	ErrKefuAllOffline      = -4401 // 没有在线客服

	// 绿色爸爸的制裁
	ErrGreenbabaSanctionChannel    = -4421 // 该房间暂时无法使用
	ErrGreenbabaSanctionOpUser     = -4422 // 您挑战了绿色爸爸的力量 正在被制裁 (说人话就是您被封了)
	ErrGreenbabaSanctionTargetUser = -4423 // 对方挑战了绿色爸爸的力量 正在被制裁(说人话就是对方被封了)

	// exchange 4450 - 4500
	ErrExchangeItemPriceNotUpToDate      = -4450 // 兑换价格不是最新
	ErrExchangeItemInvalidCurrencyType   = -4451 // 无效的兑换类型
	ErrExchangeDuplicateOrderId          = -4452 // 每个提现周期只能提现一次哦
	ErrExchangeTbeanInsufficientFunds    = -4453 // 今天豆豆已经抢光了哟，请明天再来吧～
	ErrExchangePointsSettling            = -4454 // 正在结算积分中，请稍候再来吧~
	ErrExchangeFuseProtection            = -4455 // 积分兑换T豆库存不足，请稍后再试
	ErrExchangeFuseProtectionProcess     = -4456 // 积分兑换T豆库存达到警戒线
	ErrExchangeBlackUser                 = -4457 // 账号异常，请联系客服
	ErrExchangeParamErr                  = -4458 // 参数错误
	ErrExchangeSignErr                   = -4459 // 签约状态更新错误
	ErrExchangeCheckFaceNotPass          = -4460 // 人脸校验不通过
	ErrExchange2327Cash                  = -4461 // 请在每月23号-27号发起提现哦
	ErrExchangeWeekEndCash               = -4462 // 请在周末发起提现哦
	ErrExchangeCannotCash                = -4463 // 暂不可进行积分提现
	ErrExchangeNotRealName               = -4464 // 还没实名认证或处于审核中
	ErrExchangeNotAge18Cash              = -4465 // 因你未满18周岁，不可发起提现
	ErrExchangePointNotEnough            = -4466 // 积分不足
	ErrExchangeMaskPk10000               = -4467 // 10000以上积分才能提现哦
	ErrExchangeCannotLargerThan1000wOnce = -4468 // 单次兑换不能超过1000万积分
	ErrExchangeWithdrawMonthMax          = -4469 // 提现异常，请联系TT ID80119
	ErrExchangeConfErr                   = -4470 // 配置错误
	ErrExchangeAlgErr                    = -4471 // 算法错误

	// tbean 4500 - 4520
	ErrTbeanDuplicateOrder     = -4500 // 重复的订单号
	ErrTbeanNoEnoughBalance    = -4501 // T豆余额不足
	ErrTbeanIllegalOrderStatus = -4502
	ErrTbeanSystemError        = -4503 // T豆系统错误
	ErrTbeanFreezeBlackList    = -4504 // 账号异常，请联系客服
	ErrTbeanParamError         = -4505 // 参数错误
	ErrTbeanAbnormalProtect    = -4506 // 异常充值
	ErrTbeanMinorRecharge      = -4507 // 未成年人充值

	// unified pay 4520 - 4530
	ErrUnifiedPayDuplicateOrder     = -4520 // 重复的订单号
	ErrUnifiedPayIllegalOrderStatus = -4521
	ErrUnifiedPayNoSuchOrder        = -4522 // 无此订单

	// unified pay rmb 4531-4540
	ErrUnifiedPayRmbNoSupport        = -4531 // 不支持该功能
	ErrUnifiedPayRmbCbErr            = -4532 // 回调错误
	ErrUnifiedPayRmbOrderFailed      = -4533 // 下单失败
	ErrUnifiedPayRmbDuplicateOrder   = -4534 // 重复订单号
	ErrUnifiedPayRmbNoSuchOrder      = -4535 // 不存在的订单
	ErrUnifiedPayRmbErr              = -4536 // 支付系统错误
	ErrUnifiedPayRmbRiskFailed       = -4537 // 风控检测失败
	ErrUnifiedPayRmbRiskNeedFace     = -4538 // 支付前需要进行人脸认证
	ErrUnifiedPayRmbRiskNeedRealname = -4539 // 支付前需要进行实名认证
	ErrUnifiedPayRmbRiskFaceFailed   = -4540 // 人脸认证失败

	// rush  4541 - 4550
	ErrRushInQueue = -4541 // 正在排队中，请稍候

	// find_friends 4551 - 4599
	ErrFindFriendsNoRegistered             = -4551 // 你未在扩圈中登记资料，请先完善资料
	ErrAnotherQuickMatchIsAfoot            = -4552 // 你有另一个匹配正在进行中，请别着急~
	ErrQuickMatchCurrentChannelIsSupplying = -4553 // 当前房间正在补位中，请别着急~
	ErrQuickMatchGameNotSupport            = -4554 // 你选择的游戏已经不再支持匹配，请重新选择~
	ErrQuickMatchPunishedForDeserter       = -4555 // 由于你最近从匹配房间中秒退，请稍后再进行匹配~
	ErrFindFriendsReachedDailyLikeLimit    = -4556 // 今天已经喜欢了太多人了，不要那么花心哦

	//headwear
	ErrHeadwearNotFound        = -4570 // 不存在的麦位框
	ErrHeadwearNotInUse        = -4571 // 用户没有使用麦位框
	ErrHeadwearNotHave         = -4572 // 用户没有该麦位框
	ErrHeadwearExpired         = -4573 // 麦位框已经过期
	ErrHeadwearNotSameCp       = -4574 // 与该麦位框绑定的CP不一致
	ErrHeadwearOrderidExist    = -4575 // 麦位框订单已存在
	ErrHeadwearCpTypeNotCpUid  = -4576 // CP麦位框缺少CP对象UID
	ErrHeadwearCanNotUse       = -4577 // 无法使用该麦位框，请升级版本
	ErrHeadwearCanNotChangeUse = -4578 // 无法更换麦位框哦~
	ErrHeadwearConfExist       = -4579 // 麦位框配置已存在

	// activity_present 4600 - 4619
	ErrActivityPresentEnded = -4600 // 活动已结束

	// Trivia Game 4620 - 4680
	ErrChannelTriviaGameNotSupport                     = -4620 // 当前版本不支持答题，请升级版本
	ErrChannelTriviaGameActNotExist                    = -4621 // 答题活动不存在或者尚未开始
	ErrChannelTriviaGameQuestionNotExist               = -4622 // 题目不存在
	ErrChannelTriviaGameQuestionExpire                 = -4623 // 题目过期或者尚未开始答题
	ErrChannelTriviaGameAnswerNoQualify                = -4624 // 您已经被淘汰了,不能继续答题
	ErrChannelTriviaGameNotQuestionPhase               = -4625 // 非答题阶段
	ErrChannelTriviaGameShowSolutionEarly              = -4626 // 答案公布太早了
	ErrChannelTriviaGameErrPhase                       = -4627 // 阶段顺序异常
	ErrChannelTriviaGameNoLivesToResurrect             = -4628 // 复活卡不足
	ErrChannelTriviaGameNoResurrectChancesInThisPeriod = -4629 // 本轮已经无法使用复活机会
	ErrChannelTriviaGameNotStart                       = -4630 // 活动未开始
	ErrChannelTriviaGameAlreadyEnd                     = -4631 // 活动已经结束
	ErrChannelTriviaGameAlreadyShowSolution            = -4632 // 已经公布过答案
	ErrChannelTriviaGameAlreadyAnswer                  = -4633 // 不能重复答题

	// esgw 4680 - 4700
	ErrEsgwNotFound = -4686 // 不存在

	// guildrecommend 4701 - 4720
	ErrGuildGameCfgNotExist = -4701 // 非公会游戏
	ErrGuildGameAddLimit    = -4702 // 同时添加的公会主打游戏太多咯

	// game recruit 4800- 4850
	ErrGameRecruitExist          = -4800 // 游戏招募已存在
	ErrGameRecruitNotHaveTeam    = -4801 // 没有加入任何队伍
	ErrGameRecruitNotExist       = -4802 // 该游戏组队已结束
	ErrGameRecruitFullMember     = -4803 // 该游戏组队已满员
	ErrGameRecruitNotInChannel   = -4804 // 用户不在频道中
	ErrGameRecruitFrequenceLimit = -4805 // 频率限制

	// presentlogic 4851 - 4880
	ErrPresentSourceInvalid  = -4851 // 礼物不存在
	ErrPresentInternalSender = -4852 // 消费异常，请检查您的消费对象账号
	ErrPresentInternalTarget = -4853 // 消费失败，当前消费对象账号异常

	// backpack 背包 4900 - 4949
	ErrBackpackPackageItemNotExist       = -4900 // 包裹不存在该包裹项配置
	ErrBackpackUserNotEnoughItem         = -4901 // 背包物品不足
	ErrBackpackPackageItemTimeout        = -4902 // 背包物品已过期
	ErrBackpackFuncCardAlreadyUse        = -4903 // 已使用同类型卡片
	ErrBackpackUserItemNotFind           = -4904 // 用户背包找不到该项
	ErrBackpackUseSourceIdErr            = -4905 // 使用物品源id不一致
	ErrBackpackUseItemTypeErr            = -4906 // 使用物品类型不一致
	ErrBackpackUseFragmentSendErr        = -4907 // 碎片不支持赠送
	ErrBackpackUseFreezeOrderConfilctErr = -4908 // order_id 冲突
	ErrBackpackUseFreezeOrderNonexistErr = -4909 // order_id 不存在
	ErrBackpackUseFreezeOrderFinishedErr = -4910 // order_id 已经完成
	ErrBackpackUseFunccardLevelLimit     = -4911 // 不可使用比当前低倍或相同倍数的加速卡
	ErrBackpackUseFreezeTypeInvalidErr   = -4912 // 该类型不支持冻结
	ErrBackpackOrderExist                = -4913 // 订单号已存在
	ErrBackpackNumLimit                  = -4914 // 超过单次发包裹数量
	ErrBackpackOrderNotExist             = -4915 // 订单不存在
	ErrBackpackTimestampInvalid          = -4916 // 时间戳参数错误
	ErrBackpackIsDeleted                 = -4917 // 包裹不存在
	ErrBackpackNumInvalid                = -4918 // 非法物品数量
	ErrBackpackSysDbFail                 = -4919 // 背包系统错误
	ErrBackpackInvalidParams             = -4920 // 参数错误
	ErrBackpackGetFuncCardErr            = -4921 // 获取卡片配置出错
	ErrBackpackFuncCardTypeErr           = -4922 // 卡片类型错误

	// channel dating game 房间相亲游戏 4950 - 5000
	ErrChannelDatinggameEntryNotOpen          = -4950 // 该房间暂无相亲游戏模式权限
	ErrChannelDatinggamePhaseError            = -4951 // 相亲游戏阶段设置错误
	ErrChannelDatinggameApplyMicUserOverLimit = -4952 // 相亲游戏申请上麦人数超过限制
	ErrChannelDatinggameUserNotSelectLikeObj  = -4953 // 用户没有选择心动对象
	ErrChannelDatinggameCanNotOpenUser        = -4954 // 不能公布心动对象
	ErrChannelDatinggameErrStageOperation     = -4955 // 该阶段不能进行此操作
	ErrChannelDatinggameNotVip                = -4956 // 不可上土豪王座
	ErrChannelDatinggamePhaseVipError         = -4957 // 此相亲阶段不可上土豪王座
	ErrChannelDatinggameOpenUserCoolDown      = -4958 // 公布心动对象冷却中~
	ErrChannelDatinggameAlreadyOpenUser       = -4959 // 已经公布过该用户心动对象~

	// checkin 5001 - 5020
	ErrUserAlreadyCheckin    = -5001 // 今天已签到
	ErrCheckinAwardNotExist  = -5002 // 签到奖励不存在
	ErrCheckinConfigNotExist = -5003 // 签到配置不存在

	// 实名认证 5021 - 5040
	ErrRealnameNotSetingErr        = -5021 // 需要进行实名认证
	ErrRealnameNotFinished         = -5022 // 实名认证没有完成
	ErrRealnameAlreadyFinished     = -5023 // 实名认证已经完成
	ErrRealnameUnknownStatus       = -5024 // 未知的实名认证状态
	ErrRealnameNotIdentityInfo     = -5025 // 没有该用户的身份证信息
	ErrRealnameNotStatusInfo       = -5026 // 没有认证信息
	ErrRealnameBindPhoneLimit      = -5027 // 此手机号已经认证十个账号，请用其它手机号认证
	ErrRealnameBindPhoneLimitTt    = -5028 // 绑定的手机号已经认证十个账号，请联系客服
	ErrRealnameBindIdentityLimit   = -5029 // 此身份证已实名认证了十个账号,请使用其他身份信息验证
	ErrRealnameInvalidParameter    = -5030 // 参数错误
	ErrRealnameInvalidIdentityNum  = -5031 // 无效的身份证号码
	ErrRealnameInvalidIdentityName = -5032 // 名字错误
	ErrRealnameUpgradeStopService  = -5033 // 实名认证系统升级中，暂不可用

	// faceid 失败，走 web 实名流程
	ErrRealnameNeedManualCheck     = -5034 // 认证失败，请升级客户端走人工审核通道
	ErrRealnameInvalidIdentityInfo = -5035 // 身份信息有误，请检查姓名或身份证号并重新输入
	ErrRealnameCheckFaceFailLimit  = -5036 // 今日人脸识别认证次数过多，无法进行认证
	ErrRealnameCntLimit            = -5037 // 今日实名认证次数过多，无进行认证

	// 拦截实名
	ErrRealnameNeedIdAuth   = -5038 // 需要完成实名认证
	ErrRealnameNeedFaceAuth = -5039 // 需要完成实名认证和刷脸认证
	ErrRealnameYoungAuth    = -5040 // 未成年人不允许操作

	// 房间送礼统计 5041 - 5060
	ErrCountIsOffErr       = -5041 // 已经关闭了开关
	ErrCountIsOnErr        = -5042 // 已经开启了开关
	ErrCountOnMicErr       = -5043 // 上麦事件消费错误
	ErrCountIsOff          = -5044 // 计数器未开启
	ErrCountRankNotSupport = -5045 // 计数战力榜维护中

	// 反垃圾 5061 ~ 5099
	ErrAntispamNeedVerifyCode        = -5061 // 需要弹验证码进行行为验证
	ErrAntispamVerifyCodeCheckFailed = -5062 // 验证码验证错误
	ErrAntispamTokenNotExist         = -5071 // 反垃圾token不存在
	ErrAntispamSuspectWarning        = -5080 // 请谨慎防范冒充诈骗
	ErrAntispamSuspectReject         = -5081 // 诈骗风险较高，无法操作
	ErrAntispamImNotSeen             = -5082 // IM违规内容
	ErrAntispamDrawGameBanned        = -5083 // 涂鸦功能因违规被禁用
	ErrAntispamChannelImNotSeen      = -5084 // 公屏违规内容

	//表情包 5100 ~ 5120
	ErrEmojiMaxLimit             = -5100 // 你添加的表情数量已经达到上限，请删除部分表情再尝试添加
	ErrEmojiAlreadyAdd           = -5101 // 你已经添加过这个表情了哦
	ErrEmojiVerifyError          = -5102 // 校验失败
	ErrEmojiDownloadError        = -5103 // 下载失败
	ErrEmojiUploadError          = -5105 // 上传失败
	ErrEmojiUploadFileTooLarge   = -5106 // 文件大小超出系统最大限制
	ErrEmojiPkgPermissionDenied  = -5107 // 无权限获取或修改表情包
	ErrEmojiTargetNotExists      = -5108 // 该表情不存在
	ErrEmojiUnsupportedImgFormat = -5109 // 不支持的图片格式
	ErrEmojiAlreadyAuditReject   = -5110 // 添加失败，该表情已被审核拒绝

	// channel-personalization 5120 ~ 5139
	ErrChannelPslUnknownDecorationType     = -5120 // 不支持的装饰类型
	ErrChannelPslGrantingExpiredDecoration = -5121 // 该装饰已经过期
	ErrChannelPslGrantingInvalidDecoration = -5122 // 请求无效
	ErrChannelPslGrantingOrderIdDuplicate  = -5123 // 发放装饰的订单号重复
	ErrChannelPslConfigDuplicate           = -5124 // 装饰配置重复
	ErrChannelPslConfigNotExist            = -5125 // 装饰配置不存在
	ErrChannelPslConfigError               = -5126 // 其他装饰配置错误
	ErrCustomTextCheckError                = -5127 // 不符合平台审核规范，请重新修改
	ErrCustomTextChangeLimit               = -5128 // 文案调整次数已达上限

	// 家长监护模式和实名 5140 ~ 5159
	ErrGuardianPwdErr     = -5141 // 密码错误
	ErrGuardianOnErr      = -5142 // 已经打开家长监护模式
	ErrGuardianOffErr     = -5143 // 已经关闭家长监护模式
	ErrGuardianPwdNullErr = -5144 // 密码不能为空
	ErrGuardianDbErr      = -5145 // 数据库访问异常
	ErrGuardianUnonErr    = -5146 // 未开启家长监护模式

	// 家长监控模式忘记密码申诉时扫脸验证
	ErrGuaidianCheckIdentityByFaceNotPass = -5147 // 扫脸验证未通过
	ErrGuaidianCheckCountIsOverLimit      = -5148 // 今天已经申诉很多次了，明天再试吧
	ErrGuaidianYoungOffDenied             = -5149 // 您是未成年人，不能关闭青少年模式
	ErrRealnameCheckFaceException         = -5150 // 人脸认证异常
	ErrRealnameAuthLimit                  = -5151 // 暂无法进行实名认证
	ErrRealnameForbidDeviceAuth           = -5152 // 检测到您的账户存在风险，请48小时后再尝试
	ErrRealnameNeedFaceAuthLimit          = -5153 // 需要进行人脸认证解除限制
	ErrRealnameNeedRealnameLimit          = -5154 // 需要进行人脸认证解除限制，请先进行实名认证
	ErrRealnameFaceAuthFailLimit          = -5155 // 需要进行人脸认证解除限制，人脸验证失败
	ErrRealnameFaceAuthTimeout            = -5156 // 人脸认证超时

	// 5160 ~ 5250 异步内容
	ErrUgcTopicNotExists                 = -5160 // 该话题不存在
	ErrUgcTopicDisable                   = -5161 // 该主题还没上线
	ErrUgcTopicCreateDuplicateName       = -5162 // 主题名称同名冲突
	ErrUgcTopicCreateDuplicateBindUid    = -5163 // 主题绑定的官方账号已经被使用
	ErrUgcTopicBindedParent              = -5164 // 话题绑定的圈子失败
	ErrUgcTopicBindedGame                = -5165 // 话题绑定游戏ID失败，重复绑定,topicid:%s,topicname:%s
	ErrUgcHadLike                        = -5170 // 已经点过赞了
	ErrUgcHadNotLike                     = -5171 // 已经取消赞了
	ErrUgcFriendshipFollowLimited        = -5172 // 关注失败，你的关注已达到上限
	ErrUgcFriendshipAntispamHit          = -5173 // 关注失败，对方已开启免打扰
	ErrUgcFriendshipBatchFollowLimited   = -5174 // 关注失败，批量关注达到上限
	ErrUgcInteractivePermissionDenied    = -5175 // 无权限操作
	ErrUgcInvalidFeedGroup               = -5176 // 无效的feed 类型
	ErrUgcInvalidVisitRecordType         = -5177 // 非法的记录类型
	ErrUgcFriendshipBatchUnfollowLimited = -5178 // 取关失败，单次取关数量达到上限
	ErrUgcHotRecordNotExists             = -5180 // 该热度记录不存在
	ErrUgcNotAllowToFollowYourself       = -5181 // 不允许关注你自己哦
	ErrUgcPostNotExists                  = -5190 // 帖子不存在
	ErrUgcCommentNotExists               = -5191 // 评论不存在
	ErrUgcPermissionDenied               = -5192 // 权限不足
	ErrUgcAttachmentStatusInvalid        = -5193 // 附件状态无效
	ErrUgcPostBanned                     = -5194 // 该动态已被屏蔽
	ErrUgcPostDeleted                    = -5195 // 该动态已被删除
	ErrUgcCommentBanned                  = -5196 // 评论已被屏蔽
	ErrUgcCommentDeleted                 = -5197 // 评论已被删除
	ErrUgcPostPostCooldown               = -5198 // 手速太快啦，歇一会再来吧
	ErrUgcPostCommentCooldown            = -5199 // 哎呀，手速太快啦，人家接受不了惹
	ErrUgcPostOpInBlackList              = -5200 // 你已拉黑对方，需移出黑名单后才可操作
	ErrUgcPostOpInBlackListForbid        = -5201 // 由于对方的设置，你暂时无法操作
	ErrUgcFriendshipOpInBlackListForbid  = -5202 // 你已拉黑对方，需移出黑名单后才可重新关注
	ErrUgcFollowOpInBlackListForbid      = -5203 // 由于对方的设置，你暂无法关注
	ErrUgcStickyPostCountLimit           = -5204 // 置顶动态已达上限
	ErrUgcStickyCommentCountLimit        = -5205 // 置顶评论已达上限
	ErrUgcPostPrivacyPolicyPrivate       = -5206 // opps~这条动态被隐藏起来啦
	ErrUgcCannotMarkPostSticky           = -5207 // 置顶失败，私密动态不支持置顶哦~
	ErrUgcUserAttitudeCommentLimit       = -5208 // 操作频繁，休息一下吧
	ErrUgcPostCommentOnlyShowSelf        = -5209 // 评论仅自己可见
	ErrUgcCelebrityNotExisted            = -5210 // UID对应的用户并非优质用户
	ErrUgcCelebrityExisted               = -5211 // 用户已是优质用户
	ErrUgcGetFollowPostFrequentErr       = -5212 // 拉取用户关注的位置频次过高
	ErrUgcGetMoodDelErr                  = -5213 // 拉取的心情聚合页心情已删除
	ErrUgcRecommendNil                   = -5214 // 推荐流为空
	ErrUgcRecommendErr                   = -5215 // 推荐流报错
	ErrUgcRecommendBussNil               = -5216 // 推荐流业务返回为空
	ErrUgcRecommendCantPublishVideo      = -5217 // 不能上传视频

	// audio post 5220
	ErrScriptShouldUni = -5220 // 台本重复

	// 用户进房特效 5251 - 5260
	ErrInvalidUserDecorationType = -5251 // 类型错误
	ErrUserDecorationNotExist    = -5252 // 座驾不存在

	// 用户服务协议 5261 - 5270
	ErrUserContractUnknown        = -5261 // 用户服务协议状态异常
	ErrUserContractInvalidVersion = -5262 // 用户服务协议版本错误

	// 私域帖子流
	ErrNonPublicUgcNoPermissionPublish          = -5280 // 无权限发布
	ErrNonPublicUgcNoPermissionTalkPublish      = -5281 // 加入社团才能发布哦
	ErrNonPublicUgcNoPermissionKnowledgePublish = -5282 // 只有社团核心成员才能发布哦
	ErrNonPublicUgcNoPermissionCategoryPublish  = -5283 // 只有加入该品类的社团才能发布哦
	ErrNonPublicUgcNoPermissionInteract         = -5284 // 加入该社团才能互动哦
	ErrNonPublicUgcNoPermissionVoteExpired      = -5285 // 投票已结束
	ErrNonPublicUgcNoPermissionVoteVoted        = -5286 // 已经投过票啦，不能再投啦

	// 用户认证 5300-5320
	ErrUnmatchPushSample = -5300 // 推送参数个数与模板对不上

	//
	ErrCarVersionNotSupport = -5321 // 当前车载版本不支持该功能 请使用APP版本来操作

	// 新人开黑推荐房，房主设置展示 5331 - 5340
	ErrNoviceRecommendUnknownStatus    = -5331 // 状态类型不受支持
	ErrNoviceRecommendAlreadyDisplay   = -5332 // 展示已打开
	ErrNoviceRecommendAlreadyHide      = -5333 // 展示已关闭
	ErrNoviceRecommendPermissionDenied = -5334 // 没有权限改变展示状态
	ErrNoviceRecommendNotRecommend     = -5335 // 不是开黑新人推荐房

	// 主题房间 5341-5360，5362-5379
	ErrTopicChannelCreateChannelLevelNotAllow         = -5341 // 您的等级未达到10级哦~请先到大厅挑挑吧~
	ErrTopicChannelCreateChannelTagRequired           = -5342 // 完善个人标签才能创建房间哟~
	ErrTopicChannelCreateChannelRecommendTypeNowAllow = -5343 // 推荐房暂时不支持发布到约玩大厅哦~
	ErrTopicChannelNotFound                           = -5344 // 主题房间不存在~
	ErrTopicChannelChannelLocked                      = -5345 // 房间已上锁，解锁房间后才能发起哦~
	ErrTopicChannelTabNotFound                        = -5346 // 未找到对应标签哦~
	ErrTopicChannelNameInvalid                        = -5347 // 房间名含有非法字符~
	ErrTopicChannelNameIsNull                         = -5348 // 房间名不能为空~
	ErrTopicChannelNameFormattedInvalid               = -5349 // 请输入15个字以内的有效房间名~
	ErrTopicChannelPermissionDenied                   = -5350 // 无权限操作
	ErrTopicChannelNameSensitive                      = -5351 // 房间名中包含敏感词，若多次发布包含敏感词的房间名，将面临封号危险
	ErrTopicChannelNameSensitiveForHighRisk           = -5352 // 服务器无响应，发布失败
	ErrTopicChannelCreateChannelGameTagRequired       = -5353 // 完善游戏卡才能创建房间哟~
	ErrTopicChannelSetRoomNameFormatInvalid           = -5354 // 设置房间名参数错误
	ErrTopicChannelNameNoExist                        = -5355 // 房间名不存在
	ErrTopicChannelNameConfigVersionInvalid           = -5356 // 房间名配置版本旧了
	ErrTopicChannelSetRoomNameVersionInvalid          = -5357 // 设置房间名版本号错误
	ErrTopicChannelNoPermissionChange                 = -5358 // 发布了游戏类房间需升级到最新版哟~快去升级吧~
	ErrTopicChannelCanNotUseTab                       = -5359 // 小可爱您好，为了给您提供更好的房间体验，非游戏房间暂不开放，我们正在积极优化升级敬请期待。
	ErrTopicChannelNotAllowModifyName                 = -5360 // 发布公开房间暂时不支持自定义房间名哟
	ErrTopicChannelReleaseTooManyTimes                = -5362 // 发布房间操作过于频繁，请明天再来吧
	ErrTopicChannelReleaseBorderOnLimit               = -5363 // 温馨提示：频繁发布房间可能会导致发布功能受限哦
	ErrTopicChannelTabIsNotExist                      = -5364 // 系统维护更新中，无法将房间切换至该玩法
	ErrTopicChannelCanNotPublish                      = -5365 // 该房间暂不支持发布
	ErrTopicChannelSelectTabBeforePublish             = -5366 // 选择房间标签后才能发布~
	ErrFallBackSwitchCantSet                          = -5367 // 业务兜底开关不支持切换
	ErrTopicChannelCommonError                        = -5368 // 主题房通用错误
	ErrTopicChannelBlockSettingNeedRefresh            = -5369 // 发布信息已过期，请点击修改按钮重新发布

	// 标签匹配 5361, 5380 - 5400
	ErrUsermatchMatchOverLimit = -5361 // 今天已经找了很多人了哟，先和他们聊聊吧~

	// 直播业务调整 5401 - 5440
	ErrExchangeLiveBroNoBegin         = -5401 // 现在不是可兑换的时间啊~
	ErrExchangeLiveBroNoLiveUser      = -5402 // 没有可兑换的直播收益~
	ErrExchangeLiveBroExchangeAlready = -5403 // 已经兑换过了~

	//敲门  5441-5460
	ErrKnockDb               = -5441 // 敲门时数据库错误
	ErrKnockNotUidExist      = -5442 // 处理敲门时没有操作者uid信息
	ErrKnockWrongRoomType    = -5443 // 错误房间类型
	ErrKnockRedisFail        = -5444 // redis报错
	ErrKnockOneUserOneMinute = -5445 // 一分钟内对同一玩家请求数只能一次
	ErrKnockTimeOut          = -5446 // 同意拒绝超过timeout时间（10s）
	ErrKnockTicketTimeout    = -5447 // 敲门进房只有5分钟的有效期哦，已经过期请重新敲门
	ErrKnockTicketError      = -5448 // 敲门信息错误，不要伪装成别人进房哟
	ErrKnockPeopleInroom     = -5449 // 敲门者在房间内
	ErrKnockAdminEverHandle  = -5450 // 其它房管已处理

	// 用户拉黑 5461 - 5480
	ErrUserBlackListAddSelf            = -5461 // 无法拉黑自己
	ErrUserBlackListAddOfficialAccount = -5462 // 官方账号无法拉黑
	ErrUserBlackListWasAdded           = -5463 // 对方已在黑名单
	ErrUserBlackListPatRelation        = -5464 // 拍拍关系有效期内不可以拉黑对方

	// 主播签约 5481 - 5530
	ErrContractApplyNoBindPhone          = -5481 // 你的账号未绑定未绑定手机号码，无法签约
	ErrContractApplyNoRealname           = -5482 // 你尚未完成实名认证，无法签约
	ErrContractApplyUnderAge             = -5483 // 你不满足签约年龄要求，无法签约
	ErrContractNonexist                  = -5484 // 合约不存在
	ErrContractExist                     = -5485 // 合约已存在
	ErrContractApplyIdentityLimit        = -5486 // 身份证已经绑定合约
	ErrContractApplyHaveContract         = -5487 // 已经签约
	ErrContractApplyTodayLimit           = -5489 // 次日0点之后才可以重新签约哟
	ErrContractApplyAlready              = -5490 // 重复申请
	ErrContractHandleConflict            = -5491 // 已被其它公会签约
	ErrContractHandleTimeout             = -5492 // 超时的申请，已失效
	ErrContractHandleInvalid             = -5493 // 该条申请已失效
	ErrContractApplysignNonexist         = -5494 // 签约申请不存在
	ErrContractExtensionExist            = -5495 // 已邀请续约
	ErrContractExtensionNonexist         = -5496 // 续约不存在
	ErrContractApplyLimit                = -5497 // 申请次数超限
	ErrContractApplyIdentityApplyLimit   = -5498 // 同一实名账号已申请
	ErrContractExtensionCannot           = -5499 // 未达到续约条件
	ErrContractYearSignLimit             = -5500 // 您本年度的签约次数已达上限，暂时无法签约新的公会
	ErrContractApplyIdentityTypeLimit    = -5501 // 您正在申请其他身份
	ErrContractApplyHaveIdentityType     = -5502 // 已拥有该身份身份
	ErrContractWithdrawApplyStatusChange = -5503 // 申请状态发生变化，撤销失败
	ErrContractApplyGuildNotCoopType     = -5504 // 公会无该合作库类型
	ErrContractApplyBlacklistLimit       = -5505 // 您正处于该身份申请冷却期中，暂时无法发起对应身份的考核申请
	ErrContractApplyHasHandling          = -5506 // 已有会长同意你的申请，请先等待官方处理结果再继续申请。可前往“申请记录”中查看详情
	ErrContractApplySignLiveAnchorLimit  = -5510 // 抱歉，您选择的公会今日已没有签约额度，明天再来吧～
	ErrContractApplyLiveAnchorGuildLimit = -5511 // 今日可提交的官方审批名额已达限额哦~
	ErrContractAudioLilmitStatusDay      = -5512 // 本日语音主播审批同意次数已达上限
	ErrContractAudioLilmitStatusMonth    = -5513 // 本月语音主播审批同意次数已达上限
	ErrContractApplyAnchorLimitAge       = -5514 // 年龄不符，不符合签约要求
	ErrContractApplyAnchorLimitXiaohao   = -5515 // 有其他非同公会的账号存在签约关系，不符合签约要求
	ErrContractApplyAnchorLimitRecharge  = -5516 // 账号属于消费账号，不符合签约要求
	ErrContractApplySignLimitEsport      = -5517 // 电竞指导不可解约
	ErrContractApplySignLimitOther       = -5518 // 不满足解约条件
	ErrContractApplySignLimitExchange    = -5519 // 存在积分冻结的签约用户不可解约
	ErrContractApplySignLimitAgent       = -5520 // 经纪人或管理员的签约账号不可解约
	ErrContractApplySignIsInternalUid    = -5521 // 申请异常，请检查申请签约的公会账号
	ErrContractApplySignIsInternalGuild  = -5522 // 申请失败，当前申请签约公会账号异常
	ErrContractApplySignSettlement       = -5523 // 您的账号不满足签约条件，请联系客服~

	// 砸蛋活动 5531 - 5540
	ErrSmashEggChanceNotEnough = -5531 // 道具不足
	ErrSmashEggReachDailyLimit = -5532 // 今日已达上限，明日再试试吧
	ErrSmashEggSystemMaintain  = -5533 // 系统维护
	ErrSmashEggActivityUpdate  = -5534 // 活动版本变化，请重新拉取配置

	// 反垃圾/作弊/黑产 错误码 5541-5550
	ErrBlacklistCheckNotPass = -5541 // 账号异常，该功能不可用

	// 主题房间2 5551-5650
	ErrTopicChannelFreeze                            = -5551 // 您的账号处于禁言状态，暂不可以可进行此操作
	ErrTopicChannelQuickMatchVersionMiniGameRequired = -5552 // 发起速配失败，请升级至最新版本才可以匹配到小游戏房间哦
	ErrTopicChannelNameCanNotDiy                     = -5553 // 因系统升级，暂不支持修改房间名称，请重新选择房间名称发布
	ErrTopicChannelUserLevelLimit                    = -5554 // 您当前等级不足，暂不允许发布房间
	ErrTopicChannelQuickMatchNoChannel               = -5555 // 当前匹配不到房间哦
	ErrTopicChannelNameViolationOfThesaurus          = -5556 // 文本包含违反当前玩法规范的内容

	// 主题房运营后台 5651-5669
	ErrTopicChannelPublishConfigConflict = -5651 // 发布房间配置冲突
	ErrTopicChannelSensitiveChannelName  = -5652 // 审核失败，不允许保存

	//首页开黑tab广告位 5670 - 5680
	ErrGetGangupAdvConfFailed     = -5670 // 获取开黑tab广告位配置失败
	ErrGetGangupAdvUserInfoFailed = -5671 // 获取开黑tab广告位用户信息失败
	ErrNonValidGangupAdvConf      = -5672 // 无有效开黑tab广告位配置

	// 小游戏 5681 - 5700
	ErrOpengameChangeGameTooOfen  = -5681 // 修改房间游戏太频繁
	ErrOpengameTemporaryPlayernum = -5682 // 小游戏匹配人数为0
	ErrMiniGameMaintain           = -5683 // 游戏维护中,请稍后匹配

	// 贵族体系 5701 - 5704
	ErrNobilityExclusiveMagicexpression = -5701 // 成为贵族即可使用该表情
	ErrNonNobiltiyExclusivePresent      = -5702 // 没有贵族专属礼物特权
	ErrNonNobiltiyNotPrivilege          = -5703 // 没有使用此特权权限
	ErrNobilitySensitiveTimeRange       = -5704 // 暂不能发送小喇叭消息
	ErrNobilityEqulLevel                = -5705 // 充值后等级小于等于原等级
	ErrNobilityInvisibleTakeHoldMic     = -5706 // 该用户不在房间内
	ErrNobilityFrequentlyLimit          = -5707 // 操作太频繁，请稍后再试
	ErrNobilitySystemErr                = -5708 // 贵族系统错误
	ErrNobilityDuplicateOrderErr        = -5709 // 贵族系统订单重复

	//荣誉宫殿
	ErrGuildHonorHallParaInval   = -5715 // 荣誉宫殿系统参数错误
	ErrGuildHonorHallSetRepeated = -5716 // 荣誉称号重复

	// 语音直播
	ErrAddFansLoveValueOrderExistLimit   = -5771 // 订单号已存在
	ErrFansGroupLeaveLimit               = -5772 // 退出主播直播间后才可以操作退团哦
	ErrChannelLivePkOtherAnchorGameLimit = -5773 // 该直播间正在玩互动玩法，暂不支持连PK哦
	ErrChannelLivePkAnchorGameLimit      = -5774 // 互动玩法进行时不支持开启PK哦
	ErrFansGroupWearPlateLimit           = -5775 // 请先设置团名
	ErrChannelLiveYkwOpenLimit           = -5776 // 神秘人不可以开播
	ErrChannelLivePkCntLimit             = -5777 // 每日20:00～22:00期间只允许连麦PK两次哦～
	ErrChannelLiveAppointPkIngNoLaunchPk = -5778 // 正进行活动比赛PK，不可主动发起PK
	ErrChannelLiveAppointPkIngNoAcceptPk = -5779 // 对方正进行活动比赛PK，暂不可接受PK
	ErrChannelLiveNotAuthority           = -5780 // 没有语音直播权限
	ErrChannelLiveIdInvalid              = -5781 // 过期的直播ID
	ErrChannelLiveNotOpen                = -5782 // 达人还未开启听听哦
	ErrFansGroupNameNotStanderd          = -5783 // 粉丝团名不允许出现敏感词汇/数字/符号/英文/表情，请检查后重新提交
	ErrFansGroupNameIsExist              = -5784 // 团名已被占用
	ErrFansGroupNameVerifying            = -5785 // 团名正在审核，审核结果将助手推送
	ErrFansGroupNameMemberCntLimit       = -5786 // 粉丝团人数达到10可自定义团名
	ErrFansGroupNameFontCntLimit         = -5787 // 粉丝团名称最多3个字
	ErrFansSetGroupNameCntLimit          = -5788 // 团名暂不支持修改，需修改请联系官方

	//PK相关
	ErrChannelLivePkRepeatedApply  = -5789 // 不能重复申请
	ErrChannelLivePkIng            = -5790 // 对方正在PK
	ErrChannelLiveNotPkAuth        = -5791 // 暂无PK权限
	ErrChannelLiveCloseLimit       = -5792 // PK中不能结束直播哦~
	ErrChannelLivePkMatchInvalidTy = -5793 // 匹配类型跟服务端不一致

	//打龙

	// 主题房渠道号相关
	ErrDistributorInvalid = -5800 // 无效渠道号

	// 蒙面聊天 5800 - 5810
	ErrMaskedCallClosing          = -5801 // 不在开放时间
	ErrMaskedCallNoMoreTicket     = -5802 // 匹配次数不足
	ErrMaskedCallTooMuchTipOff    = -5803 // 被举报次数过多
	ErrEnterOtherChannelInLive    = -5811 // 不允许在直播时进入其他房间
	ErrUserChannelPeopleOverlimit = -5812 // 该房间人数已达上限

	// usermodification 5820-5821
	ErrMaxModifySexLimit   = -5820 // 已修改过性别，不能再修改咯
	ErrOldVersionModifySex = -5821 // 当前版本不允许修改性别，请升级到最新版本

	//超级会员
	ErrSuperPlayerDuplicateOrderid    = -5850 // 重复订单
	ErrSuperPlayerInvalidPara         = -5851 // 非法参数
	ErrSuperPlayerInvalidPacket       = -5852 // 重复购买订单
	ErrSuperPlayerSysErr              = -5853 // 超级会员系统错误
	ErrSuperPlayerInvalidAppstoreUser = -5854 // 开通会员的苹果帐号存在AB帐号问题

	// 超级会员 - 特别关心 5860 - 5870
	ErrSuperPlayerSpecialConcernCountLimit = -5860 // 每个人最多有5个特别关心的小可爱哦～
	ErrSuperPlayerPrivilegeExpire          = -5861 // 出错啦！你的超级玩家已经到期咯

	//骑士团
	ErrKnightGroupInvalidConf            = -5871 // 非法参数
	ErrKnightGroupSysErr                 = -5872 // 服务错误
	ErrKnightGroupOnlineChannelidInvalid = -5873 // 用户不在房间

	//神秘人
	ErrYouknowwhoPrivilegeExpire = -5875 // 出错啦！你的神秘人已经过期
	ErrYouknowwhoMutualExclusion = -5876 // 进厅隐身和神秘人不可同时开启
	ErrYouknowwhoInvalidOper     = -5877 // 神秘人非法操作

	//骑士团5880-5889
	ErrKnightGroupScoreNotEnougth = -5880 // 骑士积分不足
	ErrKnightGroupScoreOrderExist = -5881 // 骑士积分订单已存在

	// 房间切换玩法相关 5900 - 5990
	ErrTopicChannelNotAllowSwitchPlay          = -5900 // 该主题房不支持切换玩法
	ErrTopicChannelFrequentlySwitchPlay        = -5901 // 操作过于频繁
	ErrTopicChannelCannotSwitchPlayWhenRelease = -5902 // 主题房发布过程中不可以修改小队信息和玩法或重复发布哦
	ErrTopicChannelReleaseFreezing             = -5903 // 发布房间正在冷却中，请稍后再试
	ErrTopicChannelNotOwnerSwitchPlay          = -5904 // 房间正在发布中，仅房主可切换房间玩法
	ErrTopicChannelChangeFreezing              = -5905 // 修改主题房CD中
	ErrTopicChannelTooManyMemberSwitchPlay     = -5906 // 房间人数过多无法直接切换
	ErrTopicChannelOwnerNotReal                = -5907 // 房主没有实名认证

	// tab配置后台 -6000-6100
	ErrConfigTabDeleteWarn            = -6000 // 存在分类与主题绑定，不能删除
	ErrUpdateCategoryPlatformTypeWarn = -6001 // 分类的展示平台属性无法修改
	ErrConfigTabConflictWarn          = -6002 // 配置展示平台和分类冲突
	ErrConfigTabHeadNotConfig         = -6003 // 没有配置首页配置

	// 临时房 6010 - 6030
	ErrTempchannelInvalidChannelid = -6010 // 临时房id无效

	//房间密码错误限制
	ErrChannelPwdErrLimit = -6031 // 错误操作频繁，24小时内您将无法再通过输入密码进入该房间

	// 礼物合成 6041 - 6050
	ErrConversionComposeGiftConfInvalid             = -6041 // 无效的合成礼物配置
	ErrConversionMaterialTotalPriceNotEnough        = -6042 // 原料价值不足以合成目标礼物
	ErrConersionComposeGiftConfPackagInvalid        = -6043 // 合成礼物包裹配置错误
	ErrConersionComposeGiftMaterialUnitPriceTooHigh = -6044 // 原材料单价不可高于合成礼物单价
	ErrConersionComposeGiftLossTooMuch              = -6045 // 所选方案亏损太多啦

	// 组队大厅错误码 6051-6079
	ErrGameHallAuditErr               = -6051 // 服务出现问题
	ErrGameHallAuditTextReject        = -6052 // 涉及违规用语，请文明交流
	ErrGameHallEnterRoomLimit         = -6053 // 该房间人数已满，再看看其他车队吧
	ErrGameHallSendMsgLimit           = -6054 // 今天次数已经用光啦，明天再来吧
	ErrGameHallSendMsgNotMatchTab     = -6055 // 需要您处于对应玩法房才可以发送哦
	ErrGameHallSendMsgMuchPeople      = -6056 // 您的房间人数较多，该功能暂时不可以使用
	ErrGameHallSendMsgNotInOwnChannel = -6057 // 需要您回到自己的房间才可以发送哦
	ErrGameHallSendMsgNotInChannel    = -6058 // 需要您处于自己的房间才可以发送哦
	ErrGameHallSendMsgReachThreshold  = -6059 // 今天您发言数量达上限了哦
	ErrGameHallSendMsgBan             = -6060 // T盾禁言
	ErrGameHallSendMsgBusinessBan     = -6061 // 业务后台禁言
	ErrGameHallJumpMsgExpired         = -6062 // 跳转消息已过期
	ErrGameHallEnterRoomLockLimit     = -6063 // 很抱歉，该房间设置了锁房不可进入
	ErrGameHallNoSendTeamMsg          = -6064 // 暂时不开放该功能，可以点击输入框先去聊聊天哦～

	// 背包风控服务
	ErrRiskControlBackpackTbeanLimit       = -6080 // 风控限制,业务T豆余额不足
	ErrRiskControlBackpackCountLimit       = -6081 // 风控限制,包裹数量额度不足
	ErrRiskControlBackpackSignalCountLimit = -6082 // 风控限制，单次包裹数量限制
	ErrRiskControlBackpackSignalTbeanLimit = -6083 // 风控限制,单次T豆价值限制
	ErrRiskControlBackpackConfigNotFound   = -6084 // 找不到对应业务风控配置
	ErrRiskControlBackpackOrderidInvalid   = -6085 // 风控限制,订单格式错误
	ErrRiskControlBackpackBusinessNotFound = -6086 // 风控限制,找不到对应业务配置
	ErrRiskControlBackpackAuthCheckFail    = -6087 // 风控限制,密钥检查错误
	ErrRiskControlBackpackSysFail          = -6088 // 风控系统错误
	ErrRiskControlBackpackDuplicateOrderid = -6089 // 订单重复

	// 发奖平台风控
	ErrRiskControlAwardCenterOrderExist       = -6095 // 订单已存在
	ErrRiskControlAwardCenterOrderInvalid     = -6096 // 订单号格式有误
	ErrRiskControlAwardCenterSignalCountLimit = -6097 // 风控限制，超出单次发放数量限制
	ErrRiskControlAwardCenterConfigInvalid    = -6098 // 业务配置有误
	ErrRiskControlAwardCenterDailyCountLimit  = -6099 // 风控限制，超出每日发奖人次限制

	// 娱乐玩法熔断
	ErrRiskControlPlobGameFuse = -6100 // 业务熔断

	// 昵称头像上传推送
	ErrUserNicknameViolate  = -6110 // 用户昵称违规
	ErrUserProfileViolate   = -6111 // 用户头像违规
	ErrChannelNameViolate   = -6112 // 房间名违规
	ErrChannelProfilViolate = -6113 // 房间头像违规

	//处罚禁止进入房间
	ErrBannedEnterChannel = -6120 // 禁止进房

	//踢号失败，请重试
	ErrBannedKickUserFail = -6121 // 踢号失败，请重试

	// 房间小队 6130-6140
	ErrChannelTeamNotFount = -6130 // 数据不存在
	ErrChannelTeamToast    = -6131 // 处理信息有误

	// 游戏雷达相关
	ErrGameRadarConfigUpdated      = -6141 // 游戏雷达配置已更新，请重新获取
	ErrGameRadarGameCardIncomplete = -6142 // 游戏卡缺少必填信息无法打开雷达，请先将信息填写完整哦
	ErrGameRadarIncomplete         = -6143 // 雷达信息缺少必填选项，请先将信息填写完整哦
	ErrGameRadarTooManyCharacter   = -6144 // 字数太多啦
	ErrGameRadarQuickFailed        = -6145 // 无法快速开启雷达
	ErrGameRadarModelIncorrect     = -6146 // 无正确雷达游戏模式
	ErrGameRadarIsClosed           = -6147 // 雷达已按时关闭，点击再次开启
	ErrGameRadarInviteExceed       = -6148 // 你已经约玩很多人啦，耐心等待对方回应吧～
	ErrGameRadarIsAlreadyOpened    = -6149 // 雷达已经开启

	// 房间抽奖 6150-6160
	ErrChannelLotteryBeginFail                 = -6150 // 房间抽奖开始失败
	ErrChannelLotteryTextErr                   = -6151 // 敏感词
	ErrChannelLotteryTimeViolate               = -6152 // 时间不对
	ErrChannelLotteryJoinLotteryErr            = -6153 // 参与抽奖失败
	ErrChannelLotteryInfoErr                   = -6154 // 处理信息有误
	ErrChannelLotteryJoinLotteryConditionLimit = -6155 // 参与抽奖条件受限

	//房间踢人
	ErrChannelKickoutNotInChannel = -6160 // 该用户不在房间

	// 扩列墙
	ErrSayhiCountExceedDayLimit = -6170 // 今天的搭讪已经超出上限啦~
	ErrChatCardExamineFailed    = -6171 // 扩列卡片审核不通过~
	ErrChatCardHandlePrivate    = -6172 // 扩列卡片仅自己可见
	ErrChatCardHandleFailed     = -6173 // 扩列卡片处理失败

	//邀请进房相关
	ErrInvitePlayerFromChannel = -6180 // 互相关注成为玩伴后，才能邀请Ta进房一起玩哦

	// 服务器通用错误参数
	ErrRequestParamInvalid  = -6190 // 请求参数错误
	ErrRepositoryFailed     = -6191 // 存储错误
	ErrExternalSystemFailed = -6192 // 外部第三方系统错误
	ErrCommApiScrap         = -6193 // 接口已经下线

	//官方频道
	ErrOfficialChannelNotInRunning          = -6200 // 无主播时官频不可送礼哦
	ErrOfficialChannelScheduleAlreadyChange = -6201 // 排班信息已更新，请重新获取再修改
	ErrOfficialChannelPermissionDenied      = -6203 // 操作用户权限不足

	//活动大房错误码
	ErrSuperChannelTypeUnsupport       = -6300 // 请更新至最新版本进入新类型房间
	ErrSuperChannelCloseMicQueueFailed = -6301 // 请先将小麦位上用户抱下麦后再关闭连麦

	// 会员隐身 -6350
	ErrUserVisitorRecordLimitOut = -6350 // 会员设置隐藏数量超过限制人数

	// 小纸条 -6360
	ErrSlipNoteStatusErr        = -6360 // 纸条状态异常
	ErrPickSlipNoteFailed       = -6361 // 纸条评论选择失败
	ErrNoEnoughSlipNote         = -6362 // 发布纸条次数已用完
	ErrSlipNotePoolIsEmpty      = -6363 // 纸条被人抢光啦，一会再试试吧~
	ErrExceedFetchSlipNoteLimit = -6364 // 瓶子被你掏空啦，明天再来吧~
	ErrHoldZeroMicMustContract  = -6370 // 成为签约成员才能上此麦位哟

	// 蒙面pk 6380-6399
	ErrMaskedPkNotQualification        = -6380 // 该厅无权限参与比赛
	ErrMaskedPkNotInActivityTime       = -6381 // 不在活动期
	ErrMaskedPkInPkCannotCancel        = -6382 // 正在pk中，无法取消
	ErrMaskedPkCancelCntLimit          = -6383 // 取消次数过多，无法取消
	ErrMaskedPkNotJoinGame             = -6384 // 未参与pk活动
	ErrMaskedPkCannotMatching          = -6385 // 该状态无法开始匹配
	ErrMaskedPkNotEnoughChip           = -6386 // 该时段比赛奖金发放完毕，请参加下时段的比赛
	ErrMaskedPkGameEndCannotMatching   = -6387 // 比赛即将结束，无法匹配
	ErrMaskedPkAddConfigTimeDupliacate = -6388 // 比赛时间与现有的比赛重叠
	ErrMaskedPkHadGivenUp              = -6389 // 已有管理员放弃了本场比赛，请参加下时段的比赛
	ErrMaskedPkCannotGivenUp           = -6390 // 已参与了本场比赛，无法放弃
	ErrMaskedPkInLivePk                = -6391 // 正在进行直播pk，无法开始蒙面pk匹配

	// obs-gateway 6400-6599

	// 常用错误码
	ErrObsgwNoSuchKey = -6460 // 指定的键不存在

	// 其他错误码
	ErrObsgwAccessDenied    = -6400 // 拒绝访问
	ErrObsgwExpiredToken    = -6414 // 过期凭证
	ErrObsgwInvalidArgument = -6425 // 非法参数
	ErrObsgwInvalidToken    = -6442 // 非法凭证
	ErrObsgwNoSuchBucket    = -6458 // 指定的空间不存在

	// 非常用错误码
	ErrObsgwAccessPointAlreadyOwnedByYou                   = -6401
	ErrObsgwAccountProblem                                 = -6402
	ErrObsgwAllAccessDisabled                              = -6403
	ErrObsgwAmbiguousGrantByEmailAddress                   = -6404
	ErrObsgwAuthorizationHeaderMalformed                   = -6405
	ErrObsgwBadDigest                                      = -6406
	ErrObsgwBucketAlreadyExists                            = -6407
	ErrObsgwBucketAlreadyOwnedByYou                        = -6408
	ErrObsgwBucketNotEmpty                                 = -6409
	ErrObsgwCredentialsNotSupported                        = -6410
	ErrObsgwCrossLocationLoggingProhibited                 = -6411
	ErrObsgwEntityTooSmall                                 = -6412
	ErrObsgwEntityTooLarge                                 = -6413
	ErrObsgwIllegalLocationConstraintException             = -6415
	ErrObsgwIllegalVersioningConfigurationException        = -6416
	ErrObsgwIncompleteBody                                 = -6417
	ErrObsgwIncorrectNumberOfFilesInPostRequest            = -6418
	ErrObsgwInlineDataTooLarge                             = -6419
	ErrObsgwInternalError                                  = -6420
	ErrObsgwInvalidAccessKeyId                             = -6421
	ErrObsgwInvalidAccessPoint                             = -6422
	ErrObsgwInvalidAccessPointAliasError                   = -6423
	ErrObsgwInvalidAddressingHeader                        = -6424
	ErrObsgwInvalidBucketName                              = -6426
	ErrObsgwInvalidBucketState                             = -6427
	ErrObsgwInvalidDigest                                  = -6428
	ErrObsgwInvalidEncryptionAlgorithmError                = -6429
	ErrObsgwInvalidLocationConstraint                      = -6430
	ErrObsgwInvalidObjectState                             = -6431
	ErrObsgwInvalidPart                                    = -6432
	ErrObsgwInvalidPartOrder                               = -6433
	ErrObsgwInvalidPayer                                   = -6434
	ErrObsgwInvalidPolicyDocument                          = -6435
	ErrObsgwInvalidRange                                   = -6436
	ErrObsgwInvalidRequest                                 = -6437
	ErrObsgwInvalidSecurity                                = -6438
	ErrObsgwInvalidSoapRequest                             = -6439
	ErrObsgwInvalidStorageClass                            = -6440
	ErrObsgwInvalidTargetBucketForLogging                  = -6441
	ErrObsgwInvalidUri                                     = -6443
	ErrObsgwKeyTooLongError                                = -6444
	ErrObsgwMalformedAclError                              = -6445
	ErrObsgwMalformedPostRequest                           = -6446
	ErrObsgwMalformedXml                                   = -6447
	ErrObsgwMaxMessageLengthExceeded                       = -6448
	ErrObsgwMaxPostPreDataLengthExceededError              = -6449
	ErrObsgwMetadataTooLarge                               = -6450
	ErrObsgwMethodNotAllowed                               = -6451
	ErrObsgwMissingAttachment                              = -6452
	ErrObsgwMissingContentLength                           = -6453
	ErrObsgwMissingRequestBodyError                        = -6454
	ErrObsgwMissingSecurityElement                         = -6455
	ErrObsgwMissingSecurityHeader                          = -6456
	ErrObsgwNoLoggingStatusForKey                          = -6457
	ErrObsgwNoSuchBucketPolicy                             = -6459
	ErrObsgwNoSuchLifecycleConfiguration                   = -6461
	ErrObsgwNoSuchTagSet                                   = -6462
	ErrObsgwNoSuchUpload                                   = -6463
	ErrObsgwNoSuchVersion                                  = -6464
	ErrObsgwNotImplemented                                 = -6465
	ErrObsgwNotModified                                    = -6466
	ErrObsgwNotSignedUp                                    = -6467
	ErrObsgwOperationAborted                               = -6468
	ErrObsgwPermanentRedirect                              = -6469
	ErrObsgwPreconditionFailed                             = -6470
	ErrObsgwRedirect                                       = -6471
	ErrObsgwRequestHeaderSectionTooLarge                   = -6472
	ErrObsgwRequestIsNotMultiPartContent                   = -6473
	ErrObsgwRequestTimeout                                 = -6474
	ErrObsgwRequestTimeTooSkewed                           = -6475
	ErrObsgwRequestTorrentOfBucketError                    = -6476
	ErrObsgwRestoreAlreadyInProgress                       = -6477
	ErrObsgwServerSideEncryptionConfigurationNotFoundError = -6478
	ErrObsgwServiceUnavailable                             = -6479
	ErrObsgwSignatureDoesNotMatch                          = -6480 // 签名不一致
	ErrObsgwSlowDown                                       = -6481
	ErrObsgwTemporaryRedirect                              = -6482
	ErrObsgwTokenRefreshRequired                           = -6483
	ErrObsgwTooManyAccessPoints                            = -6484
	ErrObsgwTooManyBuckets                                 = -6485
	ErrObsgwUnexpectedContent                              = -6486
	ErrObsgwUnresolvableGrantByEmailAddress                = -6487
	ErrObsgwUserKeyMustBeSpecified                         = -6488
	ErrObsgwNoSuchAccessPoint                              = -6489
	ErrObsgwInvalidTag                                     = -6490
	ErrObsgwMalformedPolicy                                = -6491

	// 监管需求，创建公会群组
	ErrGuildCreateLevelNotEnough = -6501 // 创建失败，账号需达到平台等级%d级和实名认证后才可创建公会。
	ErrGroupCreateLevelNotEnough = -6502 // 创建失败，账号需达到平台等级%d级和实名认证后才可创建群组。

	// 审核配置相关 6520-6540
	ErrSearchCountLimit         = -6520 // 搜索过于频繁，请稍后再试~
	ErrContentInvalidInputAgain = -6521 // 内容不合规，请重新输入
	ErrCensoringAuditing        = -6525 // 正在审核中，请稍后

	// 亲密（挚友）关系相关 6541-6560
	ErrFellowInviteExistingUniqueBind       = -6541 // 对方已经跟其他人建立了唯一关系
	ErrFellowInviteNotExist                 = -6542 // 这条邀请已经失效了哦
	ErrFellowInviteExistingFellowBind       = -6543 // 你们已经存在亲密关系了~
	ErrFellowInviteNoSiteTarget             = -6544 // 对方的挚友位不足，无法绑定
	ErrFellowInviteNoSiteSelf               = -6545 // 自己的挚友位不足
	ErrFellowInviteExistingFellowInvite     = -6546 // 你已经向Ta发送了挚友申请
	ErrFellowFellowAddBlacklist             = -6547 // 无法拉黑挚友哦
	ErrFellowFellowExistingInviteFromTarget = -6548 // Ta已经向你发送了挚友申请，先去看看吧～
	ErrFellowFellowFellowTypeNotMatch       = -6549 // 非唯一关系类型错误
	ErrFellowInviteHandledInvite            = -6550 // 对方已处理你的申请，不能撤回了哟
	ErrFellowUnboundDuplicate               = -6551 // 你已发起了关系解除
	ErrFellowNotFellow                      = -6552 // 你们还不是挚友，无法使用此功能
	ErrFellowInviteCpExist                  = -6553 // 你已经有CP关系，无法发起申请哦
	ErrFellowInviteBlacklist                = -6554 // 你已拉黑对方或被对方拉黑，无法发出申请
	ErrFellowInviteMaxSite                  = -6555 // 对方挚友关系已达上限，无法向Ta发出申请
	ErrFellowChannelInviteDuplicate         = -6556 // 你已有申请正在等待回应，过一会儿再来吧~
	ErrFellowChannelInviteNotInRoom         = -6557 // Ta已经离开房间，无法发起申请
	ErrRareInvalid                          = -6558 // 你没有该稀缺关系或已过期
	ErrFellowUkwNotAllowSendPresent         = -6559 // 神秘人期间不可送挚友礼物
	ErrFellowUkwNotAllowReceivePresent      = -6560 // 对方是神秘人，不可送挚友礼物

	// cp战 6561-6580
	ErrChannelCpGameCannotChangePhase    = -6561 // 无法切换到该阶段
	ErrChannelCpGameCannotAddTime        = -6562 // 此阶段最多只能加十分钟哦~不能再加了
	ErrChannelCpGameCannotEnterNextPhase = -6563 // 由于没人上麦，不可进入下一阶段哦
	ErrChannelCpGameCannotHoldMvpMic     = -6564 // 非MVP用户不能上MVP麦哦

	// 常用设备相关 6581-6600
	ErrUsualDiviceNotUsualDeivceFace       = -6581 // 为保障您的账号财产安全，请进行安全验证
	ErrUsualDiviceNotUsualDeivceMessage    = -6582 // 为保障您的账号财产安全，请进行安全验证
	ErrUsualDiviceNotUsualDeivceBindPhone  = -6583 // 当前设备不是常用设备，需要绑定手机号
	ErrUsualDiviceNotUsualDeivceLowVersion = -6584 // 为了保障您的账号财产安全，当前操作需要进行安全验证，请升级至最新版TT语音以完成验证
	ErrUsualDiviceNotUsualDeivceOnlyFace   = -6585 // 为保障您的账号财产安全，请进行安全验证

	// 接歌抢唱 6601-6620
	ErrSingARoundChannelInfoNotMatch          = -6601 // 房间状态信息不匹配
	ErrSingARoundNoVacancyIsLeft              = -6602 // 游戏位已被占满
	ErrSingARoundYouHaveBeenEliminated        = -6603 // 你已被淘汰
	ErrSingARoundHasPassedTheGrabMicStage     = -6604 // 已经过了抢唱阶段
	ErrSingARoundHasPassedTheHelpStage        = -6605 // 已经过了帮唱阶段
	ErrSingARoundHasPassedTheSingingStage     = -6606 // 已经过了唱歌阶段
	ErrSingARoundCanNotJoinTheGame            = -6607 // 当局人数已满，无法加入游戏
	ErrSingARoundCanNotPrepare                = -6608 // 暂时无法准备，请稍后再试
	ErrSingARoundLimitBeh                     = -6609 // 你被反馈在该场景捣乱，已被进行行为限制
	ErrSingARoundCanNotJoinThePassThroughGame = -6610 // 闯关模式下中途不能参与哟
	ErrSingARoundJoinLimit                    = -6611
	ErrSingARoundNotSupportCouple             = -6612 // 更新至最新版本后才可进入该专区

	// 房间礼物红包 6621-6640
	ErrChannelRedPacketSendLimit      = -6621 // 今天发红包已达上限，明天可以继续
	ErrChannelRedPacketPublicMsgFail  = -6622 // 公屏内容不合适，需要调整下喔~
	ErrChannelRedPacketConfNotExist   = -6623 // 红包配置不存在或已失效
	ErrChannelRedPacketOrderNotExist  = -6624 // 红包订单不存在
	ErrChannelRedPacketChannelTypeErr = -6625 // 该类型房间不支持红包功能

	// 幸运礼物 6641-6660
	ErrMagicSpiritSendLimit          = -6641 // 送太快啦~明天再试试吧~
	ErrMagicSpiritOrderNotExist      = -6642 // 订单不存在
	ErrMagicSpiritNotValid           = -6643 // 系统维护，暂不支持送出该礼物，请稍后再试
	ErrMagicSpiritSendPerAmountLimit = -6644 // 送太快啦~稍后再试试吧~
	ErrMagicSpiritOrderExist         = -6645 // 订单重复

	// ktv房间 6661-6690
	ErrKtvOperationAddSongDeny    = -6661 // 无法点歌
	ErrKtvOperationDeleteSongDeny = -6662 // 无法删除
	ErrKtvOperationCutSongDeny    = -6663 // 无法切歌
	ErrKtvOperationTopSongDeny    = -6664 // 无法顶歌
	ErrKtvOperationError          = -6665 // 操作失败，请稍后重试
	ErrKtvClose                   = -6666 // KTV功能维护中
	ErrKtvBurstError              = -6667 // 爆灯失败

	// 黑暗礼物奖励 6691-6699
	ErrDarkGiftBonusBuffConfErr = -6691 // 配置错误

	// httpproxy 6700 ~ 6749
	ErrHttpproxyTimeout = -6701 // 网络访问超时

	// K歌之王 -6750 - -6770
	ErrMusicRankLocateFailure = -6750 // 定位失败

	//房间包厢6771 ~ 6780
	ErrSendMsgChannelBoxIdErr = -6771 // 房间包厢ID错误

	// 房间版本控制 6781 - 6790
	ErrChannelVersionControlHit = -6781 // 当前版本不支持，请升级版本
	ErrChannelVersionControlBan = -6782 // 该房间玩法正在升级维护中，逛逛其他玩法吧~

	// 主播审核 6791 - 6810
	ErrAnchorCheckDbErr          = -6791 // 数据库错误
	ErrAnchorCheckStartRecordErr = -6792 // 开始录制错误
	ErrAnchorCheckStopRecordErr  = -6793 // 结束录制错误
	ErrAnchorCheckStatusErr      = -6794 // 状态错误

	//团战房 6811-6850
	ErrMeleeChannelWhiteListUpperLimit = -6811 // 人数超过上麦名单上限
	ErrChannelHoldMicSizeLimit         = -6812 // 上麦人数已满
	ErrChannelApplyMicTokenErr         = -6813 // 上麦token错误
	ErrChannelReapplyMic               = -6814 // 重复申请麦位
	ErrDingCallNameAuditing            = -6815 // 闪电召集名称正在审核中，请稍后
	ErrDingCallNickNameReject          = -6816 // 闪电召集名称审核不通过，请修改后发起一键召集
	ErrDingCallGroupUnvalid            = -6817 // 房主未创建闪电召集
	ErrDingCallSecretErr               = -6818 // 密令错误，请重新输入
	ErrDingCallSmsCodeErr              = -6819 // 短信验证码错误
	ErrDingCallSecretClose             = -6820 // 管理员关闭了闪电密令，请重试
	ErrDingCallSecretOpen              = -6821 // 管理员打开了闪电密令，请重试
	ErrDingCallNickNameAuditing        = -6822 // 你的闪电召集昵称正在审核中，请稍后
	ErrDingCallNameReject              = -6823 // 你的闪电召集昵称审核不通过，请修改后发起一键召集
	ErrDingCallVerifyCodeTooMuch       = -6824 // 操作频繁，请24小时之后重置
	ErrDingCallExceedUser              = -6825 // 已达到闪电召集绑定上限
	ErrDingCallNotifyMsgHandled        = -6826 // 消息已处理
	ErrMeleeChannelNoOnMicAccess       = -6827 // 无上麦权限
	ErrDingCallIsAuditing              = -6828 // 申请审核中，请稍后再试
	ErrDingCallPhoneUnvalid            = -6829 // 号码无效
	ErrDingCallExceedChannelCnt        = -6830 // 超出人数上限
	ErrDingCallTooOften                = -6831 // 线路繁忙，请稍后再试
	ErrDingCallUserHasAdd              = -6832 // 已加入该房间的闪电召集
	ErrMeleeChannelApplySensitive      = -6833 // 您的申请内容不合规，请重新发起申请
	ErrDingCallNotUse                  = -6834 // 功能维护中

	// 航海寻宝 6851-6870
	ErrOnePieceBuyChanceLimit               = -6851 // 购买失败，请重试
	ErrOnePieceUseChanceDailyLimit          = -6852 // 导航发现您今日航行过久，请勿疲劳驾驶
	ErrOnePieceRemainChanceNotEnough        = -6853 // 剩余汽油不足
	ErrOnePieceNotAvailable                 = -6854 // 服务升级中，请耐心等待~
	ErrOnePieceConfigFail                   = -6855 // 配置错误
	ErrOnePieceSeniorLotteryMultipleDisable = -6856 // 航道拥挤，暂无法航行~

	// pia戏服务 6871-6900
	ErrNoPiaPermission        = -6871 // 暂无权限
	ErrPiaIsRunning           = -6872 // 请结束剧本再进行选择
	ErrForbiddenMaskpk2pia    = -6873 // 参加蒙面PK期间无法切换到Pia戏
	ErrPiaSticktimeOverlapped = -6874 // Pia戏房间置顶时间重叠
	ErrPiaRoomTypeUnmatched   = -6875 // Pia戏房间类型错误
	ErrPiaRoomCommonErr       = -6876 // 通用错误

	// 说唱 -6901 - -6909
	ErrRapRespectCoolDown = -6901 // 手速太快啦，俺受不了，歇一会再点吧

	//对公结算业务错误码 	-6910 - -6949
	ErrExchangeDbErr             = -6910
	ErrNoMasterUid               = -6911 // 你不满足申请条件，无法申请。可前往[签约管理页-问答帮助]了解相关信息（会长没有对公）
	ErrExchangeDayApply          = -6912 // 今天发送申请次数已达上限
	ErrExchangeApplyScore        = -6913 // 你不满足申请条件，无法申请。可前往[签约管理页-问答帮助]了解相关信息（积分不满足）
	ErrExchangeYearApply         = -6914 // 你本年内的个人对公结算设置次数已达上限，无法申请
	ErrExchangeLock              = -6915 // 获取锁失败
	ErrExchangeUidNotMatch       = -6916 // 签署用户不匹配
	ErrExchangeTimeErr           = -6917 // 未到提现开放时间
	ErrExchangeIng               = -6918 // 正在提现
	ErrExchangeTypeNotSupport    = -6919 // 不支持的提现类型
	ErrExchangeOrderFinished     = -6920 // 提现次数已达上限，下个提现开放时间再来吧～
	ErrExchangeFlowIdNotMatch    = -6921 // FlowId不匹配
	ErrExchangeApplyStatus       = -6922 // 当前申请状态不允许
	ErrExchangePlaceCount        = -6923 // 当前公会暂无个人对公结算申请名额，无法申请
	ErrExchangeIsPublic          = -6924 // 已经是对公状态
	ErrExchangeApplyNotFinish    = -6925 // 上一个申请未完成
	ErrExchangeFlowExist         = -6926 // 签署流程已创建
	ErrExchangeStatus            = -6927 // 状态错误
	ErrWhiteMaxExceed            = -6928 // 超出最大值
	ErrExchangeIsPrivate         = -6929 // 已经是对私状态
	ErrExchangeMaxRange          = -6930 // 参数超出最大值
	ErrExchangeNotMaster         = -6931 // 没有对公主体
	ErrExchangeMasterPlaceCount  = -6932 // 公会当前暂无个人对公结算申请余额，无法同意申请
	ErrExchangeUndoneTransaction = -6933 // 还有未结清的积分
	ErrExchangeOrderNotExist     = -6934 // 提现编号不存在
	ErrExchangeSumNotFinish      = -6935 // 正在提现中
	ErrExchangeMasterNotReal     = -6936 // 会长没有实名认证
	ErrExchangeGetScoreFailed    = -6937 // 积分获取失败
	ErrExchangeGetGuildServer    = -6938 // 会长服务号ID不存在
	ErrExchangeHasNoGuild        = -6939 // 你不满足申请条件，无法申请。可前往[签约管理页-问答帮助]了解相关信息（没有公会）
	ErrMasterNotMaster           = -6940 // 不是对公会长
	ErrExchangeTtidNotExist      = -6941 // 暂无结果
	ErrExchangeTransfer          = -6942 // 您是对公合作的会长，请到公会经营管理后台发起提现

	//升级礼物错误码 -6950 - -6969
	ErrLevelupDbErr          = -6950 // 数据错误
	ErrLevelupParamErr       = -6951 // 参数错误
	ErrLevelupParentNotExist = -6952 // 父级礼物不存在
	ErrLevelupExpErr         = -6953 // 经验值错误
	ErrLevelupLevelErr       = -6954 // 等级值错误
	ErrLevelupLevelItemErr   = -6955 // 礼物等级没有达到
	ErrLevelupConfigErr      = -6956 // 礼物配置错误
	ErrLevelupVersionErr     = -6957 // 礼物版本错误，请重新进入频道拉取最新版本

	// 挂房听歌 -6970 - -6979
	ErrListeningHaveCheckedIn = -6970 // 今天已经成功打卡，明天再打卡吧
	ErrListeningTokenExpired  = -6971 // 登录信息过期
	ErrListeningErrorHttp     = -6972 // HTTP接口报错
	ErrListeningParamErr      = -6973 // 参数错误
	ErrListeningCtrlErr       = -6974 // 操作失败，请稍后再试

	// 结算单 -6980 - -7020
	ErrSettleDbErr                     = -6980 // 数据库错误
	ErrSettleParamErr                  = -6981 // 参数错误
	ErrSettleUploadErr                 = -6982 // 上传失败
	ErrSettleChangeStatusErr           = -6983 // 状态变更失败
	ErrSettleReceiptAssociateErr       = -6984 // 发票关联失败
	ErrSettleUnknownBillTypeErr        = -6985 // 结算单类型错误
	ErrSettleBillEmptyErr              = -6986 // 结算单数据为空
	ErrSettleBillNotFoundErr           = -6987 // 结算单不存在
	ErrSettleReceiptBillNotFoundErr    = -6988 // 发票单不存在
	ErrSettleDetailNotFoundErr         = -6989 // 明细不存在
	ErrSettleRecordMonthSettledErr     = -6990 // 录入已结算的月份
	ErrSettleFileRepeatUploadErr       = -6991 // 文件重复上传
	ErrSettleCreatingErr               = -6992 // 结算单创建中
	ErrSettleCommissionApiErr          = -6993 // 佣金平台接口错误
	ErrSettleComNoIdentifyErr          = -6994 // 未完成实名认证
	ErrSettleFrequentlyErr             = -6995 // 操作频繁，请稍后再试
	ErrSettleComCardPayErr             = -6996 // 用户银行卡打款异常
	ErrSettleComBankIncomplete         = -6997 // 银行卡信息不完整
	ErrSettleComInsufficientBalanceErr = -6998 // 佣金余额不足
	ErrSettleFileNotFoundErr           = -6999 // 结算单文件不存在
	ErrSettleNotAllowWithdrawErr       = -7000 // 当前无法提现
	ErrSettleWithdrawOverLimitErr      = -7001 // 提现异常，请联系TTID80119
	ErrSettleWithdrawFailedErr         = -7002 // 提现异常，请稍后再试
	ErrSettleWithdrawRepeatErr         = -7003 // 存在处理中的提现，请稍后查看
	ErrSettleWithdrawMinLimitErr       = -7004 // 提现金额不能小于1元
	ErrSettleReceiptAmountMatchErr     = -7005 // 结算单金额与发票金额不匹配
	ErrSettleReceiptVerifyErr          = -7006 // 发票查验错误
	ErrSettleReceiptRepeatErr          = -7007 // 重复使用，已关联其他订单
	ErrSettleReceiptItemErr            = -7008 // 发票项目名称错误
	ErrSettleReceiptPurchaserErr       = -7009 // 发票购买方信息错误
	ErrSettleReceiptSellerErr          = -7010 // 发票销售方信息错误
	ErrSettleWithdrawRiskErr           = -7011 // 本次提现失败，提现账号异常，有疑问请联系客服或TTID80513
	ErrSettleReceiptSysRetryErr        = -7012 // 系统错误，请稍后重试
	ErrSettleWithdrawRiskInternal      = -7013 // 提现异常，请检查您的账号
	ErrSettleWithdrawWithdrawnErr      = -7014 // 结算单已提现，请勿重复操作

	// 星际巡航 -7021 - -7040
	ErrStarTrekRoundIsOver         = -7021 // 正在计算上期结果，请稍后~
	ErrStarTrekInvestValLimit      = -7022 // 本轮投入值已达到上限，请耐心等待巡航结果~
	ErrStarTrekInvestTooOften      = -7023 // 操作太快拉，请稍后重试~
	ErrStarTrekInvestAddFail       = -7024 // 投入失败，请稍后重试~
	ErrStarTrekInvestRollbackFail  = -7025 // 回滚失败
	ErrStarTrekInvestDailyValLimit = -7026 // 当日最多投入100000豆补给值
	ErrStarTrekNotAvaliable        = -7027 // 系统维护中

	// rhythm -7041 - -7060
	ErrRhythmPermissionDenied                     = -7041 // 权限不足
	ErrRhythmBusinessLogicErr                     = -7402 // 业务逻辑错误
	ErrPersonalCertificationNotExist              = -7043 // 该认证已过期
	ErrNotUpToStandard                            = -7044 // 未达标
	ErrNoBindPhoneNumber                          = -7045 // 未绑定手机号
	ErrJoinSocialCommunityFans                    = -7046 // 社团核心成员席位已满，加入为普通成员
	ErrSocialCommunityApplyToJoin                 = -7047 // 需要申请加入社群
	ErrRejectJoinSocialCommunity                  = -7048 // 拒绝任何人加入社群
	ErrSocialCommunityUserAlreadyJoin             = -7049 // 用户寂静加入社群
	ErrSocialCommunityNoExist                     = -7050 // 社团不存在
	ErrSocialCommunityInvitationCodeIsValidInInit = -7051 // 资格审核中，请稍后再试
	ErrSocialCommunityInvitationCodeNotValid      = -7052 // 该邀请码已无效
	ErrSocialCommunityInvitationCodeNotThis       = -7053 // 此邀请码不属于你加入的第一个社团
	ErrSocialCommunityInvitationCodeHitRisk       = -7054 // 系统判定你的账号异常，提交失败
	ErrMuseRolePlayUserCardIsNotExist             = -7055 // 用户角色卡不存在

	// 神秘人 -7061 - -7065
	ErrUkwOpFromDenied   = -7061 // 神秘人期间不可进行该操作
	ErrUkwOpToDenied     = -7062 // 不可对神秘人进行该操作
	ErrUkwOpCannotFollow = -7063 // 不可关注神秘人

	// 礼物积分运营后台 -7066 - -7085
	ErrUserscoreMgrParamErr    = -7066 // 参数错误
	ErrUserscoreMgrParamMaxLen = -7067 // 查询个数超出最大范围
	ErrUserscoreMgrDbErr       = -7068 // 数据库错误
	ErrUserscoreMgrTaskWait    = -7069 // 等待上一个任务完成

	// 提审开关运营后台 -7100 ~ -7101
	ErrAccountIsolationSwOpenRepeatedErr   = -7100 // 账号隔离开关不可以重复开启
	ErrAccountIsolationSwInsertRepeatedErr = -7101 // 账号隔离配置不可以重复插入

	// 乐队
	ErrConcertBandMemberCannotStartWhenSinging = -7800 // 表演完当前这首歌才可以点歌噢
	ErrConcertAudienceCannotStartWhenSinging   = -7801 // 听乐队表演完当前这首歌才可以点歌噢
	ErrConcertJoinFailed                       = -7802 // 系统错误，请重新尝试加入演奏

	// 游戏券 -7831 - -7850
	ErrGameTicketOrderExist         = -7831 // 订单已存在
	ErrGameTicketConfigNotExist     = -7832 // 配置不存在
	ErrGameTicketRemainNotEnough    = -7833 // 余额不足
	ErrGameTicketTimeGapTooLarge    = -7834 // 系统时间差过大
	ErrGameTicketRechargeBuyFail    = -7835 // 购买失败，请重试
	ErrGameTicketRechargeUnder18    = -7836 // 未成年用户不允许购买
	ErrGameTicketRechargePerLimit   = -7837 // 超过单次购买限制
	ErrGameTicketRechargeDailyLimit = -7838 // 今日购买太多啦！明天再来吧~
	ErrCouponDailyLimit             = -7839 // 您今日优惠券领取次数已达上限！明天再来吧~

	// 营收铭牌 -7851 - -7870
	ErrRevenueNameplateActivityOrderExist = -7851 // 营收活动订单已存在
	ErrRevenueNameplateActivityDataErr    = -7852 // 营收活动数据库更新异常

	//ugc房间内玩什么 （数字炸弹，真心话大冒险） -7900 - -8000
	ErrMinigameNumBombInvalidParam   = -7900 // 数字炸弹设置参数不合法,客户端将重新同步
	ErrMinigameNumBombSettingInvalid = -7901 // 数字炸弹设置参数不合法

	// 流量卡 -8010 - -8020
	ErrGrantFlowCardOrderExist  = -8010 // 订单已存在
	ErrGrantFlowCardDatabaseErr = -8011 // 数据库错误

	// 心愿单 -8021 - -8040
	ErrWishListGiftNotFound   = -8021 // 礼物不存在
	ErrWishListGiftExpire     = -8022 // 你选择的礼物目前已下架，请重新选择心愿礼物~
	ErrWishListLiveRepeat     = -8023 // 同一场直播只能设置一次心愿单
	ErrWishListSensitiveWords = -8024 // 你填写的心愿答谢语有违规词汇，请检查后重新填写
	ErrWishListCommonErr      = -8025 // 心愿单业务错误

	// 对账 -8041 - -8060
	ErrReconcileNotSupport = -8041 // 对账配置不支持

	// 娱乐厅大冒险 -8061 - -8080
	ErrPgcAdventurePhraseErr          = -8061 // 阶段信息错误
	ErrPgcAdventureNotYourTurn        = -8062 // 还没轮到你
	ErrPgcAdventureNotHost            = -8063 // 仅主持人可操作
	ErrPgcAdventureBeginAdventureFail = -8064 // 参与用户不足以开启玩法哦～
	ErrPgcAdventureHostCtrlFail       = -8065 // 主持控制失败
	ErrPgcAdventureEnrollFail         = -8066 // 报名失败

	// 谜境相关业务错误码 -8081- -8188
	ErrMijingUserRepeatedCommentSameScenarioErr = -8081 // 不可以对同个剧本重复评分哦~
	ErrMijingInviteCountLack                    = -8082 // 今日还可邀请%d个人
	ErrMijingInviteExhaust                      = -8083 // 今日邀请次数已达上限
	ErrMijingSystemIsBeingMaintained            = -8084 // 系统正在维护中，请稍后重试~

	// 天配匹配-8190- -8200
	ErrPerfectMatchCancelLimit              = -8190 // 您今日取消匹配次数过多，无法匹配。明日再来吧~
	ErrPerfectMatchCancelOverMax            = -8191 // 超过每日取消匹配次数上限
	ErrPerfectMatchRefreshQuestionNotEnough = -8192 // 更换次数已达上限，无法继续更换
	ErrPerfectMatchSyatemErr                = -8193 // 服务异常，系统已取消匹配，金额已退款到对应账户中
	ErrPerfectMatchInMatch                  = -8194 // 正在进行匹配，无法重复发起
	ErrPerfectMatchAlreadyAnswer            = -8195 // 已提交答案，无法重复操作
	ErrPerfectMatchAlreadyGetQuestion       = -8196 // 已获取答案，无法重复操作
	ErrPerfectMatchOrderNotExist            = -8197 // 天配订单不存在
	ErrPerfectMatchNotInMatch               = -8198 // 已取消匹配，请退出重试
	ErrPerfectMatchCommonErr                = -8199 // 天配业务错误

	// 天配房间玩法 -8201-8220
	ErrPerfectMatchGamePublishCluesLimit           = -8201 // 发布线索达到上限
	ErrPerfectMatchGameGetGameInfoFail             = -8202 // 获取游戏信息失败
	ErrPerfectMatchGameCannotChangePhase           = -8203 // 无法切换游戏阶段
	ErrPerfectMatchGamePlayerCannotBlowLight       = -8204 // 玩家尚未达到爆灯条件
	ErrPerfectMatchGameCluesPropUseLimit           = -8205 // 该道具已经使用过了
	ErrPerfectMatchGamePlayerCanOnlyChooseOnce     = -8206 // 只能选择一个心动对象
	ErrPerfectMatchGameHoldMicFail                 = -8207 // 无法上游戏麦位
	ErrPerfectMatchGameNotStart                    = -8208 // 玩法暂未开启
	ErrPerfectMatchGameInitPlayersFail             = -8209 // 玩家信息初始化失败
	ErrPerfectMatchGameSwitchSchemeFailLockChannel = -8210 // 房间已上锁，无法切换到天配房间，请手动解锁
	ErrPerfectMatchGameSwitchSchemeFailStarting    = -8211 // 游戏进行中，无法切换模式

	// 公会合作 -8221-8240
	ErrGuildCoopHasGuild        = -8221 // 已加入公会
	ErrGuildCoopApproveErr      = -8222 // 审批错误
	ErrGuildCoopApplyLimit      = -8223 // 限制申请
	ErrGuildCoopLimitErr        = -8224 // 该账号处于申请流程中不能被限制，请先结束流程
	ErrGuildCoopApplyRepeat     = -8225 // 已加入合作库
	ErrGuildCoopStatusErr       = -8226 // 状态异常
	ErrGuildCoopNotRealName     = -8227 // 未实名认证
	ErrGuildCoopPhoneRegistered = -8228 // 该手机号已注册TT，请前往APP申请
	ErrGuildCoopNotFound        = -8229 // 该公会未加入合作库
	ErrGuildCoopSensitiveWords  = -8230 // 您输入的公会名称包含敏感词
	ErrGuildCoopCreateGuildErr  = -8231 // 创建公会失败
	ErrGuildCoopNotAllowDismiss = -8232 // 请先退出公会合作库

	// 猫猫餐厅 -8241-8260
	ErrChanceGameNotAvailable        = -8241 // 系统维护中
	ErrCatCanteenBuyChanceLimit      = -8242 // 购买量达到限制
	ErrCatCanteenUseChanceLimit      = -8243 // 使用量达到限制
	ErrCatCanteenChanceNotEnough     = -8244 // 鱼干余额不足
	ErrCatCanteenEntryItemNotEnough  = -8245 // 背包道具不足，请前往上一关获取
	ErrCatCanteenConfErr             = -8246 // 配置有误
	ErrCatCanteenLotteryDrawTooQuick = -8247 // 操作太快了，请稍后再试吧
	ErrCatCanteenOrderNotExist       = -8248 // 订单不存在

	// 营收外部游戏开放通用服务 -8261-8280
	ErrRevenueExtGameMountDuplicate      = -8261 // 游戏已挂载
	ErrRevenueExtGameBackstageOpFail     = -8262 // 配置操作失败
	ErrRevenueExtGameNotPermission       = -8263 // 无权限
	ErrRevenueExtGameMountFail           = -8264 // 游戏挂载失败
	ErrRevenueExtGameConfNotExist        = -8265 // 游戏配置不存在
	ErrRevenueExtGameCannotPlayOther     = -8266 // 互动游戏期间，不能开启其他玩法
	ErrRevenueExtGameCannotOpenInPlaying = -8267 // 请先关闭互动玩法直播~
	ErrRevenueExtGameGameNotExist        = -8268 // 游戏不存在

	// 荣耀世界 -8281-8300
	ErrGloryGetRedPointFail    = -8281 // 获取红点信息失败
	ErrGloryGetTaskInfoFail    = -8282 // 获取挑战任务信息失败
	ErrGloryRewardTaskFail     = -8283 // 领取挑战任务奖励失败
	ErrGloryFloatingLayerParam = -8284 // 悬浮层参数错误

	//荣耀世界-名流殿堂 -8301-8320
	ErrGloryCelebrityWeekRankFail   = -8301 // 获取名流周榜失败
	ErrGloryCelebrityPalaceInfoFail = -8302 // 获取名流殿堂信息失败
	ErrGloryCelebrityReplayNotFind  = -8303 // 该回顾不存在

	// 荣耀世界抽奖 -8321-8340
	ErrGloryLotteryOutOfPrestige     = -8321 // 声望不足
	ErrGloryLotteryOutOfLotteryTimes = -8322 // 领取次数不足

	//被房间拉黑不能进房
	ErrInChannelCreatorBlackEnterErr = -8341 // 暂不允许进入房间
	ErrRpcDeprecatedErr              = -8342 // 接口已废弃

	//channel业务错误  -8343 ~ -8400
	ErrChannelViewIdUsed                 = -8343 // 房间ID已被使用,不能再被分配
	ErrChannelViewIdInvalid              = -8344 // 房间ID不合法,不能被分配
	ErrChannelUserOwnChannelExist        = -8345 // 用户个人房已存在，不能再创建
	ErrChannelCreateNewChannelFailed     = -8346 // 创建新房间失败
	ErrChannelEnterBusinessTokenGenErr   = -8347 // 进房token生成错误
	ErrChannelEnterBusinessTokenCheckErr = -8348 // 进房token检查错误

	// 娱乐房推荐业务错误 -8500 ~ -8510
	ErrChannelRecommendQuickEntryConfigErr = -8500 // 配置错误
	ErrChannelRecommendRelationshipErr     = -8501 // 推荐关系链错误
	ErrChannelRecommendTopOverlayErr       = -8502 // 顶部浮窗错误

	// 交易中间件错误 -8511 ~ -8560
	ErrExchangeMiddlewareNotSupport          = -8511 // 不支持
	ErrExchangeMiddlewareClientErr           = -8512 // 客户端转换错误
	ErrExchangeMiddlewareMaxErr              = -8513 // 超出最大值
	ErrExchangeMiddlewareOrderLenErr         = -8514 // order长度超出最大值
	ErrExchangeMiddlewareParamErr            = -8515 // 参数错误
	ErrExchangeMiddlewareUidConssistentErr   = -8516 // Uid不一致
	ErrExchangeMiddlewareBalanceNotEnoughErr = -8517 // 余额不足
	ErrExchangeMiddlewareAppidNotExist       = -8518 // APPID不存在
	ErrExchangeMiddlewareSignErr             = -8519 // 签名错误
	ErrExchangeMiddlewareLock                = -8520 // 无法获取锁

	//房间操作权限管理 -8561 ~ -8570
	ErrChannelOperatePermissionNotSupport = -8561 // 不支持的操作

	//直播多人pk错误码 -8571 ~ -8590
	ErrChannelLiveMultiPkApplyCancelErr      = -8571 // 加入失败，邀请人已经取消了此次邀请
	ErrChannelLiveMultiPkTeamMemFullErr      = -8572 // 当前的PK队伍已经满员了
	ErrChannelLiveMultiPkApplyNoPkPermission = -8573 // 邀请失败，该主播暂无四人PK的权限
	ErrChannelLiveMultiPkTeamCancelErr       = -8574 // 组队结束，四人PK的发起方取消了此次PK邀请～
	ErrChannelLiveMultiPkTeamApplyingErr     = -8575 // 正在邀请好友加入队伍时不支持选择随机匹配哦，稍后试试吧～
	ErrChannelLiveMultiPkApplyFail           = -8576 // 邀请失败

	// 营收连麦管理服务错误码 -8591 ~ -8610
	ErrRevenueAudioStreamStartMemberBusy = -8591 // 房间繁忙

	// 房间榜单 channelemeberviprank -8611 ~ -8640
	ErrChannelRankDbErr         = -8611 // 数据库错误
	ErrChannelRankCacheErr      = -8612 // 缓存错误
	ErrChannelRankParseErr      = -8613 // 数据解析错误
	ErrChannelRankVipLevelErr   = -8614 // VIP等级错误
	ErrChannelRankReadConfErr   = -8615 // 配置异常
	ErrChannelRankOrderIdRepeat = -8616 // 订单号重复

	// 谜境 -8641 - -8840
	ErrMijingJingBadRequest                  = -8641 // 无效的请求
	ErrMijingJingDbError                     = -8642 // 数据库错误
	ErrMijingUnderMaintenance                = -8643 // 功能正在维护中，请稍后试试～
	ErrMijingFailedToReportChannelInviteTeam = -8644 // 邀请失败
	ErrMijingInternalServerError             = -8645 // 服务内部错误
	ErrMijingGenerateAvatarLimiteMaxErr      = -8646 // 啊哦，今日生成形象的次数已用完，请明天再来哦
	ErrMijingGetEscapeTipMsgLimit            = -8647 // Ta暂时离开了哦~明天再来吧~

	// 珍宝馆 -8850 - -8970
	ErrTreasureHouseActivityNotValid    = -8850 // 当前不在珍宝馆活动期
	ErrTreasureHouseClaimNotValid       = -8851 // 无法领取该权限
	ErrTreasureHouseNoPrivilege         = -8852 // 没有该礼物权限
	ErrTreasureHouseConditionTimeChange = -8853 // 条件发放阶段不允许修改时间
	ErrTreasureHouseActivityNotFound    = -8854 // 活动不存在
	ErrTreasureHouseWhiteListErr        = -8855 // 白名单错误

	// 用户召回 -8971 - -8990
	ErrUserRecallNoAllow = -8971 // 该用户已回归，不可再召回喔
	ErrUserRecallSmsSend = -8972 // 已经发过短信啦，试试其他方式吧~
	ErrUserRecallParams  = -8973 // 参数错误
	ErrUserRecallStatus  = -8974 // 状态错误

	// 虚拟主播 -9001 - -9020
	ErrVirtualAnchorNotAppLiveErr = -9001 // 虚拟形象开播中~请下播后再试

	// 用户召回发奖 -9021 - -9040
	ErrUserRecallRecordNotFound         = -9021 // 未找到召回记录
	ErrUserRecallRecordInvalidAwardCfg  = -9022 // 奖励配置错误
	ErrUserRecallRecordNotMeetCondition = -9023 // 不满足领奖条件

	// TT购周边商城 -9041 - -9050
	ErrTtPeripheralMallDbErr           = -9041 // DB错误
	ErrTtPeripheralMallStockNotEnough  = -9042 // 当前库存不足，可选择减少商品个数噢~
	ErrTtPeripheralMallProductNotFound = -9043 // 商品不存在
	ErrTtPeripheralMallLimit           = -9044 // 购买上限

	// 礼物图鉴 -9051 - -9060
	ErrIllustrationNotFound = -9051 // 未知图鉴
	ErrIllustrationParamErr = -9052 // 参数错误
	ErrIllustrationDbErr    = -9053 // 数据库错误
	ErrIllustrationGetErr   = -9054 // 获取礼物图鉴信息错误

	// 荣耀世界魔法祈愿错误码 -9061 ~ -9070
	ErrGloryMagicOutOfPrestige = -9061 // 荣耀星钻不足

	//房间麦位布局 -9071 - -9090

	//内部错误，例如获取锁失败之类的
	ErrChannelMicLayoutInnerErr                  = -9071 // 内部错误
	ErrChannelMicLayoutSwitchCloseErr            = -9072 // 房间麦位布局功能已关闭
	ErrChannelMicLayoutSwitchCurTimePresentErr   = -9073 // 当前在时间礼物播放中，不能切换
	ErrChannelMicLayoutSwitchTemplateInvalidErr  = -9074 // 布局模板无效
	ErrChannelMicLayoutParamInvalid              = -9075 // 参数不合法
	ErrChannelMicLayoutMgrInnerErr               = -9076 // 布局管理内部错误
	ErrChannelMicLayoutMgrTemplateNotExist       = -9077 // 布局模板不存在
	ErrChannelMicLayoutMgrTemplateInvalid        = -9078 // 布局模板参数无效
	ErrChannelMicLayoutMgrGetLockFailed          = -9079 // 获取锁失败
	ErrChannelMicLayoutUnsupportSchemeDetailType = -9080 // 当前在不支持的房间玩法中，不能切换布局
	ErrChannelMicLayoutSwitchIsSameMicLayout     = -9081 // 当前已在该布局中，无需切换
	ErrChannelMicLayoutSwitchFreqLimitErr        = -9082 // 切换布局过于频繁，请稍后再试~
	ErrChannelMicLayoutMgrLayoutNotExist         = -9083 // 布局不存在
	ErrChannelMicLayoutMgrLayoutNotChange        = -9084 // 布局未发生变化

	// 平台教育考试 -9091 ~ -9100
	ErrPlatformExamStartStatusErr = -9091 // 当前考试未配置试题或配置试题数量小于5，请重新检查
	ErrPlatformExamNotFound       = -9092 // 试卷不存在

	//pc极速版房间相关错误 -9101 ~ -9150
	ErrPcLfgNotSupportChannel     = -9101 // 不支持的房间
	ErrPcLfgInvalidShortcutNumber = -9102 // 至少保留两个便捷工具哦

	//T次元房间相关错误吗 -9151 ~ -9200
	ErrTcyNotSupportChannel = -9151 // 不支持的房间类型
	ErrTcyKickOutChannelErr = -9152 // 踢出其他房间失败

	//房间音频配置相关错误码 -9201 ~ -9220
	ErrChannelMicAudioUserChannelNotCreated      = -9201 // 用户未创建个人房
	ErrChannelMicAudioBitRateUpgradeBudgetRanOut = -9202 // 今日码率调整次数已用完
	ErrChannelMicAudioBitRateConfigNotExist      = -9203 // 不存在要升级的码率配置
	ErrChannelMicAudioBitRateUserAlreadyUpgraded = -9204 // 房间已经处于高码率

	// *****************************不要在 -10000 到 -20000之间定义业务错误码begin****************************************************

	// *****************************这个范围错误码会归入系统失败调用统计begin***********************************************************

	// SYSTEM_ERR -10000   ([-20000, -10000] 错误码会归入系统失败调用统计)
	ErrSendMsgFriendVerifyFailed             = -10001
	ErrSendMsgWriteMsgFailed                 = -10002
	ErrSendMsgGetNickFailed                  = -10003
	ErrSendMsgGetUseraccountFailed           = -10004
	ErrFriendSystemErr                       = -10005
	ErrAccountSystemErr                      = -10006
	ErrSeqGenSystemErr                       = -10007
	ErrHeadImageSystemErr                    = -10008
	ErrCheckFriendSystemErr                  = -10009
	ErrTimelineSvrSystemErr                  = -10010
	ErrAttachmentSvrSystemErr                = -10011
	ErrGuildTimelineSvrSystemErr             = -10012
	ErrSearchSvrSystemErr                    = -10013
	ErrGuildSvrSystemErr                     = -10014
	ErrGameSvrSystemErr                      = -10015
	ErrAlbumSvrSystemErr                     = -10016
	ErrGiftpkgSvrSystemErr                   = -10017
	ErrCircleTimelinesvrErr                  = -10018
	ErrContainsSensitiveWord                 = -10019
	ErrCirclesvrErr                          = -10020
	ErrMissionTimelinesvrErr                 = -10021
	ErrAntisvrErr                            = -10022
	ErrPublicErr                             = -10023
	ErrGenerateTimelineErr                   = -10024
	ErrObsessionErr                          = -10025
	ErrSdkMsgErr                             = -10026
	ErrChannelImErr                          = -10027
	ErrEventcenterErr                        = -10028
	ErrCircletimelineErr                     = -10029
	ErrTbeanApiErr                           = -10030
	ErrPointsApiErr                          = -10031
	ErrCurrencyApiErr                        = -10032
	ErrCurrencyExchangeApiErr                = -10033
	ErrGuildTransNotFinish                   = -10034
	ErrChannelmicSvrErr                      = -10035
	ErrChannelolSvrErr                       = -10036
	ErrChannelHoldmicUserNotInCurrentChannel = -10037
	ErrAntispamSvrErr                        = -10038
	ErrUgcFriendshipSvrErr                   = -10039
	ErrAntispamlogicSvrErr                   = -10040
	ErrAntispamTimeoutErr                    = -10041
	ErrAntispamcxtsvrSvrError                = -10042
	ErrApiUnimplement                        = -10043 // 接口未定义
	ErrTimelineDataOverFlowErr               = -10044 // timeline返回包过大
	ErrHttpUnmarshalError                    = -10050 // HTTP解析错误
	ErrHttpParaError                         = -10051 // HTTP参数错误
	ErrIsNotMasterError                      = -10052

	//业务熔断错误码
	ErrErrFuseValid = -10060 // 服务繁忙，请稍后再试

	// 业务基础系统错误码 [-10061,10069]
	ErrAuthLogicErr    = -10061
	ErrRealnameAuthErr = -10062
	ErrGuildErr        = -10063
	ErrBizBaseSysErr   = -10066 // 系统错误
	ErrRevenueSvrErr   = -10070 // // 营收系统错误

	//房间基础系统错误码[-10100 ~ 10199] begin -----------------
	ErrChannelLevelDbOperationFailed      = -10100
	ErrChannelMicDbOperationFailed        = -10101
	ErrChannelMsgExpressDbOperationFailed = -10102
	ErrChannelCoreLogicInternalErr        = -10103

	// 房间小游戏流程控制器
	ErrChannelOpenGameControllerSysErr     = -10104
	ErrChannelSvrDbErr                     = -10105
	ErrChannelSvrRedisErr                  = -10106
	ErrChannelOperatePermissionMgrSysErr   = -10107
	ErrChannelMusicSysErr                  = -10108
	ErrChannelOpenGameLogicSysErr          = -10109
	ErrChannelOpenGameAuthSysErr           = -10110
	ErrOfficialLiveChannelInternalErr      = -10111
	ErrChannelBackgroundSysErr             = -10112
	ErrChannelOpenGameControllerHttpSysErr = -10113
	ErrChannelolSvrRedisErr                = -10114
	ErrTmpChannelOpenGameSysErr            = -10115
	ErrChannelOpenGameSysErr               = -10116
	ErrChannelAudioViolationSysErr         = -10117
	ErrUserSwitchSysErr                    = -10118
	ErrChannelSchemeDbErr                  = -10119
	ErrChannelMicLayoutDbErr               = -10120
	ErrChannelMicLayoutMgrDbErr            = -10121
	ErrChannelCustomShortcutDbErr          = -10122
	ErrChannelMicAudioDbErr                = -10123

	//房间基础系统错误码[-10100 ~ 10199] end----------------

	// 人脸认证 [-12351, -12400] // 玩球啊  不看注释的吗?([-20000, -10000] 错误码会归入系统失败调用统计) 不能用于业务错误码
	ErrRealnameNeedFaceAuthBeforeConsume = -12351 // 消费前需要进行人脸认证
	ErrRealnameNeedRealnameBeforeConsume = -12352 // 消费前需要进行人脸认证，请先进行实名认证
	ErrRealnameFaceAuthFailBeforeConsume = -12353 // 消费前需要进行人脸认证，人脸验证失败

	// 中间件访问异常 [-12401, -12410]
	ErrMiddlewareUnknownErr      = -12401
	ErrMiddlewareLogicErr        = -12402
	ErrMiddlewareNetworkErr      = -12403
	ErrMiddlewareRequestCanceled = -12404
	ErrMiddlewareRequestTimeout  = -12405

	// *****************************不要在 -10000 到 -20000之间定义业务错误码end****************************************************

	// *****************************这个范围错误码会归入系统失败调用统计end***********************************************************

	//新群组相关 [-20001,-20100]

	// 新公告相关
	ErrGroupAnnouncementCheckingErr       = -20001 // 已有公告正在审核中,请稍后再试
	ErrGroupAnnouncementCountLimitErr     = -20002 // 公告数量已满，请先删除已有公告
	ErrGroupAnnouncementWordLimitErr      = -20003 // 公告字数已超过限制
	ErrGroupAnnouncementUpdateNotFoundErr = -20004 // 该公告已被修改或删除
	ErrGroupAnnouncementSensitive         = -20005 // 输入的内容包含敏感词

	//群认证
	ErrGroupJoinCheckErr           = -20010 // 回答错误
	ErrGroupJoinCheckJoinTypeCheck = -20011 // 群验证方式已经被修改
	ErrGroupJoinCheckSensitiveErr  = -20012 // 您的设置涉及敏感词汇，请重新设置

	// 挚友服务稀缺关系 [-22000, -22050]
	ErrFellowAddRelationshipFail           = -22000 // 添加关系配置失败
	ErrFellowUpdateRelationshipFail        = -22001 // 更新关系配置失败
	ErrFellowDeleteRelationshipFail        = -22002 // 删除关系配置失败
	ErrFellowAddChannelRelationshipFail    = -22003 // 添加房间下发关系配置失败
	ErrFellowUpdateChannelRelationshipFail = -22004 // 更新房间下发关系配置失败
	ErrFellowDeleteChannelRelationshipFail = -22005 // 删除房间下发关系配置失败
	ErrFellowGetUserNameplateFail          = -22006 // 无法获取用户的铭牌
	ErrFellowBoxRareListUkwBan             = -22007 // 对方正开启神秘人身份，暂不可查看关系

	// 角色扮演 [-22051,-22060]
	ErrRoleplayRoleNameAuditing = -22051 // 角色名称审核中
	ErrRoleplayRoleSetTooMany   = -22052 // 修改次数过多，请稍后再试

	// 广场帖子投票 [-22061,-22065]
	ErrUgcVoteCreator = -22061 // 投票发起人不可参与投票哦
	ErrUgcVoteExpired = -22062 // 投票已结束
	ErrUgcVoteVoted   = -22063 // 已经投过票啦，不能再投啦

	// pia戏 [-22066,-22080]
	ErrPiaOrderDramaListAdd          = -22066 // 点本失败
	ErrPiaOrderDramaListGet          = -22067 // 获取点本列表失败
	ErrPiaOrderDramaListDelete       = -22068 // 删除指定的点本记录失败
	ErrPiaMicRoleMapSelectRole       = -22069 // 选择角色失败
	ErrPiaMicRoleMapCancelRole       = -22070 // 取消选择角色失败
	ErrPiaChannelDramaSelect         = -22071 // 选本失败
	ErrPiaChannelDramaOperate        = -22072 // 走本操作失败
	ErrPiaChannelDramaGetStatus      = -22073 // 获取房间走本详情失败
	ErrPiaChannelBgmOperate          = -22074 // BGM操作失败
	ErrPiaCrateCopyFail              = -22075 // 创建副本失败
	ErrPiaCrateCopyFailForLimitation = -22076 // 创建副本失败，暂停过bgm的剧本不能生成副本
	ErrPiaChannelBgmVolOperate       = -22077 // BGM音量操作失败
	ErrPiaChannelChangePlayingType   = -22078 // 切换走本方式失败
	ErrPiaSystemIsNotAvailable       = -22079 // pia戏玩法维护中
	ErrPiaGetMyPlayingRecord         = -22080 // 查询我的参演记录失败

	// 房间子频道 [-22081,-22100]
	ErrChannelboxAdminPermCanceled = -22081 // 您已被取消管理员权限
	ErrChannelboxMicNotEnough      = -22082 // 可分配麦位数不足
	ErrChannelboxBoxInUse          = -22083 // 子频道使用中，请稍后操作
	ErrChannelboxBoxNotExists      = -22084 // 子频道不存在，请刷新
	ErrChannelboxMicInUse          = -22085 // 麦位使用中，请稍后创建
	ErrChannelboxBoxNameReview     = -22086 // 子频道名称审核中
	ErrChananelboxMicAllocBusy     = -22087 // 麦位分配繁忙，请重试

	// 点唱厅[-22101, -22130]
	ErrSingingHallAuditFail        = -22101 // 不符合平台审核规范,无法设置名称
	ErrSingingHallAuditPartSuccess = -22102 // 有部份歌曲上传失败，请重试

	// 进房控制[-22131, -22140]
	ErrChannelEnterControlWhiteListInterceptor = -22131 // 当前不在进房名单内，请升级版本查看

	// pgc跨房pk [-22141, -22200]
	ErrPgcChannelPkNotApplicable       = -22141 // 对方房间当前不符合pk条件
	ErrPgcChannelPkGuildidError        = -22142 // ID输入错误，请重试
	ErrPgcChannelPkLimitMultiInvite    = -22143 // 已向其他厅发起pk，请耐心等待回应
	ErrPgcChannelPkInviteHadRefuse     = -22144 // 对方已拒绝您的pk邀请。5分钟内无法再向该厅发起第二次pk
	ErrPgcChannelPkLimitPkError        = -22145 // 该厅已到达pk限额了哦，请稍后再试
	ErrPgcChannelPkLimitMultiPkFrom    = -22146 // 正在PK中
	ErrPgcChannelPkHadOtherInvite      = -22147 // 已向其他厅发起pk
	ErrPgcChannelPkOurPkSwitchClose    = -22148 // 请联系房间房主或超管开启pk权限
	ErrPgcChannelPkLimitPkWhenMaskedPk = -22149 // 蒙面pk期间，无法发起pk
	ErrPgcChannelPkInvalidParam        = -22150 // 参数有误

	// 房间公屏[-22201, -22210]
	ErrChannelImTextSensitiveWord = -22201
	ErrChannelImMsgFailPure       = -22202

	// pia戏 [-22211,-22270]
	ErrPiaBatchDeleteMyPlayingRecord = -22211 // 批量删除我的参演记录失败
	ErrPiaAddDramaFeedbackFail       = -22212 // 新增剧本反馈失败
	ErrPiaDramaCopyUnsupported       = -22213 // 副本不支持此操作
	ErrPiaBusinessFail               = -22214 // 业务处理失败
	ErrPiaMicFollowFail              = -22215 // 麦位跟随失败

	// channel-open-game [-22271,-22280]
	ErrChannelOpenGameGameIdEmpty                = -22271 // 对局信息缺失
	ErrChannelOpenGameTmpChnCannotSetMode        = -22272 // 临时房不允许修改模式
	ErrChannelOpenGameJoystickVerifyFail         = -22273 // 找不到对局位
	ErrChannelOpenGameLoadSeqMismatch            = -22274 // 无法处理:对局检查失败
	ErrChannelOpenGameNoPermission               = -22275 // 无法处理:权限检查失败
	ErrChannelOpenGameStatusNotPrepare           = -22276 // 无法处理:对局已经开始
	ErrChannelOpenGamePlayerAreadyFull           = -22277 // 无法处理:对局人数已满
	ErrChannelOpenGameUserNotJoined              = -22278 // 无法处理:用户状态不满足
	ErrChannelOpenGamePlayerNumberLmtErr         = -22279 // 无法处理:不能开始对局
	ErrChannelOpenGameControllerRaceResourceFail = -22280

	//zego 音频token [-22281, -22290]
	ErrChannelAudioTokenNoPermission = -22281 // 音频连接失败，请退房重试

	//push token [-22291, -22300]
	ErrPushTokenNoPermission = -22291 // 没有申请pushtoken权限

	// pgc-channel-game 营收房游戏玩法 [-22301, -22350]
	ErrPgcChannelGameNoStartGamePermission   = -22301 // 您暂无开启甩雷玩法的权限～
	ErrPgcChannelGameUnderversion            = -22302 // 版本过低
	ErrPgcChannelGameGameplayIncompatibility = -22303 // 当前不可开启甩雷玩法
	ErrPgcChannelGameNotEnoughPlayer         = -22304 // 至少有2名参与用户才可开启玩法哦～
	ErrPgcChannelGame_RequireOnHostMic       = -22305 // 请坐上主持麦后开启

	// 拍卖房 [-22351,-22380]
	ErrOfferingRoomAppliedListAlreadyApply = -22351 // 已经申请过了
	ErrOfferingRoomAppliedListFull         = -22352 // 申请列表已满
	ErrOfferingRoomConfigOutDate           = -22353 // 配置过期
	ErrOfferingRoomSettlePhaseTop1OutDate  = -22354 // 第一名不匹配
	ErrOfferingRoomSwitchSchemeNotAllow    = -22355 // 拍卖过程中不可切换其他房间模版
	ErrOfferingRoomHostMissing             = -22357 // 因10分钟内无人主持，本轮拍卖取消
	ErrOfferingRoomNotFound                = -22358 // 通用错误码，查询的信息不存在
	ErrOfferingRoomTShellFail              = -22359 // 自定义的关系名称机审不通过
	ErrOfferingRoomOrderSnInvalid          = -22360 // 订单SN无效
	ErrOfferingRoomOrderCommitFail         = -22361 // 订单确认失败
	ErrOfferingRoomOrderRollbackFail       = -22362 // 订单回滚失败
	ErrOfferingRoomCommonError             = -22363 // 通用错误
	ErrOfferingRoomLockError               = -22364 // 上锁失败

	// 房间玩法互斥 [-22381, -22390]
	ErrChannelGameMutualCommon     = -22381 // 通用错误
	ErrChannelGameMutualRegister   = -22382 // 注册失败
	ErrChannelGameMutualUnregister = -22383 // 注销失败

	// 房间消息推送api [-22391, -22400]
	ErrChannelMsgApiParamsInvalid   = -22391 // 推送参数无效
	ErrChannelMsgApiTooManyRecvUser = -22392 // 接收用户数量过多

	// 聊天机器人 [-22401, -22450]
	ErrChatBotAiPartnerNotFound    = -22401 // 角色不存在
	ErrChatBotAiRoleNotFound       = -22402 // 角色不存在，和其他角色聊聊吧
	ErrChatBotAiPartnerExists      = -22403 // 已拥有自己的树洞
	ErrChatBotInvalidMsgType       = -22404 // 不支持发送的消息类型
	ErrChatBotInvalidMsgContent    = -22405 // 消息中包含敏感内容
	ErrChatBotAiPartnerInSilence   = -22406 // AI伴侣沉默中
	ErrChatBotChangeRoleReachLimit = -22407 // 修改树洞形象次数达到上限
	ErrChatBotCreateAiRoleLimit    = -22408 // 创建的角色达到上限
	ErrChatBotCreateAiPartnerLimit = -22409 // 聊过的伴侣达到上限

	// 房间自定义麦位名称 [-22451, -22470]
	ErrChannelMicNameMicIdInvalid     = -22451 // 麦位ID非法
	ErrChannelMicNameUpdateDataTooOld = -22452 // 麦位名称数据版本号太老
	ErrChannelMicNameSwitchOff        = -22453 // 自定义麦位名称开关未打开，无法修改数据
	ErrChannelMicNameReqInvalid       = -22454 // 麦位名称请求参数无效
	ErrChannelMicNameGetLockFailed    = -22455 // 其他人正在修改麦位名称中，请稍后再试

	//channel-scheme(房间玩法)相关[-22471, -22490]
	ErrChannelSchemeDetailTypeNotPermitSwitch = -22471 // 该玩法类型不能互相切换

	// channel-level房间等级 [-22491, -22510]
	ErrChannelLevelReqInvalid     = -22492 // 房间等级请求参数无效
	ErrChannelLevelNotInWhiteList = -22493 // 房间不在白名单内

	// channelmic(c++)房间麦位 [-22511, -22530]
	ErrChannelMicReqInvalid            = -22511 // 房间麦位请求参数无效
	ErrChannelMicGetLockFailed         = -22512 // 房间麦位获取锁失败
	ErrChannelMicIdNotExist            = -22513 // 房间麦位不存在
	ErrChannelMicModeInvalid           = -22515 // 无效的麦位模式
	ErrChannelMicExpandNotAllowed      = -22516 // 房间麦位列表不允许扩展
	ErrChannelMicParseFromStringFailed = -22517 // 房间麦位数据解析错误
	ErrChannelMicOperatorInfoInvalid   = -22518 // 操作者当前麦位信息有误
	ErrChannelMicTargetNotAllowed      = -22519 // 目标麦位不允许操作
	ErrChannelMicUnexpectedDbResponse  = -22520 // 房间麦位数据库数据异常
	ErrChannelMicParseFieldFailed      = -22521 // 解析麦位字段错误
	ErrChannelMicMiddleSourceIllegal   = -22522 // 操作来源非法

	// channel-msg-express 房间消息推送 [-22531, -22550]
	ErrChannelMsgExpressReqInvalid               = -22531 // 请求参数无效
	ErrChannelMsgExpressPermissionDeny           = -22532 // 无发送该房间消息权限
	ErrChannelMsgExpressUnsupportedDuplicateRule = -22534 // 不支持的duplicate规则类型
	ErrChannelMsgExpressSerializationFailed      = -22535 // 数据序列化出错

	// 未成年消费 [-22551, -22570]
	ErrRealnameForbidBeforeConsume = -22551 // 账号异常，请联系客服

	// 游戏搭子 [-22571, -22620]
	ErrGamePalCardNotFound                  = -22571 // 游戏搭子卡不存在
	ErrGamePalCardCreateLimitExceed         = -22572 // 创建游戏搭子卡超过上限
	ErrGamePalCardPublishCrowdLimit         = -22573 // 发布游戏搭子卡人群限制
	ErrGamePalCardInReview                  = -22574 // 游戏搭子卡审核中
	ErrGamePalCardLightenTimeLimit          = -22575 // 游戏搭子卡擦亮过于频繁
	ErrGamePalCardLightenFreqLimit          = -22576 // 游戏搭子卡擦亮次数超出当日限制
	ErrGamePalTabNotFound                   = -22577 // 服务出现问题
	ErrGamePalCardInvalidState              = -22578 // 错误的游戏搭子卡状态
	ErrGamePalCardInvalidAuditState         = -22579 // 错误的游戏搭子卡审核状态
	ErrGamePalCardSocialDeclTooOrdinary     = -22580 // 您的宣言太普通啦，试试描述一下你想要找的ta是怎么样的吧
	ErrGamePalCardEditCardAgain             = -22581 // 请重新编辑您的搭子卡信息
	ErrGamePalSuperPublishFreqLimit         = -22582 // 不可频繁使用，晚一点再来吧
	ErrGamePalSuperPublishRcmdUserListEmpty = -22583 // 555555搭子太少了，稍后再试试吧
	ErrGamePalSuperPublishInvalidMsgContent = -22584 // 请重新选择打招呼消息
	ErrGamePalAssistantClickPush            = -22585 // 操作太频繁啦

	// 礼物周卡 [-22621, -22640]
	ErrPresentWeekCardNotFound           = -22621 // 周卡配置不存在
	ErrPresentWeekCardNotAvailable       = -22622 // 暂不可购买
	ErrPresentWeekCardDuplicateBuy       = -22623 // 请勿重复购买
	ErrPresentWeekCardSysErr             = -22624 // 系统错误
	ErrPresentWeekCardReceiveFailExpired = -22625 // 领取失败，奖励已过期

	// channel-open-game-controller [-22641,-22650]
	ErrChannelOpenGameControllerHttpBusinessFail = -22641 // 无法处理:开始对局失败
	ErrChannelOpenGameControllerInterruptReject  = -22642 // 游戏进行中不能操作哦~

	// 电竞陪玩订单域 [-22651, -22690]
	ErrEsportsOrderCommonError                 = -22651 // 订单域通用错误
	ErrEsportsRoleApplyRecordNotFound          = -22652 // 找不到申请记录
	ErrEsportsRoleApplyAlready                 = -22653 // 已提交身份申请
	ErrEsportsRoleSystemError                  = -22654 // 系统错误
	ErrEsportsRoleUserInApplyBlacklist         = -22655 // 您的账号已被冻结，请%s后再申请
	ErrEsportsOrderNotExist                    = -22656 // 订单不存在
	ErrEsportsOrderIsDeleted                   = -22657 // 订单已删除
	ErrEsportsOrderStatusUpdated               = -22658 // 订单状态已更新，请刷新后重试
	ErrEsportsOrderRefundTshell                = -22659 // 不符合平台审核规范，请重新填写
	ErrEsportsOrderCannotPay                   = -22660 // 不可下单
	ErrEsportsOrderCannotPayCoachRoleReclaimed = -22661 // 电竞指导身份被回收，无法下单
	ErrEsportsOrderPayRemarkReviewReject       = -22662 // 您填写的订单备注不符合平台审核规范，请重新修改
	ErrEsportsOneKeyFindStatusNotMatch         = -22663 // 一键找人状态不匹配(已完成、已超时、已取消)

	//电竞指导技能 [-22691, -22699]
	ErrEsportsSkillReject    = -22691 // 您填写的大神技能内容不符合平台审核规范，请重新修改
	ErrEsportsSkillNotFound  = -22692 // 您的技能不存在
	ErrEsportsSkillCommonErr = -22693 // 大神技能信息错误
	ErrEsportSkillFreeze     = -22694 // 您的技能已被冻结，请在冻结时间结束后再开启

	// 电竞大厅 [-22700, -22710]
	ErrEsportHallGameCardInfoInvalid   = -22700 // 审核不通过
	ErrEsportsHallNoReceiveTime        = -22701 // 当前不在接单时段，可私聊大神沟通时间哦～
	ErrEsportsHallInviteOrderInvalid   = -22702 // 邀请已失效
	ErrEsportsHallCantReceiveOrder     = -22703 // 您当前不可接单
	ErrEsportsHallRiskControl          = -22704 // 风控拦截
	ErrEsportsHallBlacklist            = -22705 // 黑名单拦截
	ErrEsportHallProductNotFound       = -22706 // 大神已停止接单该技能
	ErrEsportHallCantQuickReceiveOrder = -22707 // 您当前不可快速接单
	ErrEsportHallNeedSubWxgzh          = -22708 // 需要绑定微信公众号
	ErrEsportHallGuaranteeSwitchNeedOn = -22709 // 包赢开关未打开
	ErrEsportHallGameCardNotFound      = -22710 // 游戏卡片已删除

	// 电竞积分 [-22711, -22720]
	ErrEsportsScoreDuplicateOrder    = -22711 // 重复电竞积分订单
	ErrEsportsScoreInsufficientPoint = -22712 // 电竞积分不足

	// 电竞客服 [-22715, -22720]
	ErrEsportsCustomerNotFound = -22715 // 找不到客服

	// 房间音频违规 [-22721, -22750]
	ErrChannelAudioViolationJudgmentNotExist          = -22721 // 审判不存在
	ErrChannelAudioViolationJudgmentAlreadyClose      = -22722 // 审判已经关闭
	ErrChannelAudioViolationUserJudgmentTooOften      = -22723 // 对同一用户发起审判过于频繁
	ErrChannelAudioViolationChannelJudgmentTooOften   = -22724 // 同一房间内发起审判过于频繁
	ErrChannelAudioViolationJudgmentAlreadyAppeal     = -22725 // 您已提交过申诉
	ErrChannelAudioViolationAudioFileAlreadySet       = -22726 // 该音频文件已经上报过
	ErrChannelAudioViolationDetectDisable             = -22727 // 该用户或房间目前不需要进行违规检测
	ErrChannelAudioViolationUserImmediatePass         = -22728 // 该用户不需要审判，直接放过
	ErrChannelAudioViolationUserImmediatePunish       = -22729 // 该用户不需要审判，直接处罚
	ErrChannelAudioViolationJudgmentSwitchOff         = -22730 // 审判开关已关闭
	ErrChannelAudioViolationJudgmentUserAlreadyVote   = -22731 // 该用户已经投过票
	ErrChannelAudioViolationUserJudgmentCountReachMax = -22732 // 该用户进房后的审判次数到达限制
	ErrChannelAudioViolationChannelTypeNotUgc         = -22733 // 房间类型非ugc
	ErrChannelAudioViolationSampledAudioNotAllowSave  = -22734 // 该抽样音频文件不允许保存

	//公会房间管理员设置
	ErrSetGuildChannelAdminMustContract = -22751 // 不是公会的签约用户,不可升为管理员

	// 电竞大神等级[-22761, -22765]
	ErrEsportGodLevelSysErr = -22761 // 大神等级系统错误

	// 营收广告接入 [-22766, -22770]
	ErrRevenueAdSysErr = -22766 // 营收广告系统错误

	// 开黑形象 [-22771, -22780]
	ErrUserRateImageSelfRateLimitExceed    = -22771 // 今天的次数用完啦，明天再来试试吧
	ErrUserRateImageGameBindConfigNotExist = -22772 // 游戏交友战力配置出问题啦...
	ErrUserRateImageGameBindConfigChange   = -22773 // 抱歉交友战力评估系统出问题，请重测

	// 超级玩家和SVIP特权 [-22781, -22800]
	ErrSuperPlayerPrivilegeFreqErr          = -22781 // 操作频繁，请稍后重试
	ErrSuperPlayerPrivilegeNoSvip           = -22782 // 未开通SVIP
	ErrSuperPlayerPrivilegeNoImRemain       = -22783 // 无剩余搭讪次数
	ErrSuperPlayerPrivilegeStealthOverLimit = -22784 // 本周隐身次数达到上限
	ErrSuperPlayerPrivilegeStealthExpired   = -22785 // 隐身已失效
	ErrSuperPlayerPrivilegeStealthGodKing   = -22786 // 神王无法使用隐身特权

	// 电竞通用错误 [-22801, -22810]
	ErrEsportsCommonErr = -22801 // 电竞相关通用错误

	// AIGC 灵魂伴侣 [-22811, -22850]
	ErrAigcSoulmateShareKeyExpired             = -22811 // 分享已过期
	ErrAigcSoulmateShareKeyReachMaxUser        = -22812 // 使用人数已达上限
	ErrAigcSoulmateSharePermissionDenied       = -22813 // 没有访问权限
	ErrAigcSoulmateShareReachDailyLimit        = -22814 // 今日分享次数已用完
	ErrAigcSoulmateShareRoleNotFound           = -22815 // 分享角色已过期
	ErrAigcSoulmateShareKeyNotFound            = -22816 // 分享不存在
	ErrAigcSoulmateInvalidLikeState            = -22817 // 非法的点赞状态
	ErrAigcSoulmateInteractiveGameNotFound     = -22818 // 不存在的互动玩法
	ErrAigcSoulmateCreateInteractiveGameLimit  = -22819 // 创建的互动玩法达到上限
	ErrAigcSoulmateCannotExposeInteractiveGame = -22820 // 该互动玩法不推荐热门
	ErrAigcSoulmateNoSentence                  = -22822 // 句数消耗完毕啦

	// 赛事系统 [-22851, -22900]
	ErrEsportsAdminSysInternalErr                        = -22851 // 赛事管理系统内部错误
	ErrEsportsAdminTenantIdInvalid                       = -22852 // 租户ID异常
	ErrEsportsAdminTenantRoleNotExist                    = -22853 // 租户角色不存在
	ErrEsportsAdminTenantAccountBindError                = -22854 // 该租户id已绑定对应的账号
	ErrEsportsAdminTenantAccountSysAdminDelNotAllow      = -22855 // 超级管理员不能删除
	ErrEsportsAdminGameEventNotExist                     = -22856 // 赛事信息不存在
	ErrEsportsAdminGameSettleLockExist                   = -22857 // 赛事还在结算中，请稍等
	ErrEsportsAdminGameStatusErr                         = -22858 // 赛事状态错误
	ErrEsportsAdminGameRecordHaveNegative                = -22859 // 比赛击杀结果异常，请核对
	ErrEsportsAdminGameKillNumGt100                      = -22860 // 总击杀数不能大于100
	ErrEsportsAdminGameRepeatSaveGameRecord              = -22861 // 赛事战绩已录入，请勿重复操作
	ErrEsportsAdminGetSsoInfoErr                         = -22862 // 获取登录身份异常
	ErrEsportsAdminGetUserInfoErr                        = -22863 // 获取用户信息异常
	ErrEsportsAdminAccountIsNull                         = -22864 // 工号不存在
	ErrEsportsAdminSsoTokenIsNull                        = -22865 // sso token不能为空
	ErrEsportsAdminSsoTokenErr                           = -22866 // sso token失效
	ErrEsportsAdminCloudPhoneNotExist                    = -22867 // 云手机不存在
	ErrEsportsAdminCloudPhoneStatusNotAllowed            = -22868 // 云手机状态不允许该操作
	ErrEsportsAdminCloudPhoneNotAvailable                = -22869 // 无可用的云手机
	ErrEsportsAdminQiniuNotAuth                          = -22870 // 七牛云的ak与sk未授权
	ErrEsportsAdminQiniuGetUploadTokenFail               = -22871 // 获取七牛云的uploadToken失败
	ErrEsportsAdminAppidAuthFail                         = -22872 // appid鉴权失败
	ErrEsportsMatchPlatformSysInternalErr                = -22873 // 赛事平台系统内部错误
	ErrEsportsMatchPlatformSignErr                       = -22874 // 签名异常
	ErrEsportsMatchPlatformStateLockExist                = -22875 // 获取赛事状态锁失败
	ErrEsportsMatchPlatformGameEventNotExist             = -22876 // 赛事信息不存在
	ErrEsportsMatchPlatformGameEventDstStatusCanNotReach = -22877 // 无法更新赛事状态到目标状态
	ErrEsportsMatchPlatformGameAlreadyExist              = -22878 // 赛事已存在
	ErrEsportsMatchPlatformParamErr                      = -22879 // 请求参数异常
	ErrEsportsAdminTenantAccountPermissionErr            = -22880 // 账号未绑定租户权限
	ErrEsportsAdminCloudDeviceNotFound                   = -22881 // 该设备不存在
	ErrEsportsAdminCloudDeviceGetTokenErr                = -22882 // 该设备不存在

	// 摘星列车 [-22901, -22920]
	ErrStarTrainAdminConfCommonErr = -22901 // 摘星列车配置后台通用错误

	// 邀请陌生人进房 [-22921, -22940]
	ErrInviteRoomInviteLimit          = -22921 // 今天已经邀请很多人了，明天再来吧
	ErrInviteRoomLastInviteNotExpired = -22922 // 上次邀请还未过期，请稍后再试
	ErrInviteRoomUserNotInChannel     = -22923 // 只有在房间内才能发出邀请
	ErrInviteRoomInviteRecordNotExist = -22924 // 邀请记录不存在或已失效

	// IM游戏搭子预约开黑 [-22941, -22949]
	ErrGameAppointmentNotEffect = -22941 // 该邀请已失效或不存在
	ErrReachMaxOnlineSetCount   = -22942 // 提醒人数已达到上限

	// 互评
	ErrGameUserRateRisk = -22950 // 评价风控限制仅自己可见

	// 虚拟形象 virtual-avatar [-22951, -22970]
	ErrVirtualAvatarNotFound                = -22951 // 不存在的形象
	ErrVirtualAvatarNotInUse                = -22952 // 用户没有使用中的虚拟形象
	ErrVirtualAvatarExpired                 = -22953 // 虚拟形象已过期
	ErrVirtualAvatarOrderidExist            = -22954 // 虚拟形象订单已存在
	ErrVirtualAvatarHaveNotRelation         = -22955 // 该形象没有绑定的双人关系
	ErrVirtualImageOrderNotExist            = -22956 // 订单不存在
	ErrVirtualImageCommodityNotExist        = -22957 // 商品已下架，无法购买
	ErrVirtualImageTotalPriceLimit          = -22958 // 超过购买价格限制
	ErrVirtualImageCommodityConfigChange    = -22959 // 商品信息发生更新，请重新核对确认
	ErrVirtualImageCommodityDuplicateBuy    = -22960 // 无法重复购买
	ErrVirtualImageRelationErr              = -22961 // 双人关系绑定或使用错误
	ErrVirtualImageSetDisplaySwitchFail     = -22962 // 设置外显开关失败
	ErrVirtualImageRelationUseFailReflash   = -22963 // 双人关系展示失败
	ErrVirtualImageCommodityNotBuy          = -22964 // 商品不可购买
	ErrVirtualImageCommodityPackageNotExist = -22965 // 套餐已下架
	ErrVirtualImagePackageAlreadyOnShelf    = -22966 // 套餐正在上架中 deprecated

	// 挚友小屋 fellow_house [-22971, -22990]
	ErrFellowHouseBuyHouseFailConditionCheck  = -22971 // 该挚友不符合小屋购买条件，请选择其他挚友
	ErrFellowHouseOrderNotExist               = -22972 // 订单不存在
	ErrFellowHouseBuyHouseFail                = -22973 // 购买失败，请重试
	ErrFellowHouseOrderExist                  = -22974 // 订单已存在
	ErrFellowHouseCannotUseHouse              = -22975 // 无法使用小屋
	ErrFellowHouseRecoveryHouseAlreadyExpired = -22976 // 回收的小屋已过期

	// 礼物套装 present-set [-22991, -23010]
	ErrPresentSetCommonErr       = -22991 // 礼物套装系统错误
	ErrPresentSetNotFound        = -22992 // 礼物套装不存在
	ErrPresentSetEmperorNotFound = -22993 // 帝王套不存在
	ErrPresentSetEmperorNotMatch = -22994 // 套装礼物与关联帝王套礼物不匹配
	ErrPresentSetPresentNotFound = -22995 // 礼物不存在

	// 礼物对决 channel-gift-pk [-23011, -23030]
	ErrChannelGiftPkNotEntry      = -23011 // 无权限参与
	ErrChannelGiftPkCannotMatch   = -23012 // 当前非玩法开启时间哦~
	ErrChannelGiftPkOrderNotExist = -23013 // 订单不存在
	ErrChannelGiftPkCommonErr     = -23014 // 礼物对决系统错误

	// 新首充活动 new-recharge-act [-23031, -23040]
	ErrNewRechargeActNotInActivityTime = -23031 // 当前首充活动已结束~

	// 房间榜单 channel-rank-logic [-23041, -23060]
	ErrChannelMemberRankCommonErr = -23041 // 爱意榜系统错误
	ErrChannelOnlineRankCommonErr = -23042 // 在线榜系统错误

	// 虚拟形象 virtual-avatar [-23061, -23090]
	ErrVirtualImageCommonErr = -23061 // 虚拟形象通用错误

	// 会长后台 [-23091, -23110]
	ErrGuildManageCommonErr       = -23091 // 会长经营后台通用错误
	ErrGuildManageFreqErr         = -23092 // 操作频繁，请稍后重试
	ErrCommonLogicOperateTooOften = -23111 // 操作太频繁，频率限制

	// 房间第三方游戏接入 [-23121, -23130]
	ErrChannelExtGameAwardOrderExists = -23121 // 订单已存在
	ErrChannelExtGameGameAppidInvalid = -23122 // 无效的AppId
	ErrChannelExtGameJsCodeInvalid    = -23123 // 无效的JsCode
	ErrChannelExtGameGetJscodeFail    = -23124 // 获取JsCode失败
	ErrChannelExtGameJoinRoomFail     = -23125 // 加入房间失败

	// ugc-community [-23151, -23200]
	ErrUgcCommunityPostNotFound             = -23151 // 帖子不存在
	ErrUgcCommunityPostRoleCannotAssociated = -23152 // 角色不可被关联
	ErrUgcCommunityPostInvalidCensorResult  = -23153 // 错误的审核状态
	ErrUgcCommunityCommentContentRisk       = -23154 // 评论内容未通过审核，请重新编辑
	ErrUgcCommunityCommentRootDelete        = -23155 // 首评论已被删除，不可再回复
	ErrUgcCommunityCommentReplyDelete       = -23156 // 回复评论已被删除，不可再回复
	ErrUgcCommunityTopicNotFound            = -23157 // 话题不存在
	ErrUgcCommunityTopicExists              = -23158 // 话题已存在

	// channel-wedding婚礼房 [-23201,-23220]
	ErrChannelWeddingMinigameCommonErr         = -23201 // 婚礼房小游戏通用错误
	ErrChannelWeddingChairGamePlayerNotEnough  = -23202 // 玩家人数不足3人不支持开启游戏
	ErrChannelWeddingPlanCommonError           = -23203 // 婚礼策划通用错误
	ErrChannelWeddingOrderNotExist             = -23204 // 订单不存在
	ErrChannelWeddingStatusNotMatch            = -23205 // 状态不匹配,请重试
	ErrChannelWeddingReserveDuplicate          = -23206 // 重复预约
	ErrChannelWeddingReserveConflict           = -23207 // 预约时间冲突
	ErrChannelWeddingStageSwitchFail           = -23208 // 阶段切换失败
	ErrChannelWeddingHallStatusChanged         = -23209 // 预约观礼失败～
	ErrChannelWeddingConsultGnobilityIntercept = -23210 // 贵族拦截

	// aigc-group [-23251, -23300]
	ErrAigcGroupJoinedGroupReachedLimit = -23251 // 加入群聊数量达到上限
	ErrAigcGroupGroupNotFound           = -23252 // 群聊不存在
	ErrAigcGroupTemplateNotFound        = -23253 // 群聊配置不存在
	ErrAigcGroupGroupExisted            = -23254 // 群聊已存在
	ErrAigcGroupTemplateSexMismatch     = -23255 // 性别不匹配
	ErrAigcGroupMatchFailed             = -23256 // 匹配失败，再试试吧
	ErrAigcGroupMultiGroupDismiss       = -22821 // 长时间无消息，该群已解散

	// aigc-intimacy [-23301, -23350]
	ErrAigcIntimacyValueAdded = -23301 // 重复增加亲密值

	// ！！！！！！！！！！！！不要在 -10000 到 -20000之间定义业务错误码  不要在文件最后加错误码！！！！！
) // status codes

var CodeMessageMap = map[int]string{
	Success:                                "成功",
	ErrSys:                                 "系统错误",
	ErrSystemBusy:                          "系统繁忙",
	ErrRequestTimeout:                      "处理超时",
	ErrBadRequest:                          "无效的请求",
	ErrTrafficAdmin:                        "流量管制",
	ErrTrafficFrequently:                   "请求太频繁",
	ErrGrpcNotFound:                        "实例找不到",
	ErrGrpcFailedPrecondition:              "前置条件未就绪",
	ErrGrpcUnimplemented:                   "接口未实现",
	ErrGrpcInternal:                        "服务内部错误",
	ErrGrpcUnavailable:                     "服务不可用",
	ErrGrpcCancelled:                       "请求被取消",
	ErrPbParseError:                        "协议解析出错",
	ErrWasmCircuitBreak:                    "请求被熔断",
	ErrParam:                               "参数错误",
	ErrDummyErrorCode:                      "// 该错误不应该显示给用户",
	ErrPureErrorCode:                       "// 该错误码不应该显示给用户",
	ErrCancelled:                           "取消",
	ErrUnknown:                             "未知错误",
	ErrSendPresentFailToMyself:             "不可以送礼给自己喔",
	ErrNormalVerifyTooOftenErr:             "获取短信验证码太频繁",
	ErrChannelInGameNotSupportChangeMic:    "房间在游戏中，不支持上下麦",
	ErrOperateTooOften:                     "操作太频繁，频率限制",
	ErrTgameNoButtonAction:                 "不支持游戏中该按钮的点击回调",
	ErrTgameForbidImChatWithMicMate:        "该游戏不支持跟麦上成员进行im聊天",
	ErrTgameForbidSendPresentWhenGameStart: "游戏已开始，为了保证游戏公平性，暂时不能给Ta送礼哦",
	ErrTgameForbidSendMagicExpressionWhenGameStart:       "游戏已开始，为了保证游戏公平性，暂时不能发送魔法表情哦",
	ErrPlayTogetherAnotherStillExist:                     "与该用户存在另外一个 一起玩的邀请未失效",
	ErrPlayTogetherQuitChannelError:                      "退出当前房间失败",
	ErrPlayTogetherQuitChannelCancel:                     "拒绝退出当前房间",
	ErrFrescoRequestFail:                                 "//FRESCO下载图片失败",
	ErrFrescoRequestCancel:                               "//FRESCO下载图片取",
	ErrDownloadResourceFail:                              "//下载资源失败",
	ErrNoAvailableMic:                                    "//没有空麦位可以上",
	ErrNotifySendReqNotLogin:                             "//请求由于未登录被拦截",
	ErrPresentReturnParamsError:                          "//礼物请求返回参数有误",
	ErrAccountPhoneExist:                                 "该手机号码已被注册",
	ErrAccountUsernameExist:                              "该用户名已存在",
	ErrAccountNotExist:                                   "账号不存在",
	ErrAccountPasswordWrong:                              "密码或用户名错误",
	ErrAccountAliasModified:                              "账号已经修改过了",
	ErrAccountCompleted:                                  "用户资料已经完善过了",
	ErrAccountNoHeadImage:                                "用户没有上传头像",
	ErrAccountVerifyCodeWrong:                            "验证码输入有误，请重新输入",
	ErrAccountPasswordSame:                               "新密码不能与旧密码相同",
	ErrAccountPhoneFormatWrong:                           "手机号码格式不对",
	ErrAccountUsernameFormatWrong:                        "用户名不能包含特殊字符",
	ErrAccountPermissionDenied:                           "权限不足",
	ErrAccountHasNoGuild:                                 "用户未加入公会",
	ErrAccountVerifyTooFreq:                              "验证短信一段时间内只能下发一条， 请稍后再试",
	ErrAccountExist:                                      "账号已存在",
	ErrAccountMibaoNotSet:                                "用户未设置密保",
	ErrAccountMibaoAnswerErr:                             "密保答案错误",
	ErrAccountMibaoQuestionErr:                           "密保问题错误",
	ErrAccountNicknameSensitive:                          "你输入的昵称包含敏感词，修改失败",
	ErrAccountSignatureSensitive:                         "你输入的签名包含敏感词，修改失败",
	ErrAccountMibaoAlreadyInit:                           "用户已经设置过密保",
	ErrAccountResetClose:                                 "注册失败，同一个手机号只能注册一个帐号",
	ErrAccountBanned:                                     "该帐号由于安全问题已被暂时冻结，请联系客服",
	ErrAccountRegFrequenceLimit:                          "同设备不能注册多个",
	ErrAccountOpenidExist:                                "Openid已存在",
	ErrAccountUserPhoneAlreadyBinded:                     "该手机号已经绑定到你的账号，无需再次绑定",
	ErrAccountUserBindedAnotherPhone:                     "你当前已经绑定到了另一个手机号，无法绑定",
	ErrAccountPhoneBindedToAnother:                       "该手机号已被绑定过，请更新到最新版本中绑定，在【我】-【关于TT语音】-【检查更新】中更新版本",
	ErrAccountRequireNewPassword:                         "需要设置一个密码",
	ErrAccountSetTaillightMedalidInvalid:                 "你没有该勋章或已过期 不能将其设置为昵称后显示",
	ErrAccountMedalNotallowSettaillight:                  "昵称后不能显示该类型的勋章",
	ErrAccountWatcherPermissionLimit:                     "权限不足",
	ErrAccountToLoginCaptcha:                             "需要登录验证",
	ErrAccountVerifyLoginCaptcha:                         "登录验证码错误",
	ErrAccountNoNeedCaptcha:                              "不需要图片验证码",
	ErrAccountBanRegFrequenceLimit:                       "该帐号由于安全问题已被限制注册，请联系客服",
	ErrAccountBanAuthFrequenceLimit:                      "该帐号由于安全问题已被限制登录，请联系客服",
	ErrAccountInvalid:                                    "账号异常",
	ErrAccountRegIpFrequenceLimit:                        "注册帐号IP频率限制",
	ErrAccountSearchNewRegContactFrequenceLimit:          "由于安全问题已被限制操作，请联系客服",
	ErrAccountSmsThreshholdExceed:                        "发送短信过多，请明天再试",
	ErrAccountNoBindedPhoneToUser:                        "该账号未绑定任何手机号",
	ErrAccountRebindVerifiedPhoneInvalid:                 "已绑定的手机验证失败",
	ErrAccountThirdpartyDetachDisabled:                   "该功能已经下线",
	ErrAccountPhoneFormatBanned:                          "手机号码格式不对",
	ErrAccountRegLimitedByClientVersion:                  "请升级客户端至最新版",
	ErrAccountThirdpartyIdNotDetachable:                  "该账号无法进行解绑",
	ErrAccountDeviceFormatBanned:                         "设备异常",
	ErrAccountLoginNeedBindPhone:                         "该账号需要绑定手机号才能登陆，请升级客户端版本至最新",
	ErrAccountModifyPwdNeedVerifycode:                    "修改密码需要进行手机验证，请升级客户端版本至最新",
	ErrAccountDeviceUnusual:                              "您的版本过旧，为确保账号安全，需更新为最新版才可继续使用",
	ErrAccountDiffAccTryPwdTooMuch:                       "由于该设备登陆密码错误次数过多，该设备5小时内不能登陆",
	ErrAccountSameAccTryPwdTooMuch:                       "由于之前一段时间该账户密码错误次数超过5次，该账号5小时内不能登陆",
	ErrAccountPwdNullErr:                                 "未设置密码",
	ErrAccountBannedForever:                              "该帐号已被冻结",
	ErrAccountLoginNxLimit:                               "请勿尝试同时登录多设备",
	ErrAccountRegNeedBindPhone:                           "需要绑定手机号才能注册，请升级客户端版本至最新",
	ErrAccountLoginNeedBindRealnameauthPhone:             "根据国家相关政策要求,登录需要填写手机号，请升级客户端版本至最新",
	ErrAccountUnregister:                                 "用户已注销",
	ErrAccountLoginBindRealnameauthPhoneLimit:            "此手机号已认证十个账号，请使用其它手机号进行认证",
	ErrAccountBindLoginPhoneIsExpected:                   "此手机号未绑定至任何账户，请重试并绑定为登录手机",
	ErrAccountBindSecurePhoneIsExpected:                  "此手机号已经绑定至其他账户，请重试并绑定为安全手机",
	ErrAccountBindSecurePhoneLimit:                       "此手机号已被10个账号绑定，请使用其它手机号操作",
	ErrAccountRegBannedByPolicy:                          "您的手机号码或IP所属区域暂不支持注册",
	ErrAccountBannedWithInfo:                             "该帐号由于安全问题已被暂时冻结，请联系客服",
	ErrAccountBannedWithNotLoggedIn:                      "由于您过长时间未登录，该账号已被冻结封禁",
	ErrAccountSameAccountTryPwdTooMuch:                   "由于之前一段时间该账户密码或用户名错误超过10次, 该账号当天以内不能登陆",
	ErrAccountLoginFail:                                  "账号或密码错误，如忘记密码请尝试使用手机验证码登录",
	ErrAccountAbnormalFunctionInvalid:                    "您被判定为异常账号，无法使用该功能",
	ErrAccountAbnormalFunctionInvalid2:                   "该功能仅对部分用户开放，敬请谅解",
	ErrAccountThirdPartyApiFail:                          "第三方接口调用失败",
	ErrAccountRebindPhoneNotAllowInGameSdk:               "如需换绑手机号，请回到TT语音App内操作",
	ErrAccountWaitUnregister:                             "账号处于注销流程中",
	ErrAccountBlackSample:                                "您的账号存在异常，请联系客服进行处理",
	ErrAccountBannedBlack:                                "该帐号由于安全问题已被暂时冻结，请联系客服",
	ErrAccountSuspectFraud:                               "您操作的次数太多啦，请稍后再试",
	ErrAccountPcAuthNeedApply:                            "您的账号在尝试电脑登录，请在APP进行此操作",
	ErrAccountPcAuthNeedApplyExpired:                     "登录已超时",
	ErrAccountPcAuthNeedApplyReject:                      "登录验证失败，请重试",
	ErrAccountNeedStealingVerifyCode:                     "登录失败，请通过验证码登录",
	ErrAccountStealingVerifyOption:                       "登录失败，请通过验证",
	ErrAccountStealingVerifyNeedFace:                     "登录失败，需要进行人脸认证解除限制",
	ErrAccountPhoneIsRisk:                                "当前手机号存在风险，请更换再试",
	ErrAccountIdcardIsRisk:                               "当前身份证号存在风险，请更换再试",
	ErrAccountRebindPhoneFraud:                           "检测近期改绑频繁，请过几天再尝试",
	ErrAccountNotLogin:                                   "账号未登录",
	ErrSendMsgDownlAttachmentExceed:                      "附件已失效",
	ErrSendMsgNotGroupMember:                             "你不在该群，发送失败",
	ErrSendMsgParasErr:                                   "发消息参数不对",
	ErrSendMsgMute:                                       "你被禁言了，发送失败",
	ErrSendMsgAllMute:                                    "全员禁言中，只有管理员可以发言",
	ErrSendMsgGuildLimit:                                 "客官你今天公会内的自由聊天人数已经用完，想要继续搭讪请先添加玩伴吧~",
	ErrSendMsgQuitGuildFar:                               "对不起，已超出挽回聊天有效时间，发起会话失败。",
	ErrSendMsgAtEveryoneNotAllow:                         "该用户没有发送@全体消息的权限",
	ErrSendMsgAtEveryoneTimesToomuch:                     "该用户发送@全体消息的次数超过限制",
	ErrSendMsgStrangerTimesToomuch:                       "对方未回复前只能发送5条消息哦~",
	ErrSendMsgStrangerTargetsToomuch:                     "你今天发起的聊天有点多哦~先和刚认识的小伙伴聊聊吧",
	ErrSendMsgStrangerUnusualAccount:                     "你的账号异常，无法和陌生人打招呼呢~",
	ErrSendMsgStrangerIpTargetsToomuch:                   "你的IP已达到陌生人打招呼次数上限",
	ErrSendMsgCantTalkWithGmHelper:                       "你不是会长，不能和会长小秘聊啦~",
	ErrSendMsgIsBlockByTarget:                            "发送失败，对方已拒收",
	ErrSendMsgBlockTarget:                                "你已拉黑对方，对方无法接收你的信息",
	ErrSendMsgSensitive:                                  "发送失败，不符合平台管理规则",
	ErrUserAntispamAccountBannedPost:                     "您的账号目前处于禁言期，请稍后再尝试",
	ErrSendMsgStrangerTimesToomuchTimeLimit:              "发送失败，你最近发起聊天过于频繁，请稍后再试",
	ErrSendMsgStrangerTargetRevToomuch:                   "发送失败~今天有很多人找Ta聊天啦~",
	ErrSendMsgStrangerReportedToomany:                    "由于你被频繁举报，账号存在异常，60分钟内不能发起私聊，如有疑问，请联系客服",
	ErrSendMsgTooManyUsersChat:                           "发送失败，今天有很多用户找ta聊天啦",
	ErrSendMsgCanNotRcvMsg:                               "对方暂不接收私聊信息喔",
	ErrSendMsgMasterapprenticeYoualreadybound:            "你已经和对方绑定啦～",
	ErrSendMsgMasterapprenticeUpperlimit:                 "发送失败，你绑定的徒弟数已达到上限啦",
	ErrSendMsgMasterapprenticeAccountabnormal:            "对方账号异常，不能绑定为徒弟哟",
	ErrSendMsgMasterapprenticeAccountnotsatisfied:        "对方不满足收徒条件，不能绑定为徒弟，您可到<徒弟大厅>中收徒",
	ErrSendMsgMasterapprenticeTargetalreadybound:         "绑定失败，对方已被绑定其他师父啦，您可到<徒弟大厅>中收徒",
	ErrSendMsgMasterapprenticeIndev:                      "此功能维护中，敬请期待",
	ErrSendMsgMasterapprenticeAlreadyfollow:              "你已经关注Ta啦",
	ErrSendMsgMasterapprenticeEntranceclosed:             "入口已关闭",
	ErrSendMsgMasterapprenticeAlreadyinvited:             "你刚刚已邀请过Ta啦",
	ErrSendMsgMasterapprenticeAlreadyapprentice:          "已成功收Ta为徒啦，快去完成每日互动任务吧",
	ErrSendMsgMasterapprenticeOver:                       "五日师徒任务已完成，Ta已经出师啦，不能重复收徒哦",
	ErrSendMsgMasterapprenticeTerminal:                   "你们没有连续完成每日互动任务，师徒关系已解绑，不能再次收Ta为徒哦",
	ErrSendMsgMasterapprenticeNotnewdevice:               "对方不是新设备，不能绑定为徒弟哦",
	ErrUserAntispamAccountBannedPostV2:                   "您的账号目前处于禁言期，请稍后再尝试",
	ErrUserAntispamAccountTakeHoldMicBanned:              "你抱的用户由于违规被禁言",
	ErrUserAntispamAccountBannedPostV3:                   "您的账号目前处于禁言期，请稍后再尝试",
	ErrUserAntispamUnbanAllFailed:                        "一级封禁，解封失败",
	ErrUserAntispamUnbanUserFailed:                       "一级封禁，账号解封失败",
	ErrUserAntispamUnbanDeviceFailed:                     "一级封禁，设备解封失败",
	ErrFriendTargetNotFriend:                             "对方还不是你的好友",
	ErrFriendLimit:                                       "客官今天发出了很多添加玩伴的请求了，添加玩伴的体力用完了哦~",
	ErrFriendKefuLimit:                                   "客服帐号不能加好友",
	ErrFriendGonghuiKefuLimit:                            "公会客服帐号不能加好友",
	ErrFriendBaned:                                       "由于对方的设置，你不能添加对方为玩伴",
	ErrFriendTotalSizeLimit:                              "你的好友数量已达到500人上限",
	ErrFriendTargetTotalSizeLimit:                        "对方的好友数量已达到500人上限",
	ErrFriendIpLimit:                                     "客官你的IP今天发出了很多添加玩伴的请求了，添加玩伴的体力用完了哦~",
	ErrGuildNameExist:                                    "会长大人，该名字已经被抢注，赶紧换个更霸气的名字吧。",
	ErrGuildNotExist:                                     "公会不存在",
	ErrGuildShortIdSet:                                   "该公会已经设置过短号，不能重新设置",
	ErrGuildShortIdExist:                                 "该短号已被使用",
	ErrGuildUserHave:                                     "用户已加入公会了",
	ErrGuildNoPermission:                                 "无权限操作",
	ErrGuildApplyExist:                                   "用户已发起加入公会的申请",
	ErrGuildApplyHaveReviewed:                            "已被其他管理员审核通过",
	ErrGuildApplyHaveConfirmed:                           "此申请已经确认通过了",
	ErrGuildMemberNotExist:                               "非公会成员",
	ErrGuildApplyNotExist:                                "用户未发起对加入公会的申请",
	ErrGroupMemberNotExist:                               "不是群成员",
	ErrGroupMemberExist:                                  "已经是群成员",
	ErrGroupNotExist:                                     "群不存在",
	ErrGroupOwnerCannotBeRemoved:                         "群主不能退群",
	ErrGuildOwnerCannotBeRemoved:                         "会长不能退出公会",
	ErrGroupCannotJoin:                                   "此群禁止任何人加入",
	ErrGroupApplyExist:                                   "用户已发起加入群的申请",
	ErrGroupApplyNotExist:                                "用户未发起对加入群的申请",
	ErrGroupApplyHaveReviewed:                            "已被其他管理员审核通过",
	ErrGroupUserHaveBeenOwner:                            "用户已经是群主",
	ErrGuildAddGameExist:                                 "该游戏已经是群游戏了",
	ErrGroupDeleteMemberSelf:                             "踢出群用户列表中包含自己",
	ErrGuildDeleteMemberSelf:                             "踢出公会用户列表中包含自己",
	ErrGroupMuteMemberSelf:                               "禁言用户列表不能包含自己",
	ErrGroupUnmuteMemberSelf:                             "恢复发言用户列表不能包含自己",
	ErrGroupCannotExitGuildMainGroup:                     "不能退出公会总群",
	ErrGroupParamErr:                                     "参数错误",
	ErrGuildGameNotExist:                                 "非公会主玩游戏",
	ErrGroupUserNotOwner:                                 "此用户不是群主",
	ErrGuildCantDismissMemberToMuch:                      "解散公会，必须公会成员总数少于10人",
	ErrGuildAlreadyJoin:                                  "用户已经加入公会",
	ErrGuildSetGameUrlInvalid:                            "设置游戏链接，URL不正确",
	ErrGuildIdNotMatch:                                   "公会id不正确",
	ErrGuildSetGameUrlCantReach:                          "已设置成功，但您设置的链接可能无法访问，请确认哦。",
	ErrGuildSetGameUrlNotApk:                             "已设置成功，但该链接可能不是安卓游戏下载链接，请确认哦。",
	ErrGuildApplyUserJoinOtherGuild:                      "申请已失效，或用户已加入其它公会",
	ErrGuildMainGroupNoOwner:                             "总群不能设置群主",
	ErrGuildPunishPinNoPermission:                        "无权限发布图钉广播",
	ErrGuildNameSensitive:                                "你输入的公会名包含敏感词，修改失败",
	ErrGuildGroupNameSensitive:                           "你输入的群名称包含敏感词，修改失败",
	ErrGuildDescriptionSensitive:                         "你输入的群简介包含敏感词，修改失败",
	ErrGuildMemberCardSensitive:                          "你输入的群名片包含敏感词，修改失败",
	ErrGuildNameContainsGameName:                         "你输入的公会名称中包含游戏名，无法创建",
	ErrGuildNameAllDigit:                                 "无法使用纯数字作为公会名",
	ErrGuildHadOnceJoinGuild:                             "快速加入失败",
	ErrGuildAddGameExceedLimit:                           "公会游戏已达上限",
	ErrGuildNameNotMatch:                                 "公会名称不对",
	ErrGuildNameMapErr:                                   "公会名称映射错误",
	ErrGroupMemberCountLimit:                             "临时群已满员",
	ErrGuildNameInvalid:                                  "公会名称含有非法字符",
	ErrGuildUserHavebeenHighadminlevel:                   "目标用户已经被设置为更高权限",
	ErrGroupMemberCardOffline:                            "修改群名片失败",
	ErrGroupAddmemberLimit:                               "每次拉人加群不能超过50人上限",
	ErrGroupApplyExceed:                                  "该入群申请已过期",
	ErrGuildCreateFrequenceLimit:                         "当天不能创建多个公会",
	ErrGuildNotCheckin:                                   "今天未签到",
	ErrGuildCheckinSupplement:                            "没有补签天数",
	ErrGroupDismissed:                                    "该群已被解散",
	ErrGuildRefuseQuitJointimeShort:                      "加入公会满30分钟后，才可以退出公会哦~~ 在公会里多玩一会儿吧，小伙伴们舍不得你走呢",
	ErrGuildAlreadyDonate:                                "今天已捐献",
	ErrGuildExtraGameExceedLimit:                         "额外扩充游戏数量已达上限，公会等级提升后可扩充更多数量",
	ErrGuildContributionNotEnough:                        "公会可用贡献值不足",
	ErrGuildUserIsOfficial:                               "用户已经是官员",
	ErrGuildUserNotOfficial:                              "用户不是官员",
	ErrGuildOfficialNotExist:                             "职位不存在",
	ErrGuildStoragePNotShelve:                            "商品还没上架",
	ErrGuildStoragePSoldout:                              "商品已经卖完",
	ErrGuildStoragePHadModify:                            "商品价格已发生变化，请刷新再试",
	ErrGuildStoragePTotalError:                           "商品的总数与礼物列表不匹配",
	ErrGuildStorageHasNotExamineRecord:                   "找不到审查记录",
	ErrGuildStorageAddPError:                             "添加商品失败",
	ErrGuildDeductMemContributionFail:                    "扣除个人贡献失败",
	ErrGuildReturnMemContributionFail:                    "返回个人贡献失败",
	ErrGuildStorageAddGiftBoxFail:                        "放入个人宝箱失败",
	ErrGuildStoragePNotEnough:                            "商品数量不足，快去【礼包中心】补货吧~",
	ErrGuildStoragePUserCanNotGain:                       "你还没有领取资格",
	ErrGuildAlreadyCheckIn:                               "今天已签到",
	ErrGuildStoragePAllotInvalid:                         "分配操作无效",
	ErrGuildStoragePPassDu:                               "领取失败，礼包领取尚未开始或者已经过期",
	ErrGuildMemberCardGuildLimit:                         "该成员已退出公会",
	ErrGuildPrefixIsTooLong:                              "马甲长度超过限制",
	ErrGuildMemberOptCdUid:                               "用户已达到当天操作次数上限",
	ErrGuildMemberOptCdIp:                                "IP已达到本时段内操作次数上限",
	ErrGuildMemberOptCdDevice:                            "设备已达到当天操作次数上限",
	ErrGuildOfficialExist:                                "职位已经存在",
	ErrGuildSpiltGiftCardValueOverLimit:                  "切分的礼品卡单张面额超过了限制",
	ErrGuildStorageVoucherPermission:                     "此商品只有会长才能操作",
	ErrGuildStoragePNotExist:                             "商品不存在",
	ErrGuildStoragePDeleteConditionFail:                  "只能删除已过期的商品",
	ErrGuildGiftCardBalanceNotEnough:                     "礼品卡余额不足",
	ErrGuildGiftCardCantNotUnshelve:                      "本版本不支持代金券下架操作，请先检查升级",
	ErrGuildGiftCardFoundNot:                             "找不到礼品卡",
	ErrGuildGiftCardStatus:                               "礼品卡状态异常",
	ErrGuildGiftCardRemoveFail:                           "只能删除过期和状态异常的礼品卡",
	ErrGuildGiftCardRepeat:                               "该代金券每个用户只能领取一次",
	ErrGameNotExist:                                      "游戏不存在",
	ErrGuildOfficialMemberReachLimit:                     "您只能设置50个公会管理",
	ErrGuildMemberReachLimit:                             "公会成员已经达上限，无法加入",
	ErrGuildModifyPrefixLimit:                            "30分钟内只能修改一次成员马甲，请稍候再试。",
	ErrGuildSensitive:                                    "输入的内容包含敏感词，修改失败",
	ErrAuthAutoLoginAlreadyOnline:                        "该帐号已在其它设备登录，请手动登录",
	ErrUploadFaceAccountTypeErr:                          "上传头像，帐号类型错误",
	ErrAuthAutoLoginFailed:                               "自动登录失败，请手动登录",
	ErrThirdPartyAccessTokenErr:                          "第三方账号token验证失败",
	ErrImageInvalidErr:                                   "图片涉嫌违规",
	ErrChinaMobileAccessTokenErr:                         "中国移动一键登录token验证失败",
	ErrChinaMobileNotfindPhone:                           "中国移动一键登录没有匹配到手机号",
	ErrChinaUnicomAccessTokenErr:                         "中国联通一键登录token验证失败",
	ErrChinaUnicomNotfindPhone:                           "中国联通一键登录没有匹配到手机号",
	ErrChuangLanAccessTokenErr:                           "创蓝token验证失败",
	ErrChuangLanNotfindPhone:                             "创蓝没有匹配到手机号",
	ErrAppleAuthCodeFail:                                 "AppleID验证失败",
	ErrAlbumNotExist:                                     "相册不存在",
	ErrPhotoNotExist:                                     "相片不存在",
	ErrAlbumDefaultAlbumCantDelete:                       "默认相册不允许删除",
	ErrNoPermissionDeleteAlbum:                           "你没有权限删除该相册",
	ErrNoPermissionDeletePhoto:                           "你没有权限删除该照片",
	ErrAlbumNoPermission:                                 "没权限修改相册",
	ErrHeadDynamicImageNoPermission:                      "没有动态头像权限",
	ErrGiftpkgGuildPkgNotEnough:                          "公会礼包不足",
	ErrGiftpkgPkgNotFound:                                "找不到这种礼包",
	ErrGiftpkgAlreadyApplying:                            "这种礼包公会已经在申请中",
	ErrGiftpkgApplyidDoneOrNotExist:                      "该申请已经处理过，或者不存在",
	ErrGiftpkgApplyidNotExist:                            "该申请不存在",
	ErrGiftpkgApplyidAlreadyDone:                         "该申请已处理过",
	ErrGiftpkgPkgNotEnough:                               "后台礼包不足",
	ErrGiftpkgApplyNoPermission:                          "无权限申请礼包",
	ErrGiftpkgUserHadFetedThisPkg:                        "已经领取过这种礼包",
	ErrGiftpkgTaohaoNoUsableId:                           "淘号，已经没有可用的序列号了",
	ErrGiftpkgRedPkgEmpty:                                "红包已经被领完了",
	ErrGiftpkgStorageSerialNotEnough:                     "仓库序列号不足",
	ErrGiftpkgApplyHasNoThisGame:                         "公会没有这款游戏，不能申请该礼包",
	ErrGiftpkgNotFoundRedPkg:                             "找不到该红包",
	ErrGiftpkgRedPkgTargetErr:                            "红包只能发到公会群或者公会好友",
	ErrGiftpkgRedPkgGuildErr:                             "你不是该公会的成员，不能领取该公会的红包",
	ErrGiftpkgAlreadyFetchRedpkg:                         "已经领取过这个红包",
	ErrGiftpkgNotGuildOwner:                              "用户不是会长",
	ErrGiftpkgPkgNotUsed:                                 "该礼包已经下架",
	ErrGiftpkgApplyMemberNotEnough:                       "该款礼包需要公会人数达到5人以上才能申请，你的公会人数不够，赶紧邀请更多的小伙伴加入你的公会吧!",
	ErrGiftpkgUserHadFetchedToday:                        "你今天领礼包次数已达到上限，请明天签到后再来吧",
	ErrGiftpkgUserNotCheckinToday:                        "公会签到后才可以领礼包哦",
	ErrGiftpkgDeviceHadFetchedToday:                      "你今天领礼包次数已达到上限，请明天签到后再来吧",
	ErrGiftpkgApplyCdNotReady:                            "申请过于频繁，请稍后再试",
	ErrGiftpkgExceedFetchFailed:                          "领取失败，该礼包已过期",
	ErrGiftpkgPriceUpdated:                               "礼包价格已更新",
	ErrVerifycodeForSensitiveOp:                          "敏感操作需要输入短信验证码，请更新至最新版本进行操作",
	ErrNoPhoneBind:                                       "该操作需要绑定手机",
	ErrVerifycodeSessionChanged:                          "登录信息变化,请重新获取验证码",
	ErrVerifycodeNoCode:                                  "验证码未生成,请重新获取验证码",
	ErrVerifycodeWrongCode:                               "验证码不正确,请重新输入",
	ErrGiftpkgDevHadFetedThisPkg:                         "设备已经领取过这种礼包",
	ErrVerifycodeParamErr:                                "验证码操作的请求参数有误",
	ErrNeedPhoneBind:                                     "该操作需要绑定手机,请到【我】-【隐私与安全】中绑定手机或者升级客户端",
	ErrVerifycodeWrongCodeToomuch:                        "验证码错误过多, 请重新获取验证码",
	ErrVerifycodeExceedLimit:                             "今日接收验证码次数已达上限",
	ErrCircleNotFound:                                    "游戏圈不存在",
	ErrCircleJoinAlreadyJoined:                           "已经加入了游戏圈",
	ErrCircleTopicDeleted:                                "该主题已被删除",
	ErrCircleTopicNotExists:                              "该主题不存在",
	ErrCircleCommentDeleted:                              "该评论已被删除",
	ErrCircleCommentNotExists:                            "该评论不存在",
	ErrCircleLikeAlreadyLiked:                            "你已经点过赞了",
	ErrCircleLikeNotExists:                               "还未赞过",
	ErrCircleNotCreator:                                  "该操作只允许作者进行",
	ErrCircleTopicTitleSensitive:                         "你输入的标题包含敏感词，发布失败",
	ErrCircleTopicContentSensitive:                       "你输入的内容包含敏感词，发布失败",
	ErrCircleCommentSensitive:                            "你输入的内容包含敏感词，发布失败",
	ErrCircleNoPermissionMute:                            "无权限禁言",
	ErrCirclePostTopicCoolingdown:                        "你的操作过于频繁, 请稍后再试",
	ErrCirclePostCommentCoolingdown:                      "你的操作过于频繁, 请稍后再试",
	ErrCirclePostSimilarTopic:                            "你的操作过于频繁, 请稍后再试",
	ErrCircleHaveNotJoinCircle:                           "未加入圈子",
	ErrCircleCanNotQuitActivity:                          "无法退出活动圈",
	ErrCircleNoPermissionHightlight:                      "无权限加精",
	ErrCircleNoPermissionCancleHightlight:                "无权限删精",
	ErrCircleNoPermissionDelTopicComment:                 "无权限删评论",
	ErrCircleUserMuteComment:                             "你已被禁言，暂时无法发送评论",
	ErrCircleUserMuteTopic:                               "你已被禁言，暂时无法发送主题",
	ErrCircleContentLenOverlimit:                         "你发的内容太多了，伦家受不了呐",
	ErrCircleTitleLenOverlimit:                           "你标题都这么长，吓死我了，嘤嘤嘤~~~",
	ErrCircleAnnouncementSendTopic:                       "公告圈禁止发主题",
	ErrCircleAnnouncementQuit:                            "禁止退出公告圈",
	ErrCircleTopicPictureSensitive:                       "主题图片包含敏感信息",
	ErrCircleAnnouncementJoin:                            "禁止关注公告圈",
	ErrCircleAnnouncementVersion:                         "当前版本不支持该操作，请更新版本",
	ErrCircleTopicNotDeleted:                             "该主题未被删除",
	ErrCircleIsNowReadonly:                               "T^T游戏圈帖子只支持查看，不支持发帖、点赞、评论等操作哟",
	ErrRecruitNotExist:                                   "招募不存在或者已经结束",
	ErrRecruitPostContentSensitive:                       "招募内容包含敏感词汇",
	ErrRecruitPostReddiamonNotEnough:                     "红钻不够 不能招募",
	ErrRecruitPostGuildAlreadyPost:                       "该公会在这个游戏发过招募还没有结束",
	ErrRecruitGuildNotUsercurrent:                        "您已经不在创建该招募的公会了",
	ErrRecruitSupportAlreadyDo:                           "每天只能为自己公会顶一次",
	ErrRecruitOpNoPermission:                             "没有操作权限",
	ErrRecruitPostGuildcontrbutionNotEnough:              "贡献值不够 不能招募",
	ErrGrowUserMissionCollected:                          "奖励已领取",
	ErrGrowCurrencyAdded:                                 "用户红钻已经添加，不能重复添加",
	ErrGrowCurrencyNotEnough:                             "你的红钻数量不足，赶紧去做任务领红钻吧～",
	ErrGrowMissionAccepted:                               "任务已接受",
	ErrGrowMissionUnacceptable:                           "任务不可接受",
	ErrReddiamondNotEnoughInStock:                        "红钻池红钻不足",
	ErrTimeLimitMissionConfigNotExist:                    "限时任务不存在",
	ErrTimeLimitMissionEventNotExist:                     "限时任务类型不存在",
	ErrTimeLimitMissionConfigParam:                       "限时任务配置参数异常",
	ErrTimeLimitMissionAcceptLimit:                       "该任务的领取名额已满",
	ErrTimeLimitUserMissionNotExist:                      "用户限时任务不存在",
	ErrTimeLimitUserMissionAccepted:                      "用户限时任务已接受",
	ErrMedalUserMedalOrderExists:                         "发勋章订单重复",
	ErrPushMessageNotExists:                              "推送信息不存在",
	ErrPushMessageReviewed:                               "消息之前已经审核过了",
	ErrPushMessageRefused:                                "消息已经被拒绝",
	ErrPushMessageDeleted:                                "消息已被删除",
	ErrPushParamError:                                    "推送参数错误",
	ErrPushWithoutBroadcast:                              "无推送筛选条件，如需广播请指定广播模式",
	ErrPublicCanNotUnsubSystemAccount:                    "无法取消关注该公众号",
	ErrSessionRoomNotExists:                              "房间不存在",
	ErrSessionRoomUserInOtherRoom:                        "用户已经在其它房间",
	ErrSessionRoomUserNotInRoom:                          "用户不在此房间内",
	ErrSessionRoomUserNotInAnyRoom:                       "用户不在任何房间",
	ErrSession1v1NotFriend:                               "和对方不是好友",
	ErrSessionCallinWaitCoolDown:                         "召集令冷却中",
	ErrSessionGroupNoCallin:                              "这个群当前没有召集令",
	ErrSessionAlreadyAcceptCallin:                        "用户已经接受了召集了",
	ErrSessionCallinNotEnd:                               "召集令尚未结束",
	ErrSessionCallinTooFrequency:                         "发起召集令过于频繁",
	ErrSessionRoomUserExceed:                             "当前开黑人数已满",
	ErrTokenBadToken:                                     "token无效或者已经过期, 请重新登录",
	ErrTokenIsRequired:                                   "token无效, 请重新登录",
	ErrTokenWasExpired:                                   "token已过期, 请重新登录",
	ErrTokenInvalidRefreshToken:                          "无效的refresh token",
	ErrTgroupCreateLimitExceed:                           "用户创建的兴趣群数量已经超过上限",
	ErrTgroupInvalidTgroupAccount:                        "无效的群帐号",
	ErrTgroupNoPermission:                                "无权限操作",
	ErrTgroupOwnerCanntQuit:                              "群主不允许退群",
	ErrTgroupCreateNeedInviteFriend:                      "需要邀请一个好友才能创建群组",
	ErrTgroupTargetInvalid:                               "操作对象错误",
	ErrTgroupDisplayIdNotExist:                           "群不存在",
	ErrTgroupJoinGroupLimit:                              "用户加入的兴趣群过多",
	ErrTgroupInvalidInvite:                               "非法的邀请",
	ErrTgroupSensitive:                                   "输入的内容包含敏感词，修改失败",
	ErrSessionNotExist:                                   "会话不存在",
	ErrSessionExpired:                                    "会话过期",
	ErrPresInvalidProxyAddress:                           "错误的proxy地址",
	ErrWebsessionServerError:                             "系统错误",
	ErrWebsessionInvalidCode:                             "Invalid auth code",
	ErrWebsessionInvalidToken:                            "Invalid token",
	ErrWebsessionInvalidScope:                            "Invalid scope",
	ErrMallNoProduct:                                     "找不到商品",
	ErrMallItemTypeMismatch:                              "物品类型不匹配",
	ErrMallInvalidCategory:                               "无效的Category",
	ErrMallInvalidItemType:                               "无效的物品类型",
	ErrMallNoEnoughStorage:                               "库存不足",
	ErrMallActivityTimeOverlappedWithAnother:             "活动与另一个活动冲突",
	ErrMallNoSuchActivity:                                "找不到相应的活动",
	ErrMallActivityCanNotBeUpdated:                       "活动当前无法被修改",
	ErrMallInvalidConfig:                                 "活动配置有问题",
	ErrMallCurrentActivityNone:                           "当前没有活动",
	ErrMallNotCurrentActivity:                            "非当前活动",
	ErrMallActivityNotOpen:                               "活动还未开始",
	ErrMallActivityClosed:                                "活动已经结束",
	ErrMallNoSuchProductItem:                             "找不到相应的物品",
	ErrMallCurrentActivityInProgress:                     "当前有活动在进行",
	ErrMallFailedToParseProductItem:                      "无法解析物品数据",
	ErrMallUserHadMedal:                                  "你已经拥有该勋章",
	ErrMallUserHaveAlreadyPurchasedProduct:               "你已经在本次活动中购买过该商品了",
	ErrChannelNameLenError:                               "房间名称长度不正确",
	ErrChannelBindtypeInvalid:                            "创建的房间类型不匹配",
	ErrChannelNotExist:                                   "房间已经被删除",
	ErrChannelMicrophoneOverlimit:                        "上麦人数达到上限",
	ErrChannelNameSensitive:                              "不能使用敏感词作为房间名",
	ErrChannelNameAllDigit:                               "无法使用纯数字作为房间名",
	ErrChannelBindGuildNotMember:                         "用户不是该房间所属公会成员",
	ErrChannelNoPermission:                               "权限不足",
	ErrChannelMemberOverlimit:                            "房间内成员数目达到上限",
	ErrChannelGuildChannelSizeOverlimit:                  "公会内开黑房间总数达到上限",
	ErrChannelUserAlreadySetMuted:                        "用户已经被禁言",
	ErrChannelUserAlreadyHoldMicrophone:                  "用户已经在麦上",
	ErrChannelUserChannelMuted:                           "你已经被禁言",
	ErrChannelUserNotJoinAnyChannel:                      "用户没有加入任何房间",
	ErrChannelUserNotJoinThisChannel:                     "用户不是该房间成员,请尝试退出房间后重新进入",
	ErrChannelMicrophoneEntryAlreadyDisable:              "麦位已经被关闭",
	ErrChannelMicrophoneEntryAlreadyEnabled:              "麦位已经被启用",
	ErrChannelInLockScreenStat:                           "房间公屏处于锁定状态 不允许发公屏消息",
	ErrChannelUserNotHoldMicrophone:                      "用户没有上麦",
	ErrChannelMicmodeNotsupportOper:                      "房间当前模式不支持该操作",
	ErrChannelTopicinfoSensitive:                         "房间话题包含敏感词",
	ErrChannelMicrophoneDisable:                          "麦位已经被管理员禁用",
	ErrChannelUserMicrophoneKickPunishTime:               "你被管理员设为旁听 暂时无法上麦",
	ErrChannelUserChannelKickPunishTime:                  "你被管理员踢出房间 暂时无法进入",
	ErrChannelMuteTargetHaveduty:                         "用户是公会管理员 不能被禁言",
	ErrChannelMicrophoneMute:                             "你已经被禁言 暂时无法上麦",
	ErrChannelUnmuteTargetHaveduty:                       "普通管理员没有权限对其他管理员解除禁言",
	ErrChannelPwdWrong:                                   "该房间已经上锁，当前版本不支持房间密码输入，请更新APP",
	ErrChannelMicModeNotSupport:                          "该房间处于当前版本不支持的模式下,请将应用升级到最新版本",
	ErrChannelMusicNotHoldMic:                            "上麦后才能播放背景音乐哦",
	ErrChannelSearchTimeOverlimit:                        "哎呀您慢点啊~~ 手速这么快~~伦家受不了的哟",
	ErrChannelMicModeAlreadySet:                          "您已经处于该模式下了哦",
	ErrChannelBindtypeClientNotSupport:                   "您当前版本不支持进入该类型房间,请升级到最新版本",
	ErrChannelOperPermissonUnderTarget:                   "这里的人等级都好高～伦家打不过啦",
	ErrChannelMusicKuwoApiErr:                            "战歌服务正在努力修复中",
	ErrChannelPwdInvalid:                                 "输入的房间密码无效",
	ErrChannelMsgAttachmentExpire:                        "数据已经过期",
	ErrChannelMsgAttachmentFormatInvalid:                 "房间不支持发送该格式数据或者数据大小超过限制",
	ErrChannelConveneCdLmt:                               "房间召集CD中",
	ErrChannelConvening:                                  "房间正在召集",
	ErrChannelNotConvening:                               "当前房间没有召集哦",
	ErrChannelMemberConfirmStatus:                        "响应召集失败",
	ErrNoUserChannelConvene:                              "没有在召集的房间哦",
	ErrChannelMicrSpaceIdInvalid:                         "麦位信息有误",
	ErrChannelMicrSpaceInfoEmpty:                         "找不到麦位数据",
	ErrChannelMicrSpaceNotallowHold:                      "该麦位被关闭或麦上有人",
	ErrChannelMicModeChangeFromFun:                       "娱乐房人数超过500人时，不能切换为开黑房哦",
	ErrChannelPersonalAdminCntLimit:                      "管理员数量已经达到上限",
	ErrChannelTagIdIsReserved:                            "该标签指暂不允许设置",
	ErrChannelCollectNumOverLimit:                        "你收藏的房间太多了",
	ErrChannelTrafficAdmin:                               "房间当前人数过多，请稍后再试",
	ErrChannelSetBackgroundFail:                          "房间背景设置失败",
	ErrChannelSetBackgroundBgExpire:                      "背景已失效",
	ErrChannelSetBackgroundBgSubtypeLimit:                "专属背景不能在此玩法使用哦",
	ErrChannelCollectGuildOwner:                          "你是会长，不能取消收藏哦",
	ErrChannelCollectManager:                             "你是房间管理，不能取消收藏哦",
	ErrChannelMusicListEmpty:                             "歌单为空",
	ErrChannelMusicCountLimit:                            "已经达到歌单上限",
	ErrChannelMusicCannotShare:                           "房间当前禁止分享音乐",
	ErrChannelMusicHoldNomic:                             "分享失败,你当前不在麦上",
	ErrChannelMusicMusicNotExist:                         "歌曲不存在,可能已经被删除",
	ErrChannelRemoveCollectFailed:                        "取消收藏失败",
	ErrChannelFungameAlreadyStart:                        "房间已经在进行小游戏",
	ErrChannelInFunMicMode:                               "房间处于娱乐模式,请升级到最新版本才可以操作",
	ErrChannelMicrophoneEntryAlreadyMute:                 "麦位已经被闭麦",
	ErrChannelInvalidTagId:                               "不存在此标签",
	ErrChannelRefreshCd:                                  "刷新太快，请稍后",
	ErrChannelTmpAllocPoolEmpty:                          "临时房间不足",
	ErrChannelTmpAllocTypeInvalid:                        "不能分配该类型的临时房间",
	ErrChannelTmpAllocNotExist:                           "临时房间不存在或者已经解散",
	ErrChannelMicModeVersionNotSupport:                   "房间处于新版布局中,请升级到最新版本才可以操作",
	ErrChannelSwitchAttachmentMsg:                        "该房间暂时不允许发图片哦",
	ErrChannelSwitchLevelLmt:                             "该房间暂时不允许新人发言哦",
	ErrChannelSwitchLiveConnectMicLmt:                    "当前主播没有开启连麦哦",
	ErrChannelLiveConnectMicApplyCountLmt:                "当前申请连麦人数已经达到上限，不能继续申请哦",
	ErrChannelLiveConnectMicCountLmt:                     "当前连麦人数已经达到上限，不能继续连麦哦",
	ErrChannelLiveNotStarting:                            "别急嘛,主播还没有开播哦",
	ErrChannelHcNoHoldmic:                                "HC频道不支持上麦操作",
	ErrChannelInDatinggameMicMode:                        "房间处于相亲模式,请升级到最新版本才可以操作",
	ErrChannelIsRecommendOpLimit:                         "房间被加入推荐库中，不能执行该操作",
	ErrChannelOpCdOverlimit:                              "哎呀您慢点啊~~ 手速这么快~~伦家受不了的哟",
	ErrChannelCollectCd:                                  "操作太快啦，一会儿再来试试",
	ErrChannelQueueUpMicSwitchLmt:                        "房间排麦功能没有开启哦",
	ErrChannelQueueUpMicApplyCountLmt:                    "当前申请排麦的人数已经达到上限，不能继续申请哦",
	ErrChannelQueueUpMicApplyAlready:                     "你已经申请过排麦了，请等待主持人的处理",
	ErrChannelLockedConveneUnavailable:                   "锁房状态下不可召集",
	ErrChannelMiniGameUnavailable:                        "当前版本不支持小游戏玩法，请更新到最新版本体验酷炫好玩的房间小游戏",
	ErrChannelSwitchMiniGameUnavailable:                  "切换小游戏失败，请更新至最新版本体验酷炫好玩的房间小游戏哟",
	ErrChannelSwitchWerewolvesGameUnavailable:            "切换失败，请更新至最新版本体验酷炫好玩的狼人杀小游戏哟",
	ErrChannelPublicWerewolvesGameUnavailable:            "发布失败，请更新至最新版本体验酷炫好玩的狼人杀小游戏哟",
	ErrChannelHourRankUnavaiable:                         "蒙面PK赛时段暂不开启小时榜展示，小时榜奖励正常发放",
	ErrChannelMicModeLockedInPk:                          "参加蒙面PK赛期间无法切换麦位模式",
	ErrChannelIsCollected:                                "已收藏该房间",
	ErrChannelModifyNameTextRejectRisk:                   "对不起，您的房间名称未通过审核",
	ErrChannelModifyNameSpecialSchemeWordsLimit:          "房间名称字数限制",
	ErrChannelTypeNotSupport:                             "不支持该类型的房间",
	ErrChannelMusicOpLimit:                               "操作太快啦，一会儿再来试试",
	ErrChannelMusicListTypeNotSupport:                    "该歌曲来源于当前版本不支持的模式，请升级版本后再进行操作",
	ErrChannelOpenGamePlayingLimit:                       "游戏过程中无法踢出用户/切换房间类型",
	ErrActivityNotEnoughPrizeTale:                        "没有足够的奖励可领取",
	ErrRedpacketStagetimeOverlay:                         "修改的阶段时间和已有配置重合",
	ErrRedpacketStagetimeTooLate:                         "修改的阶段时间必须在当前时间前2小时",
	ErrCeremony2018StageError:                            "年度盛典当前阶段不能执行该操作",
	ErrCeremony2018SignupSexErr:                          "您的性别不符合报名要求，请修改",
	ErrCeremony2018SignupAlready:                         "您已经报过名了 不能重复报名",
	ErrCeremony2018JoinTeamAlready:                       "您已经加入了一个多人队伍",
	ErrCeremony2018NotTeamMember:                         "您不是该队伍成员",
	ErrCeremony2018NotTeamCreater:                        "您不是该队伍的创建者，没有权限",
	ErrCeremony2018TeamNotExist:                          "该队伍不存在或者已经解散",
	ErrCeremony2018TeamMemberSizeOverlimit:               "该队伍已经满员,无法加入",
	ErrNewyearBeat2019GameNotStart:                       "游戏尚未开始",
	ErrNewyearBeat2019GameAlreadyPlay:                    "本轮游戏您已经参与过了",
	ErrNewyearBeat2019GameAlreadyEnd:                     "本轮游戏已结束",
	ErrNewyearBeat2019LotteryError:                       "游戏抽奖错误",
	ErrCommissionMoneyNotEnough100:                       "金额小于100，不可提现",
	ErrCommissionAccountIsFrost:                          "你的账户已被冻结，不可提现。如有问题，请联系客服",
	ErrCommissionGuildLvTooLow:                           "公会星级达到Lv.2以上才能提现",
	ErrCommissionGetMoneyTimeErr:                         "每月的4日~6日才可以申请提现",
	ErrCommissionUserInfoIdCardErr:                       "身份证填写格式有误, 请核对后重试",
	ErrCommissionUserInfoBankIdErr:                       "开户行填写格式有误，请核对后重试",
	ErrCommissionUserInfoPhoneErr:                        "手机号码填写格式有误, 请核对后重试",
	ErrCommissionUserInfoBankCardErr:                     "银行卡号填写格式有误，请核对后重试",
	ErrCommissionMoneyNotEnough:                          "提现金额超过余额，请重新申请",
	ErrCommissionApiTimeout:                              "佣金系统接口超时",
	ErrCommissionApiRespError:                            "佣金系统操作不成功",
	ErrCommissionUserBankInfoError:                       "上次提现银行卡账户信息不正确，请重新设置银行卡",
	ErrNumericsUnknownRankType:                           "未知的排行榜类型",
	ErrNumericsPresentRecordNotsupportType:               "财富魅力值接口不支持该类型礼物",
	ErrNumericsUserNoContract:                            "用户未签约",
	ErrNumericsUserNotFound:                              "用戶不存在",
	ErrNumericsDb:                                        "数据库错误",
	ErrNumericsRepeatOp:                                  "重复操作",
	ErrNumericsNotEnough:                                 "该账号财富值不足",
	ErrNumericsRichCardLock:                              "你已锁定财富值，无法使用财富卡",
	ErrNumericsCommonErr:                                 "财富值业务错误",
	ErrNumericsVipPackGetErr:                             "暂无法领取礼包，请联系官方客服",
	ErrNumericsVipPackNoCondition:                        "未达到领取条件，暂无法领取礼包",
	ErrNumericsVipPackPackErr:                            "包裹错误，暂无法领取礼包",
	ErrNumericsVipPackGiftErr:                            "礼物已下架，请选择其他礼物",
	ErrNumericsVipPackFrequencyErr:                       "领取频繁，请稍后再试",
	ErrNumericsVipPackTriggerRisk:                        "暂无法领取礼包，请联系官方客服",
	ErrLbsJsonParseErr:                                   "json解析失败",
	ErrLbsSsdbOperErr:                                    "ssdb操作失败",
	ErrLbsIpNotFound:                                     "IP地址无效",
	ErrLbsLocationNotFound:                               "经纬度无效",
	ErrLbsLocationOutOfDate:                              "经纬度数据过期",
	ErrGuildJoinTimeLimit:                                "加入公会时间不足",
	ErrTeamSvrUserAlreadyHaveTeam:                        "用户已有战队",
	ErrTeamSvrTeamNotExist:                               "战队不存在",
	ErrTeamSvrUserNoVoteChance:                           "每天只能给出一个支持哦~",
	ErrTeamSvrApproved:                                   "战队已批准，禁止再修改",
	ErrLeagueSvrUploadImg:                                "当前阶段不可上传截图",
	ErrGamePreorderNotExist:                              "预约不存在",
	ErrUserReportPhonelistInvalid:                        "上报手机列表出错",
	ErrCityRecommendAddrNotExist:                         "用户地址不存在",
	ErrSecurityAlreadyBindPhone:                          "你已经绑定了手机",
	ErrSecurityAccountBlack:                              "该帐号禁止操作",
	ErrGamelottoSvrNoEnoughChance:                        "抱歉 剩余抽奖次数为0",
	ErrGamelottoSvrReachLimit:                            "已达到当日抽奖次数限制",
	ErrGamelottoSvrNeedReddiamond:                        "继续抽奖需要消耗红钻",
	ErrGamelottoNoEnoughReddiamond:                       "红钻不足",
	ErrGamelottoPoolEmpty:                                "库存已空",
	ErrGamelottoConsumeReddiamondFailed:                  "红钻扣除失败",
	ErrGamelottFirstLottoHasDone:                         "首抽已经领取",
	ErrGamelottoLoginToGetFreeChance:                     "红钻抽奖次数已到达上线,登录获取免费抽奖次数",
	ErrGamelottoNoSuchOrder:                              "无效的订单号",
	ErrGamelottoFragmentNotEnough:                        "碎片不足",
	ErrGuildmemberlvInvalidDonateOption:                  "捐献值错误",
	ErrGuildmemberlvContributionLack:                     "贡献值不足",
	ErrGuildStorgeProductOverLimit:                       "公会商品数量超过限制",
	ErrGuildStorgeProductItemOverLimit:                   "商品兑换码超过限制svn",
	ErrGuildcircleTopTopicCountLimitExceeded:             "置顶帖数量超过限制",
	ErrGuildcircleTitleIsEmpty:                           "标题不能为空",
	ErrTtGiftCenterNotRankSubType:                        "改子礼包无法排序",
	ErrTtGiftCenterNotEnoughStorage:                      "库存不足",
	ErrTtGiftNotExistGift:                                "礼包不存在",
	ErrTtGiftNotEnoughCurrency:                           "货币不足",
	ErrTtGiftSpendCurrencyFailed:                         "扣费失败",
	ErrTtGiftNotAllowToPick:                              "未开放淘号",
	ErrTtGiftNotthingCanClaim:                            "没有可领取的礼包",
	ErrTtGiftLimitRegisterAt:                             "礼包限定了可领取用户的注册时间",
	ErrFindFriendMatchingNull:                            "没有匹配的用户",
	ErrFindFriendExamIdNotExist:                          "该测试项不存在~",
	ErrFindFriendMatchCntOverLimit:                       "今天召唤了很多个小可爱啦，明天再试试吧",
	ErrUserPresentConfigNotExist:                         "礼物不存在",
	ErrUserPresentLmtEffectedBegin:                       "礼物未上架",
	ErrUserPresentLmtEffectedEnd:                         "礼物已下架",
	ErrUserPresentConfigOverdue:                          "礼物配置已过期",
	ErrUserPresentConfigParam:                            "礼物配置参数异常",
	ErrUserPresentBuyFailed:                              "购买礼物失败",
	ErrUserPresentInvalidItemCount:                       "礼物数量异常",
	ErrUserPresentOrderNotExist:                          "礼物订单不存在",
	ErrUserPresentTagChannelDating:                       "此房间不可送出相亲房礼物",
	ErrUserPresentInvalidTargetUserSize:                  "送礼失败",
	ErrUserPresentUnableSendUserPresent:                  "无法给该用户送礼",
	ErrUserPresentBackpackRiskControl:                    "礼物赠送失败，不符合平台规则，有疑问请联系客服80040或80513",
	ErrUserPresentChannelGameOnly:                        "送礼失败！小互动礼物只能在游戏中送出哦",
	ErrUserPresentFlowConfigNotExist:                     "礼物流光配置不存在",
	ErrUserPresentSpendOverLimit:                         "单笔赠送金额不能超过2万元",
	ErrUserPresentBatchNotSupport:                        "该礼物不支持全麦赠送",
	ErrUserPresentBoxInvalid:                             "开盒参数错误",
	ErrUserPresentServiceInfoInvalid:                     "参数错误",
	ErrScenePresentOrderExist:                            "送礼场景的礼物订单号已存在",
	ErrUserScoreConfigNotExist:                           "积分配置不存在",
	ErrUserScoreNotEnough:                                "积分不足",
	ErrUserScoreOrderExist:                               "积分订单号已存在",
	ErrUserScoreChangeReasonInvalid:                      "非法积分变化原因",
	ErrUserScoreOrderRollback:                            "积分已回滚",
	ErrUserScoreInvalidDealtoken:                         "校验不通过",
	ErrUserPresentAccountRiskControl:                     "账号异常，请稍后再试~",
	ErrUserPresentInvalidTargetUserTicket:                "只能对麦上的签约用户使用喔",
	ErrUserPresentInvalidChannelTicket:                   "免费体验券只能在对应房间使用喔，快去使用吧~",
	ErrUserPresentChannelTicketMainMic:                   "券不能送给主持喔，送给其他麦位嘉宾吧~",
	ErrAntiDbErr:                                         "访问DB错误",
	ErrAntiNotExist:                                      "记录不存在",
	ErrAntiRedisErr:                                      "访问Redis错误",
	ErrAntiCmdOpBanForbid:                                "当前处于系统维护升级中，该功能暂不可用",
	ErrAntiCmdOpUserBanForbid:                            "系统升级中，该功能暂不可用",
	ErrChannelVotePkGetLockFail:                          "获取锁失败",
	ErrChannelVotePkRedisFail:                            "系统错误",
	ErrChannelVotePkExist:                                "发起投票失败，该房间正在投票中",
	ErrChannelVotePkArgsFail:                             "参数错误",
	ErrChannelVotePkNotExist:                             "PK已经结束",
	ErrChannelVotePkNotMember:                            "不是PK成员",
	ErrChannelVotePkNotAdmin:                             "需要管理员权限",
	ErrChannelVotePkCommonFail:                           "投票失败",
	ErrChannelVotePkNotLeftVote:                          "你的票数已经用完啦，感谢你的支持哟",
	ErrChannelVotePkNameInvalid:                          "投票话题中包含敏感词，请重新输入",
	ErrChannelVotePkNotChannelMode:                       "只有娱乐模式才能开启投票哟",
	ErrChannelDrawGameStatusOff:                          "涂鸦小画板已经关闭，请联系房管打开画板功能",
	ErrChannelDrawGameLineCntMax:                         "当前画板操作次数过多，请清屏后再画吧",
	ErrChannelDrawGameNotAdmin:                           "需要管理员权限",
	ErrChannelDrawGameLineNotExist:                       "线不存在",
	ErrChannelDraeGamePointCntMax:                        "笔画太长啦，松手再继续吧~",
	ErrChannelDrawGameUnavailable:                        "系统升级暂不可用",
	ErrGetFriendDebrisGiftTimeOut:                        "消息已过期",
	ErrGetFriendDebrisGiftRepeat:                         "礼物已经领取",
	ErrSendFriendDebrisDayCntLimit:                       "今日送礼次数达到上限",
	ErrReceiveFriendDebrisDayCntLimit:                    "对方今日收礼次数达到上限",
	ErrSendFriendTargetUidErr:                            "不能领取不是送给自己的礼物",
	ErrYouReceiveFriendDebrisDayCntLimit:                 "今日收礼次数已达上限，明天再试吧",
	ErrOnlineChannelFollowAuthClose:                      "您的玩伴未开启“跟随进房”功能，快去让他开启吧",
	ErrOnlineChannelFollowAuthNotOpen:                    "用户跟随进房间设置未打开",
	ErrOnlineChannelFollowNotEnter:                       "您跟随的玩伴现在没有在房间内",
	ErrUsertagConfNotExist:                               "标签不存在",
	ErrUsertagConfDel:                                    "该标签已经被删除 不可设置",
	ErrUsertagGameOptConfDel:                             "游戏标签的该选项已经被删除 不可设置",
	ErrUsertagSetCountOverlimit:                          "设置的标签数量超过限制",
	ErrUsertagSetGametagNickSensitive:                    "游戏卡片中游戏昵称包含敏感词",
	ErrUsertagSetBirthdayFormatErr:                       "无效的生日标签格3904式",
	ErrGamecardDuplicateCreate:                           "游戏卡重复创建",
	ErrGameTabNotExistErr:                                "游戏暂不存在",
	ErrGamecardSetParamErr:                               "游戏卡参数错误",
	ErrSearchSphinxErr:                                   "Sphinx错误",
	ErrUserAlreadyBindKefu:                               "该用户当前由其他客服绑定服务",
	ErrKefuAllOffline:                                    "没有在线客服",
	ErrGreenbabaSanctionChannel:                          "该房间暂时无法使用",
	ErrGreenbabaSanctionOpUser:                           "您挑战了绿色爸爸的力量 正在被制裁 (说人话就是您被封了)",
	ErrGreenbabaSanctionTargetUser:                       "对方挑战了绿色爸爸的力量 正在被制裁(说人话就是对方被封了)",
	ErrExchangeItemPriceNotUpToDate:                      "兑换价格不是最新",
	ErrExchangeItemInvalidCurrencyType:                   "无效的兑换类型",
	ErrExchangeDuplicateOrderId:                          "每个提现周期只能提现一次哦",
	ErrExchangeTbeanInsufficientFunds:                    "今天豆豆已经抢光了哟，请明天再来吧～",
	ErrExchangePointsSettling:                            "正在结算积分中，请稍候再来吧~",
	ErrExchangeFuseProtection:                            "积分兑换T豆库存不足，请稍后再试",
	ErrExchangeFuseProtectionProcess:                     "积分兑换T豆库存达到警戒线",
	ErrExchangeBlackUser:                                 "账号异常，请联系客服",
	ErrExchangeParamErr:                                  "参数错误",
	ErrExchangeSignErr:                                   "签约状态更新错误",
	ErrExchangeCheckFaceNotPass:                          "人脸校验不通过",
	ErrExchange2327Cash:                                  "请在每月23号-27号发起提现哦",
	ErrExchangeWeekEndCash:                               "请在周末发起提现哦",
	ErrExchangeCannotCash:                                "暂不可进行积分提现",
	ErrExchangeNotRealName:                               "还没实名认证或处于审核中",
	ErrExchangeNotAge18Cash:                              "因你未满18周岁，不可发起提现",
	ErrExchangePointNotEnough:                            "积分不足",
	ErrExchangeMaskPk10000:                               "10000以上积分才能提现哦",
	ErrExchangeCannotLargerThan1000wOnce:                 "单次兑换不能超过1000万积分",
	ErrExchangeWithdrawMonthMax:                          "提现异常，请联系TT ID80119",
	ErrExchangeConfErr:                                   "配置错误",
	ErrExchangeAlgErr:                                    "算法错误",
	ErrTbeanDuplicateOrder:                               "重复的订单号",
	ErrTbeanNoEnoughBalance:                              "T豆余额不足",
	ErrTbeanSystemError:                                  "T豆系统错误",
	ErrTbeanFreezeBlackList:                              "账号异常，请联系客服",
	ErrTbeanParamError:                                   "参数错误",
	ErrTbeanAbnormalProtect:                              "异常充值",
	ErrTbeanMinorRecharge:                                "未成年人充值",
	ErrUnifiedPayDuplicateOrder:                          "重复的订单号",
	ErrUnifiedPayNoSuchOrder:                             "无此订单",
	ErrUnifiedPayRmbNoSupport:                            "不支持该功能",
	ErrUnifiedPayRmbCbErr:                                "回调错误",
	ErrUnifiedPayRmbOrderFailed:                          "下单失败",
	ErrUnifiedPayRmbDuplicateOrder:                       "重复订单号",
	ErrUnifiedPayRmbNoSuchOrder:                          "不存在的订单",
	ErrUnifiedPayRmbErr:                                  "支付系统错误",
	ErrUnifiedPayRmbRiskFailed:                           "风控检测失败",
	ErrUnifiedPayRmbRiskNeedFace:                         "支付前需要进行人脸认证",
	ErrUnifiedPayRmbRiskNeedRealname:                     "支付前需要进行实名认证",
	ErrUnifiedPayRmbRiskFaceFailed:                       "人脸认证失败",
	ErrRushInQueue:                                       "正在排队中，请稍候",
	ErrFindFriendsNoRegistered:                           "你未在扩圈中登记资料，请先完善资料",
	ErrAnotherQuickMatchIsAfoot:                          "你有另一个匹配正在进行中，请别着急~",
	ErrQuickMatchCurrentChannelIsSupplying:               "当前房间正在补位中，请别着急~",
	ErrQuickMatchGameNotSupport:                          "你选择的游戏已经不再支持匹配，请重新选择~",
	ErrQuickMatchPunishedForDeserter:                     "由于你最近从匹配房间中秒退，请稍后再进行匹配~",
	ErrFindFriendsReachedDailyLikeLimit:                  "今天已经喜欢了太多人了，不要那么花心哦",
	ErrHeadwearNotFound:                                  "不存在的麦位框",
	ErrHeadwearNotInUse:                                  "用户没有使用麦位框",
	ErrHeadwearNotHave:                                   "用户没有该麦位框",
	ErrHeadwearExpired:                                   "麦位框已经过期",
	ErrHeadwearNotSameCp:                                 "与该麦位框绑定的CP不一致",
	ErrHeadwearOrderidExist:                              "麦位框订单已存在",
	ErrHeadwearCpTypeNotCpUid:                            "CP麦位框缺少CP对象UID",
	ErrHeadwearCanNotUse:                                 "无法使用该麦位框，请升级版本",
	ErrHeadwearCanNotChangeUse:                           "无法更换麦位框哦~",
	ErrHeadwearConfExist:                                 "麦位框配置已存在",
	ErrActivityPresentEnded:                              "活动已结束",
	ErrChannelTriviaGameNotSupport:                       "当前版本不支持答题，请升级版本",
	ErrChannelTriviaGameActNotExist:                      "答题活动不存在或者尚未开始",
	ErrChannelTriviaGameQuestionNotExist:                 "题目不存在",
	ErrChannelTriviaGameQuestionExpire:                   "题目过期或者尚未开始答题",
	ErrChannelTriviaGameAnswerNoQualify:                  "您已经被淘汰了,不能继续答题",
	ErrChannelTriviaGameNotQuestionPhase:                 "非答题阶段",
	ErrChannelTriviaGameShowSolutionEarly:                "答案公布太早了",
	ErrChannelTriviaGameErrPhase:                         "阶段顺序异常",
	ErrChannelTriviaGameNoLivesToResurrect:               "复活卡不足",
	ErrChannelTriviaGameNoResurrectChancesInThisPeriod:   "本轮已经无法使用复活机会",
	ErrChannelTriviaGameNotStart:                         "活动未开始",
	ErrChannelTriviaGameAlreadyEnd:                       "活动已经结束",
	ErrChannelTriviaGameAlreadyShowSolution:              "已经公布过答案",
	ErrChannelTriviaGameAlreadyAnswer:                    "不能重复答题",
	ErrEsgwNotFound:                                      "不存在",
	ErrGuildGameCfgNotExist:                              "非公会游戏",
	ErrGuildGameAddLimit:                                 "同时添加的公会主打游戏太多咯",
	ErrGameRecruitExist:                                  "游戏招募已存在",
	ErrGameRecruitNotHaveTeam:                            "没有加入任何队伍",
	ErrGameRecruitNotExist:                               "该游戏组队已结束",
	ErrGameRecruitFullMember:                             "该游戏组队已满员",
	ErrGameRecruitNotInChannel:                           "用户不在频道中",
	ErrGameRecruitFrequenceLimit:                         "频率限制",
	ErrPresentSourceInvalid:                              "礼物不存在",
	ErrPresentInternalSender:                             "消费异常，请检查您的消费对象账号",
	ErrPresentInternalTarget:                             "消费失败，当前消费对象账号异常",
	ErrBackpackPackageItemNotExist:                       "包裹不存在该包裹项配置",
	ErrBackpackUserNotEnoughItem:                         "背包物品不足",
	ErrBackpackPackageItemTimeout:                        "背包物品已过期",
	ErrBackpackFuncCardAlreadyUse:                        "已使用同类型卡片",
	ErrBackpackUserItemNotFind:                           "用户背包找不到该项",
	ErrBackpackUseSourceIdErr:                            "使用物品源id不一致",
	ErrBackpackUseItemTypeErr:                            "使用物品类型不一致",
	ErrBackpackUseFragmentSendErr:                        "碎片不支持赠送",
	ErrBackpackUseFreezeOrderConfilctErr:                 "order_id 冲突",
	ErrBackpackUseFreezeOrderNonexistErr:                 "order_id 不存在",
	ErrBackpackUseFreezeOrderFinishedErr:                 "order_id 已经完成",
	ErrBackpackUseFunccardLevelLimit:                     "不可使用比当前低倍或相同倍数的加速卡",
	ErrBackpackUseFreezeTypeInvalidErr:                   "该类型不支持冻结",
	ErrBackpackOrderExist:                                "订单号已存在",
	ErrBackpackNumLimit:                                  "超过单次发包裹数量",
	ErrBackpackOrderNotExist:                             "订单不存在",
	ErrBackpackTimestampInvalid:                          "时间戳参数错误",
	ErrBackpackIsDeleted:                                 "包裹不存在",
	ErrBackpackNumInvalid:                                "非法物品数量",
	ErrBackpackSysDbFail:                                 "背包系统错误",
	ErrBackpackInvalidParams:                             "参数错误",
	ErrBackpackGetFuncCardErr:                            "获取卡片配置出错",
	ErrBackpackFuncCardTypeErr:                           "卡片类型错误",
	ErrChannelDatinggameEntryNotOpen:                     "该房间暂无相亲游戏模式权限",
	ErrChannelDatinggamePhaseError:                       "相亲游戏阶段设置错误",
	ErrChannelDatinggameApplyMicUserOverLimit:            "相亲游戏申请上麦人数超过限制",
	ErrChannelDatinggameUserNotSelectLikeObj:             "用户没有选择心动对象",
	ErrChannelDatinggameCanNotOpenUser:                   "不能公布心动对象",
	ErrChannelDatinggameErrStageOperation:                "该阶段不能进行此操作",
	ErrChannelDatinggameNotVip:                           "不可上土豪王座",
	ErrChannelDatinggamePhaseVipError:                    "此相亲阶段不可上土豪王座",
	ErrChannelDatinggameOpenUserCoolDown:                 "公布心动对象冷却中~",
	ErrChannelDatinggameAlreadyOpenUser:                  "已经公布过该用户心动对象~",
	ErrUserAlreadyCheckin:                                "今天已签到",
	ErrCheckinAwardNotExist:                              "签到奖励不存在",
	ErrCheckinConfigNotExist:                             "签到配置不存在",
	ErrRealnameNotSetingErr:                              "需要进行实名认证",
	ErrRealnameNotFinished:                               "实名认证没有完成",
	ErrRealnameAlreadyFinished:                           "实名认证已经完成",
	ErrRealnameUnknownStatus:                             "未知的实名认证状态",
	ErrRealnameNotIdentityInfo:                           "没有该用户的身份证信息",
	ErrRealnameNotStatusInfo:                             "没有认证信息",
	ErrRealnameBindPhoneLimit:                            "此手机号已经认证十个账号，请用其它手机号认证",
	ErrRealnameBindPhoneLimitTt:                          "绑定的手机号已经认证十个账号，请联系客服",
	ErrRealnameBindIdentityLimit:                         "此身份证已实名认证了十个账号,请使用其他身份信息验证",
	ErrRealnameInvalidParameter:                          "参数错误",
	ErrRealnameInvalidIdentityNum:                        "无效的身份证号码",
	ErrRealnameInvalidIdentityName:                       "名字错误",
	ErrRealnameUpgradeStopService:                        "实名认证系统升级中，暂不可用",
	ErrRealnameNeedManualCheck:                           "认证失败，请升级客户端走人工审核通道",
	ErrRealnameInvalidIdentityInfo:                       "身份信息有误，请检查姓名或身份证号并重新输入",
	ErrRealnameCheckFaceFailLimit:                        "今日人脸识别认证次数过多，无法进行认证",
	ErrRealnameCntLimit:                                  "今日实名认证次数过多，无进行认证",
	ErrRealnameNeedIdAuth:                                "需要完成实名认证",
	ErrRealnameNeedFaceAuth:                              "需要完成实名认证和刷脸认证",
	ErrRealnameYoungAuth:                                 "未成年人不允许操作",
	ErrCountIsOffErr:                                     "已经关闭了开关",
	ErrCountIsOnErr:                                      "已经开启了开关",
	ErrCountOnMicErr:                                     "上麦事件消费错误",
	ErrCountIsOff:                                        "计数器未开启",
	ErrCountRankNotSupport:                               "计数战力榜维护中",
	ErrAntispamNeedVerifyCode:                            "需要弹验证码进行行为验证",
	ErrAntispamVerifyCodeCheckFailed:                     "验证码验证错误",
	ErrAntispamTokenNotExist:                             "反垃圾token不存在",
	ErrAntispamSuspectWarning:                            "请谨慎防范冒充诈骗",
	ErrAntispamSuspectReject:                             "诈骗风险较高，无法操作",
	ErrAntispamImNotSeen:                                 "IM违规内容",
	ErrAntispamDrawGameBanned:                            "涂鸦功能因违规被禁用",
	ErrAntispamChannelImNotSeen:                          "公屏违规内容",
	ErrEmojiMaxLimit:                                     "你添加的表情数量已经达到上限，请删除部分表情再尝试添加",
	ErrEmojiAlreadyAdd:                                   "你已经添加过这个表情了哦",
	ErrEmojiVerifyError:                                  "校验失败",
	ErrEmojiDownloadError:                                "下载失败",
	ErrEmojiUploadError:                                  "上传失败",
	ErrEmojiUploadFileTooLarge:                           "文件大小超出系统最大限制",
	ErrEmojiPkgPermissionDenied:                          "无权限获取或修改表情包",
	ErrEmojiTargetNotExists:                              "该表情不存在",
	ErrEmojiUnsupportedImgFormat:                         "不支持的图片格式",
	ErrEmojiAlreadyAuditReject:                           "添加失败，该表情已被审核拒绝",
	ErrChannelPslUnknownDecorationType:                   "不支持的装饰类型",
	ErrChannelPslGrantingExpiredDecoration:               "该装饰已经过期",
	ErrChannelPslGrantingInvalidDecoration:               "请求无效",
	ErrChannelPslGrantingOrderIdDuplicate:                "发放装饰的订单号重复",
	ErrChannelPslConfigDuplicate:                         "装饰配置重复",
	ErrChannelPslConfigNotExist:                          "装饰配置不存在",
	ErrChannelPslConfigError:                             "其他装饰配置错误",
	ErrCustomTextCheckError:                              "不符合平台审核规范，请重新修改",
	ErrCustomTextChangeLimit:                             "文案调整次数已达上限",
	ErrGuardianPwdErr:                                    "密码错误",
	ErrGuardianOnErr:                                     "已经打开家长监护模式",
	ErrGuardianOffErr:                                    "已经关闭家长监护模式",
	ErrGuardianPwdNullErr:                                "密码不能为空",
	ErrGuardianDbErr:                                     "数据库访问异常",
	ErrGuardianUnonErr:                                   "未开启家长监护模式",
	ErrGuaidianCheckIdentityByFaceNotPass:                "扫脸验证未通过",
	ErrGuaidianCheckCountIsOverLimit:                     "今天已经申诉很多次了，明天再试吧",
	ErrGuaidianYoungOffDenied:                            "您是未成年人，不能关闭青少年模式",
	ErrRealnameCheckFaceException:                        "人脸认证异常",
	ErrRealnameAuthLimit:                                 "暂无法进行实名认证",
	ErrRealnameForbidDeviceAuth:                          "检测到您的账户存在风险，请48小时后再尝试",
	ErrRealnameNeedFaceAuthLimit:                         "需要进行人脸认证解除限制",
	ErrRealnameNeedRealnameLimit:                         "需要进行人脸认证解除限制，请先进行实名认证",
	ErrRealnameFaceAuthFailLimit:                         "需要进行人脸认证解除限制，人脸验证失败",
	ErrRealnameFaceAuthTimeout:                           "人脸认证超时",
	ErrUgcTopicNotExists:                                 "该话题不存在",
	ErrUgcTopicDisable:                                   "该主题还没上线",
	ErrUgcTopicCreateDuplicateName:                       "主题名称同名冲突",
	ErrUgcTopicCreateDuplicateBindUid:                    "主题绑定的官方账号已经被使用",
	ErrUgcTopicBindedParent:                              "话题绑定的圈子失败",
	ErrUgcTopicBindedGame:                                "话题绑定游戏ID失败，重复绑定,topicid:%s,topicname:%s",
	ErrUgcHadLike:                                        "已经点过赞了",
	ErrUgcHadNotLike:                                     "已经取消赞了",
	ErrUgcFriendshipFollowLimited:                        "关注失败，你的关注已达到上限",
	ErrUgcFriendshipAntispamHit:                          "关注失败，对方已开启免打扰",
	ErrUgcFriendshipBatchFollowLimited:                   "关注失败，批量关注达到上限",
	ErrUgcInteractivePermissionDenied:                    "无权限操作",
	ErrUgcInvalidFeedGroup:                               "无效的feed 类型",
	ErrUgcInvalidVisitRecordType:                         "非法的记录类型",
	ErrUgcFriendshipBatchUnfollowLimited:                 "取关失败，单次取关数量达到上限",
	ErrUgcHotRecordNotExists:                             "该热度记录不存在",
	ErrUgcNotAllowToFollowYourself:                       "不允许关注你自己哦",
	ErrUgcPostNotExists:                                  "帖子不存在",
	ErrUgcCommentNotExists:                               "评论不存在",
	ErrUgcPermissionDenied:                               "权限不足",
	ErrUgcAttachmentStatusInvalid:                        "附件状态无效",
	ErrUgcPostBanned:                                     "该动态已被屏蔽",
	ErrUgcPostDeleted:                                    "该动态已被删除",
	ErrUgcCommentBanned:                                  "评论已被屏蔽",
	ErrUgcCommentDeleted:                                 "评论已被删除",
	ErrUgcPostPostCooldown:                               "手速太快啦，歇一会再来吧",
	ErrUgcPostCommentCooldown:                            "哎呀，手速太快啦，人家接受不了惹",
	ErrUgcPostOpInBlackList:                              "你已拉黑对方，需移出黑名单后才可操作",
	ErrUgcPostOpInBlackListForbid:                        "由于对方的设置，你暂时无法操作",
	ErrUgcFriendshipOpInBlackListForbid:                  "你已拉黑对方，需移出黑名单后才可重新关注",
	ErrUgcFollowOpInBlackListForbid:                      "由于对方的设置，你暂无法关注",
	ErrUgcStickyPostCountLimit:                           "置顶动态已达上限",
	ErrUgcStickyCommentCountLimit:                        "置顶评论已达上限",
	ErrUgcPostPrivacyPolicyPrivate:                       "opps~这条动态被隐藏起来啦",
	ErrUgcCannotMarkPostSticky:                           "置顶失败，私密动态不支持置顶哦~",
	ErrUgcUserAttitudeCommentLimit:                       "操作频繁，休息一下吧",
	ErrUgcPostCommentOnlyShowSelf:                        "评论仅自己可见",
	ErrUgcCelebrityNotExisted:                            "UID对应的用户并非优质用户",
	ErrUgcCelebrityExisted:                               "用户已是优质用户",
	ErrUgcGetFollowPostFrequentErr:                       "拉取用户关注的位置频次过高",
	ErrUgcGetMoodDelErr:                                  "拉取的心情聚合页心情已删除",
	ErrUgcRecommendNil:                                   "推荐流为空",
	ErrUgcRecommendErr:                                   "推荐流报错",
	ErrUgcRecommendBussNil:                               "推荐流业务返回为空",
	ErrUgcRecommendCantPublishVideo:                      "不能上传视频",
	ErrScriptShouldUni:                                   "台本重复",
	ErrInvalidUserDecorationType:                         "类型错误",
	ErrUserDecorationNotExist:                            "座驾不存在",
	ErrUserContractUnknown:                               "用户服务协议状态异常",
	ErrUserContractInvalidVersion:                        "用户服务协议版本错误",
	ErrNonPublicUgcNoPermissionPublish:                   "无权限发布",
	ErrNonPublicUgcNoPermissionTalkPublish:               "加入社团才能发布哦",
	ErrNonPublicUgcNoPermissionKnowledgePublish:          "只有社团核心成员才能发布哦",
	ErrNonPublicUgcNoPermissionCategoryPublish:           "只有加入该品类的社团才能发布哦",
	ErrNonPublicUgcNoPermissionInteract:                  "加入该社团才能互动哦",
	ErrNonPublicUgcNoPermissionVoteExpired:               "投票已结束",
	ErrNonPublicUgcNoPermissionVoteVoted:                 "已经投过票啦，不能再投啦",
	ErrUnmatchPushSample:                                 "推送参数个数与模板对不上",
	ErrCarVersionNotSupport:                              "当前车载版本不支持该功能 请使用APP版本来操作",
	ErrNoviceRecommendUnknownStatus:                      "状态类型不受支持",
	ErrNoviceRecommendAlreadyDisplay:                     "展示已打开",
	ErrNoviceRecommendAlreadyHide:                        "展示已关闭",
	ErrNoviceRecommendPermissionDenied:                   "没有权限改变展示状态",
	ErrNoviceRecommendNotRecommend:                       "不是开黑新人推荐房",
	ErrTopicChannelCreateChannelLevelNotAllow:            "您的等级未达到10级哦~请先到大厅挑挑吧~",
	ErrTopicChannelCreateChannelTagRequired:              "完善个人标签才能创建房间哟~",
	ErrTopicChannelCreateChannelRecommendTypeNowAllow:    "推荐房暂时不支持发布到约玩大厅哦~",
	ErrTopicChannelNotFound:                              "主题房间不存在~",
	ErrTopicChannelChannelLocked:                         "房间已上锁，解锁房间后才能发起哦~",
	ErrTopicChannelTabNotFound:                           "未找到对应标签哦~",
	ErrTopicChannelNameInvalid:                           "房间名含有非法字符~",
	ErrTopicChannelNameIsNull:                            "房间名不能为空~",
	ErrTopicChannelNameFormattedInvalid:                  "请输入15个字以内的有效房间名~",
	ErrTopicChannelPermissionDenied:                      "无权限操作",
	ErrTopicChannelNameSensitive:                         "房间名中包含敏感词，若多次发布包含敏感词的房间名，将面临封号危险",
	ErrTopicChannelNameSensitiveForHighRisk:              "服务器无响应，发布失败",
	ErrTopicChannelCreateChannelGameTagRequired:          "完善游戏卡才能创建房间哟~",
	ErrTopicChannelSetRoomNameFormatInvalid:              "设置房间名参数错误",
	ErrTopicChannelNameNoExist:                           "房间名不存在",
	ErrTopicChannelNameConfigVersionInvalid:              "房间名配置版本旧了",
	ErrTopicChannelSetRoomNameVersionInvalid:             "设置房间名版本号错误",
	ErrTopicChannelNoPermissionChange:                    "发布了游戏类房间需升级到最新版哟~快去升级吧~",
	ErrTopicChannelCanNotUseTab:                          "小可爱您好，为了给您提供更好的房间体验，非游戏房间暂不开放，我们正在积极优化升级敬请期待。",
	ErrTopicChannelNotAllowModifyName:                    "发布公开房间暂时不支持自定义房间名哟",
	ErrTopicChannelReleaseTooManyTimes:                   "发布房间操作过于频繁，请明天再来吧",
	ErrTopicChannelReleaseBorderOnLimit:                  "温馨提示：频繁发布房间可能会导致发布功能受限哦",
	ErrTopicChannelTabIsNotExist:                         "系统维护更新中，无法将房间切换至该玩法",
	ErrTopicChannelCanNotPublish:                         "该房间暂不支持发布",
	ErrTopicChannelSelectTabBeforePublish:                "选择房间标签后才能发布~",
	ErrFallBackSwitchCantSet:                             "业务兜底开关不支持切换",
	ErrTopicChannelCommonError:                           "主题房通用错误",
	ErrTopicChannelBlockSettingNeedRefresh:               "发布信息已过期，请点击修改按钮重新发布",
	ErrUsermatchMatchOverLimit:                           "今天已经找了很多人了哟，先和他们聊聊吧~",
	ErrExchangeLiveBroNoBegin:                            "现在不是可兑换的时间啊~",
	ErrExchangeLiveBroNoLiveUser:                         "没有可兑换的直播收益~",
	ErrExchangeLiveBroExchangeAlready:                    "已经兑换过了~",
	ErrKnockDb:                                           "敲门时数据库错误",
	ErrKnockNotUidExist:                                  "处理敲门时没有操作者uid信息",
	ErrKnockWrongRoomType:                                "错误房间类型",
	ErrKnockRedisFail:                                    "redis报错",
	ErrKnockOneUserOneMinute:                             "一分钟内对同一玩家请求数只能一次",
	ErrKnockTimeOut:                                      "同意拒绝超过timeout时间（10s）",
	ErrKnockTicketTimeout:                                "敲门进房只有5分钟的有效期哦，已经过期请重新敲门",
	ErrKnockTicketError:                                  "敲门信息错误，不要伪装成别人进房哟",
	ErrKnockPeopleInroom:                                 "敲门者在房间内",
	ErrKnockAdminEverHandle:                              "其它房管已处理",
	ErrUserBlackListAddSelf:                              "无法拉黑自己",
	ErrUserBlackListAddOfficialAccount:                   "官方账号无法拉黑",
	ErrUserBlackListWasAdded:                             "对方已在黑名单",
	ErrUserBlackListPatRelation:                          "拍拍关系有效期内不可以拉黑对方",
	ErrContractApplyNoBindPhone:                          "你的账号未绑定未绑定手机号码，无法签约",
	ErrContractApplyNoRealname:                           "你尚未完成实名认证，无法签约",
	ErrContractApplyUnderAge:                             "你不满足签约年龄要求，无法签约",
	ErrContractNonexist:                                  "合约不存在",
	ErrContractExist:                                     "合约已存在",
	ErrContractApplyIdentityLimit:                        "身份证已经绑定合约",
	ErrContractApplyHaveContract:                         "已经签约",
	ErrContractApplyTodayLimit:                           "次日0点之后才可以重新签约哟",
	ErrContractApplyAlready:                              "重复申请",
	ErrContractHandleConflict:                            "已被其它公会签约",
	ErrContractHandleTimeout:                             "超时的申请，已失效",
	ErrContractHandleInvalid:                             "该条申请已失效",
	ErrContractApplysignNonexist:                         "签约申请不存在",
	ErrContractExtensionExist:                            "已邀请续约",
	ErrContractExtensionNonexist:                         "续约不存在",
	ErrContractApplyLimit:                                "申请次数超限",
	ErrContractApplyIdentityApplyLimit:                   "同一实名账号已申请",
	ErrContractExtensionCannot:                           "未达到续约条件",
	ErrContractYearSignLimit:                             "您本年度的签约次数已达上限，暂时无法签约新的公会",
	ErrContractApplyIdentityTypeLimit:                    "您正在申请其他身份",
	ErrContractApplyHaveIdentityType:                     "已拥有该身份身份",
	ErrContractWithdrawApplyStatusChange:                 "申请状态发生变化，撤销失败",
	ErrContractApplyGuildNotCoopType:                     "公会无该合作库类型",
	ErrContractApplyBlacklistLimit:                       "您正处于该身份申请冷却期中，暂时无法发起对应身份的考核申请",
	ErrContractApplyHasHandling:                          "已有会长同意你的申请，请先等待官方处理结果再继续申请。可前往“申请记录”中查看详情",
	ErrContractApplySignLiveAnchorLimit:                  "抱歉，您选择的公会今日已没有签约额度，明天再来吧～",
	ErrContractApplyLiveAnchorGuildLimit:                 "今日可提交的官方审批名额已达限额哦~",
	ErrContractAudioLilmitStatusDay:                      "本日语音主播审批同意次数已达上限",
	ErrContractAudioLilmitStatusMonth:                    "本月语音主播审批同意次数已达上限",
	ErrContractApplyAnchorLimitAge:                       "年龄不符，不符合签约要求",
	ErrContractApplyAnchorLimitXiaohao:                   "有其他非同公会的账号存在签约关系，不符合签约要求",
	ErrContractApplyAnchorLimitRecharge:                  "账号属于消费账号，不符合签约要求",
	ErrContractApplySignLimitEsport:                      "电竞指导不可解约",
	ErrContractApplySignLimitOther:                       "不满足解约条件",
	ErrContractApplySignLimitExchange:                    "存在积分冻结的签约用户不可解约",
	ErrContractApplySignLimitAgent:                       "经纪人或管理员的签约账号不可解约",
	ErrContractApplySignIsInternalUid:                    "申请异常，请检查申请签约的公会账号",
	ErrContractApplySignIsInternalGuild:                  "申请失败，当前申请签约公会账号异常",
	ErrContractApplySignSettlement:                       "您的账号不满足签约条件，请联系客服~",
	ErrSmashEggChanceNotEnough:                           "道具不足",
	ErrSmashEggReachDailyLimit:                           "今日已达上限，明日再试试吧",
	ErrSmashEggSystemMaintain:                            "系统维护",
	ErrSmashEggActivityUpdate:                            "活动版本变化，请重新拉取配置",
	ErrBlacklistCheckNotPass:                             "账号异常，该功能不可用",
	ErrTopicChannelFreeze:                                "您的账号处于禁言状态，暂不可以可进行此操作",
	ErrTopicChannelQuickMatchVersionMiniGameRequired:     "发起速配失败，请升级至最新版本才可以匹配到小游戏房间哦",
	ErrTopicChannelNameCanNotDiy:                         "因系统升级，暂不支持修改房间名称，请重新选择房间名称发布",
	ErrTopicChannelUserLevelLimit:                        "您当前等级不足，暂不允许发布房间",
	ErrTopicChannelQuickMatchNoChannel:                   "当前匹配不到房间哦",
	ErrTopicChannelNameViolationOfThesaurus:              "文本包含违反当前玩法规范的内容",
	ErrTopicChannelPublishConfigConflict:                 "发布房间配置冲突",
	ErrTopicChannelSensitiveChannelName:                  "审核失败，不允许保存",
	ErrGetGangupAdvConfFailed:                            "获取开黑tab广告位配置失败",
	ErrGetGangupAdvUserInfoFailed:                        "获取开黑tab广告位用户信息失败",
	ErrNonValidGangupAdvConf:                             "无有效开黑tab广告位配置",
	ErrOpengameChangeGameTooOfen:                         "修改房间游戏太频繁",
	ErrOpengameTemporaryPlayernum:                        "小游戏匹配人数为0",
	ErrMiniGameMaintain:                                  "游戏维护中,请稍后匹配",
	ErrNobilityExclusiveMagicexpression:                  "成为贵族即可使用该表情",
	ErrNonNobiltiyExclusivePresent:                       "没有贵族专属礼物特权",
	ErrNonNobiltiyNotPrivilege:                           "没有使用此特权权限",
	ErrNobilitySensitiveTimeRange:                        "暂不能发送小喇叭消息",
	ErrNobilityEqulLevel:                                 "充值后等级小于等于原等级",
	ErrNobilityInvisibleTakeHoldMic:                      "该用户不在房间内",
	ErrNobilityFrequentlyLimit:                           "操作太频繁，请稍后再试",
	ErrNobilitySystemErr:                                 "贵族系统错误",
	ErrNobilityDuplicateOrderErr:                         "贵族系统订单重复",
	ErrGuildHonorHallParaInval:                           "荣誉宫殿系统参数错误",
	ErrGuildHonorHallSetRepeated:                         "荣誉称号重复",
	ErrAddFansLoveValueOrderExistLimit:                   "订单号已存在",
	ErrFansGroupLeaveLimit:                               "退出主播直播间后才可以操作退团哦",
	ErrChannelLivePkOtherAnchorGameLimit:                 "该直播间正在玩互动玩法，暂不支持连PK哦",
	ErrChannelLivePkAnchorGameLimit:                      "互动玩法进行时不支持开启PK哦",
	ErrFansGroupWearPlateLimit:                           "请先设置团名",
	ErrChannelLiveYkwOpenLimit:                           "神秘人不可以开播",
	ErrChannelLivePkCntLimit:                             "每日20:00～22:00期间只允许连麦PK两次哦～",
	ErrChannelLiveAppointPkIngNoLaunchPk:                 "正进行活动比赛PK，不可主动发起PK",
	ErrChannelLiveAppointPkIngNoAcceptPk:                 "对方正进行活动比赛PK，暂不可接受PK",
	ErrChannelLiveNotAuthority:                           "没有语音直播权限",
	ErrChannelLiveIdInvalid:                              "过期的直播ID",
	ErrChannelLiveNotOpen:                                "达人还未开启听听哦",
	ErrFansGroupNameNotStanderd:                          "粉丝团名不允许出现敏感词汇/数字/符号/英文/表情，请检查后重新提交",
	ErrFansGroupNameIsExist:                              "团名已被占用",
	ErrFansGroupNameVerifying:                            "团名正在审核，审核结果将助手推送",
	ErrFansGroupNameMemberCntLimit:                       "粉丝团人数达到10可自定义团名",
	ErrFansGroupNameFontCntLimit:                         "粉丝团名称最多3个字",
	ErrFansSetGroupNameCntLimit:                          "团名暂不支持修改，需修改请联系官方",
	ErrChannelLivePkRepeatedApply:                        "不能重复申请",
	ErrChannelLivePkIng:                                  "对方正在PK",
	ErrChannelLiveNotPkAuth:                              "暂无PK权限",
	ErrChannelLiveCloseLimit:                             "PK中不能结束直播哦~",
	ErrChannelLivePkMatchInvalidTy:                       "匹配类型跟服务端不一致",
	ErrDistributorInvalid:                                "无效渠道号",
	ErrMaskedCallClosing:                                 "不在开放时间",
	ErrMaskedCallNoMoreTicket:                            "匹配次数不足",
	ErrMaskedCallTooMuchTipOff:                           "被举报次数过多",
	ErrEnterOtherChannelInLive:                           "不允许在直播时进入其他房间",
	ErrUserChannelPeopleOverlimit:                        "该房间人数已达上限",
	ErrMaxModifySexLimit:                                 "已修改过性别，不能再修改咯",
	ErrOldVersionModifySex:                               "当前版本不允许修改性别，请升级到最新版本",
	ErrSuperPlayerDuplicateOrderid:                       "重复订单",
	ErrSuperPlayerInvalidPara:                            "非法参数",
	ErrSuperPlayerInvalidPacket:                          "重复购买订单",
	ErrSuperPlayerSysErr:                                 "超级会员系统错误",
	ErrSuperPlayerInvalidAppstoreUser:                    "开通会员的苹果帐号存在AB帐号问题",
	ErrSuperPlayerSpecialConcernCountLimit:               "每个人最多有5个特别关心的小可爱哦～",
	ErrSuperPlayerPrivilegeExpire:                        "出错啦！你的超级玩家已经到期咯",
	ErrKnightGroupInvalidConf:                            "非法参数",
	ErrKnightGroupSysErr:                                 "服务错误",
	ErrKnightGroupOnlineChannelidInvalid:                 "用户不在房间",
	ErrYouknowwhoPrivilegeExpire:                         "出错啦！你的神秘人已经过期",
	ErrYouknowwhoMutualExclusion:                         "进厅隐身和神秘人不可同时开启",
	ErrYouknowwhoInvalidOper:                             "神秘人非法操作",
	ErrKnightGroupScoreNotEnougth:                        "骑士积分不足",
	ErrKnightGroupScoreOrderExist:                        "骑士积分订单已存在",
	ErrTopicChannelNotAllowSwitchPlay:                    "该主题房不支持切换玩法",
	ErrTopicChannelFrequentlySwitchPlay:                  "操作过于频繁",
	ErrTopicChannelCannotSwitchPlayWhenRelease:           "主题房发布过程中不可以修改小队信息和玩法或重复发布哦",
	ErrTopicChannelReleaseFreezing:                       "发布房间正在冷却中，请稍后再试",
	ErrTopicChannelNotOwnerSwitchPlay:                    "房间正在发布中，仅房主可切换房间玩法",
	ErrTopicChannelChangeFreezing:                        "修改主题房CD中",
	ErrTopicChannelTooManyMemberSwitchPlay:               "房间人数过多无法直接切换",
	ErrTopicChannelOwnerNotReal:                          "房主没有实名认证",
	ErrConfigTabDeleteWarn:                               "存在分类与主题绑定，不能删除",
	ErrUpdateCategoryPlatformTypeWarn:                    "分类的展示平台属性无法修改",
	ErrConfigTabConflictWarn:                             "配置展示平台和分类冲突",
	ErrConfigTabHeadNotConfig:                            "没有配置首页配置",
	ErrTempchannelInvalidChannelid:                       "临时房id无效",
	ErrChannelPwdErrLimit:                                "错误操作频繁，24小时内您将无法再通过输入密码进入该房间",
	ErrConversionComposeGiftConfInvalid:                  "无效的合成礼物配置",
	ErrConversionMaterialTotalPriceNotEnough:             "原料价值不足以合成目标礼物",
	ErrConersionComposeGiftConfPackagInvalid:             "合成礼物包裹配置错误",
	ErrConersionComposeGiftMaterialUnitPriceTooHigh:      "原材料单价不可高于合成礼物单价",
	ErrConersionComposeGiftLossTooMuch:                   "所选方案亏损太多啦",
	ErrGameHallAuditErr:                                  "服务出现问题",
	ErrGameHallAuditTextReject:                           "涉及违规用语，请文明交流",
	ErrGameHallEnterRoomLimit:                            "该房间人数已满，再看看其他车队吧",
	ErrGameHallSendMsgLimit:                              "今天次数已经用光啦，明天再来吧",
	ErrGameHallSendMsgNotMatchTab:                        "需要您处于对应玩法房才可以发送哦",
	ErrGameHallSendMsgMuchPeople:                         "您的房间人数较多，该功能暂时不可以使用",
	ErrGameHallSendMsgNotInOwnChannel:                    "需要您回到自己的房间才可以发送哦",
	ErrGameHallSendMsgNotInChannel:                       "需要您处于自己的房间才可以发送哦",
	ErrGameHallSendMsgReachThreshold:                     "今天您发言数量达上限了哦",
	ErrGameHallSendMsgBan:                                "T盾禁言",
	ErrGameHallSendMsgBusinessBan:                        "业务后台禁言",
	ErrGameHallJumpMsgExpired:                            "跳转消息已过期",
	ErrGameHallEnterRoomLockLimit:                        "很抱歉，该房间设置了锁房不可进入",
	ErrGameHallNoSendTeamMsg:                             "暂时不开放该功能，可以点击输入框先去聊聊天哦～",
	ErrRiskControlBackpackTbeanLimit:                     "风控限制,业务T豆余额不足",
	ErrRiskControlBackpackCountLimit:                     "风控限制,包裹数量额度不足",
	ErrRiskControlBackpackSignalCountLimit:               "风控限制，单次包裹数量限制",
	ErrRiskControlBackpackSignalTbeanLimit:               "风控限制,单次T豆价值限制",
	ErrRiskControlBackpackConfigNotFound:                 "找不到对应业务风控配置",
	ErrRiskControlBackpackOrderidInvalid:                 "风控限制,订单格式错误",
	ErrRiskControlBackpackBusinessNotFound:               "风控限制,找不到对应业务配置",
	ErrRiskControlBackpackAuthCheckFail:                  "风控限制,密钥检查错误",
	ErrRiskControlBackpackSysFail:                        "风控系统错误",
	ErrRiskControlBackpackDuplicateOrderid:               "订单重复",
	ErrRiskControlAwardCenterOrderExist:                  "订单已存在",
	ErrRiskControlAwardCenterOrderInvalid:                "订单号格式有误",
	ErrRiskControlAwardCenterSignalCountLimit:            "风控限制，超出单次发放数量限制",
	ErrRiskControlAwardCenterConfigInvalid:               "业务配置有误",
	ErrRiskControlAwardCenterDailyCountLimit:             "风控限制，超出每日发奖人次限制",
	ErrRiskControlPlobGameFuse:                           "业务熔断",
	ErrUserNicknameViolate:                               "用户昵称违规",
	ErrUserProfileViolate:                                "用户头像违规",
	ErrChannelNameViolate:                                "房间名违规",
	ErrChannelProfilViolate:                              "房间头像违规",
	ErrBannedEnterChannel:                                "禁止进房",
	ErrBannedKickUserFail:                                "踢号失败，请重试",
	ErrChannelTeamNotFount:                               "数据不存在",
	ErrChannelTeamToast:                                  "处理信息有误",
	ErrGameRadarConfigUpdated:                            "游戏雷达配置已更新，请重新获取",
	ErrGameRadarGameCardIncomplete:                       "游戏卡缺少必填信息无法打开雷达，请先将信息填写完整哦",
	ErrGameRadarIncomplete:                               "雷达信息缺少必填选项，请先将信息填写完整哦",
	ErrGameRadarTooManyCharacter:                         "字数太多啦",
	ErrGameRadarQuickFailed:                              "无法快速开启雷达",
	ErrGameRadarModelIncorrect:                           "无正确雷达游戏模式",
	ErrGameRadarIsClosed:                                 "雷达已按时关闭，点击再次开启",
	ErrGameRadarInviteExceed:                             "你已经约玩很多人啦，耐心等待对方回应吧～",
	ErrGameRadarIsAlreadyOpened:                          "雷达已经开启",
	ErrChannelLotteryBeginFail:                           "房间抽奖开始失败",
	ErrChannelLotteryTextErr:                             "敏感词",
	ErrChannelLotteryTimeViolate:                         "时间不对",
	ErrChannelLotteryJoinLotteryErr:                      "参与抽奖失败",
	ErrChannelLotteryInfoErr:                             "处理信息有误",
	ErrChannelLotteryJoinLotteryConditionLimit:           "参与抽奖条件受限",
	ErrChannelKickoutNotInChannel:                        "该用户不在房间",
	ErrSayhiCountExceedDayLimit:                          "今天的搭讪已经超出上限啦~",
	ErrChatCardExamineFailed:                             "扩列卡片审核不通过~",
	ErrChatCardHandlePrivate:                             "扩列卡片仅自己可见",
	ErrChatCardHandleFailed:                              "扩列卡片处理失败",
	ErrInvitePlayerFromChannel:                           "互相关注成为玩伴后，才能邀请Ta进房一起玩哦",
	ErrRequestParamInvalid:                               "请求参数错误",
	ErrRepositoryFailed:                                  "存储错误",
	ErrExternalSystemFailed:                              "外部第三方系统错误",
	ErrCommApiScrap:                                      "接口已经下线",
	ErrOfficialChannelNotInRunning:                       "无主播时官频不可送礼哦",
	ErrOfficialChannelScheduleAlreadyChange:              "排班信息已更新，请重新获取再修改",
	ErrOfficialChannelPermissionDenied:                   "操作用户权限不足",
	ErrSuperChannelTypeUnsupport:                         "请更新至最新版本进入新类型房间",
	ErrSuperChannelCloseMicQueueFailed:                   "请先将小麦位上用户抱下麦后再关闭连麦",
	ErrUserVisitorRecordLimitOut:                         "会员设置隐藏数量超过限制人数",
	ErrSlipNoteStatusErr:                                 "纸条状态异常",
	ErrPickSlipNoteFailed:                                "纸条评论选择失败",
	ErrNoEnoughSlipNote:                                  "发布纸条次数已用完",
	ErrSlipNotePoolIsEmpty:                               "纸条被人抢光啦，一会再试试吧~",
	ErrExceedFetchSlipNoteLimit:                          "瓶子被你掏空啦，明天再来吧~",
	ErrHoldZeroMicMustContract:                           "成为签约成员才能上此麦位哟",
	ErrMaskedPkNotQualification:                          "该厅无权限参与比赛",
	ErrMaskedPkNotInActivityTime:                         "不在活动期",
	ErrMaskedPkInPkCannotCancel:                          "正在pk中，无法取消",
	ErrMaskedPkCancelCntLimit:                            "取消次数过多，无法取消",
	ErrMaskedPkNotJoinGame:                               "未参与pk活动",
	ErrMaskedPkCannotMatching:                            "该状态无法开始匹配",
	ErrMaskedPkNotEnoughChip:                             "该时段比赛奖金发放完毕，请参加下时段的比赛",
	ErrMaskedPkGameEndCannotMatching:                     "比赛即将结束，无法匹配",
	ErrMaskedPkAddConfigTimeDupliacate:                   "比赛时间与现有的比赛重叠",
	ErrMaskedPkHadGivenUp:                                "已有管理员放弃了本场比赛，请参加下时段的比赛",
	ErrMaskedPkCannotGivenUp:                             "已参与了本场比赛，无法放弃",
	ErrMaskedPkInLivePk:                                  "正在进行直播pk，无法开始蒙面pk匹配",
	ErrObsgwNoSuchKey:                                    "指定的键不存在",
	ErrObsgwAccessDenied:                                 "拒绝访问",
	ErrObsgwExpiredToken:                                 "过期凭证",
	ErrObsgwInvalidArgument:                              "非法参数",
	ErrObsgwInvalidToken:                                 "非法凭证",
	ErrObsgwNoSuchBucket:                                 "指定的空间不存在",
	ErrObsgwSignatureDoesNotMatch:                        "签名不一致",
	ErrGuildCreateLevelNotEnough:                         "创建失败，账号需达到平台等级%d级和实名认证后才可创建公会。",
	ErrGroupCreateLevelNotEnough:                         "创建失败，账号需达到平台等级%d级和实名认证后才可创建群组。",
	ErrSearchCountLimit:                                  "搜索过于频繁，请稍后再试~",
	ErrContentInvalidInputAgain:                          "内容不合规，请重新输入",
	ErrCensoringAuditing:                                 "正在审核中，请稍后",
	ErrFellowInviteExistingUniqueBind:                    "对方已经跟其他人建立了唯一关系",
	ErrFellowInviteNotExist:                              "这条邀请已经失效了哦",
	ErrFellowInviteExistingFellowBind:                    "你们已经存在亲密关系了~",
	ErrFellowInviteNoSiteTarget:                          "对方的挚友位不足，无法绑定",
	ErrFellowInviteNoSiteSelf:                            "自己的挚友位不足",
	ErrFellowInviteExistingFellowInvite:                  "你已经向Ta发送了挚友申请",
	ErrFellowFellowAddBlacklist:                          "无法拉黑挚友哦",
	ErrFellowFellowExistingInviteFromTarget:              "Ta已经向你发送了挚友申请，先去看看吧～",
	ErrFellowFellowFellowTypeNotMatch:                    "非唯一关系类型错误",
	ErrFellowInviteHandledInvite:                         "对方已处理你的申请，不能撤回了哟",
	ErrFellowUnboundDuplicate:                            "你已发起了关系解除",
	ErrFellowNotFellow:                                   "你们还不是挚友，无法使用此功能",
	ErrFellowInviteCpExist:                               "你已经有CP关系，无法发起申请哦",
	ErrFellowInviteBlacklist:                             "你已拉黑对方或被对方拉黑，无法发出申请",
	ErrFellowInviteMaxSite:                               "对方挚友关系已达上限，无法向Ta发出申请",
	ErrFellowChannelInviteDuplicate:                      "你已有申请正在等待回应，过一会儿再来吧~",
	ErrFellowChannelInviteNotInRoom:                      "Ta已经离开房间，无法发起申请",
	ErrRareInvalid:                                       "你没有该稀缺关系或已过期",
	ErrFellowUkwNotAllowSendPresent:                      "神秘人期间不可送挚友礼物",
	ErrFellowUkwNotAllowReceivePresent:                   "对方是神秘人，不可送挚友礼物",
	ErrChannelCpGameCannotChangePhase:                    "无法切换到该阶段",
	ErrChannelCpGameCannotAddTime:                        "此阶段最多只能加十分钟哦~不能再加了",
	ErrChannelCpGameCannotEnterNextPhase:                 "由于没人上麦，不可进入下一阶段哦",
	ErrChannelCpGameCannotHoldMvpMic:                     "非MVP用户不能上MVP麦哦",
	ErrUsualDiviceNotUsualDeivceFace:                     "为保障您的账号财产安全，请进行安全验证",
	ErrUsualDiviceNotUsualDeivceMessage:                  "为保障您的账号财产安全，请进行安全验证",
	ErrUsualDiviceNotUsualDeivceBindPhone:                "当前设备不是常用设备，需要绑定手机号",
	ErrUsualDiviceNotUsualDeivceLowVersion:               "为了保障您的账号财产安全，当前操作需要进行安全验证，请升级至最新版TT语音以完成验证",
	ErrUsualDiviceNotUsualDeivceOnlyFace:                 "为保障您的账号财产安全，请进行安全验证",
	ErrSingARoundChannelInfoNotMatch:                     "房间状态信息不匹配",
	ErrSingARoundNoVacancyIsLeft:                         "游戏位已被占满",
	ErrSingARoundYouHaveBeenEliminated:                   "你已被淘汰",
	ErrSingARoundHasPassedTheGrabMicStage:                "已经过了抢唱阶段",
	ErrSingARoundHasPassedTheHelpStage:                   "已经过了帮唱阶段",
	ErrSingARoundHasPassedTheSingingStage:                "已经过了唱歌阶段",
	ErrSingARoundCanNotJoinTheGame:                       "当局人数已满，无法加入游戏",
	ErrSingARoundCanNotPrepare:                           "暂时无法准备，请稍后再试",
	ErrSingARoundLimitBeh:                                "你被反馈在该场景捣乱，已被进行行为限制",
	ErrSingARoundCanNotJoinThePassThroughGame:            "闯关模式下中途不能参与哟",
	ErrSingARoundNotSupportCouple:                        "更新至最新版本后才可进入该专区",
	ErrChannelRedPacketSendLimit:                         "今天发红包已达上限，明天可以继续",
	ErrChannelRedPacketPublicMsgFail:                     "公屏内容不合适，需要调整下喔~",
	ErrChannelRedPacketConfNotExist:                      "红包配置不存在或已失效",
	ErrChannelRedPacketOrderNotExist:                     "红包订单不存在",
	ErrChannelRedPacketChannelTypeErr:                    "该类型房间不支持红包功能",
	ErrMagicSpiritSendLimit:                              "送太快啦~明天再试试吧~",
	ErrMagicSpiritOrderNotExist:                          "订单不存在",
	ErrMagicSpiritNotValid:                               "系统维护，暂不支持送出该礼物，请稍后再试",
	ErrMagicSpiritSendPerAmountLimit:                     "送太快啦~稍后再试试吧~",
	ErrMagicSpiritOrderExist:                             "订单重复",
	ErrKtvOperationAddSongDeny:                           "无法点歌",
	ErrKtvOperationDeleteSongDeny:                        "无法删除",
	ErrKtvOperationCutSongDeny:                           "无法切歌",
	ErrKtvOperationTopSongDeny:                           "无法顶歌",
	ErrKtvOperationError:                                 "操作失败，请稍后重试",
	ErrKtvClose:                                          "KTV功能维护中",
	ErrKtvBurstError:                                     "爆灯失败",
	ErrDarkGiftBonusBuffConfErr:                          "配置错误",
	ErrHttpproxyTimeout:                                  "网络访问超时",
	ErrMusicRankLocateFailure:                            "定位失败",
	ErrSendMsgChannelBoxIdErr:                            "房间包厢ID错误",
	ErrChannelVersionControlHit:                          "当前版本不支持，请升级版本",
	ErrChannelVersionControlBan:                          "该房间玩法正在升级维护中，逛逛其他玩法吧~",
	ErrAnchorCheckDbErr:                                  "数据库错误",
	ErrAnchorCheckStartRecordErr:                         "开始录制错误",
	ErrAnchorCheckStopRecordErr:                          "结束录制错误",
	ErrAnchorCheckStatusErr:                              "状态错误",
	ErrMeleeChannelWhiteListUpperLimit:                   "人数超过上麦名单上限",
	ErrChannelHoldMicSizeLimit:                           "上麦人数已满",
	ErrChannelApplyMicTokenErr:                           "上麦token错误",
	ErrChannelReapplyMic:                                 "重复申请麦位",
	ErrDingCallNameAuditing:                              "闪电召集名称正在审核中，请稍后",
	ErrDingCallNickNameReject:                            "闪电召集名称审核不通过，请修改后发起一键召集",
	ErrDingCallGroupUnvalid:                              "房主未创建闪电召集",
	ErrDingCallSecretErr:                                 "密令错误，请重新输入",
	ErrDingCallSmsCodeErr:                                "短信验证码错误",
	ErrDingCallSecretClose:                               "管理员关闭了闪电密令，请重试",
	ErrDingCallSecretOpen:                                "管理员打开了闪电密令，请重试",
	ErrDingCallNickNameAuditing:                          "你的闪电召集昵称正在审核中，请稍后",
	ErrDingCallNameReject:                                "你的闪电召集昵称审核不通过，请修改后发起一键召集",
	ErrDingCallVerifyCodeTooMuch:                         "操作频繁，请24小时之后重置",
	ErrDingCallExceedUser:                                "已达到闪电召集绑定上限",
	ErrDingCallNotifyMsgHandled:                          "消息已处理",
	ErrMeleeChannelNoOnMicAccess:                         "无上麦权限",
	ErrDingCallIsAuditing:                                "申请审核中，请稍后再试",
	ErrDingCallPhoneUnvalid:                              "号码无效",
	ErrDingCallExceedChannelCnt:                          "超出人数上限",
	ErrDingCallTooOften:                                  "线路繁忙，请稍后再试",
	ErrDingCallUserHasAdd:                                "已加入该房间的闪电召集",
	ErrMeleeChannelApplySensitive:                        "您的申请内容不合规，请重新发起申请",
	ErrDingCallNotUse:                                    "功能维护中",
	ErrOnePieceBuyChanceLimit:                            "购买失败，请重试",
	ErrOnePieceUseChanceDailyLimit:                       "导航发现您今日航行过久，请勿疲劳驾驶",
	ErrOnePieceRemainChanceNotEnough:                     "剩余汽油不足",
	ErrOnePieceNotAvailable:                              "服务升级中，请耐心等待~",
	ErrOnePieceConfigFail:                                "配置错误",
	ErrOnePieceSeniorLotteryMultipleDisable:              "航道拥挤，暂无法航行~",
	ErrNoPiaPermission:                                   "暂无权限",
	ErrPiaIsRunning:                                      "请结束剧本再进行选择",
	ErrForbiddenMaskpk2pia:                               "参加蒙面PK期间无法切换到Pia戏",
	ErrPiaSticktimeOverlapped:                            "Pia戏房间置顶时间重叠",
	ErrPiaRoomTypeUnmatched:                              "Pia戏房间类型错误",
	ErrPiaRoomCommonErr:                                  "通用错误",
	ErrRapRespectCoolDown:                                "手速太快啦，俺受不了，歇一会再点吧",
	ErrNoMasterUid:                                       "你不满足申请条件，无法申请。可前往[签约管理页-问答帮助]了解相关信息（会长没有对公）",
	ErrExchangeDayApply:                                  "今天发送申请次数已达上限",
	ErrExchangeApplyScore:                                "你不满足申请条件，无法申请。可前往[签约管理页-问答帮助]了解相关信息（积分不满足）",
	ErrExchangeYearApply:                                 "你本年内的个人对公结算设置次数已达上限，无法申请",
	ErrExchangeLock:                                      "获取锁失败",
	ErrExchangeUidNotMatch:                               "签署用户不匹配",
	ErrExchangeTimeErr:                                   "未到提现开放时间",
	ErrExchangeIng:                                       "正在提现",
	ErrExchangeTypeNotSupport:                            "不支持的提现类型",
	ErrExchangeOrderFinished:                             "提现次数已达上限，下个提现开放时间再来吧～",
	ErrExchangeFlowIdNotMatch:                            "FlowId不匹配",
	ErrExchangeApplyStatus:                               "当前申请状态不允许",
	ErrExchangePlaceCount:                                "当前公会暂无个人对公结算申请名额，无法申请",
	ErrExchangeIsPublic:                                  "已经是对公状态",
	ErrExchangeApplyNotFinish:                            "上一个申请未完成",
	ErrExchangeFlowExist:                                 "签署流程已创建",
	ErrExchangeStatus:                                    "状态错误",
	ErrWhiteMaxExceed:                                    "超出最大值",
	ErrExchangeIsPrivate:                                 "已经是对私状态",
	ErrExchangeMaxRange:                                  "参数超出最大值",
	ErrExchangeNotMaster:                                 "没有对公主体",
	ErrExchangeMasterPlaceCount:                          "公会当前暂无个人对公结算申请余额，无法同意申请",
	ErrExchangeUndoneTransaction:                         "还有未结清的积分",
	ErrExchangeOrderNotExist:                             "提现编号不存在",
	ErrExchangeSumNotFinish:                              "正在提现中",
	ErrExchangeMasterNotReal:                             "会长没有实名认证",
	ErrExchangeGetScoreFailed:                            "积分获取失败",
	ErrExchangeGetGuildServer:                            "会长服务号ID不存在",
	ErrExchangeHasNoGuild:                                "你不满足申请条件，无法申请。可前往[签约管理页-问答帮助]了解相关信息（没有公会）",
	ErrMasterNotMaster:                                   "不是对公会长",
	ErrExchangeTtidNotExist:                              "暂无结果",
	ErrExchangeTransfer:                                  "您是对公合作的会长，请到公会经营管理后台发起提现",
	ErrLevelupDbErr:                                      "数据错误",
	ErrLevelupParamErr:                                   "参数错误",
	ErrLevelupParentNotExist:                             "父级礼物不存在",
	ErrLevelupExpErr:                                     "经验值错误",
	ErrLevelupLevelErr:                                   "等级值错误",
	ErrLevelupLevelItemErr:                               "礼物等级没有达到",
	ErrLevelupConfigErr:                                  "礼物配置错误",
	ErrLevelupVersionErr:                                 "礼物版本错误，请重新进入频道拉取最新版本",
	ErrListeningHaveCheckedIn:                            "今天已经成功打卡，明天再打卡吧",
	ErrListeningTokenExpired:                             "登录信息过期",
	ErrListeningErrorHttp:                                "HTTP接口报错",
	ErrListeningParamErr:                                 "参数错误",
	ErrListeningCtrlErr:                                  "操作失败，请稍后再试",
	ErrSettleDbErr:                                       "数据库错误",
	ErrSettleParamErr:                                    "参数错误",
	ErrSettleUploadErr:                                   "上传失败",
	ErrSettleChangeStatusErr:                             "状态变更失败",
	ErrSettleReceiptAssociateErr:                         "发票关联失败",
	ErrSettleUnknownBillTypeErr:                          "结算单类型错误",
	ErrSettleBillEmptyErr:                                "结算单数据为空",
	ErrSettleBillNotFoundErr:                             "结算单不存在",
	ErrSettleReceiptBillNotFoundErr:                      "发票单不存在",
	ErrSettleDetailNotFoundErr:                           "明细不存在",
	ErrSettleRecordMonthSettledErr:                       "录入已结算的月份",
	ErrSettleFileRepeatUploadErr:                         "文件重复上传",
	ErrSettleCreatingErr:                                 "结算单创建中",
	ErrSettleCommissionApiErr:                            "佣金平台接口错误",
	ErrSettleComNoIdentifyErr:                            "未完成实名认证",
	ErrSettleFrequentlyErr:                               "操作频繁，请稍后再试",
	ErrSettleComCardPayErr:                               "用户银行卡打款异常",
	ErrSettleComBankIncomplete:                           "银行卡信息不完整",
	ErrSettleComInsufficientBalanceErr:                   "佣金余额不足",
	ErrSettleFileNotFoundErr:                             "结算单文件不存在",
	ErrSettleNotAllowWithdrawErr:                         "当前无法提现",
	ErrSettleWithdrawOverLimitErr:                        "提现异常，请联系TTID80119",
	ErrSettleWithdrawFailedErr:                           "提现异常，请稍后再试",
	ErrSettleWithdrawRepeatErr:                           "存在处理中的提现，请稍后查看",
	ErrSettleWithdrawMinLimitErr:                         "提现金额不能小于1元",
	ErrSettleReceiptAmountMatchErr:                       "结算单金额与发票金额不匹配",
	ErrSettleReceiptVerifyErr:                            "发票查验错误",
	ErrSettleReceiptRepeatErr:                            "重复使用，已关联其他订单",
	ErrSettleReceiptItemErr:                              "发票项目名称错误",
	ErrSettleReceiptPurchaserErr:                         "发票购买方信息错误",
	ErrSettleReceiptSellerErr:                            "发票销售方信息错误",
	ErrSettleWithdrawRiskErr:                             "本次提现失败，提现账号异常，有疑问请联系客服或TTID80513",
	ErrSettleReceiptSysRetryErr:                          "系统错误，请稍后重试",
	ErrSettleWithdrawRiskInternal:                        "提现异常，请检查您的账号",
	ErrSettleWithdrawWithdrawnErr:                        "结算单已提现，请勿重复操作",
	ErrStarTrekRoundIsOver:                               "正在计算上期结果，请稍后~",
	ErrStarTrekInvestValLimit:                            "本轮投入值已达到上限，请耐心等待巡航结果~",
	ErrStarTrekInvestTooOften:                            "操作太快拉，请稍后重试~",
	ErrStarTrekInvestAddFail:                             "投入失败，请稍后重试~",
	ErrStarTrekInvestRollbackFail:                        "回滚失败",
	ErrStarTrekInvestDailyValLimit:                       "当日最多投入100000豆补给值",
	ErrStarTrekNotAvaliable:                              "系统维护中",
	ErrRhythmPermissionDenied:                            "权限不足",
	ErrRhythmBusinessLogicErr:                            "业务逻辑错误",
	ErrPersonalCertificationNotExist:                     "该认证已过期",
	ErrNotUpToStandard:                                   "未达标",
	ErrNoBindPhoneNumber:                                 "未绑定手机号",
	ErrJoinSocialCommunityFans:                           "社团核心成员席位已满，加入为普通成员",
	ErrSocialCommunityApplyToJoin:                        "需要申请加入社群",
	ErrRejectJoinSocialCommunity:                         "拒绝任何人加入社群",
	ErrSocialCommunityUserAlreadyJoin:                    "用户寂静加入社群",
	ErrSocialCommunityNoExist:                            "社团不存在",
	ErrSocialCommunityInvitationCodeIsValidInInit:        "资格审核中，请稍后再试",
	ErrSocialCommunityInvitationCodeNotValid:             "该邀请码已无效",
	ErrSocialCommunityInvitationCodeNotThis:              "此邀请码不属于你加入的第一个社团",
	ErrSocialCommunityInvitationCodeHitRisk:              "系统判定你的账号异常，提交失败",
	ErrMuseRolePlayUserCardIsNotExist:                    "用户角色卡不存在",
	ErrUkwOpFromDenied:                                   "神秘人期间不可进行该操作",
	ErrUkwOpToDenied:                                     "不可对神秘人进行该操作",
	ErrUkwOpCannotFollow:                                 "不可关注神秘人",
	ErrUserscoreMgrParamErr:                              "参数错误",
	ErrUserscoreMgrParamMaxLen:                           "查询个数超出最大范围",
	ErrUserscoreMgrDbErr:                                 "数据库错误",
	ErrUserscoreMgrTaskWait:                              "等待上一个任务完成",
	ErrAccountIsolationSwOpenRepeatedErr:                 "账号隔离开关不可以重复开启",
	ErrAccountIsolationSwInsertRepeatedErr:               "账号隔离配置不可以重复插入",
	ErrConcertBandMemberCannotStartWhenSinging:           "表演完当前这首歌才可以点歌噢",
	ErrConcertAudienceCannotStartWhenSinging:             "听乐队表演完当前这首歌才可以点歌噢",
	ErrConcertJoinFailed:                                 "系统错误，请重新尝试加入演奏",
	ErrGameTicketOrderExist:                              "订单已存在",
	ErrGameTicketConfigNotExist:                          "配置不存在",
	ErrGameTicketRemainNotEnough:                         "余额不足",
	ErrGameTicketTimeGapTooLarge:                         "系统时间差过大",
	ErrGameTicketRechargeBuyFail:                         "购买失败，请重试",
	ErrGameTicketRechargeUnder18:                         "未成年用户不允许购买",
	ErrGameTicketRechargePerLimit:                        "超过单次购买限制",
	ErrGameTicketRechargeDailyLimit:                      "今日购买太多啦！明天再来吧~",
	ErrCouponDailyLimit:                                  "您今日优惠券领取次数已达上限！明天再来吧~",
	ErrRevenueNameplateActivityOrderExist:                "营收活动订单已存在",
	ErrRevenueNameplateActivityDataErr:                   "营收活动数据库更新异常",
	ErrMinigameNumBombInvalidParam:                       "数字炸弹设置参数不合法,客户端将重新同步",
	ErrMinigameNumBombSettingInvalid:                     "数字炸弹设置参数不合法",
	ErrGrantFlowCardOrderExist:                           "订单已存在",
	ErrGrantFlowCardDatabaseErr:                          "数据库错误",
	ErrWishListGiftNotFound:                              "礼物不存在",
	ErrWishListGiftExpire:                                "你选择的礼物目前已下架，请重新选择心愿礼物~",
	ErrWishListLiveRepeat:                                "同一场直播只能设置一次心愿单",
	ErrWishListSensitiveWords:                            "你填写的心愿答谢语有违规词汇，请检查后重新填写",
	ErrWishListCommonErr:                                 "心愿单业务错误",
	ErrReconcileNotSupport:                               "对账配置不支持",
	ErrPgcAdventurePhraseErr:                             "阶段信息错误",
	ErrPgcAdventureNotYourTurn:                           "还没轮到你",
	ErrPgcAdventureNotHost:                               "仅主持人可操作",
	ErrPgcAdventureBeginAdventureFail:                    "参与用户不足以开启玩法哦～",
	ErrPgcAdventureHostCtrlFail:                          "主持控制失败",
	ErrPgcAdventureEnrollFail:                            "报名失败",
	ErrMijingUserRepeatedCommentSameScenarioErr:          "不可以对同个剧本重复评分哦~",
	ErrMijingInviteCountLack:                             "今日还可邀请%d个人",
	ErrMijingInviteExhaust:                               "今日邀请次数已达上限",
	ErrMijingSystemIsBeingMaintained:                     "系统正在维护中，请稍后重试~",
	ErrPerfectMatchCancelLimit:                           "您今日取消匹配次数过多，无法匹配。明日再来吧~",
	ErrPerfectMatchCancelOverMax:                         "超过每日取消匹配次数上限",
	ErrPerfectMatchRefreshQuestionNotEnough:              "更换次数已达上限，无法继续更换",
	ErrPerfectMatchSyatemErr:                             "服务异常，系统已取消匹配，金额已退款到对应账户中",
	ErrPerfectMatchInMatch:                               "正在进行匹配，无法重复发起",
	ErrPerfectMatchAlreadyAnswer:                         "已提交答案，无法重复操作",
	ErrPerfectMatchAlreadyGetQuestion:                    "已获取答案，无法重复操作",
	ErrPerfectMatchOrderNotExist:                         "天配订单不存在",
	ErrPerfectMatchNotInMatch:                            "已取消匹配，请退出重试",
	ErrPerfectMatchCommonErr:                             "天配业务错误",
	ErrPerfectMatchGamePublishCluesLimit:                 "发布线索达到上限",
	ErrPerfectMatchGameGetGameInfoFail:                   "获取游戏信息失败",
	ErrPerfectMatchGameCannotChangePhase:                 "无法切换游戏阶段",
	ErrPerfectMatchGamePlayerCannotBlowLight:             "玩家尚未达到爆灯条件",
	ErrPerfectMatchGameCluesPropUseLimit:                 "该道具已经使用过了",
	ErrPerfectMatchGamePlayerCanOnlyChooseOnce:           "只能选择一个心动对象",
	ErrPerfectMatchGameHoldMicFail:                       "无法上游戏麦位",
	ErrPerfectMatchGameNotStart:                          "玩法暂未开启",
	ErrPerfectMatchGameInitPlayersFail:                   "玩家信息初始化失败",
	ErrPerfectMatchGameSwitchSchemeFailLockChannel:       "房间已上锁，无法切换到天配房间，请手动解锁",
	ErrPerfectMatchGameSwitchSchemeFailStarting:          "游戏进行中，无法切换模式",
	ErrGuildCoopHasGuild:                                 "已加入公会",
	ErrGuildCoopApproveErr:                               "审批错误",
	ErrGuildCoopApplyLimit:                               "限制申请",
	ErrGuildCoopLimitErr:                                 "该账号处于申请流程中不能被限制，请先结束流程",
	ErrGuildCoopApplyRepeat:                              "已加入合作库",
	ErrGuildCoopStatusErr:                                "状态异常",
	ErrGuildCoopNotRealName:                              "未实名认证",
	ErrGuildCoopPhoneRegistered:                          "该手机号已注册TT，请前往APP申请",
	ErrGuildCoopNotFound:                                 "该公会未加入合作库",
	ErrGuildCoopSensitiveWords:                           "您输入的公会名称包含敏感词",
	ErrGuildCoopCreateGuildErr:                           "创建公会失败",
	ErrGuildCoopNotAllowDismiss:                          "请先退出公会合作库",
	ErrChanceGameNotAvailable:                            "系统维护中",
	ErrCatCanteenBuyChanceLimit:                          "购买量达到限制",
	ErrCatCanteenUseChanceLimit:                          "使用量达到限制",
	ErrCatCanteenChanceNotEnough:                         "鱼干余额不足",
	ErrCatCanteenEntryItemNotEnough:                      "背包道具不足，请前往上一关获取",
	ErrCatCanteenConfErr:                                 "配置有误",
	ErrCatCanteenLotteryDrawTooQuick:                     "操作太快了，请稍后再试吧",
	ErrCatCanteenOrderNotExist:                           "订单不存在",
	ErrRevenueExtGameMountDuplicate:                      "游戏已挂载",
	ErrRevenueExtGameBackstageOpFail:                     "配置操作失败",
	ErrRevenueExtGameNotPermission:                       "无权限",
	ErrRevenueExtGameMountFail:                           "游戏挂载失败",
	ErrRevenueExtGameConfNotExist:                        "游戏配置不存在",
	ErrRevenueExtGameCannotPlayOther:                     "互动游戏期间，不能开启其他玩法",
	ErrRevenueExtGameCannotOpenInPlaying:                 "请先关闭互动玩法直播~",
	ErrRevenueExtGameGameNotExist:                        "游戏不存在",
	ErrGloryGetRedPointFail:                              "获取红点信息失败",
	ErrGloryGetTaskInfoFail:                              "获取挑战任务信息失败",
	ErrGloryRewardTaskFail:                               "领取挑战任务奖励失败",
	ErrGloryFloatingLayerParam:                           "悬浮层参数错误",
	ErrGloryCelebrityWeekRankFail:                        "获取名流周榜失败",
	ErrGloryCelebrityPalaceInfoFail:                      "获取名流殿堂信息失败",
	ErrGloryCelebrityReplayNotFind:                       "该回顾不存在",
	ErrGloryLotteryOutOfPrestige:                         "声望不足",
	ErrGloryLotteryOutOfLotteryTimes:                     "领取次数不足",
	ErrInChannelCreatorBlackEnterErr:                     "暂不允许进入房间",
	ErrRpcDeprecatedErr:                                  "接口已废弃",
	ErrChannelViewIdUsed:                                 "房间ID已被使用,不能再被分配",
	ErrChannelViewIdInvalid:                              "房间ID不合法,不能被分配",
	ErrChannelUserOwnChannelExist:                        "用户个人房已存在，不能再创建",
	ErrChannelCreateNewChannelFailed:                     "创建新房间失败",
	ErrChannelEnterBusinessTokenGenErr:                   "进房token生成错误",
	ErrChannelEnterBusinessTokenCheckErr:                 "进房token检查错误",
	ErrChannelRecommendQuickEntryConfigErr:               "配置错误",
	ErrChannelRecommendRelationshipErr:                   "推荐关系链错误",
	ErrChannelRecommendTopOverlayErr:                     "顶部浮窗错误",
	ErrExchangeMiddlewareNotSupport:                      "不支持",
	ErrExchangeMiddlewareClientErr:                       "客户端转换错误",
	ErrExchangeMiddlewareMaxErr:                          "超出最大值",
	ErrExchangeMiddlewareOrderLenErr:                     "order长度超出最大值",
	ErrExchangeMiddlewareParamErr:                        "参数错误",
	ErrExchangeMiddlewareUidConssistentErr:               "Uid不一致",
	ErrExchangeMiddlewareBalanceNotEnoughErr:             "余额不足",
	ErrExchangeMiddlewareAppidNotExist:                   "APPID不存在",
	ErrExchangeMiddlewareSignErr:                         "签名错误",
	ErrExchangeMiddlewareLock:                            "无法获取锁",
	ErrChannelOperatePermissionNotSupport:                "不支持的操作",
	ErrChannelLiveMultiPkApplyCancelErr:                  "加入失败，邀请人已经取消了此次邀请",
	ErrChannelLiveMultiPkTeamMemFullErr:                  "当前的PK队伍已经满员了",
	ErrChannelLiveMultiPkApplyNoPkPermission:             "邀请失败，该主播暂无四人PK的权限",
	ErrChannelLiveMultiPkTeamCancelErr:                   "组队结束，四人PK的发起方取消了此次PK邀请～",
	ErrChannelLiveMultiPkTeamApplyingErr:                 "正在邀请好友加入队伍时不支持选择随机匹配哦，稍后试试吧～",
	ErrChannelLiveMultiPkApplyFail:                       "邀请失败",
	ErrRevenueAudioStreamStartMemberBusy:                 "房间繁忙",
	ErrChannelRankDbErr:                                  "数据库错误",
	ErrChannelRankCacheErr:                               "缓存错误",
	ErrChannelRankParseErr:                               "数据解析错误",
	ErrChannelRankVipLevelErr:                            "VIP等级错误",
	ErrChannelRankReadConfErr:                            "配置异常",
	ErrChannelRankOrderIdRepeat:                          "订单号重复",
	ErrMijingJingBadRequest:                              "无效的请求",
	ErrMijingJingDbError:                                 "数据库错误",
	ErrMijingUnderMaintenance:                            "功能正在维护中，请稍后试试～",
	ErrMijingFailedToReportChannelInviteTeam:             "邀请失败",
	ErrMijingInternalServerError:                         "服务内部错误",
	ErrMijingGenerateAvatarLimiteMaxErr:                  "啊哦，今日生成形象的次数已用完，请明天再来哦",
	ErrMijingGetEscapeTipMsgLimit:                        "Ta暂时离开了哦~明天再来吧~",
	ErrTreasureHouseActivityNotValid:                     "当前不在珍宝馆活动期",
	ErrTreasureHouseClaimNotValid:                        "无法领取该权限",
	ErrTreasureHouseNoPrivilege:                          "没有该礼物权限",
	ErrTreasureHouseConditionTimeChange:                  "条件发放阶段不允许修改时间",
	ErrTreasureHouseActivityNotFound:                     "活动不存在",
	ErrTreasureHouseWhiteListErr:                         "白名单错误",
	ErrUserRecallNoAllow:                                 "该用户已回归，不可再召回喔",
	ErrUserRecallSmsSend:                                 "已经发过短信啦，试试其他方式吧~",
	ErrUserRecallParams:                                  "参数错误",
	ErrUserRecallStatus:                                  "状态错误",
	ErrVirtualAnchorNotAppLiveErr:                        "虚拟形象开播中~请下播后再试",
	ErrUserRecallRecordNotFound:                          "未找到召回记录",
	ErrUserRecallRecordInvalidAwardCfg:                   "奖励配置错误",
	ErrUserRecallRecordNotMeetCondition:                  "不满足领奖条件",
	ErrTtPeripheralMallDbErr:                             "DB错误",
	ErrTtPeripheralMallStockNotEnough:                    "当前库存不足，可选择减少商品个数噢~",
	ErrTtPeripheralMallProductNotFound:                   "商品不存在",
	ErrTtPeripheralMallLimit:                             "购买上限",
	ErrIllustrationNotFound:                              "未知图鉴",
	ErrIllustrationParamErr:                              "参数错误",
	ErrIllustrationDbErr:                                 "数据库错误",
	ErrIllustrationGetErr:                                "获取礼物图鉴信息错误",
	ErrGloryMagicOutOfPrestige:                           "荣耀星钻不足",
	ErrChannelMicLayoutInnerErr:                          "内部错误",
	ErrChannelMicLayoutSwitchCloseErr:                    "房间麦位布局功能已关闭",
	ErrChannelMicLayoutSwitchCurTimePresentErr:           "当前在时间礼物播放中，不能切换",
	ErrChannelMicLayoutSwitchTemplateInvalidErr:          "布局模板无效",
	ErrChannelMicLayoutParamInvalid:                      "参数不合法",
	ErrChannelMicLayoutMgrInnerErr:                       "布局管理内部错误",
	ErrChannelMicLayoutMgrTemplateNotExist:               "布局模板不存在",
	ErrChannelMicLayoutMgrTemplateInvalid:                "布局模板参数无效",
	ErrChannelMicLayoutMgrGetLockFailed:                  "获取锁失败",
	ErrChannelMicLayoutUnsupportSchemeDetailType:         "当前在不支持的房间玩法中，不能切换布局",
	ErrChannelMicLayoutSwitchIsSameMicLayout:             "当前已在该布局中，无需切换",
	ErrChannelMicLayoutSwitchFreqLimitErr:                "切换布局过于频繁，请稍后再试~",
	ErrChannelMicLayoutMgrLayoutNotExist:                 "布局不存在",
	ErrChannelMicLayoutMgrLayoutNotChange:                "布局未发生变化",
	ErrPlatformExamStartStatusErr:                        "当前考试未配置试题或配置试题数量小于5，请重新检查",
	ErrPlatformExamNotFound:                              "试卷不存在",
	ErrPcLfgNotSupportChannel:                            "不支持的房间",
	ErrPcLfgInvalidShortcutNumber:                        "至少保留两个便捷工具哦",
	ErrTcyNotSupportChannel:                              "不支持的房间类型",
	ErrTcyKickOutChannelErr:                              "踢出其他房间失败",
	ErrChannelMicAudioUserChannelNotCreated:              "用户未创建个人房",
	ErrChannelMicAudioBitRateUpgradeBudgetRanOut:         "今日码率调整次数已用完",
	ErrChannelMicAudioBitRateConfigNotExist:              "不存在要升级的码率配置",
	ErrChannelMicAudioBitRateUserAlreadyUpgraded:         "房间已经处于高码率",
	ErrApiUnimplement:                                    "接口未定义",
	ErrTimelineDataOverFlowErr:                           "timeline返回包过大",
	ErrHttpUnmarshalError:                                "HTTP解析错误",
	ErrHttpParaError:                                     "HTTP参数错误",
	ErrErrFuseValid:                                      "服务繁忙，请稍后再试",
	ErrBizBaseSysErr:                                     "系统错误",
	ErrRevenueSvrErr:                                     "// 营收系统错误",
	ErrRealnameNeedFaceAuthBeforeConsume:                 "消费前需要进行人脸认证",
	ErrRealnameNeedRealnameBeforeConsume:                 "消费前需要进行人脸认证，请先进行实名认证",
	ErrRealnameFaceAuthFailBeforeConsume:                 "消费前需要进行人脸认证，人脸验证失败",
	ErrGroupAnnouncementCheckingErr:                      "已有公告正在审核中,请稍后再试",
	ErrGroupAnnouncementCountLimitErr:                    "公告数量已满，请先删除已有公告",
	ErrGroupAnnouncementWordLimitErr:                     "公告字数已超过限制",
	ErrGroupAnnouncementUpdateNotFoundErr:                "该公告已被修改或删除",
	ErrGroupAnnouncementSensitive:                        "输入的内容包含敏感词",
	ErrGroupJoinCheckErr:                                 "回答错误",
	ErrGroupJoinCheckJoinTypeCheck:                       "群验证方式已经被修改",
	ErrGroupJoinCheckSensitiveErr:                        "您的设置涉及敏感词汇，请重新设置",
	ErrFellowAddRelationshipFail:                         "添加关系配置失败",
	ErrFellowUpdateRelationshipFail:                      "更新关系配置失败",
	ErrFellowDeleteRelationshipFail:                      "删除关系配置失败",
	ErrFellowAddChannelRelationshipFail:                  "添加房间下发关系配置失败",
	ErrFellowUpdateChannelRelationshipFail:               "更新房间下发关系配置失败",
	ErrFellowDeleteChannelRelationshipFail:               "删除房间下发关系配置失败",
	ErrFellowGetUserNameplateFail:                        "无法获取用户的铭牌",
	ErrFellowBoxRareListUkwBan:                           "对方正开启神秘人身份，暂不可查看关系",
	ErrRoleplayRoleNameAuditing:                          "角色名称审核中",
	ErrRoleplayRoleSetTooMany:                            "修改次数过多，请稍后再试",
	ErrUgcVoteCreator:                                    "投票发起人不可参与投票哦",
	ErrUgcVoteExpired:                                    "投票已结束",
	ErrUgcVoteVoted:                                      "已经投过票啦，不能再投啦",
	ErrPiaOrderDramaListAdd:                              "点本失败",
	ErrPiaOrderDramaListGet:                              "获取点本列表失败",
	ErrPiaOrderDramaListDelete:                           "删除指定的点本记录失败",
	ErrPiaMicRoleMapSelectRole:                           "选择角色失败",
	ErrPiaMicRoleMapCancelRole:                           "取消选择角色失败",
	ErrPiaChannelDramaSelect:                             "选本失败",
	ErrPiaChannelDramaOperate:                            "走本操作失败",
	ErrPiaChannelDramaGetStatus:                          "获取房间走本详情失败",
	ErrPiaChannelBgmOperate:                              "BGM操作失败",
	ErrPiaCrateCopyFail:                                  "创建副本失败",
	ErrPiaCrateCopyFailForLimitation:                     "创建副本失败，暂停过bgm的剧本不能生成副本",
	ErrPiaChannelBgmVolOperate:                           "BGM音量操作失败",
	ErrPiaChannelChangePlayingType:                       "切换走本方式失败",
	ErrPiaSystemIsNotAvailable:                           "pia戏玩法维护中",
	ErrPiaGetMyPlayingRecord:                             "查询我的参演记录失败",
	ErrChannelboxAdminPermCanceled:                       "您已被取消管理员权限",
	ErrChannelboxMicNotEnough:                            "可分配麦位数不足",
	ErrChannelboxBoxInUse:                                "子频道使用中，请稍后操作",
	ErrChannelboxBoxNotExists:                            "子频道不存在，请刷新",
	ErrChannelboxMicInUse:                                "麦位使用中，请稍后创建",
	ErrChannelboxBoxNameReview:                           "子频道名称审核中",
	ErrChananelboxMicAllocBusy:                           "麦位分配繁忙，请重试",
	ErrSingingHallAuditFail:                              "不符合平台审核规范,无法设置名称",
	ErrSingingHallAuditPartSuccess:                       "有部份歌曲上传失败，请重试",
	ErrChannelEnterControlWhiteListInterceptor:           "当前不在进房名单内，请升级版本查看",
	ErrPgcChannelPkNotApplicable:                         "对方房间当前不符合pk条件",
	ErrPgcChannelPkGuildidError:                          "ID输入错误，请重试",
	ErrPgcChannelPkLimitMultiInvite:                      "已向其他厅发起pk，请耐心等待回应",
	ErrPgcChannelPkInviteHadRefuse:                       "对方已拒绝您的pk邀请。5分钟内无法再向该厅发起第二次pk",
	ErrPgcChannelPkLimitPkError:                          "该厅已到达pk限额了哦，请稍后再试",
	ErrPgcChannelPkLimitMultiPkFrom:                      "正在PK中",
	ErrPgcChannelPkHadOtherInvite:                        "已向其他厅发起pk",
	ErrPgcChannelPkOurPkSwitchClose:                      "请联系房间房主或超管开启pk权限",
	ErrPgcChannelPkLimitPkWhenMaskedPk:                   "蒙面pk期间，无法发起pk",
	ErrPgcChannelPkInvalidParam:                          "参数有误",
	ErrPiaBatchDeleteMyPlayingRecord:                     "批量删除我的参演记录失败",
	ErrPiaAddDramaFeedbackFail:                           "新增剧本反馈失败",
	ErrPiaDramaCopyUnsupported:                           "副本不支持此操作",
	ErrPiaBusinessFail:                                   "业务处理失败",
	ErrPiaMicFollowFail:                                  "麦位跟随失败",
	ErrChannelOpenGameGameIdEmpty:                        "对局信息缺失",
	ErrChannelOpenGameTmpChnCannotSetMode:                "临时房不允许修改模式",
	ErrChannelOpenGameJoystickVerifyFail:                 "找不到对局位",
	ErrChannelOpenGameLoadSeqMismatch:                    "无法处理:对局检查失败",
	ErrChannelOpenGameNoPermission:                       "无法处理:权限检查失败",
	ErrChannelOpenGameStatusNotPrepare:                   "无法处理:对局已经开始",
	ErrChannelOpenGamePlayerAreadyFull:                   "无法处理:对局人数已满",
	ErrChannelOpenGameUserNotJoined:                      "无法处理:用户状态不满足",
	ErrChannelOpenGamePlayerNumberLmtErr:                 "无法处理:不能开始对局",
	ErrChannelAudioTokenNoPermission:                     "音频连接失败，请退房重试",
	ErrPushTokenNoPermission:                             "没有申请pushtoken权限",
	ErrPgcChannelGameNoStartGamePermission:               "您暂无开启甩雷玩法的权限～",
	ErrPgcChannelGameUnderversion:                        "版本过低",
	ErrPgcChannelGameGameplayIncompatibility:             "当前不可开启甩雷玩法",
	ErrPgcChannelGameNotEnoughPlayer:                     "至少有2名参与用户才可开启玩法哦～",
	ErrPgcChannelGame_RequireOnHostMic:                   "请坐上主持麦后开启",
	ErrOfferingRoomAppliedListAlreadyApply:               "已经申请过了",
	ErrOfferingRoomAppliedListFull:                       "申请列表已满",
	ErrOfferingRoomConfigOutDate:                         "配置过期",
	ErrOfferingRoomSettlePhaseTop1OutDate:                "第一名不匹配",
	ErrOfferingRoomSwitchSchemeNotAllow:                  "拍卖过程中不可切换其他房间模版",
	ErrOfferingRoomHostMissing:                           "因10分钟内无人主持，本轮拍卖取消",
	ErrOfferingRoomNotFound:                              "通用错误码，查询的信息不存在",
	ErrOfferingRoomTShellFail:                            "自定义的关系名称机审不通过",
	ErrOfferingRoomOrderSnInvalid:                        "订单SN无效",
	ErrOfferingRoomOrderCommitFail:                       "订单确认失败",
	ErrOfferingRoomOrderRollbackFail:                     "订单回滚失败",
	ErrOfferingRoomCommonError:                           "通用错误",
	ErrOfferingRoomLockError:                             "上锁失败",
	ErrChannelGameMutualCommon:                           "通用错误",
	ErrChannelGameMutualRegister:                         "注册失败",
	ErrChannelGameMutualUnregister:                       "注销失败",
	ErrChannelMsgApiParamsInvalid:                        "推送参数无效",
	ErrChannelMsgApiTooManyRecvUser:                      "接收用户数量过多",
	ErrChatBotAiPartnerNotFound:                          "角色不存在",
	ErrChatBotAiRoleNotFound:                             "角色不存在，和其他角色聊聊吧",
	ErrChatBotAiPartnerExists:                            "已拥有自己的树洞",
	ErrChatBotInvalidMsgType:                             "不支持发送的消息类型",
	ErrChatBotInvalidMsgContent:                          "消息中包含敏感内容",
	ErrChatBotAiPartnerInSilence:                         "AI伴侣沉默中",
	ErrChatBotChangeRoleReachLimit:                       "修改树洞形象次数达到上限",
	ErrChatBotCreateAiRoleLimit:                          "创建的角色达到上限",
	ErrChatBotCreateAiPartnerLimit:                       "聊过的伴侣达到上限",
	ErrChannelMicNameMicIdInvalid:                        "麦位ID非法",
	ErrChannelMicNameUpdateDataTooOld:                    "麦位名称数据版本号太老",
	ErrChannelMicNameSwitchOff:                           "自定义麦位名称开关未打开，无法修改数据",
	ErrChannelMicNameReqInvalid:                          "麦位名称请求参数无效",
	ErrChannelMicNameGetLockFailed:                       "其他人正在修改麦位名称中，请稍后再试",
	ErrChannelSchemeDetailTypeNotPermitSwitch:            "该玩法类型不能互相切换",
	ErrChannelLevelReqInvalid:                            "房间等级请求参数无效",
	ErrChannelLevelNotInWhiteList:                        "房间不在白名单内",
	ErrChannelMicReqInvalid:                              "房间麦位请求参数无效",
	ErrChannelMicGetLockFailed:                           "房间麦位获取锁失败",
	ErrChannelMicIdNotExist:                              "房间麦位不存在",
	ErrChannelMicModeInvalid:                             "无效的麦位模式",
	ErrChannelMicExpandNotAllowed:                        "房间麦位列表不允许扩展",
	ErrChannelMicParseFromStringFailed:                   "房间麦位数据解析错误",
	ErrChannelMicOperatorInfoInvalid:                     "操作者当前麦位信息有误",
	ErrChannelMicTargetNotAllowed:                        "目标麦位不允许操作",
	ErrChannelMicUnexpectedDbResponse:                    "房间麦位数据库数据异常",
	ErrChannelMicParseFieldFailed:                        "解析麦位字段错误",
	ErrChannelMicMiddleSourceIllegal:                     "操作来源非法",
	ErrChannelMsgExpressReqInvalid:                       "请求参数无效",
	ErrChannelMsgExpressPermissionDeny:                   "无发送该房间消息权限",
	ErrChannelMsgExpressUnsupportedDuplicateRule:         "不支持的duplicate规则类型",
	ErrChannelMsgExpressSerializationFailed:              "数据序列化出错",
	ErrRealnameForbidBeforeConsume:                       "账号异常，请联系客服",
	ErrGamePalCardNotFound:                               "游戏搭子卡不存在",
	ErrGamePalCardCreateLimitExceed:                      "创建游戏搭子卡超过上限",
	ErrGamePalCardPublishCrowdLimit:                      "发布游戏搭子卡人群限制",
	ErrGamePalCardInReview:                               "游戏搭子卡审核中",
	ErrGamePalCardLightenTimeLimit:                       "游戏搭子卡擦亮过于频繁",
	ErrGamePalCardLightenFreqLimit:                       "游戏搭子卡擦亮次数超出当日限制",
	ErrGamePalTabNotFound:                                "服务出现问题",
	ErrGamePalCardInvalidState:                           "错误的游戏搭子卡状态",
	ErrGamePalCardInvalidAuditState:                      "错误的游戏搭子卡审核状态",
	ErrGamePalCardSocialDeclTooOrdinary:                  "您的宣言太普通啦，试试描述一下你想要找的ta是怎么样的吧",
	ErrGamePalCardEditCardAgain:                          "请重新编辑您的搭子卡信息",
	ErrGamePalSuperPublishFreqLimit:                      "不可频繁使用，晚一点再来吧",
	ErrGamePalSuperPublishRcmdUserListEmpty:              "555555搭子太少了，稍后再试试吧",
	ErrGamePalSuperPublishInvalidMsgContent:              "请重新选择打招呼消息",
	ErrGamePalAssistantClickPush:                         "操作太频繁啦",
	ErrPresentWeekCardNotFound:                           "周卡配置不存在",
	ErrPresentWeekCardNotAvailable:                       "暂不可购买",
	ErrPresentWeekCardDuplicateBuy:                       "请勿重复购买",
	ErrPresentWeekCardSysErr:                             "系统错误",
	ErrPresentWeekCardReceiveFailExpired:                 "领取失败，奖励已过期",
	ErrChannelOpenGameControllerHttpBusinessFail:         "无法处理:开始对局失败",
	ErrChannelOpenGameControllerInterruptReject:          "游戏进行中不能操作哦~",
	ErrEsportsOrderCommonError:                           "订单域通用错误",
	ErrEsportsRoleApplyRecordNotFound:                    "找不到申请记录",
	ErrEsportsRoleApplyAlready:                           "已提交身份申请",
	ErrEsportsRoleSystemError:                            "系统错误",
	ErrEsportsRoleUserInApplyBlacklist:                   "您的账号已被冻结，请%s后再申请",
	ErrEsportsOrderNotExist:                              "订单不存在",
	ErrEsportsOrderIsDeleted:                             "订单已删除",
	ErrEsportsOrderStatusUpdated:                         "订单状态已更新，请刷新后重试",
	ErrEsportsOrderRefundTshell:                          "不符合平台审核规范，请重新填写",
	ErrEsportsOrderCannotPay:                             "不可下单",
	ErrEsportsOrderCannotPayCoachRoleReclaimed:           "电竞指导身份被回收，无法下单",
	ErrEsportsOrderPayRemarkReviewReject:                 "您填写的订单备注不符合平台审核规范，请重新修改",
	ErrEsportsOneKeyFindStatusNotMatch:                   "一键找人状态不匹配(已完成、已超时、已取消)",
	ErrEsportsSkillReject:                                "您填写的大神技能内容不符合平台审核规范，请重新修改",
	ErrEsportsSkillNotFound:                              "您的技能不存在",
	ErrEsportsSkillCommonErr:                             "大神技能信息错误",
	ErrEsportSkillFreeze:                                 "您的技能已被冻结，请在冻结时间结束后再开启",
	ErrEsportHallGameCardInfoInvalid:                     "审核不通过",
	ErrEsportsHallNoReceiveTime:                          "当前不在接单时段，可私聊大神沟通时间哦～",
	ErrEsportsHallInviteOrderInvalid:                     "邀请已失效",
	ErrEsportsHallCantReceiveOrder:                       "您当前不可接单",
	ErrEsportsHallRiskControl:                            "风控拦截",
	ErrEsportsHallBlacklist:                              "黑名单拦截",
	ErrEsportHallProductNotFound:                         "大神已停止接单该技能",
	ErrEsportHallCantQuickReceiveOrder:                   "您当前不可快速接单",
	ErrEsportHallNeedSubWxgzh:                            "需要绑定微信公众号",
	ErrEsportHallGuaranteeSwitchNeedOn:                   "包赢开关未打开",
	ErrEsportHallGameCardNotFound:                        "游戏卡片已删除",
	ErrEsportsScoreDuplicateOrder:                        "重复电竞积分订单",
	ErrEsportsScoreInsufficientPoint:                     "电竞积分不足",
	ErrEsportsCustomerNotFound:                           "找不到客服",
	ErrChannelAudioViolationJudgmentNotExist:             "审判不存在",
	ErrChannelAudioViolationJudgmentAlreadyClose:         "审判已经关闭",
	ErrChannelAudioViolationUserJudgmentTooOften:         "对同一用户发起审判过于频繁",
	ErrChannelAudioViolationChannelJudgmentTooOften:      "同一房间内发起审判过于频繁",
	ErrChannelAudioViolationJudgmentAlreadyAppeal:        "您已提交过申诉",
	ErrChannelAudioViolationAudioFileAlreadySet:          "该音频文件已经上报过",
	ErrChannelAudioViolationDetectDisable:                "该用户或房间目前不需要进行违规检测",
	ErrChannelAudioViolationUserImmediatePass:            "该用户不需要审判，直接放过",
	ErrChannelAudioViolationUserImmediatePunish:          "该用户不需要审判，直接处罚",
	ErrChannelAudioViolationJudgmentSwitchOff:            "审判开关已关闭",
	ErrChannelAudioViolationJudgmentUserAlreadyVote:      "该用户已经投过票",
	ErrChannelAudioViolationUserJudgmentCountReachMax:    "该用户进房后的审判次数到达限制",
	ErrChannelAudioViolationChannelTypeNotUgc:            "房间类型非ugc",
	ErrChannelAudioViolationSampledAudioNotAllowSave:     "该抽样音频文件不允许保存",
	ErrSetGuildChannelAdminMustContract:                  "不是公会的签约用户,不可升为管理员",
	ErrEsportGodLevelSysErr:                              "大神等级系统错误",
	ErrRevenueAdSysErr:                                   "营收广告系统错误",
	ErrUserRateImageSelfRateLimitExceed:                  "今天的次数用完啦，明天再来试试吧",
	ErrUserRateImageGameBindConfigNotExist:               "游戏交友战力配置出问题啦...",
	ErrUserRateImageGameBindConfigChange:                 "抱歉交友战力评估系统出问题，请重测",
	ErrSuperPlayerPrivilegeFreqErr:                       "操作频繁，请稍后重试",
	ErrSuperPlayerPrivilegeNoSvip:                        "未开通SVIP",
	ErrSuperPlayerPrivilegeNoImRemain:                    "无剩余搭讪次数",
	ErrSuperPlayerPrivilegeStealthOverLimit:              "本周隐身次数达到上限",
	ErrSuperPlayerPrivilegeStealthExpired:                "隐身已失效",
	ErrSuperPlayerPrivilegeStealthGodKing:                "神王无法使用隐身特权",
	ErrEsportsCommonErr:                                  "电竞相关通用错误",
	ErrAigcSoulmateShareKeyExpired:                       "分享已过期",
	ErrAigcSoulmateShareKeyReachMaxUser:                  "使用人数已达上限",
	ErrAigcSoulmateSharePermissionDenied:                 "没有访问权限",
	ErrAigcSoulmateShareReachDailyLimit:                  "今日分享次数已用完",
	ErrAigcSoulmateShareRoleNotFound:                     "分享角色已过期",
	ErrAigcSoulmateShareKeyNotFound:                      "分享不存在",
	ErrAigcSoulmateInvalidLikeState:                      "非法的点赞状态",
	ErrAigcSoulmateInteractiveGameNotFound:               "不存在的互动玩法",
	ErrAigcSoulmateCreateInteractiveGameLimit:            "创建的互动玩法达到上限",
	ErrAigcSoulmateCannotExposeInteractiveGame:           "该互动玩法不推荐热门",
	ErrAigcSoulmateNoSentence:                            "句数消耗完毕啦",
	ErrEsportsAdminSysInternalErr:                        "赛事管理系统内部错误",
	ErrEsportsAdminTenantIdInvalid:                       "租户ID异常",
	ErrEsportsAdminTenantRoleNotExist:                    "租户角色不存在",
	ErrEsportsAdminTenantAccountBindError:                "该租户id已绑定对应的账号",
	ErrEsportsAdminTenantAccountSysAdminDelNotAllow:      "超级管理员不能删除",
	ErrEsportsAdminGameEventNotExist:                     "赛事信息不存在",
	ErrEsportsAdminGameSettleLockExist:                   "赛事还在结算中，请稍等",
	ErrEsportsAdminGameStatusErr:                         "赛事状态错误",
	ErrEsportsAdminGameRecordHaveNegative:                "比赛击杀结果异常，请核对",
	ErrEsportsAdminGameKillNumGt100:                      "总击杀数不能大于100",
	ErrEsportsAdminGameRepeatSaveGameRecord:              "赛事战绩已录入，请勿重复操作",
	ErrEsportsAdminGetSsoInfoErr:                         "获取登录身份异常",
	ErrEsportsAdminGetUserInfoErr:                        "获取用户信息异常",
	ErrEsportsAdminAccountIsNull:                         "工号不存在",
	ErrEsportsAdminSsoTokenIsNull:                        "sso token不能为空",
	ErrEsportsAdminSsoTokenErr:                           "sso token失效",
	ErrEsportsAdminCloudPhoneNotExist:                    "云手机不存在",
	ErrEsportsAdminCloudPhoneStatusNotAllowed:            "云手机状态不允许该操作",
	ErrEsportsAdminCloudPhoneNotAvailable:                "无可用的云手机",
	ErrEsportsAdminQiniuNotAuth:                          "七牛云的ak与sk未授权",
	ErrEsportsAdminQiniuGetUploadTokenFail:               "获取七牛云的uploadToken失败",
	ErrEsportsAdminAppidAuthFail:                         "appid鉴权失败",
	ErrEsportsMatchPlatformSysInternalErr:                "赛事平台系统内部错误",
	ErrEsportsMatchPlatformSignErr:                       "签名异常",
	ErrEsportsMatchPlatformStateLockExist:                "获取赛事状态锁失败",
	ErrEsportsMatchPlatformGameEventNotExist:             "赛事信息不存在",
	ErrEsportsMatchPlatformGameEventDstStatusCanNotReach: "无法更新赛事状态到目标状态",
	ErrEsportsMatchPlatformGameAlreadyExist:              "赛事已存在",
	ErrEsportsMatchPlatformParamErr:                      "请求参数异常",
	ErrEsportsAdminTenantAccountPermissionErr:            "账号未绑定租户权限",
	ErrEsportsAdminCloudDeviceNotFound:                   "该设备不存在",
	ErrEsportsAdminCloudDeviceGetTokenErr:                "该设备不存在",
	ErrStarTrainAdminConfCommonErr:                       "摘星列车配置后台通用错误",
	ErrInviteRoomInviteLimit:                             "今天已经邀请很多人了，明天再来吧",
	ErrInviteRoomLastInviteNotExpired:                    "上次邀请还未过期，请稍后再试",
	ErrInviteRoomUserNotInChannel:                        "只有在房间内才能发出邀请",
	ErrInviteRoomInviteRecordNotExist:                    "邀请记录不存在或已失效",
	ErrGameAppointmentNotEffect:                          "该邀请已失效或不存在",
	ErrReachMaxOnlineSetCount:                            "提醒人数已达到上限",
	ErrGameUserRateRisk:                                  "评价风控限制仅自己可见",
	ErrVirtualAvatarNotFound:                             "不存在的形象",
	ErrVirtualAvatarNotInUse:                             "用户没有使用中的虚拟形象",
	ErrVirtualAvatarExpired:                              "虚拟形象已过期",
	ErrVirtualAvatarOrderidExist:                         "虚拟形象订单已存在",
	ErrVirtualAvatarHaveNotRelation:                      "该形象没有绑定的双人关系",
	ErrVirtualImageOrderNotExist:                         "订单不存在",
	ErrVirtualImageCommodityNotExist:                     "商品已下架，无法购买",
	ErrVirtualImageTotalPriceLimit:                       "超过购买价格限制",
	ErrVirtualImageCommodityConfigChange:                 "商品信息发生更新，请重新核对确认",
	ErrVirtualImageCommodityDuplicateBuy:                 "无法重复购买",
	ErrVirtualImageRelationErr:                           "双人关系绑定或使用错误",
	ErrVirtualImageSetDisplaySwitchFail:                  "设置外显开关失败",
	ErrVirtualImageRelationUseFailReflash:                "双人关系展示失败",
	ErrVirtualImageCommodityNotBuy:                       "商品不可购买",
	ErrVirtualImageCommodityPackageNotExist:              "套餐已下架",
	ErrVirtualImagePackageAlreadyOnShelf:                 "套餐正在上架中 deprecated",
	ErrFellowHouseBuyHouseFailConditionCheck:             "该挚友不符合小屋购买条件，请选择其他挚友",
	ErrFellowHouseOrderNotExist:                          "订单不存在",
	ErrFellowHouseBuyHouseFail:                           "购买失败，请重试",
	ErrFellowHouseOrderExist:                             "订单已存在",
	ErrFellowHouseCannotUseHouse:                         "无法使用小屋",
	ErrFellowHouseRecoveryHouseAlreadyExpired:            "回收的小屋已过期",
	ErrPresentSetCommonErr:                               "礼物套装系统错误",
	ErrPresentSetNotFound:                                "礼物套装不存在",
	ErrPresentSetEmperorNotFound:                         "帝王套不存在",
	ErrPresentSetEmperorNotMatch:                         "套装礼物与关联帝王套礼物不匹配",
	ErrPresentSetPresentNotFound:                         "礼物不存在",
	ErrChannelGiftPkNotEntry:                             "无权限参与",
	ErrChannelGiftPkCannotMatch:                          "当前非玩法开启时间哦~",
	ErrChannelGiftPkOrderNotExist:                        "订单不存在",
	ErrChannelGiftPkCommonErr:                            "礼物对决系统错误",
	ErrNewRechargeActNotInActivityTime:                   "当前首充活动已结束~",
	ErrChannelMemberRankCommonErr:                        "爱意榜系统错误",
	ErrChannelOnlineRankCommonErr:                        "在线榜系统错误",
	ErrVirtualImageCommonErr:                             "虚拟形象通用错误",
	ErrGuildManageCommonErr:                              "会长经营后台通用错误",
	ErrGuildManageFreqErr:                                "操作频繁，请稍后重试",
	ErrCommonLogicOperateTooOften:                        "操作太频繁，频率限制",
	ErrChannelExtGameAwardOrderExists:                    "订单已存在",
	ErrChannelExtGameGameAppidInvalid:                    "无效的AppId",
	ErrChannelExtGameJsCodeInvalid:                       "无效的JsCode",
	ErrChannelExtGameGetJscodeFail:                       "获取JsCode失败",
	ErrChannelExtGameJoinRoomFail:                        "加入房间失败",
	ErrUgcCommunityPostNotFound:                          "帖子不存在",
	ErrUgcCommunityPostRoleCannotAssociated:              "角色不可被关联",
	ErrUgcCommunityPostInvalidCensorResult:               "错误的审核状态",
	ErrUgcCommunityCommentContentRisk:                    "评论内容未通过审核，请重新编辑",
	ErrUgcCommunityCommentRootDelete:                     "首评论已被删除，不可再回复",
	ErrUgcCommunityCommentReplyDelete:                    "回复评论已被删除，不可再回复",
	ErrUgcCommunityTopicNotFound:                         "话题不存在",
	ErrUgcCommunityTopicExists:                           "话题已存在",
	ErrChannelWeddingMinigameCommonErr:                   "婚礼房小游戏通用错误",
	ErrChannelWeddingChairGamePlayerNotEnough:            "玩家人数不足3人不支持开启游戏",
	ErrChannelWeddingPlanCommonError:                     "婚礼策划通用错误",
	ErrChannelWeddingOrderNotExist:                       "订单不存在",
	ErrChannelWeddingStatusNotMatch:                      "状态不匹配,请重试",
	ErrChannelWeddingReserveDuplicate:                    "重复预约",
	ErrChannelWeddingReserveConflict:                     "预约时间冲突",
	ErrChannelWeddingStageSwitchFail:                     "阶段切换失败",
	ErrChannelWeddingHallStatusChanged:                   "预约观礼失败～",
	ErrChannelWeddingConsultGnobilityIntercept:           "贵族拦截",
	ErrAigcGroupJoinedGroupReachedLimit:                  "加入群聊数量达到上限",
	ErrAigcGroupGroupNotFound:                            "群聊不存在",
	ErrAigcGroupTemplateNotFound:                         "群聊配置不存在",
	ErrAigcGroupGroupExisted:                             "群聊已存在",
	ErrAigcGroupTemplateSexMismatch:                      "性别不匹配",
	ErrAigcGroupMatchFailed:                              "匹配失败，再试试吧",
	ErrAigcGroupMultiGroupDismiss:                        "长时间无消息，该群已解散",
	ErrAigcIntimacyValueAdded:                            "重复增加亲密值",
}

// MessageFromCode get message associated with the code
func MessageFromCode(code int) string {
	if m, ok := CodeMessageMap[code]; ok {
		return m
	}

	return ""
}
