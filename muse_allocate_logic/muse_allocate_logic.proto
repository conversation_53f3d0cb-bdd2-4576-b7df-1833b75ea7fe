// 文档地址： https://q9jvw0u5f5.feishu.cn/docx/FTkkd4ZdKoUXEvxThq9cdMgMnCX
syntax = "proto3";

package ga.muse_allocate_logic;

import "ga_base.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/muse-allocate-logic";

message GetMuseAllocateInfoReq {
  ga.BaseReq base_req = 1;
  uint32 scene = 2; // 场景
  int32 ui_style = 3;  //0--旧的 1--新的
}

message GetMuseAllocateInfoResp {
  ga.BaseResp base_resp = 1;
  oneof info{
    MuseAllocateChannel channel = 2;
    MuseAllocateChannelV2 channel_v2 = 4;
    MuseAllocateNonEnterChannelInfo new_ui = 5;
  };
  uint32 interval = 3; // 间隔（s）
}

message MuseAllocateChannel {
  string title = 1; // 抬头信息
  MuseAllocateChannelInfo channel_info = 2; // 房间信息
  repeated MuseAllocateUser user_list = 3; // 麦上用户
  MuseAllocateUser reunion_user = 4; // 重逢用户
  string background_img_url = 5;
  string mate_id = 6;
}

message MuseAllocateChannelInfo {
  uint32 id = 1;
  string name = 2;
  uint32 num = 3;
  uint32 tab_id = 4;
}

message MuseAllocateUser {
  uint32 uid = 1;
  string username = 2;
  string nickname = 3;
  int32 sex = 4;
}

// 6.38.0 房间下发优化
message MuseAllocateChannelV2 {
  string title = 1; // 抬头信息
  MuseAllocateChannelInfoV2 channel_info = 2; // 房间信息
  MuseAllocateUserV2 reunion_user = 3; // 展示用户信息
  string background_img_url = 5;
  string background_text_color = 6;
  string mate_id = 7;
}

// 房间信息
message MuseAllocateChannelInfoV2 {
  uint32 channel_id = 1;
  string channel_name = 2;
  uint32 tab_id = 3;
  uint32 user_num = 4; // 在房人数
}

//  3. 用户属性标识
enum UserAttrCertType{
  USER_ATTR_CERT_TYPE_UNSPECIFIED = 0;
  USER_ATTR_CERT_TYPE_REUNION = 1; // 重逢用户
  USER_ATTR_CERT_TYPE_FRIEND = 2; // 好友
  USER_ATTR_CERT_TYPE_CHANNEL_OWNER = 3; // 房主
}

message MuseAllocateUserV2 {
  MuseAllocateUser user_info = 1;
  string user_attr_cert_icon_url = 2; // 用户属性标识
  string user_attr_cert_text=3; // 用户属性文案
//  MuseAllocateUserLabelText label_text = 4; // 用户标签文案
}

//message MuseAllocateUserLabelText{
//  string text = 1; // 文案
//  string text_color = 2; // 文案颜色
//  repeated string bg_color = 3; // 背景颜色
//}

message GetMuseAllocateNonEnterChannelReq {
  ga.BaseReq base_req = 1;
  uint32 uid = 2;
}

message GetMuseAllocateNonEnterChannelResp {
  ga.BaseResp base_resp = 1;
  bool hit_group_rule = 2;
  int32 ui_style = 3; // 0: 旧版 1: 新版
  int32 request_count = 4; // 请求次数配置
  int64 time_interval = 5; // 下次请求时间间隔配置
  int32   unread_msg_limit=6;  //未读消息小于多少，才会去请求
  int32  poll_time=7;   //轮询请求时间
}

message GetMuseAllocateNonEnterChannelInfoReq {
  ga.BaseReq base_req = 1;
  uint32 uid = 2;
  int32 ui_style = 3;
  int64 unread = 4; // 未读数
}

message PersonalCertification {
  string id = 1;
  string icon = 2;
  string text = 3;
  repeated string color = 4;
  string text_shadow_color = 5; /* 文字阴影颜色 */
}

message MuseAllocateNonEnterChannelInfo {
  string background_img_url = 1;
  string background_color = 2;
  MuseAllocateChannelInfoV2 channel_info = 3; // 房间信息
  string tab_name = 4;
  string tab_color = 5;
  MuseAllocateUserV2 user_info = 6;
  PersonalCertification cert = 7;
  string channel_message = 8;  //弹窗中间特征文案
  string button_text = 9; // 按钮文案
  string mate_id = 10;
  KTVSongGloryInfo glory_info=11;//获取称号
}

message GetMuseAllocateNonEnterChannelInfoResp {
  ga.BaseResp base_resp = 1;
  oneof ui {
    MuseAllocateChannelV2 old_ui = 2;
    MuseAllocateNonEnterChannelInfo new_ui = 3;
  }
}

message KTVSongGloryInfo{
  string glory_name = 5; // 称号名称
  string glory_img = 6; // 头标
  string glory_bg_img = 7; // 背景颜色
  uint32 glory_rank = 8; // 排行
  uint32 glory_level = 9;  //0--市级称号  1--省级称号 2--国服称号
  string glory_loc_code = 10;//位置code
  string glory_singer_id = 11;//歌手id3

}

message GetQuickEnterChannelModelReq {
  ga.BaseReq base_req = 1;
}

enum QuickEnterChannelModelType {
  QUICK_ENTER_CHANNEL_MODEL_TYPE_UNSPECIFIED = 0;
  QUICK_ENTER_CHANNEL_MODEL_TYPE_GANG_UP = 1; // 开黑
  QUICK_ENTER_CHANNEL_MODEL_TYPE_MUSIC = 2; // music
}

message QuickEnterChannelModel{
  uint32 model_type = 1; // QuickEnterChannelModelType
  string match_id = 2; // 匹配id
  QuickEnterChannelModelResource resource = 3; // 资源
  bool pinned = 4; // 是否每次置顶
}

message QuickEnterChannelModelResource{
  repeated string user_account_list = 1; // 用户
  string text = 2; // 文案
  string bg_url = 3; // 背景图
}

message GetQuickEnterChannelModelResp {
  ga.BaseResp base_resp = 1;
  repeated QuickEnterChannelModel model_list = 2;
}

message GetTodayCoupleListReq {
  ga.BaseReq base_req = 1;
  uint32 uid = 2;
}

message CoupleInfo {
  uint32 uid = 1; // cp uid
  string fake_avatar_url = 2; // 假头像
  string name = 3; // 用户名 服务端*覆盖
  int32 sex = 4; // 性别,男1女2
  string birth = 5; // 生日计算年龄，xxxx-xx-xx, 空字符串时不显示年龄
  string loc = 6; // 地理位置信息
  bool online = 7; // 用户是否在线
  string rcmd_tips = 8; // 推荐语
  string greet_text = 9; // 打招呼文案
  string account = 10; // 头像
  string trace_id = 11; // 推荐透传，客户端埋点需要上报该字段
}

message GetTodayCoupleListResp {
  ga.BaseResp base_resp = 1;
  repeated CoupleInfo couple_list = 2;
  repeated string robot_image=3;
  bool is_new_version=4;
  string title=5;
}

message GetFlashChatMatchingConditionReq {
  ga.BaseReq base_req = 1;
  uint32 uid = 2;
}

message GetFlashChatMatchingConditionResp {
  ga.BaseResp base_resp = 1;
  repeated FlashChatMatchingCondition conditions = 2;
  string title = 3;
}

message FlashChatMatchingCondition {
  string name = 1; // 条件名称
  repeated OptionList list  = 2;
}

message OptionList {
  string text = 1;
  bool select = 2;
  bool is_multiple_select = 3;
}

message GetFlashChatMatchingResultReq {
  ga.BaseReq base_req = 1;
  uint32 uid = 2;
  repeated FlashChatMatchingCondition conditions = 3;
  string source_type = 4; // 1-即时闪聊， 2-今日CP
}

message GetFlashChatMatchingResultResp {
  ga.BaseResp base_resp = 1;
  string tip = 2;
  uint32 remaining_num = 3; // 剩余次数
  repeated MatchUserInfo list = 4;
  string remain_title = 5;
}

message MatchUserInfo {
  uint32 uid = 1;
  string account = 2;
  string nickname = 3;
  uint32 gender = 4;
  uint32 follow_status=5; //see FOLLOW_STATUS_TYPE
  string greet_text = 6; // 打招呼文案
}

message GetFlashChatObjectInfoReq {
  ga.BaseReq base_req = 1;
  uint32 target_uid = 2;
  uint32 my_uid = 3;
  bool is_open_location = 4;
  string target_account = 5;
}

message GetFlashChatObjectInfoResp {
  ga.BaseResp base_resp = 1;
  string age = 2;
  string position = 3;
  uint32 sex = 4;
  bool target_online_status = 5;
  bool is_invalid_location = 6;
}



message FlashChatEntryPreCheckRequest{
  ga.BaseReq base_req = 1;
  string enter_source=2;  // 闪聊入口来源
}

message FlashChatEntryPreCheckResponse{
  ga.BaseResp base_resp = 1;
}

message ViewPersonalHomepageReportReq {
  ga.BaseReq base_req = 1;
  uint32 my_uid = 2;
  uint32 target_uid = 3;
  string target_account = 4;
}

message ViewPersonalHomepageReportResp {
  ga.BaseResp base_resp = 1;
}

message ViewHomepagePush {
  string message = 1;
  uint32 deadline_time = 2;
  string target_account = 3;
}



