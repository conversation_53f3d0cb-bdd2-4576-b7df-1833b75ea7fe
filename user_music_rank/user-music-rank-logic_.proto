syntax = "proto3";

package ga.user_music_rank;

import "ga_base.proto";


option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/user-music-rank";

//获取用户信息
message BatchMusicRankUserInfoReq {
  BaseReq base_req = 1;
  bool need_mic = 2;
  repeated uint32 uids = 3;
}

message BatchMusicRankUserInfoResp {
  BaseResp base_resp = 1;
  map<uint32, UserGloryInfo> user_glory_map = 2;
}
message UserGloryInfo {
  uint32 uid = 1;
  string glory_name = 2; // 称号名称
  string glory_img = 3; // 头标
  string glory_color = 4; // 背景颜色
  string glory_bg_img = 5; // 背景图
  uint32 glory_rank = 6; // 排行分

  string dan_img = 7;
  string dan_bg_img = 8;
  string dan_name = 9;

  string mic_img = 10;
  string play_color = 11;
  string banner_img = 12;
}

message GetUserMusicRankDialogReq{
  BaseReq base_req = 1;
}
message GetUserMusicRankDialogResp{
  BaseResp base_resp = 1;
  GloryDialog glory_dialog = 2;
  SeasonDialog season_dialog = 3;
  message GloryDialog {
    uint32 country_count = 1;
    uint32 province_count = 2;
    uint32 city_count = 3;

    repeated SingerGlory glories = 4;

    message SingerGlory{
      string singer_name = 1;
      string singer_image = 2;
      MusicRankGloryResource glory = 3;
    }
  }


  message SeasonDialog {
    string first_title_img = 1;
    string second_title_img = 2;
    StarLevel pre_star_level = 3;
    StarLevel cur_star_level = 4;
    // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
    uint32 channelId = 5;//推送才有
    // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
    uint32 tabId = 6;//推送才有
  }

  message StarLevel{
    string title = 1;
    string img = 2;
  }
}

message UserMusicRankDialogConfirmReq{
  BaseReq base_req = 1;
  ReqType req_type = 2;
  enum ReqType{
    SessionDialog = 0;
    GloryDialog = 1;
  }
}
message UserMusicRankDialogConfirmResp{
  BaseResp base_resp = 1;
}

message ListUserMusicRankSingerScoreReq {
  BaseReq base_req = 1;
}
message ListUserMusicRankSingerScoreResp {
  BaseResp base_resp = 1;
  repeated SingerScoreInfo infos = 2;

  message SingerScoreInfo{
    string singer_id = 1;//歌手ID
    string singer_name = 2;//歌手名称
    string singer_avatar = 3;//歌手头像
    MusicRankGloryResource glory = 4;//称号
    uint32 score = 5;//唱力
    string desc = 6;//该用户当前唱力描述文案
  }
}

message GetUserMusicRankSingerScoreDetailReq {
  BaseReq base_req = 1;
  string singer_id = 2;//歌手id
  // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
  ReqType reqType = 3;
  enum ReqType{
    Full = 0;//包含歌曲信息
    Simple = 1;//只有歌手信息
  }
}

message GetUserMusicRankSingerScoreDetailResp {
  BaseResp base_resp = 1;
  SingerScoreInfo singer_score_info = 2;//歌手唱力信息
  repeated SongScoreInfo song_score_info = 3;//歌手歌曲唱力信息

  message SingerScoreInfo{
    string singer_id = 1;//歌手ID
    string singer_name = 2;//歌手名称
    string singer_avatar = 3;//歌手头像
    MusicRankGloryResource glory = 4;//称号
    uint32 score = 5;//当前唱力
    uint32 max_score = 6;//历史最高唱力
    uint32 sang_num = 7;//当前赛季已唱歌曲
  }

  message SongScoreInfo{
    string song_id = 1;//歌曲id
    string image = 2;//歌曲封面
    string name = 3;//歌曲名称
    uint32 score = 4;//歌曲最高得分
    uint32 total_score = 5;//歌曲总分
    string singer_names_desc = 6;//歌手名称拼接
    uint32 origin_score = 7;//原始分数
    uint32 burst_light_num = 8;//爆灯数量
    string burst_light_copy = 9;//加成文案
    int32 vendor_id = 10; // 版权id
    int32 pitch_ability = 11; // 是否有音高线
  }
}

message MusicRankGloryResource {
  string glory_name = 1; // 称号名称
  string glory_img = 2; // 头标
  string glory_color = 3; // 背景颜色
  uint32 glory_rank = 4; // 排行分
  string play_color = 5;
  string banner_img = 6;
  string glory_bg_img = 7; // 背景图
  string glory_id = 8;
  MusicRankGloryLevel level = 9;

  string loc_code = 10;//位置code
  string singer_id = 11;//歌手id
}

enum MusicRankGloryLevel{
  City = 0;//市级称号
  Province = 1;//省级称号
  Country = 2;//国服称号
}

// ---------------------- push -----------------------------

// 升段推送
message UserMusicRankLevelUp {
  uint32 channel_id = 1;
  uint32 uid = 2;
  bool is_popup = 3;
  string dan_name = 4;
  LevelUpResource star = 5;
  LevelUpResource dan = 6;
  string my_dan_addr = 7;
  string copy = 8; // 文案
  bool is_level_up = 9; // 是否升级
  string toast_copy = 10; // 上限文档
}

message LevelUpResource {
  string img = 1;
  string md5 = 2;
}

// 麦位推送
message UserMusicRankMicExtInfo {
  uint32 channel_id = 1;
  uint32 uid = 2;
  string mic_img = 3;
  uint32 rank = 4;
}
