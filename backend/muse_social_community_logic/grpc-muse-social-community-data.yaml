cmdInfoList:
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36700
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/ListMuseSocialCommunityNavBars
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36701
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/ListMuseSocialCommunityNavSecondaryBars
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36702
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/GetSocialCommunityChatChannelHistoryMsg
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36703
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/BatGetMuseSocialCommunityUsersRole
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36705
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/JoinSocialCommunityFans
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36706
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/GetSocialCommunityDetail
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36707
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/GetSocialCommunityProfilePages
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36708
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/GetMySocialCommunity
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36710
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/SendWelcomePush
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36711
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/BatGetRankInChannel
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36712
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/RemoveSocialCommunityMember
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36713
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/ExitSocialCommunity
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36716
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/GetChannelAssociateSocialCommunity
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36717
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/GetMySocialCommunityPage
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36718
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/ListCategoryTypes
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36719
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/ListCategories
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36720
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/ApplyCreateSocialCommunity
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36721
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/GetSocialCommunityFloat
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36722
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/JoinSocialCommunity
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36723
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/ValidateUserHasCreateQualification
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36724
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/GetSocialCommunityBase
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36725
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/GetUserSocialGroupIds
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36726
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/MuseSocialPreviewGroupMessage
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36727
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/MuseSocialGroupRemoveAdmin
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36728
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/MuseSocialGroupSetAllMute
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36729
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/MuseSocialGroupMuteMember
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36730
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/MuseSocialGroupUnmuteMember
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36731
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/MuseSocialGroupGetMuteList
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36732
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/MuseSocialGroupGetMemberList
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36733
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/MuseSocialGroupGetDetailInfo
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36734
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/MuseSocialGroupAddAdmin
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36735
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/GetSocialGroupOnlineMembers
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36736
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/BatGetSocialCommunityKernelMembers
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36737
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/GetGroupActiveMembers
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36738
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/GetSocialCommunityMemberList
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36740
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/GetSocialCommunityAnnounceNewsCount
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36741
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/UpsertMuseSocialAnnounce
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36742
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/ListAnnounceDestinations
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36743
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/ListMuseSocialAnnounces
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36744
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/SetMuseSocialAnnounceInterest
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36745
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/RemoveMuseSocialAnnounce
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36746
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/ListMuseSocialAnnounceInterestUsers
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36747
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/ValidateUserHasCreateAnnouncePermissions
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36748
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/SetCommunityAdditionMode
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36749
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/GetCommunityAdditionMode
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36750
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/ListSocialCommunitySystemMessage
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36751
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/SubmitApplicationToJoinCommunity
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36752
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/UpsertJoinSocialCommunityMessageStatus
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36753
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/GetSocialCommunityUpdateLevelTip
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36754
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/GetSocialCommunityLevelDetail
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36755
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/SocialCommunityCheckIn
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36756
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/GetSocialCommunityContentStreamNewsCount
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36757
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/GetSocialCommunityContentStream
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36758
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/ListMuseSocialCommunityCommentMessage
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36759
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/ListMuseSocialCommunityAttitudeMessage
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36760
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/GetSocialCommunityNonPublicUserCard
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36761
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/IntroduceSocialCommunityByCategoryId
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36762
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/ListMuseSocialCommunityNavBarsV2
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36763
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/ListMuseSocialCommunityNavSecondaryBarsV2
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36764
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/ListMuseSocialCommunityGroups
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36765
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/BatchMuseSocialCommunityNavBarsV2
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36766
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/UpdateSocialCommunityInfo
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36767
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/GetSocialCommunityEditableInfo
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36768
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/ReportPersonalChannelViewSocialCommunity
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36769
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/GetMemberStatusInTheSocialCommunity
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36770
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/GetSocialCommunityAssistantMsgCount
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36771
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/GetSocialCommunityInvitationCodeDetail
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36772
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/GetSocialCommunityInvitationCodeShareText
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36773
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/SocialCommunitySharePreCheck
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36774
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/CheckSocialCommunityInvitationUser
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36775
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/SearchSocialCommunity
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36776
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/GetUserSocialCommunityList
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36777
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/GetExtendMicPermission
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36778
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/SetExtendMicNumbers
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36779
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/GetExtendMicNumbers
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36780
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/GetSocialCommunityCaptain
  - source: api/muse_social_community_logic/grpc_muse_social_community.proto
    cmd: 36781
    lang: go
    method: /ga.api.muse_social_community_logic.MuseSocialCommunity/GetSocialCommunityActivityPopup

