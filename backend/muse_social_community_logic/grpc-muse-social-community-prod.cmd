# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

36700:
api-route-configurator --etcd-endpoints *************:2379 create --id 36700 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/ListMuseSocialCommunityNavBars
36701:
api-route-configurator --etcd-endpoints *************:2379 create --id 36701 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/ListMuseSocialCommunityNavSecondaryBars
36702:
api-route-configurator --etcd-endpoints *************:2379 create --id 36702 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/GetSocialCommunityChatChannelHistoryMsg
36703:
api-route-configurator --etcd-endpoints *************:2379 create --id 36703 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/BatGetMuseSocialCommunityUsersRole
36705:
api-route-configurator --etcd-endpoints *************:2379 create --id 36705 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/JoinSocialCommunityFans
36706:
api-route-configurator --etcd-endpoints *************:2379 create --id 36706 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/GetSocialCommunityDetail
36707:
api-route-configurator --etcd-endpoints *************:2379 create --id 36707 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/GetSocialCommunityProfilePages
36708:
api-route-configurator --etcd-endpoints *************:2379 create --id 36708 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/GetMySocialCommunity
36710:
api-route-configurator --etcd-endpoints *************:2379 create --id 36710 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/SendWelcomePush
36711:
api-route-configurator --etcd-endpoints *************:2379 create --id 36711 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/BatGetRankInChannel
36712:
api-route-configurator --etcd-endpoints *************:2379 create --id 36712 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/RemoveSocialCommunityMember
36713:
api-route-configurator --etcd-endpoints *************:2379 create --id 36713 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/ExitSocialCommunity
36714:
api-route-configurator --etcd-endpoints *************:2379 create --id 36714 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/UpdateMemberRole
36715:
api-route-configurator --etcd-endpoints *************:2379 create --id 36715 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/GetSocialCommunityRoleLeftNumbers
36716:
api-route-configurator --etcd-endpoints *************:2379 create --id 36716 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/GetChannelAssociateSocialCommunity
36717:
api-route-configurator --etcd-endpoints *************:2379 create --id 36717 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/GetMySocialCommunityPage
36718:
api-route-configurator --etcd-endpoints *************:2379 create --id 36718 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/ListCategoryTypes
36719:
api-route-configurator --etcd-endpoints *************:2379 create --id 36719 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/ListCategories
36720:
api-route-configurator --etcd-endpoints *************:2379 create --id 36720 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/ApplyCreateSocialCommunity
36721:
api-route-configurator --etcd-endpoints *************:2379 create --id 36721 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/GetSocialCommunityFloat
36722:
api-route-configurator --etcd-endpoints *************:2379 create --id 36722 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/JoinSocialCommunity
36723:
api-route-configurator --etcd-endpoints *************:2379 create --id 36723 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/ValidateUserHasCreateQualification
36724:
api-route-configurator --etcd-endpoints *************:2379 create --id 36724 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/GetSocialCommunityBase
36725:
api-route-configurator --etcd-endpoints *************:2379 create --id 36725 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/GetUserSocialGroupIds
36726:
api-route-configurator --etcd-endpoints *************:2379 create --id 36726 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/MuseSocialPreviewGroupMessage
36727:
api-route-configurator --etcd-endpoints *************:2379 create --id 36727 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/MuseSocialGroupRemoveAdmin
36728:
api-route-configurator --etcd-endpoints *************:2379 create --id 36728 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/MuseSocialGroupSetAllMute
36729:
api-route-configurator --etcd-endpoints *************:2379 create --id 36729 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/MuseSocialGroupMuteMember
36730:
api-route-configurator --etcd-endpoints *************:2379 create --id 36730 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/MuseSocialGroupUnmuteMember
36731:
api-route-configurator --etcd-endpoints *************:2379 create --id 36731 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/MuseSocialGroupGetMuteList
36732:
api-route-configurator --etcd-endpoints *************:2379 create --id 36732 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/MuseSocialGroupGetMemberList
36733:
api-route-configurator --etcd-endpoints *************:2379 create --id 36733 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/MuseSocialGroupGetDetailInfo
36734:
api-route-configurator --etcd-endpoints *************:2379 create --id 36734 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/MuseSocialGroupAddAdmin
36735:
api-route-configurator --etcd-endpoints *************:2379 create --id 36735 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/GetSocialGroupOnlineMembers
36736:
api-route-configurator --etcd-endpoints *************:2379 create --id 36736 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/BatGetSocialCommunityKernelMembers
36737:
api-route-configurator --etcd-endpoints *************:2379 create --id 36737 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/GetGroupActiveMembers
36738:
api-route-configurator --etcd-endpoints *************:2379 create --id 36738 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/GetSocialCommunityMemberList
36740:
api-route-configurator --etcd-endpoints *************:2379 create --id 36740 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/GetSocialCommunityAnnounceNewsCount
36741:
api-route-configurator --etcd-endpoints *************:2379 create --id 36741 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/UpsertMuseSocialAnnounce
36742:
api-route-configurator --etcd-endpoints *************:2379 create --id 36742 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/ListAnnounceDestinations
36743:
api-route-configurator --etcd-endpoints *************:2379 create --id 36743 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/ListMuseSocialAnnounces
36744:
api-route-configurator --etcd-endpoints *************:2379 create --id 36744 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/SetMuseSocialAnnounceInterest
36745:
api-route-configurator --etcd-endpoints *************:2379 create --id 36745 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/RemoveMuseSocialAnnounce
36746:
api-route-configurator --etcd-endpoints *************:2379 create --id 36746 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/ListMuseSocialAnnounceInterestUsers
36747:
api-route-configurator --etcd-endpoints *************:2379 create --id 36747 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/ValidateUserHasCreateAnnouncePermissions
36748:
api-route-configurator --etcd-endpoints *************:2379 create --id 36748 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/SetCommunityAdditionMode
36749:
api-route-configurator --etcd-endpoints *************:2379 create --id 36749 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/GetCommunityAdditionMode
36750:
api-route-configurator --etcd-endpoints *************:2379 create --id 36750 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/ListSocialCommunitySystemMessage
36751:
api-route-configurator --etcd-endpoints *************:2379 create --id 36751 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/SubmitApplicationToJoinCommunity
36752:
api-route-configurator --etcd-endpoints *************:2379 create --id 36752 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/UpsertJoinSocialCommunityMessageStatus
36753:
api-route-configurator --etcd-endpoints *************:2379 create --id 36753 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/GetSocialCommunityUpdateLevelTip
36754:
api-route-configurator --etcd-endpoints *************:2379 create --id 36754 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/GetSocialCommunityLevelDetail
36755:
api-route-configurator --etcd-endpoints *************:2379 create --id 36755 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/SocialCommunityCheckIn
36756:
api-route-configurator --etcd-endpoints *************:2379 create --id 36756 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/GetSocialCommunityContentStreamNewsCount
36757:
api-route-configurator --etcd-endpoints *************:2379 create --id 36757 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/GetSocialCommunityContentStream
36758:
api-route-configurator --etcd-endpoints *************:2379 create --id 36758 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/ListMuseSocialCommunityCommentMessage
36759:
api-route-configurator --etcd-endpoints *************:2379 create --id 36759 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/ListMuseSocialCommunityAttitudeMessage
36760:
api-route-configurator --etcd-endpoints *************:2379 create --id 36760 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/GetSocialCommunityNonPublicUserCard
36761:
api-route-configurator --etcd-endpoints *************:2379 create --id 36761 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/IntroduceSocialCommunityByCategoryId
36762:
api-route-configurator --etcd-endpoints *************:2379 create --id 36762 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/ListMuseSocialCommunityNavBarsV2
36763:
api-route-configurator --etcd-endpoints *************:2379 create --id 36763 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/ListMuseSocialCommunityNavSecondaryBarsV2
36764:
api-route-configurator --etcd-endpoints *************:2379 create --id 36764 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/ListMuseSocialCommunityGroups
36765:
api-route-configurator --etcd-endpoints *************:2379 create --id 36765 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/BatchMuseSocialCommunityNavBarsV2
36766:
api-route-configurator --etcd-endpoints *************:2379 create --id 36766 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/UpdateSocialCommunityInfo
36767:
api-route-configurator --etcd-endpoints *************:2379 create --id 36767 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/GetSocialCommunityEditableInfo
36768:
api-route-configurator --etcd-endpoints *************:2379 create --id 36768 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/ReportPersonalChannelViewSocialCommunity
36769:
api-route-configurator --etcd-endpoints *************:2379 create --id 36769 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/GetMemberStatusInTheSocialCommunity
36770:
api-route-configurator --etcd-endpoints *************:2379 create --id 36770 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/GetSocialCommunityAssistantMsgCount
36771:
api-route-configurator --etcd-endpoints *************:2379 create --id 36771 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/GetSocialCommunityInvitationCodeDetail
36772:
api-route-configurator --etcd-endpoints *************:2379 create --id 36772 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/GetSocialCommunityInvitationCodeShareText
36773:
api-route-configurator --etcd-endpoints *************:2379 create --id 36773 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/SocialCommunitySharePreCheck
36774:
api-route-configurator --etcd-endpoints *************:2379 create --id 36774 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/CheckSocialCommunityInvitationUser
36775:
api-route-configurator --etcd-endpoints *************:2379 create --id 36775 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/SearchSocialCommunity
36776:
api-route-configurator --etcd-endpoints *************:2379 create --id 36776 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/GetUserSocialCommunityList
36777:
api-route-configurator --etcd-endpoints *************:2379 create --id 36777 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/GetExtendMicPermission
36778:
api-route-configurator --etcd-endpoints *************:2379 create --id 36778 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/SetExtendMicNumbers
36779:
api-route-configurator --etcd-endpoints *************:2379 create --id 36779 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/GetExtendMicNumbers
36780:
api-route-configurator --etcd-endpoints *************:2379 create --id 36780 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/GetSocialCommunityCaptain
36781:
api-route-configurator --etcd-endpoints *************:2379 create --id 36781 --source api/muse_social_community_logic/grpc_muse_social_community.proto --lang go --method /ga.api.muse_social_community_logic.MuseSocialCommunity/GetSocialCommunityActivityPopup
