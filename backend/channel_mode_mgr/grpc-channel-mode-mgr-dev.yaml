apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  namespace: quicksilver
  name: grpc-channel-mode-mgr-logic
spec:
  gateways:
  - istio-system/apiv2-gateway
  - istio-system/apiv2-gateway-internal
  hosts:
  - '*'
  http:
  - match:
    - uri:
        prefix: /ga.api.channel_mode_mgr.ChannelModeMgrLogic/
    rewrite:
      uri: /logic.ChannelModeMgrLogic/
    delegate:
       name: tt-rev-channel-mode-mgr-logic-delegator-80
       namespace: quicksilver


