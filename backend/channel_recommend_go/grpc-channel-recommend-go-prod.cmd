# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

5053:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 5053 --source api/channel_recommend_go/grpc_channel_recommend_go.proto --lang go --method /ga.api.channel_recommend_go.ChannelRecommendLogicService/GetRecFeedbackConfig
5054:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 5054 --source api/channel_recommend_go/grpc_channel_recommend_go.proto --lang go --method /ga.api.channel_recommend_go.ChannelRecommendLogicService/DoRecFeedback
5055:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 5055 --source api/channel_recommend_go/grpc_channel_recommend_go.proto --lang go --method /ga.api.channel_recommend_go.ChannelRecommendLogicService/GetRecLotteryChList
5061:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 5061 --source api/channel_recommend_go/grpc_channel_recommend_go.proto --lang go --method /ga.api.channel_recommend_go.ChannelRecommendLogicService/GetRecommendChannelService
5080:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 5080 --source api/channel_recommend_go/grpc_channel_recommend_go.proto --lang go --method /ga.api.channel_recommend_go.ChannelRecommendLogicService/GetRecommendChannelsV2Service
5081:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 5081 --source api/channel_recommend_go/grpc_channel_recommend_go.proto --lang go --method /ga.api.channel_recommend_go.ChannelRecommendLogicService/GetRecommonChannelListByTagIdV2Service
5082:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 5082 --source api/channel_recommend_go/grpc_channel_recommend_go.proto --lang go --method /ga.api.channel_recommend_go.ChannelRecommendLogicService/GetRecChListByPerTagIdV2Service
5083:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 5083 --source api/channel_recommend_go/grpc_channel_recommend_go.proto --lang go --method /ga.api.channel_recommend_go.ChannelRecommendLogicService/GetQuickEntryConfigV2Service
5084:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 5084 --source api/channel_recommend_go/grpc_channel_recommend_go.proto --lang go --method /ga.api.channel_recommend_go.ChannelRecommendLogicService/GetChannelTopOverLay
5085:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 5085 --source api/channel_recommend_go/grpc_channel_recommend_go.proto --lang go --method /ga.api.channel_recommend_go.ChannelRecommendLogicService/GetRevenueSwitchHubService
5086:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 5086 --source api/channel_recommend_go/grpc_channel_recommend_go.proto --lang go --method /ga.api.channel_recommend_go.ChannelRecommendLogicService/SetRevenueSwitchHubService
5087:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 5087 --source api/channel_recommend_go/grpc_channel_recommend_go.proto --lang go --method /ga.api.channel_recommend_go.ChannelRecommendLogicService/GetGlobalTopOverLay
