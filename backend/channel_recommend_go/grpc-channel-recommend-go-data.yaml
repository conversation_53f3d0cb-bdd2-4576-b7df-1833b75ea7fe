cmdInfoList:
  - source: api/channel_recommend_go/grpc_channel_recommend_go.proto
    cmd: 5053
    lang: go
    method: /ga.api.channel_recommend_go.ChannelRecommendLogicService/GetRecFeedbackConfig
  - source: api/channel_recommend_go/grpc_channel_recommend_go.proto
    cmd: 5054
    lang: go
    method: /ga.api.channel_recommend_go.ChannelRecommendLogicService/DoRecFeedback
  - source: api/channel_recommend_go/grpc_channel_recommend_go.proto
    cmd: 5055
    lang: go
    method: /ga.api.channel_recommend_go.ChannelRecommendLogicService/GetRecLotteryChList
  - source: api/channel_recommend_go/grpc_channel_recommend_go.proto
    cmd: 5080
    lang: go
    method: /ga.api.channel_recommend_go.ChannelRecommendLogicService/GetRecommendChannelsV2Service
  - source: api/channel_recommend_go/grpc_channel_recommend_go.proto
    cmd: 5081
    lang: go
    method: /ga.api.channel_recommend_go.ChannelRecommendLogicService/GetRecommonChannelListByTagIdV2Service
  - source: api/channel_recommend_go/grpc_channel_recommend_go.proto
    cmd: 5082
    lang: go
    method: /ga.api.channel_recommend_go.ChannelRecommendLogicService/GetRecChListByPerTagIdV2Service
  - source: api/channel_recommend_go/grpc_channel_recommend_go.proto
    cmd: 5083
    lang: go
    method: /ga.api.channel_recommend_go.ChannelRecommendLogicService/GetQuickEntryConfigV2Service
  - source: api/channel_recommend_go/grpc_channel_recommend_go.proto
    cmd: 5084
    lang: go
    method: /ga.api.channel_recommend_go.ChannelRecommendLogicService/GetChannelTopOverLay
  - source: api/channel_recommend_go/grpc_channel_recommend_go.proto
    cmd: 5085
    lang: go
    method: /ga.api.channel_recommend_go.ChannelRecommendLogicService/GetRevenueSwitchHubService
  - source: api/channel_recommend_go/grpc_channel_recommend_go.proto
    cmd: 5086
    lang: go
    method: /ga.api.channel_recommend_go.ChannelRecommendLogicService/SetRevenueSwitchHubService
  - source: api/channel_recommend_go/grpc_channel_recommend_go.proto
    cmd: 5087
    lang: go
    method: /ga.api.channel_recommend_go.ChannelRecommendLogicService/GetGlobalTopOverLay

