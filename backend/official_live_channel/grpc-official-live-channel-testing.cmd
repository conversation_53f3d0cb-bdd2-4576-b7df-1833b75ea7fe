# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

30410:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 30410 --source api/official_live_channel/grpc_official_live_channel.proto --lang go --method /ga.api.official_live_channel.OfficialLiveChannelLogic/GetOfficialLiveChannelInfo
30411:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 30411 --source api/official_live_channel/grpc_official_live_channel.proto --lang go --method /ga.api.official_live_channel.OfficialLiveChannelLogic/GetOfficialLiveChannelRelaySchedule
30412:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 30412 --source api/official_live_channel/grpc_official_live_channel.proto --lang go --method /ga.api.official_live_channel.OfficialLiveChannelLogic/CancelOfficialLiveChannelRelay
30413:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 30413 --source api/official_live_channel/grpc_official_live_channel.proto --lang go --method /ga.api.official_live_channel.OfficialLiveChannelLogic/GetLiveChannelRelay
30414:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 30414 --source api/official_live_channel/grpc_official_live_channel.proto --lang go --method /ga.api.official_live_channel.OfficialLiveChannelLogic/ReportRelayLiveChannelAudio
30415:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 30415 --source api/official_live_channel/grpc_official_live_channel.proto --lang go --method /ga.api.official_live_channel.OfficialLiveChannelLogic/GetOfficialChannelDescribe
