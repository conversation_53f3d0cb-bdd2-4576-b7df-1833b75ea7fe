# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

29000:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 29000 --source api/guild_honor_halls/grpc_guild_honor_halls.proto --lang go --method /ga.api.guild_honor_halls.GuildHonorHallsLogic/GetHonorGuildList
29001:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 29001 --source api/guild_honor_halls/grpc_guild_honor_halls.proto --lang go --method /ga.api.guild_honor_halls.GuildHonorHallsLogic/SetGuildHotChannelList
29002:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 29002 --source api/guild_honor_halls/grpc_guild_honor_halls.proto --lang go --method /ga.api.guild_honor_halls.GuildHonorHallsLogic/SetGuildCharmMemberList
29003:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 29003 --source api/guild_honor_halls/grpc_guild_honor_halls.proto --lang go --method /ga.api.guild_honor_halls.GuildHonorHallsLogic/SearchGuildMember
29004:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 29004 --source api/guild_honor_halls/grpc_guild_honor_halls.proto --lang go --method /ga.api.guild_honor_halls.GuildHonorHallsLogic/GetHonorGuild
