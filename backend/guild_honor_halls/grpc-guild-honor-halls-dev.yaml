apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  namespace: quicksilver
  name: grpc-guild-honor-halls-logic
spec:
  gateways:
  - istio-system/apiv2-gateway
  - istio-system/apiv2-gateway-internal
  hosts:
  - '*'
  http:
  - match:
    - uri:
        prefix: /ga.api.guild_honor_halls.GuildHonorHallsLogic/
    rewrite:
      uri: /logic.GuildHonorHallsLogic/
    delegate:
       name: guild-honor-halls-logic-delegator-80
       namespace: quicksilver


