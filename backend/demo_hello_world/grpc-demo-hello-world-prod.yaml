apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  namespace: quicksilver
  name: grpc-demo-hello-world-logic
spec:
  gateways:
  - quicksilver/tt-api-gateway
  - quicksilver/tt-api-gateway-internal
  hosts:
  - '*'
  http:
  - match:
    - uri:
        prefix: /ga.api.demo_hello_world.DemoHelloWorldLogic/
    rewrite:
      uri: /logic.ugc.DemoHelloWorldLogic/
    delegate:
       name: demo-helloworld-logic-delegator-80
       namespace: quicksilver


