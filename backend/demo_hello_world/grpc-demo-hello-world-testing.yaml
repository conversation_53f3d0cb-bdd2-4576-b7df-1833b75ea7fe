apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  namespace: quicksilver
  name: grpc-demo-hello-world-logic
spec:
  gateways:
  - istio-ingress/apiv2-gateway
  - istio-ingress/apiv2-gateway-internal
  hosts:
  - '*'
  http:
  - match:
    - uri:
        prefix: /ga.api.demo_hello_world.DemoHelloWorldLogic/
    rewrite:
      uri: /logic.ugc.DemoHelloWorldLogic/
    delegate:
       name: demo-helloworld-logic-delegator-80
       namespace: quicksilver


