cmdInfoList:
  - source: api/muse_allocate_logic/grpc_muse_allocate.proto
    cmd: 38001
    lang: go
    method: /ga.api.muse_allocate_logic.MuseAllocateLogic/GetMuseAllocateInfo
  - source: api/muse_allocate_logic/grpc_muse_allocate.proto
    cmd: 38002
    lang: go
    method: /ga.api.muse_allocate_logic.MuseAllocateLogic/GetMuseAllocateNonEnterChannel
  - source: api/muse_allocate_logic/grpc_muse_allocate.proto
    cmd: 38003
    lang: go
    method: /ga.api.muse_allocate_logic.MuseAllocateLogic/GetMuseAllocateNonEnterChannelInfo
  - source: api/muse_allocate_logic/grpc_muse_allocate.proto
    cmd: 38004
    lang: go
    method: /ga.api.muse_allocate_logic.MuseAllocateLogic/GetQuickEnterChannelModel
  - source: api/muse_allocate_logic/grpc_muse_allocate.proto
    cmd: 38005
    lang: go
    method: /ga.api.muse_allocate_logic.MuseAllocateLogic/GetTodayCoupleList
  - source: api/muse_allocate_logic/grpc_muse_allocate.proto
    cmd: 38100
    lang: go
    method: /ga.api.muse_allocate_logic.MuseAllocateLogic/GetFlashChatMatchingCondition
  - source: api/muse_allocate_logic/grpc_muse_allocate.proto
    cmd: 38101
    lang: go
    method: /ga.api.muse_allocate_logic.MuseAllocateLogic/GetFlashChatMatchingResult
  - source: api/muse_allocate_logic/grpc_muse_allocate.proto
    cmd: 38102
    lang: go
    method: /ga.api.muse_allocate_logic.MuseAllocateLogic/GetFlashChatObjectInfo
  - source: api/muse_allocate_logic/grpc_muse_allocate.proto
    cmd: 38103
    lang: go
    method: /ga.api.muse_allocate_logic.MuseAllocateLogic/FlashChatEntryPreCheck
  - source: api/muse_allocate_logic/grpc_muse_allocate.proto
    cmd: 38104
    lang: go
    method: /ga.api.muse_allocate_logic.MuseAllocateLogic/ViewPersonalHomepageReport

