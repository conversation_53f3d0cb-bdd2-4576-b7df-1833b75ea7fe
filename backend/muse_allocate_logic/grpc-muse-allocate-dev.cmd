# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

38001:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 38001 --source api/muse_allocate_logic/grpc_muse_allocate.proto --lang go --method /ga.api.muse_allocate_logic.MuseAllocateLogic/GetMuseAllocateInfo
38002:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 38002 --source api/muse_allocate_logic/grpc_muse_allocate.proto --lang go --method /ga.api.muse_allocate_logic.MuseAllocateLogic/GetMuseAllocateNonEnterChannel
38003:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 38003 --source api/muse_allocate_logic/grpc_muse_allocate.proto --lang go --method /ga.api.muse_allocate_logic.MuseAllocateLogic/GetMuseAllocateNonEnterChannelInfo
38004:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 38004 --source api/muse_allocate_logic/grpc_muse_allocate.proto --lang go --method /ga.api.muse_allocate_logic.MuseAllocateLogic/GetQuickEnterChannelModel
38005:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 38005 --source api/muse_allocate_logic/grpc_muse_allocate.proto --lang go --method /ga.api.muse_allocate_logic.MuseAllocateLogic/GetTodayCoupleList
38100:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 38100 --source api/muse_allocate_logic/grpc_muse_allocate.proto --lang go --method /ga.api.muse_allocate_logic.MuseAllocateLogic/GetFlashChatMatchingCondition
38101:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 38101 --source api/muse_allocate_logic/grpc_muse_allocate.proto --lang go --method /ga.api.muse_allocate_logic.MuseAllocateLogic/GetFlashChatMatchingResult
38102:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 38102 --source api/muse_allocate_logic/grpc_muse_allocate.proto --lang go --method /ga.api.muse_allocate_logic.MuseAllocateLogic/GetFlashChatObjectInfo
38103:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 38103 --source api/muse_allocate_logic/grpc_muse_allocate.proto --lang go --method /ga.api.muse_allocate_logic.MuseAllocateLogic/FlashChatEntryPreCheck
38104:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 38104 --source api/muse_allocate_logic/grpc_muse_allocate.proto --lang go --method /ga.api.muse_allocate_logic.MuseAllocateLogic/ViewPersonalHomepageReport
