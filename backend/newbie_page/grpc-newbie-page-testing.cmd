# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

3310:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 3310 --source api/newbie_page/grpc_newbie_page.proto --lang go --method /ga.api.newbie_page.NewbiePageLogic/GetNewbiePageConfig
3311:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 3311 --source api/newbie_page/grpc_newbie_page.proto --lang go --method /ga.api.newbie_page.NewbiePageLogic/SetUserNewbiePageTag
3312:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 3312 --source api/newbie_page/grpc_newbie_page.proto --lang go --method /ga.api.newbie_page.NewbiePageLogic/SetUserPageTagList
3313:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 3313 --source api/newbie_page/grpc_newbie_page.proto --lang go --method /ga.api.newbie_page.NewbiePageLogic/GetUserPageTagList
