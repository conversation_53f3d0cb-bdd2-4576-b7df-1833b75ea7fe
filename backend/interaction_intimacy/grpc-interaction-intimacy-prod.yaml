apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  namespace: quicksilver
  name: grpc-interaction-intimacy-logic
spec:
  gateways:
  - quicksilver/tt-api-gateway
  - quicksilver/tt-api-gateway-internal
  hosts:
  - '*'
  http:
  - match:
    - uri:
        prefix: /ga.api.interaction_intimacy.InteractionIntimacyLogic/
    rewrite:
      uri: /logic.InteractionIntimacyLogic/
    delegate:
       name: interaction-intimacy-logic-delegator-80
       namespace: quicksilver


