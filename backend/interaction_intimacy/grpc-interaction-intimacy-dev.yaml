apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  namespace: quicksilver
  name: grpc-interaction-intimacy-logic
spec:
  gateways:
  - istio-system/apiv2-gateway
  - istio-system/apiv2-gateway-internal
  hosts:
  - '*'
  http:
  - match:
    - uri:
        prefix: /ga.api.interaction_intimacy.InteractionIntimacyLogic/
    rewrite:
      uri: /logic.InteractionIntimacyLogic/
    delegate:
       name: interaction-intimacy-logic-delegator-80
       namespace: quicksilver


