# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

33081:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 33081 --source api/competition_entrance/grpc_competition_entrance.proto --lang go --method /ga.api.competition_entrance.CompetitionEntranceLogic/GetCompetitionEntrance
33082:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 33082 --source api/competition_entrance/grpc_competition_entrance.proto --lang go --method /ga.api.competition_entrance.CompetitionEntranceLogic/GetGameTmpChannelCfg
33083:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 33083 --source api/competition_entrance/grpc_competition_entrance.proto --lang go --method /ga.api.competition_entrance.CompetitionEntranceLogic/GetChannelCompetitionEntranceList
33084:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 33084 --source api/competition_entrance/grpc_competition_entrance.proto --lang go --method /ga.api.competition_entrance.CompetitionEntranceLogic/CheckShowFloatingComponent
