apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  namespace: quicksilver
  name: grpc-competition-entrance-logic
spec:
  gateways:
  - istio-ingress/apiv2-gateway
  - istio-ingress/apiv2-gateway-internal
  hosts:
  - '*'
  http:
  - match:
    - uri:
        prefix: /ga.api.competition_entrance.CompetitionEntranceLogic/
    rewrite:
      uri: /logic.CompetitionEntranceLogic/
    delegate:
       name: competition-entrance-logic-delegator-80
       namespace: quicksilver


