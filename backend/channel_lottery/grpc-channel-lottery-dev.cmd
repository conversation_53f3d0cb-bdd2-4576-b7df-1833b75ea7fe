# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

30431:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 30431 --source api/channel_lottery/grpc_channel_lottery.proto --lang go --method /ga.api.channel_lottery.ChannelLotteryLogic/ShowChannelLotterySetting
30432:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 30432 --source api/channel_lottery/grpc_channel_lottery.proto --lang go --method /ga.api.channel_lottery.ChannelLotteryLogic/GetChannelLotterySetting
30433:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 30433 --source api/channel_lottery/grpc_channel_lottery.proto --lang go --method /ga.api.channel_lottery.ChannelLotteryLogic/SetChannelLotteryInfo
30434:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 30434 --source api/channel_lottery/grpc_channel_lottery.proto --lang go --method /ga.api.channel_lottery.ChannelLotteryLogic/JoinChannelLottery
30435:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 30435 --source api/channel_lottery/grpc_channel_lottery.proto --lang go --method /ga.api.channel_lottery.ChannelLotteryLogic/GetChannelLotteryInfoList
30436:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 30436 --source api/channel_lottery/grpc_channel_lottery.proto --lang go --method /ga.api.channel_lottery.ChannelLotteryLogic/BeginChannelLottery
30437:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 30437 --source api/channel_lottery/grpc_channel_lottery.proto --lang go --method /ga.api.channel_lottery.ChannelLotteryLogic/SendChannelLotteryPresent
30438:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 30438 --source api/channel_lottery/grpc_channel_lottery.proto --lang go --method /ga.api.channel_lottery.ChannelLotteryLogic/GetChannelLotteryInfo
30439:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 30439 --source api/channel_lottery/grpc_channel_lottery.proto --lang go --method /ga.api.channel_lottery.ChannelLotteryLogic/SearchCustomGifts
36521:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 36521 --source api/channel_lottery/grpc_channel_lottery.proto --lang go --method /ga.api.channel_lottery.ChannelLotteryLogic/ReportEnterShareChannel
36522:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 36522 --source api/channel_lottery/grpc_channel_lottery.proto --lang go --method /ga.api.channel_lottery.ChannelLotteryLogic/GetEnterChannelUserCnt
36523:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 36523 --source api/channel_lottery/grpc_channel_lottery.proto --lang go --method /ga.api.channel_lottery.ChannelLotteryLogic/GetConditionMissionProgress
