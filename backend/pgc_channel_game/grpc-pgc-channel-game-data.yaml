cmdInfoList:
  - source: api/pgc_channel_game/grpc_pgc_channel_game.proto
    cmd: 36351
    lang: go
    method: /ga.api.pgc_channel_game.PgcChannelGameLogic/GetGameList
  - source: api/pgc_channel_game/grpc_pgc_channel_game.proto
    cmd: 36352
    lang: go
    method: /ga.api.pgc_channel_game.PgcChannelGameLogic/SetGamePhase
  - source: api/pgc_channel_game/grpc_pgc_channel_game.proto
    cmd: 36353
    lang: go
    method: /ga.api.pgc_channel_game.PgcChannelGameLogic/SetNextBombUser
  - source: api/pgc_channel_game/grpc_pgc_channel_game.proto
    cmd: 36354
    lang: go
    method: /ga.api.pgc_channel_game.PgcChannelGameLogic/GetChannelGameInfo
  - source: api/pgc_channel_game/grpc_pgc_channel_game.proto
    cmd: 36355
    lang: go
    method: /ga.api.pgc_channel_game.PgcChannelGameLogic/DigitalBombEnroll
  - source: api/pgc_channel_game/grpc_pgc_channel_game.proto
    cmd: 36356
    lang: go
    method: /ga.api.pgc_channel_game.PgcChannelGameLogic/DigitalBombSelectGameUser
  - source: api/pgc_channel_game/grpc_pgc_channel_game.proto
    cmd: 36357
    lang: go
    method: /ga.api.pgc_channel_game.PgcChannelGameLogic/DigitalBombSelectNumber
  - source: api/pgc_channel_game/grpc_pgc_channel_game.proto
    cmd: 36358
    lang: go
    method: /ga.api.pgc_channel_game.PgcChannelGameLogic/AdventureEnroll
  - source: api/pgc_channel_game/grpc_pgc_channel_game.proto
    cmd: 36359
    lang: go
    method: /ga.api.pgc_channel_game.PgcChannelGameLogic/AdventureSelectGameUser
  - source: api/pgc_channel_game/grpc_pgc_channel_game.proto
    cmd: 36360
    lang: go
    method: /ga.api.pgc_channel_game.PgcChannelGameLogic/AdventureRandomSteps
  - source: api/pgc_channel_game/grpc_pgc_channel_game.proto
    cmd: 36361
    lang: go
    method: /ga.api.pgc_channel_game.PgcChannelGameLogic/AdventureControlNext

