# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

36351:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 36351 --source api/pgc_channel_game/grpc_pgc_channel_game.proto --lang go --method /ga.api.pgc_channel_game.PgcChannelGameLogic/GetGameList
36352:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 36352 --source api/pgc_channel_game/grpc_pgc_channel_game.proto --lang go --method /ga.api.pgc_channel_game.PgcChannelGameLogic/SetGamePhase
36353:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 36353 --source api/pgc_channel_game/grpc_pgc_channel_game.proto --lang go --method /ga.api.pgc_channel_game.PgcChannelGameLogic/SetNextBombUser
36354:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 36354 --source api/pgc_channel_game/grpc_pgc_channel_game.proto --lang go --method /ga.api.pgc_channel_game.PgcChannelGameLogic/GetChannelGameInfo
36355:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 36355 --source api/pgc_channel_game/grpc_pgc_channel_game.proto --lang go --method /ga.api.pgc_channel_game.PgcChannelGameLogic/DigitalBombEnroll
36356:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 36356 --source api/pgc_channel_game/grpc_pgc_channel_game.proto --lang go --method /ga.api.pgc_channel_game.PgcChannelGameLogic/DigitalBombSelectGameUser
36357:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 36357 --source api/pgc_channel_game/grpc_pgc_channel_game.proto --lang go --method /ga.api.pgc_channel_game.PgcChannelGameLogic/DigitalBombSelectNumber
36358:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 36358 --source api/pgc_channel_game/grpc_pgc_channel_game.proto --lang go --method /ga.api.pgc_channel_game.PgcChannelGameLogic/AdventureEnroll
36359:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 36359 --source api/pgc_channel_game/grpc_pgc_channel_game.proto --lang go --method /ga.api.pgc_channel_game.PgcChannelGameLogic/AdventureSelectGameUser
36360:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 36360 --source api/pgc_channel_game/grpc_pgc_channel_game.proto --lang go --method /ga.api.pgc_channel_game.PgcChannelGameLogic/AdventureRandomSteps
36361:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 36361 --source api/pgc_channel_game/grpc_pgc_channel_game.proto --lang go --method /ga.api.pgc_channel_game.PgcChannelGameLogic/AdventureControlNext
