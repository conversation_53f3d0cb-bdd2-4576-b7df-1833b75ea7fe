apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  namespace: quicksilver
  name: grpc-game-screenshot-logic
spec:
  gateways:
  - istio-ingress/apiv2-gateway
  - istio-ingress/apiv2-gateway-internal
  hosts:
  - '*'
  http:
  - match:
    - uri:
        prefix: /ga.api.game_screenshot.GameScreenshotLogic/
    rewrite:
      uri: /logic.GameScreenshotLogic/
    delegate:
       name: game-screenshot-logic-delegator-80
       namespace: quicksilver


