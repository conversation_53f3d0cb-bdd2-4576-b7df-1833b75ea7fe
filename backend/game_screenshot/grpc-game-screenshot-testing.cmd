# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

36500:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 36500 --source api/game_screenshot/grpc_game_screenshot.proto --lang go --method /ga.api.game_screenshot.GameScreenshotLogic/GetGameScreenshotSummary
36501:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 36501 --source api/game_screenshot/grpc_game_screenshot.proto --lang go --method /ga.api.game_screenshot.GameScreenshotLogic/GetGameScreenshotSetting
36502:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 36502 --source api/game_screenshot/grpc_game_screenshot.proto --lang go --method /ga.api.game_screenshot.GameScreenshotLogic/UpdateGameScreenshotSetting
36503:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 36503 --source api/game_screenshot/grpc_game_screenshot.proto --lang go --method /ga.api.game_screenshot.GameScreenshotLogic/GetGameScreenshotHint
36504:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 36504 --source api/game_screenshot/grpc_game_screenshot.proto --lang go --method /ga.api.game_screenshot.GameScreenshotLogic/GetDiyGameScreenshot
36506:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 36506 --source api/game_screenshot/grpc_game_screenshot.proto --lang go --method /ga.api.game_screenshot.GameScreenshotLogic/SetDiyGameScreenshotFinish
36507:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 36507 --source api/game_screenshot/grpc_game_screenshot.proto --lang go --method /ga.api.game_screenshot.GameScreenshotLogic/GetGameScreenshotGuide
