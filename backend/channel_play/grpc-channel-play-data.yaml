cmdInfoList:
  - source: api/channel_play/grpc_channel_play.proto
    cmd: 3056
    lang: go
    method: /ga.api.channel_play.ChannelPlayLogic/ShowTopicChannelTabList
  - source: api/channel_play/grpc_channel_play.proto
    cmd: 3080
    lang: go
    method: /ga.api.channel_play.ChannelPlayLogic/ListTopicChannel
  - source: api/channel_play/grpc_channel_play.proto
    cmd: 3081
    lang: go
    method: /ga.api.channel_play.ChannelPlayLogic/GetSecondaryFilter
  - source: api/channel_play/grpc_channel_play.proto
    cmd: 3082
    lang: go
    method: /ga.api.channel_play.ChannelPlayLogic/GetSecondaryFilterByCategory
  - source: api/channel_play/grpc_channel_play.proto
    cmd: 3083
    lang: go
    method: /ga.api.channel_play.ChannelPlayLogic/GetDefaultRoomNameList
  - source: api/channel_play/grpc_channel_play.proto
    cmd: 3084
    lang: go
    method: /ga.api.channel_play.ChannelPlayLogic/PublishGangupChannel
  - source: api/channel_play/grpc_channel_play.proto
    cmd: 3085
    lang: go
    method: /ga.api.channel_play.ChannelPlayLogic/CancelGangupChannelPublish
  - source: api/channel_play/grpc_channel_play.proto
    cmd: 3086
    lang: go
    method: /ga.api.channel_play.ChannelPlayLogic/GetHomePageHeadConfig
  - source: api/channel_play/grpc_channel_play.proto
    cmd: 3087
    lang: go
    method: /ga.api.channel_play.ChannelPlayLogic/GetHotMiniGames
  - source: api/channel_play/grpc_channel_play.proto
    cmd: 3088
    lang: go
    method: /ga.api.channel_play.ChannelPlayLogic/GetQuickMiniGames
  - source: api/channel_play/grpc_channel_play.proto
    cmd: 3089
    lang: go
    method: /ga.api.channel_play.ChannelPlayLogic/GetPlayQuestions
  - source: api/channel_play/grpc_channel_play.proto
    cmd: 3090
    lang: go
    method: /ga.api.channel_play.ChannelPlayLogic/GameInsertFlowConfig
  - source: api/channel_play/grpc_channel_play.proto
    cmd: 3091
    lang: go
    method: /ga.api.channel_play.ChannelPlayLogic/GetHomePageGuide
  - source: api/channel_play/grpc_channel_play.proto
    cmd: 3092
    lang: go
    method: /ga.api.channel_play.ChannelPlayLogic/GetMoreTabConfig
  - source: api/channel_play/grpc_channel_play.proto
    cmd: 3093
    lang: go
    method: /ga.api.channel_play.ChannelPlayLogic/GetFilterItemByEntrance
  - source: api/channel_play/grpc_channel_play.proto
    cmd: 3094
    lang: go
    method: /ga.api.channel_play.ChannelPlayLogic/SetDIYFilterByEntrance
  - source: api/channel_play/grpc_channel_play.proto
    cmd: 3095
    lang: go
    method: /ga.api.channel_play.ChannelPlayLogic/GetDIYFilterByEntrance
  - source: api/channel_play/grpc_channel_play.proto
    cmd: 3096
    lang: go
    method: /ga.api.channel_play.ChannelPlayLogic/GetNegativeFeedBackInRoom
  - source: api/channel_play/grpc_channel_play.proto
    cmd: 3097
    lang: go
    method: /ga.api.channel_play.ChannelPlayLogic/ReportNegativeFeedBackInRoom
  - source: api/channel_play/grpc_channel_play.proto
    cmd: 3098
    lang: go
    method: /ga.api.channel_play.ChannelPlayLogic/GetPublishOptionGuide
  - source: api/channel_play/grpc_channel_play.proto
    cmd: 3099
    lang: go
    method: /ga.api.channel_play.ChannelPlayLogic/GetNewQuickMatchConfig
  - source: api/channel_play/grpc_channel_play.proto
    cmd: 3100
    lang: go
    method: /ga.api.channel_play.ChannelPlayLogic/GetTopicChannelCfgInfo
  - source: api/channel_play/grpc_channel_play.proto
    cmd: 3101
    lang: go
    method: /ga.api.channel_play.ChannelPlayLogic/SetUgcChannelPlayMode
  - source: api/channel_play/grpc_channel_play.proto
    cmd: 3102
    lang: go
    method: /ga.api.channel_play.ChannelPlayLogic/TypingStatusBroadcast
  - source: api/channel_play/grpc_channel_play.proto
    cmd: 3103
    lang: go
    method: /ga.api.channel_play.ChannelPlayLogic/GetChannelPlayModeGuide
  - source: api/channel_play/grpc_channel_play.proto
    cmd: 3104
    lang: go
    method: /ga.api.channel_play.ChannelPlayLogic/ReportDailyTask
  - source: api/channel_play/grpc_channel_play.proto
    cmd: 3105
    lang: go
    method: /ga.api.channel_play.ChannelPlayLogic/HomePageHeadConfigEnterCheck
  - source: api/channel_play/grpc_channel_play.proto
    cmd: 3106
    lang: go
    method: /ga.api.channel_play.ChannelPlayLogic/GetChannelListGuideConfigs
  - source: api/channel_play/grpc_channel_play.proto
    cmd: 3107
    lang: go
    method: /ga.api.channel_play.ChannelPlayLogic/GetTabInfos
  - source: api/channel_play/grpc_channel_play.proto
    cmd: 3112
    lang: go
    method: /ga.api.channel_play.ChannelPlayLogic/GetRecommendGames
  - source: api/channel_play/grpc_channel_play.proto
    cmd: 3113
    lang: go
    method: /ga.api.channel_play.ChannelPlayLogic/RefreshGameLabel
  - source: api/channel_play/grpc_channel_play.proto
    cmd: 3114
    lang: go
    method: /ga.api.channel_play.ChannelPlayLogic/GetSupportTabList
  - source: api/channel_play/grpc_channel_play.proto
    cmd: 3115
    lang: go
    method: /ga.api.channel_play.ChannelPlayLogic/GetChannelMicVolSet
  - source: api/channel_play/grpc_channel_play.proto
    cmd: 3116
    lang: go
    method: /ga.api.channel_play.ChannelPlayLogic/SetChannelMicVol
  - source: api/channel_play/grpc_channel_play.proto
    cmd: 31500
    lang: go
    method: /ga.api.channel_play.ChannelPlayLogic/CreateHobbyChannel
  - source: api/channel_play/grpc_channel_play.proto
    cmd: 31510
    lang: go
    method: /ga.api.channel_play.ChannelPlayLogic/GetGameHomePageDIYFilter
  - source: api/channel_play/grpc_channel_play.proto
    cmd: 31511
    lang: go
    method: /ga.api.channel_play.ChannelPlayLogic/SetGameHomePageDIYFilter
  - source: api/channel_play/grpc_channel_play.proto
    cmd: 31512
    lang: go
    method: /ga.api.channel_play.ChannelPlayLogic/GetGameHomePageFilter

