# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

3750:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 3750 --source api/super_player_dress/grpc_super_player_dress.proto --lang go --method /ga.api.super_player_dress.SuperPlayerDressLogic/GetDressConfigList
3751:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 3751 --source api/super_player_dress/grpc_super_player_dress.proto --lang go --method /ga.api.super_player_dress.SuperPlayerDressLogic/GetDressConfigMaxVersion
3752:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 3752 --source api/super_player_dress/grpc_super_player_dress.proto --lang go --method /ga.api.super_player_dress.SuperPlayerDressLogic/GetChannelCurrDressId
3753:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 3753 --source api/super_player_dress/grpc_super_player_dress.proto --lang go --method /ga.api.super_player_dress.SuperPlayerDressLogic/GetUserCurrSpecialConcernDressId
3754:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 3754 --source api/super_player_dress/grpc_super_player_dress.proto --lang go --method /ga.api.super_player_dress.SuperPlayerDressLogic/RemoveChannelCurrDressId
3755:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 3755 --source api/super_player_dress/grpc_super_player_dress.proto --lang go --method /ga.api.super_player_dress.SuperPlayerDressLogic/GetUserCurrChatBubbleDressId
3756:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 3756 --source api/super_player_dress/grpc_super_player_dress.proto --lang go --method /ga.api.super_player_dress.SuperPlayerDressLogic/GetUserCurrChatBgDressIdList
