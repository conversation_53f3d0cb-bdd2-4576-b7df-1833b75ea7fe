apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  namespace: quicksilver
  name: grpc-super-player-dress-logic
spec:
  gateways:
  - istio-ingress/apiv2-gateway
  - istio-ingress/apiv2-gateway-internal
  hosts:
  - '*'
  http:
  - match:
    - uri:
        prefix: /ga.api.super_player_dress.SuperPlayerDressLogic/
    rewrite:
      uri: /logic.SuperPlayerDressLogic/
    delegate:
       name: super-player-dress-logic-delegator-80
       namespace: quicksilver


