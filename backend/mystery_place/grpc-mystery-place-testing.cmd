# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

36000:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 36000 --source api/mystery_place/grpc_mystery_place.proto --lang go --method /ga.api.mystery_place.MysteryPlaceLogic/ListScenarioInfo
36001:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 36001 --source api/mystery_place/grpc_mystery_place.proto --lang go --method /ga.api.mystery_place.MysteryPlaceLogic/GetScenarioInfo
36002:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 36002 --source api/mystery_place/grpc_mystery_place.proto --lang go --method /ga.api.mystery_place.MysteryPlaceLogic/GetLoginTask
36003:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 36003 --source api/mystery_place/grpc_mystery_place.proto --lang go --method /ga.api.mystery_place.MysteryPlaceLogic/ReceiveLoginTaskAward
36004:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 36004 --source api/mystery_place/grpc_mystery_place.proto --lang go --method /ga.api.mystery_place.MysteryPlaceLogic/GetBalanceInfo
36005:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 36005 --source api/mystery_place/grpc_mystery_place.proto --lang go --method /ga.api.mystery_place.MysteryPlaceLogic/CheckScenarioRoom
36006:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 36006 --source api/mystery_place/grpc_mystery_place.proto --lang go --method /ga.api.mystery_place.MysteryPlaceLogic/Invite2MyRoom
36007:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 36007 --source api/mystery_place/grpc_mystery_place.proto --lang go --method /ga.api.mystery_place.MysteryPlaceLogic/GetInvite2MyRoomResult
36008:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 36008 --source api/mystery_place/grpc_mystery_place.proto --lang go --method /ga.api.mystery_place.MysteryPlaceLogic/ListRecommendedScenarioSimpleInfo
36009:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 36009 --source api/mystery_place/grpc_mystery_place.proto --lang go --method /ga.api.mystery_place.MysteryPlaceLogic/GetRecommendedScenarioDetailInfo
36010:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 36010 --source api/mystery_place/grpc_mystery_place.proto --lang go --method /ga.api.mystery_place.MysteryPlaceLogic/GetRoomShareLinkByTabId
36011:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 36011 --source api/mystery_place/grpc_mystery_place.proto --lang go --method /ga.api.mystery_place.MysteryPlaceLogic/MysteryPlaceChannelList
36012:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 36012 --source api/mystery_place/grpc_mystery_place.proto --lang go --method /ga.api.mystery_place.MysteryPlaceLogic/GetCommentPageParams
36013:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 36013 --source api/mystery_place/grpc_mystery_place.proto --lang go --method /ga.api.mystery_place.MysteryPlaceLogic/CommentToScenario
36020:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 36020 --source api/mystery_place/grpc_mystery_place.proto --lang go --method /ga.api.mystery_place.MysteryPlaceLogic/GetNewScenarioTip
36021:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 36021 --source api/mystery_place/grpc_mystery_place.proto --lang go --method /ga.api.mystery_place.MysteryPlaceLogic/MarkNewScenarioTipRead
36022:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 36022 --source api/mystery_place/grpc_mystery_place.proto --lang go --method /ga.api.mystery_place.MysteryPlaceLogic/MysteryPlaceClientConfig
36023:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 36023 --source api/mystery_place/grpc_mystery_place.proto --lang go --method /ga.api.mystery_place.MysteryPlaceLogic/GetPlayedScenarioRecordList
36024:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 36024 --source api/mystery_place/grpc_mystery_place.proto --lang go --method /ga.api.mystery_place.MysteryPlaceLogic/GetScenarioChapterSummary
36025:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 36025 --source api/mystery_place/grpc_mystery_place.proto --lang go --method /ga.api.mystery_place.MysteryPlaceLogic/SetPlayedScenarioRecordVisibility
36026:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 36026 --source api/mystery_place/grpc_mystery_place.proto --lang go --method /ga.api.mystery_place.MysteryPlaceLogic/GetPlayedScenarioRecordDetail
36027:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 36027 --source api/mystery_place/grpc_mystery_place.proto --lang go --method /ga.api.mystery_place.MysteryPlaceLogic/GetRcmdMiJingTab
36031:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 36031 --source api/mystery_place/grpc_mystery_place.proto --lang go --method /ga.api.mystery_place.MysteryPlaceLogic/ListRecommendedScenarioDetailInfo
36033:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 36033 --source api/mystery_place/grpc_mystery_place.proto --lang go --method /ga.api.mystery_place.MysteryPlaceLogic/IsUserHasScenarioFreeCoupons
36040:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 36040 --source api/mystery_place/grpc_mystery_place.proto --lang go --method /ga.api.mystery_place.MysteryPlaceLogic/HomePageBigTofu
36041:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 36041 --source api/mystery_place/grpc_mystery_place.proto --lang go --method /ga.api.mystery_place.MysteryPlaceLogic/HomePageRightTofu
36044:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 36044 --source api/mystery_place/grpc_mystery_place.proto --lang go --method /ga.api.mystery_place.MysteryPlaceLogic/GetChannelScenarioInfo
36065:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 36065 --source api/mystery_place/grpc_mystery_place.proto --lang go --method /ga.api.mystery_place.MysteryPlaceLogic/ReportScenarioSubscribeStatus
36067:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 36067 --source api/mystery_place/grpc_mystery_place.proto --lang go --method /ga.api.mystery_place.MysteryPlaceLogic/GetPlayedScenarioRecordListByID
36068:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 36068 --source api/mystery_place/grpc_mystery_place.proto --lang go --method /ga.api.mystery_place.MysteryPlaceLogic/CheckRisk
36431:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 36431 --source api/mystery_place/grpc_mystery_place.proto --lang go --method /ga.api.mystery_place.MysteryPlaceLogic/GetHotRankScenarioList
36432:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 36432 --source api/mystery_place/grpc_mystery_place.proto --lang go --method /ga.api.mystery_place.MysteryPlaceLogic/MijingShowCommentEntrance
