apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  namespace: quicksilver
  name: grpc-user-visitor-record-logic
spec:
  gateways:
  - istio-system/apiv2-gateway
  - istio-system/apiv2-gateway-internal
  hosts:
  - '*'
  http:
  - match:
    - uri:
        prefix: /ga.api.user_visitor_record.UserVisitorRecordLogic/
    rewrite:
      uri: /logic.UserVisitorRecordLogic/
    delegate:
       name: user-visitor-record-logic-delegator-80
       namespace: quicksilver


