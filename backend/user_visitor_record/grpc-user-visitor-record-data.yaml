cmdInfoList:
  - source: api/user_visitor_record/grpc_user_visitor_record.proto
    cmd: 30150
    lang: go
    method: /ga.api.user_visitor_record.UserVisitorRecordLogic/ReportUserVisitorRecord
  - source: api/user_visitor_record/grpc_user_visitor_record.proto
    cmd: 30151
    lang: go
    method: /ga.api.user_visitor_record.UserVisitorRecordLogic/GetUserVisitorRecordList
  - source: api/user_visitor_record/grpc_user_visitor_record.proto
    cmd: 30152
    lang: go
    method: /ga.api.user_visitor_record.UserVisitorRecordLogic/GetUserBeVisitorRecordList
  - source: api/user_visitor_record/grpc_user_visitor_record.proto
    cmd: 30153
    lang: go
    method: /ga.api.user_visitor_record.UserVisitorRecordLogic/GetUserBeVisitorRecordCount
  - source: api/user_visitor_record/grpc_user_visitor_record.proto
    cmd: 30154
    lang: go
    method: /ga.api.user_visitor_record.UserVisitorRecordLogic/GetAllTaskStatus
  - source: api/user_visitor_record/grpc_user_visitor_record.proto
    cmd: 30155
    lang: go
    method: /ga.api.user_visitor_record.UserVisitorRecordLogic/SetShowUserBeVisitorRecordCount
  - source: api/user_visitor_record/grpc_user_visitor_record.proto
    cmd: 30156
    lang: go
    method: /ga.api.user_visitor_record.UserVisitorRecordLogic/GetShowUserBeVisitorRecordCount
  - source: api/user_visitor_record/grpc_user_visitor_record.proto
    cmd: 30157
    lang: go
    method: /ga.api.user_visitor_record.UserVisitorRecordLogic/GetHideList
  - source: api/user_visitor_record/grpc_user_visitor_record.proto
    cmd: 30158
    lang: go
    method: /ga.api.user_visitor_record.UserVisitorRecordLogic/GetHideStatusByUid
  - source: api/user_visitor_record/grpc_user_visitor_record.proto
    cmd: 30159
    lang: go
    method: /ga.api.user_visitor_record.UserVisitorRecordLogic/SetHideStatusByUid

