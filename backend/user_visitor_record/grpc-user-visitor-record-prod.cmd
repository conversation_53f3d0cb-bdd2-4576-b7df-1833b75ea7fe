# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

30150:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 30150 --source api/user_visitor_record/grpc_user_visitor_record.proto --lang go --method /ga.api.user_visitor_record.UserVisitorRecordLogic/ReportUserVisitorRecord
30151:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 30151 --source api/user_visitor_record/grpc_user_visitor_record.proto --lang go --method /ga.api.user_visitor_record.UserVisitorRecordLogic/GetUserVisitorRecordList
30152:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 30152 --source api/user_visitor_record/grpc_user_visitor_record.proto --lang go --method /ga.api.user_visitor_record.UserVisitorRecordLogic/GetUserBeVisitorRecordList
30153:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 30153 --source api/user_visitor_record/grpc_user_visitor_record.proto --lang go --method /ga.api.user_visitor_record.UserVisitorRecordLogic/GetUserBeVisitorRecordCount
30154:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 30154 --source api/user_visitor_record/grpc_user_visitor_record.proto --lang go --method /ga.api.user_visitor_record.UserVisitorRecordLogic/GetAllTaskStatus
30155:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 30155 --source api/user_visitor_record/grpc_user_visitor_record.proto --lang go --method /ga.api.user_visitor_record.UserVisitorRecordLogic/SetShowUserBeVisitorRecordCount
30156:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 30156 --source api/user_visitor_record/grpc_user_visitor_record.proto --lang go --method /ga.api.user_visitor_record.UserVisitorRecordLogic/GetShowUserBeVisitorRecordCount
30157:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 30157 --source api/user_visitor_record/grpc_user_visitor_record.proto --lang go --method /ga.api.user_visitor_record.UserVisitorRecordLogic/GetHideList
30158:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 30158 --source api/user_visitor_record/grpc_user_visitor_record.proto --lang go --method /ga.api.user_visitor_record.UserVisitorRecordLogic/GetHideStatusByUid
30159:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 30159 --source api/user_visitor_record/grpc_user_visitor_record.proto --lang go --method /ga.api.user_visitor_record.UserVisitorRecordLogic/SetHideStatusByUid
