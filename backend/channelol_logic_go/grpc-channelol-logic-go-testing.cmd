# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

2050:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 2050 --source api/channelol_logic_go/grpc_channelol_logic_go.proto --lang go --method /ga.api.channelol_logic_go.ChannelolLogicGo/ChannelGetHistory
3501:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 3501 --source api/channelol_logic_go/grpc_channelol_logic_go.proto --lang go --method /ga.api.channelol_logic_go.ChannelolLogicGo/CChannelGetUnderTheMicroList
436:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 436 --source api/channelol_logic_go/grpc_channelol_logic_go.proto --lang go --method /ga.api.channelol_logic_go.ChannelolLogicGo/QuickJoinChannel
439:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 439 --source api/channelol_logic_go/grpc_channelol_logic_go.proto --lang go --method /ga.api.channelol_logic_go.ChannelolLogicGo/KickoutChannelMember
459:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 459 --source api/channelol_logic_go/grpc_channelol_logic_go.proto --lang go --method /ga.api.channelol_logic_go.ChannelolLogicGo/ChannelGetMemberInfo
50856:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 50856 --source api/channelol_logic_go/grpc_channelol_logic_go.proto --lang go --method /ga.api.channelol_logic_go.ChannelolLogicGo/GetChannelOnlineMember
50876:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 50876 --source api/channelol_logic_go/grpc_channelol_logic_go.proto --lang go --method /ga.api.channelol_logic_go.ChannelolLogicGo/ChannelHeartbeatUpdate
