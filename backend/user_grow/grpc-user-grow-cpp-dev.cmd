# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

2098:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 2098 --source api/user_grow/grpc_user_grow_cpp.proto --lang cpp --method /ga.api.user_grow.UserGrowLogic/ChannelUserSetVisibleStatus
2099:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 2099 --source api/user_grow/grpc_user_grow_cpp.proto --lang cpp --method /ga.api.user_grow.UserGrowLogic/BatchGetNobilityInfos
5066:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 5066 --source api/user_grow/grpc_user_grow_cpp.proto --lang cpp --method /ga.api.user_grow.UserGrowLogic/ChannelGetTrumpetLeftCnt
5068:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 5068 --source api/user_grow/grpc_user_grow_cpp.proto --lang cpp --method /ga.api.user_grow.UserGrowLogic/GetNobilityInfo
5069:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 5069 --source api/user_grow/grpc_user_grow_cpp.proto --lang cpp --method /ga.api.user_grow.UserGrowLogic/GetNobilityDetailInfo
