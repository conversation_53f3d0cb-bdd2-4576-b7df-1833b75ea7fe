# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

31520:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 31520 --source api/music_topic_channel/grpc_music_topic_channel.proto --lang go --method /ga.api.music_topic_channel.MusicTopicChannelLogic/GetMusicChannelFilterV2
31521:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 31521 --source api/music_topic_channel/grpc_music_topic_channel.proto --lang go --method /ga.api.music_topic_channel.MusicTopicChannelLogic/ListHobbyChannelV2
31522:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 31522 --source api/music_topic_channel/grpc_music_topic_channel.proto --lang go --method /ga.api.music_topic_channel.MusicTopicChannelLogic/GetMusicHomePageViewV2
31523:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 31523 --source api/music_topic_channel/grpc_music_topic_channel.proto --lang go --method /ga.api.music_topic_channel.MusicTopicChannelLogic/QuickMatchHobbyChannelV2
31524:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 31524 --source api/music_topic_channel/grpc_music_topic_channel.proto --lang go --method /ga.api.music_topic_channel.MusicTopicChannelLogic/PublishMusicChannel
31525:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 31525 --source api/music_topic_channel/grpc_music_topic_channel.proto --lang go --method /ga.api.music_topic_channel.MusicTopicChannelLogic/CancelMusicChannelPublish
31526:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 31526 --source api/music_topic_channel/grpc_music_topic_channel.proto --lang go --method /ga.api.music_topic_channel.MusicTopicChannelLogic/GetMusicFilterItemByIds
31527:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 31527 --source api/music_topic_channel/grpc_music_topic_channel.proto --lang go --method /ga.api.music_topic_channel.MusicTopicChannelLogic/ListMusicChannels
31528:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 31528 --source api/music_topic_channel/grpc_music_topic_channel.proto --lang go --method /ga.api.music_topic_channel.MusicTopicChannelLogic/GetTabPublishHotRcmd
31529:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 31529 --source api/music_topic_channel/grpc_music_topic_channel.proto --lang go --method /ga.api.music_topic_channel.MusicTopicChannelLogic/GetResourceConfigByChannelId
31530:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 31530 --source api/music_topic_channel/grpc_music_topic_channel.proto --lang go --method /ga.api.music_topic_channel.MusicTopicChannelLogic/MuseGetTopicChannelInfo
31531:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 31531 --source api/music_topic_channel/grpc_music_topic_channel.proto --lang go --method /ga.api.music_topic_channel.MusicTopicChannelLogic/GetAssociateRevChannels
31532:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 31532 --source api/music_topic_channel/grpc_music_topic_channel.proto --lang go --method /ga.api.music_topic_channel.MusicTopicChannelLogic/ListMuseSocialCommunityChannels
31535:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 31535 --source api/music_topic_channel/grpc_music_topic_channel.proto --lang go --method /ga.api.music_topic_channel.MusicTopicChannelLogic/ListChannelPreferenceKeywords
