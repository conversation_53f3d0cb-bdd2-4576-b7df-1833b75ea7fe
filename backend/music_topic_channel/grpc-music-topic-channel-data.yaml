cmdInfoList:
  - source: api/music_topic_channel/grpc_music_topic_channel.proto
    cmd: 31520
    lang: go
    method: /ga.api.music_topic_channel.MusicTopicChannelLogic/GetMusicChannelFilterV2
  - source: api/music_topic_channel/grpc_music_topic_channel.proto
    cmd: 31521
    lang: go
    method: /ga.api.music_topic_channel.MusicTopicChannelLogic/ListHobbyChannelV2
  - source: api/music_topic_channel/grpc_music_topic_channel.proto
    cmd: 31522
    lang: go
    method: /ga.api.music_topic_channel.MusicTopicChannelLogic/GetMusicHomePageViewV2
  - source: api/music_topic_channel/grpc_music_topic_channel.proto
    cmd: 31523
    lang: go
    method: /ga.api.music_topic_channel.MusicTopicChannelLogic/QuickMatchHobbyChannelV2
  - source: api/music_topic_channel/grpc_music_topic_channel.proto
    cmd: 31524
    lang: go
    method: /ga.api.music_topic_channel.MusicTopicChannelLogic/PublishMusicChannel
  - source: api/music_topic_channel/grpc_music_topic_channel.proto
    cmd: 31525
    lang: go
    method: /ga.api.music_topic_channel.MusicTopicChannelLogic/CancelMusicChannelPublish
  - source: api/music_topic_channel/grpc_music_topic_channel.proto
    cmd: 31526
    lang: go
    method: /ga.api.music_topic_channel.MusicTopicChannelLogic/GetMusicFilterItemByIds
  - source: api/music_topic_channel/grpc_music_topic_channel.proto
    cmd: 31527
    lang: go
    method: /ga.api.music_topic_channel.MusicTopicChannelLogic/ListMusicChannels
  - source: api/music_topic_channel/grpc_music_topic_channel.proto
    cmd: 31528
    lang: go
    method: /ga.api.music_topic_channel.MusicTopicChannelLogic/GetTabPublishHotRcmd
  - source: api/music_topic_channel/grpc_music_topic_channel.proto
    cmd: 31529
    lang: go
    method: /ga.api.music_topic_channel.MusicTopicChannelLogic/GetResourceConfigByChannelId
  - source: api/music_topic_channel/grpc_music_topic_channel.proto
    cmd: 31530
    lang: go
    method: /ga.api.music_topic_channel.MusicTopicChannelLogic/MuseGetTopicChannelInfo
  - source: api/music_topic_channel/grpc_music_topic_channel.proto
    cmd: 31531
    lang: go
    method: /ga.api.music_topic_channel.MusicTopicChannelLogic/GetAssociateRevChannels
  - source: api/music_topic_channel/grpc_music_topic_channel.proto
    cmd: 31532
    lang: go
    method: /ga.api.music_topic_channel.MusicTopicChannelLogic/ListMuseSocialCommunityChannels
  - source: api/music_topic_channel/grpc_music_topic_channel.proto
    cmd: 31535
    lang: go
    method: /ga.api.music_topic_channel.MusicTopicChannelLogic/ListChannelPreferenceKeywords

