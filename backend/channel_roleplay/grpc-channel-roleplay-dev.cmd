# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

104001:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 104001 --source api/channel_roleplay/grpc_channel_roleplay.proto --lang go --method /ga.api.channel_roleplay.ChannelRoleplayLogic/GetChannelHoldMicUserRoleList
104002:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 104002 --source api/channel_roleplay/grpc_channel_roleplay.proto --lang go --method /ga.api.channel_roleplay.ChannelRoleplayLogic/SetMyChannelRole
104003:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 104003 --source api/channel_roleplay/grpc_channel_roleplay.proto --lang go --method /ga.api.channel_roleplay.ChannelRoleplayLogic/EnterBox
104004:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 104004 --source api/channel_roleplay/grpc_channel_roleplay.proto --lang go --method /ga.api.channel_roleplay.ChannelRoleplayLogic/ExitBox
104005:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 104005 --source api/channel_roleplay/grpc_channel_roleplay.proto --lang go --method /ga.api.channel_roleplay.ChannelRoleplayLogic/HandleApplyBox
104006:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 104006 --source api/channel_roleplay/grpc_channel_roleplay.proto --lang go --method /ga.api.channel_roleplay.ChannelRoleplayLogic/GetBoxInfo
104007:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 104007 --source api/channel_roleplay/grpc_channel_roleplay.proto --lang go --method /ga.api.channel_roleplay.ChannelRoleplayLogic/GetBoxInfosByLimit
104008:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 104008 --source api/channel_roleplay/grpc_channel_roleplay.proto --lang go --method /ga.api.channel_roleplay.ChannelRoleplayLogic/GetChannelUserRoleList
50052:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 50052 --source api/channel_roleplay/grpc_channel_roleplay.proto --lang go --method /ga.api.channel_roleplay.ChannelRoleplayLogic/OpenCommonMic
50053:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 50053 --source api/channel_roleplay/grpc_channel_roleplay.proto --lang go --method /ga.api.channel_roleplay.ChannelRoleplayLogic/UpsertBoxInfo
50054:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 50054 --source api/channel_roleplay/grpc_channel_roleplay.proto --lang go --method /ga.api.channel_roleplay.ChannelRoleplayLogic/DelBoxInfo
