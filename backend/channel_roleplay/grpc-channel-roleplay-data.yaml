cmdInfoList:
  - source: api/channel_roleplay/grpc_channel_roleplay.proto
    cmd: 50052
    lang: go
    method: /ga.api.channel_roleplay.ChannelRoleplayLogic/OpenCommonMic
  - source: api/channel_roleplay/grpc_channel_roleplay.proto
    cmd: 50053
    lang: go
    method: /ga.api.channel_roleplay.ChannelRoleplayLogic/UpsertBoxInfo
  - source: api/channel_roleplay/grpc_channel_roleplay.proto
    cmd: 50054
    lang: go
    method: /ga.api.channel_roleplay.ChannelRoleplayLogic/DelBoxInfo
  - source: api/channel_roleplay/grpc_channel_roleplay.proto
    cmd: 104001
    lang: go
    method: /ga.api.channel_roleplay.ChannelRoleplayLogic/GetChannelHoldMicUserRoleList
  - source: api/channel_roleplay/grpc_channel_roleplay.proto
    cmd: 104002
    lang: go
    method: /ga.api.channel_roleplay.ChannelRoleplayLogic/SetMyChannelRole
  - source: api/channel_roleplay/grpc_channel_roleplay.proto
    cmd: 104003
    lang: go
    method: /ga.api.channel_roleplay.ChannelRoleplayLogic/EnterBox
  - source: api/channel_roleplay/grpc_channel_roleplay.proto
    cmd: 104004
    lang: go
    method: /ga.api.channel_roleplay.ChannelRoleplayLogic/ExitBox
  - source: api/channel_roleplay/grpc_channel_roleplay.proto
    cmd: 104005
    lang: go
    method: /ga.api.channel_roleplay.ChannelRoleplayLogic/HandleApplyBox
  - source: api/channel_roleplay/grpc_channel_roleplay.proto
    cmd: 104006
    lang: go
    method: /ga.api.channel_roleplay.ChannelRoleplayLogic/GetBoxInfo
  - source: api/channel_roleplay/grpc_channel_roleplay.proto
    cmd: 104007
    lang: go
    method: /ga.api.channel_roleplay.ChannelRoleplayLogic/GetBoxInfosByLimit
  - source: api/channel_roleplay/grpc_channel_roleplay.proto
    cmd: 104008
    lang: go
    method: /ga.api.channel_roleplay.ChannelRoleplayLogic/GetChannelUserRoleList

