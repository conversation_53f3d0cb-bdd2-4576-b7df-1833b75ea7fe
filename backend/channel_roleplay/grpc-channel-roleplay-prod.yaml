apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  namespace: quicksilver
  name: grpc-channel-roleplay-logic
spec:
  gateways:
  - quicksilver/tt-api-gateway
  - quicksilver/tt-api-gateway-internal
  hosts:
  - '*'
  http:
  - match:
    - uri:
        prefix: /ga.api.channel_roleplay.ChannelRoleplayLogic/
    rewrite:
      uri: /logic.ChannelRoleplayLogic/
    delegate:
       name: channel-roleplay-logic-delegator-80
       namespace: quicksilver


