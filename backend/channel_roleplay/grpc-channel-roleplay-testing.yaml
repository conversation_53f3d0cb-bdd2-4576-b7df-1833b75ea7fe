apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  namespace: quicksilver
  name: grpc-channel-roleplay-logic
spec:
  gateways:
  - istio-ingress/apiv2-gateway
  - istio-ingress/apiv2-gateway-internal
  hosts:
  - '*'
  http:
  - match:
    - uri:
        prefix: /ga.api.channel_roleplay.ChannelRoleplayLogic/
    rewrite:
      uri: /logic.ChannelRoleplayLogic/
    delegate:
       name: channel-roleplay-logic-delegator-80
       namespace: quicksilver


