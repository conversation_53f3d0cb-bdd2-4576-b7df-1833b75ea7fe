# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

30211:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 30211 --source api/master_apprentice/grpc_master_apprentice.proto --lang go --method /ga.api.master_apprentice.MasterApprenticeLogic/MasterInviteMsg
30212:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 30212 --source api/master_apprentice/grpc_master_apprentice.proto --lang go --method /ga.api.master_apprentice.MasterApprenticeLogic/ApprenticeEstablishMsg
30213:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 30213 --source api/master_apprentice/grpc_master_apprentice.proto --lang go --method /ga.api.master_apprentice.MasterApprenticeLogic/EntranceInfo
