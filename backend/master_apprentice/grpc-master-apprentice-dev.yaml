apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  namespace: quicksilver
  name: grpc-master-apprentice-logic
spec:
  gateways:
  - istio-system/apiv2-gateway
  - istio-system/apiv2-gateway-internal
  hosts:
  - '*'
  http:
  - match:
    - uri:
        prefix: /ga.api.master_apprentice.MasterApprenticeLogic/
    rewrite:
      uri: /logic.MasterApprenticeLoigc/
    delegate:
       name: master-apprentice-logic-delegator-80
       namespace: quicksilver


