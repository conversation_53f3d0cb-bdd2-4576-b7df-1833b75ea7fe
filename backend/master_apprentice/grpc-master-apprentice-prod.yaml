apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  namespace: quicksilver
  name: grpc-master-apprentice-logic
spec:
  gateways:
  - quicksilver/tt-api-gateway
  - quicksilver/tt-api-gateway-internal
  hosts:
  - '*'
  http:
  - match:
    - uri:
        prefix: /ga.api.master_apprentice.MasterApprenticeLogic/
    rewrite:
      uri: /logic.MasterApprenticeLoigc/
    delegate:
       name: master-apprentice-logic-delegator-80
       namespace: quicksilver


