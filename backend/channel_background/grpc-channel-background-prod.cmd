# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

2020:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 2020 --source api/channel_background/grpc_channel_background.proto --lang go --method /ga.api.channel_background.ChannelBackgroundLogic/GetCurChannelBackgroundInfo
2021:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 2021 --source api/channel_background/grpc_channel_background.proto --lang go --method /ga.api.channel_background.ChannelBackgroundLogic/ChangeCurChannelBackground
2022:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 2022 --source api/channel_background/grpc_channel_background.proto --lang go --method /ga.api.channel_background.ChannelBackgroundLogic/GetChannelBackgroundInfoList
2023:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 2023 --source api/channel_background/grpc_channel_background.proto --lang go --method /ga.api.channel_background.ChannelBackgroundLogic/CheckChannelBackgroundUpdate
2024:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 2024 --source api/channel_background/grpc_channel_background.proto --lang go --method /ga.api.channel_background.ChannelBackgroundLogic/GetRecommendBackground
2025:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 2025 --source api/channel_background/grpc_channel_background.proto --lang go --method /ga.api.channel_background.ChannelBackgroundLogic/GetKHBackgroundInfoList
2026:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 2026 --source api/channel_background/grpc_channel_background.proto --lang go --method /ga.api.channel_background.ChannelBackgroundLogic/GetPclfgCurChannelBackground
2027:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 2027 --source api/channel_background/grpc_channel_background.proto --lang go --method /ga.api.channel_background.ChannelBackgroundLogic/SetPclfgCurChannelBackground
2028:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 2028 --source api/channel_background/grpc_channel_background.proto --lang go --method /ga.api.channel_background.ChannelBackgroundLogic/GetPclfgChannelBackgroundList
