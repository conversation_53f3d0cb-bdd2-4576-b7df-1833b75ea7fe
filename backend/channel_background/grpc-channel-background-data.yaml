cmdInfoList:
  - source: api/channel_background/grpc_channel_background.proto
    cmd: 2020
    lang: go
    method: /ga.api.channel_background.ChannelBackgroundLogic/GetCurChannelBackgroundInfo
  - source: api/channel_background/grpc_channel_background.proto
    cmd: 2021
    lang: go
    method: /ga.api.channel_background.ChannelBackgroundLogic/ChangeCurChannelBackground
  - source: api/channel_background/grpc_channel_background.proto
    cmd: 2022
    lang: go
    method: /ga.api.channel_background.ChannelBackgroundLogic/GetChannelBackgroundInfoList
  - source: api/channel_background/grpc_channel_background.proto
    cmd: 2023
    lang: go
    method: /ga.api.channel_background.ChannelBackgroundLogic/CheckChannelBackgroundUpdate
  - source: api/channel_background/grpc_channel_background.proto
    cmd: 2024
    lang: go
    method: /ga.api.channel_background.ChannelBackgroundLogic/GetRecommendBackground
  - source: api/channel_background/grpc_channel_background.proto
    cmd: 2025
    lang: go
    method: /ga.api.channel_background.ChannelBackgroundLogic/GetKHBackgroundInfoList
  - source: api/channel_background/grpc_channel_background.proto
    cmd: 2026
    lang: go
    method: /ga.api.channel_background.ChannelBackgroundLogic/GetPclfgCurChannelBackground
  - source: api/channel_background/grpc_channel_background.proto
    cmd: 2027
    lang: go
    method: /ga.api.channel_background.ChannelBackgroundLogic/SetPclfgCurChannelBackground
  - source: api/channel_background/grpc_channel_background.proto
    cmd: 2028
    lang: go
    method: /ga.api.channel_background.ChannelBackgroundLogic/GetPclfgChannelBackgroundList

