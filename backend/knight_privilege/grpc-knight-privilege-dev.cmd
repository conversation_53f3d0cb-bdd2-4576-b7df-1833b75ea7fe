# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

3774:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 3774 --source api/knight_privilege/grpc_knight_privilege.proto --lang go --method /ga.api.knight_privilege.KnightPrivilegeLogic/GetKnightGroupEntry
3775:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 3775 --source api/knight_privilege/grpc_knight_privilege.proto --lang go --method /ga.api.knight_privilege.KnightPrivilegeLogic/GetKnightGroupDetialInfo
3776:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 3776 --source api/knight_privilege/grpc_knight_privilege.proto --lang go --method /ga.api.knight_privilege.KnightPrivilegeLogic/GetKnightGroupOpenStatus
3777:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 3777 --source api/knight_privilege/grpc_knight_privilege.proto --lang go --method /ga.api.knight_privilege.KnightPrivilegeLogic/GetKnightCardInfo
