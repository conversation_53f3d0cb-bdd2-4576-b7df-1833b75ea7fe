apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  namespace: quicksilver
  name: grpc-knight-privilege-logic
spec:
  gateways:
  - quicksilver/tt-api-gateway
  - quicksilver/tt-api-gateway-internal
  hosts:
  - '*'
  http:
  - match:
    - uri:
        prefix: /ga.api.knight_privilege.KnightPrivilegeLogic/
    rewrite:
      uri: /logic.knightprivilege.KnightPrivilegeLogic/
    delegate:
       name: knight-privilege-logic-delegator-80
       namespace: quicksilver


