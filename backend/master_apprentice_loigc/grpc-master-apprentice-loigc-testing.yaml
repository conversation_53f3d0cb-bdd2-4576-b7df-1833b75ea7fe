apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  namespace: quicksilver
  name: grpc-master-apprentice-loigc-logic
spec:
  gateways:
  - istio-ingress/apiv2-gateway
  - istio-ingress/apiv2-gateway-internal
  hosts:
  - '*'
  http:
  - match:
    - uri:
        prefix: /ga.api.master_apprentice_loigc.MasterApprenticeLoigc/
    rewrite:
      uri: /logic.MasterApprenticeLoigc/
    delegate:
       name: master-apprentice-logic-delegator-80
       namespace: quicksilver


