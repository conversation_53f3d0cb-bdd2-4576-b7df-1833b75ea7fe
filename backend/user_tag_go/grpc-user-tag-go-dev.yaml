apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  namespace: quicksilver
  name: grpc-user-tag-go-logic
spec:
  gateways:
  - istio-system/apiv2-gateway
  - istio-system/apiv2-gateway-internal
  hosts:
  - '*'
  http:
  - match:
    - uri:
        prefix: /ga.api.user_tag_go.UserTagLogicGo/
    rewrite:
      uri: /logic.UserTagLogicGo/
    delegate:
       name: user-tag-logic-go-delegator-80
       namespace: quicksilver


