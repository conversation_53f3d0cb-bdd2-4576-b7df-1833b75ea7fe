apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  namespace: quicksilver
  name: grpc-gnobility-logic
spec:
  gateways:
  - quicksilver/tt-api-gateway
  - quicksilver/tt-api-gateway-internal
  hosts:
  - '*'
  http:
  - match:
    - uri:
        prefix: /ga.api.gnobility.GnobilityLogic/
    rewrite:
      uri: /logic.GnobilityLogic/
    delegate:
       name: gnobility-logic-delegator-80
       namespace: quicksilver


