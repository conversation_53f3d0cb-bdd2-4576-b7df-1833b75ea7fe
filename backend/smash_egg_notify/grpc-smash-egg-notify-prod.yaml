apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  namespace: quicksilver
  name: grpc-smash-egg-notify-logic
spec:
  gateways:
  - quicksilver/tt-api-gateway
  - quicksilver/tt-api-gateway-internal
  hosts:
  - '*'
  http:
  - match:
    - uri:
        prefix: /ga.api.smash_egg_notify.SmashEggNotifyLogic/
    rewrite:
      uri: /smash_egg_notify_logic.SmashEggNotifyLogic/
    delegate:
       name: smash-egg-notify-logic-delegator-80
       namespace: quicksilver


