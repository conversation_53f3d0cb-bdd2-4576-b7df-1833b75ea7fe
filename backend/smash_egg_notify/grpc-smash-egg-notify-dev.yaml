apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  namespace: quicksilver
  name: grpc-smash-egg-notify-logic
spec:
  gateways:
  - istio-system/apiv2-gateway
  - istio-system/apiv2-gateway-internal
  hosts:
  - '*'
  http:
  - match:
    - uri:
        prefix: /ga.api.smash_egg_notify.SmashEggNotifyLogic/
    rewrite:
      uri: /smash_egg_notify_logic.SmashEggNotifyLogic/
    delegate:
       name: smash-egg-notify-logic-delegator-80
       namespace: quicksilver


