apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  namespace: quicksilver
  name: grpc-revenue-nameplate-logic
spec:
  gateways:
  - istio-system/apiv2-gateway
  - istio-system/apiv2-gateway-internal
  hosts:
  - '*'
  http:
  - match:
    - uri:
        prefix: /ga.api.revenue_nameplate.RevenueNameplateLogic/
    rewrite:
      uri: /logic.RevenueNameplateLogic/
    delegate:
       name: revenue-nameplate-logic-delegator-80
       namespace: quicksilver


