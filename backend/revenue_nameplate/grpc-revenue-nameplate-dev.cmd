# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

3861:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 3861 --source api/revenue_nameplate/grpc_revenue_nameplate.proto --lang go --method /ga.api.revenue_nameplate.RevenueNameplateLogic/GetUserNameplateInfo
3862:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 3862 --source api/revenue_nameplate/grpc_revenue_nameplate.proto --lang go --method /ga.api.revenue_nameplate.RevenueNameplateLogic/SetUserNameplateInfo
3863:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 3863 --source api/revenue_nameplate/grpc_revenue_nameplate.proto --lang go --method /ga.api.revenue_nameplate.RevenueNameplateLogic/GetUserAllNameplateList
