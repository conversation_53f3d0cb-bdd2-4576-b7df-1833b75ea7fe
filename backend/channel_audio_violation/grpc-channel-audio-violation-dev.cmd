# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

50862:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 50862 --source api/channel_audio_violation/grpc_channel_audio_violation.proto --lang go --method /ga.api.channel_audio_violation.ChannelAudioViolationLogic/StartJudgment
50863:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 50863 --source api/channel_audio_violation/grpc_channel_audio_violation.proto --lang go --method /ga.api.channel_audio_violation.ChannelAudioViolationLogic/JudgmentVote
50864:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 50864 --source api/channel_audio_violation/grpc_channel_audio_violation.proto --lang go --method /ga.api.channel_audio_violation.ChannelAudioViolationLogic/JudgmentAppeal
50865:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 50865 --source api/channel_audio_violation/grpc_channel_audio_violation.proto --lang go --method /ga.api.channel_audio_violation.ChannelAudioViolationLogic/ReportAudioFile
50866:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 50866 --source api/channel_audio_violation/grpc_channel_audio_violation.proto --lang go --method /ga.api.channel_audio_violation.ChannelAudioViolationLogic/IsDetectEnabled
50877:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 50877 --source api/channel_audio_violation/grpc_channel_audio_violation.proto --lang go --method /ga.api.channel_audio_violation.ChannelAudioViolationLogic/GetAudioSamplingConfig
50878:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 50878 --source api/channel_audio_violation/grpc_channel_audio_violation.proto --lang go --method /ga.api.channel_audio_violation.ChannelAudioViolationLogic/IsSampledAudioNeedSave
50879:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 50879 --source api/channel_audio_violation/grpc_channel_audio_violation.proto --lang go --method /ga.api.channel_audio_violation.ChannelAudioViolationLogic/ReportSampledAudio
