cmdInfoList:
  - source: api/channel_audio_violation/grpc_channel_audio_violation.proto
    cmd: 50862
    lang: go
    method: /ga.api.channel_audio_violation.ChannelAudioViolationLogic/StartJudgment
  - source: api/channel_audio_violation/grpc_channel_audio_violation.proto
    cmd: 50863
    lang: go
    method: /ga.api.channel_audio_violation.ChannelAudioViolationLogic/JudgmentVote
  - source: api/channel_audio_violation/grpc_channel_audio_violation.proto
    cmd: 50864
    lang: go
    method: /ga.api.channel_audio_violation.ChannelAudioViolationLogic/JudgmentAppeal
  - source: api/channel_audio_violation/grpc_channel_audio_violation.proto
    cmd: 50865
    lang: go
    method: /ga.api.channel_audio_violation.ChannelAudioViolationLogic/ReportAudioFile
  - source: api/channel_audio_violation/grpc_channel_audio_violation.proto
    cmd: 50866
    lang: go
    method: /ga.api.channel_audio_violation.ChannelAudioViolationLogic/IsDetectEnabled
  - source: api/channel_audio_violation/grpc_channel_audio_violation.proto
    cmd: 50877
    lang: go
    method: /ga.api.channel_audio_violation.ChannelAudioViolationLogic/GetAudioSamplingConfig
  - source: api/channel_audio_violation/grpc_channel_audio_violation.proto
    cmd: 50878
    lang: go
    method: /ga.api.channel_audio_violation.ChannelAudioViolationLogic/IsSampledAudioNeedSave
  - source: api/channel_audio_violation/grpc_channel_audio_violation.proto
    cmd: 50879
    lang: go
    method: /ga.api.channel_audio_violation.ChannelAudioViolationLogic/ReportSampledAudio

