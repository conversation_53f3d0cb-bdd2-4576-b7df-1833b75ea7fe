# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

36591:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 36591 --source api/revenue_ext_game/grpc_revenue_ext_game.proto --lang go --method /ga.api.revenue_ext_game.RevenueExtGameLogic/MountExtGame
36592:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 36592 --source api/revenue_ext_game/grpc_revenue_ext_game.proto --lang go --method /ga.api.revenue_ext_game.RevenueExtGameLogic/UnmountExtGame
36593:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 36593 --source api/revenue_ext_game/grpc_revenue_ext_game.proto --lang go --method /ga.api.revenue_ext_game.RevenueExtGameLogic/GetExtGameCfgList
36594:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 36594 --source api/revenue_ext_game/grpc_revenue_ext_game.proto --lang go --method /ga.api.revenue_ext_game.RevenueExtGameLogic/ReportUserWantPlay
36595:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 36595 --source api/revenue_ext_game/grpc_revenue_ext_game.proto --lang go --method /ga.api.revenue_ext_game.RevenueExtGameLogic/GetUserExtGameInfo
36596:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 36596 --source api/revenue_ext_game/grpc_revenue_ext_game.proto --lang go --method /ga.api.revenue_ext_game.RevenueExtGameLogic/GetChannelExtGameAccess
36597:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 36597 --source api/revenue_ext_game/grpc_revenue_ext_game.proto --lang go --method /ga.api.revenue_ext_game.RevenueExtGameLogic/GetExtGameScoreRank
36598:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 36598 --source api/revenue_ext_game/grpc_revenue_ext_game.proto --lang go --method /ga.api.revenue_ext_game.RevenueExtGameLogic/GetExtGameRankNameplate
51301:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 51301 --source api/revenue_ext_game/grpc_revenue_ext_game.proto --lang go --method /ga.api.revenue_ext_game.RevenueExtGameLogic/GetExtGameInfoList
51302:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 51302 --source api/revenue_ext_game/grpc_revenue_ext_game.proto --lang go --method /ga.api.revenue_ext_game.RevenueExtGameLogic/GetExtGameAccessList
51303:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 51303 --source api/revenue_ext_game/grpc_revenue_ext_game.proto --lang go --method /ga.api.revenue_ext_game.RevenueExtGameLogic/GetExtGameOpenId
51304:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 51304 --source api/revenue_ext_game/grpc_revenue_ext_game.proto --lang go --method /ga.api.revenue_ext_game.RevenueExtGameLogic/GetExtGameJsCode
51305:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 51305 --source api/revenue_ext_game/grpc_revenue_ext_game.proto --lang go --method /ga.api.revenue_ext_game.RevenueExtGameLogic/GetExtGameWhiteChannel
51306:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 51306 --source api/revenue_ext_game/grpc_revenue_ext_game.proto --lang go --method /ga.api.revenue_ext_game.RevenueExtGameLogic/CheckExtGameChannel
51307:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 51307 --source api/revenue_ext_game/grpc_revenue_ext_game.proto --lang go --method /ga.api.revenue_ext_game.RevenueExtGameLogic/ExtGameLoginCheck
