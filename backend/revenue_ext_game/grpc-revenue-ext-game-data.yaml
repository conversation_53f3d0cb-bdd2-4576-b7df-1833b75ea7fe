cmdInfoList:
  - source: api/revenue_ext_game/grpc_revenue_ext_game.proto
    cmd: 36591
    lang: go
    method: /ga.api.revenue_ext_game.RevenueExtGameLogic/MountExtGame
  - source: api/revenue_ext_game/grpc_revenue_ext_game.proto
    cmd: 36592
    lang: go
    method: /ga.api.revenue_ext_game.RevenueExtGameLogic/UnmountExtGame
  - source: api/revenue_ext_game/grpc_revenue_ext_game.proto
    cmd: 36593
    lang: go
    method: /ga.api.revenue_ext_game.RevenueExtGameLogic/GetExtGameCfgList
  - source: api/revenue_ext_game/grpc_revenue_ext_game.proto
    cmd: 36594
    lang: go
    method: /ga.api.revenue_ext_game.RevenueExtGameLogic/ReportUserWantPlay
  - source: api/revenue_ext_game/grpc_revenue_ext_game.proto
    cmd: 36595
    lang: go
    method: /ga.api.revenue_ext_game.RevenueExtGameLogic/GetUserExtGameInfo
  - source: api/revenue_ext_game/grpc_revenue_ext_game.proto
    cmd: 36596
    lang: go
    method: /ga.api.revenue_ext_game.RevenueExtGameLogic/GetChannelExtGameAccess
  - source: api/revenue_ext_game/grpc_revenue_ext_game.proto
    cmd: 36597
    lang: go
    method: /ga.api.revenue_ext_game.RevenueExtGameLogic/GetExtGameScoreRank
  - source: api/revenue_ext_game/grpc_revenue_ext_game.proto
    cmd: 36598
    lang: go
    method: /ga.api.revenue_ext_game.RevenueExtGameLogic/GetExtGameRankNameplate
  - source: api/revenue_ext_game/grpc_revenue_ext_game.proto
    cmd: 51301
    lang: go
    method: /ga.api.revenue_ext_game.RevenueExtGameLogic/GetExtGameInfoList
  - source: api/revenue_ext_game/grpc_revenue_ext_game.proto
    cmd: 51302
    lang: go
    method: /ga.api.revenue_ext_game.RevenueExtGameLogic/GetExtGameAccessList
  - source: api/revenue_ext_game/grpc_revenue_ext_game.proto
    cmd: 51303
    lang: go
    method: /ga.api.revenue_ext_game.RevenueExtGameLogic/GetExtGameOpenId
  - source: api/revenue_ext_game/grpc_revenue_ext_game.proto
    cmd: 51304
    lang: go
    method: /ga.api.revenue_ext_game.RevenueExtGameLogic/GetExtGameJsCode
  - source: api/revenue_ext_game/grpc_revenue_ext_game.proto
    cmd: 51305
    lang: go
    method: /ga.api.revenue_ext_game.RevenueExtGameLogic/GetExtGameWhiteChannel
  - source: api/revenue_ext_game/grpc_revenue_ext_game.proto
    cmd: 51306
    lang: go
    method: /ga.api.revenue_ext_game.RevenueExtGameLogic/CheckExtGameChannel
  - source: api/revenue_ext_game/grpc_revenue_ext_game.proto
    cmd: 51307
    lang: go
    method: /ga.api.revenue_ext_game.RevenueExtGameLogic/ExtGameLoginCheck

