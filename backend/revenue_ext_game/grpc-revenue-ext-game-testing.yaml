apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  namespace: quicksilver
  name: grpc-revenue-ext-game-logic
spec:
  gateways:
  - istio-ingress/apiv2-gateway
  - istio-ingress/apiv2-gateway-internal
  hosts:
  - '*'
  http:
  - match:
    - uri:
        prefix: /ga.api.revenue_ext_game.RevenueExtGameLogic/
    rewrite:
      uri: /logic.RevenueExtGameLogic/
    delegate:
       name: revenue-ext-game-logic-delegator-80
       namespace: quicksilver


