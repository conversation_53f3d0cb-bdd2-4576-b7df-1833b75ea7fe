apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  namespace: quicksilver
  name: grpc-channel-red-packet-logic
spec:
  gateways:
  - quicksilver/tt-api-gateway
  - quicksilver/tt-api-gateway-internal
  hosts:
  - '*'
  http:
  - match:
    - uri:
        prefix: /ga.api.channel_red_packet.ChannelRedPacketLogic/
    rewrite:
      uri: /logic.channel_red_packet_logic.ChannelRedPacketLogic/
    delegate:
       name: channel-red-packet-logic-delegator-80
       namespace: quicksilver


