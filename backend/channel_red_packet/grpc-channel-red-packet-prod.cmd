# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

30951:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 30951 --source api/channel_red_packet/grpc_channel_red_packet.proto --lang go --method /ga.api.channel_red_packet.ChannelRedPacketLogic/GetRedPacketConf
30952:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 30952 --source api/channel_red_packet/grpc_channel_red_packet.proto --lang go --method /ga.api.channel_red_packet.ChannelRedPacketLogic/GetRedPacketList
30953:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 30953 --source api/channel_red_packet/grpc_channel_red_packet.proto --lang go --method /ga.api.channel_red_packet.ChannelRedPacketLogic/SendRedPacket
30954:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 30954 --source api/channel_red_packet/grpc_channel_red_packet.proto --lang go --method /ga.api.channel_red_packet.ChannelRedPacketLogic/ReportRedPacketClickCnt
