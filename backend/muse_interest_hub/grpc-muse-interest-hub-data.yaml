cmdInfoList:
  - source: api/muse_interest_hub/grpc_muse_interest_hub.proto
    cmd: 31560
    lang: go
    method: /ga.api.muse_interest_hub.MuseInterestHubLogic/MuseCommonReport
  - source: api/muse_interest_hub/grpc_muse_interest_hub.proto
    cmd: 31561
    lang: go
    method: /ga.api.muse_interest_hub.MuseInterestHubLogic/GetMuseSwitchHub
  - source: api/muse_interest_hub/grpc_muse_interest_hub.proto
    cmd: 31562
    lang: go
    method: /ga.api.muse_interest_hub.MuseInterestHubLogic/SetMuseSwitchHub
  - source: api/muse_interest_hub/grpc_muse_interest_hub.proto
    cmd: 31564
    lang: go
    method: /ga.api.muse_interest_hub.MuseInterestHubLogic/CheckJumpSquarePage
  - source: api/muse_interest_hub/grpc_muse_interest_hub.proto
    cmd: 31565
    lang: go
    method: /ga.api.muse_interest_hub.MuseInterestHubLogic/GetMTExperimentStrategy
  - source: api/muse_interest_hub/grpc_muse_interest_hub.proto
    cmd: 31566
    lang: go
    method: /ga.api.muse_interest_hub.MuseInterestHubLogic/GetHomeFollowFloat
  - source: api/muse_interest_hub/grpc_muse_interest_hub.proto
    cmd: 31567
    lang: go
    method: /ga.api.muse_interest_hub.MuseInterestHubLogic/FollowChannelHalfScreenUserList
  - source: api/muse_interest_hub/grpc_muse_interest_hub.proto
    cmd: 31568
    lang: go
    method: /ga.api.muse_interest_hub.MuseInterestHubLogic/ChannelInviteFriendPreprocessing
  - source: api/muse_interest_hub/grpc_muse_interest_hub.proto
    cmd: 31569
    lang: go
    method: /ga.api.muse_interest_hub.MuseInterestHubLogic/ChannelSpecialFriendList
  - source: api/muse_interest_hub/grpc_muse_interest_hub.proto
    cmd: 31571
    lang: go
    method: /ga.api.muse_interest_hub.MuseInterestHubLogic/OneClickInviteAllInfo
  - source: api/muse_interest_hub/grpc_muse_interest_hub.proto
    cmd: 31572
    lang: go
    method: /ga.api.muse_interest_hub.MuseInterestHubLogic/OneClickInviteAll
  - source: api/muse_interest_hub/grpc_muse_interest_hub.proto
    cmd: 31573
    lang: go
    method: /ga.api.muse_interest_hub.MuseInterestHubLogic/CheckIsInOtherUsersBlackList
  - source: api/muse_interest_hub/grpc_muse_interest_hub.proto
    cmd: 31574
    lang: go
    method: /ga.api.muse_interest_hub.MuseInterestHubLogic/GetUserCurrentChannelId
  - source: api/muse_interest_hub/grpc_muse_interest_hub.proto
    cmd: 31575
    lang: go
    method: /ga.api.muse_interest_hub.MuseInterestHubLogic/OneClickInviteAllGetMicId
  - source: api/muse_interest_hub/grpc_muse_interest_hub.proto
    cmd: 31576
    lang: go
    method: /ga.api.muse_interest_hub.MuseInterestHubLogic/AutoInviteMicPanel
  - source: api/muse_interest_hub/grpc_muse_interest_hub.proto
    cmd: 31577
    lang: go
    method: /ga.api.muse_interest_hub.MuseInterestHubLogic/GetUserRelationship
  - source: api/muse_interest_hub/grpc_muse_interest_hub.proto
    cmd: 31578
    lang: go
    method: /ga.api.muse_interest_hub.MuseInterestHubLogic/NonMicUserListOrder
  - source: api/muse_interest_hub/grpc_muse_interest_hub.proto
    cmd: 31579
    lang: go
    method: /ga.api.muse_interest_hub.MuseInterestHubLogic/GetRoomExitGuideFollowList
  - source: api/muse_interest_hub/grpc_muse_interest_hub.proto
    cmd: 31580
    lang: go
    method: /ga.api.muse_interest_hub.MuseInterestHubLogic/ShowRoomExitFollowPopup
  - source: api/muse_interest_hub/grpc_muse_interest_hub.proto
    cmd: 31581
    lang: go
    method: /ga.api.muse_interest_hub.MuseInterestHubLogic/GetRoomIcebreakerPopup
  - source: api/muse_interest_hub/grpc_muse_interest_hub.proto
    cmd: 31582
    lang: go
    method: /ga.api.muse_interest_hub.MuseInterestHubLogic/SendPublicMessageDirectly
  - source: api/muse_interest_hub/grpc_muse_interest_hub.proto
    cmd: 31583
    lang: go
    method: /ga.api.muse_interest_hub.MuseInterestHubLogic/GetHiddenHomePageZoneConfig
  - source: api/muse_interest_hub/grpc_muse_interest_hub.proto
    cmd: 31584
    lang: go
    method: /ga.api.muse_interest_hub.MuseInterestHubLogic/GetHiddenChannelCategoryTypeConfig
  - source: api/muse_interest_hub/grpc_muse_interest_hub.proto
    cmd: 31585
    lang: go
    method: /ga.api.muse_interest_hub.MuseInterestHubLogic/GetMuseRecommendEmojis
  - source: api/muse_interest_hub/grpc_muse_interest_hub.proto
    cmd: 31586
    lang: go
    method: /ga.api.muse_interest_hub.MuseInterestHubLogic/GetCustomIcebreakerPopupSwitch
  - source: api/muse_interest_hub/grpc_muse_interest_hub.proto
    cmd: 31587
    lang: go
    method: /ga.api.muse_interest_hub.MuseInterestHubLogic/SetCustomIcebreakerPopupSwitch
  - source: api/muse_interest_hub/grpc_muse_interest_hub.proto
    cmd: 31588
    lang: go
    method: /ga.api.muse_interest_hub.MuseInterestHubLogic/UpdateCustomIcebreakerPopup
  - source: api/muse_interest_hub/grpc_muse_interest_hub.proto
    cmd: 31589
    lang: go
    method: /ga.api.muse_interest_hub.MuseInterestHubLogic/GetCustomIcebreakerConfigInfo
  - source: api/muse_interest_hub/grpc_muse_interest_hub.proto
    cmd: 31590
    lang: go
    method: /ga.api.muse_interest_hub.MuseInterestHubLogic/GetPublicScreenShortcutImage

