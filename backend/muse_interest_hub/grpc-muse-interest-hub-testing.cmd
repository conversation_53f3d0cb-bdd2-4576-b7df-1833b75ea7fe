# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

31560:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 31560 --source api/muse_interest_hub/grpc_muse_interest_hub.proto --lang go --method /ga.api.muse_interest_hub.MuseInterestHubLogic/MuseCommonReport
31561:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 31561 --source api/muse_interest_hub/grpc_muse_interest_hub.proto --lang go --method /ga.api.muse_interest_hub.MuseInterestHubLogic/GetMuseSwitchHub
31562:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 31562 --source api/muse_interest_hub/grpc_muse_interest_hub.proto --lang go --method /ga.api.muse_interest_hub.MuseInterestHubLogic/SetMuseSwitchHub
31564:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 31564 --source api/muse_interest_hub/grpc_muse_interest_hub.proto --lang go --method /ga.api.muse_interest_hub.MuseInterestHubLogic/CheckJumpSquarePage
31565:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 31565 --source api/muse_interest_hub/grpc_muse_interest_hub.proto --lang go --method /ga.api.muse_interest_hub.MuseInterestHubLogic/GetMTExperimentStrategy
31566:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 31566 --source api/muse_interest_hub/grpc_muse_interest_hub.proto --lang go --method /ga.api.muse_interest_hub.MuseInterestHubLogic/GetHomeFollowFloat
31567:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 31567 --source api/muse_interest_hub/grpc_muse_interest_hub.proto --lang go --method /ga.api.muse_interest_hub.MuseInterestHubLogic/FollowChannelHalfScreenUserList
31568:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 31568 --source api/muse_interest_hub/grpc_muse_interest_hub.proto --lang go --method /ga.api.muse_interest_hub.MuseInterestHubLogic/ChannelInviteFriendPreprocessing
31569:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 31569 --source api/muse_interest_hub/grpc_muse_interest_hub.proto --lang go --method /ga.api.muse_interest_hub.MuseInterestHubLogic/ChannelSpecialFriendList
31571:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 31571 --source api/muse_interest_hub/grpc_muse_interest_hub.proto --lang go --method /ga.api.muse_interest_hub.MuseInterestHubLogic/OneClickInviteAllInfo
31572:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 31572 --source api/muse_interest_hub/grpc_muse_interest_hub.proto --lang go --method /ga.api.muse_interest_hub.MuseInterestHubLogic/OneClickInviteAll
31573:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 31573 --source api/muse_interest_hub/grpc_muse_interest_hub.proto --lang go --method /ga.api.muse_interest_hub.MuseInterestHubLogic/CheckIsInOtherUsersBlackList
31574:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 31574 --source api/muse_interest_hub/grpc_muse_interest_hub.proto --lang go --method /ga.api.muse_interest_hub.MuseInterestHubLogic/GetUserCurrentChannelId
31575:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 31575 --source api/muse_interest_hub/grpc_muse_interest_hub.proto --lang go --method /ga.api.muse_interest_hub.MuseInterestHubLogic/OneClickInviteAllGetMicId
31576:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 31576 --source api/muse_interest_hub/grpc_muse_interest_hub.proto --lang go --method /ga.api.muse_interest_hub.MuseInterestHubLogic/AutoInviteMicPanel
31577:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 31577 --source api/muse_interest_hub/grpc_muse_interest_hub.proto --lang go --method /ga.api.muse_interest_hub.MuseInterestHubLogic/GetUserRelationship
31578:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 31578 --source api/muse_interest_hub/grpc_muse_interest_hub.proto --lang go --method /ga.api.muse_interest_hub.MuseInterestHubLogic/NonMicUserListOrder
31579:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 31579 --source api/muse_interest_hub/grpc_muse_interest_hub.proto --lang go --method /ga.api.muse_interest_hub.MuseInterestHubLogic/GetRoomExitGuideFollowList
31580:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 31580 --source api/muse_interest_hub/grpc_muse_interest_hub.proto --lang go --method /ga.api.muse_interest_hub.MuseInterestHubLogic/ShowRoomExitFollowPopup
31581:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 31581 --source api/muse_interest_hub/grpc_muse_interest_hub.proto --lang go --method /ga.api.muse_interest_hub.MuseInterestHubLogic/GetRoomIcebreakerPopup
31582:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 31582 --source api/muse_interest_hub/grpc_muse_interest_hub.proto --lang go --method /ga.api.muse_interest_hub.MuseInterestHubLogic/SendPublicMessageDirectly
31583:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 31583 --source api/muse_interest_hub/grpc_muse_interest_hub.proto --lang go --method /ga.api.muse_interest_hub.MuseInterestHubLogic/GetHiddenHomePageZoneConfig
31584:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 31584 --source api/muse_interest_hub/grpc_muse_interest_hub.proto --lang go --method /ga.api.muse_interest_hub.MuseInterestHubLogic/GetHiddenChannelCategoryTypeConfig
31585:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 31585 --source api/muse_interest_hub/grpc_muse_interest_hub.proto --lang go --method /ga.api.muse_interest_hub.MuseInterestHubLogic/GetMuseRecommendEmojis
31586:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 31586 --source api/muse_interest_hub/grpc_muse_interest_hub.proto --lang go --method /ga.api.muse_interest_hub.MuseInterestHubLogic/GetCustomIcebreakerPopupSwitch
31587:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 31587 --source api/muse_interest_hub/grpc_muse_interest_hub.proto --lang go --method /ga.api.muse_interest_hub.MuseInterestHubLogic/SetCustomIcebreakerPopupSwitch
31588:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 31588 --source api/muse_interest_hub/grpc_muse_interest_hub.proto --lang go --method /ga.api.muse_interest_hub.MuseInterestHubLogic/UpdateCustomIcebreakerPopup
31589:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 31589 --source api/muse_interest_hub/grpc_muse_interest_hub.proto --lang go --method /ga.api.muse_interest_hub.MuseInterestHubLogic/GetCustomIcebreakerConfigInfo
31590:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 31590 --source api/muse_interest_hub/grpc_muse_interest_hub.proto --lang go --method /ga.api.muse_interest_hub.MuseInterestHubLogic/GetPublicScreenShortcutImage
