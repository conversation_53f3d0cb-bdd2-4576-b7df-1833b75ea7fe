apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  namespace: quicksilver
  name: grpc-personal-certification-logic
spec:
  gateways:
  - istio-system/apiv2-gateway
  - istio-system/apiv2-gateway-internal
  hosts:
  - '*'
  http:
  - match:
    - uri:
        prefix: /ga.api.personal_certification.PersonalCertificationLogic/
    rewrite:
      uri: /logic.PersonalCertificationLogic/
    delegate:
       name: personal-certification-logic-delegator-80
       namespace: quicksilver


