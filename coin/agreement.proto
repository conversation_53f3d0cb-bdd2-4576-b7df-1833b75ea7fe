syntax = "proto3";

package ga.coin; //golang 忽略它

import "ga_base.proto";

// 优先于 `package`
option java_package = "com.qw.coin";
option go_package = "golang.52tt.com/protocol/app/coin";


// 获取 用户的货币服务协议状态
message GetCoinAgreementReq {
  //BaseReq 包含 app_id, market_id; uid 在头部
  ga.BaseReq base_req = 1;

  // 业务类型, 取值: “iap-coin” - 端内充值，购买虚拟货币
  string scope = 2;
}
message GetCoinAgreementResp {
  ga.BaseResp base_resp = 1;
  bool is_agreed = 2; //是否已签署
  string agreement_url = 3; //服务协议 url
}


