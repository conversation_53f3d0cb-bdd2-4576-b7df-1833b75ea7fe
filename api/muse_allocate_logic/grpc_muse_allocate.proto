// Code generated by protoc-gen-ga-api. DO NOT EDIT.
// 	protoc        v3.9.1

syntax = "proto3";
package ga.api.muse_allocate_logic;

import "muse_allocate_logic/muse_allocate_logic.proto";
import "api/extension/extension.proto";

option java_package      = "com.quwan.tt.proto.api";
option objc_class_prefix = "RPC";
option go_package = "golang.52tt.com/protocol/app/api/muse_allocate_logic;muse_allocate_logic";

service MuseAllocateLogic {
    option (ga.api.extension.logic_service_name) = "muse-allocate-logic";
    option (ga.api.extension.logic_service_language) = "go";
    option (ga.api.extension.logic_service_uri_rewrite) = "/muse_allocate_logic.MuseAllocateLogic/";
    rpc GetMuseAllocateInfo(ga.muse_allocate_logic.GetMuseAllocateInfoReq) returns (ga.muse_allocate_logic.GetMuseAllocateInfoResp) {
        option (ga.api.extension.command) = {
             id: 38001;
        };
    }

    rpc GetMuseAllocateNonEnterChannel(ga.muse_allocate_logic.GetMuseAllocateNonEnterChannelReq) returns (ga.muse_allocate_logic.GetMuseAllocateNonEnterChannelResp) {
        option (ga.api.extension.command) = {
            id: 38002;
        };
    }

    rpc GetMuseAllocateNonEnterChannelInfo(ga.muse_allocate_logic.GetMuseAllocateNonEnterChannelInfoReq) returns (ga.muse_allocate_logic.GetMuseAllocateNonEnterChannelInfoResp) {
        option (ga.api.extension.command) = {
            id: 38003;
        };
    }

    rpc GetQuickEnterChannelModel(ga.muse_allocate_logic.GetQuickEnterChannelModelReq) returns (ga.muse_allocate_logic.GetQuickEnterChannelModelResp) {
        option (ga.api.extension.command) = {
            id: 38004;
        };
    }

    rpc GetTodayCoupleList(ga.muse_allocate_logic.GetTodayCoupleListReq) returns (ga.muse_allocate_logic.GetTodayCoupleListResp) {
        option (ga.api.extension.command) = {
            id: 38005;
        };
    }

    rpc GetFlashChatMatchingCondition (ga.muse_allocate_logic.GetFlashChatMatchingConditionReq) returns (ga.muse_allocate_logic.GetFlashChatMatchingConditionResp) {
        option (ga.api.extension.command) = {
          id: 38100;
        };
    }

    rpc GetFlashChatMatchingResult (ga.muse_allocate_logic.GetFlashChatMatchingResultReq) returns (ga.muse_allocate_logic.GetFlashChatMatchingResultResp) {
        option (ga.api.extension.command) = {
          id: 38101;
        };
    }

    rpc GetFlashChatObjectInfo (ga.muse_allocate_logic.GetFlashChatObjectInfoReq) returns (ga.muse_allocate_logic.GetFlashChatObjectInfoResp) {
        option (ga.api.extension.command) = {
          id: 38102;
        };
    }

  rpc FlashChatEntryPreCheck (ga.muse_allocate_logic.FlashChatEntryPreCheckRequest) returns (ga.muse_allocate_logic.FlashChatEntryPreCheckResponse) {
    option (ga.api.extension.command) = {
      id: 38103;
    };
  }

    rpc ViewPersonalHomepageReport (ga.muse_allocate_logic.ViewPersonalHomepageReportReq) returns (ga.muse_allocate_logic.ViewPersonalHomepageReportResp) {
      option (ga.api.extension.command) = {
        id: 38104;
      };
    }
}


