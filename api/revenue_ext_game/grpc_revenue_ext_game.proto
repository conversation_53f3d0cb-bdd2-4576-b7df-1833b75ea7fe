// Code generated by protoc-gen-ga-api. DO NOT EDIT.
// 	protoc        v3.9.1

syntax = "proto3";
package ga.api.revenue_ext_game;

import "revenue_ext_game_logic/revenue_ext_game_logic.proto";
import "api/extension/extension.proto";

option java_package      = "com.quwan.tt.proto.api";
option objc_class_prefix = "RPC";
option go_package = "golang.52tt.com/protocol/app/api/revenue_ext_game;revenue_ext_game";

service RevenueExtGameLogic {
    option (ga.api.extension.logic_service_name) = "revenue-ext-game-logic";
    option (ga.api.extension.logic_service_language) = "go";
    option (ga.api.extension.logic_service_uri_rewrite) = "/logic.RevenueExtGameLogic/";
    rpc MountExtGame(ga.revenue_ext_game_logic.MountExtGameRequest) returns (ga.revenue_ext_game_logic.MountExtGameResponse) {
        option (ga.api.extension.command) = {
             id: 36591;
        };
    }
    rpc UnmountExtGame(ga.revenue_ext_game_logic.UnmountExtGameRequest) returns (ga.revenue_ext_game_logic.UnmountExtGameResponse) {
        option (ga.api.extension.command) = {
             id: 36592;
        };
    }
    rpc GetExtGameCfgList(ga.revenue_ext_game_logic.GetExtGameCfgListRequest) returns (ga.revenue_ext_game_logic.GetExtGameCfgListResponse) {
        option (ga.api.extension.command) = {
             id: 36593;
        };
    }
    rpc ReportUserWantPlay(ga.revenue_ext_game_logic.ReportUserWantPlayRequest) returns (ga.revenue_ext_game_logic.ReportUserWantPlayResponse) {
        option (ga.api.extension.command) = {
             id: 36594;
        };
    }
    rpc GetUserExtGameInfo(ga.revenue_ext_game_logic.GetUserExtGameInfoRequest) returns (ga.revenue_ext_game_logic.GetUserExtGameInfoResponse) {
        option (ga.api.extension.command) = {
             id: 36595;
        };
    }
    rpc GetChannelExtGameAccess(ga.revenue_ext_game_logic.GetChannelExtGameAccessRequest) returns (ga.revenue_ext_game_logic.GetChannelExtGameAccessResponse) {
        option (ga.api.extension.command) = {
             id: 36596;
        };
    }
    rpc GetExtGameScoreRank(ga.revenue_ext_game_logic.GetExtGameScoreRankRequest) returns (ga.revenue_ext_game_logic.GetExtGameScoreRankResponse) {
        option (ga.api.extension.command) = {
             id: 36597;
        };
    }
    rpc GetExtGameRankNameplate(ga.revenue_ext_game_logic.GetExtGameRankNameplateRequest) returns (ga.revenue_ext_game_logic.GetExtGameRankNameplateResponse) {
        option (ga.api.extension.command) = {
             id: 36598;
        };
    }

     rpc GetExtGameInfoList(ga.revenue_ext_game_logic.GetExtGameInfoListRequest) returns (ga.revenue_ext_game_logic.GetExtGameInfoListResponse) {
         option (ga.api.extension.command) = {
              id: 51301;
         };
     }

    rpc GetExtGameAccessList(ga.revenue_ext_game_logic.GetExtGameAccessListRequest) returns (ga.revenue_ext_game_logic.GetExtGameAccessListResponse) {
        option (ga.api.extension.command) = {
             id: 51302;
        };
    }

    rpc GetExtGameOpenId(ga.revenue_ext_game_logic.GetExtGameOpenIdRequest) returns (ga.revenue_ext_game_logic.GetExtGameOpenIdResponse) {
        option (ga.api.extension.command) = {
             id: 51303;
        };
    }

    rpc GetExtGameJsCode(ga.revenue_ext_game_logic.GetExtGameJsCodeRequest) returns (ga.revenue_ext_game_logic.GetExtGameJsCodeResponse) {
        option (ga.api.extension.command) = {
             id: 51304;
        };
    }
    
    rpc GetExtGameWhiteChannel(ga.revenue_ext_game_logic.GetExtGameWhiteChannelRequest) returns (ga.revenue_ext_game_logic.GetExtGameWhiteChannelResponse) {
        option (ga.api.extension.command) = {
             id: 51305;
        };
    }
    rpc CheckExtGameChannel(ga.revenue_ext_game_logic.CheckExtGameChannelRequest) returns (ga.revenue_ext_game_logic.CheckExtGameChannelResponse) {
        option (ga.api.extension.command) = {
             id: 51306;
        };
    }
    rpc ExtGameLoginCheck(ga.revenue_ext_game_logic.ExtGameLoginCheckRequest) returns (ga.revenue_ext_game_logic.ExtGameLoginCheckResponse) {
        option (ga.api.extension.command) = {
             id: 51307;
        };
    }
}


