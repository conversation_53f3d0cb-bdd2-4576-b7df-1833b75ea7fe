syntax = "proto3";
package ga.api.channel_audio_violation;

import "channel_audio_violation/channel_audio_violation.proto";
import "api/extension/extension.proto";

option go_package = "golang.52tt.com/protocol/app/api/channel_audio_violation;channel_audio_violation";
option java_package = "com.quwan.tt.proto.api";
option objc_class_prefix = "RPC";

service ChannelAudioViolationLogic {
  option (ga.api.extension.logic_service_name) = "channel-audio-violation-logic";
  option (ga.api.extension.logic_service_language) = "go";
  option (ga.api.extension.logic_service_uri_rewrite) = "";

  // 房间的违规检测是否开启
  rpc IsDetectEnabled(ga.channel_audio_violation.IsDetectEnabledRequest) returns (ga.channel_audio_violation.IsDetectEnabledResponse) {
    option (ga.api.extension.command) = {
      id: 50866;
    };
  }

  // 发起一轮违规审判
  rpc StartJudgment(ga.channel_audio_violation.StartJudgmentRequest) returns (ga.channel_audio_violation.StartJudgmentResponse) {
    option (ga.api.extension.command) = {
      id: 50862;
    };
  }

  // 违规审判投票
  rpc JudgmentVote(ga.channel_audio_violation.JudgmentVoteRequest) returns (ga.channel_audio_violation.JudgmentVoteResponse) {
    option (ga.api.extension.command) = {
      id: 50863;
    };
  }

  // 对审判结果发起申诉
  rpc JudgmentAppeal(ga.channel_audio_violation.JudgmentAppealRequest) returns (ga.channel_audio_violation.JudgmentAppealResponse) {
    option (ga.api.extension.command) = {
      id: 50864;
    };
  }

  // 上报违规的音频文件
  rpc ReportAudioFile(ga.channel_audio_violation.ReportAudioFileRequest) returns (ga.channel_audio_violation.ReportAudioFileResponse) {
    option (ga.api.extension.command) = {
      id: 50865;
    };
  }

  // 获取音频抽样配置
  rpc GetAudioSamplingConfig(ga.channel_audio_violation.GetAudioSamplingConfigRequest) returns (ga.channel_audio_violation.GetAudioSamplingConfigResponse) {
    option (ga.api.extension.command) = {
      id: 50877;
    };
  }

  // 抽样音频是否需要保存
  rpc IsSampledAudioNeedSave(ga.channel_audio_violation.IsSampledAudioNeedSaveRequest) returns (ga.channel_audio_violation.IsSampledAudioNeedSaveResponse) {
    option (ga.api.extension.command) = {
      id: 50878;
    };
  }

  // 上报抽样的音频
  rpc ReportSampledAudio(ga.channel_audio_violation.ReportSampledAudioRequest) returns (ga.channel_audio_violation.ReportSampledAudioResponse) {
    option (ga.api.extension.command) = {
      id: 50879;
    };
  }
}