// Code generated by protoc-gen-ga-api. DO NOT EDIT.
// 	protoc        v5.27.0

syntax = "proto3";
package ga.api.channel_background;

import "channelbackgroundlogic/channel_background_logic_.proto";
import "api/extension/extension.proto";

option java_package      = "com.quwan.tt.proto.api";
option objc_class_prefix = "RPC";
option go_package = "golang.52tt.com/protocol/app/api/channel_background;channel_background";

service ChannelBackgroundLogic {
    option (ga.api.extension.logic_service_name) = "channelbackground-logic";
    option (ga.api.extension.logic_service_language) = "go";
    option (ga.api.extension.logic_service_uri_rewrite) = "/logic.ChannelBackgroundLogic/";
    rpc GetCurChannelBackgroundInfo(ga.channelbackgroundlogic.GetCurChannelBackgroundInfoReq) returns (ga.channelbackgroundlogic.GetCurChannelBackgroundInfoResp) {
        option (ga.api.extension.command) = {
             id: 2020;
        };
    }
    rpc ChangeCurChannelBackground(ga.channelbackgroundlogic.ChangeCurChannelBackgroundReq) returns (ga.channelbackgroundlogic.ChangeCurChannelBackgroundResp) {
        option (ga.api.extension.command) = {
             id: 2021;
        };
    }
    rpc GetChannelBackgroundInfoList(ga.channelbackgroundlogic.GetChannelBackgroundInfoListReq) returns (ga.channelbackgroundlogic.GetChannelBackgroundInfoListResp) {
        option (ga.api.extension.command) = {
             id: 2022;
        };
    }
    rpc CheckChannelBackgroundUpdate(ga.channelbackgroundlogic.CheckChannelBackgroundUpdateReq) returns (ga.channelbackgroundlogic.CheckChannelBackgroundUpdateResp) {
        option (ga.api.extension.command) = {
             id: 2023;
        };
    }
    rpc GetRecommendBackground(ga.channelbackgroundlogic.GetRecommendBackgroundReq) returns (ga.channelbackgroundlogic.GetRecommendBackgroundResp) {
        option (ga.api.extension.command) = {
             id: 2024;
        };
    }
    rpc GetKHBackgroundInfoList(ga.channelbackgroundlogic.GetKHBackgroundInfoListReq) returns (ga.channelbackgroundlogic.GetKHBackgroundInfoListResp) {
        option (ga.api.extension.command) = {
             id: 2025;
        };
    }
    rpc GetPclfgCurChannelBackground(ga.channelbackgroundlogic.GetPclfgCurChannelBackgroundReq) returns (ga.channelbackgroundlogic.GetPclfgCurChannelBackgroundResp) {
        option (ga.api.extension.command) = {
            id: 2026;
        };
    }
    rpc SetPclfgCurChannelBackground(ga.channelbackgroundlogic.SetPclfgCurChannelBackgroundReq) returns (ga.channelbackgroundlogic.SetPclfgCurChannelBackgroundResp) {
        option (ga.api.extension.command) = {
            id: 2027;
        };
    }
    rpc GetPclfgChannelBackgroundList(ga.channelbackgroundlogic.GetPclfgChannelBackgroundListReq) returns (ga.channelbackgroundlogic.GetPclfgChannelBackgroundListResp) {
        option (ga.api.extension.command) = {
            id: 2028;
        };
    }
}


