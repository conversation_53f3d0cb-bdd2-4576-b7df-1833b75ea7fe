// Code generated by protoc-gen-ga-api. DO NOT EDIT.
// 	protoc        v3.9.1

syntax = "proto3";
package ga.api.online;



import "online/friendol_.proto";
import "api/extension/extension.proto";

option java_package      = "com.quwan.tt.proto.api";
option objc_class_prefix = "RPC";
option go_package = "golang.52tt.com/protocol/app/api/online;online";

service OnlineLogic {
    option (ga.api.extension.logic_service_name) = "online-logic";
    option (ga.api.extension.logic_service_language) = "go";
    option (ga.api.extension.logic_service_uri_rewrite) = "/logic.OnlineLogic/";
    rpc GetGroupOnlineCount(ga.online.GetGroupOnlineCountReq) returns (ga.online.GetGroupOnlineCountResp) {
        option (ga.api.extension.command) = {
             id: 1100
        };
    }
    rpc ReportUserOnlineGameActive(ga.online.ReportPlayingGameReq) returns (ga.online.ReportPlayingGameResp) {
        option (ga.api.extension.command) = {
             id: 1121
        };
    }
    rpc GetUserOnlineFriendList(ga.online.GetOnlineFriendsReq) returns (ga.online.GetOnlineFriendsResp) {
        option (ga.api.extension.command) = {
             id: 1122
        };
    }
    rpc GetUserOfflineFriendList(ga.online.GetOfflineFriendsReq) returns (ga.online.GetOfflineFriendsResp) {
        option (ga.api.extension.command) = {
             id: 1123
        };
    }
    rpc ReportFollowChannelAuth(ga.online.UpdateFollowChannelAuthReq) returns (ga.online.UpdateFollowChannelAuthResp) {
        option (ga.api.extension.command) = {
             id: 1124
        };
    }
    rpc GetFollowChannelAuth(ga.online.GetFollowChannelAuthReq) returns (ga.online.GetFollowChannelAuthResp) {
        option (ga.api.extension.command) = {
             id: 1125
        };
    }
    rpc GetUserFollowChannelInfo(ga.online.GetUserFollowChannelInfoReq) returns (ga.online.GetUserFollowChannelInfoResp) {
        option (ga.api.extension.command) = {
             id: 1126
        };
    }
    rpc GetChannelOnlineMemberCnt( ga.online.GetChannelOnlineMemberCntReq ) returns( ga.online.GetChannelOnlineMemberCntResp ) {
        option(ga.api.extension.command) = {
            id: 1127
        };
    }
}

