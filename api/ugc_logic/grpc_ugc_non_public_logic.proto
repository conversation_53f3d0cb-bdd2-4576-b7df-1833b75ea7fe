// auto gen by pbtools v1

syntax = "proto3";

package ga.api.ugc_logic;

import "api/extension/extension.proto";
import "ugc_non_public/ugc_non_public.proto";

option go_package = "golang.52tt.com/protocol/app/api/ugc_logic;ugc_logic";
option java_package = "com.quwan.tt.proto.api";
option objc_class_prefix = "RPC";

service NonLogic {
  option (ga.api.extension.logic_service_language) = "go";
  option (ga.api.extension.logic_service_name) = "ugc-logic";
  option (ga.api.extension.logic_service_uri_rewrite) = "/logic.ugc_logic.NonLogic/";


  rpc PostNonPublicPost(ga.ugc_non_public.PostNonPublicPostReq) returns (ga.ugc_non_public.PostNonPublicPostResp) {
    option (ga.api.extension.command) = {
      id: 2632
    };
  }

  rpc GetNonPublicPostPublishExtend(ga.ugc_non_public.GetNonPublicPostPublishExtendReq) returns (ga.ugc_non_public.GetNonPublicPostPublishExtendResp) {
    option (ga.api.extension.command) = {
      id: 2633
    };
  }

  rpc MarkNonPublicPostAttachmentUploaded(ga.ugc_non_public.MarkNonPublicPostAttachmentUploadedReq) returns (ga.ugc_non_public.MarkNonPublicPostAttachmentUploadedResp) {
    option (ga.api.extension.command) = {
      id: 2634
    };
  }

  rpc GetNonPublicNewsFeeds(ga.ugc_non_public.GetNonPublicNewsFeedsReq) returns (ga.ugc_non_public.GetNonPublicNewsFeedsResp) {
    option (ga.api.extension.command) = {
      id: 2635
    };
  }

  rpc GetNonPublicPost(ga.ugc_non_public.GetNonPublicPostReq) returns (ga.ugc_non_public.GetNonPublicPostResp) {
    option (ga.api.extension.command) = {
      id: 2636
    };
  }

  rpc PostNonPublicComment(ga.ugc_non_public.PostNonPublicCommentReq) returns (ga.ugc_non_public.PostNonPublicCommentResp) {
    option (ga.api.extension.command) = {
      id: 2637
    };
  }

  rpc GetNonPublicCommentList(ga.ugc_non_public.GetNonPublicCommentListReq) returns (ga.ugc_non_public.GetNonPublicCommentListResp) {
    option (ga.api.extension.command) = {
      id: 2638
    };
  }

  rpc NonPublicExpressAttitude(ga.ugc_non_public.NonPublicExpressAttitudeReq) returns (ga.ugc_non_public.NonPublicExpressAttitudeResp) {
    option (ga.api.extension.command) = {
      id: 2639
    };
  }

  rpc MarkUserStreamRecord(ga.ugc_non_public.MarkUserStreamRecordReq) returns (ga.ugc_non_public.MarkUserStreamRecordResp) {
    option (ga.api.extension.command) = {
      id: 2640
    };
  }

  rpc ReportNonPublicPostShare(ga.ugc_non_public.ReportNonPublicPostShareRequest) returns (ga.ugc_non_public.ReportNonPublicPostShareResponse) {
    option (ga.api.extension.command) = {
      id: 2641
    };
  }


  rpc DeleteNonPublicPost(ga.ugc_non_public.DeleteNonPublicPostRequest) returns (ga.ugc_non_public.DeleteNonPublicPostResponse) {
    option (ga.api.extension.command) = {
      id: 2642
    };
  }

  rpc DeleteNonPublicComment(ga.ugc_non_public.DeleteNonPublicCommentRequest) returns (ga.ugc_non_public.DeleteNonPublicCommentResponse) {
    option (ga.api.extension.command) = {
      id: 2643
    };
  }

  rpc EditNonPublicPost(ga.ugc_non_public.EditNonPublicPostRequest) returns (ga.ugc_non_public.EditNonPublicPostResponse) {
    option (ga.api.extension.command) = {
      id: 2644
    };
  }
  rpc GetNonPublicEditPostExtend(ga.ugc_non_public.GetNonPublicEditPostExtendRequest) returns (ga.ugc_non_public.GetNonPublicEditPostExtendResponse) {
    option (ga.api.extension.command) = {
      id: 2645
    };
  }

  /*批量转换社群讨论贴*/
  rpc BatchConversionNonPublicPost(ga.ugc_non_public.BatchConversionNonPublicPostRequest) returns (ga.ugc_non_public.BatchConversionNonPublicPostResponse) {
    option (ga.api.extension.command) = {
      id: 2646
    };
  }

  /*强插转换过的帖子推送*/
  rpc ForceConversionPostPush(ga.ugc_non_public.ForceConversionPostPushRequest) returns (ga.ugc_non_public.ForceConversionPostPushResponse) {
    option (ga.api.extension.command) = {
      id: 2647
    };
  }

  rpc NonPublicPostVoteOption(ga.ugc_non_public.NonPublicPostVoteOptionRequest) returns (ga.ugc_non_public.NonPublicPostVoteOptionResponse) {
    option (ga.api.extension.command) = {
      id: 2648
    };
  }


}
