// Code generated by protoc-gen-ga-api. DO NOT EDIT.
// 	protoc        v5.27.0

syntax = "proto3";
package ga.api.present_go;

import "present_go_logic/present-go-logic_.proto";
import "redpacket/redpacket_.proto";
import "userpresent/userpresent_.proto";
import "time_present/time_present.proto";
import "api/extension/extension.proto";

option java_package      = "com.quwan.tt.proto.api";
option objc_class_prefix = "RPC";
option go_package = "golang.52tt.com/protocol/app/api/present_go;present_go";

service PresentGoLogic {
    option (ga.api.extension.logic_service_name) = "present-go-logic";
    option (ga.api.extension.logic_service_language) = "go";
    option (ga.api.extension.logic_service_uri_rewrite) = "/logic.present_go_logic.PresentGoLogic/";
    rpc GetNeedPopUpPresentList(ga.present_go_logic.GetNeedPopUpPresentListReq) returns (ga.present_go_logic.GetNeedPopUpPresentListResp) {
        option (ga.api.extension.command) = {
             id: 1174;
        };
    }
    rpc GetUserActPresentArea(ga.present_go_logic.GetUserActPresentAreaReq) returns (ga.present_go_logic.GetUserActPresentAreaResp) {
        option (ga.api.extension.command) = {
             id: 1190;
        };
    }
    rpc GetPresentExtraConfig(ga.present_go_logic.GetPresentExtraConfigReq) returns (ga.present_go_logic.GetPresentExtraConfigResp) {
        option (ga.api.extension.command) = {
             id: 1191;
        };
    }
    rpc GetPresentEffectTimeDetail(ga.present_go_logic.GetPresentEffectTimeDetailReq) returns (ga.present_go_logic.GetPresentEffectTimeDetailResp) {
        option (ga.api.extension.command) = {
             id: 1192;
        };
    }
    rpc ChannelPresentSend(ga.userpresent.SendPresentReq) returns (ga.userpresent.SendPresentResp) {
        option (ga.api.extension.command) = {
             id: 1165;
             deprecated: true;
        };
    }
    rpc PresentBatchSend(ga.userpresent.BatchSendPresentReq) returns (ga.userpresent.BatchSendPresentResp) {
        option (ga.api.extension.command) = {
             id: 1170;
             deprecated: true;
        };
    }
    rpc GetCustomizedPresentList(ga.present_go_logic.GetCustomizedPresentListReq) returns (ga.present_go_logic.GetCustomizedPresentListResp) {
        option (ga.api.extension.command) = {
             id: 36200;
        };
    }
    rpc GetCustomizedPresentDetail(ga.present_go_logic.GetCustomizedPresentDetailReq) returns (ga.present_go_logic.GetCustomizedPresentDetailResp) {
        option (ga.api.extension.command) = {
             id: 36201;
        };
    }
    rpc ReportCustomOptionChoose(ga.present_go_logic.ReportCustomOptionChooseReq) returns (ga.present_go_logic.ReportCustomOptionChooseResp) {
        option (ga.api.extension.command) = {
             id: 36202;
        };
    }
    rpc GetUserPresentInfo(ga.userpresent.GetUserPresentInfoReq) returns (ga.userpresent.GetUserPresentInfoResp) {
        option (ga.api.extension.command) = {
             id: 1166;
             deprecated: true;
        };
    }
    rpc GetPresentConfigList(ga.userpresent.GetPresentConfigListReq) returns (ga.userpresent.GetPresentConfigListResp) {
        option (ga.api.extension.command) = {
             id: 1167;
             deprecated: true;
        };
    }
    rpc GetPresentConfigById(ga.userpresent.GetPresentConfigByIdReq) returns (ga.userpresent.GetPresentConfigByIdResp) {
        option (ga.api.extension.command) = {
             id: 1168;
             deprecated: true;
        };
    }
    rpc GetUserPresentDetailList(ga.userpresent.GetUserPresentDetailListReq) returns (ga.userpresent.GetUserPresentDetailListResp) {
        option (ga.api.extension.command) = {
             id: 1169;
             deprecated: true;
        };
    }
    rpc PresentSendByIM(ga.userpresent.IMSendPresentReq) returns (ga.userpresent.IMSendPresentResp) {
        option (ga.api.extension.command) = {
             id: 1171;
             deprecated: true;
        };
    }
    rpc GetNamingPresentConfigList(ga.userpresent.GetNamingPresentConfigListReq) returns (ga.userpresent.GetNamingPresentConfigListResp) {
        option (ga.api.extension.command) = {
             id: 1172;
             deprecated: true;
        };
    }
    rpc GetPresentDynamicTemplateConfig(ga.userpresent.GetPresentDynamicTemplateConfigReq) returns (ga.userpresent.GetPresentDynamicTemplateConfigResp) {
        option (ga.api.extension.command) = {
             id: 1173;
             deprecated: true;
        };
    }
    rpc PresentGetFlowConfigList(ga.userpresent.GetPresentFlowConfigListReq) returns (ga.userpresent.GetPresentFlowConfigListResp) {
        option (ga.api.extension.command) = {
             id: 1175;
             deprecated: true;
        };
    }
    rpc GetPresentFlowConfigById(ga.userpresent.GetPresentFlowConfigByIdReq) returns (ga.userpresent.GetPresentFlowConfigByIdResp) {
        option (ga.api.extension.command) = {
             id: 1176;
             deprecated: true;
        };
    }
    rpc GetDrawPresentPara(ga.userpresent.GetDrawPresentParaReq) returns (ga.userpresent.GetDrawPresentParaResp) {
        option (ga.api.extension.command) = {
             id: 1177;
             deprecated: true;
        };
    }
    rpc GetImPresentItemIdList(ga.userpresent.GetImPresentItemIdListReq) returns (ga.userpresent.GetImPresentItemIdListResp) {
        option (ga.api.extension.command) = {
             id: 1178;
             deprecated: true;
        };
    }
    rpc GetStangerImItemIdList(ga.userpresent.GetStangerImItemIdListReq) returns (ga.userpresent.GetStangerImItemIdListResp) {
        option (ga.api.extension.command) = {
             id: 1179;
             deprecated: true;
        };
    }
    rpc GetFriendGift(ga.redpacket.GetFriendGiftReq) returns (ga.redpacket.GetFriendGiftResp) {
        option (ga.api.extension.command) = {
             id: 2802;
             deprecated: true;
        };
    }
    rpc UnpackPresentBox(ga.userpresent.UnpackPresentBoxReq) returns (ga.userpresent.UnpackPresentBoxResp) {
        option (ga.api.extension.command) = {
             id: 1193;
        };
    }
    rpc CommonSendPresent(ga.present_go_logic.CommonSendPresentReq) returns (ga.present_go_logic.CommonSendPresentResp) {
        option (ga.api.extension.command) = {
             id: 1194;
        };
    }
    rpc PresentConfigSync(ga.present_go_logic.PresentConfigSyncReq) returns (ga.present_go_logic.PresentConfigSyncResp) {
        option (ga.api.extension.command) = {
             id: 1195;
        };
    }
    rpc EmperorSetSend(ga.present_go_logic.EmperorSetSendReq) returns (ga.present_go_logic.EmperorSetSendResp) {
        option (ga.api.extension.command) = {
             id: 1196;
        };
    }
    rpc GetEmperorSetConfigById(ga.present_go_logic.GetEmperorSetConfigByIdReq) returns (ga.present_go_logic.GetEmperorSetConfigByIdResp) {
        option (ga.api.extension.command) = {
             id: 1197;
        };
    }
    rpc UnpackEmperorBox(ga.present_go_logic.UnpackEmperorBoxReq) returns (ga.present_go_logic.UnpackEmperorBoxResp) {
        option (ga.api.extension.command) = {
             id: 1198;
        };
    }
    rpc GetTimePresentList(ga.time_present.GetTimePresentListReq) returns (ga.time_present.GetTimePresentListResp) {
        option (ga.api.extension.command) = {
             id: 1199;
        };
    }
    rpc GetTimePresentOnShelf(ga.time_present.GetTimePresentOnShelfReq) returns (ga.time_present.GetTimePresentOnShelfResp) {
        option (ga.api.extension.command) = {
             id: 39400;
        };
    }
    rpc GetChannelLiveIntimatePresentList(ga.time_present.GetChannelLiveIntimatePresentListReq) returns (ga.time_present.GetChannelLiveIntimatePresentListResp) {
        option (ga.api.extension.command) = {
             id: 39401;
        };
    }
    rpc GetLiveIntimatePresentConfigList(ga.time_present.GetLiveIntimatePresentConfigListReq) returns (ga.time_present.GetLiveIntimatePresentConfigListResp) {
        option (ga.api.extension.command) = {
             id: 39402;
        };
    }
    rpc GetLiveIntimatePresentOnShelf(ga.time_present.GetLiveIntimatePresentOnShelfReq) returns (ga.time_present.GetLiveIntimatePresentOnShelfResp) {
        option (ga.api.extension.command) = {
             id: 39403;
        };
    }
    rpc GetPresentSetInfo(ga.present_go_logic.GetPresentSetInfoReq) returns (ga.present_go_logic.GetPresentSetInfoResp) {
        option (ga.api.extension.command) = {
             id: 39301;
        };
    }
    rpc GetPresentSetDetail(ga.present_go_logic.GetPresentSetDetailReq) returns (ga.present_go_logic.GetPresentSetDetailResp) {
        option (ga.api.extension.command) = {
             id: 39302;
        };
    }
}


