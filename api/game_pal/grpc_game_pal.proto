syntax = "proto3";
package ga.api.game_pal;

import "game_pal_logic/game_pal_logic.proto";
import "api/extension/extension.proto";

option java_package      = "com.quwan.tt.proto.api";
option objc_class_prefix = "RPC";
option go_package = "golang.52tt.com/protocol/app/api/game_pal;game_pal";

service GamePalLogic {
    option (ga.api.extension.logic_service_name) = "game-pal-logic";
    option (ga.api.extension.logic_service_language) = "go";

    // 校验发布条件
    rpc CheckPublishCondition(ga.game_pal_logic.CheckPublishConditionRequest) returns (ga.game_pal_logic.CheckPublishConditionResponse) {
        option (ga.api.extension.command) = {
             id: 4051;
        };
    }

    // 获取游戏搭子卡属性
    rpc GetGamePalCardProps(ga.game_pal_logic.GetGamePalCardPropsRequest) returns (ga.game_pal_logic.GetGamePalCardPropsResponse) {
        option (ga.api.extension.command) = {
             id: 4052;
        };
    }

    // 发布游戏搭子卡
    rpc PublishGamePalCard(ga.game_pal_logic.PublishGamePalCardRequest) returns (ga.game_pal_logic.PublishGamePalCardResponse) {
        option (ga.api.extension.command) = {
             id: 4053;
        };
    }

    // 点亮游戏搭子卡
    rpc LightenGamePalCard(ga.game_pal_logic.LightenGamePalCardRequest) returns (ga.game_pal_logic.LightenGamePalCardResponse) {
        option (ga.api.extension.command) = {
             id: 4054;
        };
    }

    // 获取用户所有游戏主题搭子卡列表
    rpc GetGamePalCardList(ga.game_pal_logic.GetGamePalCardListRequest) returns (ga.game_pal_logic.GetGamePalCardListResponse) {
        option (ga.api.extension.command) = {
             id: 4056;
        };
    }

    // 获取用户单个游戏主题搭子卡
    rpc GetGamePalCard(ga.game_pal_logic.GetGamePalCardRequest) returns (ga.game_pal_logic.GetGamePalCardResponse) {
        option (ga.api.extension.command) = {
             id: 4057;
        };
    }

    // 删除游戏搭子卡
    rpc DeleteGamePalCard(ga.game_pal_logic.DeleteGamePalCardRequest) returns (ga.game_pal_logic.DeleteGamePalCardResponse) {
        option (ga.api.extension.command) = {
             id: 4058;
        };
    }

    // 获取搭子卡筛选项
    rpc GetGamePalFilterByTabId(ga.game_pal_logic.GetGamePalFilterByTabIdRequest) returns (ga.game_pal_logic.GetGamePalFilterByTabIdResponse) {
        option (ga.api.extension.command) = {
            id: 4059;
        };
    }

    // 获取游戏搭子卡列表
    rpc GetGamePalList(ga.game_pal_logic.GetGamePalListReq) returns (ga.game_pal_logic.GetGamePalListResp) {
        option (ga.api.extension.command) = {
            id: 4060;
        };
    }

    // 游戏搭子超级发布
    rpc GamePalSuperPublish(ga.game_pal_logic.GamePalSuperPublishRequest) returns (ga.game_pal_logic.GamePalSuperPublishResponse) {
        option (ga.api.extension.command) = {
            id: 4061;
        };
    }

    // 上报给游戏搭子打招呼
    rpc ReportGamePalGreeting(ga.game_pal_logic.ReportGamePalGreetingRequest) returns (ga.game_pal_logic.ReportGamePalGreetingResponse) {
        option (ga.api.extension.command) = {
            id: 4062;
        };
    }

    // 获取超级发布属性
    rpc GetGamePalSuperPublishProps(ga.game_pal_logic.GetGamePalSuperPublishPropsRequest) returns (ga.game_pal_logic.GetGamePalSuperPublishPropsResponse) {
        option (ga.api.extension.command) = {
             id: 4063;
        };
    }

    // 获取一键 say hi 半屏游戏搭子列表
    rpc GetGreetingGamePalUserList(ga.game_pal_logic.GetGreetingGamePalUserListRequest) returns (ga.game_pal_logic.GetGreetingGamePalUserListResponse) {
        option (ga.api.extension.command) = {
            id: 4065;
        };
    }

    // 筛选满足打招呼条件的游戏搭子列表
    rpc FilterGreetingGamePalUserList(ga.game_pal_logic.FilterGreetingGamePalUserListRequest) returns (ga.game_pal_logic.FilterGreetingGamePalUserListResponse) {
        option (ga.api.extension.command) = {
            id: 4066;
        };
    }

    // 获取IM聊天需要展示的对方搭子卡信息
    rpc GetIMShowGamePalCard(ga.game_pal_logic.GetIMShowGamePalCardRequest) returns (ga.game_pal_logic.GetIMShowGamePalCardResponse) {
        option (ga.api.extension.command) = {
            id: 4067;
        };
    }

    // 判断是否展示引导用户填写搭子卡弹窗
    rpc ShowUserGamePalGuide(ga.game_pal_logic.ShowUserGamePalGuideRequest) returns (ga.game_pal_logic.ShowUserGamePalGuideResponse) {
        option (ga.api.extension.command) = {
            id: 4068;
        };
    }

    // 获取im tab搭子卡列表
    rpc GetImTabGamePalList(ga.game_pal_logic.GetImTabGamePalListReq) returns (ga.game_pal_logic.GetImTabGamePalListResp) {
        option (ga.api.extension.command) = {
            id: 4069;
        };
    }

    // 发布房间时处理擦亮搭子卡/创建引导逻辑
    rpc HandleGamePalCardOnPublishRoom(ga.game_pal_logic.HandleGamePalCardOnPublishRoomRequest) returns (ga.game_pal_logic.HandleGamePalCardOnPublishRoomResponse) {
        option (ga.api.extension.command) = {
            id: 4070;
        };
    }

    // 发布房间时处理擦亮搭子卡/创建引导逻辑
    rpc AssistantPushGamePal(ga.game_pal_logic.AssistantPushGamePalReq) returns (ga.game_pal_logic.AssistantPushGamePalResp) {
        option (ga.api.extension.command) = {
            id: 4071;
        };
    }
}