syntax = "proto3";
package ga.api.game_play_logic;

import "game_play_logic/game_play_logic.proto";
import "api/extension/extension.proto";

option java_package      = "com.quwan.tt.proto.api";
option objc_class_prefix = "RPC";
option go_package = "golang.52tt.com/protocol/app/api/game_play_logic;game_play_logic";

service GamePlayLogic {
    option (ga.api.extension.logic_service_name) = "game-play-logic";
    option (ga.api.extension.logic_service_language) = "go";

    // 用户对开黑用户评价触发上报
    rpc UserGameRateReport(ga.game_play_logic.UserGameRateReportReq) returns (ga.game_play_logic.UserGameRateReportResp) {
        option (ga.api.extension.command) = {
             id: 5901;
        };
    }

    // IM页面获取未评价的数量，展示红点
    rpc GetUserNotRateCount(ga.game_play_logic.GetUserNotRateCountReq) returns (ga.game_play_logic.GetUserNotRateCountResp) {
        option (ga.api.extension.command) = {
             id: 5902;
        };
    }

    // 获取IM聊天页首次触发上报是否满足下发评价信息的条件
    rpc SetFirstChatToken(ga.game_play_logic.SetFirstChatTokenReq) returns (ga.game_play_logic.SetFirstChatTokenResp) {
        option (ga.api.extension.command) = {
            id: 5903;
        };
    }

    // 获取互评标签和信誉分信息
    rpc GetRateReputationInfo(ga.game_play_logic.GetRateReputationInfoReq) returns (ga.game_play_logic.GetRateReputationInfoResp) {
        option (ga.api.extension.command) = {
            id: 5904;
        };
    }

    // 查询用户获客口径和AB实验结果
    rpc GetUserAcquisitionAndABTestResult(ga.game_play_logic.GetUserAcquisitionAndABTestResultReq) returns (ga.game_play_logic.GetUserAcquisitionAndABTestResultResp) {
        option (ga.api.extension.command) = {
            id: 5905;
        };
    }

    // 获取回归弹窗
    rpc GetRecallPopUp(ga.game_play_logic.GetRecallPopUpReq) returns (ga.game_play_logic.GetRecallPopUpResp) {
        option (ga.api.extension.command) = {
            id: 3108;
        };
    }

    // 提交回归弹窗
    rpc SubmitRecallPopUp(ga.game_play_logic.SubmitRecallPopUpReq) returns (ga.game_play_logic.SubmitRecallPopUpResp) {
        option (ga.api.extension.command) = {
            id: 3109;
        };
    }

    //获取组队弹窗
    rpc GetRecallTeamUp(ga.game_play_logic.GetRecallTeamUpReq) returns (ga.game_play_logic.GetRecallTeamUpResp) {
        option (ga.api.extension.command) = {
            id: 3110;
        };
    }

    // 提交组队弹窗
    rpc SubmitRecallTeamUp(ga.game_play_logic.SubmitRecallTeamUpReq) returns (ga.game_play_logic.SubmitRecallTeamUpResp) {
        option (ga.api.extension.command) = {
            id: 3111;
        };
    }

    // 音频转换文本asr
    rpc TransAudioToText(ga.game_play_logic.TransAudioToTextReq) returns (ga.game_play_logic.TransAudioToTextResp) {
        option (ga.api.extension.command) = {
            id: 5906;
        };
    }

    // 注册页，开黑配置选项
    rpc GetRegisterPageConfigs(ga.game_play_logic.GetRegisterPageConfigsReq) returns (ga.game_play_logic.GetRegisterPageConfigsResp) {
        option (ga.api.extension.command) = {
            id: 5907;
        };
    }

    // 保存游戏时间信息
    rpc SaveGameTime(ga.game_play_logic.SaveGameTimeRequest) returns (ga.game_play_logic.SaveGameTimeResponse) {
        option (ga.api.extension.command) = {
             id: 5908;
        };
    }

    // 获取游戏时间信息
    rpc GetGameTime(ga.game_play_logic.GetGameTimeRequest) returns (ga.game_play_logic.GetGameTimeResponse) {
        option (ga.api.extension.command) = {
             id: 5909;
        };
    }

    // 设置进房提醒
    rpc SetEnterRoomNotify(ga.game_play_logic.SetEnterRoomNotifyRequest) returns (ga.game_play_logic.SetEnterRoomNotifyResponse) {
        option (ga.api.extension.command) = {
             id: 5910;
        };
    }

    // 检查用户是否在线/在房
    rpc CheckUserInRoom(ga.game_play_logic.CheckUserInRoomReq) returns (ga.game_play_logic.CheckUserInRoomResp) {
        option (ga.api.extension.command) = {
            id: 5912;
        };
    }

    // 获取游戏时间列表
    rpc GetGameTimeList(ga.game_play_logic.GetGameTimeListRequest) returns (ga.game_play_logic.GetGameTimeListResponse) {
        option (ga.api.extension.command) = {
            id: 5913;
        };
    }

    rpc FastPCFeedback(ga.game_play_logic.FastPCFeedbackReq) returns (ga.game_play_logic.FastPCFeedbackResp) {
        option (ga.api.extension.command) = {
            id: 5914;
        };
    }

}
