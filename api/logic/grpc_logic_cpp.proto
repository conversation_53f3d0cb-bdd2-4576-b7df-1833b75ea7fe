syntax = "proto3";
package ga.api.logic;



import "api/extension/extension.proto";
import "game/game_.proto";
import "group/group_.proto";
import "contact/contact.proto";
import "official_account/official_account.proto";
import "session/session_.proto";
import "online/friendol_.proto";

//server_name=logic
//language=cpp

option java_package = "com.quwan.tt.proto.api";
option go_package = "golang.52tt.com/protocol/app/api/logic;logic";
option objc_class_prefix = "RPC";

service Logic {
    //friend.proto
    rpc AddFriend(ga.contact.AddFriendReq) returns (ga.contact.AddFriendResp) {
        option (ga.api.extension.command) = {
            id: 30
        };
    }

    rpc BanFriend(ga.contact.BanFriendReq) returns (ga.contact.BanFriendResp) {
        option (ga.api.extension.command) = {
            id: 34
        };
    }

    rpc CheckInviteFriendCode(ga.online.CheckInviteFriendCodeReq) returns (ga.online.CheckInviteFriendCodeResp) {
        option (ga.api.extension.command) = {
            id: 2190
        };
    }

    rpc CheckShowAddInviteCode(ga.online.CheckShowAddInviteCodeReq) returns (ga.online.CheckShowAddInviteCodeResp) {
        option (ga.api.extension.command) = {
            id: 2191
        };
    }

    rpc DeleteFriends(ga.contact.DeleteFriendReq) returns (ga.contact.DeleteFriendResp) {
        option (ga.api.extension.command) = {
            id: 24
        };
    }

    rpc MarkFriendWithStar(ga.contact.MarkFriendWithStarReq) returns (ga.contact.MarkFriendWithStarResp) {
        option (ga.api.extension.command) = {
            id: 35
        };
    }

    rpc RemarkFriend(ga.contact.RemarkFriendReq) returns (ga.contact.RemarkFriendResp) {
        option (ga.api.extension.command) = {
            id: 33
        };
    }

    rpc VerifyFriend(ga.contact.VerifyFriendReq) returns (ga.contact.VerifyFriendResp) {
        option (ga.api.extension.command) = {
            id: 31
        };
    }

    //game
    rpc CheckGameUpgrade(ga.game.CheckGameUpgradeReq) returns (ga.game.CheckGameUpgradeResp) {
        option (ga.api.extension.command) = {
            id: 330
        };
    }

    rpc FeaturedGames(ga.game.FeaturedGamesReq) returns (ga.game.FeaturedGamesResp) {
        option (ga.api.extension.command) = {
            id: 95
        };
    }

    // rpc GetGameTag(ga.game.GetGameTagReq) returns (ga.game.GetGameTagResp) {
    //     option (ga.api.extension.command) = {
    //         id: 343
    //     };
    // }

    rpc GetGameByTag(ga.game.GetGameByTagReq) returns (ga.game.GetGameByTagResp) {
        option (ga.api.extension.command) = {
            id: 342
        };
    }

    rpc GetDiscoverContent(ga.game.GetDiscoverPageContentReq) returns (ga.game.GetDiscoverPageContentResp) {
        option (ga.api.extension.command) = {
            id: 341
        };
    }

    rpc GetPopGame(ga.game.GetPopGameReq) returns (ga.game.GetPopGameResp) {
        option (ga.api.extension.command) = {
            id: 344
        };
    }

    rpc GetGameCenterTabList(ga.game.GetGameCenterTabListReq) returns (ga.game.GetGameCenterTabListResp) {
        option (ga.api.extension.command) = {
            id: 340
        };
    }

    rpc GameGetAnn(ga.game.GameGetAnnDetailReq) returns (ga.game.GameGetAnnDetailResp) {
        option (ga.api.extension.command) = {
            id: 316
        };
    }

    rpc GetBothLikeGameService(ga.game.GetBothLikeGameReq) returns (ga.game.GetBothLikeGameResp) {
        option (ga.api.extension.command) = {
            id: 492
        };
    }

    rpc GetGameConfig(ga.game.GetGameConfigReq) returns (ga.game.GetGameConfigResp) {
        option (ga.api.extension.command) = {
            id: 166;
            deprecated: true;
        };
    }

    rpc GetGameDiscoveryList(ga.game.GetDiscoveryGameReq) returns (ga.game.GetDiscoveryGameResp) {
        option (ga.api.extension.command) = {
            id: 324
        };
    }

    rpc GetGameRecommendCardListService(ga.game.GetGameRecommendCardListReq) returns (ga.game.GetGameRecommendCardListResp) {
        option (ga.api.extension.command) = {
            id: 701
        };
    }

    rpc GetGameTabList(ga.game.GetGameTabListReq) returns (ga.game.GetGameTabListResp) {
        option (ga.api.extension.command) = {
            id: 322
        };
    }

    rpc SearchGuildPlaying(ga.game.SearchGuildPlayingReq) returns (ga.game.SearchGuildPlayingResp) {
        option (ga.api.extension.command) = {
            id: 491
        };
    }

    rpc GetTopGameDownloadUrl(ga.game.TopGameGetDownloadUrlReq) returns (ga.game.TopGameGetDownloadUrlResp) {
        option (ga.api.extension.command) = {
            id: 321
        };
    }

    rpc GetTopGameList(ga.game.TopGameGetListReq) returns (ga.game.TopGameGetListResp) {
        option (ga.api.extension.command) = {
            id: 320
        };
    }

    rpc GuildPlayingGame(ga.game.GuildPlayingGameReq) returns (ga.game.GuildPlayingGameResp) {
        option (ga.api.extension.command) = {
            id: 167
        };
    }

    rpc HotGames(ga.game.HotGamesReq) returns (ga.game.HotGamesResp) {
        option (ga.api.extension.command) = {
            id: 83
        };
    }

    rpc IncreaseGamePackageDownloadCount(ga.game.IncreaseGamePackageDownloadCountReq) returns (ga.game.IncreaseGamePackageDownloadCountResp) {
        option (ga.api.extension.command) = {
            id: 201
        };
    }

    rpc MatchUserGames(ga.game.MatchUserGameReq) returns (ga.game.MatchUserGameResp) {
        option (ga.api.extension.command) = {
            id: 139
        };
    }

    rpc SearchGameAndCircle(ga.game.SearchGameAndCircleReq) returns (ga.game.SearchGameAndCircleResp) {
        option (ga.api.extension.command) = {
            id: 490
        };
    }

    rpc SearchGame(ga.game.SearchGameReq) returns (ga.game.SearchGameResp) {
        option (ga.api.extension.command) = {
            id: 93
        };
    }

    //group
    rpc GroupBatchDelBulletin(ga.group.GroupBatchDelBulletinReq) returns (ga.group.GroupDeleteBulletinResp) {
        option (ga.api.extension.command) = {
            id: 395
        };
    }

    rpc GroupDeleteBulletin(ga.group.GroupDeleteBulletinReq) returns (ga.group.GroupDeleteBulletinResp) {
        option (ga.api.extension.command) = {
            id: 444
        };
    }

    rpc GroupGetBulletins(ga.group.GroupGetBulletinsReq) returns (ga.group.GroupGetBulletinsResp) {
        option (ga.api.extension.command) = {
            id: 445
        };
    }

    rpc GroupPublishBulletin(ga.group.GroupPublishBulletinReq) returns (ga.group.GroupPublishBulletinResp) {
        option (ga.api.extension.command) = {
            id: 443
        };
    }

    //offical_account
    rpc OfficialAccountGetDetail(ga.official_account.GetOfficialAccountDetailReq) returns (ga.official_account.GetOfficialAccountDetailResp) {
        option (ga.api.extension.command) = {
            id: 185
        };
    }

    rpc OfficialAccountGetNewestMessage(ga.official_account.OfficialAccountGetNewestMessageReq) returns (ga.official_account.OfficialAccountGetNewestMessageResp) {
        option (ga.api.extension.command) = {
            id: 184
        };
    }

    // buf:lint:ignore RPC_PASCAL_CASE
    rpc unsubscribeOfficialAccount(ga.official_account.unsubscribeOfficialAccountReq) returns (ga.official_account.unsubscribeOfficialAccountResp) {
        option (ga.api.extension.command) = {
            id: 183
        };
    }

    rpc OfficialAccountUpdateSettings(ga.official_account.OfficialAccountUpdateSettingsReq) returns (ga.official_account.OfficialAccountUpdateSettingsResp) {
        option (ga.api.extension.command) = {
            id: 181
        };
    }

    //session
    rpc SessionCreateIn1v1(ga.session.SessionCreateIn1v1Req) returns (ga.session.SessionCreateIn1v1Resp) {
        option (ga.api.extension.command) = {
            id: 254
        };
    }

    rpc SessionCreateInGroup(ga.session.SessionCreateInGroupReq) returns (ga.session.SessionCreateInGroupResp) {
        option (ga.api.extension.command) = {
            id: 250
        };
    }

    rpc SessionJoin(ga.session.SessionJoinReq) returns (ga.session.SessionJoinResp) {
        option (ga.api.extension.command) = {
            id: 251
        };
    }

    rpc SessionQuit(ga.session.SessionQuitReq) returns (ga.session.SessionQuitResp) {
        option (ga.api.extension.command) = {
            id: 252
        };
    }

    rpc SessionReportUpdate(ga.session.SessionReportUpdateReq) returns (ga.session.SessionReportUpdateResp) {
        option (ga.api.extension.command) = {
            id: 253
        };
    }

    //temp_group
    rpc GroupAddMember(ga.group.GroupAddMemberReq) returns (ga.group.GroupAddMemberResp) {
        option (ga.api.extension.command) = {
            id: 75
        };
    }

    rpc GroupGetMemberList(ga.group.GroupGetMemberListReq) returns (ga.group.GroupGetMemberListResp) {
        option (ga.api.extension.command) = {
            id: 78
        };
    }

    rpc GroupModifyName(ga.group.GroupModifyNameReq) returns (ga.group.GroupModifyNameResp) {
        option (ga.api.extension.command) = {
            id: 77
        };
    }

    rpc GroupRemoveMember(ga.group.GroupRemoveMemberReq) returns (ga.group.GroupRemoveMemberResp) {
        option (ga.api.extension.command) = {
            id: 76
        };
    }

    option (ga.api.extension.logic_service_name) = "logic";
    option (ga.api.extension.logic_service_language) = "cpp";
}
