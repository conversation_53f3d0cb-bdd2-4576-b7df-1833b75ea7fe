syntax="proto3";

// buf:lint:ignore PACKAGE_DIRECTORY_MATCH
package quwan.api;

import "google/protobuf/any.proto";

option java_package = "com.quwan.tt.proto.api";
option go_package = "golang.52tt.com/protocol/app/api/status;status";
option objc_class_prefix = "RPC";

message CommonStatus {
    // 状态码
    int32 code = 1;
    // 错误信息
    string message = 2;
    // 错误详情
    repeated google.protobuf.Any details = 3;
}