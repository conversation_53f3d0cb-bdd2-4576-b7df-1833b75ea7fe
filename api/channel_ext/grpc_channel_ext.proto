syntax = "proto3";
package ga.api.channel_ext;



import "api/extension/extension.proto";
import "app/app.proto";
import "channel/channel_.proto";
import "channel/channel_personalization_.proto";

//server_name=channelextlogic
//language=cpp

option java_package = "com.quwan.tt.proto.api";
option go_package = "golang.52tt.com/protocol/app/api/channel_ext;channel_ext";
option objc_class_prefix = "RPC";

service ChannelExtLogic {
    rpc GetCharmAndRichLableInfo(ga.app.GetRichAndCharmLableInfoReq) returns (ga.app.GetRichAndCharmLableInfoResp) {
        option (ga.api.extension.command) = {
            id: 620
        };
    }

    rpc ChannelActivateUserDecoration(ga.channel.ActivateUserChannelDecorationReq) returns (ga.channel.ActivateUserChannelDecorationResp) {
        option (ga.api.extension.command) = {
            id: 2521
        };
    }

    rpc ChannelGetAdvList(ga.channel.GetChannelAdvReq) returns (ga.channel.GetChannelAdvResp) {
        option (ga.api.extension.command) = {
            id: 2081
        };
    }

    rpc ChannelGetCardList(ga.channel.GetChannelCardReq) returns (ga.channel.GetChannelCardResp) {
        option (ga.api.extension.command) = {
            id: 2076
        };
    }

    rpc ChannelGetChannelByTagId(ga.channel.GetChannelListByTagIdReq) returns (ga.channel.GetChannelListByTagIdResp) {
        option (ga.api.extension.command) = {
            id: 2077
        };
    }

    // buf:lint:ignore RPC_PASCAL_CASE
    rpc channelConsumeTopN(ga.channel.ChannelGetConsumTopNReq) returns (ga.channel.ChannelGetConsumTopNResp) {
        option (ga.api.extension.command) = {
            id: 458
             deprecated: true
             rewrite_full_path: "/ga.api.channel_rank.ChannelRankLogic/ChannelConsumeTopN"
        };
    }

    rpc ChannelGetGameMatchListByTagId(ga.channel.GetGameMatchListByTagIdReq) returns (ga.channel.GetGameMatchListByTagIdResp) {
        option (ga.api.extension.command) = {
            id: 2088
        };
    }

    rpc ChannelGetGameMatchListHomePage(ga.channel.GetGameMatchListHomePageReq) returns (ga.channel.GetGameMatchListHomePageResp) {
        option (ga.api.extension.command) = {
            id: 2087
        };
    }

    rpc ChannelGetGameMatchOptions(ga.channel.GetGameMatchOptionsReq) returns (ga.channel.GetGameMatchOptionsResp) {
        option (ga.api.extension.command) = {
            id: 2086
        };
    }

    rpc ChannelGetGfitHistory(ga.channel.GetChannelGiftHistoryReq) returns (ga.channel.GetChannelGiftHistoryResp) {
        option (ga.api.extension.command) = {
            id: 2051
        };
    }

    rpc ChannelGetHomeDetail(ga.channel.GetChannelHomeDetailReq) returns (ga.channel.GetChannelHomeDetailResp) {
        option (ga.api.extension.command) = {
            id: 2083
        };
    }

    rpc ChannelGetHotList(ga.channel.GetHotChannelListReq) returns (ga.channel.GetHotChannelListResp) {
        option (ga.api.extension.command) = {
            id: 2073
        };
    }

    rpc ChannelHourRankById(ga.channel.GetChannelHourRankByIdReq) returns (ga.channel.GetChannelHourRankByIdResp) {
        option (ga.api.extension.command) = {
            id: 623
            deprecated: true
        };
    }

    rpc ChannelHourRankTopN(ga.channel.GetChannelHourRankTopNReq) returns (ga.channel.GetChannelHourRankTopNResp) {
        option (ga.api.extension.command) = {
            id: 622
            deprecated: true
        };
    }

    rpc CChannelGetMicroUserGameTagService(ga.channel.ChannelGetMicroUserGameTagReq) returns (ga.channel.ChannelGetMicroUserGameTagResp) {
        option (ga.api.extension.command) = {
            id: 3502
        };
    }

    rpc ChannelGetNoviceRecommendStatus(ga.channel.GetNoviceRecommendChannelStatusReq) returns (ga.channel.GetNoviceRecommendChannelStatusResp) {
        option (ga.api.extension.command) = {
            id: 2720
        };
    }

    rpc ChannelGetRecommend(ga.channel.GetRecommendChannelListReq) returns (ga.channel.GetRecommendChannelListResp) {
        option (ga.api.extension.command) = {
            id: 2071
        };
    }

    rpc GetChannelRefreshCD(ga.channel.GetChannelRefreshCDReq) returns (ga.channel.GetChannelRefreshCDResp) {
        option (ga.api.extension.command) = {
            id: 2082
        };
    }

    // buf:lint:ignore RPC_PASCAL_CASE
    rpc channelGetShowSwitch(ga.channel.GetChannelShowSwitchReq) returns (ga.channel.GetChannelShowSwitchResp) {
        option (ga.api.extension.command) = {
            id: 2074
        };
    }

    rpc ChannelGetTagId(ga.channel.GetChannelTagIdReq) returns (ga.channel.GetChannelTagIdResp) {
        option (ga.api.extension.command) = {
            id: 2079
        };
    }

    rpc ChannelGetTagList(ga.channel.GetChannelTagListReq) returns (ga.channel.GetChannelTagListResp) {
        option (ga.api.extension.command) = {
            id: 2075
        };
    }

    rpc ChannelGetUserDecorationList(ga.channel.GetUserChannelDecorationListReq) returns (ga.channel.GetUserChannelDecorationListResp) {
        option (ga.api.extension.command) = {
            id: 2520
        };
    }

    rpc ChannelWeekConsumeTopN(ga.channel.ChannelGetWeekConsumeTopNReq) returns (ga.channel.ChannelGetWeekConsumeTopNResp) {
        option (ga.api.extension.command) = {
            id: 621
        };
    }

    rpc ChannelRefreshTime(ga.channel.RefreshChannelReq) returns (ga.channel.RefreshChannelResp) {
        option (ga.api.extension.command) = {
            id: 2080
        };
    }

    rpc ChannelSetTagId(ga.channel.SetChannelTagIdReq) returns (ga.channel.SetChannelTagIdResp) {
        option (ga.api.extension.command) = {
            id: 2078
        };
    }

    rpc ChannelStartGameMatch(ga.channel.StartGameMatchReq) returns (ga.channel.StartGameMatchResp) {
        option (ga.api.extension.command) = {
            id: 2089
        };
    }

    option (ga.api.extension.logic_service_name) = "channelextlogic";
    option (ga.api.extension.logic_service_language) = "cpp";
}
