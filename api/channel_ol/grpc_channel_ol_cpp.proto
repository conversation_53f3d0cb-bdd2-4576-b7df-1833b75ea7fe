syntax = "proto3";
package ga.api.channel_ol;



import "api/extension/extension.proto";
import "channel/channel_.proto";

//server_name=channelollogic
//language=cpp

option java_package = "com.quwan.tt.proto.api";
option go_package = "golang.52tt.com/protocol/app/api/channel_ol;channel_ol";
option objc_class_prefix = "RPC";

service ChannelOLLogic {
    rpc QuickJoinChannel(ga.channel.QuickJoinChannelReq) returns (ga.channel.QuickJoinChannelResp) {
        option (ga.api.extension.command) = {
            id: 436
            deprecated: true
            rewrite_full_path: "/ga.api.channelol_logic_go.ChannelolLogicGo/QuickJoinChannel"
        };
    }

    rpc ChannelGetHistory(ga.channel.GetChannelHistoryReq) returns (ga.channel.GetChannelHistoryResp) {
        option (ga.api.extension.command) = {
            id: 2050
            deprecated: true
            rewrite_full_path: "/ga.api.channelol_logic_go.ChannelolLogicGo/ChannelGetHistory"
        };
    }

    rpc ChannelGetMemberInfo(ga.channel.GetChannelMemberInfoReq) returns (ga.channel.GetChannelMemberInfoResp) {
        option (ga.api.extension.command) = {
            id: 459
            deprecated: true
            rewrite_full_path: "/ga.api.channelol_logic_go.ChannelolLogicGo/ChannelGetMemberInfo"
        };
    }

    rpc ChannelGetMemberList(ga.channel.ChannelGetMemberListReq) returns (ga.channel.ChannelGetMemberListResp) {
        option (ga.api.extension.command) = {
            id: 426
            deprecated: true
        };
    }

    rpc CChannelGetUnderTheMicroList(ga.channel.ChannelGetUnderTheMicroListReq) returns (ga.channel.ChannelGetUnderTheMicroListResp) {
        option (ga.api.extension.command) = {
            id: 3501
            deprecated: true
            rewrite_full_path: "/ga.api.channelol_logic_go.ChannelolLogicGo/CChannelGetUnderTheMicroList"
        };
    }

    rpc KickoutChannelMember(ga.channel.KickoutChannelReq) returns (ga.channel.KickoutChannelResp) {
        option (ga.api.extension.command) = {
            id: 439
            deprecated: true
            rewrite_full_path: "/ga.api.channelol_logic_go.ChannelolLogicGo/KickoutChannelMember"
        };
    }

    rpc ChannelPcHelperJoin(ga.channel.ChannelPcHelperEnterReq) returns (ga.channel.ChannelPcHelperEnterResp) {
        option (ga.api.extension.command) = {
            id: 2084
        };
    }

    option (ga.api.extension.logic_service_name) = "channelollogic";
    option (ga.api.extension.logic_service_language) = "cpp";
}
