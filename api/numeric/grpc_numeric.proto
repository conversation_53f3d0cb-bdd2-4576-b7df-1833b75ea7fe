// Code generated by protoc-gen-ga-api. DO NOT EDIT.
// 	protoc        v3.9.1

syntax = "proto3";
package ga.api.numeric;

import "numeric_logic/numeric-logic_.proto";
import "api/extension/extension.proto";

option java_package      = "com.quwan.tt.proto.api";
option objc_class_prefix = "RPC";
option go_package = "golang.52tt.com/protocol/app/api/numeric;numeric";

service NumericLogic {
    option (ga.api.extension.logic_service_name) = "numeric-logic";
    option (ga.api.extension.logic_service_language) = "go";
    option (ga.api.extension.logic_service_uri_rewrite) = "/logic.NumericLogic/";
    rpc GetUserRichSwitch(ga.numeric_logic.GetUserRichSwitchReq) returns (ga.numeric_logic.GetUserRichSwitchResp) {
        option (ga.api.extension.command) = {
             id: 31400; // 废弃
        };
    }
    rpc SetUserRichSwitch(ga.numeric_logic.SetUserRichSwitchReq) returns (ga.numeric_logic.SetUserRichSwitchResp) {
        option (ga.api.extension.command) = {
             id: 31401; // 废弃
        };
    }
    rpc GetUserNumericLock(ga.numeric_logic.GetUserNumericLockReq) returns (ga.numeric_logic.GetUserNumericLockResp) {
        option (ga.api.extension.command) = {
            id: 31402; // 获取用户财富值魅力值锁定状态
        };
    }
    rpc SetUserNumericLock(ga.numeric_logic.SetUserNumericLockReq) returns (ga.numeric_logic.SetUserNumericLockResp) {
        option (ga.api.extension.command) = {
            id: 31403; // 设置用户财富值魅力值锁定状态
        };
    }
    rpc GetUserGloryRank(ga.numeric_logic.GetUserGloryRankReq) returns (ga.numeric_logic.GetUserGloryRankResp) {
        option (ga.api.extension.command) = {
             id: 31410; // 获取荣誉榜单
        };
    }
    rpc GetUserVipGiftPackageInfo(ga.numeric_logic.GetUserVipGiftPackageInfoReq) returns (ga.numeric_logic.GetUserVipGiftPackageInfoResp) {
        option (ga.api.extension.command) = {
            id: 31415; // 获取用户财富值VIP礼包信息
        };
    }
}


