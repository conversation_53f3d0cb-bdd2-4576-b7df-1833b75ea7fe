// Code generated by protoc-gen-ga-api. DO NOT EDIT.
// 	protoc        v5.27.0

syntax = "proto3";
package ga.api.channel_play;

import "channel_play/channel-play_.proto";
import "hobby_channel/hobby-channel_.proto";
import "topic_channel/topic_channel_.proto";
import "api/extension/extension.proto";

option java_package      = "com.quwan.tt.proto.api";
option objc_class_prefix = "RPC";
option go_package = "golang.52tt.com/protocol/app/api/channel_play;channel_play";

service ChannelPlayLogic {
    option (ga.api.extension.logic_service_name) = "channel-play-logic";
    option (ga.api.extension.logic_service_language) = "go";
    option (ga.api.extension.logic_service_uri_rewrite) = "/logic.channel_play.ChannelPlayLogic/";
    rpc ListTopicChannel(ga.channel_play.ListTopicChannelReq) returns (ga.channel_play.ListTopicChannelResp) {
        option (ga.api.extension.command) = {
             id: 3080;
        };
    }
    rpc GetSecondaryFilter(ga.channel_play.GetSecondaryFilterReq) returns (ga.channel_play.GetSecondaryFilterResp) {
        option (ga.api.extension.command) = {
             id: 3081;
        };
    }
    rpc GetSecondaryFilterByCategory(ga.channel_play.GetSecondaryFilterByCategoryReq) returns (ga.channel_play.GetSecondaryFilterByCategoryResp) {
        option (ga.api.extension.command) = {
             id: 3082;
        };
    }
    rpc GetDefaultRoomNameList(ga.channel_play.GetDefaultRoomNameListReq) returns (ga.channel_play.GetDefaultRoomNameListResp) {
        option (ga.api.extension.command) = {
             id: 3083;
        };
    }
    rpc GetGameHomePageDIYFilter(ga.hobby_channel.GetGameHomePageDIYFilterReq) returns (ga.hobby_channel.GetGameHomePageDIYFilterResp) {
        option (ga.api.extension.command) = {
             id: 31510;
        };
    }
    rpc SetGameHomePageDIYFilter(ga.hobby_channel.SetGameHomePageDIYFilterReq) returns (ga.hobby_channel.SetGameHomePageDIYFilterResp) {
        option (ga.api.extension.command) = {
             id: 31511;
        };
    }
    rpc GetGameHomePageFilter(ga.hobby_channel.GetGameHomePageFilterReq) returns (ga.hobby_channel.GetGameHomePageFilterResp) {
        option (ga.api.extension.command) = {
             id: 31512;
        };
    }
    rpc CreateHobbyChannel(ga.hobby_channel.CreateHobbyChannelReq) returns (ga.hobby_channel.CreateHobbyChannelResp) {
        option (ga.api.extension.command) = {
             id: 31500;
        };
    }
    rpc PublishGangupChannel(ga.channel_play.PublishGangupChannelReq) returns (ga.channel_play.PublishGangupChannelResp) {
        option (ga.api.extension.command) = {
             id: 3084;
        };
    }
    rpc CancelGangupChannelPublish(ga.channel_play.CancelGangupChannelPublishReq) returns (ga.channel_play.CancelGangupChannelPublishResp) {
        option (ga.api.extension.command) = {
             id: 3085;
        };
    }
    rpc GetHomePageHeadConfig(ga.channel_play.HomePageHeadConfigReq) returns (ga.channel_play.HomePageHeadConfigResp) {
        option (ga.api.extension.command) = {
             id: 3086;
        };
    }
    rpc GetHotMiniGames(ga.channel_play.GetHotMiniGamesReq) returns (ga.channel_play.GetHotMiniGamesResp) {
        option (ga.api.extension.command) = {
             id: 3087;
        };
    }
    rpc GetQuickMiniGames(ga.channel_play.GetQuickMiniGamesReq) returns (ga.channel_play.GetQuickMiniGamesResp) {
        option (ga.api.extension.command) = {
             id: 3088;
        };
    }
    rpc GetPlayQuestions(ga.channel_play.GetPlayQuestionsReq) returns (ga.channel_play.GetPlayQuestionsResp) {
        option (ga.api.extension.command) = {
             id: 3089;
        };
    }
    rpc GameInsertFlowConfig(ga.channel_play.GameInsertFlowConfigReq) returns (ga.channel_play.GameInsertFlowConfigResp) {
        option (ga.api.extension.command) = {
             id: 3090;
        };
    }
    rpc GetHomePageGuide(ga.channel_play.GetHomePageGuideReq) returns (ga.channel_play.GetHomePageGuideResp) {
        option (ga.api.extension.command) = {
             id: 3091;
        };
    }
    rpc GetMoreTabConfig(ga.channel_play.GetMoreTabConfigReq) returns (ga.channel_play.GetMoreTabConfigResp) {
        option (ga.api.extension.command) = {
             id: 3092;
        };
    }
    rpc GetFilterItemByEntrance(ga.channel_play.GetFilterItemByEntranceReq) returns (ga.channel_play.GetFilterItemByEntranceResp) {
        option (ga.api.extension.command) = {
             id: 3093;
        };
    }
    rpc SetDIYFilterByEntrance(ga.channel_play.SetDIYFilterByEntranceReq) returns (ga.channel_play.SetDIYFilterByEntranceResp) {
        option (ga.api.extension.command) = {
             id: 3094;
        };
    }
    rpc GetDIYFilterByEntrance(ga.channel_play.GetDIYFilterByEntranceReq) returns (ga.channel_play.GetDIYFilterByEntranceResp) {
        option (ga.api.extension.command) = {
             id: 3095;
        };
    }
    rpc GetNegativeFeedBackInRoom(ga.channel_play.GetNegativeFeedBackInRoomReq) returns (ga.channel_play.GetNegativeFeedBackInRoomResp) {
        option (ga.api.extension.command) = {
             id: 3096;
        };
    }
    rpc ReportNegativeFeedBackInRoom(ga.channel_play.ReportNegativeFeedBackInRoomReq) returns (ga.channel_play.ReportNegativeFeedBackInRoomResp) {
        option (ga.api.extension.command) = {
             id: 3097;
        };
    }
    rpc GetPublishOptionGuide(ga.channel_play.GetPublishOptionGuideReq) returns (ga.channel_play.GetPublishOptionGuideResp) {
        option (ga.api.extension.command) = {
             id: 3098;
        };
    }
    rpc GetNewQuickMatchConfig(ga.channel_play.GetNewQuickMatchConfigReq) returns (ga.channel_play.GetNewQuickMatchConfigResp) {
        option (ga.api.extension.command) = {
             id: 3099;
        };
    }
    rpc GetTopicChannelCfgInfo(ga.channel_play.GetTopicChannelCfgInfoReq) returns (ga.channel_play.GetTopicChannelCfgInfoResp) {
        option (ga.api.extension.command) = {
             id: 3100;
        };
    }
    rpc SetUgcChannelPlayMode(ga.channel_play.SetUgcChannelPlayModeReq) returns (ga.channel_play.SetUgcChannelPlayModeResp) {
        option (ga.api.extension.command) = {
             id: 3101;
        };
    }
    rpc TypingStatusBroadcast(ga.channel_play.TypingStatusBroadcastReq) returns (ga.channel_play.TypingStatusBroadcastResp) {
        option (ga.api.extension.command) = {
             id: 3102;
        };
    }
    rpc GetChannelPlayModeGuide(ga.channel_play.GetChannelPlayModeGuideReq) returns (ga.channel_play.GetChannelPlayModeGuideResp) {
        option (ga.api.extension.command) = {
             id: 3103;
        };
    }
    rpc ReportDailyTask(ga.channel_play.ReportDailyTaskReq) returns (ga.channel_play.ReportDailyTaskResp) {
        option (ga.api.extension.command) = {
             id: 3104;
        };
    }
    rpc GetCache(ga.channel_play.GetCacheReq) returns (ga.channel_play.GetCacheResp) {
    }
    rpc ShowTopicChannelTabList(ga.topic_channel.ShowTopicChannelTabListReq) returns (ga.topic_channel.ShowTopicChannelTabListResp) {
        option (ga.api.extension.command) = {
             id: 3056;
        };
    }
    rpc HomePageHeadConfigEnterCheck(ga.channel_play.HomePageHeadConfigEnterCheckReq) returns (ga.channel_play.HomePageHeadConfigEnterCheckResp) {
        option (ga.api.extension.command) = {
             id: 3105;
        };
    }
    rpc GetChannelListGuideConfigs(ga.channel_play.GetChannelListGuideConfigsReq) returns (ga.channel_play.GetChannelListGuideConfigsResp) {
        option (ga.api.extension.command) = {
             id: 3106;
        };
    }
    rpc GetTabInfos(ga.channel_play.GetTabInfosReq) returns (ga.channel_play.GetTabInfosResp) {
        option (ga.api.extension.command) = {
             id: 3107;
        };
    }
    rpc GetRecommendGames(ga.channel_play.GetRecommendGamesReq) returns (ga.channel_play.GetRecommendGamesResp) {
        option (ga.api.extension.command) = {
            id: 3112;
        };
    }
    rpc RefreshGameLabel(ga.channel_play.RefreshGameLabelReq) returns (ga.channel_play.RefreshGameLabelResp) {
          option (ga.api.extension.command) = {
            id: 3113;
          };
    }
    rpc GetSupportTabList(ga.channel_play.GetSupportTabListReq) returns (ga.channel_play.GetSupportTabListResp) {
        option (ga.api.extension.command) = {
          id: 3114;
        };
    }
    rpc GetChannelMicVolSet(ga.channel_play.GetChannelMicVolSetReq) returns (ga.channel_play.GetChannelMicVolSetResp) {
      option (ga.api.extension.command) = {
        id: 3115;
      };
    }
    rpc SetChannelMicVol(ga.channel_play.SetChannelMicVolReq) returns (ga.channel_play.SetChannelMicVolResp) {
      option (ga.api.extension.command) = {
        id: 3116;
      };
    }

}


