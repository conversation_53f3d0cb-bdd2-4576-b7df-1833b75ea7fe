// Code generated by protoc-gen-ga-api. DO NOT EDIT.
// 	protoc        v3.9.1

syntax = "proto3";
package ga.api.client_gen_push;

import "clientgenpushlogic/client-gen-push_.proto";
import "api/extension/extension.proto";

option java_package      = "com.quwan.tt.proto.api";
option objc_class_prefix = "RPC";
option go_package = "golang.52tt.com/protocol/app/api/client_gen_push;client_gen_push";

service ClientGenPushLogic {
    option (ga.api.extension.logic_service_name) = "client-gen-push-logic";
    option (ga.api.extension.logic_service_language) = "go";
    option (ga.api.extension.logic_service_uri_rewrite) = "/logic.ClientGenPushLogic/";
    // buf:lint:ignore RPC_PASCAL_CASE
    rpc GA_GetGenPushSwitch(ga.clientgenpushlogic.GA_GetGenPushSwitchReq) returns (ga.clientgenpushlogic.GA_GetGenPushSwitchResp) {
        option (ga.api.extension.command) = {
             id: 30441
        };
    }
    // buf:lint:ignore RPC_PASCAL_CASE
    rpc GA_GetGenPushDoc(ga.clientgenpushlogic.GA_GetGenPushDocReq) returns (ga.clientgenpushlogic.GA_GetGenPushDocResp) {
        option (ga.api.extension.command) = {
             id: 30442
        };
    }
    // buf:lint:ignore RPC_PASCAL_CASE
    rpc GA_GetPushFactoryPrivateTemplate(ga.clientgenpushlogic.GA_GetPushFactoryPrivateTemplateReq) returns (ga.clientgenpushlogic.GA_GetPushFactoryPrivateTemplateResp) {
        option (ga.api.extension.command) = {
             id: 30443
        };
    }
    rpc GetNewUserPushCntLimit(ga.clientgenpushlogic.GetNewUserPushCntLimitReq) returns (ga.clientgenpushlogic.GetNewUserPushCntLimitResp) {
        option (ga.api.extension.command) = {
             id: 30444
        };
    }
}


