syntax = "proto3";
package ga.api.contract;

import "api/extension/extension.proto";
import "contract/contract.proto";

option java_package = "com.quwan.tt.proto.api";
option go_package = "golang.52tt.com/protocol/app/api/contract;contract";
option objc_class_prefix = "RPC";

service ContractLogicService {
    option (ga.api.extension.logic_service_name) = "contract-logic";
    option (ga.api.extension.logic_service_language) = "go";
    
    rpc CheckUserInteractEntry(ga.contract.CheckUserInteractEntryRequest) returns (ga.contract.CheckUserInteractEntryResponse) {
        option (ga.api.extension.command) = {
             id: 6800
        };
    }

    rpc GetUserInteractInfo(ga.contract.GetUserInteractInfoRequest) returns (ga.contract.GetUserInteractInfoResponse) {
        option (ga.api.extension.command) = {
             id: 6801
        };
    }

    rpc GetUserInteractViewPer(ga.contract.GetUserInteractViewPerRequest) returns (ga.contract.GetUserInteractViewPerResponse) {
        option (ga.api.extension.command) = {
             id: 6802
        };
    }

    rpc SetUserInteractViewPer(ga.contract.SetUserInteractViewPerRequest) returns (ga.contract.SetUserInteractViewPerResponse) {
        option (ga.api.extension.command) = {
             id: 6803
        };
    }

    rpc GetMultiPlayerHallTaskEntry(ga.contract.GetMultiPlayerHallTaskEntryRequest) returns (ga.contract.GetMultiPlayerHallTaskEntryResponse) {
        option (ga.api.extension.command) = {
             id: 32010
        };
    }

    rpc ContractClaimObsToken(ga.contract.ContractClaimObsTokenRequest) returns (ga.contract.ContractClaimObsTokenResponse) {
        option (ga.api.extension.command) = {
            id: 6804
        };
    }
    
}
