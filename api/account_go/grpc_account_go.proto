syntax = "proto3";
package ga.api.account_go;

import "auth/auth.proto";
import "contact/contact.proto";
import "myinfo/my_info.proto";
import "api/extension/extension.proto";

option java_package      = "com.quwan.tt.proto.api";
option objc_class_prefix = "RPC";
option go_package = "golang.52tt.com/protocol/app/api/account-go-logic;account-go-logic";

service AccountGoLogic {
    option (ga.api.extension.logic_service_name) = "account-go-logic";
    option (ga.api.extension.logic_service_language) = "go";
    option (ga.api.extension.logic_service_uri_rewrite) = "/logic.AccountGoLogic/";
    rpc GetUnregApplyAuditStatus(ga.auth.GetUnregApplyAuditStatusReq) returns (ga.auth.GetUnregApplyAuditStatusResp) {
        option (ga.api.extension.command) = {
             id: 415;
        };
    }

    rpc ClickUserDetailPreCheck(ga.contact.ClickUserDetailPreCheckReq) returns (ga.contact.ClickUserDetailPreCheckResp) {
        option (ga.api.extension.command) = {
            id: 197;
        };
    }

    rpc GetUserDetail(ga.contact.GetUserDetailReq) returns (ga.contact.GetUserDetailResp) {
        option (ga.api.extension.command) = {
             id: 27;
        };
    }

    rpc GetPhotoAlbum(ga.myinfo.GetPhotoAlbumReq) returns (ga.myinfo.GetPhotoAlbumResp) {
        option (ga.api.extension.command) = {
             id: 1201;
        };
    }

    rpc CheckRelatedLoginAccount(ga.contact.CheckRelatedLoginAccountReq) returns (ga.contact.CheckRelatedLoginAccountResp) {
        option (ga.api.extension.command) = {
            id: 604
        };
    }

    rpc GetUserStatus(ga.contact.GetUserStatusReq) returns (ga.contact.GetUserStatusResp) {
        option (ga.api.extension.command) = {
            id: 416
        };
    }

    rpc GetUserOnlineTerminalList(ga.contact.GetUserOnlineTerminalListReq) returns (ga.contact.GetUserOnlineTerminalListResp) {
        option (ga.api.extension.command) = {
            id: 601
        };
    }

    rpc ModifyNickname(ga.myinfo.ModifyNicknameReq) returns (ga.myinfo.ModifyNicknameResp) {
        option (ga.api.extension.command) = {
            id: 62
        };
    }

    rpc ModifySignature(ga.myinfo.ModifySignatureReq) returns (ga.myinfo.ModifySignatureResp) {
        option (ga.api.extension.command) = {
            id: 63
        };
    }

    rpc ModifySex(ga.auth.ModifySexReq) returns (ga.auth.ModifySexResp) {
        option (ga.api.extension.command) = {
            id: 80
        };
    }

    rpc ModifyPWD(ga.auth.ModifyPWDReq) returns (ga.auth.ModifyPWDResp) {
        option (ga.api.extension.command) = {
            id: 16
        };
    }

    rpc UserKickTerminal(ga.contact.UserKickTerminalReq) returns (ga.contact.UserKickTerminalResp) {
        option (ga.api.extension.command) = {
            id: 602
        };
    }

    rpc RebindPhone(ga.auth.RebindPhoneReq) returns (ga.auth.RebindPhoneResp) {
        option (ga.api.extension.command) = {
            id: 403
        };
    }

//    rpc GeneralCheckVerifyCode(ga.auth.GeneralCheckVerifyCodeReq) returns (ga.auth.GeneralCheckVerifyCodeResp) {
//        option (ga.api.extension.command) = {
//            id: 849
//        };
//    }
//
//    rpc GeneralSendVerifyCode(ga.auth.GeneralSendVerifyCodeReq) returns (ga.auth.GeneralSendVerifyCodeResp) {
//        option (ga.api.extension.command) = {
//            id: 848
//        };
//    }
//
//    rpc GetRelatedLoginAccount(ga.contact.GetRelatedLoginAccountReq) returns (ga.contact.GetRelatedLoginAccountResp) {
//        option (ga.api.extension.command) = {
//            id: 605
//        };
//    }
//
//    rpc GetUserCertificationList(ga.myinfo.GetUserCertifyListReq) returns (ga.myinfo.GetUserCertifyListResp) {
//        option (ga.api.extension.command) = {
//            id: 233
//        };
//    }
//
//    rpc UpdatePhotoAlbum(ga.myinfo.UpdatePhotoAlbumReq) returns (ga.myinfo.UpdatePhotoAlbumResp) {
//        option (ga.api.extension.command) = {
//            id: 1200
//        };
//    }
}


