// Code generated by protoc-gen-ga-api. DO NOT EDIT.
// 	protoc        v3.9.1

syntax = "proto3";
package ga.api.muse_interest_hub;

import "muse_interest_hub_logic/muse_interest_hub_logic.proto";
import "api/extension/extension.proto";

option java_package      = "com.quwan.tt.proto.api";
option objc_class_prefix = "RPC";
option go_package = "golang.52tt.com/protocol/app/api/muse_interest_hub;muse_interest_hub";

service MuseInterestHubLogic {
    option (ga.api.extension.logic_service_name) = "muse-interest-hub-logic";
    option (ga.api.extension.logic_service_language) = "go";
    option (ga.api.extension.logic_service_uri_rewrite) = "/logic.MuseInterestHubLogic/";
    rpc MuseCommonReport(ga.muse_interest_hub_logic.MuseCommonReportRequest) returns (ga.muse_interest_hub_logic.MuseCommonReportResponse) {
        option (ga.api.extension.command) = {
             id: 31560;
        };
    }
    rpc GetMuseSwitchHub(ga.muse_interest_hub_logic.GetMuseSwitchHubRequest) returns (ga.muse_interest_hub_logic.GetMuseSwitchHubResponse) {
        option (ga.api.extension.command) = {
             id: 31561;
        };
    }
    rpc SetMuseSwitchHub(ga.muse_interest_hub_logic.SetMuseSwitchHubRequest) returns (ga.muse_interest_hub_logic.SetMuseSwitchHubResponse) {
        option (ga.api.extension.command) = {
             id: 31562;
        };
    }

    rpc CheckJumpSquarePage(ga.muse_interest_hub_logic.CheckJumpSquarePageRequest) returns (ga.muse_interest_hub_logic.CheckJumpSquarePageResponse) {
        option (ga.api.extension.command) = {
            id: 31564;
        };
    }
    // 获取MT实验策略内容
    rpc GetMTExperimentStrategy(ga.muse_interest_hub_logic.GetMTExperimentStrategyRequest) returns (ga.muse_interest_hub_logic.GetMTExperimentStrategyResponse) {
        option (ga.api.extension.command) = {
            id: 31565;
        };
    }
    // 获取优先展示的首页跟随进房信息
    rpc GetHomeFollowFloat(ga.muse_interest_hub_logic.GetHomeFollowFloatRequest) returns (ga.muse_interest_hub_logic.GetHomeFollowFloatResponse) {
        option (ga.api.extension.command) = {
            id: 31566;
        };
    }
    // 跟随进房半屏页用户列表
    rpc FollowChannelHalfScreenUserList(ga.muse_interest_hub_logic.FollowChannelHalfScreenUserListRequest) returns (ga.muse_interest_hub_logic.FollowChannelHalfScreenUserListResponse) {
        option (ga.api.extension.command) = {
            id: 31567;
        };
    }

    rpc ChannelInviteFriendPreprocessing(ga.muse_interest_hub_logic.ChannelInviteFriendPreprocessingRequest) returns (ga.muse_interest_hub_logic.ChannelInviteFriendPreprocessingResponse) {
        option (ga.api.extension.command) = {
            id: 31568;
        };
    }

    rpc ChannelSpecialFriendList(ga.muse_interest_hub_logic.ChannelSpecialFriendListRequest) returns (ga.muse_interest_hub_logic.ChannelSpecialFriendListResponse) {
        option (ga.api.extension.command) = {
            id: 31569;
        };
    }

    rpc OneClickInviteAllInfo(ga.muse_interest_hub_logic.OneClickInviteAllInfoRequest) returns (ga.muse_interest_hub_logic.OneClickInviteAllInfoResponse) {
        option (ga.api.extension.command) = {
            id: 31571;
        };
    }

    rpc OneClickInviteAll(ga.muse_interest_hub_logic.OneClickInviteAllRequest) returns (ga.muse_interest_hub_logic.OneClickInviteAllResponse) {
        option (ga.api.extension.command) = {
            id: 31572;
        };
    }

  rpc CheckIsInOtherUsersBlackList(ga.muse_interest_hub_logic.CheckIsInOtherUsersBlackListRequest) returns (ga.muse_interest_hub_logic.CheckIsInOtherUsersBlackListResponse) {
    option (ga.api.extension.command) = {
      id: 31573;
    };
  }

  rpc GetUserCurrentChannelId(ga.muse_interest_hub_logic.GetUserCurrentChannelIdRequest) returns (ga.muse_interest_hub_logic.GetUserCurrentChannelIdResponse) {
    option (ga.api.extension.command) = {
      id: 31574;
    };
  }


    rpc OneClickInviteAllGetMicId(ga.muse_interest_hub_logic.OneClickInviteAllGetMicIdRequest) returns (ga.muse_interest_hub_logic.OneClickInviteAllGetMicIdResponse) {
        option (ga.api.extension.command) = {
            id: 31575;
        };
    }

  rpc AutoInviteMicPanel(ga.muse_interest_hub_logic.AutoInviteMicPanelRequest) returns (ga.muse_interest_hub_logic.AutoInviteMicPanelResponse) {
    option (ga.api.extension.command) = {
      id: 31576;
    };
  }

  rpc GetUserRelationship(ga.muse_interest_hub_logic.GetUserRelationshipRequest) returns (ga.muse_interest_hub_logic.GetUserRelationshipResponse) {
    option (ga.api.extension.command) = {
      id: 31577;
    };
  }

  rpc NonMicUserListOrder(ga.muse_interest_hub_logic.NonMicUserListOrderRequest) returns (ga.muse_interest_hub_logic.NonMicUserListOrderResponse) {
    option (ga.api.extension.command) = {
      id: 31578;
    };
  }


  rpc GetRoomExitGuideFollowList(ga.muse_interest_hub_logic.GetRoomExitGuideFollowListRequest) returns (ga.muse_interest_hub_logic.GetRoomExitGuideFollowListResponse) {
    option (ga.api.extension.command) = {
      id: 31579;
    };
  }

  rpc ShowRoomExitFollowPopup(ga.muse_interest_hub_logic.ShowRoomExitFollowPopupRequest) returns (ga.muse_interest_hub_logic.ShowRoomExitFollowPopupResponse) {
    option (ga.api.extension.command) = {
      id: 31580;
    };
  }

  rpc GetRoomIcebreakerPopup(ga.muse_interest_hub_logic.GetRoomIcebreakerPopupRequest) returns (ga.muse_interest_hub_logic.GetRoomIcebreakerPopupResponse) {
    option (ga.api.extension.command) = {
      id: 31581;
    };
  }
      rpc SendPublicMessageDirectly(ga.muse_interest_hub_logic.SendPublicMessageDirectlyRequest) returns (ga.muse_interest_hub_logic.SendPublicMessageDirectlyResponse) {
    option (ga.api.extension.command) = {
      id: 31582;
    };
  }

  rpc GetHiddenHomePageZoneConfig(ga.muse_interest_hub_logic.GetHiddenHomePageZoneConfigRequest) returns (ga.muse_interest_hub_logic.GetHiddenHomePageZoneConfigResponse) {
    option (ga.api.extension.command) = {
      id: 31583;
    };
  }

  rpc GetHiddenChannelCategoryTypeConfig(ga.muse_interest_hub_logic.GetHiddenChannelCategoryTypeConfigRequest) returns (ga.muse_interest_hub_logic.GetHiddenChannelCategoryTypeConfigResponse) {
    option (ga.api.extension.command) = {
      id: 31584;
    };
  }

  rpc GetMuseRecommendEmojis(ga.muse_interest_hub_logic.GetMuseRecommendEmojisRequest) returns (ga.muse_interest_hub_logic.GetMuseRecommendEmojisResponse) {
    option (ga.api.extension.command) = {
      id: 31585;
    };
  }

  rpc GetCustomIcebreakerPopupSwitch(ga.muse_interest_hub_logic.GetCustomIcebreakerPopupSwitchRequest) returns (ga.muse_interest_hub_logic.GetCustomIcebreakerPopupSwitchResponse) {
    option (ga.api.extension.command) = {
      id: 31586;
    };
  }

  rpc SetCustomIcebreakerPopupSwitch(ga.muse_interest_hub_logic.SetCustomIcebreakerPopupSwitchRequest) returns (ga.muse_interest_hub_logic.SetCustomIcebreakerPopupSwitchResponse) {
    option (ga.api.extension.command) = {
      id: 31587;
    };
  }

  rpc UpdateCustomIcebreakerPopup(ga.muse_interest_hub_logic.UpdateCustomIcebreakerPopupRequest) returns (ga.muse_interest_hub_logic.UpdateCustomIcebreakerPopupResponse) {
    option (ga.api.extension.command) = {
      id: 31588;
    };
  }

  rpc GetCustomIcebreakerConfigInfo(ga.muse_interest_hub_logic.GetCustomIcebreakerConfigInfoRequest) returns (ga.muse_interest_hub_logic.GetCustomIcebreakerConfigInfoResponse) {
    option (ga.api.extension.command) = {
      id: 31589;
    };
  }
  rpc GetPublicScreenShortcutImage(ga.muse_interest_hub_logic.GetPublicScreenShortcutImageRequest) returns (ga.muse_interest_hub_logic.GetPublicScreenShortcutImageResponse) {
    option (ga.api.extension.command) = {
      id: 31590;
    };
  }


}


