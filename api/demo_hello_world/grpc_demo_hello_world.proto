// Code generated by protoc-gen-ga-api. DO NOT EDIT.
// 	protoc        v3.9.1

syntax = "proto3";
package ga.api.demo_hello_world;

import "demo_hello_world_logic/demo-helloworld-logic.proto";
import "api/extension/extension.proto";

option java_package      = "com.quwan.tt.proto.api";
option objc_class_prefix = "RPC";
option go_package = "golang.52tt.com/protocol/app/api/demo_hello_world;demo_hello_world";

service DemoHelloWorldLogic {
    option (ga.api.extension.logic_service_name) = "demo-helloworld-logic";
    option (ga.api.extension.logic_service_language) = "go";
    option (ga.api.extension.logic_service_uri_rewrite) = "/logic.ugc.DemoHelloWorldLogic/";
    // buf:lint:ignore RPC_PASCAL_CASE
    rpc GA_AddAndEcho(ga.demo_hello_world_logic.GA_AddAndEchoReq) returns (ga.demo_hello_world_logic.GA_AddAndEchoResp) {
        option (ga.api.extension.command) = {
             id: 50002
        };
    }
}

