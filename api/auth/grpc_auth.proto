// Code generated by protoc-gen-ga-api. DO NOT EDIT.
// 	protoc        v5.27.0

syntax = "proto3";
package ga.api.auth;

import "auth/auth.proto";
import "game/game_.proto";
import "im_activity_center_logic/im-activity-center-logic_.proto";
import "api/extension/extension.proto";

option java_package      = "com.quwan.tt.proto.api";
option objc_class_prefix = "RPC";
option go_package = "golang.52tt.com/protocol/app/api/auth;auth";

service AuthLogic {
    option (ga.api.extension.logic_service_name) = "auth-logic";
    option (ga.api.extension.logic_service_language) = "go";
    option (ga.api.extension.logic_service_uri_rewrite) = "/logic.AuthLogic/";
    rpc Auth(ga.auth.AuthReq) returns (ga.auth.AuthResp) {
        option (ga.api.extension.command) = {
             id: 10;
        };
    }
    rpc Reg(ga.auth.RegReq) returns (ga.auth.RegResp) {
        option (ga.api.extension.command) = {
             id: 12;
        };
    }
    rpc ThirdPartyAuth(ga.auth.ThirdPartyAuthReq) returns (ga.auth.ThirdPartyAuthResp) {
        option (ga.api.extension.command) = {
             id: 210;
        };
    }
    rpc ThirdPartyReg(ga.auth.ThirdPartyRegReq) returns (ga.auth.ThirdPartyRegResp) {
        option (ga.api.extension.command) = {
             id: 211;
        };
    }
    rpc AccountVerifyCode(ga.auth.AccountVerifyCodeReq) returns (ga.auth.AccountVerifyCodeResp) {
        option (ga.api.extension.command) = {
             id: 14;
        };
    }
    rpc AccountVoiceVerifyCode(ga.auth.AccountVoiceVerifyCodeReq) returns (ga.auth.AccountVoiceVerifyCodeResp) {
        option (ga.api.extension.command) = {
             id: 5050;
        };
    }
    rpc SubmitVerifyCode(ga.auth.SubmitVerifyCodeReq) returns (ga.auth.SubmitVerifyCodeResp) {
        option (ga.api.extension.command) = {
             id: 232;
        };
    }
    rpc ThirdpartyVerifyCheck(ga.auth.ThridpartVerifyCheckReq) returns (ga.auth.ThridpartVerifyCheckResp) {
        option (ga.api.extension.command) = {
             id: 214;
        };
    }
    rpc BindPhoneBeforeAuth(ga.auth.BindPhoneBeforeAuthReq) returns (ga.auth.BindPhoneBeforeAuthResp) {
        option (ga.api.extension.command) = {
             id: 404;
        };
    }
    rpc BindPhoneAfterAuth(ga.auth.BindPhoneAfterAuthReq) returns (ga.auth.BindPhoneAfterAuthResp) {
        option (ga.api.extension.command) = {
             id: 405;
        };
    }
    rpc GetPcAuthApplyResult(ga.auth.GetPcAuthApplyResultReq) returns (ga.auth.GetPcAuthApplyResultResp) {
        option (ga.api.extension.command) = {
             id: 6600;
        };
    }
    rpc GetPcAuthApply(ga.auth.GetPcAuthApplyReq) returns (ga.auth.GetPcAuthApplyResp) {
        option (ga.api.extension.command) = {
             id: 6601;
        };
    }
    rpc ProcPcAuthApply(ga.auth.ProcPcAuthApplyReq) returns (ga.auth.ProcPcAuthApplyResp) {
        option (ga.api.extension.command) = {
             id: 6602;
        };
    }
    rpc GetPcAuthApplyResultV2(ga.auth.GetPcAuthApplyResultReq) returns (ga.auth.GetPcAuthApplyResultResp) {
        option (ga.api.extension.command) = {
             id: 6603;
        };
    }
    rpc ProcPcAuthApplyBeforeAuth(ga.auth.ProcPcAuthApplyReq) returns (ga.auth.ProcPcAuthApplyResp) {
        option (ga.api.extension.command) = {
             id: 6604;
        };
    }
    rpc GetGameConfig(ga.game.GetGameConfigReq) returns (ga.game.GetGameConfigResp) {
        option (ga.api.extension.command) = {
             id: 166;
        };
    }
    rpc ImActivityCenterEntrance(ga.im_activity_center_logic.ImActivityCenterEntranceReq) returns (ga.im_activity_center_logic.ImActivityCenterEntranceResp) {
        option (ga.api.extension.command) = {
             id: 30380;
        };
    }
}


