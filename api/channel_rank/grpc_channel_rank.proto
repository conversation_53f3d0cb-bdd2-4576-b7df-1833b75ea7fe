syntax = "proto3";
package ga.api.channel_rank;

import "channel/channel_.proto";
import "channel_rank/channel_rank.proto";
import "api/extension/extension.proto";

option java_package      = "com.quwan.tt.proto.api";
option objc_class_prefix = "RPC";
option go_package = "golang.52tt.com/protocol/app/api/channel_rank;channel_rank";

service ChannelRankLogic {
    option (ga.api.extension.logic_service_name) = "channel-rank-logic";
    option (ga.api.extension.logic_service_language) = "go";

    rpc GetChannelHourRankTop1List(ga.channel.GetChannelHourRankTop1ListRequest) returns (ga.channel.GetChannelHourRankTop1ListResponse) {
        option (ga.api.extension.command) = {
             id: 624 // 小时榜 Top3 欢游
        };
    }

    rpc ChannelHourRankTopN(ga.channel.GetChannelHourRankTopNReq) returns (ga.channel.GetChannelHourRankTopNResp) {
        option (ga.api.extension.command) = {
            id: 622 // 小时榜
        };
    }
    rpc ChannelHourRankById(ga.channel.GetChannelHourRankByIdReq) returns (ga.channel.GetChannelHourRankByIdResp) {
        option (ga.api.extension.command) = {
            id: 623 // 623
        };
    }

    rpc ChannelGetMemberList(ga.channel.ChannelGetMemberListReq) returns (ga.channel.ChannelGetMemberListResp) {
        option (ga.api.extension.command) = {
            id: 426 // 在线榜
        };
    }

    rpc GetLiveFansWeekRank(ga.channel_rank.GetLiveFansWeekRankRequest) returns (ga.channel_rank.GetLiveFansWeekRankResponse) {
        option (ga.api.extension.command) = {
            id: 32011 
        };
    }

    // 爱意榜
    rpc ChannelConsumeTopN(ga.channel.ChannelGetConsumTopNReq) returns (ga.channel.ChannelGetConsumTopNResp) {
         option (ga.api.extension.command) = {
             id: 458
         };
    }
}

