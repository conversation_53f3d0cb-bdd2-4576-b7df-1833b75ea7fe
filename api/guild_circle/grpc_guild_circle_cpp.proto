syntax = "proto3";
package ga.api.guild_circle;



import "api/extension/extension.proto";
import "guild/guildcircle_.proto";

//server_name=guildcirclelogic
//language=cpp

option java_package = "com.quwan.tt.proto.api";
option go_package = "golang.52tt.com/protocol/app/api/guild_circle;guild_circle";
option objc_class_prefix = "RPC";

service GuildCircleLogic {
    // buf:lint:ignore RPC_PASCAL_CASE
    rpc CMD_GuildCircleAddTopicTag(ga.guild.GuildCircleAddTopicTagReq) returns (ga.guild.GuildCircleAddTopicTagResp) {
        option (ga.api.extension.command) = {
            id: 2010
        };
    }

    rpc GuildCircleDeleteComment(ga.guild.GuildCircleDeleteCommentReq) returns (ga.guild.GuildCircleDeleteCommentResp) {
        option (ga.api.extension.command) = {
            id: 2009
        };
    }

    rpc GuildCircleDeleteTopic(ga.guild.GuildCircleDeleteTopicReq) returns (ga.guild.GuildCircleDeleteTopicResp) {
        option (ga.api.extension.command) = {
            id: 2008
        };
    }

    rpc GuildCircleGetCommentList(ga.guild.GuildCircleGetNomalCommentListReq) returns (ga.guild.GuildCircleGetNomalCommentListResp) {
        option (ga.api.extension.command) = {
            id: 2005
        };
    }

    rpc GuildCircleGetCommentReplyList(ga.guild.GuildCircleGetCommentReplyListReq) returns (ga.guild.GuildCircleGetCommentReplyListResp) {
        option (ga.api.extension.command) = {
            id: 2004
        };
    }

    rpc GuildCircleGetLikeUserList(ga.guild.GuildCircleGetLikeUserListReq) returns (ga.guild.GuildCircleGetLikeUserListResp) {
        option (ga.api.extension.command) = {
            id: 2007
        };
    }

    rpc CircleGetTopicDetail(ga.guild.GuildCircleGetTopicReq) returns (ga.guild.GuildCircleGetTopicResp) {
        option (ga.api.extension.command) = {
            id: 2002
        };
    }

    rpc GuildCircleGetTopicList(ga.guild.GuildCircleGetTopicListReq) returns (ga.guild.GuildCircleGetTopicListResp) {
        option (ga.api.extension.command) = {
            id: 2001
        };
    }

    rpc GuildCircleLikeTopic(ga.guild.GuildCircleLikeTopicReq) returns (ga.guild.GuildCircleLikeTopicResp) {
        option (ga.api.extension.command) = {
            id: 2006
        };
    }

    rpc GuildCirclePostComment(ga.guild.GuildCirclePostCommentReq) returns (ga.guild.GuildCirclePostCommentResp) {
        option (ga.api.extension.command) = {
            id: 2003
        };
    }

    rpc GuildCirclePostTopic(ga.guild.GuildCirclePostTopicReq) returns (ga.guild.GuildCirclePostTopicResp) {
        option (ga.api.extension.command) = {
            id: 2000
        };
    }

    option (ga.api.extension.logic_service_name) = "guildcirclelogic";
    option (ga.api.extension.logic_service_language) = "cpp";
}
