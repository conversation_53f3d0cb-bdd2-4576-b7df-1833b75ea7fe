syntax = "proto3";
package ga.api.guild;



// import "ancient-search_.proto";
import "api/extension/extension.proto";
import "guild/guild2_.proto";
import "guild/guild_.proto";

//server_name=guildlogic
//language=cpp

option java_package = "com.quwan.tt.proto.api";
option go_package = "golang.52tt.com/protocol/app/api/guild;guild";
option objc_class_prefix = "RPC";

service GuildLogic {
    //guild_album
    rpc GuildAddNewPhoto(ga.guild.GuildAddNewPhotoReq) returns (ga.guild.GuildAddNewPhotoResp) {
        option (ga.api.extension.command) = {
            id: 111
        };
    }

    rpc GuildAlbumCreate(ga.guild.GuildAlbumCreateReq) returns (ga.guild.GuildAlbumCreateResp) {
        option (ga.api.extension.command) = {
            id: 43
        };
    }

    rpc GuildDeleteAlbum(ga.guild.GuildDeleteAlbumReq) returns (ga.guild.GuildDeleteAlbumResp) {
        option (ga.api.extension.command) = {
            id: 69
        };
    }

    rpc GuildDeletePhotoNews(ga.guild.GuildDeletePhotoNewsReq) returns (ga.guild.GuildDeletePhotoNewsResp) {
        option (ga.api.extension.command) = {
            id: 113
        };
    }

    rpc GuildDeletePhoto(ga.guild.GuildDeletePhotoReq) returns (ga.guild.GuildDeletePhotoResp) {
        option (ga.api.extension.command) = {
            id: 70
        };
    }

    rpc GuildGetDefaultAlbumPhotoList(ga.guild.GuildGetDefaultAlbumPhotoListReq) returns (ga.guild.GuildGetDefaultAlbumPhotoListResp) {
        option (ga.api.extension.command) = {
            id: 138
        };
    }

    rpc GuildGetMyGuildAlbumList(ga.guild.GuildGetMyGuildAlbumListReq) returns (ga.guild.GuildGetMyGuildAlbumListResp) {
        option (ga.api.extension.command) = {
            id: 110
        };
    }

    rpc GuildGetPhotoList(ga.guild.GuildGetPhotoListReq) returns (ga.guild.GuildGetPhotoListResp) {
        option (ga.api.extension.command) = {
            id: 45
        };
    }

    rpc GuildGetPhotoNewsList(ga.guild.GuildGetPhotoNewsListReq) returns (ga.guild.GuildGetPhotoNewsListResp) {
        option (ga.api.extension.command) = {
            id: 112
        };
    }

    rpc GuildModifyAlbumName(ga.guild.GuildModifyAlbumNameReq) returns (ga.guild.GuildModifyAlbumNameResp) {
        option (ga.api.extension.command) = {
            id: 109
        };
    }

    //guild_storage
    rpc GuildProductUpLoad(ga.guild.GuildProductUpLoadReq) returns (ga.guild.GuildProductUpLoadResp) {
        option (ga.api.extension.command) = {
            id: 814
        };
    }

    rpc GuildProductAllot(ga.guild.GuildProductAllotReq) returns (ga.guild.GuildProductAllotResp) {
        option (ga.api.extension.command) = {
            id: 812
        };
    }

    rpc GuildProductModify(ga.guild.GuildProductModifyReq) returns (ga.guild.GuildProductModifyResp) {
        option (ga.api.extension.command) = {
            id: 811
        };
    }

    rpc GuildStorageGetOperRecord(ga.guild.GuildStorageGetOperRecordReq) returns (ga.guild.GuildStorageGetOperRecordResp) {
        option (ga.api.extension.command) = {
            id: 817
        };
    }

    rpc GuildProductExamine(ga.guild.GuildProductExamineReq) returns (ga.guild.GuildProductExamineResp) {
        option (ga.api.extension.command) = {
            id: 816
        };
    }

    rpc GuildProductRemove(ga.guild.GuildRemoveProductReq) returns (ga.guild.GuildRemoveProductResp) {
        option (ga.api.extension.command) = {
            id: 818
        };
    }

    rpc GuildProductSplit(ga.guild.GuildSplitGiftCardReq) returns (ga.guild.GuildSplitGiftCardResp) {
        option (ga.api.extension.command) = {
            id: 841
        };
    }

    rpc GuildGetGiftCard(ga.guild.GuildGetGiftCardReq) returns (ga.guild.GuildGetGiftCardResp) {
        option (ga.api.extension.command) = {
            id: 840
        };
    }

    rpc GuildAllotGiftCard(ga.guild.GuildAllotGiftCardReq) returns (ga.guild.GuildAllotGiftCardResp) {
        option (ga.api.extension.command) = {
            id: 842
        };
    }

    rpc GuildRemoveGiftCard(ga.guild.GuildRemoveGiftCardReq) returns (ga.guild.GuildRemoveGiftCardResp) {
        option (ga.api.extension.command) = {
            id: 843
        };
    }

    rpc GuildStorageGetProductDeail(ga.guild.GuildGetProductDetailReq) returns (ga.guild.GuildGetProductDetailResp) {
        option (ga.api.extension.command) = {
            id: 846
        };
    }

    rpc GuildEditProduct(ga.guild.GuildEditProductReq) returns (ga.guild.GuildEditProductResp) {
        option (ga.api.extension.command) = {
            id: 847
        };
    }

    rpc GuildStroageExamine(ga.guild.GetGuildProductExamineLstReq) returns (ga.guild.GetGuildProductExamineLstResp) {
        option (ga.api.extension.command) = {
            id: 815
        };
    }

    rpc GuildStorageHomePageSerivce(ga.guild.GuildStorageReq) returns (ga.guild.GuildStorageResp) {
        option (ga.api.extension.command) = {
            id: 810
        };
    }

    rpc GuildStorageSearch(ga.guild.GuildProductSearchReq) returns (ga.guild.GuildProductSearchResp) {
        option (ga.api.extension.command) = {
            id: 813
        };
    }
    
    //guild
    rpc GameGuildList(ga.guild.GameGuildListReq) returns (ga.guild.GameGuildListResp) {
        option (ga.api.extension.command) = {
            id: 99
        };
    }

    rpc GuildAddAdmin(ga.guild.GuildAddAdminReq) returns (ga.guild.GuildAddAdminResp) {
        option (ga.api.extension.command) = {
            id: 84
        };
    }

    rpc GuildAddExtraGame(ga.guild.GuildGameExtraListAddReq) returns (ga.guild.GuildGameExtraListAddResp) {
        option (ga.api.extension.command) = {
            id: 805
        };
    }

    rpc GuildAddGame(ga.guild.GuildAddGameReq) returns (ga.guild.GuildAddGameResp) {
        option (ga.api.extension.command) = {
            id: 47
        };
    }

    rpc GuildAddGameV2(ga.guild.GuildAddGameV2Req) returns (ga.guild.GuildAddGameV2Resp) {
        option (ga.api.extension.command) = {
            id: 2264
        };
    }

    rpc GuildAddGroupAdmin(ga.guild.GuildAddGroupAdminReq) returns (ga.guild.GuildAddGroupAdminResp) {
        option (ga.api.extension.command) = {
            id: 60
        };
    }

    rpc GuildAddGroupMember(ga.guild.GuildAddGroupMemberReq) returns (ga.guild.GuildAddGroupMemberResp) {
        option (ga.api.extension.command) = {
            id: 89
        };
    }

    rpc GuildAddOfficialMember(ga.guild.GuildOfficialMemberAddReq) returns (ga.guild.GuildOfficialMemberAddResp) {
        option (ga.api.extension.command) = {
            id: 834
        };
    }

    rpc GuildApplyJoinGroup(ga.guild.GuildApplyJoinGroupReq) returns (ga.guild.GuildApplyJoinGroupResp) {
        option (ga.api.extension.command) = {
            id: 87
        };
    }

    rpc GuildBlogGetActivityList(ga.guild.GuildBlogGetActivityListReq) returns (ga.guild.GuildBlogGetActivityListResp) {
        option (ga.api.extension.command) = {
            id: 463
        };
    }

    rpc GuildBlogGetAdv(ga.guild.GuildGetAdvDetailReq) returns (ga.guild.GuildGetAdvDetailResp) {
        option (ga.api.extension.command) = {
            id: 356
        };
    }

    rpc GuildBlogGetAnn(ga.guild.GuildGetAnnDetailReq) returns (ga.guild.GuildGetAnnDetailResp) {
        option (ga.api.extension.command) = {
            id: 357
        };
    }

    rpc GuildChangeGame(ga.guild.GuildChangeGameReq) returns (ga.guild.GuildChangeGameResp) {
        option (ga.api.extension.command) = {
            id: 2261
        };
    }

    rpc GuildCheckIn(ga.guild.GuildCheckInReq) returns (ga.guild.GuildCheckInResp) {
        option (ga.api.extension.command) = {
            id: 81
        };
    }

    rpc GuildCheckInSupplement(ga.guild.GuildCheckInSupplementReq) returns (ga.guild.GuildCheckInSupplementResp) {
        option (ga.api.extension.command) = {
            id: 462
        };
    }

    rpc GuildCreateGameGroup(ga.guild.GuildCreateGameGroupReq) returns (ga.guild.GuildCreateGameGroupResp) {
        option (ga.api.extension.command) = {
            id: 54
        };
    }

    rpc GuildCreateLimit(ga.guild.CreateGuildLimitReq) returns (ga.guild.CreateGuildLimitResp) {
        option (ga.api.extension.command) = {
            id: 96000
        };
    }

    rpc GuildCreateOfficial(ga.guild.GuildOfficialCreateReq) returns (ga.guild.GuildOfficialCreateResp) {
        option (ga.api.extension.command) = {
            id: 830
        };
    }

    rpc GuildCreate(ga.guild.GuildCreateReq) returns (ga.guild.GuildCreateResp) {
        option (ga.api.extension.command) = {
            id: 39
        };
    }

    rpc GuildDelOfficial(ga.guild.GuildOfficailDelReq) returns (ga.guild.GuildOfficailDelResp) {
        option (ga.api.extension.command) = {
            id: 832
        };
    }

    rpc GuildDeleteAdmin(ga.guild.GuildDeleteAdminReq) returns (ga.guild.GuildDeleteAdminResp) {
        option (ga.api.extension.command) = {
            id: 85
        };
    }

    rpc GuildDeleteGroupAdmin(ga.guild.GuildDeleteGroupAdminReq) returns (ga.guild.GuildDeleteGroupAdminResp) {
        option (ga.api.extension.command) = {
            id: 71
        };
    }

    rpc GuildDeleteGroupMember(ga.guild.GuildDeleteGroupMemberReq) returns (ga.guild.GuildDeleteGroupMemberResp) {
        option (ga.api.extension.command) = {
            id: 90
        };
    }

    rpc GuildDeleteGroupOwner(ga.guild.GuildDeleteGroupOwnerReq) returns (ga.guild.GuildDeleteGroupOwnerResp) {
        option (ga.api.extension.command) = {
            id: 115
        };
    }

    rpc GuildDeleteGuildGame(ga.guild.GuildDeleteGuildGameReq) returns (ga.guild.GuildDeleteGuildGameResp) {
        option (ga.api.extension.command) = {
            id: 168
        };
    }

    rpc GuildDeleteGuildGameV2(ga.guild.GuildDeleteGuildGameV2Req) returns (ga.guild.GuildDeleteGuildGameV2Resp) {
        option (ga.api.extension.command) = {
            id: 2265
        };
    }

    rpc GuildDeleteMember(ga.guild.GuildDeleteMemberReq) returns (ga.guild.GuildDeleteMemberResp) {
        option (ga.api.extension.command) = {
            id: 91
        };
    }

    rpc GuildDeleteNotice(ga.guild.GuildDeleteNoticeReq) returns (ga.guild.GuildDeleteNoticeResp) {
        option (ga.api.extension.command) = {
            id: 102
        };
    }

    rpc GuildDismissGameGroup(ga.guild.GuildDismissGameGroupReq) returns (ga.guild.GuildDismissGameGroupResp) {
        option (ga.api.extension.command) = {
            id: 55
        };
    }

    rpc GuildDismiss(ga.guild.GuildDismissReq) returns (ga.guild.GuildDismissResp) {
        option (ga.api.extension.command) = {
            id: 50
        };
    }

    rpc GuildDonate(ga.guild.GuildDonateReq) returns (ga.guild.GuildDonateResp) {
        option (ga.api.extension.command) = {
            id: 363
        };
    }

    rpc GuildDownloadVoice(ga.guild.GuildDownloadVoiceReq) returns (ga.guild.GuildDownloadVoiceResp) {
        option (ga.api.extension.command) = {
            id: 108
        };
    }

    rpc GuildEnablePrefix(ga.guild.GuildEnablePrefixReq) returns (ga.guild.GuildEnablePrefixResp) {
        option (ga.api.extension.command) = {
            id: 104
        };
    }

    rpc GuildGameDownloadReport(ga.guild.GuildGameDownloadReportReq) returns (ga.guild.GuildGameDownloadReportResp) {
        option (ga.api.extension.command) = {
            id: 140
        };
    }

    rpc GuildGetBaseInfo(ga.guild.GuildGetBaseInfoReq) returns (ga.guild.GuildGetBaseInfoResp) {
        option (ga.api.extension.command) = {
            id: 349
        };
    }

    rpc GuildGetBlackList(ga.guild.GuildGetBlackListReq) returns (ga.guild.GuildGetBlackListResp) {
        option (ga.api.extension.command) = {
            id: 870
        };
    }

    rpc GuildGetCheckInList(ga.guild.GuildGetCheckInListReq) returns (ga.guild.GuildGetCheckInListResp) {
        option (ga.api.extension.command) = {
            id: 82
        };
    }

    rpc GuildGetDonateList(ga.guild.GuildGetDonateListReq) returns (ga.guild.GuildGetDonateListResp) {
        option (ga.api.extension.command) = {
            id: 364
        };
    }

    rpc GuildGetDonateOption(ga.guild.GuildGetDonateOptionReq) returns (ga.guild.GuildGetDonateOptionResp) {
        option (ga.api.extension.command) = {
            id: 365
        };
    }

    rpc GuildGetGameDownloadUrl(ga.guild.GuildGetGameDownloadUrlReq) returns (ga.guild.GuildGetGameDownloadUrlResp) {
        option (ga.api.extension.command) = {
            id: 131
        };
    }

    rpc GuildGetGameGroupInfo(ga.guild.GuildGetGameGroupInfoReq) returns (ga.guild.GuildGetGameGroupInfoResp) {
        option (ga.api.extension.command) = {
            id: 56
        };
    }

    rpc GuildGetGameGroupList(ga.guild.GuildGetGameGroupListReq) returns (ga.guild.GuildGetGameGroupListResp) {
        option (ga.api.extension.command) = {
            id: 53
        };
    }

    rpc GuildGetGameInfo(ga.guild.GuildGetGameInfoReq) returns (ga.guild.GuildGetGameInfoResp) {
        option (ga.api.extension.command) = {
            id: 52
        };
    }

    rpc GuildGetGameList(ga.guild.GuildGetGameListReq) returns (ga.guild.GuildGetGameListResp) {
        option (ga.api.extension.command) = {
            id: 132
        };
    }

    rpc GuildGetGameListV2(ga.guild.GuildGameListGetReq) returns (ga.guild.GuildGameListGetResp) {
        option (ga.api.extension.command) = {
            id: 804
        };
    }

    rpc GuildGetGameListV3(ga.guild.GuildGameListGetV2Req) returns (ga.guild.GuildGameListGetV2Resp) {
        option (ga.api.extension.command) = {
            id: 2260
        };
    }

    rpc GuildGetGroupMemberList(ga.guild.GuildGetGroupMemberListReq) returns (ga.guild.GuildGetGroupMemberListResp) {
        option (ga.api.extension.command) = {
            id: 59
        };
    }

    rpc GuildGetGroupMemberListV2(ga.guild.GuildGetGroupMemberListReqV2) returns (ga.guild.GuildGetGroupMemberListRespV2) {
        option (ga.api.extension.command) = {
            id: 351
        };
    }

    rpc GuildGetGroupMember(ga.guild.GuildGetGroupMemberReq) returns (ga.guild.GuildGetGroupMemberResp) {
        option (ga.api.extension.command) = {
            id: 94
        };
    }

    // buf:lint:ignore RPC_PASCAL_CASE
    rpc CMD_GuildGetGroupMemberV2(ga.guild.GuildGetGroupMemberReqV2) returns (ga.guild.GuildGetGroupMemberRespV2) {
        option (ga.api.extension.command) = {
            id: 353
        };
    }

    rpc GuildGetHotGameList(ga.guild.GuildGetHotGameListReq) returns (ga.guild.GuildGetHotGameListResp) {
        option (ga.api.extension.command) = {
            id: 2263
        };
    }

    rpc GuildGetInfo(ga.guild.GuildGetInfoReq) returns (ga.guild.GuildGetInfoResp) {
        option (ga.api.extension.command) = {
            id: 40
            deprecated: true
        };
    }

    rpc GuildGetJoinHistory(ga.guild.GuildGetJoinHistoryReq) returns (ga.guild.GuildGetJoinHistoryResp) {
        option (ga.api.extension.command) = {
            id: 820
        };
    }

    rpc GuildGetMemberCard(ga.guild.GuildGetMemberCardReq) returns (ga.guild.GuildGetMemberCardResp) {
        option (ga.api.extension.command) = {
            id: 361
        };
    }

    rpc GuildGetMemberContributionList(ga.guild.GuildGetMemberContributionListReq) returns (ga.guild.GuildGetMemberContributionListResp) {
        option (ga.api.extension.command) = {
            id: 360
        };
    }

    rpc GuildGetMemberGameDownloadList(ga.guild.GuildGetMemberGameDownloadListReq) returns (ga.guild.GuildGetMemberGameDownloadListResp) {
        option (ga.api.extension.command) = {
            id: 141
        };
    }

    rpc GuildGetMemberInfos(ga.guild.GuildGetMemberInfosReq) returns (ga.guild.GuildGetMemberInfosResp) {
        option (ga.api.extension.command) = {
            id: 352
        };
    }

    rpc GuildGetMemberListByRankType(ga.guild.GuildGetMemberListByRankTypeReq) returns (ga.guild.GuildGetMemberListByRankTypeResp) {
        option (ga.api.extension.command) = {
            id: 362
        };
    }

    rpc GuildGetMemberList(ga.guild.GuildGetMemberListReq) returns (ga.guild.GuildGetMemberListResp) {
        option (ga.api.extension.command) = {
            id: 46
        };
    }

    rpc GuildGetMemberTitleList(ga.guild.GuildGetMemberTitleListReq) returns (ga.guild.GuildGetMemberTitleListResp) {
        option (ga.api.extension.command) = {
            id: 367
        };
    }

    rpc GuildGetMonthRankList(ga.guild.GuildGetMonthRankListReq) returns (ga.guild.GuildGetMonthRankListResp) {
        option (ga.api.extension.command) = {
            id: 465
        };
    }

    rpc GuildGetNoticeList(ga.guild.GuildGetNoticeListReq) returns (ga.guild.GuildGetNoticeListResp) {
        option (ga.api.extension.command) = {
            id: 92
        };
    }

    rpc GuildGetOfficialInfo(ga.guild.GuildOfficialInfoGetReq) returns (ga.guild.GuildOfficialInfoGetResp) {
        option (ga.api.extension.command) = {
            id: 835
        };
    }

    rpc GuildGetOfficialMember(ga.guild.GuildOfficialMemberGetReq) returns (ga.guild.GuildOfficialMemberGetResp) {
        option (ga.api.extension.command) = {
            id: 833
        };
    }

    rpc GuildGetPresentInfo(ga.guild.GetGuildPresentInfoReq) returns (ga.guild.GetGuildPresentInfoResp) {
        option (ga.api.extension.command) = {
            id: 368
        };
    }

    rpc GuildGetRankList(ga.guild.GuildGetRankListReq) returns (ga.guild.GuildGetRankListResp) {
        option (ga.api.extension.command) = {
            id: 464
        };
    }

    rpc GuildGetRecommendListByGames(ga.guild.GuildGetRecommendListByGamesReq) returns (ga.guild.GuildGetRecommendListByGamesResp) {
        option (ga.api.extension.command) = {
            id: 2262
        };
    }

    rpc GuildGetUidOfficialInfo(ga.guild.GuildOfficialInfoGetByUidReq) returns (ga.guild.GuildOfficialInfoGetByUidResp) {
        option (ga.api.extension.command) = {
            id: 837
            deprecated: true
        };
    }

    rpc GuildGroupGetMuteList(ga.guild.GuildGroupGetMuteListReq) returns (ga.guild.GuildGroupGetMuteListResp) {
        option (ga.api.extension.command) = {
            id: 202
        };
    }

    rpc GuildGroupGetMuteMemberList(ga.guild.GuildGroupGetMuteMemberListReq) returns (ga.guild.GuildGroupGetMuteMemberListResp) {
        option (ga.api.extension.command) = {
            id: 355
        };
    }

    rpc GuildGroupMute(ga.guild.GuildGroupMuteReq) returns (ga.guild.GuildGroupMuteResp) {
        option (ga.api.extension.command) = {
            id: 61
        };
    }

    rpc GuildGroupSetAllMute(ga.guild.GuildGroupSetAllMuteReq) returns (ga.guild.GuildGroupSetAllMuteResp) {
        option (ga.api.extension.command) = {
            id: 106
        };
    }

    rpc GuildGroupUnmute(ga.guild.GuildGroupUnmuteReq) returns (ga.guild.GuildGroupUnmuteResp) {
        option (ga.api.extension.command) = {
            id: 72
        };
    }

    rpc GuildHandleJoinGroup(ga.guild.GuildHandleJoinGroupReq) returns (ga.guild.GuildHandleJoinGroupResp) {
        option (ga.api.extension.command) = {
            id: 88
        };
    }

    rpc GuildHandleJoin(ga.guild.GuildHandleJoinReq) returns (ga.guild.GuildHandleJoinResp) {
        option (ga.api.extension.command) = {
            id: 51
        };
    }

    rpc GuildJoin(ga.guild.GuildJoinReq) returns (ga.guild.GuildJoinResp) {
        option (ga.api.extension.command) = {
            id: 48
        };
    }

    rpc GuildModifyGameGroupOrder(ga.guild.GuildModifyGameGroupOrderReq) returns (ga.guild.GuildModifyGameGroupOrderResp) {
        option (ga.api.extension.command) = {
            id: 169
        };
    }

    rpc GuildModifyGameOrder(ga.guild.GuildModifyGameOrderReq) returns (ga.guild.GuildModifyGameOrderResp) {
        option (ga.api.extension.command) = {
            id: 114
        };
    }

    rpc GuildModifyGameUrl(ga.guild.GuildModifyGameUrlReq) returns (ga.guild.GuildModifyGameUrlResp) {
        option (ga.api.extension.command) = {
            id: 128
        };
    }

    rpc GuildModifyGroupGame(ga.guild.GuildModifyGroupGameReq) returns (ga.guild.GuildModifyGroupGameResp) {
        option (ga.api.extension.command) = {
            id: 803
        };
    }

    rpc GuildModifyGroupMemberCard(ga.guild.GuildModifyGroupMemberCardReq) returns (ga.guild.GuildModifyGroupMemberCardResp) {
        option (ga.api.extension.command) = {
            id: 97
        };
    }

    rpc GuildModifyGroupName(ga.guild.GuildModifyGroupNameReq) returns (ga.guild.GuildModifyGroupNameResp) {
        option (ga.api.extension.command) = {
            id: 96
        };
    }

    rpc GuildModifyGroupVerify(ga.guild.GuildModifyGroupVerifyReq) returns (ga.guild.GuildModifyGroupVerifyResp) {
        option (ga.api.extension.command) = {
            id: 98
        };
    }

    rpc GuildModifyInfo(ga.guild.GuildModifyInfoReq) returns (ga.guild.GuildModifyInfoResp) {
        option (ga.api.extension.command) = {
            id: 41
        };
    }

    rpc GuildModifyMemberRemark(ga.guild.GuildModifyMemberRemarkReq) returns (ga.guild.GuildModifyMemberRemarkResp) {
        option (ga.api.extension.command) = {
            id: 103
        };
    }

    rpc GuildModifyNotice(ga.guild.GuildModifyNoticeReq) returns (ga.guild.GuildModifyNoticeResp) {
        option (ga.api.extension.command) = {
            id: 101
        };
    }

    rpc GuildModifyOfficial(ga.guild.GuildOfficialModifyReq) returns (ga.guild.GuildOfficialModifyResp) {
        option (ga.api.extension.command) = {
            id: 831
        };
    }

    rpc GuildModifyPermission(ga.guild.GuildModifyPermissionReq) returns (ga.guild.GuildModifyPermissionResp) {
        option (ga.api.extension.command) = {
            id: 105
        };
    }

    rpc GuildModifyVerify(ga.guild.GuildModifyVerifyReq) returns (ga.guild.GuildModifyVerifyResp) {
        option (ga.api.extension.command) = {
            id: 130
        };
    }

    rpc GuildOwnerBc(ga.guild.GuildOwnerBcReq) returns (ga.guild.GuildOwnerBcResp) {
        option (ga.api.extension.command) = {
            id: 135
        };
    }

    rpc GuildPublishNotice(ga.guild.GuildPublishNoticeReq) returns (ga.guild.GuildPublishNoticeResp) {
        option (ga.api.extension.command) = {
            id: 100
        };
    }

    rpc GuildQuickJoin(ga.guild.GuildQuickJoinReq) returns (ga.guild.GuildQuickJoinResp) {
        option (ga.api.extension.command) = {
            id: 137
        };
    }

    rpc GuildQuitGameGroup(ga.guild.GuildQuitGameGroupReq) returns (ga.guild.GuildQuitGameGroupResp) {
        option (ga.api.extension.command) = {
            id: 58
        };
    }

    rpc GuildQuit(ga.guild.GuildQuitReq) returns (ga.guild.GuildQuitResp) {
        option (ga.api.extension.command) = {
            id: 49
        };
    }

    rpc GuildRemoveBlackList(ga.guild.GuildRemoveBlackListReq) returns (ga.guild.GuildRemoveBlackListResp) {
        option (ga.api.extension.command) = {
            id: 871
        };
    }

    rpc GuildRemoveOfficialMember(ga.guild.GuildOfficialMemberRemoveReq) returns (ga.guild.GuildOfficialMemberRemoveResp) {
        option (ga.api.extension.command) = {
            id: 836
        };
    }

    // rpc GuildSearch(ga.GuildSearchReq) returns (ga.GuildSearchResp) {
    //     option (ga.api.extension.command) = {
    //         id: 42
    //     };
    // }

    rpc GuildSetGroupNotRecvMsg(ga.guild.GuildSetGroupNotRecvMsgReq) returns (ga.guild.GuildSetGroupNotRecvMsgResp) {
        option (ga.api.extension.command) = {
            id: 159
        };
    }

    rpc GuildSetGroupOwner(ga.guild.GuildSetGroupOwnerReq) returns (ga.guild.GuildSetGroupOwnerResp) {
        option (ga.api.extension.command) = {
            id: 86
        };
    }

    rpc GuildSetMemberTitle(ga.guild.GuildSetMemberTitleReq) returns (ga.guild.GuildSetMemberTitleResp) {
        option (ga.api.extension.command) = {
            id: 366
        };
    }

    rpc GuildSetUseCustomUrl(ga.guild.GuildSetUseCustomUrlReq) returns (ga.guild.GuildSetUseCustomUrlResp) {
        option (ga.api.extension.command) = {
            id: 134
        };
    }

    rpc GuildStarLevel(ga.guild.GuildStarLevelReq) returns (ga.guild.GuildStarLevelResp) {
        option (ga.api.extension.command) = {
            id: 350
            deprecated: true
        };
    }

    rpc GuildStarLevelV3(ga.guild.GuildStarLevelReqV3) returns (ga.guild.GuildStarLevelRespV3) {
        option (ga.api.extension.command) = {
            id: 358
        };
    }

    rpc GuildUploadVoice(ga.guild.GuildUploadVoiceReq) returns (ga.guild.GuildUploadVoiceResp) {
        option (ga.api.extension.command) = {
            id: 107
        };
    }

    rpc GuildVerifyMembersInGuild(ga.guild.GuildVerifyMembersReq) returns (ga.guild.GuildVerifyMembersResp) {
        option (ga.api.extension.command) = {
            id: 354
        };
    }

    rpc TopGuildList(ga.guild.TopGuildListReq) returns (ga.guild.TopGuildListResp) {
        option (ga.api.extension.command) = {
            id: 170
        };
    }

    option (ga.api.extension.logic_service_name) = "guildlogic";
    option (ga.api.extension.logic_service_language) = "cpp";
}
