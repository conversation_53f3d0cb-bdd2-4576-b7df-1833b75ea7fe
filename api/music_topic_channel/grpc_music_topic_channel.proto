// Code generated by protoc-gen-ga-api. DO NOT EDIT.
// 	protoc        v5.27.0

syntax = "proto3";
package ga.api.music_topic_channel;

import "hobby_channel/hobby-channel_.proto";
import "music_topic_channel/music-topic-channel-logic_.proto";
import "api/extension/extension.proto";

option java_package      = "com.quwan.tt.proto.api";
option objc_class_prefix = "RPC";
option go_package = "golang.52tt.com/protocol/app/api/music_topic_channel;music_topic_channel";

service MusicTopicChannelLogic {
    option (ga.api.extension.logic_service_name) = "music-topic-channel-logic";
    option (ga.api.extension.logic_service_language) = "go";
    option (ga.api.extension.logic_service_uri_rewrite) = "/logic.MusicTopicChannelLogic/";
    rpc GetMusicChannelFilterV2(ga.music_topic_channel.GetMusicChannelFilterV2Req) returns (ga.music_topic_channel.GetMusicChannelFilterV2Resp) {
        option (ga.api.extension.command) = {
             id: 31520;
        };
    }
    rpc ListHobbyChannelV2(ga.music_topic_channel.ListHobbyChannelV2Req) returns (ga.hobby_channel.ListHobbyChannelResp) {
        option (ga.api.extension.command) = {
             id: 31521;
        };
    }
    rpc GetMusicHomePageViewV2(ga.music_topic_channel.GetMusicHomePageViewV2Req) returns (ga.music_topic_channel.GetMusicHomePageViewV2Resp) {
        option (ga.api.extension.command) = {
             id: 31522;
        };
    }
    rpc QuickMatchHobbyChannelV2(ga.music_topic_channel.QuickMatchHobbyChannelV2Req) returns (ga.music_topic_channel.QuickMatchHobbyChannelV2Resp) {
        option (ga.api.extension.command) = {
             id: 31523;
        };
    }
    rpc PublishMusicChannel(ga.music_topic_channel.PublishMusicChannelReq) returns (ga.music_topic_channel.PublishMusicChannelResp) {
        option (ga.api.extension.command) = {
             id: 31524;
        };
    }
    rpc CancelMusicChannelPublish(ga.music_topic_channel.CancelMusicChannelPublishReq) returns (ga.music_topic_channel.CancelMusicChannelPublishResp) {
        option (ga.api.extension.command) = {
             id: 31525;
        };
    }
    rpc GetMusicFilterItemByIds(ga.music_topic_channel.GetMusicFilterItemByIdsReq) returns (ga.music_topic_channel.GetMusicFilterItemByIdsResp) {
        option (ga.api.extension.command) = {
             id: 31526;
        };
    }
    rpc ListMusicChannels(ga.music_topic_channel.ListMusicChannelsReq) returns (ga.music_topic_channel.ListMusicChannelsResp) {
        option (ga.api.extension.command) = {
             id: 31527;
        };
    }
    rpc GetTabPublishHotRcmd(ga.music_topic_channel.GetTabPublishHotRcmdReq) returns (ga.music_topic_channel.GetTabPublishHotRcmdResp) {
        option (ga.api.extension.command) = {
             id: 31528;
        };
    }
    rpc GetResourceConfigByChannelId(ga.music_topic_channel.GetResourceConfigByChannelIdReq) returns (ga.music_topic_channel.GetResourceConfigByChannelIdResp) {
        option (ga.api.extension.command) = {
             id: 31529;
        };
    }
    rpc MuseGetTopicChannelInfo(ga.music_topic_channel.MuseGetTopicChannelInfoRequest) returns (ga.music_topic_channel.MuseGetTopicChannelInfoResponse) {
        option (ga.api.extension.command) = {
             id: 31530;
        };
    }
    rpc GetAssociateRevChannels(ga.music_topic_channel.GetAssociateRevChannelsRequest) returns (ga.music_topic_channel.GetAssociateRevChannelsResponse) {
        option (ga.api.extension.command) = {
             id: 31531;
        };
    }
    rpc ListMuseSocialCommunityChannels(ga.music_topic_channel.ListMuseSocialCommunityChannelsRequest) returns (ga.music_topic_channel.ListMuseSocialCommunityChannelsResponse) {
        option (ga.api.extension.command) = {
             id: 31532;
        };
    }
  // 偏好关键词
  rpc ListChannelPreferenceKeywords(ga.music_topic_channel.ListChannelPreferenceKeywordsRequest) returns (ga.music_topic_channel.ListChannelPreferenceKeywordsResponse) {
    option (ga.api.extension.command) = {
      id: 31535;
    };
  }
}


