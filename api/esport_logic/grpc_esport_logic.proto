syntax = "proto3";

package ga.api.esport_logic;

import "api/extension/extension.proto";
import "esport_logic/esport_logic.proto";

option go_package = "golang.52tt.com/protocol/app/api/esport_logic;esport_logic";
option java_package = "com.quwan.tt.proto.api";
option objc_class_prefix = "RPC";


service EsportLogic {
    option (ga.api.extension.logic_service_language) = "go";
    option (ga.api.extension.logic_service_name) = "esport-logic";

    // 获取游戏列表
    rpc GetGameList (ga.esport_logic.GetGameListRequest) returns (ga.esport_logic.GetGameListResponse) {
        option (ga.api.extension.command) = {
            id: 40000;
        };
    };
    // 获取开关状态
    rpc GetSwitch (ga.esport_logic.GetSwitchRequest) returns (ga.esport_logic.GetSwitchResponse) {
        option (ga.api.extension.command) = {
            id: 40001;
        };
    };

    // 获取用户电竞指导身份信息（个人主页大神页入口、成为电竞指导入口(非工会入口)）
    rpc GetESportApplyAccess (ga.esport_logic.GetESportApplyAccessRequest) returns (ga.esport_logic.GetESportApplyAccessResponse) {
        option (ga.api.extension.command) = {
            id: 40002;
        };
    };

    // 找人优化 获取顶部游戏列表
    rpc EsportGetTopGameList (ga.esport_logic.GetTopGameListRequest) returns (ga.esport_logic.GetTopGameListResponse) {
        option (ga.api.extension.command) = {
            id: 40003;
        };
    };

    // 获取游戏属性(筛选项)
    rpc GetInviteOrderRecommend (ga.esport_logic.GetInviteOrderRecommendRequest) returns (ga.esport_logic.GetInviteOrderRecommendResponse) {
        option (ga.api.extension.command) = {
            id: 40004;
        };
    };


    // 获取游戏属性(筛选项)
    rpc GetGamePropertyList (ga.esport_logic.GetGamePropertyListRequest) returns (ga.esport_logic.GetGamePropertyListResponse) {
        option (ga.api.extension.command) = {
            id: 40005;
        };
    };


    // 获取电竞专区电竞教练列表
    rpc GetEsportAreaCoachList (ga.esport_logic.GetEsportAreaCoachListRequest) returns (ga.esport_logic.GetEsportAreaCoachListResponse) {
        option (ga.api.extension.command) = {
            id: 40006;
        };
    };

    // 个人主页技能商品列表
    rpc GetHomePageSkillProductList (ga.esport_logic.GetHomePageSkillProductListRequest) returns (ga.esport_logic.GetHomepageSkillProductListResponse) {
        option (ga.api.extension.command) = {
            id: 40007;
        };
    };


    // im浮窗商品列表/订单状态
    rpc GetIMFloatWindowInfo (ga.esport_logic.GetIMFloatWindowInfoRequest) returns (ga.esport_logic.GetIMFloatWindowInfoResponse) {
        option (ga.api.extension.command) = {
            id: 40008;
        };
    };


    // 邀请下单
    rpc InviteOrder (ga.esport_logic.InviteOrderRequest) returns (ga.esport_logic.InviteOrderResponse) {
        option (ga.api.extension.command) = {
            id: 40009;
        };
    };


    // 玩家下单
    rpc PlayerPayOrder (ga.esport_logic.PlayerPayOrderRequest) returns (ga.esport_logic.PlayerPayOrderResponse) {
        option (ga.api.extension.command) = {
            id: 40010;
        };
    };

    // 玩家取消订单
    rpc PlayerCancelOrder (ga.esport_logic.PlayerCancelOrderRequest) returns (ga.esport_logic.PlayerCancelOrderResponse) {
        option (ga.api.extension.command) = {
            id: 40011;
        };
    };

    // 玩家确认完成订单
    rpc PlayerFinishOrder (ga.esport_logic.PlayerFinishOrderRequest) returns (ga.esport_logic.PlayerFinishOrderResponse) {
        option (ga.api.extension.command) = {
            id: 40012;
        };
    };

    // 电竞指导接单
    rpc CoachReceiveOrder (ga.esport_logic.CoachReceiveOrderRequest) returns (ga.esport_logic.CoachReceiveOrderResponse) {
        option (ga.api.extension.command) = {
            id: 40013;
        };
    };

    // 电竞指导拒绝接单
    rpc CoachRefuseOrder (ga.esport_logic.CoachRefuseOrderRequest) returns (ga.esport_logic.CoachRefuseOrderResponse) {
        option (ga.api.extension.command) = {
            id: 40014;
        };
    };

    // 获取订单详情
    rpc GetOrderDetail (ga.esport_logic.GetOrderDetailRequest) returns (ga.esport_logic.GetOrderDetailResponse) {
        option (ga.api.extension.command) = {
            id: 40015;
        };
    };

    // 获取订单列表
    rpc GetOrderList (ga.esport_logic.GetOrderListRequest) returns (ga.esport_logic.GetOrderListResponse) {
        option (ga.api.extension.command) = {
            id: 40016;
        };
    };

    // 删除订单
    rpc DelOrderRecord (ga.esport_logic.DelOrderRecordRequest) returns (ga.esport_logic.DelOrderRecordResponse) {
        option (ga.api.extension.command) = {
            id: 40017;
        };
    };

    // 获取原因文案列表
    rpc GetReasonTextList (ga.esport_logic.GetReasonTextListRequest) returns (ga.esport_logic.GetReasonTextListResponse) {
        option (ga.api.extension.command) = {
            id: 40018;
        };
    };

    // 电竞指导提醒玩家去完成订单
    rpc CoachNotifyFinishOrder (ga.esport_logic.CoachNotifyFinishOrderRequest) returns (ga.esport_logic.CoachNotifyFinishOrderResponse) {
        option (ga.api.extension.command) = {
            id: 40019;
        };
    };

    // 接受退款
    rpc AcceptRefund (ga.esport_logic.AcceptRefundRequest) returns (ga.esport_logic.AcceptRefundResponse) {
        option (ga.api.extension.command) = {
            id: 40020;
        };
    };


    // 处理下单邀请
    rpc HandleInviteOrder (ga.esport_logic.HandleInviteOrderRequest) returns (ga.esport_logic.HandleInviteOrderResponse) {
        option (ga.api.extension.command) = {
            id: 40021;
        };
    };

    // 获取评价快捷词列表
    rpc GetEvaluateWordList (ga.esport_logic.GetEvaluateWordListRequest) returns (ga.esport_logic.GetEvaluateWordListResponse) {
        option (ga.api.extension.command) = {
            id: 40022;
        };
    };

    // 分页获取用户评价列表
  //  rpc GetEvaluateList(ga.esport_logic.GetEvaluateListRequest) returns (ga.esport_logic.GetEvaluateListResponse) {
  //    option (ga.api.extension.command) = {
  //      id: 40023;
  //    };
  //  };

    // 评价
    rpc Evaluate (ga.esport_logic.EvaluateRequest) returns (ga.esport_logic.EvaluateResponse) {
        option (ga.api.extension.command) = {
            id: 40024;
        };
    };

    // 新建游戏名片时获取配置
    rpc GetEsportGameCardConfig (ga.esport_logic.GetEsportGameCardConfigRequest) returns (ga.esport_logic.GetEsportGameCardConfigResponse) {
        option (ga.api.extension.command) = {
            id: 40025;
        };
    };

    // 获取游戏名片信息
    rpc GetEsportGameCardInfo (ga.esport_logic.GetEsportGameCardInfoRequest) returns (ga.esport_logic.GetEsportGameCardInfoResponse) {
        option (ga.api.extension.command) = {
            id: 40026;
        };
    };

    // 更新创建游戏名片
    rpc UpsertEsportGameCardInfo (ga.esport_logic.UpsertEsportGameCardInfoRequest) returns (ga.esport_logic.UpsertEsportGameCardInfoResponse) {
        option (ga.api.extension.command) = {
            id: 40027;
        };
    };

    // 获取游戏名片列表
    rpc GetEsportGameCardList (ga.esport_logic.GetEsportGameCardListRequest) returns (ga.esport_logic.GetEsportGameCardListResponse) {
        option (ga.api.extension.command) = {
            id: 40028;
        };
    };

    // 获取游戏名片列表
    rpc SendEsportGameCard (ga.esport_logic.SendEsportGameCardRequest) returns (ga.esport_logic.SendEsportGameCardResponse) {
        option (ga.api.extension.command) = {
            id: 40029;
        };
    };

    // 删除游戏名片
    rpc DeleteEsportGameCard (ga.esport_logic.DeleteEsportGameCardRequest) returns (ga.esport_logic.DeleteEsportGameCardResponse) {
        option (ga.api.extension.command) = {
            id: 40030;
        };
    };

    // 获取配置了游戏名片的游戏
    rpc GetGameCardGame (ga.esport_logic.GetGameCardGameRequest) returns (ga.esport_logic.GetGameCardGameResponse) {
        option (ga.api.extension.command) = {
            id: 40031;
        };
    };

    // 上报用户从大神游戏详情卡片进入大神IM页
    rpc EnterEsportIMPageReport (ga.esport_logic.EnterEsportIMPageReportRequest) returns (ga.esport_logic.EnterEsportIMPageReportResponse) {
        option (ga.api.extension.command) = {
            id: 40032;
        };
    };


    // 获取IM页电竞tag
    rpc GetChatListEsportTags (ga.esport_logic.GetChatListEsportTagsRequest) returns (ga.esport_logic.GetChatListEsportTagsResponse) {
        option (ga.api.extension.command) = {
            id: 40033;
        };
    };

    // 获取快捷回复列表
    rpc GetEsportQuickReplyList (ga.esport_logic.GetEsportQuickReplyListRequest) returns (ga.esport_logic.GetEsportQuickReplyListResponse) {
        option (ga.api.extension.command) = {
            id: 40034;
        };
    };

    // 上报快捷回复
    rpc ReportQuickReply (ga.esport_logic.ReportQuickReplyRequest) returns (ga.esport_logic.ReportQuickReplyResponse) {
        option (ga.api.extension.command) = {
            id: 40035;
        };
    };

    // 上报已曝光大神
    rpc ReportExposeCoach (ga.esport_logic.ReportExposeCoachRequest) returns (ga.esport_logic.ReportExposeCoachResponse) {
        option (ga.api.extension.command) = {
            id: 40036;
        };
    };

    // 玩家下单前置检查
    rpc PlayerPayOrderPreCheck (ga.esport_logic.PlayerPayOrderRequest) returns (ga.esport_logic.PlayerPayOrderResponse) {
        option (ga.api.extension.command) = {
            id: 40037;
        };
    };
    // 估算订单总价
    rpc EstimateOrderTotalPrice (ga.esport_logic.EstimateOrderTotalPriceRequest) returns (ga.esport_logic.EstimateOrderTotalPriceResponse) {
        option (ga.api.extension.command) = {
            id: 40038;
        };
    };
    // 联系客服
    rpc ContactCustomerService (ga.esport_logic.ContactCustomerServiceRequest) returns (ga.esport_logic.ContactCustomerServiceResponse) {
        option (ga.api.extension.command) = {
            id: 40039;
        };
    };
    // 判断指定用户是否是客服
    rpc IsUserACustomer (ga.esport_logic.IsUserACustomerRequest) returns (ga.esport_logic.IsUserACustomerResponse) {
        option (ga.api.extension.command) = {
            id: 40040;
        };
    };
    // 获取ugc房推荐列表的入口
    rpc GetUGCReListEnt (ga.esport_logic.GetUGCReListEntRequest) returns (ga.esport_logic.GetUGCReListEntResponse) {
        option (ga.api.extension.command) = {
            id: 40041;
        };
    };
    // 获取ugc房推荐大神卡信息接口
    rpc GetUGCReCoachCardInfo (ga.esport_logic.GetUGCReCoachCardInfoRequest) returns (ga.esport_logic.GetUGCReCoachCardInfoResponse) {
        option (ga.api.extension.command) = {
            id: 40042;
        };
    };
    // 更新不再推荐状态接口
    rpc NoMoreReOnUGC (ga.esport_logic.NoMoreReOnUGCRequest) returns (ga.esport_logic.NoMoreReOnUGCResponse) {
        option (ga.api.extension.command) = {
            id: 40043;
        };
    };
    // 获取推荐大神列表接口
    rpc GetRecommendedGodList (ga.esport_logic.GetRecommendedGodListRequest) returns (ga.esport_logic.GetRecommendedGodListResponse) {
        option (ga.api.extension.command) = {
            id: 40044;
        };
    };
    // 登录获取优惠券弹窗
    rpc LoginAppShowCouponRemain (ga.esport_logic.LoginAppShowCouponRemainRequest) returns (ga.esport_logic.LoginAppShowCouponRemainResponse) {
        option (ga.api.extension.command) = {
            id: 40045;
        };
    };
    // 显示手动发放的优惠券
    rpc ShowManualGrantCoupon (ga.esport_logic.ShowManualGrantCouponRequest) returns (ga.esport_logic.ShowManualGrantCouponResponse) {
        option (ga.api.extension.command) = {
            id: 40046;
        };
    };
    // 标记手动发放的优惠券已读
    rpc MarkManualGrantCouponRead (ga.esport_logic.MarkManualGrantCouponReadRequest) returns (ga.esport_logic.MarkManualGrantCouponReadResponse) {
        option (ga.api.extension.command) = {
            id: 40047;
        };
    };
    // 获取优惠券入口信息
    rpc GetCouponEntranceInfo (ga.esport_logic.GetCouponEntranceInfoRequest) returns (ga.esport_logic.GetCouponEntranceInfoResponse) {
        option (ga.api.extension.command) = {
            id: 40048;
        };
    };
    // 获取首页优惠券入口信息
    rpc GetHomeCouponEntranceInfo (ga.esport_logic.GetHomeCouponEntranceInfoRequest) returns (ga.esport_logic.GetHomeCouponEntranceInfoResponse) {
        option (ga.api.extension.command) = {
            id: 40049;
        };
    };
    // 电竞专区顶部金刚位
    rpc GetEsportAreaTopBannerList (ga.esport_logic.GetEsportAreaTopBannerListRequest) returns (ga.esport_logic.GetEsportAreaTopBannerListResponse) {
        option (ga.api.extension.command) = {
            id: 40050;
        };
    };
    // 获取电竞大神任务信息
    rpc GetEsportCoachMissionInfo (ga.esport_logic.GetEsportCoachMissionInfoRequest) returns (ga.esport_logic.GetEsportCoachMissionInfoResponse) {
        option (ga.api.extension.command) = {
            id: 40051;
        };
    };
    // 获取新用户退出挽留展示的推荐大神（复用专区列表的结构）
    rpc GetBackRecallReCoach (ga.esport_logic.GetBackRecallReCoachRequest) returns (ga.esport_logic.GetBackRecallReCoachResponse) {
        option (ga.api.extension.command) = {
            id: 40052;
        };
    };
    // 获取专区新用户承接弹出页的数据
    rpc GetNewCustomerTabSetting (ga.esport_logic.GetNewCustomerTabSettingRequest) returns (ga.esport_logic.GetNewCustomerTabSettingResponse) {
        option (ga.api.extension.command) = {
            id: 40053;
        };
    };
    // 提交专区新用户承接弹出页的数据
    rpc PostNewCustomerTabSetting (ga.esport_logic.PostNewCustomerTabSettingRequest) returns (ga.esport_logic.PostNewCustomerTabSettingResponse) {
        option (ga.api.extension.command) = {
            id: 40054;
        };
    };

    // GetOneKeyFindCoachEntry 获取一键找人入口
    rpc GetOneKeyFindCoachEntry (ga.esport_logic.GetOneKeyFindCoachEntryRequest) returns (ga.esport_logic.GetOneKeyFindCoachEntryResponse) {
        option (ga.api.extension.command) = {
            id: 40055;
        };
    };

    // GetOneKeyPublishCfg 获取一键找人发布配置
    rpc GetOneKeyPublishCfg (ga.esport_logic.GetOneKeyPublishCfgRequest) returns (ga.esport_logic.GetOneKeyPublishCfgResponse) {
        option (ga.api.extension.command) = {
            id: 40056;
        };
    };

    // PublishOneKeyFindCoach 发布一键找人
    rpc PublishOneKeyFindCoach (ga.esport_logic.PublishOneKeyFindCoachRequest) returns (ga.esport_logic.PublishOneKeyFindCoachResponse) {
        option (ga.api.extension.command) = {
            id: 40057;
        };
    };

    // CancelOneKeyFindCoach 取消一键找人
    rpc CancelOneKeyFindCoach (ga.esport_logic.CancelOneKeyFindCoachRequest) returns (ga.esport_logic.CancelOneKeyFindCoachResponse) {
      option (ga.api.extension.command) = {
        id: 40058;
      };
    };

    // StickOneKeyFindCoach 置顶一键找人
    rpc StickOneKeyFindCoach (ga.esport_logic.StickOneKeyFindCoachRequest) returns (ga.esport_logic.StickOneKeyFindCoachResponse) {
      option (ga.api.extension.command) = {
        id: 40059;
      };
    };

    // GetGoingOneKeyFindCoach 获取进行中的一键找人
    rpc GetGoingOneKeyFindCoach (ga.esport_logic.GetGoingOneKeyFindCoachRequest) returns (ga.esport_logic.GetGoingOneKeyFindCoachResponse) {
      option (ga.api.extension.command) = {
        id: 40060;
      };
    };

    // EsportReportClickIm 电竞IM页点击上报
    rpc EsportReportClickIm (ga.esport_logic.EsportReportClickImRequest) returns (ga.esport_logic.EsportReportClickImResponse) {
      option (ga.api.extension.command) = {
        id: 40061;
      };
    };

    // EsportRegionHeartbeat 电竞区域心跳
    rpc EsportRegionHeartbeat (ga.esport_logic.EsportRegionHeartbeatRequest) returns (ga.esport_logic.EsportRegionHeartbeatResponse) {
      option (ga.api.extension.command) = {
        id: 40062;
      };
    };

    // CheckIfCanPublishOneKeyFindCoach 检查是否可以发布一键找人
    rpc CheckIfCanPublishOneKeyFindCoach (ga.esport_logic.CheckIfCanPublishOneKeyFindCoachRequest) returns (ga.esport_logic.CheckIfCanPublishOneKeyFindCoachResponse) {
      option (ga.api.extension.command) = {
        id: 40063;
      };
    };

    // GetGlobalOneKeyFindCfg 获取全局一键找人配置
    rpc GetGlobalOneKeyFindCfg (ga.esport_logic.GetGlobalOneKeyFindCfgRequest) returns (ga.esport_logic.GetGlobalOneKeyFindCfgResponse) {
      option (ga.api.extension.command) = {
        id: 40064;
      };
    };
}
