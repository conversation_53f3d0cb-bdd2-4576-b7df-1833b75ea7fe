// Code generated by protoc-gen-ga-api. DO NOT EDIT.
// 	protoc        v5.27.0

syntax = "proto3";
package ga.api.topic_channel;

import "topic_channel/topic_channel_.proto";
import "channel/channel_.proto";
import "api/extension/extension.proto";

option java_package      = "com.quwan.tt.proto.api";
option objc_class_prefix = "RPC";
option go_package = "golang.52tt.com/protocol/app/api/topic_channel;topic_channel";

service TopicChannelLogic {
    option (ga.api.extension.logic_service_name) = "topic-channel-logic";
    option (ga.api.extension.logic_service_language) = "go";
    option (ga.api.extension.logic_service_uri_rewrite) = "/logic.topic_channel.TopicChannelLogic/";
    rpc CreateTopicChannel(ga.topic_channel.CreateTopicChannelReq) returns (ga.topic_channel.CreateTopicChannelResp) {
        option (ga.api.extension.command) = {
             id: 3002;
        };
    }
    rpc GetTopicChannelInfo(ga.topic_channel.GetTopicChannelInfoReq) returns (ga.topic_channel.GetTopicChannelInfoResp) {
        option (ga.api.extension.command) = {
             id: 3003;
        };
    }
    rpc ListRecommendTopicChannel(ga.topic_channel.ListRecommendTopicChannelReq) returns (ga.topic_channel.ListRecommendTopicChannelResp) {
        option (ga.api.extension.command) = {
             id: 3000;
        };
    }
    rpc TabTopicChannel(ga.topic_channel.TabTopicChannelReq) returns (ga.topic_channel.TabTopicChannelResp) {
        option (ga.api.extension.command) = {
             id: 3001;
        };
    }
    rpc GetTopicChannelRoomName(ga.topic_channel.GetTopicChannelRoomNameReq) returns (ga.topic_channel.GetTopicChannelRoomNameResp) {
        option (ga.api.extension.command) = {
             id: 3004;
        };
    }
    rpc GetRoomProxyTip(ga.topic_channel.GetRoomProxyTipReq) returns (ga.topic_channel.GetRoomProxyTipResp) {
        option (ga.api.extension.command) = {
             id: 3010;
        };
    }
    rpc GetBannerList(ga.channel.GetChannelAdvReq) returns (ga.channel.GetChannelAdvResp) {
        option (ga.api.extension.command) = {
             id: 3011;
        };
    }
    rpc HideTopicChannel(ga.topic_channel.HideTopicChannelReq) returns (ga.topic_channel.HideTopicChannelResp) {
        option (ga.api.extension.command) = {
             id: 3020;
        };
    }
    rpc KeepAliveTopicChannel(ga.topic_channel.KeepAliveTopicChannelReq) returns (ga.topic_channel.KeepAliveTopicChannelResp) {
        option (ga.api.extension.command) = {
             id: 3021;
        };
    }
    rpc GetChannelRoomNameConfig(ga.topic_channel.GetChannelRoomNameConfigReq) returns (ga.topic_channel.GetChannelRoomNameConfigResp) {
        option (ga.api.extension.command) = {
             id: 3022;
        };
    }
    rpc CreateTopicChannelV2(ga.topic_channel.CreateTopicChannelV2Req) returns (ga.topic_channel.CreateTopicChannelV2Resp) {
        option (ga.api.extension.command) = {
             id: 3030;
        };
    }
    rpc QuickFormTeam(ga.topic_channel.QuickFormTeamReq) returns (ga.topic_channel.QuickFormTeamResp) {
        option (ga.api.extension.command) = {
             id: 3031;
        };
    }
    rpc ListTopicChannelV2(ga.topic_channel.ListTopicChannelV2Req) returns (ga.topic_channel.ListTopicChannelV2Resp) {
        option (ga.api.extension.command) = {
             id: 3032;
        };
    }
    rpc GetChannelDialog(ga.topic_channel.GetChannelDialogReq) returns (ga.topic_channel.GetChannelDialogResp) {
        option (ga.api.extension.command) = {
             id: 3033;
        };
    }
    rpc GetDialogV2(ga.topic_channel.GetDialogV2Req) returns (ga.topic_channel.GetDialogV2Resp) {
        option (ga.api.extension.command) = {
             id: 3040;
        };
    }
    rpc ListTabBlocks(ga.topic_channel.ListTabBlocksReq) returns (ga.topic_channel.ListTabBlocksResp) {
        option (ga.api.extension.command) = {
             id: 3034;
        };
    }
    rpc GetFormTeamInfo(ga.topic_channel.GetFormTeamInfoReq) returns (ga.topic_channel.GetFormTeamInfoResp) {
        option (ga.api.extension.command) = {
             id: 3035;
        };
    }
    rpc GetGuideConfig(ga.topic_channel.GetGuideConfigReq) returns (ga.topic_channel.GetGuideConfigResp) {
        option (ga.api.extension.command) = {
             id: 3038;
        };
    }
    rpc SwitchGamePlay(ga.topic_channel.SwitchGamePlayReq) returns (ga.topic_channel.SwitchGamePlayResp) {
        option (ga.api.extension.command) = {
             id: 3036;
        };
    }
    rpc GetTabList(ga.topic_channel.GetTabListReq) returns (ga.topic_channel.GetTabListResp) {
        option (ga.api.extension.command) = {
             id: 3037;
        };
    }
    rpc QuickFormTeamV2(ga.topic_channel.QuickFormTeamV2Req) returns (ga.topic_channel.QuickFormTeamV2Resp) {
        option (ga.api.extension.command) = {
             id: 3041;
        };
    }
    rpc GetHomePageCardList(ga.topic_channel.GetHomePageCardListReq) returns (ga.topic_channel.GetHomePageCardListResp) {
        option (ga.api.extension.command) = {
             id: 3039;
        };
    }
    rpc ListFreshmanRecommendedChannel(ga.topic_channel.ListFreshmanRecommendedChannelReq) returns (ga.topic_channel.ListFreshmanRecommendedChannelResp) {
        option (ga.api.extension.command) = {
             id: 3042;
        };
    }
    rpc ListPlaymateRecommendedChannel(ga.topic_channel.ListPlaymateRecommendedChannelReq) returns (ga.topic_channel.ListPlaymateRecommendedChannelResp) {
        option (ga.api.extension.command) = {
             id: 3043;
        };
    }
    rpc CreateAndReleaseTopicChannel(ga.topic_channel.CreateAndReleaseTopicChannelReq) returns (ga.topic_channel.CreateAndReleaseTopicChannelResp) {
        option (ga.api.extension.command) = {
             id: 3051;
        };
    }
    rpc DistributionTopicChannel(ga.topic_channel.DistributionTopicChannelReq) returns (ga.topic_channel.DistributionTopicChannelResp) {
        option (ga.api.extension.command) = {
             id: 3053;
        };
    }
    rpc GetTabListWhenCreate(ga.topic_channel.GetTabListWhenCreateReq) returns (ga.topic_channel.GetTabListWhenCreateResp) {
        option (ga.api.extension.command) = {
             id: 3054;
        };
    }
    rpc ListTopicChannelV3(ga.topic_channel.ListTopicChannelV3Req) returns (ga.topic_channel.ListTopicChannelV3Resp) {
        option (ga.api.extension.command) = {
             id: 3055;
        };
    }
    rpc ShowTopicChannelTabList(ga.topic_channel.ShowTopicChannelTabListReq) returns (ga.topic_channel.ShowTopicChannelTabListResp) {
        option (ga.api.extension.command) = {
             id: 3056;
             deprecated: true;
        };
    }
    rpc GetSubTabList(ga.topic_channel.GetSubTabListReq) returns (ga.topic_channel.GetSubTabListResp) {
        option (ga.api.extension.command) = {
             id: 3057;
        };
    }
    rpc GetQuickMatchTabList(ga.topic_channel.GetQuickMatchTabListReq) returns (ga.topic_channel.GetQuickMatchTabListResp) {
        option (ga.api.extension.command) = {
             id: 3058;
        };
    }
    rpc GetNegativeFeedBackOption(ga.topic_channel.GetNegativeFeedBackOptionReq) returns (ga.topic_channel.GetNegativeFeedBackOptionResp) {
        option (ga.api.extension.command) = {
             id: 3059;
        };
    }
    rpc NegativeFeedBack(ga.topic_channel.NegativeFeedBackReq) returns (ga.topic_channel.NegativeFeedBackResp) {
        option (ga.api.extension.command) = {
             id: 3060;
        };
    }
    rpc GetLiveTogetherConfig(ga.topic_channel.GetLiveTogetherConfigReq) returns (ga.topic_channel.GetLiveTogetherConfigResp) {
        option (ga.api.extension.command) = {
             id: 3062;
        };
    }
    rpc SetLiveTogetherStatus(ga.topic_channel.SetLiveTogetherStatusReq) returns (ga.topic_channel.SetLiveTogetherStatusResp) {
        option (ga.api.extension.command) = {
             id: 3063;
        };
    }
    rpc LabelSearch(ga.topic_channel.LabelSearchReq) returns (ga.topic_channel.LabelSearchResp) {
        option (ga.api.extension.command) = {
             id: 3070;
        };
    }
    rpc GetGameLabels(ga.topic_channel.GetGameLabelsReq) returns (ga.topic_channel.GetGameLabelsResp) {
        option (ga.api.extension.command) = {
             id: 3071;
        };
    }
    rpc GetLabelSearchGuide(ga.topic_channel.GetLabelSearchGuideReq) returns (ga.topic_channel.GetLabelSearchGuideResp) {
        option (ga.api.extension.command) = {
             id: 3072;
        };
    }
    rpc GetTCCache(ga.topic_channel.GetTCCacheReq) returns (ga.topic_channel.GetTCCacheResp) {
    }
}


