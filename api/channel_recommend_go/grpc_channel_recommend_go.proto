syntax = "proto3";
package ga.api.channel_recommend_go;

import "api/extension/extension.proto";
import "channel_recommend_logic/channel_recommend_logic_.proto";
import "channel/channel_.proto";

option java_package = "com.quwan.tt.proto.api";
option go_package = "golang.52tt.com/protocol/app/api/channel_recommend_go;channel_recommend_go";
option objc_class_prefix = "RPC";

service ChannelRecommendLogicService {
    option (ga.api.extension.logic_service_name) = "channel-recommend-logic";
    option (ga.api.extension.logic_service_language) = "go";
    
    rpc GetRecLotteryChList(ga.channel_recommend_logic.GetRecLotteryChListRequest) returns (ga.channel_recommend_logic.GetRecLotteryChListResponse) {
        option (ga.api.extension.command) = {
             id: 5055
        };
    }

    rpc GetRecommendChannelService(ga.channel.GetRecommonChannelListReq) returns (ga.channel.GetRecommonChannelListResp) {
        option (ga.api.extension.command) = {
            id: 5061;
            deprecated: true;
        };
    }

    //新版获取娱乐TAB推荐房列表
    rpc GetRecommendChannelsV2Service(ga.channel.GetRecommonChannelListV2Req) returns (ga.channel.GetRecommonChannelListV2Resp) {
        option (ga.api.extension.command) = {
            id: 5080;
        };
    }
    //新版根据房间标签获取房间列表
    rpc GetRecommonChannelListByTagIdV2Service(ga.channel.GetRecommonChannelListByTagIdReq) returns (ga.channel.GetRecommonChannelListByTagIdResp) {
        option (ga.api.extension.command) = {
            id: 5081;
        };
    }
    //新版个性化房间列表
    rpc GetRecChListByPerTagIdV2Service(ga.channel.GetRecChListByPerTagIdReq ) returns (ga.channel.GetRecChListByPerTagIdResp) {
        option (ga.api.extension.command) = {
            id: 5082;
        };
    }
    //新版个性化房间列表
    rpc GetQuickEntryConfigV2Service(ga.channel.GetQuickEntryConfigReq ) returns (ga.channel.GetQuickEntryConfigResp) {
        option (ga.api.extension.command) = {
            id: 5083;
        };
    }
    //获取房间顶部浮窗
    rpc GetChannelTopOverLay(ga.channel_recommend_logic.GetChannelTopOverLayRequest ) returns (ga.channel_recommend_logic.GetChannelTopOverLayResponse) {
        option (ga.api.extension.command) = {
            id: 5084;
        };
    }
    //获取营收开关
    rpc GetRevenueSwitchHubService(ga.channel_recommend_logic.GetRevenueSwitchHubRequest ) returns (ga.channel_recommend_logic.GetRevenueSwitchHubResponse) {
        option (ga.api.extension.command) = {
            id: 5085;
        };
    }
    //设置营收开关
    rpc SetRevenueSwitchHubService(ga.channel_recommend_logic.SetRevenueSwitchHubRequest ) returns (ga.channel_recommend_logic.SetRevenueSwitchHubResponse) {
        option (ga.api.extension.command) = {
            id: 5086;
        };
    }
    // 获取通用顶部浮窗
    rpc GetGlobalTopOverLay(ga.channel_recommend_logic.GetGlobalTopOverLayRequest) returns (ga.channel_recommend_logic.GetGlobalTopOverLayResponse) {
        option (ga.api.extension.command) = {
            id: 5087
        };
    }
     // 获取推荐列表房间反馈配置
    rpc GetRecFeedbackConfig(ga.channel_recommend_logic.GetRecFeedbackConfigRequest) returns (ga.channel_recommend_logic.GetRecFeedbackConfigResponse) {
        option (ga.api.extension.command) = {
            id: 5053
        };
    }
     // 推荐房间反馈
    rpc DoRecFeedback(ga.channel_recommend_logic.DoRecFeedbackRequest) returns (ga.channel_recommend_logic.DoRecFeedbackResponse) {
        option (ga.api.extension.command) = {
            id: 5054
        };
    }
    
}
