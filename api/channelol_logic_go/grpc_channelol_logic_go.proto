// Code generated by protoc-gen-ga-api. DO NOT EDIT.
// 	protoc        v3.9.1

syntax = "proto3";
package ga.api.channelol_logic_go;

import "channelol_logic_go/channelol_logic_go.proto";
import "api/extension/extension.proto";
import "channel/channel_.proto";

option java_package      = "com.quwan.tt.proto.api";
option objc_class_prefix = "RPC";
option go_package = "golang.52tt.com/protocol/app/api/channelol_logic_go;channelol_logic_go";

service ChannelolLogicGo {
    option (ga.api.extension.logic_service_name) = "channelol-logic-go";
    option (ga.api.extension.logic_service_language) = "go";
    option (ga.api.extension.logic_service_uri_rewrite) = "";
    rpc GetChannelOnlineMember(ga.channelol_logic_go.GetChannelOnlineMemberRequest) returns (ga.channelol_logic_go.GetChannelOnlineMemberResponse) {
        option (ga.api.extension.command) = {
             id: 50856;
        };
    }

    rpc CChannelGetUnderTheMicroList(ga.channel.ChannelGetUnderTheMicroListReq) returns (ga.channel.ChannelGetUnderTheMicroListResp) {
        option (ga.api.extension.command) = {
            id: 3501;
        };
    }

    rpc ChannelGetMemberInfo(ga.channel.GetChannelMemberInfoReq) returns (ga.channel.GetChannelMemberInfoResp) {
        option (ga.api.extension.command) = {
            id: 459;
        };
    }

    rpc QuickJoinChannel(ga.channel.QuickJoinChannelReq) returns (ga.channel.QuickJoinChannelResp) {
        option (ga.api.extension.command) = {
            id: 436;
        };
    }

    rpc ChannelGetHistory(ga.channel.GetChannelHistoryReq) returns (ga.channel.GetChannelHistoryResp) {
        option (ga.api.extension.command) = {
            id: 2050;
        };
    }

    rpc KickoutChannelMember(ga.channel.KickoutChannelReq) returns (ga.channel.KickoutChannelResp) {
        option (ga.api.extension.command) = {
            id: 439;
        };
    }

    rpc ChannelHeartbeatUpdate(ga.channelol_logic_go.ChannelHeartbeatUpdateRequest) returns (ga.channelol_logic_go.ChannelHeartbeatUpdateResponse) {
        option (ga.api.extension.command) = {
            id: 50876;
        };
    }
}


