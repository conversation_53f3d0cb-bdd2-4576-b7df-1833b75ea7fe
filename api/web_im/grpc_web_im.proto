// Code generated by protoc-gen-ga-api. DO NOT EDIT.
// 	protoc        v3.9.1

syntax = "proto3";
package ga.api.web_im;

import "web_im_logic/web_im_logic.proto";
import "api/extension/extension.proto";

option java_package      = "com.quwan.tt.proto.api";
option objc_class_prefix = "RPC";
option go_package = "golang.52tt.com/protocol/app/api/web_im;web_im";

service WebImLogic {
    option (ga.api.extension.logic_service_name) = "web-im-logic";
    option (ga.api.extension.logic_service_language) = "go";
    option (ga.api.extension.logic_service_uri_rewrite) = "/logic.WebImLogic/";
    rpc SendWebImMsg(ga.web_im_logic.SendWebImMsgRequest) returns (ga.web_im_logic.SendWebImMsgResponse) {
        option (ga.api.extension.command) = {
             id: 1350;
        };
    }
    rpc GetWebImMsgList(ga.web_im_logic.GetWebImMsgListRequest) returns (ga.web_im_logic.GetWebImMsgListResponse) {
        option (ga.api.extension.command) = {
             id: 1351;
        };
    }
    rpc ReadWebImMsg(ga.web_im_logic.ReadWebImMsgRequest) returns (ga.web_im_logic.ReadWebImMsgResponse) {
        option (ga.api.extension.command) = {
             id: 1352;
        };
    }
    rpc GetUserWebImMsgList(ga.web_im_logic.GetUserWebImMsgListRequest) returns (ga.web_im_logic.GetUserWebImMsgListResponse) {
        option (ga.api.extension.command) = {
             id: 1353;
        };
    }
    rpc SendGroupImMsg(ga.web_im_logic.SendGroupImMsgRequest) returns (ga.web_im_logic.SendGroupImMsgResponse) {
        option (ga.api.extension.command) = {
             id: 1354;
        };
    }
    rpc BatchGetGroupLastMsg(ga.web_im_logic.BatchGetGroupLastMsgRequest) returns (ga.web_im_logic.BatchGetGroupLastMsgResponse) {
        option (ga.api.extension.command) = {
             id: 1355;
        };
    }

    rpc GetGroupMsgList(ga.web_im_logic.GetGroupMsgListRequest) returns (ga.web_im_logic.GetGroupMsgListResponse) {
        option (ga.api.extension.command) = {
             id: 1356;
        };
    }
}


