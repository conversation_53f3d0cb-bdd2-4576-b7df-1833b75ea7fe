// Code generated by protoc-gen-ga-api. DO NOT EDIT.
// 	protoc        v3.9.1

syntax = "proto3";
package ga.api.emoji;



import "emoji/emoji_.proto";
import "emoji/third_party_emoji.proto";
import "api/extension/extension.proto";

option java_package      = "com.quwan.tt.proto.api";
option objc_class_prefix = "RPC";
option go_package = "golang.52tt.com/protocol/app/api/emoji;emoji";

service EmojiLogic {
    option (ga.api.extension.logic_service_name) = "emoji-logic";
    option (ga.api.extension.logic_service_language) = "go";
    option (ga.api.extension.logic_service_uri_rewrite) = "/logic.EmojiLogic/";
    rpc SaveEmoji(ga.emoji.SaveEmojiReq) returns (ga.emoji.SaveEmojiResp) {
        option (ga.api.extension.command) = {
             id: 2500
        };
    }
    rpc DeleteEmoji(ga.emoji.DeleteEmojiReq) returns (ga.emoji.DeleteEmojiResp) {
        option (ga.api.extension.command) = {
             id: 2501
        };
    }
    rpc GetEmojiPkgList(ga.emoji.GetEmojiPkgListReq) returns (ga.emoji.GetEmojiPkgListResp) {
        option (ga.api.extension.command) = {
             id: 2502
        };
    }
    rpc GetEmojiListByPkg(ga.emoji.GetEmojiListByPkgReq) returns (ga.emoji.GetEmojiListByPkgResp) {
        option (ga.api.extension.command) = {
             id: 2503
        };
    }
    rpc TestEmojiA(ga.emoji.GetEmojiPkgListReq) returns (ga.emoji.GetEmojiPkgListResp) {
        option (ga.api.extension.command) = {
             id: 3038,
             deprecated: true
        };
    }
    rpc GetHotEmoji(ga.emoji.GetHotEmojiReq) returns (ga.emoji.GetHotEmojiResp) {
        option (ga.api.extension.command) = {
          id: 2511,
        };
    }
    rpc GetSearchEmoji(ga.emoji.GetSearchEmojiReq) returns (ga.emoji.GetSearchEmojiResp) {
        option (ga.api.extension.command) = {
          id: 2512,
        };
    }
    rpc CheckHotEmojiEntrance(ga.emoji.CheckHotEmojiEntranceReq) returns (ga.emoji.CheckHotEmojiEntranceResp) {
        option (ga.api.extension.command) = {
          id: 2513,
        };
    }
}

