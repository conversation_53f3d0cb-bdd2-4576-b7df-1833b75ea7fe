syntax = "proto3";
package ga.api.extension;

import "google/protobuf/descriptor.proto";

option java_package      = "com.quwan.tt.proto.api";
option go_package        = "golang.52tt.com/protocol/app/api/extension;extension";
option objc_class_prefix = "RPC";

message LogicCommand {
    uint32 id        = 1;
    uint32 api_level = 2;
    bool deprecated  = 3;
    //如果rewrite_full_path不为空，该命令号是转移至其他服务去了
    string rewrite_full_path = 4;
}

enum IgnoredBy {
    IgnoredByAll     = 0;
    IgnoredByAndroid = 1;
    IgnoredByIos     = 2;
    IgnoredByPc      = 3;
}

extend google.protobuf.ServiceOptions {
    string logic_service_name        = 65536;
    string logic_service_language    = 65537;
    string logic_service_uri_rewrite = 65538;
}
extend google.protobuf.MethodOptions {
    LogicCommand command          = 65536;
    repeated IgnoredBy ignored_by = 65537;
}

extend google.protobuf.FieldOptions {
    bool primitive_nullable = 165530;// 生成的基本数据类型与String 是否为null
}
// import "api/extension/extension.proto";
// service ExampleExtendService {
//     rpc ExampleExtendMethod(SomeRequest) returns (SomeResponse) {
//         option (ga.api.extension.ignored_by) = IgnoredByAndroid;
//         option (ga.api.extension.ignored_by) = IgnoredByIos;
//         //option (ga.api.extension.ignored_by) = IgnoredByAll;
//     };
// }
// message SomeRequest {}
// message SomeResponse {}
