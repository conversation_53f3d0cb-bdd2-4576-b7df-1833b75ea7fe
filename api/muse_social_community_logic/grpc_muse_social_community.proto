// Code generated by protoc-gen-ga-api. DO NOT EDIT.
// 	protoc        v5.27.0

syntax = "proto3";
package ga.api.muse_social_community_logic;

import "muse_social_community_logic/muse_social_community_logic.proto";
import "channel/channel_.proto";
import "api/extension/extension.proto";

option java_package      = "com.quwan.tt.proto.api";
option objc_class_prefix = "RPC";
option go_package = "golang.52tt.com/protocol/app/api/muse_social_community_logic;muse_social_community_logic";

service MuseSocialCommunity {
    option (ga.api.extension.logic_service_name) = "muse-social-community-logic";
    option (ga.api.extension.logic_service_language) = "go";
    option (ga.api.extension.logic_service_uri_rewrite) = "/muse_social_community_logic.MuseSocialCommunity/";
    rpc ListMuseSocialCommunityNavBars(ga.muse_social_community_logic.ListMuseSocialCommunityNavBarsRequest) returns (ga.muse_social_community_logic.ListMuseSocialCommunityNavBarsResponse) {
        option (ga.api.extension.command) = {
             id: 36700;
        };
    }
    rpc ListMuseSocialCommunityNavSecondaryBars(ga.muse_social_community_logic.ListMuseSocialCommunityNavSecondaryBarsRequest) returns (ga.muse_social_community_logic.ListMuseSocialCommunityNavSecondaryBarsResponse) {
        option (ga.api.extension.command) = {
             id: 36701;
        };
    }
    rpc GetSocialCommunityChatChannelHistoryMsg(ga.channel.GetChannelMsgReq) returns (ga.channel.GetChannelMsgResp) {
        option (ga.api.extension.command) = {
             id: 36702;
        };
    }
    rpc BatGetMuseSocialCommunityUsersRole(ga.muse_social_community_logic.BatGetMuseSocialCommunityUsersRoleRequest) returns (ga.muse_social_community_logic.BatGetMuseSocialCommunityUsersRoleResponse) {
        option (ga.api.extension.command) = {
             id: 36703;
        };
    }
    rpc JoinSocialCommunityFans(ga.muse_social_community_logic.JoinSocialCommunityFansRequest) returns (ga.muse_social_community_logic.JoinSocialCommunityFansResponse) {
        option (ga.api.extension.command) = {
             id: 36705;
        };
    }
    rpc GetSocialCommunityDetail(ga.muse_social_community_logic.GetSocialCommunityDetailRequest) returns (ga.muse_social_community_logic.GetSocialCommunityDetailResponse) {
        option (ga.api.extension.command) = {
             id: 36706;
        };
    }
    rpc GetSocialCommunityProfilePages(ga.muse_social_community_logic.GetSocialCommunityProfilePagesRequest) returns (ga.muse_social_community_logic.GetSocialCommunityProfilePagesResponse) {
        option (ga.api.extension.command) = {
             id: 36707;
        };
    }
    rpc GetMySocialCommunity(ga.muse_social_community_logic.GetMySocialCommunityRequest) returns (ga.muse_social_community_logic.GetMySocialCommunityResponse) {
        option (ga.api.extension.command) = {
             id: 36708;
        };
    }
    rpc SendWelcomePush(ga.muse_social_community_logic.SendWelcomePushRequest) returns (ga.muse_social_community_logic.SendWelcomePushResponse) {
        option (ga.api.extension.command) = {
             id: 36710;
        };
    }
    rpc BatGetRankInChannel(ga.muse_social_community_logic.BatGetRankInChannelRequest) returns (ga.muse_social_community_logic.BatGetRankInChannelResponse) {
        option (ga.api.extension.command) = {
             id: 36711;
        };
    }
    rpc RemoveSocialCommunityMember(ga.muse_social_community_logic.RemoveSocialCommunityMemberRequest) returns (ga.muse_social_community_logic.RemoveSocialCommunityMemberResponse) {
        option (ga.api.extension.command) = {
             id: 36712;
        };
    }
    rpc ExitSocialCommunity(ga.muse_social_community_logic.ExitSocialCommunityRequest) returns (ga.muse_social_community_logic.ExitSocialCommunityResponse) {
        option (ga.api.extension.command) = {
             id: 36713;
        };
    }
    rpc UpdateMemberRole(ga.muse_social_community_logic.UpdateMemberRoleRequest) returns (ga.muse_social_community_logic.UpdateMemberRoleResponse) {
        option (ga.api.extension.command) = {
             id: 36714;
             deprecated: true;
        };
    }
    rpc GetSocialCommunityRoleLeftNumbers(ga.muse_social_community_logic.GetSocialCommunityRoleLeftNumbersRequest) returns (ga.muse_social_community_logic.GetSocialCommunityRoleLeftNumbersResponse) {
        option (ga.api.extension.command) = {
             id: 36715;
             deprecated: true;
        };
    }
    rpc GetChannelAssociateSocialCommunity(ga.muse_social_community_logic.GetChannelAssociateSocialCommunityRequest) returns (ga.muse_social_community_logic.GetChannelAssociateSocialCommunityResponse) {
        option (ga.api.extension.command) = {
             id: 36716;
        };
    }
    rpc GetMySocialCommunityPage(ga.muse_social_community_logic.GetMySocialCommunityPageRequest) returns (ga.muse_social_community_logic.GetMySocialCommunityPageResponse) {
        option (ga.api.extension.command) = {
             id: 36717;
        };
    }
    rpc ListCategoryTypes(ga.muse_social_community_logic.ListCategoryTypesRequest) returns (ga.muse_social_community_logic.ListCategoryTypesResponse) {
        option (ga.api.extension.command) = {
             id: 36718;
        };
    }
    rpc ListCategories(ga.muse_social_community_logic.ListCategoriesRequest) returns (ga.muse_social_community_logic.ListCategoriesResponse) {
        option (ga.api.extension.command) = {
             id: 36719;
        };
    }
    rpc ApplyCreateSocialCommunity(ga.muse_social_community_logic.ApplyCreateSocialCommunityRequest) returns (ga.muse_social_community_logic.ApplyCreateSocialCommunityResponse) {
        option (ga.api.extension.command) = {
             id: 36720;
        };
    }
    rpc GetSocialCommunityFloat(ga.muse_social_community_logic.GetSocialCommunityFloatRequest) returns (ga.muse_social_community_logic.GetSocialCommunityFloatResponse) {
        option (ga.api.extension.command) = {
             id: 36721;
        };
    }
    rpc JoinSocialCommunity(ga.muse_social_community_logic.JoinSocialCommunityRequest) returns (ga.muse_social_community_logic.JoinSocialCommunityResponse) {
        option (ga.api.extension.command) = {
             id: 36722;
        };
    }
    rpc ValidateUserHasCreateQualification(ga.muse_social_community_logic.ValidateUserHasCreateQualificationRequest) returns (ga.muse_social_community_logic.ValidateUserHasCreateQualificationResponse) {
        option (ga.api.extension.command) = {
             id: 36723;
        };
    }
    rpc GetSocialCommunityBase(ga.muse_social_community_logic.GetSocialCommunityBaseRequest) returns (ga.muse_social_community_logic.GetSocialCommunityBaseResponse) {
        option (ga.api.extension.command) = {
             id: 36724;
        };
    }
    rpc GetUserSocialGroupIds(ga.muse_social_community_logic.GetUserSocialGroupIdsRequest) returns (ga.muse_social_community_logic.GetUserSocialGroupIdsResponse) {
        option (ga.api.extension.command) = {
             id: 36725;
        };
    }
    rpc MuseSocialPreviewGroupMessage(ga.muse_social_community_logic.MuseSocialPreviewGroupMessageRequest) returns (ga.muse_social_community_logic.MuseSocialPreviewGroupMessageResponse) {
        option (ga.api.extension.command) = {
             id: 36726;
        };
    }
    rpc MuseSocialGroupRemoveAdmin(ga.muse_social_community_logic.MuseSocialGroupRemoveAdminRequest) returns (ga.muse_social_community_logic.MuseSocialGroupRemoveAdminResponse) {
        option (ga.api.extension.command) = {
             id: 36727;
        };
    }
    rpc MuseSocialGroupSetAllMute(ga.muse_social_community_logic.MuseSocialGroupSetAllMuteRequest) returns (ga.muse_social_community_logic.MuseSocialGroupSetAllMuteResponse) {
        option (ga.api.extension.command) = {
             id: 36728;
        };
    }
    rpc MuseSocialGroupMuteMember(ga.muse_social_community_logic.MuseSocialGroupMuteMemberRequest) returns (ga.muse_social_community_logic.MuseSocialGroupMuteMemberResponse) {
        option (ga.api.extension.command) = {
             id: 36729;
        };
    }
    rpc MuseSocialGroupUnmuteMember(ga.muse_social_community_logic.MuseSocialGroupUnmuteMemberRequest) returns (ga.muse_social_community_logic.MuseSocialGroupUnmuteMemberResponse) {
        option (ga.api.extension.command) = {
             id: 36730;
        };
    }
    rpc MuseSocialGroupGetMuteList(ga.muse_social_community_logic.MuseSocialGroupGetMuteListRequest) returns (ga.muse_social_community_logic.MuseSocialGroupGetMuteListResponse) {
        option (ga.api.extension.command) = {
             id: 36731;
        };
    }
    rpc MuseSocialGroupGetMemberList(ga.muse_social_community_logic.MuseSocialGroupGetMemberListRequest) returns (ga.muse_social_community_logic.MuseSocialGroupGetMemberListResponse) {
        option (ga.api.extension.command) = {
             id: 36732;
        };
    }
    rpc MuseSocialGroupGetDetailInfo(ga.muse_social_community_logic.MuseSocialGroupGetDetailInfoRequest) returns (ga.muse_social_community_logic.MuseSocialGroupGetDetailInfoResponse) {
        option (ga.api.extension.command) = {
             id: 36733;
        };
    }
    rpc MuseSocialGroupAddAdmin(ga.muse_social_community_logic.MuseSocialGroupAddAdminRequest) returns (ga.muse_social_community_logic.MuseSocialGroupAddAdminResponse) {
        option (ga.api.extension.command) = {
             id: 36734;
        };
    }
    rpc GetSocialGroupOnlineMembers(ga.muse_social_community_logic.GetSocialGroupOnlineMembersRequest) returns (ga.muse_social_community_logic.GetSocialGroupOnlineMembersResponse) {
        option (ga.api.extension.command) = {
             id: 36735;
        };
    }
    rpc BatGetSocialCommunityKernelMembers(ga.muse_social_community_logic.BatGetSocialCommunityKernelMembersRequest) returns (ga.muse_social_community_logic.BatGetSocialCommunityKernelMembersResponse) {
        option (ga.api.extension.command) = {
             id: 36736;
        };
    }
    rpc GetGroupActiveMembers(ga.muse_social_community_logic.GetGroupActiveMembersRequest) returns (ga.muse_social_community_logic.GetGroupActiveMembersResponse) {
        option (ga.api.extension.command) = {
             id: 36737;
        };
    }
    rpc GetSocialCommunityMemberList(ga.muse_social_community_logic.GetSocialCommunityMemberListRequest) returns (ga.muse_social_community_logic.GetSocialCommunityMemberListResponse) {
        option (ga.api.extension.command) = {
             id: 36738;
        };
    }
    rpc GetSocialCommunityAnnounceNewsCount(ga.muse_social_community_logic.GetSocialCommunityAnnounceNewsCountRequest) returns (ga.muse_social_community_logic.GetSocialCommunityAnnounceNewsCountResponse) {
        option (ga.api.extension.command) = {
             id: 36740;
        };
    }
    rpc UpsertMuseSocialAnnounce(ga.muse_social_community_logic.UpsertMuseSocialAnnounceRequest) returns (ga.muse_social_community_logic.UpsertMuseSocialAnnounceResponse) {
        option (ga.api.extension.command) = {
             id: 36741;
        };
    }
    rpc ListAnnounceDestinations(ga.muse_social_community_logic.ListAnnounceDestinationsRequest) returns (ga.muse_social_community_logic.ListAnnounceDestinationsResponse) {
        option (ga.api.extension.command) = {
             id: 36742;
        };
    }
    rpc ListMuseSocialAnnounces(ga.muse_social_community_logic.ListMuseSocialAnnouncesRequest) returns (ga.muse_social_community_logic.ListMuseSocialAnnouncesResponse) {
        option (ga.api.extension.command) = {
             id: 36743;
        };
    }
    rpc SetMuseSocialAnnounceInterest(ga.muse_social_community_logic.SetMuseSocialAnnounceInterestRequest) returns (ga.muse_social_community_logic.SetMuseSocialAnnounceInterestResponse) {
        option (ga.api.extension.command) = {
             id: 36744;
        };
    }
    rpc RemoveMuseSocialAnnounce(ga.muse_social_community_logic.RemoveMuseSocialAnnounceRequest) returns (ga.muse_social_community_logic.RemoveMuseSocialAnnounceResponse) {
        option (ga.api.extension.command) = {
             id: 36745;
        };
    }
    rpc ListMuseSocialAnnounceInterestUsers(ga.muse_social_community_logic.ListMuseSocialAnnounceInterestUsersRequest) returns (ga.muse_social_community_logic.ListMuseSocialAnnounceInterestUsersResponse) {
        option (ga.api.extension.command) = {
             id: 36746;
        };
    }
    rpc ValidateUserHasCreateAnnouncePermissions(ga.muse_social_community_logic.ValidateUserHasCreateAnnouncePermissionsRequest) returns (ga.muse_social_community_logic.ValidateUserHasCreateAnnouncePermissionsResponse) {
        option (ga.api.extension.command) = {
             id: 36747;
        };
    }
    rpc SetCommunityAdditionMode(ga.muse_social_community_logic.SetCommunityAdditionModeRequest) returns (ga.muse_social_community_logic.SetCommunityAdditionModeResponse) {
        option (ga.api.extension.command) = {
             id: 36748;
        };
    }
    rpc GetCommunityAdditionMode(ga.muse_social_community_logic.GetCommunityAdditionModeRequest) returns (ga.muse_social_community_logic.GetCommunityAdditionModeResponse) {
        option (ga.api.extension.command) = {
             id: 36749;
        };
    }
    rpc ListSocialCommunitySystemMessage(ga.muse_social_community_logic.ListSocialCommunitySystemMessageRequest) returns (ga.muse_social_community_logic.ListSocialCommunitySystemMessageResponse) {
        option (ga.api.extension.command) = {
             id: 36750;
        };
    }
    rpc SubmitApplicationToJoinCommunity(ga.muse_social_community_logic.SubmitApplicationToJoinCommunityRequest) returns (ga.muse_social_community_logic.SubmitApplicationToJoinCommunityResponse) {
        option (ga.api.extension.command) = {
             id: 36751;
        };
    }
    rpc UpsertJoinSocialCommunityMessageStatus(ga.muse_social_community_logic.UpsertJoinSocialCommunityMessageStatusRequest) returns (ga.muse_social_community_logic.UpsertJoinSocialCommunityMessageStatusResponse) {
        option (ga.api.extension.command) = {
             id: 36752;
        };
    }
    rpc GetSocialCommunityUpdateLevelTip(ga.muse_social_community_logic.GetSocialCommunityUpdateLevelTipRequest) returns (ga.muse_social_community_logic.GetSocialCommunityUpdateLevelTipResponse) {
        option (ga.api.extension.command) = {
             id: 36753;
        };
    }
    rpc GetSocialCommunityLevelDetail(ga.muse_social_community_logic.GetSocialCommunityLevelDetailRequest) returns (ga.muse_social_community_logic.GetSocialCommunityLevelDetailResponse) {
        option (ga.api.extension.command) = {
             id: 36754;
        };
    }
    rpc SocialCommunityCheckIn(ga.muse_social_community_logic.SocialCommunityCheckInRequest) returns (ga.muse_social_community_logic.SocialCommunityCheckInResponse) {
        option (ga.api.extension.command) = {
             id: 36755;
        };
    }
    rpc GetSocialCommunityContentStreamNewsCount(ga.muse_social_community_logic.GetSocialCommunityContentStreamNewsCountRequest) returns (ga.muse_social_community_logic.GetSocialCommunityContentStreamNewsCountResponse) {
        option (ga.api.extension.command) = {
             id: 36756;
        };
    }
    rpc GetSocialCommunityContentStream(ga.muse_social_community_logic.GetSocialCommunityContentStreamRequest) returns (ga.muse_social_community_logic.GetSocialCommunityContentStreamResponse) {
        option (ga.api.extension.command) = {
             id: 36757;
        };
    }
    rpc ListMuseSocialCommunityCommentMessage(ga.muse_social_community_logic.ListMuseSocialCommunityCommentMessageRequest) returns (ga.muse_social_community_logic.ListMuseSocialCommunityCommentMessageResponse) {
        option (ga.api.extension.command) = {
             id: 36758;
        };
    }
    rpc ListMuseSocialCommunityAttitudeMessage(ga.muse_social_community_logic.ListMuseSocialCommunityAttitudeMessageRequest) returns (ga.muse_social_community_logic.ListMuseSocialCommunityAttitudeMessageResponse) {
        option (ga.api.extension.command) = {
             id: 36759;
        };
    }
    rpc GetSocialCommunityNonPublicUserCard(ga.muse_social_community_logic.GetSocialCommunityNonPublicUserCardRequest) returns (ga.muse_social_community_logic.GetSocialCommunityNonPublicUserCardResponse) {
        option (ga.api.extension.command) = {
             id: 36760;
        };
    }
    rpc IntroduceSocialCommunityByCategoryId(ga.muse_social_community_logic.IntroduceSocialCommunityByCategoryIdRequest) returns (ga.muse_social_community_logic.IntroduceSocialCommunityByCategoryIdResponse) {
        option (ga.api.extension.command) = {
             id: 36761;
        };
    }
    rpc ListMuseSocialCommunityNavBarsV2(ga.muse_social_community_logic.ListMuseSocialCommunityNavBarsV2Request) returns (ga.muse_social_community_logic.ListMuseSocialCommunityNavBarsV2Response) {
        option (ga.api.extension.command) = {
             id: 36762;
        };
    }
    rpc ListMuseSocialCommunityNavSecondaryBarsV2(ga.muse_social_community_logic.ListMuseSocialCommunityNavSecondaryBarsV2Request) returns (ga.muse_social_community_logic.ListMuseSocialCommunityNavSecondaryBarsV2Response) {
        option (ga.api.extension.command) = {
             id: 36763;
        };
    }
    rpc ListMuseSocialCommunityGroups(ga.muse_social_community_logic.ListMuseSocialCommunityGroupsRequest) returns (ga.muse_social_community_logic.ListMuseSocialCommunityGroupsResponse) {
        option (ga.api.extension.command) = {
             id: 36764;
        };
    }
    rpc BatchMuseSocialCommunityNavBarsV2(ga.muse_social_community_logic.BatchMuseSocialCommunityNavBarsV2Request) returns (ga.muse_social_community_logic.BatchMuseSocialCommunityNavBarsV2Response) {
        option (ga.api.extension.command) = {
             id: 36765;
        };
    }
    rpc UpdateSocialCommunityInfo(ga.muse_social_community_logic.UpdateSocialCommunityInfoRequest) returns (ga.muse_social_community_logic.UpdateSocialCommunityInfoResponse) {
        option (ga.api.extension.command) = {
             id: 36766;
        };
    }
    rpc GetSocialCommunityEditableInfo(ga.muse_social_community_logic.GetSocialCommunityEditableInfoRequest) returns (ga.muse_social_community_logic.GetSocialCommunityEditableInfoResponse) {
        option (ga.api.extension.command) = {
             id: 36767;
        };
    }
    rpc ReportPersonalChannelViewSocialCommunity(ga.muse_social_community_logic.ReportPersonalChannelViewSocialCommunityRequest) returns (ga.muse_social_community_logic.ReportPersonalChannelViewSocialCommunityResponse) {
        option (ga.api.extension.command) = {
             id: 36768;
        };
    }
    rpc GetMemberStatusInTheSocialCommunity(ga.muse_social_community_logic.GetMemberStatusInTheSocialCommunityRequest) returns (ga.muse_social_community_logic.GetMemberStatusInTheSocialCommunityResponse) {
        option (ga.api.extension.command) = {
             id: 36769;
        };
    }
    rpc GetSocialCommunityAssistantMsgCount(ga.muse_social_community_logic.GetSocialCommunityAssistantMsgCountRequest) returns (ga.muse_social_community_logic.GetSocialCommunityAssistantMsgCountResponse) {
        option (ga.api.extension.command) = {
             id: 36770;
        };
    }
    rpc GetSocialCommunityInvitationCodeDetail(ga.muse_social_community_logic.GetSocialCommunityInvitationCodeDetailRequest) returns (ga.muse_social_community_logic.GetSocialCommunityInvitationCodeDetailResponse) {
        option (ga.api.extension.command) = {
            id: 36771;
        };
    }
    rpc GetSocialCommunityInvitationCodeShareText(ga.muse_social_community_logic.GetSocialCommunityInvitationCodeShareTextRequest) returns (ga.muse_social_community_logic.GetSocialCommunityInvitationCodeShareTextResponse) {
        option (ga.api.extension.command) = {
            id: 36772;
        };
    }
    rpc SocialCommunitySharePreCheck(ga.muse_social_community_logic.SocialCommunitySharePreCheckRequest) returns (ga.muse_social_community_logic.SocialCommunitySharePreCheckResponse) {
        option (ga.api.extension.command) = {
            id: 36773;
        };
    }
    rpc CheckSocialCommunityInvitationUser(ga.muse_social_community_logic.CheckSocialCommunityInvitationUserRequest) returns (ga.muse_social_community_logic.CheckSocialCommunityInvitationUserResponse) {
        option (ga.api.extension.command) = {
            id: 36774;
        };
    }
    rpc SearchSocialCommunity(ga.muse_social_community_logic.SearchSocialCommunityRequest) returns (ga.muse_social_community_logic.SearchSocialCommunityResponse) {
        option (ga.api.extension.command) = {
            id: 36775;
        };
    }
    rpc GetUserSocialCommunityList(ga.muse_social_community_logic.GetUserSocialCommunityListRequest) returns (ga.muse_social_community_logic.GetUserSocialCommunityListResponse) {
        option (ga.api.extension.command) = {
            id: 36776;
        };
    }
    // 获取麦位扩展权限
    rpc GetExtendMicPermission(ga.muse_social_community_logic.GetExtendMicPermissionRequest) returns (ga.muse_social_community_logic.GetExtendMicPermissionResponse) {
        option (ga.api.extension.command) = {
            id: 36777;
        };
    }
    // 设置麦位数量
    rpc SetExtendMicNumbers(ga.muse_social_community_logic.SetExtendMicNumbersRequest) returns (ga.muse_social_community_logic.SetExtendMicNumbersResponse) {
        option (ga.api.extension.command) = {
            id: 36778;
        };
    }
    //获取已解锁的麦位数量
    rpc GetExtendMicNumbers(ga.muse_social_community_logic.GetExtendMicNumbersRequest) returns (ga.muse_social_community_logic.GetExtendMicNumbersResponse) {
        option (ga.api.extension.command) = {
            id: 36779;
        };
    }

  //获取社群主理人
  rpc GetSocialCommunityCaptain(ga.muse_social_community_logic.GetSocialCommunityCaptainRequest) returns (ga.muse_social_community_logic.GetSocialCommunityCaptainResponse) {
    option (ga.api.extension.command) = {
      id: 36780;
    };
  }

  //获取活动弹窗
  rpc GetSocialCommunityActivityPopup(ga.muse_social_community_logic.GetSocialCommunityActivityPopupRequest) returns (ga.muse_social_community_logic.GetSocialCommunityActivityPopupResponse) {
    option (ga.api.extension.command) = {
      id: 36781;
    };
  }



}


