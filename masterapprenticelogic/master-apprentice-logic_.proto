syntax = "proto3";

package ga.masterapprenticelogic;

import "ga_base.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/masterapprenticelogic";

// 师徒关系 发出邀请
message MasterInviteMsgReq{
    ga.BaseReq base_req = 1;
    string master_account = 2;
    string apprentice_account = 3;
}

message MasterInviteMsgResp{
    ga.BaseResp base_resp = 1;
}


// 师徒关系 接受邀请
message ApprenticeEstablishMsgReq{
    ga.BaseReq base_req = 1;
    string master_account = 2;
    string apprentice_account = 3;
}

message ApprenticeEstablishMsgResp{
    ga.BaseResp base_resp = 1;
}

// 入口信息
message EntranceInfoReq{
    ga.BaseReq base_req = 1;
    uint32 uid = 2;
}
message EntranceInfoResp{
    ga.BaseResp base_resp = 1;
    string title = 3; // 标题
    // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
    string subTitle = 4;  // 文案
    uint32 act_begin_time = 5; // 师徒活动开始时间
    uint32 act_end_time = 6; // 师徒活动截止时间
    uint32 invite_end_time = 7; // 收徒截止时间
    uint32 task_end_time = 8; // 师徒任务截止时间
}


