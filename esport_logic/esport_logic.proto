syntax = "proto3";

/***************电竞指导logic*****************/

package ga.esport_logic;

import "ga_base.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/esport_logic";


// 游戏类型
enum EsportGameType {
    ESPORT_GAME_TYPE_UNSPECIFIED = 0;
    ESPORT_GAME_TYPE_MOBILE = 1; // 手游
    ESPORT_GAME_TYPE_PC = 2; // 端游
}


// 分页请求游戏列表
message GetGameListRequest {
    ga.BaseReq base_req = 1;
    EsportGameType game_type = 2; // 游戏类型
    string page_token = 3; // 分页token，第一页传空字符串
  // 页大小由服务端控制
}

message GetGameListResponse {
    ga.BaseResp base_resp = 1;
    repeated GameItem item_list = 2; // 游戏列表
    string next_page_token = 3; // 非空则数据还未请求完全，下次请求带上这个token
}

// 找人优化，获取顶部游戏列表
message GetTopGameListRequest {
    ga.BaseReq base_req = 1;
    EsportGameType game_type = 2; // 游戏类型 移动端忽略
}

message GameItem {
    uint32 game_id = 1; // 游戏id
    string game_name = 2; // 游戏名称
    string game_icon = 3; // 游戏图标
}
message GetTopGameListResponse {
    ga.BaseResp base_resp = 1;
    repeated GameItem item_list = 2;
}


// 获取电竞指导总开关
message GetSwitchRequest {
    ga.BaseReq base_req = 1;
}

// 开关状态
enum EsportSwitchStatus {
    ESPORT_SWITCH_STATUS_UNSPECIFIED = 0;
    ESPORT_SWITCH_STATUS_ON = 1; // 打开
    ESPORT_SWITCH_STATUS_OFF = 2; // 关闭
}

// 用于 response 和 开关状态推送
message SwitchStatus {
    EsportSwitchStatus main_switch_status = 1; // 总开关状态
    EsportSwitchStatus homepage_switch_status = 2; // 个人主页开关
    EsportSwitchStatus sidebar_switch_status = 3; // 侧边栏开关
    EsportSwitchStatus video_desc_switch_status = 4; // 语音描述开关
}

message GetSwitchResponse {
    ga.BaseResp base_resp = 1;
    SwitchStatus switch_status = 2; // 开关状态

}

enum EsportCoachType {
    ESPORT_COACH_TYPE_UNSPECIFIED = 0;
    ESPORT_COACH_TYPE_PERSONAL = 1; // 个人指导
    ESPORT_COACH_TYPE_GUILD = 2;    // 公会指导
}

// 获取用户电竞指导身份信息（成为电竞指导入口(非工会入口)）
message GetESportApplyAccessRequest {
    ga.BaseReq base_req = 1;
}

message GetESportApplyAccessResponse {
    ga.BaseResp base_resp = 1;
    bool show_access = 2; // 是否显示成为电竞指导入口
}

message GetIMFloatWindowInfoRequest {
    ga.BaseReq base_req = 1;
    uint32 target_uid = 2; // 对方uid
}

message GetIMFloatWindowInfoResponse {
    ga.BaseResp base_resp = 1;
    repeated OrderSimpleInfo order_list = 3; // 订单列表
    repeated SkillProduct product_list = 4; // 上架的技能商品列表
}

// ================= 接单大厅 proto ================
// 判断大神身份
message GetEsportCoachTypeRequest {
    ga.BaseReq base_req = 1;
}

message GetEsportCoachTypeResponse {
    ga.BaseResp base_resp = 1;
    uint32 esport_coach_type = 2; // 指导类型 see EsportCoachType
}

// 个人主页技能列表
message GetHomePageSkillProductListRequest {
    ga.BaseReq base_req = 1;
    uint32 uid = 2; // 教练uid
    uint32 player_uid = 3; // 玩家uid，新增是因为邀请下单时，需要用于判断是否有首局优惠。若不传则用请求uid
}
message GetHomepageSkillProductListResponse {
    ga.BaseResp base_resp = 1;
    uint32 esport_coach_type = 2; // 指导类型 see EsportCoachType
    repeated SkillProduct product_list = 3; // 上架的技能商品列表
    GuildInfo guild_info = 4;  // 工会信息, 仅为公会指导时候才会有数据
    string god_page_url = 5; // 大神页url
    CoachLabel coach_label = 6; // 指导标识
    repeated string coach_label_list = 7; // 大神标识
    string god_page_url_with_mission = 8; // 大神页url(带任务)
}

// 邀请下单推荐
message GetInviteOrderRecommendRequest {
    ga.BaseReq base_req = 1;
    uint32 invite_uid = 2;
}

message GetInviteOrderRecommendResponse {
    ga.BaseResp base_resp = 1;
    SkillProduct product = 2; // 推荐的商品
}

// 邀请下单
message InviteOrderRequest {
    ga.BaseReq base_req = 1;
    uint64 skill_product_id = 2; // 商品id
    uint32 invite_uid = 3; // 邀请人uid
    uint32 order_cnt = 4; // 下单数量
    string order_desc = 5; // 下单描述
    string discount_info = 6; // 使用的优惠信息，是一个json，透传用于校验
}

message InviteOrderResponse {
    ga.BaseResp base_resp = 1;
}


message HandleInviteOrderRequest {
    ga.BaseReq base_req = 1;
    uint32 invite_id = 2; // 邀请id
    uint32 status = 3; // 状态 see InviteOrderStatus
}

message HandleInviteOrderResponse {
    ga.BaseResp base_resp = 1;
}


enum InviteOrderStatus {
    INVITE_ORDER_STATUS_UNSPECIFIED = 0;
    INVITE_ORDER_STATUS_VALID = 1; // 有效
    INVITE_ORDER_STATUS_IGNORE = 2; // 忽略
    INVITE_ORDER_STATUS_PAYED = 3; // 已支付
    INVITE_ORDER_STATUS_EXPIRED = 4; // 已失效
}

enum WaitReceiveOrderStatus {
    WAIT_RECEIVE_ORDER_STATUS_UNSPECIFIED = 0;
    WAIT_RECEIVE_ORDER_STATUS_WAIT = 1; // 等待中
    WAIT_RECEIVE_ORDER_STATUS_RECEIVE = 2; // 已接单
    WAIT_RECEIVE_ORDER_STATUS_TIMEOUT = 3; // 失效
    WAIT_RECEIVE_ORDER_STATUS_REFUSE = 4; // 拒绝
}

enum NotifyToFinishStatus {
    NOTIFY_TO_FINISH_STATUS_UNSPECIFIED = 0;
    NOTIFY_TO_FINISH_STATUS_WAIT = 1; // 等待中
    NOTIFY_TO_FINISH_STATUS_FINISH = 2; // 已完成
}

// 电竞订单im消息卡片
message EsportOrderImMsg {
    enum EventType {
        EVENT_TYPE_UNSPECIFIED = 0;
        EVENT_TYPE_INVITE_ORDER = 1; // 邀请下单 status see InviteOrderStatus
        EVENT_TYPE_WAIT_RECEIVE = 2; // 等待接单 status see WaitReceiveOrderStatus
        EVENT_TYPE_NOTIFY_TO_FINISH = 3; // 提醒去完成 status see NotifyToFinishStatus
        EVENT_TYPE_ORDER_REFUND = 4;   // 用户提起退款 status see RefundStatus
        EVENT_TYPE_ORDER_REJECT_REFUND = 5;   // 电竞指导者拒绝退款申请
        EVENT_TYPE_ORDER_APPLY_APPEAL = 6;   // 用户提起退款申诉
    };
    enum CardStyle {
        CARD_STYLE_UNSPECIFIED = 0; // 未指定，需要兜底处理
        CARD_STYLE_FAST_REFUND = 1; // 快速退款
    };
    string msg_title = 1;  // 消息标题
    ProductOrder product_order = 2; // 商品订单
    uint32 event_type = 3; // 事件类型 see EventType
    uint32 status = 4;   // 状态, 根据不同EventType确定不同状态枚举
    string content = 5;   // 消息内容
    string order_id = 6; // 订单id
    uint32 coach_uid = 7; // 陪玩uid
    uint32 player_uid = 8; // 玩家uid
    int64 end_time = 9; // 截止时间
    uint32 invite_id = 10; // 邀请id
    uint32 order_status = 11; // 订单状态 see OrderStatus
    CardStyle card_style = 12; // 特殊样式
    string remark = 13; // 备注
}

enum ImSysMsgFinishStatus {
    IM_SYS_MSG_FINISH_STATUS_UNSPECIFIED = 0;
    IM_SYS_MSG_FINISH_STATUS_NOT_EVALUATE = 1; // 未评价
    IM_SYS_MSG_FINISH_STATUS_HAS_EVALUATE = 2; // 已评价
}

// 电竞im系统消息
message EsportImSysMsg {
    enum EventType {
        EVENT_TYPE_UNSPECIFIED = 0;
        EVENT_TYPE_ORDER_NOT_RECEIVE = 1; // 未接单（拒绝接单和超时未接单）
        EVENT_TYPE_ORDER_CANCELED = 2; // 取消订单
        EVENT_TYPE_ORDER_RECEIVED = 3; // 已接单
        EVENT_TYPE_ORDER_FINISH = 4;   // 订单已完成  status see ImSysMsgFinishStatus
        EVENT_TYPE_ORDER_ACCEPT_REFUND = 5;   // 电竞指导者接受退款申请
        EVENT_TYPE_ORDER_APPEAL_RESULT = 6;   // 退款申诉结果
    }

    uint32 event_type = 1; // 事件类型
    string msg_title = 2; // 消息标题
    string msg_content = 3; // 消息内容
    string order_id = 4; // 订单id
    uint32 status = 5;
    uint32 coach_uid = 6; // 陪玩uid
    uint32 player_uid = 7; // 玩家uid
    uint32 order_status = 11; // 订单状态 see OrderStatus
}

// 电竞专区
// 获取游戏属性列表
message GetGamePropertyListRequest {
    ga.BaseReq base_req = 1;
    uint32 game_id = 2; // 游戏id
}

message GetGamePropertyListResponse {
    ga.BaseResp base_resp = 1;
    repeated GameProperty property_list = 2; // 游戏属性列表
}

// 列表请求来源
enum AreaCoachListFromSource {
    AREA_COACH_LIST_FROM_SOURCE_UNSPECIFIED = 0;
    AREA_COACH_LIST_FROM_SOURCE_ESPORT_AREA = 1; // 电竞专区
    AREA_COACH_LIST_FROM_SOURCE_ESPORT_TOP_TAB = 2; // 一级Tab
}

// 获取电竞专区技能商品列表
message GetEsportAreaCoachListRequest {
    ga.BaseReq base_req = 1;
    uint32 game_id = 2; // 游戏id
    repeated GameProperty search_option_list = 3; // 搜索条件
    uint32 offset = 4; // 翻页游标
    uint32 limit = 5; // 每页大小
    uint32 from_source = 6; // 来源 see AreaCoachListFromSource
}

message GetEsportAreaCoachListResponse {
    ga.BaseResp base_resp = 1;
    repeated EsportAreaCoachInfo coach_list = 2; // 教练列表
    uint32 next_offset = 3; // 下一页游标
    string coach_detail_url = 4; // 教练详情页, 例: https://app.52tt.com/testing/frontend-web-assist-esports-coaching-god/index.html/!#/game-detail/{gameId}?immersion=1&target_id={uid}
}

// 上报已曝光大神列表
message ReportExposeCoachRequest {
    enum ExposeCoachType {
        EXPOSE_COACH_TYPE_UNSPECIFIED = 0;
        EXPOSE_COACH_TYPE_ESPORT_AREA = 1; // 电竞专区
        EXPOSE_COACH_TYPE_ESPORT_KING_TAB = 2; // 王者tab
        EXPOSE_COACH_TYPE_ESPORT_UGC_CHANNEL = 3; // ugc开黑房
        EXPOSE_COACH_TYPE_ESPORT_HELPER_ACTIVITY = 4; // 助手活动页
        EXPOSE_COACH_TYPE_ESPORT_GLOBAL_POPUP = 5; // 全局弹窗
    }

    ga.BaseReq base_req = 1;
    repeated uint32 expose_coach_list = 2; // 曝光大神uid列表
    uint32 game_id = 3; // 曝光的游戏id
    uint32 expose_type = 4; // 曝光类型 see ExposeCoachType
}

message ReportExposeCoachResponse {
    ga.BaseResp base_resp = 1;
}

enum LabelSourceType {
    LABEL_SOURCE_TYPE_UNSPECIFIED = 0;
    LABEL_SOURCE_TYPE_PNG = 1; // PNG
    LABEL_SOURCE_TYPE_WEBP = 2; // WEBP
}
// 电竞指导标识
message CoachLabel {
    LabelSourceType type = 1; // 资源类型
    string source_url = 2; // 资源url
}

// ================= struct ================

// 技能商品信息
message SkillProduct {
    uint64 id = 1; // id
    string name = 2; // 名称
    string tag = 3; // 标签 eg:段位
    float score = 4; // 评分
    PriceInfo price = 5; // 价格
    string icon = 6; // 图标
    string background = 7; // 背景
    string background_color = 8; // 背景颜色
    uint32 order_num = 9; // 订单数量
    uint32 game_id = 10; // 游戏配置id
    bool is_famous_player = 11; // 是否是知名选手
    repeated string skill_label_list = 12; // 技能标识
    string guarantee_win_text = 13; // 包赢文案
    repeated string skill_label_url_list = 14; // 技能类型的标识图标的 url
}

// 优惠类型
enum DiscountType {
    DISCOUNT_TYPE_UNSPECIFIED = 0;
    DISCOUNT_TYPE_FIRST_ROUND = 1; // 有首局优惠
    DISCOUNT_TYPE_COUPON = 2; // 有优惠券
    DISCOUNT_TYPE_NEW_CUSTOMER = 3; // 有新客价
}

message PriceInfo {
    uint32 price = 1; // 价格        单价格式: price+price_unit/measure_cnt+measure_unit
    string price_unit = 2; // 价格单位
    uint32 measure_cnt = 3; // 计量数目
    string measure_unit = 4; // 计量单位
    uint32 max_order_cnt = 5; // 最大下单数量
    bool has_first_round_discount = 6; // 是否有首局优惠
    uint32 first_round_price = 7; // 首局优惠价格
    bool has_discount = 8; // 是否有优惠
    uint32 discount_price = 9; // 优惠价，有可能优惠后为0
    uint32 discount_type = 10; // 优惠类型，参考DiscountType
    string discount_desc = 11; // 优惠描述，如：券后价/首局价
}

message GuildInfo {
    uint32 guild_id = 1; // 工会id
    string name = 2; // 工会名称
}

enum UiStyleType {
    UI_STYLE_TYPE_UNSPECIFIED = 0; // 默认纯文字类型
    UI_STYLE_TYPE_IMG = 1; // 纯图片类型
    UI_STYLE_TYPE_ICON_TEXT = 2; // 图标+文字类型
}

message UiStyle {
    UiStyleType ui_style_type = 1; // 展示样式
    string icon_url = 2; // 图标url
    string text = 3;// 文字
}

// 游戏属性
message GameProperty {
    enum PropertyType {
        PROPERTY_TYPE_UNSPECIFIED = 0;
        PROPERTY_TYPE_GENDER = 1;  // 性别
        PROPERTY_TYPE_PRICE = 2;    // 价格
        PROPERTY_TYPE_CUSTOM = 3;   // 自定义
        PROPERTY_TYPE_FAMUOS_PLAYER = 4; // 知名选手
        PROPERTY_TYPE_GUARANTEE_WIN = 5; // 包赢
        PROPERTY_TYPE_FIRST_ROUND = 6; // 首单
    }

    enum SelectType {
        SELECT_TYPE_UNSPECIFIED = 0;
        SELECT_TYPE_SINGLE = 1; // 单选
        SELECT_TYPE_MULTI = 2; // 多选
    }

    enum ShowStyle {
        SHOW_STYLE_UNSPECIFIED = 0; // 默认展示方式
        SHOW_STYLE_EXPEND = 1; // 对属性值进行展开，单独展示
    }

    uint32 id = 1; // 属性id
    string name = 2; // 属性名称
    repeated GamePropertyVal val_list = 3; // 属性值列表
    uint32 property_type = 4; // 属性类型
    uint32 select_type = 5; // 选择类型
    bool expose = 6; // 是否外显
    ShowStyle show_style = 7; // 展示方式
}

// 游戏属性值
message GamePropertyVal {
    uint32 id = 1; // 属性值id
    string name = 2; // 属性值名称
    UiStyle ui_style = 3; // 展示样式
}

message EsportAreaCoachInfo {
    enum OnlineStatus {
        ONLINE_STATUS_UNSPECIFIED = 0;
        ONLINE_STATUS_ONLINE = 1; // 在线
        ONLINE_STATUS_OFFLINE = 2; // 离线
    }
    enum CoachStatus {
        COACH_STATUS_UNSPECIFIED = 0;
        COACH_STATUS_IN_CHANNEL = 1; // 在房间
        COACH_STATUS_ON_MIC = 2; // 在麦上
    }
    UserProfile user_profile = 1; // 电竞指导个人信息
    string tag = 2; // 标签 eg:段位
    PriceInfo price = 3; // 价格
    string text_desc = 6; // 文字描述
    string voice_desc = 7; // 语音描述
    uint32 online_status = 8; // 在线状态 1:在线 2:离线
    uint32 voice_desc_duration = 9; // 语音描述时长
    CoachLabel coach_label = 10; // 电竞指导标识
    string guarantee_win_text = 11; // 包赢文案
    bool is_famous_player = 12; // 是否是知名选手
    repeated string coach_label_list = 13; // 大神标识
    repeated string skill_label_list = 14; // 技能标识
    float evaluate_avg_score = 15; // 评价平均分
    uint32 service_order_num = 16; // 服务单数
    bool is_limit = 17; // 是否曝光限制
    bool is_in_time = 18; // 是否秒接单
    float race = 19; // 分数
    repeated string skill_label_url_list = 20; // 技能类型的标识图标的 url
    string hint = 21; // 推荐提示
    uint32 game_id = 22; // 游戏id
    bool is_new_coach = 23; // 是否新大神
    repeated string label_list_for_front_page = 24;// 首页标签
    string voice_special_label = 25; // 语音特色 为空则显示时长
    repeated string special_label_list = 26; // 特色标签
    repeated string special_label_icon_list = 27; // 特色标签图标
    uint32 strategy_id = 28; // 策略id
    uint32 recall_source_id = 29; // 召回源id
    uint32 coach_status = 30; // 大神状态, see enum CoachStatus
}

// ================= 接单大厅相关 proto ================



/******************** 订单相关 begin **********************/

// 商品订单
message ProductOrder {
    UserProfile coach = 1;    // 电竞指导个人信息
    uint64 product_id = 2;    // 商品id
    string name = 3;    // 技能名称
    string icon = 4;    // 技能图标
    PriceInfo price_info = 5; // 价格信息
    uint32 count = 6;         // 商品数量
    uint32 total_price = 7;   // 总价，是用户实际支付的金额
    uint32 game_id = 8;       // 游戏id
    string guarantee_win_text = 9; // 包赢文案
    bool can_call_customer = 10;// 能否联系客服
    uint32 coach_total_price = 11; // 大神侧看到的订单总价
    CouponUseDetail coupon_use_detail = 12; // 优惠券使用详情
    NewCustomerUseDetail new_customer_use_detail = 13; // 新客优惠使用详情
}

// 优惠券使用详情
message CouponUseDetail {
    bool use_coupon = 1; // 订单是否使用了优惠券
    uint32 coupon_money = 2; // 优惠券抵扣的金额，单位是T豆
}

// 新客优惠使用详情
message NewCustomerUseDetail {
    bool use_new_customer_discount = 1; // 是否使用新客优惠
    uint32 new_customer_price = 2; // 新客价，单位豆
    uint32 plat_bonus_fee = 3; // 平台补贴金额，单位豆
}

// 订单状态
enum OrderStatus {
    ORDER_STATUS_UNSPECIFIED = 0;
    ORDER_STATUS_PAYED = 1;             // 已支付/待接单
    ORDER_STATUS_RECEIVED = 2;          // 已接单/进行中
    ORDER_STATUS_IN_REFUNDING = 3;      // 售后退款申诉中 子状态 see RefundStatus
    ORDER_STATUS_FINISHED = 4;          // 已完成
    ORDER_STATUS_CANCELED = 5;          // 已取消 子状态 see CanceledOrderSubStatus
    ORDER_STATUS_REFUNDED = 6;          // 已退款
}

//// 订单进行中的子状态
//enum ReceivedOrderSubStatus {
//  RECEIVED_ORDER_SUB_STATUS_UNSPECIFIED = 0;
//  RECEIVED_ORDER_SUB_STATUS_NOT_NOTIFY_FINISH = 1;   // 未提醒去完成
//  RECEIVED_ORDER_SUB_STATUS_NOTIFY_FINISH = 2;       // 已提醒去完成
//}

// 订单已取消子状态
enum CanceledOrderSubStatus {
    CANCELED_ORDER_SUB_STATUS_UNSPECIFIED = 0;
    CANCELED_ORDER_SUB_STATUS_PLAYER_CANCEL = 1;    // 玩家取消
    CANCELED_ORDER_SUB_STATUS_COACH_REFUSE = 2;     // 电竞指导拒绝
    CANCELED_ORDER_SUB_STATUS_COACH_TIMEOUT = 3;    // 超时未处理
}

// 订单售后状态
enum RefundStatus {
    REFUND_STATUS_UNSPECIFIED = 0;
    REFUND_STATUS_REFUNDING = 1;         // 退款中
    REFUND_STATUS_REFUND_ACCEPT = 2;     // 电竞指导接受退款
    REFUND_STATUS_REFUND_REJECT = 3;     // 电竞指导拒绝退款
    REFUND_STATUS_APPEALING = 4;         // 申诉中
    REFUND_STATUS_APPEALING_ACCEPT = 5;  // 申诉成功
    REFUND_STATUS_APPEALING_REJECT = 6;  // 申诉失败
}

message EvaluateInfo {
    int64 evaluate_time = 1;  // 评价时间戳（秒）
    repeated string word_list = 2; // 评价快捷词
    string content = 3; // 评价内容
    EvaluateScore score_info = 4; // 评价分数
    bool is_anonymous = 5; // 是否匿名
}

// 订单评价信息
message OrderEvaluateInfo {
    bool evaluate_entry = 1;       // 是否展示评价入口
    EvaluateInfo evaluate_info = 2; // 评价信息，为空则不展示
}

// 商品订单详情
message SkillProductOrderDetail {
    UserProfile player = 1;   // 玩家个人信息
    ProductOrder product_order = 2; // 商品订单信息
    string order_id = 3;      // 订单号
    uint32 status = 4;        // 订单状态 see OrderStatus
    uint32 sub_status = 5;    // 子状态 例如 CanceledOrderSubStatus
    int64 pay_time = 6;       // 下单时间
    int64 receive_time = 7;   // 接单时间
    int64 finish_time = 8;    // 完成时间
    int64 cancel_time = 9;    // 取消时间
    int64 order_end_time = 10;// 订单过期时间
    string status_desc = 11;  // 状态描述， 若是拒绝接单，则为原因
    string order_number = 12;  // 订单编号
    string order_remark = 13; // 订单备注
    bool is_notify_finish = 14; // 是否已提醒去完成
    int64 update_time = 15;   // 订单最近更新时间
    OrderEvaluateInfo evaluate = 16;    // 订单评价信息
}

enum PayOrderSource {
    PAY_ORDER_SOURCE_UNSPECIFIED = 0;
    PAY_ORDER_SOURCE_PERSONAL_PAGE = 1;// 个人主页下单
    PAY_ORDER_SOURCE_PRIVATE_CHAT = 2;//私聊页面主动下单
    PAY_ORDER_SOURCE_INVITE = 3; //通过邀请下单
    PAY_ORDER_SOURCE_IM_VISIT_CARD = 4; //IM页访问立即下单卡片下单
    PAY_ORDER_SOURCE_IM_FLOAT_WINDOW = 5; // 电竞助手推荐大神页下单

    PAY_ORDER_SOURCE_SKILL_DETAIL = 10;   // 技能详情页下单(与前端枚举值保持一致)
    PAY_ORDER_SOURCE_IM_FLOAT_WINDOW_TOP = 11; // 电竞专区消息半屏页，顶部下单按钮；
    PAY_ORDER_SOURCE_IM_FLOAT_WINDOW_IM_CARD = 12; // 电竞专区消息半屏页，消息卡片下单按钮；
    PAY_ORDER_SOURCE_IM_FLOAT_WINDOW_INVITE = 13; // 电竞专区消息半屏页，邀请下单消息下单按钮；
    PAY_ORDER_SOURCE_IM_CUSTOMER = 14; // 电竞客服推荐大神页下单
    PAY_ORDER_SOURCE_BACK_RECALL = 15; // 电竞新用户退出挽留弹窗
    PAY_ORDER_SOURCE_ONE_KEY_FIND_COACH = 16; // 一键找人下单
}

enum PayType {
    PAY_TYPE_UNSPECIFIED = 0; // 默认是 T豆
    PAY_TYPE_ALIPAY = 1; // 支付宝支付
    PAY_TYPE_WECHAT = 2; // 微信支付
}

// 玩家下单/支付
message PlayerPayOrderRequest {
    ga.BaseReq base_req = 1;
    uint64 product_id = 2;    // 商品id
    uint32 count = 3;
    uint32 total_price = 4;
    string order_remark = 5; // 订单备注
    uint32 invite_id = 6;    // 邀请下单的id
    uint32 source = 7;       // 下单来源 see PayOrderSource
    uint32 pay_type = 8; // 支付方式 see PayType
    string discount_info = 9; // 使用的优惠信息，是一个json，透传用于校验
}

message PlayerPayOrderResponse {
    ga.BaseResp base_resp = 1;
    uint64 balance = 2;         // T豆余额
    string hint_title = 3;      // 提示标题
    string hint_content = 4;    // 提示内容
}

// 玩家取消订单
message PlayerCancelOrderRequest {
    ga.BaseReq base_req = 1;
    string order_id = 2;
    string reason = 3;        // 原因
}

message PlayerCancelOrderResponse {
    ga.BaseResp base_resp = 1;
}

enum FinishEntranceSourceType {
    FINISH_ENTRANCE_SOURCE_TYPE_UNSPECIFIED = 0;
    FINISH_ENTRANCE_SOURCE_TYPE_ORDER_DETAIL = 1; // 订单详情页
    FINISH_ENTRANCE_SOURCE_TYPE_IM = 2; // IM页
    FINISH_ENTRANCE_SOURCE_TYPE_LIST = 3; // 订单列表
    FINISH_ENTRANCE_SOURCE_TYPE_IM_FLOAT = 4; // IM浮层
}

// 玩家确认完成订单
message PlayerFinishOrderRequest {
    ga.BaseReq base_req = 1;
    string order_id = 2;
    FinishEntranceSourceType entrance_source_type = 3;//
}

message PlayerFinishOrderResponse {
    ga.BaseResp base_resp = 1;
    bool evaluate_entry = 2;  // 是否拉起评论
    bool show_coupon_popup = 3; // 是否展示优惠券弹窗
    CouponPopupInfo coupon_popup_info = 4; // 优惠券弹窗信息
}

// 电竞指导接单
message CoachReceiveOrderRequest {
    ga.BaseReq base_req = 1;
    string order_id = 2;
}

message CoachReceiveOrderResponse {
    ga.BaseResp base_resp = 1;
}

// 电竞指导拒绝接单
message CoachRefuseOrderRequest {
    ga.BaseReq base_req = 1;
    string order_id = 2;
    string reason = 3;        // 原因
}

message CoachRefuseOrderResponse {
    ga.BaseResp base_resp = 1;
}

// 电竞指导提醒玩家去完成订单
message CoachNotifyFinishOrderRequest {
    ga.BaseReq base_req = 1;
    string order_id = 2;
}

message CoachNotifyFinishOrderResponse {
    ga.BaseResp base_resp = 1;
}


//退款类型
enum RefundType {
    REFUND_TYPE_FULL_UNSPECIFIED = 0; //全额退款
    REFUND_TYPE_PARTIAL = 1; //部分退款
}

// 退款途径
enum RefundWay {
    REFUND_WAY_NORMAL_UNSPECIFIED = 0;// 正常
    REFUND_WAY_FAST = 1;//急速
}

// 退款信息
message OrderRefund {
    string refund_id = 1;      // 退款id
    uint32 status = 2;         // 状态 包含退款中申诉中等等 RefundStatus
    RefundType refund_type = 3;    // 退款类型
    string refund_reason = 4;         // 退款原因
    uint32 refund_price = 5;   // 退款金额
    uint32 refund_count = 6;   // 退款数量
    string refund_desc = 7;           // 退款说明
    int64 end_time = 8;        // 该状态的截止时间
    bool can_appeal = 9;       // 能否申诉
    string appeal_desc = 10;// 申诉说明
    string refund_reject_reason = 11;// 退款拒绝原因
    string refund_reject_desc = 12;// 退款拒绝说明
    string appeal_reason = 13;// 申诉原因
    bool can_coach_upload_appeal = 14;// 能否上传申诉凭证
    string appeal_id = 15;// 申诉id
    int64 coach_upload_appeal_deadline = 16;// 电竞指导上传申诉凭证截止时间
    RefundWay refund_way = 17; // 退款途径
}

// 获取订单详情
message GetOrderDetailRequest {
    ga.BaseReq base_req = 1;
    string order_id = 2;
    //int64 pay_time = 3;       // 下单时间
}

message GetOrderDetailResponse {
    ga.BaseResp base_resp = 1;
    SkillProductOrderDetail order = 2;    // 订单信息
    OrderRefund refund = 3;         // 退款信息
    bool show_report_entrance = 4; // 是否展示举报入口
    string hint_text = 5; // 提示文案
}

// 订单简略信息
message OrderSimpleInfo {
    UserProfile player = 1;   // 玩家个人信息
    ProductOrder product_order = 2; // 商品订单信息
    string order_id = 3;      // 订单号
    uint32 status = 4;        // 订单状态 see OrderStatus
    uint32 sub_status = 5;    // 子状态 例如 ReceivedOrderSubStatus 、CanceledOrderSubStatus
    int64 pay_time = 6;       // 下单时间
    int64 order_end_time = 7; // 订单过期时间
    string offset_id = 8;     // 位置id
    string status_desc = 9;   // 描述
    bool is_notify_finish = 10; // 是否已提醒去完成
    bool can_coach_upload_appeal = 11;// 能否上传申诉凭证, 在退款申诉中时有效
    RefundType refund_type = 12;    // 退款类型， 在退款申诉中时有效
    int64 update_time = 13;
    bool evaluate_entry = 14;  // 是否展示评论入口
    bool can_appeal = 15;       // 能否申诉
    string appeal_id = 16;      // 申诉id
    string refund_id = 17;      // 退款id
    int64 coach_upload_appeal_deadline = 18;// 电竞指导上传申诉凭证截止时间
}

// 获取订单记录列表
message GetOrderListRequest {
    ga.BaseReq base_req = 1;

    enum OrderType {
        ORDER_TYPE_UNSPECIFIED = 0;
        ORDER_TYPE_PLAYER_ORDER = 1;    // 获取玩家订单记录
        ORDER_TYPE_COACH_ORDER = 2;     // 获取电竞指导接单记录
    }
    enum StatusType {
        STATUS_TYPE_UNSPECIFIED = 0;
        STATUS_TYPE_FINISHED = 1;    // 获取已完成的历史订单
        STATUS_TYPE_UNFINISHED = 2;  // 获取进行中的订单
    }

    uint32 order_type = 2;     // see OrderType
    uint32 status_type = 3;    // see StatusType
    string offset_id = 4;     // 上一页最后一条记录的位置id, 废弃
    uint32 limit = 5;         // 每页大小
    uint32 offset = 6;        // 偏移量，即从第几条开始取
}

message GetOrderListResponse {
    ga.BaseResp base_resp = 1;
    repeated OrderSimpleInfo order_list = 2;
}

// 删除订单
message DelOrderRecordRequest {
    ga.BaseReq base_req = 1;
    string order_id = 2;
}

message DelOrderRecordResponse {
    ga.BaseResp base_resp = 1;
}

// 获取原因文案列表
message GetReasonTextListRequest {
    ga.BaseReq base_req = 1;

    enum Type {
        TYPE_UNSPECIFIED = 0;
        TYPE_COACH_REFUSE_RECEIVE_ORDER = 1;  // 拒绝接单原因文案
        TYPE_COACH_REFUSE_RECEIVE_REFUND = 2;  // 拒绝退款原因文案
        TYPE_PLAYER_CANCEL_ORDER = 3;  // 玩家取消订单原因文案
        TYPE_COACH_INVITE_ORDER = 4; // 电竞指导邀请下单原因文案
    }
    uint32 type = 2; // see enum Type
}

message GetReasonTextListResponse {
    ga.BaseResp base_resp = 1;
    repeated string list = 2;
}

// 状态变更推送通知，收到通知后客户端主动请求更新
message OrderChangeNotify {
    string order_id = 1;
    int64 notify_time = 2;
}

message AcceptRefundRequest {
    ga.BaseReq base_req = 1;
    string refund_id = 2;// 退款id
}

message AcceptRefundResponse {
    ga.BaseResp base_resp = 1;
}

// 评价维度
enum EvaluateDimension {
    EVALUATE_DIMENSION_UNSPECIFIED = 0;
    EVALUATE_DIMENSION_SERVICE = 1; // 服务水平
    EVALUATE_DIMENSION_SKILL = 2;   // 技术水平
    EVALUATE_DIMENSION_VOICE = 3;   // 声音质感
}

message StarEvaluate {
    float score = 1;  // 分数，1-5代表1-5颗星
    uint32 dimension = 2; // 评价维度 SEE EvaluateDimension
}

// 评价快捷词
message EvaluateWord {
    repeated string words = 1;  // 评价快捷词
    StarEvaluate star = 2;      // 对应星级
}

// 获取评价快捷词列表
message GetEvaluateWordListRequest {
    ga.BaseReq base_req = 1;
    string order_id = 2;     // 订单号
}

message GetEvaluateWordListResponse {
    ga.BaseResp base_resp = 1;
    repeated EvaluateWord list = 2;
}

message EvaluateScore {
    float avg_score = 1;      // 平均分
    float service_score = 2;  // 服务水平
    float skill_score = 3;    // 技术水平
    float voice_score = 4;    // 声音质感
}

// 评价
message EvaluateRequest {
    ga.BaseReq base_req = 1;
    string order_id = 2; // 订单id
    repeated string word_list = 3; // 评价快捷词
    string content = 4; // 评价内容
    EvaluateScore score_info = 5; // 评价分数
    bool is_anonymous = 6; // 是否匿名
}

message EvaluateResponse {
    ga.BaseResp base_resp = 1;
    string toast = 2; // 提示
}
/*
//message EvaluateWordCnt {
//  string word = 1;
//  uint32 cnt = 2;
//}
//
//// 评价汇总
//message EvaluateSummary {
//  uint32 total_cnt = 1; // 评价总条数
//  EvaluateScore score_info = 2; // 评价分数
//  repeated EvaluateWordCnt word_cnt_list = 3; // 评价快捷词统计
//}
//
//message EvaluateInfo {
//  bool is_anonymous = 1; // 是否匿名
//  int64 evaluate_time = 2;  // 评价时间戳（秒）
//  repeated string word_list = 3; // 评价快捷词
//  string content = 4; // 评价内容
//  EvaluateScore score_info = 5; // 评价分数
//  string order_id = 6; // 订单id
//  UserProfile player = 7;   // 玩家个人信息
//  bool is_query_user = 8;  // 是否是查询人评价的
//}
//
//// 分页获取用户评价列表
//message GetEvaluateListRequest{
//  ga.BaseReq base_req = 1;
//  uint32 uid = 2;
//  string word = 3;
//  uint32 offset = 4;
//  uint32 limit = 5;
//}
//
//message GetEvaluateListResponse{
//  ga.BaseResp base_resp = 1;
//  EvaluateSummary summary = 2;  // 评价汇总， 仅当 offset为0且word为“全部” 时有效
//  repeated EvaluateInfo list = 3;
//}
*/
/******************** 订单相关 end ************************/

/****************** 电竞游戏名片IM消息 *****************/
// 消息类型见 im.proto ESPORT_GAME_CARD = 119
message EsportGameCardImMsg {
    enum GameCardStatus {
        GAME_CARD_STATUS_UNSPECIFIED = 0;
        GAME_CARD_STATUS_FILL = 1; // 填写
        GAME_CARD_STATUS_CHANGE = 2; // 修改
        GAME_CARD_STATUS_CHANGED = 3; // 已修改
    }
    string title = 1;  // 消息标题
    uint32 status = 2; // 卡片状态 see GameCardStatus
    EsportIMGameCardInfo card_info = 3; // 卡片信息
    bool is_sys_msg = 4; // 显示为系统消息
    string outer_text = 5; // 外显文案
}

message EsportIMGameCardInfo {
    string game_icon = 1; // 游戏图标
    string top_content = 2; // 顶部内容, 提示输入或第一项的内容
    repeated EsportIMGameCardInfoItem info_item_list = 3; // 信息项
    uint32 card_id = 4; // 游戏名片id
    uint32 game_id = 5; // 游戏id
}

message EsportIMGameCardInfoItem {
    string title = 1; // 标题
    string content = 2; // 内容
}
/****************** 电竞游戏名片IM消息 *****************/


/****************** 电竞游戏名片客户端接口 *****************/
message EsportGameCardInfo {
    string game_icon = 1; // 游戏图标
    string top_content = 2; // 顶部内容, 提示输入或第一项的内容
    repeated EsportGameCardInfoItem info_item_list = 3; // 信息项
    string background_img = 4; // 背景图片
    string background_color = 5; // 背景色值
    uint32 card_id = 6; // 卡片id
    uint32 game_id = 7; // 游戏id
    string game_name = 8; // 游戏名称
}

message EsportGameCardInfoItem {
    string title = 1; // 标题
    string content = 2; // 内容
    repeated string options = 3; // 选择类型的选项
    string tips = 4; // 默认文案, 提示填写
}

// 获取游戏名片配置信息
message GetEsportGameCardConfigRequest {
    ga.BaseReq base_req = 1;
    uint32 game_id = 2; // 游戏id
}

message GetEsportGameCardConfigResponse {
    ga.BaseResp base_resp = 1;
    repeated EsportGameCardInfoItem info_item_list = 2; // 信息配置项
    string game_name = 3; // 游戏名称
}

// 查询游戏名片信息
message GetEsportGameCardInfoRequest {
    ga.BaseReq base_req = 1;
    uint32 card_id = 2; // 游戏名片id
}

message GetEsportGameCardInfoResponse {
    ga.BaseResp base_resp = 1;
    repeated EsportGameCardInfoItem info_item_list = 2; // 信息项
    string game_name = 3; // 游戏名称
}

// 保存并发送(新增/修改)
message UpsertEsportGameCardInfoRequest {
    ga.BaseReq base_req = 1;
    uint32 game_id = 2; // 游戏id
    uint32 card_id = 3; // 游戏名片id
    repeated EsportGameCardInfoItem info_item_list = 4; // 信息项, options不用回传
    uint64 msg_id = 5; // 如果从im进入修改,携带对应的msg_id
    uint32 target_uid = 6; // 保存并发送时的目标uid
}

message UpsertEsportGameCardInfoResponse {
    ga.BaseResp base_resp = 1;
}

message GetEsportGameCardListRequest {
    ga.BaseReq base_req = 1;
}

message GetEsportGameCardListResponse {
    ga.BaseResp base_resp = 1;
    repeated EsportGameCardInfo card_list = 2; // 卡片列表
}

message SendEsportGameCardRequest {
    ga.BaseReq base_req = 1;
    uint32 card_id = 2; // 游戏id
    uint32 target_uid = 3; // 对方uid
}

message SendEsportGameCardResponse {
    ga.BaseResp base_resp = 1;
}

message DeleteEsportGameCardRequest {
    ga.BaseReq base_req = 1;
    uint32 card_id = 2; // 游戏名片id
}

message DeleteEsportGameCardResponse {
    ga.BaseResp base_resp = 1;
}

// 获取可申请游戏名片的游戏
message GetGameCardGameRequest {
    ga.BaseReq base_req = 1;
}

message GetGameCardGameResponse {
    ga.BaseResp base_resp = 1;
    repeated GameItem mobile_game_list = 2;
    repeated GameItem pc_game_list = 3;
}
/****************** 电竞游戏名片客户端接口 *****************/

/****************** 用户进入大神私信页面IM相关 *****************/
// 用户从游戏详情卡进入大神私信页面上报  (废弃，h5 上报)
message EnterEsportIMPageReportRequest {
    ga.BaseReq base_req = 1;
    uint32 coach_uid = 2; // 大神uid
    uint32 game_id = 3; // 游戏id
}

message EnterEsportIMPageReportResponse {
    ga.BaseResp base_resp = 1;
}

message EsportIMGameSelfIntroItem {
    string title = 1; // 标题
    string content = 2; // 内容
}

// 大神简单自我介绍IM卡片消息
message EsportCoachSelfIntroIMMsg {
    string title = 1; // 标题

    string text_intro = 2;  // 文字介绍
    string audio_intro = 3; // 音频介绍资源链接
    uint32 audio_duration = 4; // 音频时长
    repeated EsportIMGameSelfIntroItem item_list = 5; // 内容
    string guarantee_win_text = 6; // 包赢文案(包赢段位文案), 为""则不展示包赢样式

    uint64 product_id = 7; // 快速下单商品id
    string outer_text = 8; // 外显文案

    uint32 game_id = 9; // 游戏id
    uint64 expire_ts = 10;    // 可下单的过期时间，为0则不过期

    ga.UserProfile coach_profile = 11; // 大神个人信息
}


// IM页大神信息tag
message GetChatListEsportTagsRequest {
    ga.BaseReq base_req = 1;
    repeated string account_list = 2;    // account列表
}

// 聊天页大神标签
message ChatListEsportTags {
    bool is_coach = 1;               // 是否是大神身份
    bool quick_receive = 2;          // 是否开启秒接单
}

message GetChatListEsportTagsResponse {
    ga.BaseResp base_resp = 1;
    map<string, ChatListEsportTags> tag_map = 2; // key:account
    uint32 refresh_count = 3; // 刷新人数
}

// ===================================== 电竞快捷回复 =====================================

enum QuickReplyType {
    QUICK_REPLY_TYPE_UNSPECIFIED = 0;
    QUICK_REPLY_TYPE_COACH = 1; // 大神
    QUICK_REPLY_TYPE_PLAYER = 2; // 玩家
}

enum ScreenAreaType {
    SCREEN_AREA_TYPE_UNSPECIFIED = 0; // 旧版本0值默认全屏，新版本不传0
    SCREEN_AREA_TYPE_FULL = 1; // 全屏
    SCREEN_AREA_TYPE_HALF = 2; // 半屏
    SCREEN_AREA_TYPE_OTHER = 3; // 其他 预留

}

// 获取电竞快捷回复列表
message GetEsportQuickReplyListRequest {
    ga.BaseReq base_req = 1;
    uint32 type = 2; // 快捷回复类型 see QuickReplyType
    uint32 screen_area_type = 3; // 展示区域 see ScreenAreaType
}


message GetEsportQuickReplyListResponse {
    ga.BaseResp base_resp = 1;
    repeated string replay_list = 2; // 快捷回复列表
}

// 上报快捷回复
message ReportQuickReplyRequest {
    ga.BaseReq base_req = 1;
    uint32 type = 2; // 快捷回复类型 see QuickReplyType
    string content = 3; // 快捷回复内容
    uint32 target_uid = 4; // 快捷回复对象 uid
    uint32 screen_area_type = 5; // 展示区域 see ScreenAreaType
}

// 上报快捷回复响应
message ReportQuickReplyResponse {
    ga.BaseResp base_resp = 1;
}

message EstimateOrderTotalPriceRequest {
    ga.BaseReq base_req = 1;
    uint32 player_uid = 2;
    uint32 coach_uid = 3;
    uint64 product_id = 4;
    uint32 min_amount = 5; // 最小数量
    uint32 max_amount = 6; // 最大数量
    repeated string chosen_coupons = 7; // 选中的优惠券列表，目前单选
}

message EstimateOrderTotalPriceResponse {
    ga.BaseResp base_resp = 1;
    repeated TotalPriceInfo price_list = 2;
    string enter_page_toast = 3; // 进入页面时的提示，空则表示无提示
    bool show_coupon_entrance = 4; // 是否显示优惠券入口
}

message TotalPriceInfo {
    message RedHint {
        string red_hint_str = 1;
        uint32 red_hint_cond = 2; // 展示条件，参见RedHintCond
    }
    enum RedHintCond {
        RED_HINT_COND_UNSPECIFIED = 0; // 不展示
        RED_HINT_COND_SHOW = 1; // 展示
    }
    message ToastHint {
        string toast_hint_str = 1;
        uint32 toast_hint_cond = 2; // 展示条件，参见ToastHintCond
    }
    enum ToastHintCond {
        TOAST_HINT_COND_UNSPECIFIED = 0; // 不展示
        TOAST_HINT_COND_SHOW = 1; // 展示
        TOAST_HINT_COND_ADD_AMOUNT = 2; // 只在增加数量时展示
    }
    enum CouponHintStyle {
        COUPON_HINT_STYLE_UNSPECIFIED = 0; // 未指定
        COUPON_HINT_STYLE_GRAY = 1;
        COUPON_HINT_STYLE_NORMAL = 2;
        COUPON_HINT_STYLE_RED = 3;
    }
    message CouponHint {
        string coupon_hint_str = 1; // 优惠券提示文案
        uint32 coupon_hint_style = 2; // 优惠券提示文案样式，参见CouponHintStyle
        repeated string chosen_coupons = 3; // 选中的优惠券列表，目前单选
        bool can_enter_chosen_page = 4; // 是否能进入优惠券选择页
    }
    uint32 amount = 1; // 数量
    uint32 total_price = 2; // 总价
    string discount_info = 3; // 优惠信息，是一个json，透传给下单和邀请下单接口
    RedHint red_hint = 4; // 下方的红色提示
    ToastHint toast_hint = 5; // toast提示
    uint32 coupon_price = 6; // 优惠券抵扣金额, deprecated since v6.54.5
    string coupon_reason = 7;// 不能使用优惠券的提示，当优惠金额为0时, deprecated since v6.54.5
    CouponHint coupon_hint = 8;
}

// 用json序列化为discount_info字段
message OrderDiscountInfo {
    bool use_first_round_discount = 1; // 是否使用首局优惠
    repeated string coupon_ids = 2; // 订单使用的优惠券。预留数组方便扩展，虽然当前只能用1张
    bool use_new_customer_discount = 3; // 是否使用新客优惠
}

// 联系客服
message ContactCustomerServiceRequest {
    ga.BaseReq base_req = 1;
    uint32 coach_id = 2; // 大神ID
    uint32 game_id = 3; // 游戏id
}
// 联系客服响应
message ContactCustomerServiceResponse {
    ga.BaseResp base_resp = 1;
    UserProfile customer_info = 2; // 客服个人信息
}

message IsUserACustomerRequest {
    ga.BaseReq base_req = 1;
    uint32 uid = 2;//
}

message IsUserACustomerResponse {
    ga.BaseResp base_resp = 1;
    bool is_customer = 2;
    bool can_visit_order_entrance = 3;// 是否展示公会订单列表
}

// 获取ugc房推荐列表的入口请求消息
message GetUGCReListEntRequest {
    ga.BaseReq base_req = 1;
    uint32 cid = 2;// 房间id
}

// 获取ugc房推荐列表的入口响应消息
message GetUGCReListEntResponse {
    ga.BaseResp base_resp = 1;
    bool is_visible = 2; // 入口是否可见
}

// 获取ugc房推荐大神卡信息请求消息
message GetUGCReCoachCardInfoRequest {
    ga.BaseReq base_req = 1;
    uint32 cid = 2; // 房间id
}

// 获取ugc房推荐大神卡信息响应消息
message GetUGCReCoachCardInfoResponse {
    ga.BaseResp base_resp = 1;
    EsportAreaCoachInfo gods = 2; // 推荐的大神信息，可能为空
}

enum NoMoreReOnUGCOperation {
    NO_MORE_RE_ON_UGC_OPERATION_UNSPECIFIED = 0; // 非法值
    NO_MORE_RE_ON_UGC_OPERATION_REMOVE = 1; // 不再弹出该弹窗
    NO_MORE_RE_ON_UGC_OPERATION_UNLIKE = 2; // 不喜欢该大神
}

// 更新不再推荐状态请求消息
message NoMoreReOnUGCRequest {
    ga.BaseReq base_req = 1;
    uint32 coach_id = 2; // 大神ID
    uint32 cid = 3; // 房间id
    NoMoreReOnUGCOperation operation = 4; // 操作
}

// 更新不再推荐状态响应消息
message NoMoreReOnUGCResponse {
    ga.BaseResp base_resp = 1;
}


// 获取推荐大神列表请求消息
message GetRecommendedGodListRequest {
    ga.BaseReq base_req = 1;
    uint32 cid = 2;// 房间id
}

// 获取推荐大神列表响应消息
message GetRecommendedGodListResponse {
    ga.BaseResp base_resp = 1;
    repeated EsportAreaCoachInfo gods = 2; // 推荐的大神列表
}


// ================================== 优惠卡 =======================================
// 获取优惠券封面弹窗
message GainCouponCover {
    uint32 auto_open_second = 1; // 自动打开时间，单位秒
    string waiting_resource = 2; // 等待时资源
    string opening_resource = 3; // 打开时资源
    string opening_md5 = 4; // 打开时资源md5
}

// 优惠券弹窗
message CouponPopupInfo {
    enum CouponPopupType {
        COUPON_POPUP_TYPE_UNSPECIFIED = 0;
        COUPON_POPUP_TYPE_GAIN_COVER = 1; // 获取优惠券封面
        COUPON_POPUP_TYPE_SHOW_REMAIN = 2; // 展示剩余优惠券
    }
    CouponPopupType type = 1; // 弹窗类型
    GainCouponCover gain_coupon_cover = 2; // 获取优惠券封面
    string remain_coupon = 3; // 优惠券(富文本, 规范见下方信息项协议定义及备注模板描述)
}

message ShowCouponPopupInfo {
    message CouponItem {
        string coupon_desc = 1; // 优惠券描述, 如: 优惠500豆
        string coupon_sub_desc = 2; // 优惠券副描述
        string coupon_name = 3; // 优惠券名称
        string coupon_limit_text = 4; // 优惠券使用限制文案
        string coupon_expiration = 5; // 优惠券有效期
        string bg_img = 6; // 背景图
        string corner_img = 7; // 角标图
        string corner_text = 8; // 角标文案

    }
    string title = 1; // 标题
    string sub_title = 2; // 副标题
    string btn_text = 3; // 按钮文案
    string bg_img = 4; // 背景图
    string coupon_info = 5; // 优惠券信息
    repeated CouponItem coupon_list = 6; // 优惠券列表
}
/*
模板实例:
主模板:
<div bg={{.BgImg}}>
    ...
    <div>{{.CouponInfo}}</div> // 可变的优惠券信息项
    ...
</div>
信息项模板:
<div>
    <div>{{.CouponDesc}}</div>
    <div>{{.CouponSubDesc}}</div>
    ...
</div>
 */


message GainCouponRequest {
    ga.BaseReq base_req = 1;
}

message GainCouponResponse {
    ga.BaseResp base_resp = 1;
    string coupon_info = 2; // 领取到的优惠券(富文本)
}

message LoginAppShowCouponRemainRequest {
    ga.BaseReq base_req = 1;
}

message LoginAppShowCouponRemainResponse {
    ga.BaseResp base_resp = 1;
    string coupon_info = 2; // 优惠券(富文本)
}

message ShowManualGrantCouponRequest {
    ga.BaseReq base_req = 1;
}

message ShowManualGrantCouponResponse {
    ga.BaseResp base_resp = 1;
    string coupon_info = 2; // 优惠券(富文本)
}

message MarkManualGrantCouponReadRequest {
    ga.BaseReq base_req = 1;
}

message MarkManualGrantCouponReadResponse {
    ga.BaseResp base_resp = 1;
}

message GetCouponEntranceInfoRequest {
    ga.BaseReq base_req = 1;
}

message GetCouponEntranceInfoResponse {
    ga.BaseResp base_resp = 1;
    bool show_coupon_entrance = 2; // 是否显示优惠券入口
    string coupon_expiring_text = 3; // 即将过期的优惠券文案，空则不展示
    string coupon_unused_text = 4; // 未使用的优惠券文案，空则不展示
}

message GetHomeCouponEntranceInfoRequest {
    ga.BaseReq base_req = 1;
}

message GetHomeCouponEntranceInfoResponse {
    ga.BaseResp base_resp = 1;
    string coupon_corner_img = 2; // 优惠券角标图片，空则不展示
    string coupon_bubble_text = 3; // 优惠券气泡文案，空则不展示
    bool show_coupon_bubble_forever = 4; // 是否一直显示优惠券气泡
    uint32 today_avaliable_coupon_value = 5; // 今日可用优惠券面值，单位元，0代表无可用
    int64 check_ts = 6; // 时间戳，单位秒。到点重新请求，0则不用到点重新请求
}

message GetEsportAreaTopBannerListRequest {
    ga.BaseReq base_req = 1;
}

message GetEsportAreaTopBannerListResponse {
    ga.BaseResp base_resp = 1;
    repeated TopBannerInfo banner_list = 2; // 顶部banner列表
}

message TopBannerInfo {
    string title = 1; // 标题
    string sub_title = 2; // 副标题
    uint32 sort = 3; // 排序
    string background_icon = 5; // 底图icon
    string operation_icon = 6; // 运营icon
    string jump_link = 7; // 跳转链接
    string background_img = 8; // 背景图
    string background_img_small = 9; // 小背景图
    string collapse_item_bg = 10; // 收起状态的背景图
}

// 在券状态变更时推送，不包含券 生效/过期
message CouponChangePushMsg {
}

message GetEsportCoachMissionInfoRequest {
    ga.BaseReq base_req = 1;
}

message GetEsportCoachMissionInfoResponse {
    ga.BaseResp base_resp = 1;
    string button_lottie_url = 2; // 按钮lottie动画url
    string button_jump_link = 3; // 按钮跳转链接
    string first_enter_tips = 4; // 今日首次进入文案
    string new_visitor_tips = 5; // 有未查看客户访问文案
    bool has_new_visitor = 6; // 是否有未查看客户访问
    string button_lottie_md5 = 7; // 按钮lottie动画md5
    string last_finished_mission_id = 8;// 上次完成的任务id
}



// 获取电竞专区技能商品列表
message GetBackRecallReCoachRequest {
    ga.BaseReq base_req = 1;
}

message GetBackRecallReCoachResponse {
    ga.BaseResp base_resp = 1;
    EsportAreaCoachInfo coach = 2; // 教练
    bool switch = 3; // 是否展示
    bool auto_play_audio = 4;// 是否自动播放音频
    string title = 5; // 标题
}


message GetNewCustomerTabSettingRequest {
    ga.BaseReq base_req = 1;

}

message GetNewCustomerTabSettingResponse {
    message GameList {
        repeated GameItem item_list = 1; // 游戏列表
    }
    message Video {
        string url = 1;//视频地址
        string md5 = 2;//视频md5
    }
    ga.BaseResp base_resp = 1;
    map<int32, Video> videos = 2;//视频列表，key 是性别，和用户账号的值一致
    map<int32, GameList> games = 3;//游戏列表, key是 EsportGameType
}

message PostNewCustomerTabSettingRequest {
    ga.BaseReq base_req = 1;
    repeated uint32 game_ids = 2; // 前置的游戏id
}

message PostNewCustomerTabSettingResponse {
    ga.BaseResp base_resp = 1;
}

// 一键找人 //////////////////////////////////////////

// 一键找人入口
message GetOneKeyFindCoachEntryRequest {
    ga.BaseReq base_req = 1;
}

message GetOneKeyFindCoachEntryResponse {
    ga.BaseResp base_resp = 1;
    bool show_entry = 2;
    string entry_icon = 3; // 入口图标
    string entry_title = 4; // 入口标题
    string entry_desc = 5; // 入口描述
    string entry_small_icon = 6; // 入口小图标
    int64 heartbeat_interval = 7; // 心跳间隔，单位秒。0则不发心跳
}

// 获取一键找人发布配置
message GetOneKeyPublishCfgRequest {
  ga.BaseReq base_req = 1;
  uint32 game_id = 2;
}

enum EsportOneKeyFindParamType {
    ESPORT_ONE_KEY_FIND_PARAM_TYPE_UNSPECIFIED = 0;
    ESPORT_ONE_KEY_FIND_PARAM_TYPE_SINGLE_TAG = 1[deprecated=true]; // 单选, deprecated
    ESPORT_ONE_KEY_FIND_PARAM_TYPE_MULTI_TAG = 2[deprecated=true]; // 多选, deprecated
    ESPORT_ONE_KEY_FIND_PARAM_TYPE_DIALOG_SELECT = 3; // 弹框选择
    ESPORT_ONE_KEY_FIND_PARAM_TYPE_TEXT = 4; // 文本
    ESPORT_ONE_KEY_FIND_PARAM_TYPE_SELECT_TAG = 5; // 选择标签
}

// 定义 Text 消息类型
message EsportOneKeyText {
    string tips = 1;
    uint32 text_max_size = 2;
}

// deprecated
message EsportOneKeySingleTag {
    repeated string tags = 1;
    uint32 default_select = 2; // 默认选中的tag, 从0开始
}

// deprecated 定义 MultiTag 消息类型
message EsportOneKeyMultiTag {
    repeated string tags = 1;
    uint32 min_select = 2; // 0表示不限制
    uint32 max_select = 3; // 0表示不限制
    repeated uint32 default_select_list = 4; // 默认选中的tag, 从0开始
}

message EsportOneKeySelectTag {
    repeated string tags = 1;
    uint32 min_select = 2; // 0表示不限制
    uint32 max_select = 3; // 0表示不限制
    repeated uint32 default_select_index_list = 4; // 默认选中的tag, 从0开始,传空则不默认选中
}

// 定义 DialogSelect 消息类型
message EsportOneKeyDialogSelect {
    string title = 1;
    string tips = 2;
    repeated EsportOneKeyDialogSelectList list = 3;
    // 用于游戏名片信息预填， 列表为空说明用户尚未创建该游戏的游戏卡
    repeated EsportIMGameCardInfoItem info_item_list = 4; // 信息项 (游戏区服、段位信息)
}

// 定义 DialogSelectList 消息类型
message EsportOneKeyDialogSelectList {
    string title = 1;
    repeated string data = 2;
}

message EsportOneKeyFindParamItem {
    uint32 param_type = 1; // see EsportOneKeyFindParamType, 是哪个类型，则对应类型有值
    string title = 2; // 标题（性别，要求，更多要求，我的信息...）
    EsportOneKeyText text = 3;
    EsportOneKeySingleTag single_tag = 4[deprecated=true];
    EsportOneKeyMultiTag multi_tag = 5[deprecated=true];
    EsportOneKeyDialogSelect dialog_select = 6;
    EsportOneKeySelectTag select_tag = 7;
}

// 获取一键找人发布配置响应
message GetOneKeyPublishCfgResponse {
    ga.BaseResp base_resp = 1;
    repeated EsportOneKeyFindParamItem item_list = 2;
    // 用于游戏名片信息预填， 列表为空说明用户尚未创建该游戏的游戏卡
    repeated EsportIMGameCardInfoItem info_item_list = 3[deprecated=true]; // 信息项 (游戏区服、段位信息)
}

// 发布一键找人需求
message PublishOneKeyFindCoachRequest {
    ga.BaseReq base_req = 1;
    uint32 game_id = 2; // 游戏id
    repeated EsportOneKeyFindParamItem property_list = 3; // 发布要求列表
}

message PublishOneKeyFindCoachResponse {
    ga.BaseResp base_resp = 1;
    string publish_id = 2;
    repeated string cancel_reason_list = 3 [deprecated = true]; // 取消原因列表
}

// 取消一键找人需求
message CancelOneKeyFindCoachRequest {
    ga.BaseReq base_req = 1;
    string publish_id = 2;
    string cancel_reason = 3; // 取消原因
}

message CancelOneKeyFindCoachResponse {
    ga.BaseResp base_resp = 1;
}

// 置顶一键找人需求，划到最后时请求
message StickOneKeyFindCoachRequest {
    ga.BaseReq base_req = 1;
    string publish_id = 2;
}

message StickOneKeyFindCoachResponse {
    ga.BaseResp base_resp = 1;
}


// 拉，用于杀app重进时拉取
// 获取正在进行中的一键找人
message GetGoingOneKeyFindCoachRequest {
    ga.BaseReq base_req = 1;
}

message GetGoingOneKeyFindCoachResponse {
    ga.BaseResp base_resp = 1;
    string publish_id = 2; // 当前没有则返回空
    int64 expire_ts = 3; // 过期时间戳，单位秒。用于倒计时
    repeated string head_list = 4[deprecated=true]; // 头像md5列表，用于没人接单时，随机播放
    repeated GrabOrderCoachInfo grab_list = 6; // 抢单列表
    int64 publish_ts = 7; // 发布时间戳，单位秒
    repeated string cancel_reason_list = 8[deprecated=true]; // 取消原因列表
}

message GrabOrderCoachInfo {
    EsportAreaCoachInfo coach = 1;
    uint32 grab_type = 2; // 参考 EsportOneKeyGrabType
    string grab_text = 3;
    string grab_audio = 4;
    uint32 grab_audio_duration = 5;
    int64 grab_ts = 6; // 抢单时间戳，单位秒
}
enum EsportOneKeyGrabType {
    ESPORT_ONE_KEY_GRAB_TYPE_UNSPECIFIED = 0;
    ESPORT_ONE_KEY_GRAB_TYPE_TEXT = 1;
    ESPORT_ONE_KEY_GRAB_TYPE_AUDIO = 2;
}

// 推，用于实时知道大神抢单了，但收到推送后还是去拉
// 一键找人 抢单 通知
message OneKeyFindCoachNotify {
    enum EsportOneKeyEventType {
        ESPORT_ONE_KEY_EVENT_TYPE_UNSPECIFIED = 0;
        ESPORT_ONE_KEY_EVENT_TYPE_GRAB_ORDER = 1; // 有人抢单
        ESPORT_ONE_KEY_EVENT_TYPE_TIMEOUT_NO_GRAB = 2; // 订单超时（没人抢单）
        ESPORT_ONE_KEY_EVENT_TYPE_TIMEOUT_HAS_GRAB = 3; // 订单超时（有人抢单）
    }
    string publish_id = 1;
    EsportOneKeyEventType event_type = 2;
}

// 上报用户点击聊一聊
message EsportReportClickImRequest {
    enum EsportOneKeyTriggerMode {
        ESPORT_ONE_KEY_TRIGGER_MODE_UNSPECIFIED = 0;
        ESPORT_ONE_KEY_TRIGGER_MODE_CLICK = 1; // 点击按钮
        ESPORT_ONE_KEY_TRIGGER_MODE_STAT = 2; // 看超过10s或超过3次
    }
    ga.BaseReq base_req = 1;
    uint32 coach_id = 2;
    uint32 game_id = 3;
    EsportOneKeyTriggerMode trigger_mode = 4; // 触发方式
}
message EsportReportClickImResponse {
    ga.BaseResp base_resp = 1;
}

// 用于维护在专区中的用户，便于后续推送
// 电竞专区/电竞tab 心跳，心跳间隔查找上面 heatbeat_interval
message EsportRegionHeartbeatRequest {
    ga.BaseReq base_req = 1;
    AreaCoachListFromSource from_source = 2; // 区分 电竞专区/电竞tab
}
message EsportRegionHeartbeatResponse {
    ga.BaseResp base_resp = 1;
}

// 一键找人 成功 通知
message OneKeyFindCoachSuccessNotify {
    string content = 1; // 富文本公告，包含头像昵称
    uint32 rolling_time = 2; //滚动时长 单位秒
}

message CheckIfCanPublishOneKeyFindCoachRequest {
    ga.BaseReq base_req = 1;
}

message CheckIfCanPublishOneKeyFindCoachResponse {
    ga.BaseResp base_resp = 1;
    string reason = 2; // 不能发布的原因，空则可以发布
}

// 登陆时获取全局一键找人配置
message GetGlobalOneKeyFindCfgRequest {
    ga.BaseReq base_req = 1;
}

enum OneKeyFindLottieType {
    ONE_KEY_FIND_LOTTIE_TYPE_UNSPECIFIED = 0;
    ONE_KEY_FIND_LOTTIE_TYPE_WAITING = 1; // 等待
    ONE_KEY_FIND_LOTTIE_TYPE_FLOAT = 2; // 漂浮
    ONE_KEY_FIND_LOTTIE_TYPE_SCAN_HEAD = 3; // 默认头像动画
}

message OneKeyFindLottie {
    uint32 type = 1; // see OneKeyFindLottieType
    string url = 2;
    string md5 = 3;
}

message GetGlobalOneKeyFindCfgResponse {
    ga.BaseResp base_resp = 1;
    repeated string cancel_reason_list = 2; // 取消原因列表
    repeated string fake_head_list = 3; // 假头像列表
    repeated  OneKeyFindLottie lottie_list = 4; // lottie动画列表
    uint32 max_grab_user_cnt = 5; // 最大抢单人数
    uint32 report_click_im_require_second = 6; // 上报点击IM 需要达到的秒数
    uint32 report_click_im_require_count = 7; // 上报点击IM 需要达到的次数
}
// 一键找人 end //////////////////////////////////////////

