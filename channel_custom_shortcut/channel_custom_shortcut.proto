syntax = "proto3";
package ga.channel_custom_shortcut;

import "ga_base.proto";

option go_package = "golang.52tt.com/protocol/app/channel_custom_shortcut";
option java_package = "com.yiyou.ga.model.proto";
option objc_class_prefix = "RPC";

enum ChannelCustomShortcutType {
  // 未定义
  CHANNEL_CUSTOM_SHORTCUT_TYPE_UNSPECIFIED = 0;
  // 锁房
  CHANNEL_CUSTOM_SHORTCUT_TYPE_LOCK_CHANNEL = 1;
  // 一键清屏
  CHANNEL_CUSTOM_SHORTCUT_TYPE_CLEAR_PUBLIC_SCREEN = 2;
  // 自动锁麦
  CHANNEL_CUSTOM_SHORTCUT_TYPE_AUTO_LOCK_MIC = 3;
  // 一键闭麦
  CHANNEL_CUSTOM_SHORTCUT_TYPE_MUTE_ALL_MIC = 4;
  // 麦位名称
  CHANNEL_CUSTOM_SHORTCUT_TYPE_MIC_NAME = 5;
  // 关闭公屏
  CHANNEL_CUSTOM_SHORTCUT_TYPE_CLOSE_PUBLIC_SCREEN = 6;
  // 房间详情
  CHANNEL_CUSTOM_SHORTCUT_TYPE_CHANNEL_DETAIL = 7;
  // 恢复开麦
  CHANNEL_CUSTOM_SHORTCUT_TYPE_RECOVER_ALL_MIC = 8;
  // 一键锁麦
  CHANNEL_CUSTOM_SHORTCUT_TYPE_LOCK_ALL_MIC = 9;
  // 麦位音量护盾
  CHANNEL_CUSTOM_SHORTCUT_TYPE_MIC_VOLUME_SHIELD = 10;
}

message GetUserCustomShortcutsRequest {
  ga.BaseReq base_req = 1;
}
message GetUserCustomShortcutsResponse {
  ga.BaseResp base_resp = 1;
  // 房间快捷方式列表
  repeated ChannelCustomShortcutType shortcut_list = 2;
}

message SetUserCustomShortcutsRequest {
  ga.BaseReq base_req = 1;
  // 房间快捷方式列表
  repeated ChannelCustomShortcutType shortcut_list = 2;
}
message SetUserCustomShortcutsResponse {
  ga.BaseResp base_resp = 1;
}