package conf

//go:generate quicksilver-cli test interface ../conf
//go:generate mockgen -destination=./mocks/business_config.go -package=mocks golang.52tt.com/services/channel-wedding-conf/internal/conf IBusinessConfManager

import (
	"crypto/md5"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"os"
	"sort"
	"time"

	"gitlab.ttyuyin.com/tt-infra/tyr/log"
)

const (
	BusinessConfPath = "/data/oss/conf-center/tt/"
	BusinessConfFile = "channel-wedding-conf.json"
)

var LastConfMd5Sum [md5.Size]byte

type BusinessConf struct {
	WeddingThemeCfgList    []*WeddingThemeCfg `json:"wedding_theme_cfg_list"`    // 婚礼主题配置列表
	ThemeTitleSelectedIcon string             `json:"theme_title_selected_icon"` // 选中图标
	ThemeTitleBgIcon       string             `json:"theme_title_bg_icon"`       // 主题标题背景图标

	ThemeRankBgMap map[uint32][]*ThemeRankBg `json:"theme_rank_bg_map"` // 婚礼榜单主题等级背景图标
}

type ThemeRankBg struct {
	Level  uint32 `json:"level"`
	BgIcon string `json:"bg_icon"`
}

type WeddingThemeCfg struct {
	ThemeId                     uint32             `json:"theme_id"`                       // 主题ID
	Name                        string             `json:"name"`                           // 主题名称
	Icon                        string             `json:"icon"`                           // 主题icon
	PriceInfo                   *PriceInfo         `json:"price_info"`                     // 价格信息
	RoomResourceCfg             *ResourceCfg       `json:"room_resource_cfg"`              // 房间主题资源
	SceneCfgList                []*WeddingSceneCfg `json:"scene_cfg_list"`                 // 场景配置
	LevelCfgList                []*WeddingLevelCfg `json:"level_cfg_list"`                 // 等级婚礼资源配置
	PreviewResource             *ResourceCfg       `json:"preview_resource"`               // 婚礼预告资源
	MemorialVideo               *ResourceCfg       `json:"memorial_video"`                 // 纪念视频
	ChairResConf                *ChairGameResConf  `json:"chair_res_conf"`                 // 椅子游戏资源配置
	ExamplePhoto                string             `json:"example_photo"`                  // 示例图片
	SelectedThemeTitleIcon      string             `json:"selected_theme_title_icon"`      // 选中主题标题图标
	UnselectedThemeTitleIcon    string             `json:"unselected_theme_title_icon"`    // 未选中主题标题图标
	ThemeBackground             string             `json:"theme_background"`               // 主题背景
	ThemePreviewText            string             `json:"theme_preview_text"`             // 主题预览文案
	RewardInfoDesc              string             `json:"reward_info_desc"`
	RewardInfoList              []*RewardPreview   `json:"reward_info_list"`               // 奖励信息列表
	WeddingHallComingBackground string             `json:"wedding_hall_coming_background"` // 仪式大厅未开始的背景图片
	MailLadyLeftBgIcon          string             `json:"mail_lady_left_bg_icon"`         // 信件女在左边背景图标
	MailLadyRightBgIcon         string             `json:"mail_lady_right_bg_icon"`        // 信件女在右边背景图标
	Rank                        uint32             `json:"rank"`                           // 排序
	IsDelete                    bool               `json:"is_delete"`                      // 是否下架
}

type RewardInfo struct {
	Level          uint32 `json:"level"`            // 婚礼等级
	RewardType     uint32 `json:"reward_type"`      // 奖励类型
	RewardId       string `json:"reward_id"`        // 当奖励不区分性别时，rewardId与RewardIdFemale相等; 当奖励区分性别时，代表男性奖励id
	RewardIdFemale string `json:"reward_id_Female"` // 女性奖励id
	Amount         uint32 `json:"amount"`           // 数量

	LvName string `json:"lv_name"` // 等级名称
}

type RewardPreview struct {
	Png        string `json:"png"`         // 图片地址
	TopText    string `json:"top_text"`    // 顶部文案
	BottomText string `json:"bottom_text"` //

	RewardList []*RewardInfo `json:"reward_list"`
}

type PriceInfo struct {
	Type            uint32       `json:"type"`              // 价格类型
	Price           uint32       `json:"price"`             // 价格
	NormalTimePrice []*PriceItem `json:"normal_time_price"` // 普通时段价格
	HotTimePrice    []*PriceItem `json:"hot_time_price"`    // 热门时段价格
}

type PriceItem struct {
	Price  uint32 `json:"price"`
	GiftId uint32 `json:"gift_id"`
}

type SceneBoneCfg struct {
	Level         uint32 `json:"level"`          // 等级
	SeqIndex      uint32 `json:"seq_index"`      // 分镜顺序号
	AnimationName string `json:"animation_name"` // 动画名称
	BoneId        uint32 `json:"bone_id"`        // 骨骼资源ID
	BaseBoneId    uint32 `json:"base_bone_id"`   // 基础骨骼资源ID
}

type WeddingSceneCfg struct {
	SceneId        uint32          `json:"scene_id"`          // 场景ID
	Name           string          `json:"name"`              // 场景名称
	Icon           string          `json:"icon"`              // 场景icon
	ResourceCfg    *ResourceCfg    `json:"resource_cfg"`      // 场景资源
	BoneCfgList    []*SceneBoneCfg `json:"bone_cfg_list"`     // 场景骨骼资源配置
	ClipImSmallPic string          `json:"clip_im_small_pic"` // 场景片段im小图
	ClipDefaultPic string          `json:"clip_default_pic"`  // 沉淀场景片段缺省图
}

type PreviewCfg struct {
	CfgType      uint32 `json:"cfg_type"`
	AnimationUrl string `json:"animation_url"`
	AnimationMd5 string `json:"animation_md5"`
	AnimationPng string `json:"animation_png"`
}

type GuestDressCfg struct {
	GuestType              uint32          `json:"guest_type"`                // 嘉宾类型
	Desc                   string          `json:"desc"`                      // 描述
	SuitCfg                *WeddingSuitCfg `json:"suit_cfg"`                  // 婚礼套装
	DressText              string          `json:"dress_text"`                // 婚礼套装文案
	DressPreview           string          `json:"dress_preview"`             // 婚礼套装预览
	DressPreviewSmallIcon  string          `json:"dress_preview_small_icon"`  // 婚礼套装预览小图标
	ClothesUpgradePopupPng string          `json:"clothes_upgrade_popup_png"` // 服装升级弹窗图片
}

type WeddingSuitCfg struct {
	Name       string   `json:"name"`        // 套装名称
	Icon       string   `json:"icon"`        // 套装icon
	ClothesIds []uint32 `json:"clothes_ids"` // 套装物品id列表
	Duration   uint32   `json:"duration"`    // 时长, 单位秒
}

type WeddingLevelCfg struct {
	Level                             uint32           `json:"level"`                // 等级
	RoomBackgroundPic                 string           `json:"room_background_pic"`  // 背景图片
	RoomBackgroundMp4                 string           `json:"room_background_mp4"`  // 背景视频
	GuestDressCfgList                 []*GuestDressCfg `json:"guest_dress_cfg_list"` // 嘉宾服装配置列表
	ScenePreviewCfg                   *ScenePreviewCfg `json:"scene_preview_cfg"`
	WeddingHallGoingBackground        string           `json:"wedding_hall_going_background"`         // 仪式大厅进行中的背景图片
	CertificatePic                    string           `json:"certificate_pic"`                       // 结婚证图片
	PresentId                         uint32           `json:"present_id"`                            // 高光时刻手捧花礼物包裹id
	PresentDay                        uint32           `json:"present_day"`                           // 高光时刻手捧花礼物天数 【已弃用】
	SpecialBackgroundPicture          string           `json:"special_background_picture"`            // 结婚宣言、互换戒指 需要特殊的背景资源图片
	SpecialBackgroundMp4Url           string           `json:"special_background_mp4_url"`            // 背景mp4
	FellowSpaceWeddingElement         string           `json:"fellow_space_wedding_element"`          // 挚友空间婚礼元素
	FellowSpaceWeddingBackground      string           `json:"fellow_space_wedding_background"`       // 挚友空间婚礼背景
	FellowHouseSpaceWeddingBackground string           `json:"fellow_house_space_wedding_background"` // 挚友小屋空间婚礼背景
	FellowSpaceWeddingColor           string           `json:"fellow_space_wedding_color"`            // 挚友空间婚礼颜色
	FellowSpaceWeddingCertificate     string           `json:"fellow_space_wedding_certificate"`      // 挚友空间结婚证书
	BuyWeddingPresentId               uint32           `json:"buy_wedding_present_id"`                // 购买婚礼方T豆礼物id
}

type ScenePreviewCfg struct {
	SmallIcon     string       `json:"small_icon"`
	ZoomAnimation *ResourceCfg `json:"zoom_animation"`
	ScenePng      string       `json:"scene_png"`
}

type ChairGameResConf struct {
	ChairPic            string   `json:"chair_pic"`
	SittingPoseMaleId   uint32   `json:"sitting_pose_male_id"`
	SittingPoseFemaleId uint32   `json:"sitting_pose_female_id"`
	StandbyMaleId       uint32   `json:"standby_male_id"`
	StandbyFemaleId     uint32   `json:"standby_female_id"`
	FailMaleIds         []uint32 `json:"fail_male_ids"`
	FailFemaleIds       []uint32 `json:"fail_female_ids"`
}

type ResourceCfg struct {
	Url        string   `json:"url"`          // 资源包地址
	Md5        string   `json:"md5"`          // 资源包MD5
	Pic        string   `json:"pic"`          // 图片地址
	BoneId     uint32   `json:"bone_id"`      // 骨骼资源ID
	Items      []uint32 `json:"items"`        // 物品ID列表
	BaseBoneId uint32   `json:"base_bone_id"` // 基础骨骼资源ID
}

func (c *BusinessConf) Parse(configFile string) (isChange bool, err error) {
	defer func() {
		if e := recover(); e != nil {
			err = fmt.Errorf("Failed to parse config: %v \n", e)
		}
	}()

	data, err := ioutil.ReadFile(configFile)
	if err != nil {
		return false, err
	}

	md5Sum := md5.Sum(data)
	if md5Sum == LastConfMd5Sum {
		isChange = false
		return
	}

	err = json.Unmarshal(data, &c)
	if err != nil {
		return false, err
	}

	err = c.CheckConf()
	if err != nil {
		return false, err
	}

	LastConfMd5Sum = md5Sum

	// 排序
	sort.Slice(c.WeddingThemeCfgList, func(i, j int) bool {
		return c.WeddingThemeCfgList[i].Rank < c.WeddingThemeCfgList[j].Rank
	})

	log.Infof("BusinessConf : %+v", c)
	return true, nil
}

type BusinessConfManager struct {
	Done chan interface{}
	//mutex sync.RWMutex
	conf *BusinessConf
}

func NewBusinessConfManager() (*BusinessConfManager, error) {
	businessConf := &BusinessConf{}

	businessConfFilePath := BusinessConfPath + BusinessConfFile
	if devBusinessConfPath := os.Getenv("DEV_BUSINESS_CONF_PATH"); devBusinessConfPath != "" {
		businessConfFilePath = devBusinessConfPath + BusinessConfFile
	}
	_, err := businessConf.Parse(businessConfFilePath)
	if err != nil {
		log.Errorf("NewBusinessConfManager fail to Parse BusinessConf err:%v", err)
		return nil, err
	}

	confMgr := &BusinessConfManager{
		conf: businessConf,
		Done: make(chan interface{}),
	}

	go confMgr.Watch(businessConfFilePath)

	return confMgr, nil
}

func (bm *BusinessConfManager) Reload(file string) error {
	businessConf := &BusinessConf{}

	isChange, err := businessConf.Parse(file)
	if err != nil {
		log.Errorf("NewBusinessConfManager fail to Parse BusinessConf err:%v", err)
		return err
	}

	if isChange {
		//bm.mutex.Lock()
		bm.conf = businessConf
		//bm.mutex.Unlock()

		log.Infof("Reload %+v", businessConf)
	}

	return nil
}

func (bm *BusinessConfManager) Watch(file string) {
	log.Infof("Watch start. file:%s", file)

	for {
		select {
		case _, ok := <-bm.Done:
			if !ok {
				log.Infof("Watch done")
				return
			}

		case <-time.After(30 * time.Second):
			log.Debugf("Watch check change")

			err := bm.Reload(file)
			if err != nil {
				log.Errorf("Watch Reload fail. file:%s, err:%v", file, err)
			}
		}
	}
}

func (bm *BusinessConfManager) Close() {
	close(bm.Done)
}

func (c *BusinessConf) CheckConf() error {
	return nil
}

func (bm *BusinessConfManager) GetWeddingThemeCfgList() []*WeddingThemeCfg {
	if bm.conf == nil {
		return []*WeddingThemeCfg{}
	}
	return bm.conf.WeddingThemeCfgList
}

func (bm *BusinessConfManager) GetThemeTitleBgIcon() string {
	if bm.conf == nil {
		return ""
	}
	return bm.conf.ThemeTitleBgIcon
}

func (bm *BusinessConfManager) GetThemeTitleSelectedIcon() string {
	if bm.conf == nil {
		return ""
	}
	return bm.conf.ThemeTitleSelectedIcon
}
func (bm *BusinessConfManager) GetWeddingThemeCfg(themeId uint32) *WeddingThemeCfg {
	if bm.conf == nil {
		return nil
	}

	for _, v := range bm.conf.WeddingThemeCfgList {
		if v.ThemeId == themeId {
			return v
		}
	}

	return nil
}

func (bm *BusinessConfManager) GetWeedingSceneCfg(themeId, sceneId uint32) *WeddingSceneCfg {
	if bm.conf == nil {
		return nil
	}

	themeCfg := bm.GetWeddingThemeCfg(themeId)
	if themeCfg == nil {
		return nil
	}

	for _, v := range themeCfg.SceneCfgList {
		if v.SceneId == sceneId {
			return v
		}
	}

	return nil
}

func (bm *BusinessConfManager) GetWeddingLevelCfg(themeId, level uint32) *WeddingLevelCfg {
	if bm.conf == nil {
		return nil
	}

	for _, v := range bm.conf.WeddingThemeCfgList {
		if v.ThemeId == themeId {
			for _, lv := range v.LevelCfgList {
				if lv.Level == level {
					return lv
				}
			}
		}
	}

	return nil
}

// GetWeddingRankGgMap 根据主题id获取榜单背景配置
func (bm *BusinessConfManager) GetWeddingRankGgMap() map[uint32][]*ThemeRankBg {
	if bm.conf == nil {
		return nil
	}
	return bm.conf.ThemeRankBgMap
}

func (bm *BusinessConfManager) GetAllWeddingFinishedAwardCfg() map[uint32][]*RewardPreview {
	if bm.conf == nil {
		return nil
	}

	themeAwardMap := make(map[uint32][]*RewardPreview)
	for _, v := range bm.conf.WeddingThemeCfgList {
		awardList := make([]*RewardPreview, 0)

		for _, v := range v.RewardInfoList {
			awardList = append(awardList, v)
		}

		if len(awardList) == 0 {
			continue
		}
		themeAwardMap[v.ThemeId] = awardList
	}

	return themeAwardMap
}
