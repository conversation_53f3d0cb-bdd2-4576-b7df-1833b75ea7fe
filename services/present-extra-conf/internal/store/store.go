package store

import (
	"context"
	"database/sql"
	"github.com/jinzhu/gorm"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/protocol/services/presentextraconf"
	"golang.52tt.com/services/present-extra-conf/internal/conf"
	"time"

	_ "github.com/go-sql-driver/mysql" // MySQL驱动。
)

// PresentFloatLayer 礼物浮层表
type PresentFloatLayer struct {
	GiftId              uint32    `gorm:"primary_key" sql:"type:INT(10) NOT NULL AUTO_INCREMENT COMMENT '礼物id'" json:"gift_id"`
	FrameImageUrl       string    `sql:"type:varchar(500) NOT NULL DEFAULT '' COMMENT '浮层图片链接'" json:"frame_image_url"`
	JumpUrl             string    `sql:"type:varchar(500) NOT NULL DEFAULT '' COMMENT '跳转链接'" json:"jump_url"`
	JumpUrlType         uint32    `sql:"type:INT(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '跳转链接类型'" json:"jump_url_type"`
	EffectBegin         time.Time `json:"effect_begin"`
	EffectEnd           time.Time `json:"effect_end"`
	Operator            string    `sql:"type:varchar(45) NOT NULL DEFAULT '' COMMENT '最后操作人'" json:"operator"`
	CreatedAt           time.Time `gorm:"index:update_at_index" json:"created_at,omitempty"`
	UpdatedAt           time.Time `gorm:"index:create_at_index" json:"updated_at"`
	ShowChannelTypeList string    `sql:"type:varchar(500) NOT NULL DEFAULT '' COMMENT '展示渠道'" json:"show_channel_type_list"`
	ShowAppTypeList     string    `sql:"type:varchar(500) NOT NULL DEFAULT '' COMMENT '展示app'" json:"show_app_type_list"`
	AppUrlList          string    `sql:"type:varchar(500) NOT NULL DEFAULT '' COMMENT 'app链接'" json:"app_url_list"`
	ActivityType        uint32    `sql:"type:INT(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '活动类型'" json:"activity_type"`
	SubActivityType     uint32    `sql:"type:INT(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '子活动类型'" json:"sub_activity_type"`
}

// FlashEffectConfig 闪光效果表
type FlashEffectConfig struct {
	FlashId   uint32    `gorm:"primary_key" sql:"type:INT(10) NOT NULL AUTO_INCREMENT COMMENT '效果id'" json:"flash_id"`
	FlashName string    `sql:"type:varchar(45) NOT NULL DEFAULT '' COMMENT '浮层图片链接'" json:"flash_name"`
	FlashUrl  string    `sql:"type:varchar(500) NOT NULL DEFAULT '' COMMENT '跳转链接'" json:"flash_url"`
	FlashMd5  string    `sql:"type:varchar(500) NOT NULL DEFAULT '' COMMENT  '跳转链接类型'" json:"flash_md5"`
	Operator  string    `sql:"type:varchar(45) NOT NULL DEFAULT '' COMMENT '最后操作人'" json:"operator"`
	CreatedAt time.Time `gorm:"index:update_at_index" json:"created_at,omitempty"`
	UpdatedAt time.Time `gorm:"index:create_at_index" json:"updated_at"`
}

// PresentFlashInfo 闪光效果绑定表
type PresentFlashInfo struct {
	GiftId      uint32    `gorm:"primary_key" sql:"type:INT(10) NOT NULL AUTO_INCREMENT COMMENT '礼物id'" json:"gift_id"`
	FlashId     uint32    `sql:"type:INT(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '效果id'" json:"flash_id"`
	EffectBegin time.Time `json:"effect_begin"`
	EffectEnd   time.Time `json:"effect_end"`
	Operator    string    `sql:"type:varchar(45) NOT NULL DEFAULT '' COMMENT '最后操作人'" json:"operator"`
	CreatedAt   time.Time `gorm:"index:update_at_index" json:"created_at,omitempty"`
	UpdatedAt   time.Time `gorm:"index:create_at_index"  json:"updated_at"`
}

// CustomizedPresentConfig 专属定制礼物配置表
type CustomizedPresentConfig struct {
	PrimaryGiftId uint32    `gorm:"primary_key" sql:"type:INT(10) NOT NULL AUTO_INCREMENT COMMENT '主礼物id'" json:"primary_gift_id"`
	CmsUrl        string    `sql:"type:varchar(45) NOT NULL DEFAULT '' COMMENT 'cms链接'" json:"cms_url"`
	LevelText     string    `sql:"type:varchar(45) NOT NULL DEFAULT '' COMMENT '等级说明'" json:"level_text"`
	Extend        string    `sql:"type:varchar(10000) NOT NULL DEFAULT '' COMMENT '额外信息'" json:"extend"`
	MaxLevel      uint32    `sql:"type:INT(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '最大等级'" json:"max_level"`
	CreatedAt     time.Time `gorm:"index:update_at_index" json:"created_at,omitempty"`
	UpdatedAt     time.Time `gorm:"index:create_at_index"  json:"updated_at"`
}

// Extend :CustomizedPresentConfig 的 Extend 是一个额外的json结构,结构如下
type Extend struct {
	EffectConfig []*presentextraconf.LevelEffectConfig             `json:"effect_config,omitempty"` //
	CustomMethod map[string]uint32                                 `json:"custom_method,omitempty"`
	GiftIdList   []uint32                                          `json:"gift_id_list,omitempty"`
	PreviewMap   map[uint32]*presentextraconf.CustomPresentPreview `json:"preview_map,omitempty"`
}

// CustomizedCustomConfig 专属定制礼物组件配置表
type CustomizedCustomConfig struct {
	PrimaryGiftId uint32    `gorm:"primary_key" sql:"type:INT(10) NOT NULL AUTO_INCREMENT COMMENT '主礼物id'" json:"primary_gift_id"`
	CustomId      uint32    `gorm:"primary_key" sql:"type:INT(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '组件id'" json:"custom_id"`
	CustomName    string    `sql:"type:varchar(45) NOT NULL DEFAULT '' COMMENT '组件名'" json:"custom_name"`
	CustomText    string    `sql:"type:varchar(45) NOT NULL DEFAULT '' COMMENT '组件说明'" json:"custom_text"`
	CreatedAt     time.Time `gorm:"index:update_at_index" json:"created_at,omitempty"`
	UpdatedAt     time.Time `gorm:"index:create_at_index"  json:"updated_at"`
}

// CustomizedOptionConfig 专属定制礼物样式配置表
type CustomizedOptionConfig struct {
	PrimaryGiftId uint32    `gorm:"primary_key" sql:"type:INT(10) NOT NULL AUTO_INCREMENT COMMENT '主礼物id'" json:"primary_gift_id"`
	CustomId      uint32    `gorm:"primary_key" sql:"type:INT(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '组件id'" json:"custom_id"`
	OptionId      uint32    `gorm:"primary_key" sql:"type:INT(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '组件id'" json:"option_id"`
	OptionName    string    `sql:"type:varchar(45) NOT NULL DEFAULT '' COMMENT '组件名'" json:"option_name"`
	MinLevel      uint32    `sql:"type:INT(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '最低可用等级'" json:"min_level"`
	CreatedAt     time.Time `gorm:"index:update_at_index" json:"created_at,omitempty"`
	UpdatedAt     time.Time `gorm:"index:create_at_index"  json:"updated_at"`
}

// UserOptionRecord 用户的样式点击表
type UserOptionRecord struct {
	PrimaryGiftId uint32    `gorm:"primary_key" sql:"type:INT(10) NOT NULL AUTO_INCREMENT COMMENT '主礼物id'" json:"primary_gift_id"`
	CustomId      uint32    `gorm:"primary_key" sql:"type:INT(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '组件id'" json:"custom_id"`
	OptionId      uint32    `gorm:"primary_key" sql:"type:INT(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '组件id'" json:"option_id"`
	Uid           uint32    `gorm:"primary_key" sql:"type:INT(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户uid'" json:"uid"`
	CreatedAt     time.Time `gorm:"index:update_at_index" json:"created_at,omitempty"`
	UpdatedAt     time.Time `gorm:"index:create_at_index"  json:"updated_at"`
}

// UserOptionActive 用户的样式选中表
type UserOptionActive struct {
	PrimaryGiftId uint32    `gorm:"primary_key" sql:"type:INT(10) NOT NULL AUTO_INCREMENT COMMENT '主礼物id'" json:"primary_gift_id"`
	CustomId      uint32    `gorm:"primary_key" sql:"type:INT(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '组件id'" json:"custom_id"`
	OptionId      uint32    `sql:"type:INT(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '组件id'" json:"option_id"`
	Uid           uint32    `gorm:"primary_key" sql:"type:INT(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户uid'" json:"uid"`
	CreatedAt     time.Time `gorm:"index:update_at_index" json:"created_at,omitempty"`
	UpdatedAt     time.Time `gorm:"index:create_at_index"  json:"updated_at"`
}

// EffectDelayLevelConfig 限时礼物延时配置
type EffectDelayLevelConfig struct {
	GiftId         uint32    `gorm:"primary_key;autoIncrement:false" sql:"type:INT(10) NOT NULL  COMMENT '礼物id'" json:"gift_id"`
	Level          uint32    `gorm:"primary_key;autoIncrement:false" sql:"type:INT(10) NOT NULL  COMMENT '等级'" json:"level"`
	SendCount      uint32    `sql:"type:INT(10) NOT NULL COMMENT '所需赠送个数'" json:"send_count"`
	DayCount       uint32    `sql:"type:INT(10) NOT NULL COMMENT '延长时间'" json:"day_count"`
	ExpireDayCount uint32    `sql:"type:INT(10) NOT NULL COMMENT '多少天内不赠送则消失'" json:"expire_day_count"`
	EffectBegin    uint32    `gorm:"primary_key;autoIncrement:false" sql:"type:INT(10) NOT NULL  COMMENT '上架时间'" json:"effect_begin"`
	EffectEnd      uint32    `sql:"type:INT(10) NOT NULL  COMMENT '下架时间'" json:"effect_end"`
	NoticeDayCount uint32    `sql:"type:INT(10) NOT NULL COMMENT '还有多少天到期就提醒'" json:"notice_day_count"`
	UpdatedAt      time.Time `gorm:"index:create_at_index"  json:"updated_at"`
}

// PresentEffectTimeRecord 限时礼物赠送记录
type PresentEffectTimeRecord struct {
	GiftId      uint32    `gorm:"primary_key;autoIncrement:false" sql:"type:INT(10) NOT NULL  COMMENT '礼物id'" json:"gift_id"`
	Uid         uint32    `gorm:"primary_key;autoIncrement:false" sql:"type:INT(10) NOT NULL  COMMENT 'uid'" json:"uid"`
	EffectBegin uint32    `gorm:"primary_key;autoIncrement:false" sql:"type:INT(10) NOT NULL  COMMENT '上架时间'" json:"effect_begin"`
	SendCount   uint32    `sql:"type:INT(10) NOT NULL COMMENT '已赠送个数'" json:"send_count"`
	UpdatedAt   time.Time `gorm:"index:create_at_index"  json:"updated_at"`
}

// PresentEffectDayRecord 限时礼物延长的总天数记录
type PresentEffectDayRecord struct {
	GiftId         uint32    `gorm:"primary_key;autoIncrement:false" sql:"type:INT(10) NOT NULL  COMMENT '礼物id'" json:"gift_id"`
	Uid            uint32    `gorm:"primary_key;autoIncrement:false" sql:"type:INT(10) NOT NULL  COMMENT 'uid'" json:"uid"`
	EffectBegin    uint32    `gorm:"primary_key;autoIncrement:false" sql:"type:INT(10) NOT NULL  COMMENT '上架时间'" json:"effect_begin"`
	ExpireDayCount uint32    `sql:"type:INT(10) NOT NULL  COMMENT '过期时间'" json:"expire_day_count"`
	TimeAppend     uint32    `sql:"type:INT(10) NOT NULL COMMENT '当前周期延长的时间'" json:"time_append"`
	IsLimitless    uint32    `sql:"type:INT(10) NOT NULL COMMENT '是否无限延长'" json:"is_limitless"`
	UpdatedAt      time.Time `gorm:"index:create_at_index"  json:"updated_at"`
}

// LastUpdateTime 最后更新时间
type LastUpdateTime struct {
	UpdateTime time.Time
}

// Store .
type Store struct {
	mysql *gorm.DB             // MYSQL库
	sc    *conf.ServiceConfigT // 配置
}

// NewStore .
func NewStore(ctx context.Context, sc *conf.ServiceConfigT) (*Store, error) {
	db, err := gorm.Open("mysql", sc.StoreConfig.ConnectionString())
	if err != nil {
		log.ErrorWithCtx(ctx, "mysql open fail. %+v err:%v", sc.StoreConfig, err)
		return nil, err
	}

	if sc.StoreConfig.MaxIdleConns > 0 {
		db.DB().SetMaxIdleConns(sc.StoreConfig.MaxIdleConns)
	}
	if sc.StoreConfig.MaxOpenConns > 0 {
		db.DB().SetMaxOpenConns(sc.StoreConfig.MaxOpenConns)
	}

	db.DB().SetConnMaxLifetime(time.Minute * 5)

	st := &Store{
		mysql: db,
		sc:    sc,
	}

	_ = st.InitTable()

	return st, nil
}

// NewStore .
func NewStoreForTest(ctx context.Context, sc *conf.ServiceConfigT) (*Store, error) {
	db, err := gorm.Open("mysql", sc.StoreConfig.ConnectionString())
	if err != nil {
		log.ErrorWithCtx(ctx, "mysql open fail. %+v err:%v", sc.StoreConfig, err)
		return nil, err
	}

	if sc.StoreConfig.MaxIdleConns > 0 {
		db.DB().SetMaxIdleConns(sc.StoreConfig.MaxIdleConns)
	}
	if sc.StoreConfig.MaxOpenConns > 0 {
		db.DB().SetMaxOpenConns(sc.StoreConfig.MaxOpenConns)
	}

	db.DB().SetConnMaxLifetime(time.Minute * 5)

	st := &Store{
		mysql: db,
		sc:    sc,
	}

	_ = st.InitTableWithoutMigrate()

	return st, nil
}

// NewStore .
func NewMockStore(ctx context.Context, sc *conf.ServiceConfigT, db *gorm.DB) (*Store, error) {

	st := &Store{
		mysql: db,
		sc:    sc,
	}

	return st, nil
}

func (st *Store) GetMysql() *gorm.DB {
	return st.mysql
}

func (st *Store) TxBegin() *gorm.DB {
	tx := st.mysql.Begin()
	return tx
}

func (st *Store) Transaction(ctx context.Context, f func(tx *gorm.DB) error) error {
	// begin
	tx := st.GetMysql().BeginTx(ctx, &sql.TxOptions{})

	err := f(tx)
	if err != nil {
		log.ErrorWithCtx(ctx, "Transaction fail. err:%v", err)
		tx.Rollback()
		return err
	}

	tx.Commit()
	return nil
}

func (st *Store) Close() {
	st.GetMysql().Close()
}

func (st *Store) InitTable() error {

	st.mysql.CreateTable(&PresentFloatLayer{})
	st.mysql.CreateTable(&FlashEffectConfig{})
	st.mysql.CreateTable(&PresentFlashInfo{})
	st.mysql.CreateTable(&LastUpdateTime{})
	st.mysql.CreateTable(&CustomizedPresentConfig{})
	st.mysql.CreateTable(&CustomizedCustomConfig{})
	st.mysql.CreateTable(&CustomizedOptionConfig{})
	st.mysql.CreateTable(&UserOptionRecord{})
	st.mysql.CreateTable(&UserOptionActive{})
	st.mysql.CreateTable(&PresentEffectTimeRecord{})
	st.mysql.CreateTable(&EffectDelayLevelConfig{})
	st.mysql.CreateTable(&PresentEffectDayRecord{})

	st.mysql.AutoMigrate(&PresentFloatLayer{})
	st.mysql.AutoMigrate(&FlashEffectConfig{})
	st.mysql.AutoMigrate(&PresentFlashInfo{})
	st.mysql.AutoMigrate(&LastUpdateTime{})

	st.mysql.AutoMigrate(&CustomizedPresentConfig{})
	st.mysql.AutoMigrate(&CustomizedCustomConfig{})
	st.mysql.AutoMigrate(&CustomizedOptionConfig{})
	st.mysql.AutoMigrate(&UserOptionRecord{})
	st.mysql.AutoMigrate(&UserOptionActive{})

	st.mysql.AutoMigrate(&PresentEffectTimeRecord{})
	st.mysql.AutoMigrate(&EffectDelayLevelConfig{})
	st.mysql.AutoMigrate(&PresentEffectDayRecord{})

	return nil
}

func (st *Store) InitTableWithoutMigrate() error {

	st.mysql.CreateTable(&PresentFloatLayer{})
	st.mysql.CreateTable(&FlashEffectConfig{})
	st.mysql.CreateTable(&PresentFlashInfo{})
	st.mysql.CreateTable(&LastUpdateTime{})
	st.mysql.CreateTable(&CustomizedPresentConfig{})
	st.mysql.CreateTable(&CustomizedCustomConfig{})
	st.mysql.CreateTable(&CustomizedOptionConfig{})
	st.mysql.CreateTable(&UserOptionRecord{})
	st.mysql.CreateTable(&UserOptionActive{})
	st.mysql.CreateTable(&PresentEffectTimeRecord{})
	st.mysql.CreateTable(&EffectDelayLevelConfig{})
	st.mysql.CreateTable(&PresentEffectDayRecord{})

	return nil
}

func (st *Store) GetLastUpdateTable() *gorm.DB {
	return st.GetMysql().Table("last_update_time")
}

func (*LastUpdateTime) TableName() string {
	return "last_update_time"
}

func (st *Store) SetLastUpdateTime(ctx context.Context, update time.Time) error {
	res := &LastUpdateTime{}
	err := st.GetLastUpdateTable().First(&res).Error
	if err != nil {
		if gorm.IsRecordNotFoundError(err) {
			sErr := st.GetLastUpdateTable().Create(&LastUpdateTime{UpdateTime: update}).Error
			if sErr != nil {
				log.ErrorWithCtx(ctx, "SetLastUpdateTime Create err ,  err:%v", sErr)
				return sErr
			}
		}
		log.ErrorWithCtx(ctx, "SetLastUpdateTime First err ,  err:%v", err)
		return err
	}

	sErr := st.GetLastUpdateTable().Update(&LastUpdateTime{UpdateTime: update}).Error
	if sErr != nil {
		log.ErrorWithCtx(ctx, "SetLastUpdateTime Update err ,  err:%v", sErr)
		return sErr
	}
	return sErr
}

func (st *Store) GetLastUpdateTime(ctx context.Context) (update time.Time, err error) {
	res := &LastUpdateTime{}
	err = st.GetLastUpdateTable().First(&res).Error
	if err != nil {
		log.ErrorWithCtx(ctx, "GetLastUpdateTime Create err ,  err:%v", err)
		return update, err
	}
	update = res.UpdateTime
	return update, nil
}
