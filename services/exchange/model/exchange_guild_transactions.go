package model

import (
	"github.com/jinzhu/gorm"
	Exchange "golang.52tt.com/protocol/services/exchange"
	"time"
)

type ExchangeGuildTransactions struct {
	GuildOrderId  string `gorm:"primary_key;auto_increment:false;type:varchar(200)"`
	MasterUid     uint32 `gorm:"primary_key;auto_increment:false"`
	UserId        uint32 `gorm:"primary_key;auto_increment:false;comment:'主播UID';index:index_user_id_create_at"`
	Source        uint8  `gorm:"primary_key;auto_increment:false;default:0;comment:'0积分提现，1积分暂存，2暂存提现'"`
	GuildId       uint32 `gorm:"not null"`
	Score         uint64 `gorm:"not null"`
	AnchorScore   uint64 `gorm:"not null"`
	MaskedPkScore uint64 `gorm:"not null"`
	KnightScore   uint64 `gorm:"not null"`
	EsportScore   uint64 `gorm:"not null"`
	CreateAt      uint32 `gorm:"not null;index:index_create_at_status"`
	Status        uint8  `gorm:"not null;comment:'0未完成，1已完成，2错误';index:index_create_at_status"`
	Reason        uint8  `gorm:"not null;comment:'0公对私切换，1解约，2官方回收, 3会长提现';index:index_outside_time_reason"`
	TryTimes      uint8  `gorm:"not null;default:0"`
	ErrMsg        string `gorm:"type:varchar(200);not null"`
	UpdateAt      uint32 `gorm:"not null"`
}

type SumData struct {
	UserId   uint32 `json:"user_id"`
	SumScore uint32 `json:"sum_score"`
}

const (
	X_SOURCE_TEMPSAVE    = 1
	X_SOURCE_TS_EXCHANGE = 2

	X_GUILD_X_STATUS_INIT = 0
	X_GUILD_X_STATUS_DONE = 1
	X_GUILD_X_STATUS_ERR  = 2

	X_GUILD_X_REASON_TO_PRIVATE        = 0
	X_GUILD_X_REASON_CANCEL_CONTRACT   = 1
	X_GUILD_X_REASON_OFFICIAL_RECOVERY = 2
	X_GUILD_X_REASON_EXCHANGE          = 3
	X_GUILD_X_REASON_AUTO_EXCHANGE     = 4
	X_GUILD_X_REASON_AUTO_SETTLEMENT   = 5
	X_REASON_OFFICIAL_RECYCLE          = 6
	X_REASON_UNFREEZE = 7
)

func (exchangeGuildTransactions *ExchangeGuildTransactions) TableName() string {
	return "exchange_guild_transactions"
}

type ExchangeGuildTransactionsDB struct {
	ExchangeGuildTransactions
	Db *gorm.DB
}

func (xdb *ExchangeGuildTransactionsDB) AutoMigrate() error {
	return xdb.Db.AutoMigrate(&xdb.ExchangeGuildTransactions).Error
}

func (xdb *ExchangeGuildTransactionsDB) Insert() error {
	return xdb.Db.Model(&xdb.ExchangeGuildTransactions).Create(&xdb.ExchangeGuildTransactions).Error
}

func (xdb *ExchangeGuildTransactionsDB) UpdateStatus(guildOrderId string, masterUid uint32, userId uint32, source uint8, status uint8) error {
	xdb.ExchangeGuildTransactions.UpdateAt = uint32(time.Now().Unix())
	return xdb.Db.Model(&xdb.ExchangeGuildTransactions).Where("guild_order_id=? and master_uid=? and user_id=? and source=?",
		guildOrderId, masterUid, userId, source).Update("status", status).Error
}

func (xdb *ExchangeGuildTransactionsDB) AddTryTimes(guildOrderId string, masterUid uint32, userId uint32, source uint8) error {
	xdb.ExchangeGuildTransactions.UpdateAt = uint32(time.Now().Unix())
	return xdb.Db.Model(&xdb.ExchangeGuildTransactions).Where("guild_order_id=? and master_uid=? and user_id=? and source=?",
		guildOrderId, masterUid, userId, source).Update("try_times", gorm.Expr("try_times + 1")).Error
}

func (xdb *ExchangeGuildTransactionsDB) GetAllInit1MAgo(x *[]ExchangeGuildTransactions) error {
	return xdb.Db.Model(&xdb.ExchangeGuildTransactions).Where("create_at<? and status=?", time.Now().Unix()-60, X_GUILD_X_STATUS_INIT).Find(x).Error
}

func (xdb *ExchangeGuildTransactionsDB) GetAnchorList(userId uint32, offset uint32, limit uint32, list *[]ExchangeGuildTransactions) error {
	return xdb.Db.Model(&xdb.ExchangeGuildTransactions).Where("user_id=? and source=?", userId, X_SOURCE_TEMPSAVE).Order(
		"create_at desc").Offset(offset).Limit(limit).Find(list).Error
}

func (xdb *ExchangeGuildTransactionsDB) GetDetailList(guildOrderId string, masterUid uint32, offset uint32, limit uint32, list *[]ExchangeGuildTransactions) error {
	return xdb.Db.Model(&xdb.ExchangeGuildTransactions).Where("guild_order_id=? and master_uid=?", guildOrderId, masterUid).Offset(
		offset).Limit(limit).Find(list).Error
}

func (xdb *ExchangeGuildTransactionsDB) GetData(guildOrderId string, masterUid uint32, uid uint32, source uint8) {
	xdb.Db.Model(&xdb.ExchangeGuildTransactions).Where("guild_order_id=? and master_uid=? and user_id=? and source=?",
		guildOrderId, masterUid, uid, source).First(&xdb.ExchangeGuildTransactions)
}

//获取未完成的积分
func (xdb *ExchangeGuildTransactionsDB) GetUndoneCount(uid uint32) (count uint32) {
	xdb.Db.Model(&xdb.ExchangeGuildTransactions).Where("user_id=? and status<>?", uid, X_GUILD_X_STATUS_DONE).Count(&count)
	return
}

func (xdb *ExchangeGuildTransactionsDB) GetUserLastData(uid uint32) {
	xdb.Db.Model(&xdb.ExchangeGuildTransactions).Where("user_id=? and source=?", uid, X_SOURCE_TEMPSAVE).First(&xdb.ExchangeGuildTransactions)
}

func (xdb *ExchangeGuildTransactionsDB) GetMasterUndoneCount(masterUid uint32) (count uint32) {
	xdb.Db.Model(&xdb.ExchangeGuildTransactions).Where("master_uid=? and status<>?", masterUid, X_GUILD_X_STATUS_DONE).Count(&count)
	return
}

func (xdb *ExchangeGuildTransactionsDB) GetCATransactionDuringTime(startTime int64, endTime int64) *[]ExchangeGuildTransactions {
	list := &[]ExchangeGuildTransactions{}
	xdb.Db.Model(&xdb.ExchangeGuildTransactions).Where("create_at>=? and create_at<? and reason=? and masked_pk_score=0",
		startTime, endTime, X_GUILD_X_REASON_EXCHANGE).Order("create_at asc").Find(list)
	return list
}

func (xdb *ExchangeGuildTransactionsDB) GetPKTransactionDuringTime(startTime int64, endTime int64) *[]ExchangeGuildTransactions {
	list := &[]ExchangeGuildTransactions{}
	xdb.Db.Model(&xdb.ExchangeGuildTransactions).Where("create_at>=? and create_at<? and reason=? and score=0 and anchor_score=0",
		startTime, endTime, X_GUILD_X_REASON_EXCHANGE).Order("create_at asc").Find(list)
	return list
}

func (xdb *ExchangeGuildTransactionsDB) GetAllTransactionDuringTime(startTime int64, endTime int64) *[]ExchangeGuildTransactions {
	list := &[]ExchangeGuildTransactions{}
	xdb.Db.Model(&xdb.ExchangeGuildTransactions).Where("create_at>=? and create_at<? and reason=?",
		startTime, endTime, X_GUILD_X_REASON_EXCHANGE).Order("create_at asc").Find(list)
	return list
}

func (xdb *ExchangeGuildTransactionsDB) GetTimeRangeList(masterUid uint32, startTime int64, endTime int64, reason uint8, xType uint8,
	offset uint32, limit uint32) *[]ExchangeGuildTransactions {
	list := &[]ExchangeGuildTransactions{}
	sqlStr := "master_uid=? and create_at>? and create_at<=? and status=?"
	if reason == X_GUILD_X_REASON_AUTO_SETTLEMENT {
		sqlStr += " and reason=?"
	} else {
		sqlStr += " and reason<=?"
	}
	switch Exchange.ExchangeType(xType) {
	case Exchange.ExchangeType_PRESENT:
		sqlStr += " and score>0"
	case Exchange.ExchangeType_ANCHOR:
		sqlStr += " and anchor_score>0"
	case Exchange.ExchangeType_PK:
		sqlStr += " and masked_pk_score>0"
	case Exchange.ExchangeType_KNIGHT:
		sqlStr += " and knight_score>0"
	case Exchange.ExchangeType_ESPORT:
		sqlStr += " and esport_score>0"
	default:
		return list
	}
	xdb.Db.Model(&xdb.ExchangeGuildTransactions).Where(sqlStr, masterUid, startTime, endTime, X_GUILD_X_STATUS_DONE, reason).
		Order("create_at asc").Offset(offset).Limit(limit).Find(list)
	return list
}

func (xdb *ExchangeGuildTransactionsDB) GetTimeRangeUidAutoExchangeList(exchangeType Exchange.ExchangeType, uidList []uint32,
	beginTime uint32, endTime uint32) (sumList []SumData) {
	selectSql := "user_id, sum("
	switch exchangeType {
	case Exchange.ExchangeType_PRESENT:
		selectSql += "score"
	case Exchange.ExchangeType_ANCHOR:
		selectSql += "anchor_score"
	case Exchange.ExchangeType_PK:
		selectSql += "masked_pk_score"
	case Exchange.ExchangeType_KNIGHT:
		selectSql += "knight_score"
	case Exchange.ExchangeType_ESPORT:
		selectSql += "esport_score"
	default:
		selectSql += "score"
	}
	selectSql += ") as sum_score"
	db := xdb.Db.Model(&xdb.ExchangeGuildTransactions).Where("create_at>=? and create_at<? and source=? and user_id in (?)",
		beginTime, endTime, X_SOURCE_TEMPSAVE, uidList)
	db.Group("user_id").Select(selectSql).Scan(&sumList)
	return
}

type ExchangeGuildTransactionCountSum struct {
	OrderCount uint32 `json:"order_count"`
	OrderSum   uint32 `json:"order_sum"`
}

//暂存对账
func (xdb *ExchangeGuildTransactionsDB) GetReconcileCountSum(beginTime int64, endTime int64, xType Exchange.ExchangeType) *ExchangeGuildTransactionCountSum {
	data := &ExchangeGuildTransactionCountSum{}
	db := xdb.Db.Model(&xdb.ExchangeGuildTransactions)

	selectStr := "count(*) as order_count,"
	whereStr := "create_at >= ? and create_at < ? and source = ? and status=1"
	switch xType {
	case Exchange.ExchangeType_PRESENT:
		selectStr += " ifnull(sum(score), 0) as order_sum"
		whereStr += " and score > 0"
	default:
		return data
	}
	db.Select(selectStr).Where(whereStr, beginTime, endTime, X_SOURCE_TEMPSAVE).Scan(data)
	return data
}

func (xdb *ExchangeGuildTransactionsDB) GetAfterTimeStatus(createAt int64, reason int) ([]ExchangeGuildTransactions, error) {
	var list []ExchangeGuildTransactions
	err := xdb.Db.Model(&xdb.ExchangeGuildTransactions).Where("create_at>=? and reason=? and status=?", createAt, reason, X_GUILD_X_STATUS_DONE).Find(&list).Error
	return list, err
}
