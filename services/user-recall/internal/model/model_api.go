package model

import (
	context "context"

	pb "golang.52tt.com/protocol/services/user-recall"
)

type IStore interface {
	Close()
	DelUserRecallBind(ctx context.Context, uid uint32) error
	DelUserRecallLoginPrize(ctx context.Context, uid uint32) error
	DeleteUserRecallImRecord(ctx context.Context, inviteUid uint32) error
	DeleteUserRecallInviteRecord(ctx context.Context, inviteUid uint32) error
	FinishUserRecallBindStatus(ctx context.Context, uid uint32, lastLoginTime int64) error
	GetUserBindInfo(ctx context.Context, uid uint32, lastLoginTime int64) (*UserRecallBind, error)
	GetUserRecallBindUnfinishedList(ctx context.Context, inviteUid uint32) ([]*UserRecallBind, error)
	GetUserRecallInviteRecord(ctx context.Context, inviteUid, uid uint32) ([]*UserRecallInviteRecord, error)
	GetUserRecallLoginLastData(ctx context.Context, uid uint32) (*UserRecallLoginPrize, error)
	GetUserRecallLoginPrizeList(ctx context.Context, uid uint32) ([]*UserRecallLoginPrize, error)
	Init() error
	InsertUserRecallImRecord(ctx context.Context, inviteUid, uid uint32, lastLoginTime int64) (int64, error)
	InsertUserRecallInviteRecord(ctx context.Context, inviteUid, uid, lastLoginTime uint32) error
	SaveUserRecallBind(ctx context.Context, uid uint32, lastLoginTime int64, inviteUid uint32) error
	SaveUserRecallLoginPrize(ctx context.Context, uid uint32, lastLoginTime int64, accConsume uint64, createTime int64) error
	GetUserRecallCouple(ctx context.Context, inviteUid uint32, list []*pb.LossUserInfo) ([]*UserRecallCouple, error)
	InsertUserRecallCouple(ctx context.Context, inviteUid uint32, list []*pb.LossUserInfo) error
	DeleteUserRecallCouple(ctx context.Context, inviteUid uint32) error
	GetUserRecallWhiteListDataList(ctx context.Context, inviteUid uint32) ([]*UserRecallWhiteList, error)
	SaveUserRecallWhiteList(ctx context.Context, inviteUid uint32, uid uint32) error
	DelUserRecallWhiteList(ctx context.Context, inviteUid uint32, uid uint32) error
	GetUserRecalledWhiteListData(ctx context.Context, uid uint32) (*UserRecalledWhiteList, error)
	SaveUserRecalledWhiteList(ctx context.Context, uid uint32, lastLoginTime int64, accConsume uint64) error
	DelUserRecalledWhiteList(ctx context.Context, uid uint32) error
	UpdateUserRecallInviteRecordTryTimes(ctx context.Context, inviteUid, uid uint32, lastLoginTime int64, tryTimes uint32) error
	GetInviterAllRecord(ctx context.Context, inviteUid uint32) ([]*UserRecallInviteRecord, error)
	GetUserRecallCoupleInfo(ctx context.Context, inviteUid, uid, lastLoginTime uint32) (*UserRecallCouple, error)
	GetUserRecallBindExpiredList(ctx context.Context) ([]*UserRecallBind, error)
	DelUserRecallBindDataWithTime(ctx context.Context, data *UserRecallBind) error
}
