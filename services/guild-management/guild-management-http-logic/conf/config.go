package conf

import (
	"os"
	"strings"

	beego "github.com/astaxie/beego/config"
	"golang.52tt.com/pkg/log"
)

var (
	ENV string
)

func init() {
	env := os.Getenv("MY_CLUSTER")
	if strings.Contains(env, "dev") {
		ENV = "dev"
	} else if strings.Contains(env, "test") {
		ENV = "test"
	} else if strings.Contains(env, "gray") {
		ENV = "gray"
	} else if strings.Contains(env, "prod") {
		ENV = "prod"
	} else {
		ENV = "prod"
	}

	log.Infof("MY_CLUSTER %s, ENV %s", env, ENV)
}

type ServerConfig struct {
	addr          string
	debugAddr     string
	validateToken bool
	cors          bool
	IsTest        bool

	commissionURLPrefix         string
	commissionAppID             string
	commissionSignKey           string
	AppIDGiftScore              string
	SignKeyGiftScore            string
	AppIDAwardScore             string
	SignKeyAwardScore           string
	AppIDMaskScore              string
	SignKeyMaskScore            string
	AppIDAmuseCommission        string
	SignKeyAmuseCommission      string
	AppIDYuyinBaseCommission    string
	SignKeyYuyinAwardCommission string
	AppIDYuyinAwardCommission   string
	SignKeyYuyinBaseCommission  string

	oaConfig OAConfig
}

type OAConfig struct {
	Host         string `json:"host"`
	ClientID     string `json:"client_id"`
	ClientSecret string `json:"client_secret"`
	UserAccount  string `json:"user_account"`
}

func (sc *ServerConfig) Parse(conf beego.Configer) (err error) {
	sc.validateToken = conf.DefaultBool("validate_token", true)
	sc.cors = conf.DefaultBool("cors", false)
	sc.addr = conf.String("addr")
	sc.debugAddr = conf.String("debug_addr")
	sc.IsTest = conf.DefaultBool("test", false)

	sc.commissionURLPrefix = conf.String("commission_url_prefix")
	sc.commissionAppID = conf.String("commission_appid")
	sc.commissionSignKey = conf.String("commission_sign_key")

	sc.AppIDGiftScore = conf.String("appid_gift_score")
	sc.SignKeyGiftScore = conf.String("sign_key_gift_score")
	sc.AppIDAwardScore = conf.String("appid_award_score")
	sc.SignKeyAwardScore = conf.String("sign_key_award_score")
	sc.AppIDMaskScore = conf.String("appid_mask_score")
	sc.SignKeyMaskScore = conf.String("signkey_mask_score")
	sc.AppIDAmuseCommission = conf.String("appid_amuse_commission")
	sc.SignKeyAmuseCommission = conf.String("sign_key_amuse_commission")
	sc.AppIDYuyinBaseCommission = conf.String("appid_yuyin_base_commission")
	sc.SignKeyYuyinAwardCommission = conf.String("sign_key_yuyin_award_commission")
	sc.AppIDYuyinAwardCommission = conf.String("appid_yuyin_award_commission")
	sc.SignKeyYuyinBaseCommission = conf.String("sign_key_yuyin_base_commission")

	sc.oaConfig.Host = conf.String("oa_host")
	sc.oaConfig.ClientID = conf.String("oa_client_id")
	sc.oaConfig.ClientSecret = conf.String("oa_client_secret")
	sc.oaConfig.UserAccount = conf.String("oa_user_account")

	log.Debugf("Parse sc %v", sc)
	return
}

func (sc *ServerConfig) GetValidateToken() bool {
	return sc.validateToken
}

func (sc *ServerConfig) GetCors() bool {
	return sc.cors
}

func (sc *ServerConfig) GetAddr() string {
	return sc.addr
}

func (sc *ServerConfig) GetDebugAddr() string {
	return sc.debugAddr
}

func (sc *ServerConfig) GetCommissionURLPrefix() string {
	return sc.commissionURLPrefix
}

func (sc *ServerConfig) GetCommissionAppID() string {
	return sc.commissionAppID
}

func (sc *ServerConfig) GetCommissionSignKey() string {
	return sc.commissionSignKey
}

func (sc *ServerConfig) GetOAClient() OAConfig {
	return sc.oaConfig
}
