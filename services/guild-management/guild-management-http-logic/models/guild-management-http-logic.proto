syntax = "proto3";

package guild_management_http_logic;

// 签约主播身份类型
// buf:lint:ignore ENUM_PASCAL_CASE
enum SIGN_ANCHOR_IDENTITY {
  SIGN_ANCHOR_IDENTITY_MULTIPLAYER = 0; // 多人互动身份
  SIGN_ANCHOR_IDENTITY_RADIO_LIVE = 1;  // 语音直播身份

  SIGN_ANCHOR_IDENTITY_E_SPORTS = 2;  // 电竞陪玩
}


// 公会每日数据
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GuildLiveDailyStats {
  uint32 broadcastAnchorCnt = 1;   // 开播主播数
  uint32 anchorIncome = 2;          // 主播收入
  uint32 channelFee = 3;            // 直播间收入
  uint32 date = 4;                  // 日期
  uint32 maxQueryCnt = 5;           // 最大可查询天数(按主播查询)
  uint32 validAnchorCnt = 6;        // 有效开播主播数
  uint32 newAddAnchorCnt = 7;       // 新增主播数
  float  channelPkgFeeRatio = 8;   //  直播间背包流水占比
}

// 根据月份获取每天详细数据
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetGuildLiveDailyStatsByMonthReq {
  string uid = 1;
  uint32 guildId = 2;
  uint32 begin_month = 3;    // 起始月 从1开始，本月填1
  uint32 month_cnt = 4;   // 获取月数
}
message GetGuildLiveDailyStatsByMonthResp {
  repeated GuildLiveDailyStats list = 1;
}

// 语音直播公会数据统计
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message DataRatio {
  float sameRatio = 1;   // 同比
  float lastRatio = 2;  // 环比
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GuildLiveStatsInfo {
  uint32 broadcastAnchorCnt = 1;  // 开播主播数
  DataRatio broadcastRatio = 2;   // 同比环比
  uint64 anchorIncome = 3;         // 主播收入
  DataRatio incomeRatio = 4;   // 同比环比
  uint64 channelFee = 5;           // 直播间收入
  DataRatio channelFeeRatio = 6;  // 同比环比
  uint32 validAnchorCnt = 7;       // 有效主播数
  DataRatio validAnchorRatio = 8; // 同比环比
  uint32 addValidAnchorCnt = 9;    // 新增有效主播数
  DataRatio addValidAnchorRatio = 10;  //同比环比
  uint32 newAnchorCnt = 11;        // 新增主播数
  DataRatio newAnchorRatio = 12;  // 同比环比
  uint32 potentialAnchorCnt = 13;  // 潜力主播数
  DataRatio potentialAnchorRatio = 14;  // 同比环比
  uint32  dateTs = 15;                 // 时间
  uint32  endDateTs = 16;            // 周维度需要填周结束时间
  float  channelPkgFeeRatio = 17;  // 直播间背包流水占比
  DataRatio channelPkgFeeDataRatio = 18;  // 直播间背包流水占比 同比和环比
  uint32 professionAnchorPracCnt = 19;  // 专业从业者数
  DataRatio professionAnchorPracRatio = 20;  // 专业从业者数 同比和环比
}

// 获取语音直播公会日数据(带有环比和同比)
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetGuildLiveDailyStatsInfoReq {
  uint32 guildId = 2;   // 公会id
  string uid = 3;   // 会长uid
  uint32 beginTs = 4;  // 开始时间
  uint32 endTs = 5;  // 结束时间
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetGuildLiveDailyStatsInfoResp {
  repeated GuildLiveStatsInfo infoList = 1;
}

// 获取语音直播公会周数据(带有环比和同比)
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetGuildLiveWeekStatsInfoReq {
  uint32 guildId = 2;   // 公会id
  string uid = 3;   // 会长uid
  uint32 beginTs = 4;  // 开始时间
  uint32 endTs = 5;  // 结束时间
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetGuildLiveWeekStatsInfoResp {
  repeated GuildLiveStatsInfo infoList = 1;
}

// 获取语音直播公会月数据(带有环比和同比)
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetGuildLiveMonthStatsInfoReq {
  uint32 guildId = 2;   // 公会id
  string uid = 3;   // 会长uid
  uint32 beginTs = 4;  // 开始时间
  uint32 endTs = 5;  // 结束时间
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetGuildLiveMonthStatsInfoResp {
  repeated GuildLiveStatsInfo infoList = 1;
}

// 公会直播月数据
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GuildLiveMonthlyStats {
  uint32 broadcastAnchorCnt = 1;  // 开播主播数
  uint64 anchorIncome = 2;         // 主播收入
  uint64 channelFee = 3;           // 直播间收入
  uint32 date = 4;                 // 月份日期
  uint32 validAnchorCnt = 5;       // 有效主播数
  uint32 addValidAnchorCnt = 6;    // 新增有效主播数
  uint32 maxQueryCnt = 7;          // 最大可查询月数(按主播查询)
  uint32 newAnchorCnt = 8;        // 新增主播数
  uint32 potentialAnchorCnt = 9;  // 潜力主播数
  uint32 activeAnchorCnt = 10;   // 活跃主播数
  uint32 newActiveAnchorCnt = 11;  // 月新增活跃主播数
  uint32 highQualityAnchorCnt = 12;  // 优质主播数
  float  channelPkgFeeRatio = 13;  // 直播间背包流水占比
  uint32 professionAnchorPracCnt = 14;  // 专业从业者数
  uint32 pureNewAnchorFee = 15;  // 纯新达人流水
  uint32 pureNewAnchorCnt = 16;  // 纯新达人数
  uint32 pureNewActiveAnchorCnt = 17;  // 纯新活跃达人数
  uint32 pureNewProAnchorPracCnt = 18;  // 纯新专业从业者数
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetGuildLiveMonthlyStatsReq {
  string uid = 1;
  uint32 guildId = 2;  // 页码默认最小为1
  uint32 page = 3;
  uint32 pageSize = 4;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetGuildLiveMonthlyStatsResp {
  repeated GuildLiveMonthlyStats list = 1;
  uint32 nextPage = 2;
}

// 主播汇总数据
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message AnchorTotalData {
  string account = 1;    // 账号
  string nickname = 2;   // 昵称
  string tagName = 3;  // 标签名称
  uint32 signTs = 4;  // 签约时间
  uint32 signExpireTs = 5; // 签约到期时间
  uint32 gainIdentifyTs = 6;  // 获得语音直播身份时间
  string agentNickname = 7;  // 经纪人昵称
  uint32 liveValidDayCnt = 8;  // 直播有效天
  float  liveValidHour = 9; // 直播有效时长
  uint64 channelFee = 10;  // 直播间流水
  uint64 anchorIncome = 11; // 主播流水
  uint32 totalFansCnt = 12;  // 累计粉丝数
  uint32 latestLiveTs = 13;  // 最近开播时间
  uint32 dateTs = 14;    //时间日期
  int32  sex = 15;      // 主播性别
  string username = 16;
  string agentTTid = 17;    // 经纪人TTid
  uint32 firstLiveTs = 18;  //首次开播时间戳
  string checkLevel = 19; //考核等级
  float  channelPkgFeeRatio = 20;  // 直播间背包流水占比
  float  anchorPkgIncomeRatio = 21;  // 主播收礼背包占比
  uint32 game_fee = 22; // 互动游戏流水
  uint32 game_min = 23; // 互动游戏时长 分钟
  uint32 game_active_days = 24; // 互动游戏活跃天
  uint32 game_channel_fee = 25; // 互动游戏房间流水
  uint32 virtual_fee = 26; // 虚拟直播流水
  uint32 virtual_income = 27; // 虚拟直播收礼
  uint32 virtual_min = 28; // 虚拟直播时长 分钟
  uint32 virtual_active_days = 29; // 虚拟直播活跃天
  uint32 live_active_days = 30;  // 听听活跃天
  uint32 consumer_cnt = 31;  // 付费人数
  uint32 new_fans_cnt = 32;  // 新增粉丝团数
  bool is_new_anchor = 33; //是否新增达人
  bool is_pure_new_anchor = 34;  // 是否纯新达人
  bool is_pure_new_active = 35;  // 是否纯新活跃达人
  bool is_pure_new_pro = 36;  // 是否纯新专业从业者
  bool is_pro = 37;  //是否专业从业者
  bool is_mature_anchor = 38;  // 是否成熟达人
  bool is_potential_active = 39;  // 是否潜力活跃达人
  repeated uint32 vio_list = 40;  // 违规次数 列表依次是 A,B,C 类
}

enum AnchorType
{
  DeFaultAnchor = 0;   // 默认全部类型 签约主播
  NewSignAnchor = 1;   // 新签约主播
  TodayLiveAnchor = 2; // 今日开播主播
  BreakLiveAnchor = 3;  // 断播主播
  SoonSignExpireAnchor = 4;  // 将到期主播
  MonthPotentailAnchor = 5;  // 月潜力主播
  MonthValidAnchor = 6;  // 月有效主播
  MonthNewValidAnchor = 7; // 月新增有效主播
  MonthLiveAnchor = 8;  // 开播主播
}

enum TotalQueryType
{
  QueryAll = 0;
  QueryByAnchor = 1;  // 根据主播id查询
  QueryByAgent = 2;   // 根据经纪人id查询
}

// 获取主播汇总数据
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetAnchorTotalDataReq {
  uint32 guildId = 1;  // 公会id
  string uid = 2;      // 会长uid 或者 经纪人id
  uint32 anchorType = 3; // see enum AnchorType
  string anchorAccount = 4; // 主播tid
  string agentAccount = 5;  // 经纪人tid
  uint32 beginTs = 6;
  uint32 endTs = 7;
  uint32 offset = 8;
  uint32 limit = 9;
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetAnchorTotalDataResp {
  repeated AnchorTotalData data_list = 1;
  uint32 nextOffset = 2;
  uint32 totalCnt = 3;
}

// 主播明细数据
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message AnchorDetailData {
  string account = 1;    // 账号
  string nickname = 2;   // 昵称
  string tagName = 3;   // 标签名称
  uint32 signTs = 4;     // 签约时间
  uint32 signExpireTs = 5;  // 签约到期时间
  string agentAccount = 6;   // 经纪人
  float  liveHour = 7;    //直播时长
  float  liveValidHour = 8;  // 直播有效时长
  uint32 liveValidDays = 9;  // 有效天数
  uint64 channelFee = 10;  // 直播间流水
  uint64 anchorIncome = 11; // 主播流水
  uint32 activeFansCnt = 12;  //活跃粉丝数
  uint32 sendGiftFansCnt = 13; // 粉丝送礼人数
  uint32 fansSendFee = 14;  // 粉丝送礼产生的流水
  uint32 newFollow = 15;   //新增关注
  uint32 newFans = 16;  // 新增粉丝
  uint32 dateTs = 17;       // 时间
  uint32 endDateTs = 18;    // 周维度需要填周结束时间
  int32  sex = 19;      // 主播性别  性别 0 女 1 男
  string username = 20;
  uint32 uid = 21;
  string agentNickname = 22;    // 经纪人昵称
  uint32 game_fee = 23; // 互动游戏流水
  uint32 game_min = 24; // 互动游戏时长 分钟
  uint32 game_active_days = 25; // 互动游戏活跃天
  uint32 game_channel_fee = 26; // 互动游戏房间流水
  uint32 virtual_fee = 27; // 虚拟直播流水
  uint32 virtual_income = 28; // 虚拟直播收礼
  uint32 virtual_min = 29; // 虚拟直播时长 分钟
  uint32 virtual_active_days = 30; // 虚拟直播活跃天
  uint32 live_active_days = 31;  // 听听活跃天
  uint32 consumer_cnt = 32;  // 付费人数
  uint32 new_fans_cnt = 33;  // 新增粉丝团
  repeated uint32 vio_list = 34;  // 违规次数 列表依次是 A,B,C 类
}

// 根据日维度获取主播明细数据
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetAnchorDayDetailDataReq {
  uint32 guildId = 1;   // 公会id
  string uid = 2;   // 会长uid
  string anchorAccount = 3;   // 主播tid
  string agentAccount = 4;    // 经纪人tid
  uint32 beginTs = 5;  // 开始时间
  uint32 endTs = 6;  // 结束时间
  uint32 offset = 7;
  uint32 limit = 8;
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetAnchorDayDetailDataResp {
  repeated AnchorDetailData data_list = 1;
  uint32 nextOffset = 2;
  uint32 totalCnt = 3;
}

// 根据周维度获取主播明细数据
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetAnchorWeekDetailDataReq {
  uint32 guildId = 1;   // 公会id
  string uid = 2;   // 会长uid
  string anchorAccount = 3;   // 主播tid
  string agentAccount = 4;    // 经纪人tid
  uint32 beginTs = 5;  // 开始时间
  uint32 endTs = 6;  // 结束时间
  uint32 offset = 7;
  uint32 limit = 8;
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetAnchorWeekDetailDataResp {
  repeated AnchorDetailData data_list = 1;
  uint32 nextOffset = 2;
  uint32 totalCnt = 3;
}

// 根据月维度获取主播明细数据
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetAnchorMonthDetailDataReq {
  uint32 guildId = 1;   // 公会id
  string uid = 2;   // 会长uid
  string anchorAccount = 3;   // 主播tid
  string agentAccount = 4;    // 经纪人tid
  uint32 beginTs = 5;  // 开始时间
  uint32 endTs = 6;  // 结束时间
  uint32 offset = 7;
  uint32 limit = 8;
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetAnchorMonthDetailDataResp {
  repeated AnchorDetailData data_list = 1;
  uint32 nextOffset = 2;
  uint32 totalCnt = 3;
}

// 根据主播类型获取公会主播的月数据
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetGuildMonthDetailDataByTypeReq {
  uint32 guildId = 1;   // 公会id
  string uid = 2;     // 会长uid
  uint32 anchorType = 3; // 主播类型 see AnchorType
  uint32 offset = 4;
  uint32 limit = 5;
  uint32 monthTs = 6;
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetGuildMonthDetailDataByTypeResp {
  repeated AnchorDetailData data_list = 1;
  uint32 nextOffset = 2;
  uint32 totalCnt = 3;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message AnchorMonthlyStats {
  uint32 uid = 1;
  uint32 channelFee = 2;      // 直播间流水
  uint32 anchorIncome = 3;    // 主播收入
  uint32 dayLiveValidCnt = 4; // 直播有效天
  float  liveValidHour = 5;// 有效直播时长
}

// 获取主播月统计数据
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetAnchorMonthlyStatsReq{
  uint32 guildId = 1;   // 公会id
  string uid = 2;     // 会长uid
  uint32 anchorUid = 3;   // 主播uid
  uint32 monthTs = 4;
}
message GetAnchorMonthlyStatsResp {
  AnchorMonthlyStats stats = 1;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message AnchorWeeklyStats {
  uint32 uid = 1;
  uint32 channelFee = 2;      // 直播间流水
  uint32 beginTs = 3;
  uint32 endTs = 4;
}

// 获取主播周统计数据
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetAnchorWeeklyStatsReq {
  uint32 guildId = 1;   // 公会id
  string uid = 2;     // 会长uid
  uint32 anchorUid = 3;   // 主播uid
  uint32 currTs = 4;   //当前时间
  uint32 weekCnt = 5;   // 从当前时间起向前获取多少周数据
}
message GetAnchorWeeklyStatsResp {
  repeated  AnchorWeeklyStats stats = 1;
}

// 公会任务信息
message GuildTaskInfo {
  uint64 value = 1;    //任务值
  string ratio = 2;   // 任务比例
  uint32 level = 3;   // 满足等级
}

// 公会任务比例信息
message GuildTaskRatioInfo {
  uint64 value = 1;       // 任务值
  string ratio = 2;       // 任务比例
  string extra_ratio = 3; // 额外增加比例
  uint32 level = 4;       // 等级
}

// 公会任务详情
message GuildTaskDetailInfo {
  string name = 1;    // 任务名称
  repeated GuildTaskRatioInfo ratio_list = 2; // 任务比例列表
}

// 获取公会任务信息表
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetGuildTaskListReq {
  uint32 guildId = 1;   // 公会id
  string uid = 2;   // 会长uid
  uint32 beginTs = 3;  // 开始时间
  uint32 endTs = 4;  // 结束时间
}
message GetGuildTaskListResp {
  repeated GuildTaskInfo list = 1;
  int64 remain_time = 2;
  string buff_total = 3;  // 总的比例
  repeated GuildTaskDetailInfo task_detail = 4;  // 任务详情列表
  uint32 is_new_guild = 5;  // 是否是新工会：0：否 1:是
  string new_guild_period = 6; // 新工会生效月份
}

//******  菜单权限 ******//
// 公会经营后台语音直播经营分析菜单栏 移位定义 1,2,4,8...
enum GuildLiveDataMenuType {
  GuildLiveDataInvalid = 0;
  GuildLiveStatsTotalData = 1;   //经营情况总览
  GuildLiveTaskData = 2;   // 公会任务
  GuildAnchorData = 4;   // 主播数据
  AnchorPractitionerAnalysis = 8;  // 从业者分析
  GuildBusinessDiagnosis = 16;   // 经营诊断
  AnchorFaceCheckDetail = 32;   // 从业者本人验证
}

// 公会经营后台经纪人菜单栏 移位定义 1,2,4,8...
enum GuildAgentMenuType {
  GuildAgentInvalid = 0;
  GuildAgentManage = 1;   // 经纪人管理
}

// 公会经营后台收益管理菜单栏 移位定义 1,2,4,8...
enum GuildIncomeMenuType {
  GuildIncomeInvalid = 0;
  GuildIncomeManage = 1;   // 收益管理
}

// 公会经营后台多人互动经营管理菜单栏 移位定义 1,2,4,8...
enum GuildMultiPlayDataType {
  GuildMultiPlayDataInvalid = 0;
  MultiPlayOperationData = 1; // 经营总览
  MultiPlayDetail = 2; // 数据明细
  MultiPlayPractitionerAnalysis = 4;  // 从业者分析
  MultiPlayHallTask = 8; // 承接大厅
  MultiPlayScheduleData = 16; // 接档管理
  MultiAnchorFaceCheckDetail = 32;   // 从业者本人验证
  MultiPlayWeddingReserved = 64;   // 婚礼预约
  MultiPlayBusinessDiag = 128;  // 经营分析
}

// 流量卡管理菜单栏 移位定义 1,2,4,8...
enum FlowCardMenuType {
  FlowCardInValid = 0;
  AnchorFlowCard = 1;   // 主播流量卡
}

// 签约管理菜单栏 移位定义 1,2,4,8...
enum AnchorContractMenuType {
  AnchorContractInValid = 0;
  AnchorContractApplySign = 1; // 签约申请
  AnchorContractCancel = 2;    // 解约申请
  AnchorContractList = 4;      // 签约成员列表
  AnchorContractVio = 8;   // 违规记录
  AnchorContracCancelContractType = 16;  // 解约方式选择
  AnchorContracSignRight = 32;  // 签约权益
}

// 消息管理 移位定义 1,2,4,8...
enum GuildMsgMenuType {
  GuildMsgInValid = 0;
  FuWuHaoMsg = 1;  // 平台公告
}

// 公会管理 移位定义 1,2,4,8...
enum GuildManageMenuType {
  GuildManageInValid = 0;
  PersonnelManage = 1;  // 人员设置
  LiveManage = 2; // 开播管理
}

// 违规记录 移位定义 1,2,4,8...
enum ViolationMenuType {
  ViolationInValid = 0;
  VViolationRecord = 1; // 违规记录
}

enum EsportMenuType {
  EsportMenuTypeValid = 0;
  EsportMenuTypeAudit = 1;// 修改技能审核
  EsportMenuTypePerformanceAnalysis = 2;// 电竞业绩分析
}

//自建活动管理 移位定义 1,2,4,8...
enum GuildSelfActivityMenuType {
  GuildSelfActivityMenuTypeValid = 0;
  GuildSelfActivityMenuTypeManage = 1; // 自建活动管理
}

//首页管理 移位定义 1,2,4,8...
enum HomePageMenuType {
  HomePageMenuTypeInValid = 0;  //无效
  HomePageMenuTypeMulti = 1;  // 多人互动
}

//******  菜单权限 ******//

//****** 数据权限 ******//
// 公会经营分析预览数据类型  移位定义 1,2,4,8...
enum GuildLiveStatsDataType {
  LiveStatsDataInvalid = 0;
  AnchorIncome = 1;  // 主播收入
  ChannelFee = 2;    // 直播间收入
  ChannelPkgFeeRatio = 4;  // 直播间背包流水占比
  PureNewAnchorFee = 8;   // 纯新达人流水
}

// 公会直播任务模块数据类型 移位定义 1,2,4,8...
enum GuildLiveTaskDataType {
  LiveTaskDataInvalid = 0;
}

// 主播数据数据类型 移位定义 1,2,4,8...
enum AnchorDataType {
  AnchorDataInvalid = 0;
  ChannelFeeData = 1;   // 直播间流水
  AnchorIncomeData = 2; // 主播流水
  SignTsData = 4;  // 签约时间
  SignExpireTsData = 8;  // 签约到期时间
  ChannelPkgFeeRatioData = 16;  // 直播间背包流水占比
  AnchorPkgIncomeRatioData = 32; // 主播收礼背包占比
}

// 语音直播从业者数据类型 移位定义 1,2,4,8...
enum AnchorPracDataType {
  AnchorPracDataInvalid = 0;
  PracChannelFee = 1;   // 直播间流水
  PracAnchorIncome = 2; // 主播流水
}

// 多人互动经营管理数据明细数据类型 移位定义 1,2,4,8...
enum MultiPlayDetailDataType {
  MultiPlayDetailDataInvalid = 0;
  MultiPlayDetailSignTs = 1;   // 签约时间
  MultiPlayDetailSignExpireTs = 2; // 签约到期时间
  MultiPlayDetailGuildFee = 4;  // 公会流水（T豆）
  MultiPlayDetailChGiftFee = 8;  // 房间收礼金额（元）
  MultiPlayDetailChWolfFee = 16;  // 房间狼人杀金额（元）
  MultiPlayDetailChannelFee = 32;  // 房间总流水金额（元）
}

// 多人互动经营管理从业者分析数据类型 移位定义 1,2,4,8...
enum MultiPlayPracAnalysisDataType {
  MultiPlayPracAnalysisDataInvalid = 0;
  MultiPlayPracAnalysisGiftFee = 1;   // 收礼金额（元）
  MultiPlayPracAnalysisSignTs = 2; // 签约时间
  MultiPlayPracAnalysisChannelFee = 4;  // 房间流水（元）
  MultiPlayPracAnalysisActiveFee = 8;  // 活跃从业者贡献流水（元）
  MultiPlayPracAnalysisQualityFee = 16;  // 优质从业者贡献流水（元）
  MultiPlayPracAnalysisQualityFeeRat = 32;  // 优质从业者贡献流水占比
  MultiPlayPracAnalysisPracGiftFee= 64;  // 从业者收礼流水（元）
  MultiPlayPracAnalysisPkgFee= 128;  // 包裹礼物流水（元）
  MultiPlayPracAnalysisTbeanGiftFee= 256;  // T豆礼物流水（元）
  MultiPlayPracAnalysisGuildFee= 512;  // 公会月流水（元）
  MultiPlayPracAnalysisChannelPkgFee = 1024; // 房间背包流水占比
  MultiPlayPracAnalysisGuildPkgFee = 2048; // 公会背包流水占比
}

//****** 数据权限 ******//

//****** 功能权限 ******//
// 签约申请功能权限 移位定义 1,2,4,8...
enum ContractApplySignFuncType {
  ContractApplySignInvalid = 0;
  ContractApplySignQuery = 1;  // 查询
  ContractApplySignApproval = 2;  // 审批
}

// 解约申请功能权限 移位定义 1,2,4,8...
enum ContractCancelFuncType {
  ContractCancelInvalid = 0;
  ContractCancelQuery = 1;  // 查询
  ContractCancelApproval = 2;  // 审批
}

// 签约成员列表功能权限 移位定义 1,2,4,8...
enum ContractListFuncType {
  ContractListInvalid = 0;
  ContractListQuery = 1;  // 查询
  ContractListCancelApproval = 2;  // 解约审批
  ContractListRenewApproval = 4;  // 续约审批
  ContractListExport = 8;  // 导出
  ContractListPromote = 16;  // 晋升管理
}

// 违规记录功能权限 移位定义 1,2,4,8...
enum ContractVioFuncType {
  ContractVioInvalid = 0;
  ContractVioQuery = 1;  // 查询
  ContractVioExport = 2;  // 导出
}

// 语音直播经营情况总览功能权限 移位定义 1,2,4,8...
enum LiveTotalDataFuncType {
  LiveTotalDataInvalid = 0;
  LiveTotalDataQuery = 1;  // 查询
  LiveTotalDataExport = 2;  // 导出
}

// 语音直播公会任务功能权限 移位定义 1,2,4,8...
enum LiveTaskDataFuncType {
  LiveTaskDataFuncInvalid = 0;
  LiveTaskDataQuery = 1;  // 查询
}

// 语音直播主播数据功能权限 移位定义 1,2,4,8...
enum AnchorDataFuncType {
  AnchorDataFuncInvalid = 0;
  AnchorDataQuery = 1;  // 查询
  AnchorDataExport = 2;  // 导出
}

// 语音直播从业者分析功能权限 移位定义 1,2,4,8...
enum AnchorPracAnalysisFuncType {
  AnchorPracAnalysisInvalid = 0;
  AnchorPracAnalysisQuery = 1;  // 查询
  AnchorPracAnalysisExport = 2;  // 导出
}

// 主播流量卡功能权限 移位定义 1,2,4,8...
enum FlowCardFuncType {
  FlowCardInvalid = 0;
  FlowCardQuery = 1;  // 查询
  FlowCardUse = 2;  // 使用流量卡
  FlowCardGrant = 4;  // 发放流量卡
}

// 多人互动经营管理数据明细功能权限 移位定义 1,2,4,8...
enum MultiPlayDetailFuncType {
  MultiPlayDetailInvalid = 0;
  MultiPlayDetailQuery = 1;  // 查询
  MultiPlayDetailExport = 2;  // 导出
}

// 多人互动经营从业者分析功能权限 移位定义 1,2,4,8...
enum MultiPlayPracAnalysisFuncType {
  MultiPlayPracAnalysisInvalid = 0;
  MultiPlayPracAnalysisQuery = 1;  // 查询
  MultiPlayPracAnalysisExport = 2;  // 导出
}

// 多人互动经营经营总览功能权限 移位定义 1,2,4,8...
enum MultiPlayOperationFuncType {
  MultiPlayOperationInvalid = 0;
  MultiPlayOperationQuery = 1;  // 查询
}

// 消息管理 功能权限 移位定义 1,2,4,8...
enum GuildMsgFuncType {
  GuildMsgInvalid = 0;
  GuildMsgQuery = 1;  // 查询
}


// 承接大厅 功能权限 移位定义 1,2,4,8...
enum MultiPlayHallTaskFuncType {
  MultiPlayHallTaskInvalid = 0;
  MultiPlayHallTaskEdit = 1;  // 配置任务
  MultiPlayHallTaskQuery = 2;  // 厅数据查看
  MultiPlayHallTaskExport = 4;  // 厅数据导出
}

// 接档管理 功能权限 移位定义 1,2,4,8...
enum MultiScheduleDataFuncType {
  MultiScheduleDataInvalid = 0;
  MultiScheduleDataQuery = 1;  // 查询
  MultiScheduleDataExport = 2;  // 导出
  MultiScheduleDataSetAdmin = 4;  // 设置厅管
}

// 电竞经营管理 功能权限 移位定义 1,2,4,8...
enum EsportManageFuncType {
  EsportManageInvalid = 0;
  EsportManageQuery = 1;  //数据查看
  EsportManageEdit = 2;  // 审核权限
}

// 电竞经营管理 业绩分析功能权限 移位定义 1,2,4,8...
enum EsportManagePerformanceFuncType {
  EsportManagePerformanceInvalid = 0;
  EsportManagePerformanceQuery = 1;  //数据查看
  EsportManagePerformanceExport = 2;  // 导出
}

// 开播管理 功能权限 移位定义 1,2,4,8...
enum LiveManageFuncType {
  LiveManageInvalid = 0;
  LiveManageQuery = 1;  // 查看
  LiveManageExport = 2;  // 导出
}

//经营诊断 功能权限 移位定义 1,2,4,8...
enum BusinessDiagnosisFuncType {
  BusinessDiagnosisInvalid = 0;
  BusinessDiagnosisQuery = 1;  // 查看
  BusinessDiagnosisExport = 2;  // 导出
  BusinessDiagnosisAbility = 4;  // 能力分析
  BusinessDiagnosisMgr = 8;   // 经营分析
  BusinessDiagnosisRevenue = 16;   // 营收
  BusinessDiagnosisRecruit = 32;  // 拉新
}

// 语音直播从业者本人验证 功能权限 移位定义 1,2,4,8...
enum LiveAnchorFaceCheckDetailFuncType {
  LiveAnchorFaceCheckDetailInvalid = 0;
  LiveAnchorFaceCheckDetailQuery = 1;  // 查看
  LiveAnchorFaceCheckDetailExport = 2;  // 导出
}

// 多人互动经营从业者本人验证 功能权限 移位定义 1,2,4,8...
enum MultiAnchorFaceCheckDetailFuncType {
  MultiAnchorFaceCheckDetailInvalid = 0;
  MultiAnchorFaceCheckDetailQuery = 1;  // 查看
  MultiAnchorFaceCheckDetailExport = 2;  // 导出
}

// 婚礼预约 功能权限 移位定义 1,2,4,8...
enum MultiPlayWeddingReservedFuncType {
  MultiPlayWeddingReservedInvalid = 0;
  MultiPlayWeddingReservedQuery = 1;  // 查看
  MultiPlayWeddingReservedExport = 2;  // 导出
}

//首页管理多人互动 功能权限 移位定义 1,2,4,8...
enum HomePageMultiFuncType {
  HomePageMultiFuncInValid = 0; //无效
  HomePageMultiFuncGuildMultiKeyData = 1;  //公会多人互动重点数据 
  HomePageMultiFuncBanner = 2; // 公会平台资讯
  HomePageMultiFuncMultiTopChannel = 4;  // 公会top公开厅
}

// 多人互动经营分析 功能权限 移位定义 1,2,4,8...
enum MultiBusinessDiagFuncType {
  MultiBusinessDiagFuncInValid = 0;  //无效
  MultiBusinessDiagFuncRadarChart = 1;  // 能力星图
  MultiBusinessDiagFuncRevenue = 2; // 营收tab
  MultiBusinessDiagFuncRecruit = 4; // 招新tab
  MultiBusinessDiagFuncHatch = 8; // 孵化tab
  MultiBusinessDiagFuncSafety = 16; // 安全tab
  MultiBusinessDiagFuncStability = 32; // 稳定tab
  MultiBusinessDiagFuncRiskResistance = 64; // 抗风险tab
}

//****** 功能权限 ******//


// 经营后台菜单权限
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message MenuPermission {
  uint32  guildLiveDataMenuPer = 1; // 公会经营后台语音直播经营分析菜单栏 see GuildLiveDataMenuType
  uint32  guildAgentMenuPer = 2;   // 公会经营后台经纪人菜单栏 see GuildAgentMenuType
  uint32  guildIncomeMenuPer = 3;   // 公会经营后台收益管理菜单栏 see GuildIncomeMenuType
  uint32  guildMultiPlayDataPer = 4;   // 公会经营后台多人互动经营管理菜单栏 see GuildMultiPlayDataType
  uint32  flowCardMenuPer = 5;       // 流量卡管理菜单栏 see FlowCardMenuType
  uint32  anchorContractMenuPer = 6;  // 签约管理菜单栏 see AnchorContractMenuType
  uint32  guildMsgMenuPer = 7;  //   消息管理 see GuildMsgMenuType
  uint32  guildManageMenuPer = 8;  // 公会管理 see GuildManageMenuType
  uint32  violationMenuPer = 9;  //   违规 see ViolationMenuType  弃用，已移到签约管理栏目
  uint32  esportSkillMenuPer = 10;  // 电竞经营管理 see EsportMenuType
  uint32  guildSelfActivityMenuPer = 11;  // 自建活动管理 see GuildSelfActivityMenuType
  uint32  homePageMenuPer = 12;  // 首页管理 see HomePageMenuType
}

// 直播数据权限配置
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message LiveDataPermission {
  uint32 guildLiveStatsDataPer = 1;  // 公会经营分析预览数据权限 see GuildLiveStatsDataType
  uint32 guildTaskDataPer = 2;   // 公会任务模块数据权限 see GuildTaskDataType
  uint32 anchorDataPer = 3;      // 主播数据权限  see AnchorDataType
  uint32 anchorPracDataPer = 4;   // 主播从业者数据 see AnchorPracDataType
}

// 功能权限配置
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message FunctionPermission {
  uint32 contractApplySignFuncPer = 1;  // 签约申请功能权限 see ContractApplySignFuncType
  uint32 contractCancelFuncPer = 2;  // 解约申请功能权限 see ContractCancelFuncType
  uint32 contractListFuncPer = 3;  // 签约成员列表功能权限 see ContractListFuncType
  uint32 contractVioFuncPer = 4; // 违规记录功能权限 see ContractVioFuncType
  uint32 liveTotalDataFuncPer = 5;  // 语音直播经营情况总览功能权限 see LiveTotalDataFuncType
  uint32 liveTaskDataFuncPer = 6;  // 语音直播公会任务功能权限 see LiveTaskDataFuncType
  uint32 anchorDataFuncPer = 7;  // 语音直播主播数据功能权限 see AnchorDataFuncType
  uint32 anchorPracAnalysisFuncPer = 8; // 语音直播从业者分析功能权限  see AnchorPracAnalysisFuncType
  uint32 flowCardFuncPer = 9; // 主播流量卡功能权限 see FlowCardFuncType
  uint32 multiPlayDetailFuncPer = 10;  // 多人互动经营管理数据明细功能权限 see MultiPlayDetailFuncType
  uint32 multiPlayPracAnalysisFuncPer = 11;  // 多人互动经营从业者分析功能权限  see MultiPlayPracAnalysisFuncType
  uint32 guildMsgFuncPer = 12;  // 消息管理 功能权限 see GuildMsgFuncType
  uint32 multiPlayOperationFuncPer = 13;  // 多人互动经营经营总览 功能权限 see MultiPlayOperationFuncType
  uint32 multiPlayHallTaskFuncPer = 14; // 承接大厅 see MultiPlayHallTaskFuncType
  uint32 liveManageFuncPer = 15;  // 开播管理 see LiveManageFuncType
  uint32 esportManageFuncPer = 16;  // 电竞经营管理（技能审核） see EsportManageFuncType
  uint32 esportManagePerformanceFuncPer = 17;  // 电竞经营管理（业绩分析） see EsportManagePerformanceFuncType
  uint32 multiScheduleDataFuncPer = 18;  // 多人互动经营管理接档管理功能权限 see MultiScheduleDataFuncType
  uint32 businessDiagnosisFuncPer = 19;   //经营诊断 功能权限         see BusinessDiagnosisFuncType
  uint32 liveAnchorFaceCheckDetailFuncPer = 20;   //语音从业者本人验证 功能权限        see LiveAnchorFaceCheckDetailFuncType
  uint32 multiAnchorFaceCheckDetailFuncPer = 21;   //多人互动从业者本人验证 功能权限        see MultiAnchorFaceCheckDetailFuncType
  uint32 multiWeddingReservedFuncPer = 22;   //婚礼预约 功能权限       see MultiPlayWeddingReservedFuncType
  uint32 homePageMultiFuncPer = 23;  // 首页管理多人互动 功能权限 see HomePageMultiFuncType 
  uint32 multiBusinessDiagFunc = 24; // 多人互动经营分析 功能权限 see MultiBusinessDiagFuncType
}

// 数据权限配置
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message DataPermission {
  uint32 guildLiveStatsDataPer = 1;  // 公会经营分析预览数据权限 see GuildLiveStatsDataType
  uint32 guildTaskDataPer = 2;   // 公会任务模块数据权限 see GuildTaskDataType
  uint32 anchorDataPer = 3;      // 主播数据权限  see AnchorDataType
  uint32 anchorPracDataPer = 4;   // 主播从业者数据 see AnchorPracDataType
  uint32 multiPlayDetailDataPer = 5; // 多人互动经营管理数据明细数据 see MultiPlayDetailDataType
  uint32 MultiPlayPracAnalysisDataPer = 6; // 多人互动经营管理从业者分析数据 see MultiPlayPracAnalysisDataType
}

// 公会经营后台权限
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GuildManagePermission {
  MenuPermission guildManageMenuPer = 1;  // 菜单权限
  LiveDataPermission liveDataPermission = 2;  // 语音直播经营分析数据权限 新的使用DataPermission，准备弃用这个字段
  FunctionPermission  functionPermission = 3;  // 功能权限
  DataPermission  dataPermission = 4;   // 数据权限 新的字段
}

// 获取用户的公会经营后台相关权限
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetUserGuildManagePermisReq{
  uint32 guildId = 1;
}
message GetUserGuildManagePermisResp{
  GuildManagePermission  permission = 1;
}

// 采用移位定义， 0,1,2,4,8...
// 公会代理人类型
enum AgentType {
  InValidAgent = 0; // 无效
  AgentBroker = 1;  // 经纪人
  AgentAdmin = 2;  // 管理员
}

// 公会经纪人
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GuildAgent {
  uint32 uid = 1;
  string ttid = 2;
  string nickname = 3;
  uint32 addTs = 4;  // 添加时间
  GuildManagePermission permission = 5;  // 权限
  uint32 agent_type = 6;  // 角色类型 see AgentType 拥有经纪人和管理员身份值为3
  uint32 admin_add_ts = 7;  // 管理员添加时间
}

// 获取公会代理人列表
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetGuildAgentListReq{
  uint32 guildId = 1;   // 公会id
  uint32 uid = 2;   // 会长uid
  repeated string agent_ttid_list = 3;  // 指定代理tid查询
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetGuildAgentListResp{
  repeated GuildAgent agentList = 1;
}

// 获取公会代理人信息
message GetGuildAgentInfoReq {
  uint32 guild_id = 1;   // 公会id
  string ttid = 2;
}
message GetGuildAgentInfoResp {
  string username = 1;  // 获取头像
  string nickname = 2;  // 昵称
  bool is_sign = 3;  // 是否签约本公会
  uint32 this_agent_type = 4; // 本公会角色类型 see AgentType
  uint32 other_agent_type = 5; // 其他公会角色类型 see AgentType
  uint32 uid = 6;
  uint32 invite_agent_type = 7;  // 邀请中代理人类型 see AgentType
}

// 新增公会代理人
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message AddGuildAgentReq {
  uint32 guildId = 1;   // 公会id
  uint32 uid = 2;   // 会长uid
  uint32 agentUid = 3;  // 代理人uid
  GuildManagePermission permission = 4;  // 权限
  uint32 agent_type = 5; // see AgentType
}
message AddGuildAgentResp {
}

// 公会代理人邀请
message GuildAgentInvite {
  uint32 id = 1; // 邀请id
  uint32 uid = 2;
  string ttid = 3;
  string nickname = 4;
  uint32 ts = 5;  // 邀请时间
  uint32 agent_type = 6;  // 角色类型 see AgentType 拥有经纪人和管理员身份值为3
}

// 发送公会代理人邀请
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message SendGuildAgentInviteReq {
  uint32 guildId = 1;   // 公会id
  uint32 agentUid = 2;  // 代理人uid
  uint32 agent_type = 3; // see AgentType
}
message SendGuildAgentInviteResp {
}

// 获取公会代理人邀请列表
message GetGuildAgentInviteListReq {
  uint32 guild_id = 1; // 公会id
  repeated string tid_list = 2;  // tid_list不为空时，根据tid搜索
  uint32 page = 3; // 从1开始
  uint32 page_size = 4;
}
message GetGuildAgentInviteListResp {
  repeated GuildAgentInvite invite_list = 1;
  uint32 next_page = 2;
  uint32 total_cnt = 3;
}

// 撤回邀请
message CancelGuildAgentInviteReq {
  uint32 guild_id = 1;
  uint32 id = 2;  // 邀请id
}
message CancelGuildAgentInviteResp {
}

// 删除公会经纪人
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message DelGuildAgentReq {
  uint32 guildId = 1;   // 公会id
  uint32 uid = 2;   // 会长uid
  uint32 agentUid = 3;  // 经纪人uid
  uint32 agent_type = 4; // see AgentType
}
message DelGuildAgentResp {
}

// 更新经纪人的数据权限
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message UpdateAgentDataPermissionReq {
  uint32 guildId = 1;   // 公会id
  uint32 uid = 2;   // 会长uid
  uint32 agentUid = 3;  // 经纪人uid
  GuildManagePermission permission = 4;  // 权限
}
message UpdateAgentDataPermissionResp {
}

// 主播信息
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message AgentAnchorInfo {
  uint32 uid = 1;
  string ttid = 2;
  string nickname = 3;
  string tag = 4;
  uint32 signTs = 5;  // 签约时间
  uint32 gainIdentifyTs = 6;  // 获得语音直播身份时间
}

// 获取经纪人的主播信息列表
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetAgentAnchorListReq {
  uint32 guildId = 1;   // 公会id
  uint32 uid = 2;       // 会长uid
  uint32 agentUid = 3;
  string anchorTTid = 4;   // 查询指定主播 为空时查指定经纪人的所有主播
  uint32 page = 5;
  uint32 pageSize = 6;
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetAgentAnchorListResp {
  repeated AgentAnchorInfo infoList = 1;
  uint32 nextPage = 2;
  uint32 totalCnt = 3;
}

// 查询主播的经纪人信息
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetAnchorAgentInfoReq {
  uint32 guildId = 1;   // 公会id
  uint32 uid = 2;       // 会长uid
  string anchorTTid = 3;
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetAnchorAgentInfoResp {
  bool isAssigned = 1;  // 是否被分配
  string agentNickname = 2;  // 经纪人昵称
  string anchorNickname = 3;  // 主播昵称
  string anchorUsername = 4;  // 主播ttid
}

message AnchorErrorMsg {
  string ttid = 1;
  string err_msg = 2;
}

// 增加经纪人管理主播
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message AddAgentAnchorReq {
  uint32 guildId = 1;   // 公会id
  uint32 uid = 2;       // 会长uid
  uint32 agentUid = 3;
  repeated string anchorTids = 4;
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message AddAgentAnchorResp {
  repeated AnchorErrorMsg errList = 1;
}

// 删除经纪人管理主播
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message DelAgentAnchorReq {
  uint32 guildId = 1;   // 公会id
  uint32 uid = 2;       // 会长uid
  uint32 agentUid = 3;
  repeated uint32 anchorUids = 4;
}
message DelAgentAnchorResp {
}

enum UserType {
  UserChairman = 0;  // 会长
  UserAgent = 1;  // 经纪人
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message UserInfo {
  uint32 uid = 1;
  string username = 2;
  string nickname = 3;
  string alias = 4;
  uint32 guildId = 5;  // 公会id, 会长是自己所属公会id，经纪人是管理的公会id
  uint32 userType = 6;  // see UserType
}

// 获取用户信息
message GetUserInfoReq {
  enum QueryType {
    QueryByUid = 0;
    QueryByTTid = 1;
  }
  uint32 query_type = 1;
  uint32 uid = 2;
  string ttid = 3;
}
message GetUserInfoResp {
  UserInfo user_info = 1;
}

// BLOCK -结算工单化需求- BEGIN 2022.2.10

//3.1 账户信息
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message AccountInfo {
  string companyName = 1;
  string cardNum = 2 ;
  uint32 bankId = 3;
  string bankName = 4;
  string province = 5;
  string city = 6;
  bool isCrop = 7; // 是否对公
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetAccountInfoReq {//DONE
  uint32 guildId = 1;   // 公会id
  uint32 uid = 2;       // 会长uid
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetAccountInfoResp {
  AccountInfo accountInfo = 1;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message UpdateAccountInfoReq {//DONE
  uint32 guildId = 1;   // 公会id
  uint32 uid = 2;       // 会长uid
  AccountInfo accountInfo = 3;
  string code = 4;
}
message UpdateAccountInfoResp {}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetBankListReq {//DONE
  uint32 guildId = 1;   // 公会id
  uint32 uid = 2;       // 会长uid
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message BankInfo {
  uint32 bankId = 1;
  string code = 2;
  string name = 3;
  string img = 4;
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetBankListResp {
  repeated BankInfo bankList = 1;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message CheckBankcardReq {//DONE
  uint32 guildId = 1;   // 公会id
  uint32 uid = 2;       // 会长uid
  string bankcard = 3;  // 银行卡号
  uint32 bankId = 4;
  string bankName = 5;  // 银行名
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message CheckBankcardResp {
  bool isValid = 1;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GenVerifyCodeReq {//DONE
  uint32 guildId = 1;   // 公会id
  uint32 uid = 2;       // 会长uid
}
message GenVerifyCodeResp {}

//3.2 待结算收益
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetIncomeSummaryReq {//DONE
  uint32 guildId = 1;   // 公会id
  uint32 uid = 2;       // 会长uid
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetIncomeSummaryResp {// 已汇总 等价于 待结算 的意思
  uint64 giftScore = 1;   // 对公礼物积分 待结算 - 对应会长仓库的对公礼物积分
  uint64 awardScore = 2;  // 对公奖励积分 待结算  - 对应会长仓库的对公奖励积分
  uint64 pkScore = 3;     // 对公蒙面pk积分 待结算 - 对应会长仓库的对公蒙面PK积分
  uint64 giftScoreUndo = 4;  // 对公礼物积分 待汇总   - 对应会长仓库的对公礼物积分
  uint64 awardScoreUndo = 5;  // 对公奖励积分 待汇总   - 对应会长仓库的对公奖励积分
  uint64 pkScoreUndo = 6;  // 对公蒙面pk积分 待汇总 - 对应会长仓库的对公蒙面PK积分
  uint64 amuseRoomCommission = 7; // 娱乐房收益 待结算（本月）- 对应会长服务号娱乐房本月收益
  uint64 yuyinBaseCommission = 8; // 语音直播基础收益 待结算（本月）- 对应会长服务号语音直播本月基础收益
  uint64 knightScore = 9; // 对公骑士积分 待结算
  uint64 knightScoreUndo = 10; // 对公骑士积分 待汇总
  uint64 thisMonthAmuseExtra = 11; // 本月多人互动额外奖励
  uint64 lastMonthAmuseExtra = 12; // 上月多人互动额外奖励（1号本月为0展示上月）
  bool not_settle_amuse_extra = 13; // 不结算额外收益
  uint64 interactGameCommission = 14; // 互动游戏收益 待结算（本月）
  uint64 interactGameCommissionExtra = 15; // 互动游戏会长额外奖励 待结算本月
  uint64 esportScore = 16; // 电竞积分 待结算
  uint64 esportScoreUndo = 17; // 电竞积分 待汇总
  uint64 esportCommission = 18; // 电竞收益 待结算（本月）
  bool is_display_esport = 19; // 是否展示电竞相关字段（电竞积分待结算、电竞积分待汇总、电竞收益）
  uint64 giftScoreFreeze = 20; // 对公礼物积分 冻结中
}

// 待结算部分
enum ExchangeType {// refer to protocol/services/exchange/exchange.proto
  UNKNOWN = 0;
  COMM_ANCHOR = 1;  //礼物积分和奖励积分一起
  PK = 2; //蒙面PK积分
  PRESENT = 3;  //礼物积分
  ANCHOR = 4; //奖励积分
}

// 收益统计汇总 - 娱乐房、语音直播基础收益 按天、月
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetIncomeStatsReq {//DONE
  uint32 guildId = 1;   // 公会id
  uint32 uid = 2;       // 会长uid
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetIncomeStatsResp {
  uint64 amuseSummaryDay = 1;
  uint64 amuseSummaryMonth = 2;
  uint64 yuyinSummaryDay = 3;
  uint64 yuyinSummaryMonth = 4;
  uint64 interactGameSummaryDay = 5;
  uint64 interactGameSummaryMonth = 6;
  uint64 esportSummaryDay = 7;
  uint64 esportSummaryMonth = 8;
  //others ...
}

enum TimeFilterUnit {
  BY_DAY = 0;
  BY_WEEK = 1;
  BY_MONTH = 2;
}

enum SettleType {
  Unknown = 0;
  UnSettle = 1; // 未结算数据
  Settled = 2; // 已结算数据
}

// 积分结算单详情
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message ScoreBillDetailItem {
  string anchorName = 1;
  uint32 anchorUid = 2;
  uint64 score = 3;
  string alias = 6;
  string username = 7;
  uint32 guildId = 8; // 公会id
  string date = 9; // 日期
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetScoreBillDetailReq {
  uint32 guildId = 1;   // 公会id
  uint32 uid = 2;       // 会长uid
  uint32 offset = 3;
  uint32 limit = 4;
  uint32 billType = 6;  // SettlementBillType
  string billId = 7;    // 为空表示未结算详情
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetScoreBillDetailResp {
  repeated ScoreBillDetailItem infoList = 1;
}

// 冻结积分结算单详情
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message FreezeScoreItem {
  string anchorName = 1;
  uint32 anchorUid = 2;
  uint64 freezeScore = 3; // 冻结积分
  string alias = 6;
  string username = 7;
  uint32 guildId = 8; // 公会id
  string date = 9; // 日期
  uint32 freezeStatus = 10; // 冻结状态
  int64 freezeTime = 11; // 冻结时间
  int64 unfreezeTime = 12; // 解冻时间
}
// 冻结状态
enum FreezeStatus { // from exchange.MasterFreezeUserData.freeze_status
  UnFreeze = 0; // 未冻结
  PartFreeze = 1; // 部分冻结
  AllFreeze = 2; // 全部冻结
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetFreezeScoreListReq {
  uint32 guildId = 1;   // 公会id
  uint32 uid = 2;       // 会长uid
  uint32 offset = 3;
  uint32 limit = 4;
  uint32 billType = 6;  // SettlementBillType
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetFreezeScoreListResp {
  repeated FreezeScoreItem infoList = 1;
}

// 娱乐房佣金结算单详情
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetAmuseRoomBillDetailReq {
  uint32 guildId = 1;   // 公会id
  uint32 uid = 2;       // 会长uid
  uint32 unit = 3;      // enum TimeFilterUnit
  string billId = 5;    // 为空表示未结算详情
  uint32 offset = 6;
  uint32 limit = 7;
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message AmuseRoomBillDetailItem {
  uint32 room_id = 1;
  string room_name = 2;
  uint64 flow = 3;
  uint64 income = 4;
  uint32 guildId = 5; // 公会id短号
  uint32 originGuildId = 6; // 公会id
  string date = 7; // 日期
  uint64 giftFlow = 8; // 礼物总流水
  uint64 giftIncome = 9; // 礼物总收益
  uint64 werewolfFlow = 10; // 狼人杀总流水
  uint64 werewolfIncome = 11; // 狼人杀总收益
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetAmuseRoomBillDetailResp {
  repeated AmuseRoomBillDetailItem infoList = 1;
  repeated DeductItem deductList = 2;
}

// 多人互动额外奖励结算单详情
message GetAmuseExtraBillDetailReq {
  uint32 guild_id = 1; // 公会id
  uint32 uid = 2;     // 会长uid
  string bill_id = 5;  // 为空表示未结算详情
  uint32 year_month = 4; // 202201
}
message GetAmuseExtraBillDetailResp {
  uint32 year_month = 1; // 202201
  uint64 this_month_fee = 2; // 本月流水（T豆）
  uint64 last_month_fee = 3; // 上月流水（T豆）
  uint64 this_month_income = 4; // 本月收益（金钻）
  string this_month_income_cny = 5; // 本月收益（元）
  uint64 prepaid_money = 6; // 预付金额（分）
  string prepaid_money_cny = 7; // 预付金额（元）
  string remark = 8; // 备注
  string grow_rate = 9; // 增长率
  repeated ChannelItem channel_list = 10;
  uint32 settle_start = 11;
  uint32 settle_end = 12;

  message ChannelItem {
    uint32 channel_id = 1;
    uint32 channel_display_id = 2;
    string channel_name = 3;
    string channel_tag = 4;
    uint64 this_month_fee = 5; // 本月流水（T豆）
    string settlement_rate = 6;
    uint64 this_month_income = 7; // 本月收益（金钻）
    uint32 guild_id = 8;
    uint32 guild_display_id = 9;
    uint64 last_month_fee = 10; // 上月流水（T豆）
    string grow_rate = 11;
    string guild_name = 12;
    string channel_view_id = 13;
  }
}

// 语音基础佣金结算单详情
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetYuyinBaseBillDetailReq {
  uint32 guildId = 1; // 公会id
  uint32 uid = 2;     // 会长uid
  uint32 unit = 3;    // enum TimeFilterUnit
  uint32 offset = 4;
  uint32 limit = 5;
  string billId = 7;  // 为空表示未结算详情
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message YuyinBaseBillDetailItem {
  uint32 anchorUid = 1;
  string anchorName = 2;
  uint64 flow = 3; // 总流水
  uint64 income = 4; // 总收益
  string username = 5;
  string alias = 6;
  uint32 guildId = 7; // 公会id短号
  uint32 originGuildId = 8; // 公会id
  string date = 9; // 日期
  uint64 giftFlow = 10; // 礼物总流水
  uint64 giftIncome = 11; // 礼物总收益
  uint64 knightFlow = 12; // 骑士总流水
  uint64 knightIncome = 13; // 骑士总收益
  uint64 virtualLiveFlow = 14; // 虚拟直播总流水
  uint64 virtualLiveIncome = 15; // 虚拟直播总收益
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message DeductItem {
  uint64 deductMoney = 1;
  string remark = 2;
  uint32 settlementDate = 3;
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetYuyinBaseBillDetailResp {
  repeated YuyinBaseBillDetailItem infoList = 1;
  repeated DeductItem deductList = 2;
}

// 获取语音额外奖励结算单详情
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetYuyinExtraBillDetailReq {
  uint32 guildId = 1;   // 公会id
  uint32 uid = 2;       // 会长uid
  string billId = 7;    // 为空表示未结算详情
}
//额外收益+当月公会任务奖励分成比例 的记录 \ 额外收益记录
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message YuyinExtraBillDetailItem {
  uint64 month_record_income = 1;
  uint32 extra_income_ratio = 2;
  string key = 3;
  string extra_income_ratio_v2 = 4;
  uint32 guildId = 5; // 公会id短号
  uint32 originGuildId = 6; // 公会id
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetYuyinExtraBillDetailResp {
  repeated YuyinExtraBillDetailItem infoList = 1;
}

// 获取深度合作结算单详情
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetDeepCoopBillDetailReq {// DONE
  uint32 guildId = 1;   // 公会id
  uint32 uid = 2;       // 会长uid
  uint32 offset = 3;
  uint32 limit = 4;
  uint32 yearMonth = 5;         // demo:202001
  string billId = 6;
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message DeepCoopMonthInfo {
  uint32 yearMonth = 1;         // demo:202001
  uint64 totalFlowThisMonth = 2; // 本月总流水
  uint64 totalFlowLastMonth = 3; // 上月总流水
  int32 growthRate = 4;    // 增长率
  uint64 settleMoney = 5;  // 结算金额
  uint64 prepaidMoney = 6; // 预付金额
  string remarks = 7;      // 运营备注
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message DeepCoopRoomInfo {
  string name = 1;
  string type = 2;
  uint32 guildId = 3;
  string guildName = 4;
  uint64  flowThisMonth = 5;
  uint64  flowLastMonth = 6;
  int32  growthRate = 7; // 增长率
  uint32  settleRate = 8; // 结算比例
  uint64  settleMoney = 9; // 结算金额
  uint32 channel_id = 10;
  uint32 channel_display_id = 11;
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetDeepCoopBillDetailResp {// 结算单明细 - 多人互动深度合作结算单明细
  repeated DeepCoopRoomInfo infoList = 1;// 按月份展示 TODO
  DeepCoopMonthInfo summary = 2;
}

// 获取语音主播补贴详情
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetYuyinAnchorSubsidyDetailReq {
  uint32 guildId = 1;   // 公会id
  uint32 uid = 2;       // 会长uid
  uint32 offset = 3;
  uint32 limit = 4;
  uint32 yearMonth = 5;
  string billId = 7;
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message YuyinAnchorSubsidyInfo {
  uint32 ttid = 1;
  string nickname = 2;
  string tag = 3;
  string anchorFlag = 4;
  string guildName = 5;
  string planDate = 6;
  uint64 giftFlow = 7;
  uint32 validLiveDays = 8;
  uint64 subsidyMoney = 9;
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetYuyinAnchorSubsidyDetailResp {
  uint64  subsidy = 1;
  repeated YuyinAnchorSubsidyInfo infoList = 2;
}

// 获取新公会补贴详情
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetNewGuildSubsidyDetailReq {
  uint32 guildId = 1;   // 公会id
  uint32 uid = 2;       // 会长uid
  uint32 yearMonth = 3;// demo 202010
  string billId = 8;
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetNewGuildSubsidyDetailResp {
  // 按月份、主播+新公会 2*2 展示
  string joinDate = 3;  // 入驻时间
  uint64 flowThisMonth = 4;  // 当月流水
  string subsidyTime = 5;  // 补贴时间
  uint64 subsidyMoney = 6; // 补贴金额
}

// 互动游戏会长佣金结算单详情
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetInteractGameBillDetailReq {
  uint32 guildId = 1; // 公会id
  uint32 uid = 2;     // 会长uid
  uint32 unit = 3;    // enum TimeFilterUnit
  uint32 offset = 4;
  uint32 limit = 5;
  string billId = 7;  // 为空表示未结算详情
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message InteractGameBillDetailItem {
  uint32 anchorUid = 1;
  string anchorName = 2;
  uint64 flow = 3; // 总流水
  uint64 income = 4; // 总收益
  string username = 5;
  string alias = 6;
  uint32 guildId = 7; // 公会id短号
  uint32 originGuildId = 8; // 公会id
  string date = 9; // 日期
  uint64 giftFlow = 10; // 礼物总流水
  uint64 giftIncome = 11; // 礼物总收益
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetInteractGameBillDetailResp {
  repeated InteractGameBillDetailItem infoList = 1;
}

// 电竞会长佣金结算单详情
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetEsportBillDetailReq {
  uint32 guildId = 1; // 公会id
  uint32 uid = 2;     // 会长uid
  uint32 unit = 3;    // enum TimeFilterUnit
  uint32 offset = 4;
  uint32 limit = 5;
  string billId = 7;  // 为空表示未结算详情
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message EsportBillDetailItem {
  uint32 anchorUid = 1;
  string anchorName = 2;
  uint64 flow = 3; // 总流水
  uint64 income = 4; // 总收益
  string username = 5;
  string alias = 6;
  uint32 guildId = 7; // 公会id短号
  uint32 originGuildId = 8; // 公会id
  string date = 9; // 日期
  uint64 esportFlow = 10; // 电竞总流水
  uint64 esportIncome = 11; // 电竞总收益
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetEsportBillDetailResp {
  repeated EsportBillDetailItem infoList = 1;
}

// 确认提现
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message DoWithdrawReq {
  uint32 guildId = 1;  // 公会id
  uint32 uid = 2;      // 会长uid
  string billId = 4;   // 结算单号
}
message DoWithdrawResp {}





// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message DoOperAmuseConfirmReq {// 运营录入 确认
  uint32 guildId = 1;   // 公会id
  uint32 uid = 2;       // 会长uid
}
message DoOperAmuseConfirmResp {}

// 结算单
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message SettlementBillItem {
  uint64 pubGiftScore = 1;// 对公礼物积分
  uint64 pubAwardScore = 2;// 对公奖励积分
  uint64 pubPkScore = 3;// 对公蒙面PK积分
  uint64 amuseCommission = 4;// 娱乐房佣金
  uint64 yuyinCommissionBase = 5;// 语音直播基础佣金
  uint64 yuyinCommissionAward = 7;// 语音直播奖励佣金
  uint64 deepCoopSettle = 8;// 深度合作结算金额
  uint64 prepaidMoney = 9;// 预付金额
  uint64 yuyinAnchorSubsidy = 10;// 语音直播主播补贴
  uint64 yuyinNewGuildSubsidy = 11;// 语音直播新公会补贴
  uint64 amuseDeductMoney = 12;// 娱乐房扣款金额
  uint64 yuyinDeductMoney = 13;// 语音直播扣款金额
  uint64 knightScore = 14; // 对公骑士积分
  uint64 amuseExtra = 15; // 多人互动额外奖励余额
  uint64 interactGameCommission = 16; // 互动游戏收益
  uint64 interactGameCommissionExtra = 17; // 互动游戏会长额外奖励
  uint64 esportScore = 18; // 对公电竞积分
  uint64 esportCommission = 19; // 电竞会长佣金

  string billId = 100; // 结算单ID
  uint64 incomeSum = 101; // 总金额
  uint64 incomePay = 102; // 应付金额
  SettlementBillType billType = 103; // 结算单类型
  string fileId = 104; // 文件ID
  string settleDate = 106; // 账期
  uint32 settleStart = 108; // 结算周期开始时间
  uint32 settleEnd = 109; // 结算周期结束时间
  bool allowWithdraw = 110; // 允许提现
}

// 获取待上传发票结算列表
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetSettlementBillWaitReceiptReq {
  uint32 uid = 1; // 会长ID
  uint32 guildId = 2;   // 公会id
  string receiptBillId = 6; // 发票工单ID，重新发起时使用
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetSettlementBillWaitReceiptResp {
  repeated SettlementBillItem bills = 1; // 结算单列表
  bool disableVerify = 2; // 禁用发票校验的会长
}

// 获取待提现结算单列表
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetSettlementBillWaitWithdrawReq {
  uint32 uid = 1; // 会长ID
  uint32 guildId = 2;   // 公会id
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetSettlementBillWaitWithdrawResp {
  repeated SettlementBillItem bills = 1; // 结算单列表
  uint32 taxCompensationRate = 2; // 税费补偿率
  uint32 compensationPoint = 3; // 补点比例
  uint32 taxRate = 4; // 税点
  string taxCompensationRateText = 5; // 税费补偿率
  string compensationPointText = 6; // 补点比例

}

// 发票文件上传
message UploadReceiptReq {
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message UploadReceiptResp {
  string receiptId = 1; // 发票ID
  int32 verificationResult = 2; // 校验结果 -1 失败 0 成功
  uint64 amount = 4; // 发票金额
  string receiptSign = 5; // 发票标识
  repeated ReceiptErrorInfo errorList = 6;  // 错误列表
}
message ReceiptErrorInfo {
  string title = 1;
  string result = 2;
  string reason = 3;
}

// 获取发票有效总金额
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetReceiptTotalAmountReq {
  repeated string receiptIds = 1; // 发票ID
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetReceiptTotalAmountResp {
  map<string, uint64> list = 1; // 发票ID => 金额
  uint64 totalAmount = 2; // 总金额
  uint64 validTotalAmount = 3; // 有效总金额（不包含失败）
}

// 关联发票与结算单
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message AssociateReceiptsReq {
  uint32 uid = 2;       // 会长uid
  repeated string settlementOrderIDs = 3; // 结算单IDs
  repeated string receiptIDs = 4; // 发票IDs
  repeated string expressOrderIDs = 5; // 运单号IDs
  string receiptBillId = 6; // 发票单ID，重新发起时使用
  uint32 guildId = 7;   // 公会id
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message AssociateReceiptsResp {
  string receiptBillId = 1;
}

// 获取一个发票单关联的发票文件或运单号
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetReceiptListReq {
  string receiptBillId = 1; // 发票单ID
  uint32 guildId = 2;   // 公会id
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetReceiptListResp {
  // 一张发票
  // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
  message ReceiptItem {
    string receiptId = 1; // 灵犀发票ID
    string fileName = 2; // 文件名
    string fileUrl = 3;
  }
  repeated ReceiptItem receipts = 1; // 发票列表
  repeated string expressOrderIDs = 2; // 运单号IDs
}

// 通过发票ID获取下载地址
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetReceiptUrlReq {
  string receiptId = 1; // 灵犀发票ID
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetReceiptUrlResp {
  string fileUrl = 1; // 发票下载地址
}

// 获取发票单列表
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetReceiptBillListReq {
  uint32 uid = 1; // 会长uid
  uint32 offset = 2;
  uint32 limit = 3;
  repeated OAAuditBillStatus status = 4;
  uint32 guildId = 5;   // 公会id
}

// 单个TTFPXXX
// buf:lint:ignore MESSAGE_PASCAL_CASE
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message receiptBill {
  string receiptBillId = 1; // 发票单ID
  uint32 status = 2; // 灵犀状态
  repeated string settlementBillId = 3; // 关联结算单
  string remark = 4; // 信息
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetReceiptBillListResp {
  repeated receiptBill receiptBillList = 1; // 发票单列表
  uint32 total = 2;
}

// 获取发票单关联的结算单数据
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetAssociatedBillItemsReq {
  uint32 uid = 1; // 会长uid
  string receiptBillId = 2; // 发票单ID
  uint32 guildId = 3;   // 公会id
}

message GetAssociatedBillItemsResp {
  repeated SettlementBillItem list = 1;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetWaitWithdrawMonthsReq {
  uint32 uid = 1;
  SettlementBillType billType = 2; // 结算单类型
  uint32 guildId = 3;   // 公会id
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetMonthsBySettleBillIdReq {
  uint32 uid = 1;
  string billId = 2;
  uint32 guildId = 3;   // 公会id
}

message GetWaitWithdrawMonthsResp {
  repeated uint32 months = 1; // 202201...
}

// 结算单类型
enum SettlementBillType
{
  UnKnownBillType = 0;
  GiftScore = 1;          // 对公礼物积分
  AwardScore = 2;         // 对公奖励积分
  MaskPKScore = 3;        // 对公蒙面PK积分
  AmuseCommission = 4;    // 多人互动会长佣金 (娱乐房佣金)
  YuyinBaseCommission = 5;// 语音直播会长佣金 (语音基础佣金)
  MonthMiddle = 6;        // 语音直播会长额外奖励
  DeepCoop = 7;           // 多人互动会长额外奖励 (旧深度合作)
  YuyinSubsidy = 8;       // 语音直播新公会补贴&主播补贴
  KnightScore = 9;        // 对公骑士积分
  AmuseExtra = 10;        // 多人互动会长额外奖励 (新自动结算 2022.11)
  InteractGameCommission = 11; // 互动游戏会长佣金
  InteractGameExtraCommission = 12; // 互动游戏会长额外奖励
  ESportScore = 13; // 对公电竞积分
  ESportCommission = 14; // 电竞会长佣金
}

// 结算单状态
enum SettlementBillStatus
{
  Creating = 0; // ignore
  WaitWithdraw = 1; // 待确认提现（创建完成）
  WaitReceipt = 3; // 待上传发票（确认提现完成）
  WaitAudit = 5; // 待审核（包含OA流程所有审核状态）
  Finished = 7; // 完成（当OA流程完成时）
}

// 灵犀审核状态
enum OAAuditBillStatus
{
  OAUnknown = 0;
  OAFirstViewing = 1; // 初审中
  OAReviewing = 2; // 复审中
  OARefused = 3; // 驳回
  OAFinished = 4; // 已完成
}

enum SettleStatus
{
  ALLSettleStatus = 0; // 所有
  SettleStatusWait = 1; // 未结算
  SettleStatusFinished = 2; // 已结算
}

// 房间经营数据
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetChannelOperationDataReq {
  uint32 uid = 1;
  uint32 guildId = 2; // 公会id
  string channelName = 3; // 房间名称
  uint32 offset = 4;
  uint32 limit = 5;
  repeated string channelIds = 6; // 房间ID
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message ChannelOperationData {
  string channelDisplayID = 1;
  string channelName = 2;
  string channelTag = 3;
  uint64 thisMonthFee = 4; // 房间月流水
  uint64 lastMonthFee = 5; // 上月流水
  uint64 lastMonthSamePeriodFee = 6; // 上月同期流水
  string chainRatio = 7; // 月环比
}
message GetChannelOperationDataResp {
  repeated ChannelOperationData list = 1;
  uint32 total = 2;
}

// 签约成员个人明细数据
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetContractAnchorDetailReq {
  uint32 uid = 1;
  uint32 guildId = 2; // 公会id
  uint64 startTime = 3;
  uint64 endTime = 4;
  repeated string ttid = 5;
  uint32 anchorType = 6; // 成员类型
  uint32 offset = 7;
  uint32 limit = 8;
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message ContractAnchorDetail {
  string month = 1;
  string ttid = 2;
  string nickname = 3;
  uint64 contractStartTime = 4;
  uint64 contractEndTime = 5;
  uint32 monthValidDay = 6; // 月有效接档天数
  uint32 monthValidHour = 7; // 月接档时长（小时）
  uint64 monthFee = 8; // 月流水
  uint64 reg_time = 9; // 注册时间
  string is_new_sign = 10; // 是否本月首次签约
  string is_vaild = 11; // 有效从业者
  string is_pro = 12; // 专业从业者
}
message GetContractAnchorDetailResp {
  repeated ContractAnchorDetail list = 1;
  uint32 total = 2;
}

// 成员各房间接档数据
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetContractAnchorChannelStatReq {
  uint32 uid = 1;
  uint32 guildId = 2; // 公会id
  TimeFilterUnit unit = 3; // 时间单位
  uint64 startTime = 4;
  uint64 endTime = 5;
  repeated string ttid = 6;
  uint32 offset = 7;
  uint32 limit = 8;
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message ContractAnchorChannelStat {
  string date = 1;
  string ttid = 2;
  string nickname = 3;
  string channelDisplayId = 4;
  string channelName = 5;
  string channelTag = 6;
  uint32 validHour = 7; // 接档时长（小时）
  uint32 validDay = 8; // 有效接档天数
  uint32 fee = 9; // 总金额（分）
  uint32 present_fee = 10; // 收礼金额（分）
  uint32 werewolf_fee = 11; // 狼人杀金额（分）
  uint32 channelId = 12;
  string is_new_sign = 13; // 是否本月首次签约
  string is_vaild = 14; // 有效从业者
  string is_pro = 15; // 专业从业者
}
message GetContractAnchorChannelStatResp {
  repeated ContractAnchorChannelStat list = 1;
  uint32 total = 2;
}


//******* 流量卡 **************/
// 推荐等级
enum RecommendLevel
{
  Recommend_Invalid = 0;
  Recommend_Level_S = 1;   // 超级推荐
  Recommend_Level_A = 2;   // 热门推荐
  Recommend_Level_B = 3;   // 普通推荐
}

message GuildFlowCardInfo {
  uint32 grant_id = 1;
  uint32 level = 2;  // see RecommendLevel
  uint32 expire_ts = 3;
  uint32 total_cnt = 4;
  uint32 remain_cnt = 5;   // 剩余数量
}

// 获取公会流量卡列表
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetGuildFlowCardListReq {
  uint32 level = 1;
  uint32 guildId = 2;
  uint32 page = 3;      // 从1开始
  uint32 page_size = 4;
}
message GetGuildFlowCardListResp {
  repeated GuildFlowCardInfo info_list = 1;
  uint32 next_page = 2;  // 为0 结束
  uint32 total_cnt = 3;
}

// 主播流量卡信息
message AnchorInfo {
  uint32 grant_id = 1;
  uint32 level = 2; //see RecommendLevel
  string ttid = 3;
  string nickname = 4;
  string tag = 5;
  uint32 grant_ts = 6;
  uint32 total_cnt = 7;
  uint32 remain_cnt = 8;   // 剩余数量
  uint32 guild_id = 9;  // 签约公会id
  uint32 uid = 10;
  string username = 11;
  bool   is_valid_ttid = 12;  // 是否是有效ttid
  bool   is_sign_anchor = 13;  // 是否是本公会签约主播
}

// 批量获取主播信息
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message BatGetAnchorInfoReq {
  uint32 guildId = 1;
  repeated string ttid_list = 2;
}
message BatGetAnchorInfoResp {
  repeated AnchorInfo info_list = 1;
}

// 获取公会的主播流量卡信息
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetGuildAnchorFlowCardListReq {
  uint32 guildId = 1;
  uint32 grant_id = 2;
  string anchor_id = 3;   // id不为空，指定id查询
  uint32 page = 4;   // 从 1 开始
  uint32 page_size = 5;
  uint32 level = 6;
}
message GetGuildAnchorFlowCardListResp {
  repeated AnchorInfo info_list = 1;
  uint32 next_page = 2;  // 为0 表示结束
  uint32 total_cnt = 3;
}

message ErrMsg {
  string ttid = 1;
  int32 err_code = 2;
  string err_msg = 3;
}

// 发放信息
message AnchorGrantInfo {
  uint32 anchor_uid = 1;
  uint32 cnt = 2;
  string ttid = 3;
}

// 公会发放主播流量卡
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GrantAnchorFlowCardByGuildReq {
  uint32 guildId = 1;
  uint32 grant_id = 2;
  repeated AnchorGrantInfo info_list = 3;
}
message GrantAnchorFlowCardByGuildResp {
  repeated ErrMsg err_list = 1;
}

message HourLimit {
  uint32 begin_ts = 1;
  uint32 end_ts = 2;
  uint32 cnt = 3;
}
// 获取流量卡小时使用限额信息
message GetFlowCardHourLimitReq {
  uint32 level = 1;   // see RecommendLevel
}
message GetFlowCardHourLimitResp {
  repeated  HourLimit list = 1;
}

//公会对主播使用流量卡功能
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message UseFlowCardByGuildReq {
  uint32 guildId = 1;
  uint32 grant_id = 2;
  uint32 anchor_uid = 3;
  uint32 begin_ts = 4;
}
message UseFlowCardByGuildResp {
}


//=============================================================
/*签约相关*/

// 公会审批状态
// buf:lint:ignore ENUM_PASCAL_CASE
enum APPLY_SIGN_GUILD_STATUS {
  APPLY_SIGN_GUILD_STATUS_PRESIDENT_HANDLING = 0; // 公会未审批
  APPLY_SIGN_GUILD_STATUS_PRESIDENT_ACCEPT = 1; // 公会同意
  APPLY_SIGN_GUILD_STATUS_PRESIDENT_REJECT = 2; // 公会拒绝
}

// 官方审批状态
// buf:lint:ignore ENUM_PASCAL_CASE
enum APPLY_SIGN_OFFICIAL_STATUS {
  APPLY_SIGN_OFFICIAL_STATUS_NONE = 0; // 未到官方审批流程
  APPLY_SIGN_OFFICIAL_STATUS_HANDLING = 1; // 官方待审批
  APPLY_SIGN_OFFICIAL_STATUS_ACCEPT = 2; // 官方同意
  APPLY_SIGN_OFFICIAL_STATUS_REJECT = 3; // 官方拒绝
}

// buf:lint:ignore ENUM_PASCAL_CASE
enum QUERY_APPLY_SIGN_TYPE {
  QUERY_APPLY_SIGN_TYPE_ALL = 0; // 全部申请
  QUERY_APPLY_SIGN_TYPE_HISTORY = 1; // 历史记录
}

// 签约申请信息
message ApplySignExtInfo {
  uint32 apply_id = 1;    // 签约申请记录id

  uint32 anchor_uid = 2;
  string anchor_ttid = 3;
  string anchor_account = 4;
  string anchor_nickname = 5;
  uint32 anchor_sex = 6; // 0-女，1-男

  uint32 guild_id = 7;
  uint32 apply_identity = 8; // 申请身份类型 SIGN_ANCHOR_IDENTITY
  uint32 apply_time = 9; // 申请时间
  uint32 contract_duration = 10; // 签约时长 （月）
  uint32 contract_expire_time = 11; // 合约到期时间，如果此字段非0，即已签约，为申请身份

  uint32 guild_op_status = 12; // 公会处理状态 APPLY_SIGN_GUILD_STATUS
  uint32 official_op_status = 13; // 官方处理状态 APPLY_SIGN_OFFICIAL_STATUS

  uint32 update_time = 14;
}


message GetGuildApplySignRecordListReq {
  uint32          query_type = 1; // QUERY_APPLY_SIGN_TYPE
  uint32          guild_id = 2;
  repeated string ttid_list = 3;
  uint32          page = 4; // 第一页从0开始
  uint32          page_num = 5;
}

message GetGuildApplySignRecordListResp {
  uint32 total = 1;
  repeated ApplySignExtInfo info_list = 2;
  uint32 audio_limit_status=3;
}


message PresidentHandleApplySignReq {
  uint32 apply_id = 1;    // 签约申请记录id
  uint32 handle_opr = 2;  // see HANDLE_SIGN_APPLY_OPR
  uint32 guild_id = 3;
}

message PresidentHandleApplySignResp {}

message BatchPresidentHandleApplySignReq {
  repeated uint32 apply_id = 1;    // 签约申请记录id
  uint32 guild_id = 2;
}

message BatchPresidentHandleApplySignResp {
  uint32 total = 1;
  uint32 fail_cnt = 2;
  string fail_msg = 3;
}

// 解约

// buf:lint:ignore ENUM_PASCAL_CASE
enum QUERY_CANCEL_SIGN_TYPE {
  QUERY_CANCEL_SIGN_TYPE_NOW = 0; // 解约申请
  QUERY_CANCEL_SIGN_TYPE_HISTORY = 1; // 解约记录
}

// 按钮可操作类型 移位定义 1,2,4,8....
enum CancelSignOperType {
  CancelSignOperType_NO = 0;   //不可操作
  CancelSignOperType_AGREE = 1;   //同意
  CancelSignOperType_REJECT = 2;  // 拒绝
}

// 解约原因
// buf:lint:ignore ENUM_PASCAL_CASE
enum CANCEL_REASON_TYPE {
  CANCEL_REASON_TYPE_UNKNOW = 0; // 未知原因
  CANCEL_REASON_TYPE_DAY7_NOREASON = 1; // 7天无理由
  CANCEL_REASON_TYPE_PERSON_INCOME = 2; // 个人收益不达标
  CANCEL_REASON_TYPE_GUILD_INCOME = 3; // 公会房间流水不达标
  CANCEL_REASON_TYPE_LIVE_INCOME_ACTIVE=4;//直播收益&活跃不达标
  CANCEL_REASON_TYPE_NEGOTIATE = 5; // 协商解约
  CANCEL_REASON_TYPE_AGREE_ON = 6; // 约定解约
  CANCEL_REASON_TYPE_PAY = 7; // 付费解约
}


message GetGuildCancelSignRecordListReq {
  uint32          query_type = 1; // QUERY_CANCEL_SIGN_TYPE
  uint32          guild_id = 2;
  repeated string ttid_list = 3;
  uint32          page = 4; // 第一页从0开始
  uint32          page_num = 5;
  uint32          cancel_type = 6; // 解约类型
  uint32          start_time = 7; // 开始时间
  uint32          end_time = 8; // 结束时间
}

message GetGuildCancelSignRecordListResp {
  uint32 total = 1;
  repeated CancelSignExtInfo info_list = 2;
}

message CancelSignExtInfo {
  uint32 apply_id = 1;    // 解约申请记录id

  uint32 anchor_uid = 2;
  string anchor_ttid = 3;
  string anchor_account = 4;
  string anchor_nickname = 5;
  uint32 anchor_sex = 6; // 0-女，1-男

  uint32 guild_id = 7;
  uint32 identity = 8; //  SIGN_ANCHOR_IDENTITY

  string tag_name = 9;

  uint32 agent_uid = 10; // 经纪人uid
  string agent_ttid = 11;
  string agent_nickname = 12;

  repeated AnchorIdentityInfo identity_info_list = 13; // 身份信息
  uint32 sign_time = 14; // 签约时间
  uint32 contract_expire_time = 15; // 签约到期时间

  uint32 apply_time = 16; // 申请解约时间
  uint32 reason = 17; // 解约原因 CANCEL_REASON_TYPE
  uint32 status = 18; // 解约状态
  uint32 cancel_time = 19; // 解约生效时间

  repeated uint32 reason_list=20; // 解约原因 CANCEL_REASON_TYPE

  uint32 oper_type = 21;  // 可操作类型 see CancelSignOperType  3表示可同意和可拒绝
  string result_desc = 22;  //审批结果描述
  uint32 oper_time = 23;  //操作时间
  string reject_txt = 24; //会长拒绝原因
  repeated string proof_urls = 25;  // 证据url （付费解约、会长上传urls）
  string official_notes = 26; //官方处理备注
  string pay_desc = 27;  // 付费解约描述(申请者上传)
  int64  pay_amount = 28; // 付费解约金额(分)
  repeated string proof_video_urls = 29;  // 视频证据url （付费解约、会长上传urls）
  uint32  worker_type = 30; // 从业者类型 see anchorcontract-go.proto的 ContractWorkerType

  repeated ProofShowContent proof_list = 31; //用户提供的证据列表，图片/视频
  string cancel_reason = 32; //用户解约原因文本
}

message ProofShowContent {
  enum ProofType{
    ProofType_Invalid = 0;
    ProofType_Video = 1; //视频
    ProofType_Image = 2; //图片
  }
  string url = 1;
  ProofType type = 2; // 证据类型
}

message AnchorIdentityInfo {
  uint32 actor_uid = 1;
  uint32 identity_type = 2; // see SIGN_ANCHOR_IDENTITY
  uint32 guild_id = 3;
  uint32 obtain_time = 4; // 身份获得时间
}


//处理解约申请
message AcceptCancelContractApplyReq {
  uint32 guild_id = 1;
  uint32 anchor_uid = 2;
  uint32 handle_oper_type = 3;  // 申请处理 see  anchorcontract-go.proto的 ContractWorkerType 的 CANCEL_APPLY_OPR, 1:拒绝， 2：同意
  string handle_desc = 4;  // 处理描述
  repeated string handle_proof_urls = 5;  // 处理证据
  repeated string handle_proof_video_urls = 6;  // 处理证据
  uint32  apply_id = 7; //申请记录ID
}
message AcceptCancelContractApplyResp {
}



// buf:lint:ignore ENUM_PASCAL_CASE
enum QUERY_ANCHOR_TYPE {
  QUERY_ANCHOR_TYPE_ALL = 0; // 全部用户
  QUERY_ANCHOR_TYPE_MYFOCUS = 1; // 我的关注
  QUERY_ANCHOR_TYPE_TODAYLIVE = 2; // 今日开播
  QUERY_ANCHOR_TYPE_NEWSIGN = 3; // 新签约
  QUERY_ANCHOR_TYPE_CANCEL = 4; // 解约预警
  QUERY_ANCHOR_TYPE_READYEXT = 5; // 待续约
}

//晋升邀请按钮
enum PromoteInviteButton {
  PromoteInviteButton_No = 0; // 无按钮
  PromoteInviteButton_Display = 1; // 展示
  PromoteInviteButton_Gray = 2; // 置灰
}

// 签约成员信息
message AnchorExtInfo {
  uint32 anchor_uid = 1;
  string anchor_ttid = 2;
  string anchor_account = 3;
  string anchor_nickname = 4;
  uint32 anchor_sex = 5; // 0-女，1-男

  uint32 guild_id = 6;
  repeated AnchorIdentityInfo identity_info_list = 7; // 身份信息
  string tag_name = 8;

  uint32 agent_uid = 9; // 经纪人uid
  string agent_ttid = 10;
  string agent_nickname = 11;

  uint32 last_live_at = 12; // 最近开播时间
  double day30_live_hour = 13; // 30日直播时长
  uint32 day30_live_valid_day = 14; // 30日有效直播天数
  uint32 day30_hold_valid_day = 15; // 30日有效接档天数

  uint32 sign_time = 16; // 签约时间
  uint32 contract_expire_time = 17; // 签约到期时间

  bool is_focus = 18; // 会长是否关注
  bool is_extension_contract = 19; // 是否可以续约
  bool is_cancal_contract = 20; // 是否可以解约

  string remark = 21; // 备注

  uint32 invite_button = 22;  //晋升管理按钮 see PromoteInviteButton
  uint32 prac_type = 23;  //从业者类型 see anchorcontract-go.proto的 ContractWorkerType
  int64  pay_amount = 24; // 核心管理 付费解约金额(分)
}

message GetGuildAnchorExtInfoListReq {
  uint32          query_type = 1; // QUERY_ANCHOR_TYPE
  uint32          guild_id = 2;
  repeated string anchor_ttid_list = 3;
  repeated string agent_ttid_list = 4;
  uint32          page = 5; // 第一页从0开始
  uint32          page_num = 6;
  bool            is_all_agent = 7;
}

message GetGuildAnchorExtInfoListResp {
  uint32 total = 1;
  repeated AnchorExtInfo info_list = 2;
}

//邀请晋升 /anchor-contract/InvitePromote
message InvitePromoteReq{
  uint32 guild_id = 1;
  uint32 invited_uid = 2;  //被邀请用户
  uint32 cancel_amount = 3;  //解约金额 单位 元
  uint32 sign_months = 4;  // 重新签约月数
}
message InvitePromoteResp {
}


message HandleFocusAnchorReq {
  uint32 guild_id = 1;
  uint32 anchor_uid = 2;
  uint32 handle_opr = 3; // 1-关注，0 取消关注
}
message EmptyResp {
}
message ErrResp {
  string err_msg = 1;
}

message UpdateRemarkReq {
  uint32 guild_id = 1;
  uint32 anchor_uid = 2;
  string remark = 3;
}

message GuildExtensionContractReq {
  uint32 guild_id = 1;
  uint32 anchor_uid = 2;
}
message BatchGuildExtensionContractReq{
  uint32 guild_id = 1;
  repeated uint32 anchor_uid_list = 2;
}

message CancelAnchorContractReq {
  uint32 guild_id = 1;
  uint32 anchor_uid = 2;
}
message BatchCancelAnchorContractReq{
  uint32 guild_id = 1;
  repeated uint32 anchor_uid_list = 2;
  string code = 3;
}

message CheckVerifyCoderReq {
  uint32 guild_id = 1;
  string code = 3;
}

//公众号消息体
message OfficialMessageBody {
  string title = 1;  // 标题
  string content = 2;  //内容
  string intro = 3;   //简介
  string img_url = 4;   // 图片链接
  string jump_url = 5;  // 跳转链接
  string high_light = 6;  // 高亮
}

//公众号消息
message OfficialMessage {
  enum OfficialMessageType{
    INVALID = 0; //  无效
    TEXT = 1;  // 纯文本
    PICTURE_AND_TEXT_SINGLE = 2;  // 单消息图文
    TEXT_JUMP = 3;  // 高亮跳转
  }
  uint32 message_type = 1;
  uint32 seq_id = 2;  // 消息id
  OfficialMessageBody msg_body = 3;  // 消息体
  uint32 date_ts = 4;  //时间
}

// 获取会长服务号消息列表
message GetFuWuHaoMsgListReq {
  uint32 guild_id = 1;
}
message GetFuWuHaoMsgListResp {
  repeated OfficialMessage msg_list = 1;
}

// 根据消息id获取消息内容
message GetFuWuHaoMsgListByIdReq {
  uint32 seq_id = 1;
  uint32 guild_id = 2;
}
message GetFuWuHaoMsgListByIdResp {
  OfficialMessage msg = 1;
}


// ****** 从业者分析 begin ****** //

// 能力维度
enum  AbilityType {
  Ability_Invalid = 0;  // 无
  Ability_Quality = 1;  // 优质
  Ability_Potential = 2; // 潜力
}

// 违规类型
enum ViolationsType {
  Violations_Invalid = 0;  // 无
  Violations_A = 1;  // A类违规
  Violations_B = 2;  // B类违规
  Violations_C = 3;  // C类违规
}

// 违规信息
message ViolationsInfo {
  uint32 violations_type = 1;  // see ViolationsType
  uint32 violations_cnt = 2;
  repeated uint32 ts_list = 3;  // 违规时间列表
}

// 从业者基础信息
message PractitionerBaseInfo {
  uint32  uid = 1;
  string  nickname = 2;
  string  ttid = 3;
  uint32  ability_type = 4;  // 能力维度 see AbilityType
  uint32  sign_ts = 5; // 签约时间
  uint32  income = 6;  // 收礼金额
  repeated ViolationsInfo violations_list = 7;  // 违规信息
  float   pkg_fee_ratio = 8; // 背包流水占比
  string  is_new_sign = 9;  // 是否本月首次签约
  string  is_vaild = 10; // 有效从业者
  string  is_pro = 11; // 专业从业者
}

// 从业者月信息
message PractitionerMonthLyInfo {
  uint32  ts = 1;  // 时间
  PractitionerBaseInfo base_info = 2;
  repeated uint32 active_day_list = 3;  // 活跃天数 0:7h 1:5h
  uint32 valid_live_days = 4;  // 接档天数
  uint32 consume_relation_chains = 5;  // 付费关系链数量
}

// 获取从业者月信息列表
message GetPractitionerMonthLyInfoListReq {
  uint32 month_ts = 1;
  repeated string ttid_list = 2;  // 不为空时指定id查询
  uint32 page = 3;  // 从1开始
  uint32 page_size = 4;
  uint32 guild_id = 5;
}
message GetPractitionerMonthLyInfoListResp {
  repeated PractitionerMonthLyInfo info_list = 1;
  uint32 total_cnt = 2;  // 总数
  uint32 next_page = 3;  // 为0时表示结束
}

// 从业者日信息
message PractitionerDailyInfo {
  uint32 ts = 1;  // 时间
  PractitionerBaseInfo base_info = 2;
  uint32 online_ts = 3;  // 在线时长
  uint32 valid_live_ts = 4;  // 接档时长 秒
  uint32 valid_cmsg_cnt = 5; // 接档公屏互动数（公屏打字条数）
  uint32 valid_sender_cnt = 6;  // 接档送礼人数
}

// 获取从业者日信息列表
message GetPractitionerDailyInfoListReq {
  uint32 begin_ts = 1; // 开始时间
  uint32 end_ts = 2;  // 结束时间
  repeated string ttid_list = 3;  // 不为空时指定id查询
  uint32 page = 4;  // 从1开始
  uint32 page_size = 5;
  uint32 guild_id = 6;
}
message GetPractitionerDailyInfoListResp {
  repeated PractitionerDailyInfo info_list = 1;
  uint32 total_cnt = 2; // 总数
  uint32 next_page = 3; // 为0时表示结束
}

// 公会公开房基础信息
message PgcBaseInfo {
  uint32 cid = 1;
  string display_id = 2;
  string name = 3;
  string channel_tag = 4; // 房间标签
  uint64 channel_fee = 5;  // 房间流水
}

// 公会公开房月信息
message PgcMonthlyInfo {
  uint32 ts = 1;
  PgcBaseInfo base_info = 2;
  uint32 valid_anchor_cnt = 3;  // 有效接档成员数
  uint32 active_anchor_cnt = 4;  // 活跃从业者
  uint32 quality_anchor_cnt = 5; // 优质从业者
  uint64 active_anchor_fee = 6;  // 活跃从业者贡献流水
  uint64 quality_anchor_fee = 7; // 优质从业者贡献流水
  float  quality_anchor_cnt_ratio = 8;  // 优质从业者数量占比
  float  quality_anchor_fee_ratio = 9;  // 优质从业者贡献流水占比
  float  quality_anchor_trans_ratio = 10;  // 房间优质从业者转化率
  float  channel_pkg_fee_ratio = 11;  // 房间背包流水占比
  uint32 profession_prac_cnt = 12;  // 专业从业者数
  uint32 valid_sign_member_cnt = 13;  // 有效签约成员数
}

// 获取公会公开房月信息列表
message GetPgcMonthlyInfoListReq {
  uint32 month_ts = 1;
  repeated string display_id_list = 2;  // 不为空时指定id查询
  uint32 page = 3;  // 从1开始
  uint32 page_size = 4;
  uint32 guild_id = 5;
}
message GetPgcMonthlyInfoListResp {
  repeated PgcMonthlyInfo info_list = 1;
  uint32 total_cnt = 2;  // 总数
  uint32 next_page = 3;  // 为0时表示结束
}

// 公会公开房日信息
message PgcDailyInfo {
  uint32 ts = 1;
  PgcBaseInfo base_info = 2;
  uint32 anchor_fee = 3;  // 从业者收礼流水
  uint32 pkg_gift_fee = 4; // 包裹礼物流水
  uint32 tbean_gift_fee = 5; // T豆礼物流水
  uint32 send_gift_cnt = 6;  // 送礼人数
  repeated uint32 valid_anchor_cnt_list = 7;  // 接档x小时从业者数, 依次是1,2,4
  float  channel_pkg_fee_ratio = 8;  // 房间背包流水占比
}

// 获取公会公开房日信息列表
message GetPgcDailyInfoListReq {
  uint32 begin_ts = 1; // 开始时间
  uint32 end_ts = 2;  // 结束时间
  repeated string display_id_list = 3;  // 不为空时指定id查询
  uint32 page = 4;  // 从1开始
  uint32 page_size = 5;
  uint32 guild_id = 6;
}
message GetPgcDailyInfoListResp {
  repeated PgcDailyInfo info_list = 1;
  uint32 total_cnt = 2; // 总数
  uint32 next_page = 3; // 为0时表示结束
}

// 公会月信息
message GuildMonthlyStatsInfo {
  uint32 ts = 1;
  uint64 guild_fee = 2;  // 公会月流水
  uint32 sign_anchor_cnt = 3; // 签约成员数量
  uint32 active_anchor_cnt = 4;  // 活跃从业者数量
  uint32 quality_anchor_cnt = 5;  // 优质从业者数量
  uint64 active_anchor_fee = 6;  // 活跃从业者贡献流水
  uint64 quality_anchor_fee = 7; // 优质从业者贡献流水
  float quality_anchor_cnt_ratio = 8;  // 优质从业者数量占比
  float quality_anchor_fee_ratio = 9;  // 优质从业者贡献流水占比
  float guild_pkg_fee_ratio = 10;  // 公会背包流水占比
  uint32 profession_prac_cnt = 11;  // 专业从业者数
  uint32 new_profession_prac_cnt = 12;  // 新增专业从业者
  uint32 valid_sign_member_cnt = 13;  // 有效签约成员
  uint32 new_valid_sign_mem_cnt = 14;  // 新增有效签约成员
  uint32 profession_prac_retain_cnt = 15;  // 专业从业者留存数
  float profession_prac_retain_ratio = 16;  // 专业从业者留存率
  uint32 new_prac_cnt = 17;  // 新增从业者
  uint32 new_prac_retain_cnt = 18;  // 新增专业从业者留存数
  float  new_prac_retain_ratio = 19;  // 新增专业从业者留存率
}

// 获取公会月信息列表
message GetGuildMonthlyStatsInfoListReq {
  uint32 begin_month = 1;
  uint32 end_month = 2;
  uint32 guild_id = 3;
  uint32 page = 4;  // 从1开始
  uint32 page_size = 5;
}
message GetGuildMonthlyStatsInfoListResp{
  repeated uint32 guild_id_list = 1;   // 第一个是母公会
  string guild_name = 2;  // 母公会名称
  repeated GuildMonthlyStatsInfo info_list = 3;
  uint32 total_cnt = 4;  // 总数
  uint32 next_page = 5;  // 为0时表示结束
}

// 主播从业者基础信息
message AnchorPractitionerBaseInfo {
  uint32  uid = 1;
  string  nickname = 2;
  string  ttid = 3;
  string  username = 4;  // 获取头像用的
  string  agent_ttid = 5;  // 经纪人ttid
  uint32  live_valid_days = 6;  // 有效直播天数
  float  live_valid_hours = 7;  // 有效直播时长
  uint64  channel_fee = 8;  // 直播间流水
  uint64  anchor_income = 9;  // 主播收礼值
  repeated ViolationsInfo violations_list = 10;  // 违规信息
  uint32  live_active_days = 11;  //直播活跃天数
  uint32  plat_active_days = 12;  // 平台活跃天
  string  agent_nickname = 13;  // 经纪人昵称
  float  live_hours = 14;  // 直播累计时长
  float  plat_online_hours = 15;  // TT在线时长
}

// 主播从业者基础信息
message AnchorPractitionerMonthLyInfo {
  uint32  ts = 1;  // 时间
  AnchorPractitionerBaseInfo base_info = 2;
  uint32 consume_relation_chains = 3;  // 付费关系链数量
  bool  is_new = 4; // 是否是新增主播
  bool  is_high_quality = 5;  // 是否是优质
  bool  is_active = 6; // 是否是活跃
  bool  is_new_active = 7;  // 是否是新增活跃
  bool  is_profession = 8;  // 是否是专业从业者
}

// 获取主播从业者月信息列表
message GetAnchorPractitionerMonthLyInfoListReq {
  uint32 month_ts = 1;
  repeated string ttid_list = 2;  // 不为空时指定id查询
  uint32 page = 3;  // 从1开始
  uint32 page_size = 4;
  uint32 guild_id = 5;
}
message GetAnchorPractitionerMonthLyInfoListResp {
  repeated AnchorPractitionerMonthLyInfo info_list = 1;
  uint32 total_cnt = 2;  // 总数
  uint32 next_page = 3;  // 为0时表示结束
}

// 主播从业者周信息
message AnchorPractitionerWeeklyInfo {
  uint32 begin_ts = 1;  // 开始
  uint32 end_ts = 2;  // 结束
  AnchorPractitionerBaseInfo base_info = 3;
}

// 获取主播从业者周信息列表
message GetAnchorPractitionerWeeklyInfoListReq {
  uint32 begin_ts = 1; // 开始时间
  uint32 end_ts = 2;  // 结束时间
  repeated string ttid_list = 3;  // 不为空时指定id查询
  uint32 page = 4;  // 从1开始
  uint32 page_size = 5;
  uint32 guild_id = 6;
}
message GetAnchorPractitionerWeeklyInfoListResp {
  repeated AnchorPractitionerWeeklyInfo info_list = 1;
  uint32 total_cnt = 2; // 总数
  uint32 next_page = 3; // 为0时表示结束
}

// 主播从业者日信息
message AnchorPractitionerDailyInfo {
  uint32 ts = 1;  // 时间
  AnchorPractitionerBaseInfo base_info = 2;
}

// 获取主播从业者日信息列表
message GetAnchorPractitionerDailyInfoListReq {
  uint32 begin_ts = 1; // 开始时间
  uint32 end_ts = 2;  // 结束时间
  repeated string ttid_list = 3;  // 不为空时指定id查询
  uint32 page = 4;  // 从1开始
  uint32 page_size = 5;
  uint32 guild_id = 6;
}
message GetAnchorPractitionerDailyInfoListResp {
  repeated AnchorPractitionerDailyInfo info_list = 1;
  uint32 total_cnt = 2; // 总数
  uint32 next_page = 3; // 为0时表示结束
}

// 公会主播从业者信息
message GuildAnchorPracMonthlyStatsInfo {
  uint32 ts = 1;
  uint32 quality_anchor_cnt = 2;  // 优质从业者数量
  uint32 active_anchor_cnt = 3;  // 活跃从业者数量
  uint32 new_active_anchor_cnt = 4;  // 新增活跃从业者数量
  uint32 profession_anchor_cnt = 5;  // 专业从业者数
}

// 获取公会主播月信息列表
message GetGuildAnchorPracMonthlyInfoListReq {
  uint32 begin_month = 1;
  uint32 end_month = 2;
  uint32 guild_id = 3;
}
message GetGuildAnchorPracMonthlyInfoListResp{
  repeated GuildAnchorPracMonthlyStatsInfo info_list = 1;
  uint32 guild_id = 2;
  string guild_name = 3;
}

// ****** 从业者分析 end ****** //


//汇总并结算
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message SumAndSettlementReq {
  SettlementBillType billType = 1; // 结算单类型
}

message CheckSumAndSettlementResp {
  uint32 status = 1; //1开始，2结束，3错误
}


/*违规记录*/
message GetGuildViolationRecordReq {
  uint32          guild_id = 1;
  repeated string ttid_list = 2;
  uint32          page = 3; // 第一页从0开始
  uint32          page_num = 4;
  uint32          begin_ts = 5;
  uint32          end_ts = 6;
  bool          is_export = 7;  // 是否导出
}

message ViolationRecord {
  uint32 anchor_uid = 1;
  string anchor_ttid = 2;
  string anchor_account = 3;
  string anchor_nickname = 4;
  uint32 anchor_sex = 5; // 0-女，1-男

  uint32 guild_id = 6;
  repeated uint32 identity_info_list = 7; // 身份信息,0-签约成员，1-语音主播

  uint32 agent_uid = 8; // 经纪人uid
  string agent_ttid = 9;
  string agent_nickname = 10;

  string violation_reason = 11; // 违规原因
  string violation_type = 12; // 违规内容形式
  string sanction_action_desc = 13; // 处罚方式
  string opt_time = 14; //处罚时间 字符串
  string violation_level = 15;  // 违规等级
  string violation_source = 16;  // 违规来源
}

message GetGuildViolationRecordResp {
  uint32 total = 1;
  repeated ViolationRecord list = 2;
  uint32 day_cnt_limit = 3;  //选择天数限制
}
/*违规记录 end*/

// ***** 手机验证码 ******//
enum VerifyScene {
  VerifySceneInvalid = 0;  // 无效
  VerifySceneAddAgent = 1;  // 增加公会代理人
  VerifySceneDelAgent = 2;  // 删除公会代理人
}

// 校验是否需要拉起验证
message CheckIsNeedVerifyReq {
  uint32 scene = 1 ;   // 校验场景 see VerifyScene
}
message CheckIsNeedVerifyResp {
  bool  is_need = 1;  // 是否需要拉取验证
}

// 发送验证码
message SendVerifyCodeReq {
  uint32 scene = 1 ;   // 校验场景 see VerifyScene
}
message SendVerifyCodeResp {
}

// 校验验证码
message CheckVerifyCodeReq {
  string code = 1;
  uint32 scene = 2 ;   // 校验场景 see VerifyScene
}
message CheckVerifyCodeResp {
  string err_msg = 1;
}

// ***** 手机验证码 ******//




// ****** 大神带飞券  ****** //


// 日任务类型
// buf:lint:ignore ENUM_PASCAL_CASE
enum HALL_TASK_DAY_TYPE{
  HALL_TASK_DAY_TYPE_INVALID = 0; // 无效值
  HALL_TASK_DAY_TYPE_HOLD_TIME = 1;            // 接档时间
  HALL_TASK_DAY_TYPE_VALID_OPEN_CNT = 2;       // 有效开局数
  HALL_TASK_DAY_TYPE_GOD_FLY_TICKET_CNT = 3;   // 大神带飞券
  HALL_TASK_DAY_TYPE_TBEAN_INCOME = 4;         // 流水  T豆收礼流水
}
// 周任务类型
// buf:lint:ignore ENUM_PASCAL_CASE
enum HALL_TASK_WEEK_TYPE{
  HALL_TASK_WEEK_TYPE_INVALID = 0; // 无效值
  HALL_TASK_WEEK_TYPE_VALID_HOLD_DAY_CNT = 1;   // 有效接档天数
  HALL_TASK_WEEK_TYPE_FINISH_DAY_TASK_CNT = 2;  // 完成日任务次数
  HALL_TASK_WEEK_TYPE_VALID_OPEN_CNT = 3;       // 有效开局数
  HALL_TASK_WEEK_TYPE_GOD_FLY_TICKET_CNT = 4;   // 大神带飞券
  HALL_TASK_WEEK_TYPE_TBEAN_INCOME = 5;         // 流水   T豆收礼流水
}

// 判断是否展示承接大厅模块
message CheckShowHallTaskReq{
  uint32 guild_id = 1;
}
message CheckShowHallTaskResp{
  bool is_show=1;
}


// 根据id查任务配置
message GetHallTaskConfByIdReq{
  uint32 guild_id = 1;
  uint32 task_id=2;
  uint32 query_type=3; //0-查看，1-修改
}
message GetHallTaskConfByIdResp {
  HallTaskConfDetialInfo info=1;
}

// 查询任务配置
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetHallTaskConfListReq{
  uint32 guild_id = 1;
  string task_group_name = 2; // 任务组合名称
  uint32 page = 3; // 第一页从0开始
  uint32 pageSize = 4;
}
message GetHallTaskConfListResp{
  uint32 total=1;
  repeated HallTaskConfDetialInfo list=2;
}
// 任务配置详情
message HallTaskConfDetialInfo {
  uint32 task_id=1; // 已存在 修改时传
  string task_group_name = 2; // 任务组合名称
  repeated HallTaskConfInfo day_task_list = 3; // 日任务
  repeated HallTaskConfInfo week_task_list = 4; // 周任务
  string reward_msg = 5; // 任务奖励说明
  bool has_next_week_effect=6; // 是否存在下周生效记录
  bool has_distribute=7; //是否已经分配给用户
}
message HallTaskConfInfo {
  uint32 task_type=1; // see HALL_TASK_DAY_TYPE,HALL_TASK_WEEK_TYPE
  repeated float val_list=2; //梯度数值

}

// 编辑任务配置
message AddHallTaskConfReq {
  uint32 guild_id = 1;
  HallTaskConfDetialInfo info = 2;
}
message AddHallTaskConfResp {
}

// 删除任务配置
message DelHallTaskConfReq {
  uint32 guild_id=1;
  uint32 task_id=2;
}
message DelHallTaskConfResp {
}

// 单个搜索、批量导入 获取名单
// 查询符合条件名单
message GetHallValidListReq {
  uint32 guild_id=1;
  repeated string ttid_list = 2;
}
message GetHallValidListResp {
  repeated MultiPlayUserInfo valid_list=1; // 符合条件的名单
  repeated string invalid_ttid_list=2; // 不存在的TTID名单
  repeated string no_self_guild_ttid_list=3; // 非本公会签约成员名单
  repeated string no_multi_play_ttid_list=4; // 没有多人互动身份的
  bool enable_no_self_guild=5;// 为true则可配置非本公会
}
message MultiPlayUserInfo {
  uint32 uid=1;
  string ttid=2;
  string nickname=3;
}

// 删除任务
message DelHallTaskReq{
  uint32 guild_id=1;
  uint32 uid=2; //被删除的uid
}
message DelHallTaskResp{
}

// 分配任务
message DistributeHallTaskReq {
  uint32 guild_id=1;
  uint32 task_id=2;
  repeated uint32 uid_list = 3; // 传uid
  bool is_force_update = 4; // 是否覆盖
}
message DistributeHallTaskResp {
  repeated HallTaskInfo confict_list=1; // 有冲突才返回
}
message HallTaskInfo {
  uint32 guild_id=1;
  uint32 uid=2;
  string ttid=3;
  string nickname=4;
  uint32 task_id=5;
  string task_group_name=6;

}

// 查询分配任务
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetGuildHallTaskReq {
  uint32 guild_id = 1;
  string task_group_name = 2;
  string ttid=3;
  uint32 page = 4; // 第一页从0开始
  uint32 pageSize = 5;
}
message GetGuildHallTaskResp {
  uint32 total=1;
  repeated HallTaskInfo list=2;
}

enum HallStatisticsType {
  HallStatisticsDay=0; // 日任务
  HallStatisticsWeek=1; // 周任务
}
// 查询厅数据
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetGuildHallTaskStatsReq {
  uint32 guild_id = 1;
  uint32 stats_type=2; // 厅数据统计方式 HallStatisticsType
  uint32 begin_ts = 3;
  uint32 end_ts = 4;
  uint32 page = 5; // 第一页从0开始
  uint32 pageSize = 6;
}
message GetGuildHallTaskStatsResp {
  uint32 total=1;
  repeated HallStatisticsInfo list=2;
}
message HallStatisticsInfo {
  string date=1; // 时间字符串
  uint32 channel_id=2;
  string channel_name=3;
  uint32 enter_channel_cnt=4; // 进房人数
  uint32 has_ticket_cnt=5; // 有体验券的人数
  uint32 used_ticket_cnt=6; // 使用券的人数
  uint32 hold_sign_cnt=7; // 接档人数
  uint64 channel_fee=8; // 房间流水
  uint32 record_id=9; // 厅数据id
}

// 查询接档详情
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetGuildHallTaskStatsDetialReq {
  uint32 guild_id = 1;
  uint32 stats_type=2; // 厅数据统计方式 HallStatisticsType
  uint32 record_id=3;// 厅数据id
  uint32 task_type=4;// see HALL_TASK_DAY_TYPE,HALL_TASK_WEEK_TYPE
  string ttid=5;
  uint32 page = 6; // 第一页从0开始
  uint32 pageSize = 7;
}
message GetGuildHallTaskStatsDetialResp {
  uint32 total=1;
  repeated GuildHallTaskStatsDetial list=2;
}
message GuildHallTaskStatsDetial{
  string date=1; // 时间字符串
  string task_name=2;
  uint32 uid=3;
  string ttid=4;
  string nickname=5;
  string val=6; // 完成情况
}

// ****** 大神带飞券 end ****** //


// 互动游戏额外奖励结算单详情
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetInteractGameExtraBillDetailReq {
  uint32 guildId = 1; // 公会id
  string billId = 2;  // 为空表示未结算详情
}
message GetInteractGameExtraBillDetailResp {
  uint64 game_month_total_fee =1; // 本月互动游戏总流水 T豆
  uint64 game_month_extra_income =2; // 本月互动游戏额外奖励 金钻
  repeated InteractGameExtraBillDetail info_list=3;
}
message InteractGameExtraBillDetail {
  uint32 anchor_uid = 2;
  string anchor_ttid = 3;
  string anchor_account = 4;
  string anchor_nickname = 5;
  uint32 anchor_sex = 6; // 0-女，1-男

  uint64 game_month_fee=7; // 本月互动游戏流水 T豆
  float game_income_rate=8; // 收益比率
  uint64 game_income=9; // 收益 金钻
}


// 主播基本信息
message AnchorBaseInfo {
  uint32 uid = 1;
  string account = 2; // 获取头像
  string nickname = 3; // 昵称
  string ttid = 4;
  string agent_nickname = 5;  // 经纪人昵称
  string agent_ttid = 6;  // 经纪人ttid
  string channel_name = 7;  // 房间名称
  bool is_live = 8; // 是否在直播
}

//场次数据
message MatchData {
  uint32 channel_fee = 1; // 直播间流水
  uint32 anchor_income = 2;  // 主播总收礼
  uint32 live_min = 3;  //开播总时长
  float pkg_gift_ratio = 4;  // 背包流水占比
  uint32 live_ts = 5;  // 开播时间
}

message AnchorMatchData {
  AnchorBaseInfo base_info = 1;
  MatchData match_data = 2;
}


// 获取公会的主播列表
message GetGuildAnchorListReq {
  enum SortType {
    Sort_Type_Invalid = 0; // 无效
    Sort_Type_Fee = 1; // 直播间流水排序
    Sort_Type_Income = 2; // 主播收入排序
    Sort_Type_Min = 3;  // 直播时长排序
    Sort_Type_Pkg_Ratio = 4; // 背包流水占比排序
  }
  string anchor_ttid = 1;  // 指定主播ttid查询
  string agent_ttid = 2;  // 指定经纪人ttid查询
  uint32 live_type = 3;  // 0:全部 1:开播
  uint32 sort_type = 4;  // 排序类型
  uint32 sort = 5;  //  1：降序, 2：升序
  uint32 page = 6; // 从1开始
  uint32 page_size = 7;
  uint32 guild_id = 8;
}

message GetGuildAnchorListResp {
  repeated AnchorMatchData anchor_list = 1;
  uint32 total_cnt = 2;  // 总数
  uint32 live_anchor_cnt = 3; // 开播主播数量
  uint32 guild_anchor_cnt = 4;  // 公会签约主播数量
}


// 获取主播的场次数据
message GetAnchorMatchListReq {
  uint32 anchor_uid = 1; // 主播uid
  uint32 begin_ts = 2;
  uint32 end_ts = 3;
  uint32 page = 4; // 从1开始
  uint32 page_size = 5;
  uint32 guild_id = 6;
}
message GetAnchorMatchListResp {
  AnchorBaseInfo anchor_info = 1;
  repeated MatchData data_list = 2;
  uint32 total_cnt = 3;
}

message GetGameSkillDetialReq {
  uint32 guild_id=1;
  uint32 anchor_uid=2;
  uint32 apply_id=3;//签约申请id
}
message GetGameSkillDetialResp {
  repeated UserSkillInfo list=1;
}

//用户游戏资料信息
message UserSkillInfo {
  uint32 game_id = 1;
  string game_name = 2;                      // 游戏名称
  string skill_evidence = 3;                 // 技能图
  string audio = 4;                          // 语音介绍
  uint32 audio_duration = 5;                 // 语音介绍时长(秒数)
  repeated SectionInfo section_list = 6;    // 技能详细信息
  string text_desc = 7;                      // 文字介绍
}
message SectionInfo {
  string section_name = 1;        // 资料名称（段位信息、擅长位置、擅长英雄...）
  repeated string item_list = 2;  // 填写项
}


/*ESport skill begin*/
enum ApplyESportAuditType{
  ESPORT_AUDIT_TYPE_INVALID = 0;
  ESPORT_AUDIT_TYPE_INIT = 1;              // 未审核
  ESPORT_AUDIT_TYPE_RISK_REJECT = 2;       // T盾拒绝
  ESPORT_AUDIT_TYPE_RISK_PASS = 3;         // T盾通过
  ESPORT_AUDIT_TYPE_WAIT_FOR_DUILD = 4;    // 工会审核中
  ESPORT_AUDIT_TYPE_GUILD_PASS = 5;        // 工会审核通过
  ESPORT_AUDIT_TYPE_GUILD_REJECT = 6;      // 工会审核拒绝
  ESPORT_AUDIT_TYPE_WAIT_FOR_PLATFORM = 7; // 平台审核中
  ESPORT_AUDIT_TYPE_PASS = 8;              // 平台审核通过
  ESPORT_AUDIT_TYPE_REJECT = 9;            // 平台审核拒绝
}

// /guild-management/esports/getAuditSkill
//获取新增技能审核信息请求
message BatchGetAuditSkillRequest {
  uint32 guild_id = 1;
  repeated string ttid_list = 2;
  repeated uint32 audit_type = 3;     // 审核结果 see esport-role.proto EsportAuditType
  uint32 off_set = 4;
  uint32 limit = 5;
  uint32 need_total = 6;             //是否需要总数量 1 需要
  uint32 search_type = 7;            //搜索类型
  repeated uint32 audit_source = 8;  //技能审核来源 see AuditSource
}
//获取新增技能审核信息请求
message BatchGetAuditSkillResponse {
  uint32 guild_id = 1;
  uint32 uid = 2;
  repeated uint32 audit_type = 3;     // 审核结果 see esport-role.proto EsportAuditType
  uint32 off_set = 4;
  uint32 limit = 5;
  repeated AuditSkillRecord list = 6; //审核记录
  uint32 total_count = 7;
}



message AuditSkillRecord {
  uint32 uid = 1;
  string account = 2;
  string nick_name = 3;
  string ttid = 4;
  string audit_token = 5;            //审核唯一记录
  uint32 audit_type = 6;             //审核结果 see esport-role.proto EsportAuditType
  uint32 apply_time = 7;             //申请时间
  repeated UserSkillInfo skill = 8;  //技能信息
  uint32 audit_source = 9;          //技能审核来源 see AuditSource
  string reason = 10;
  uint32 guild_id = 11;
  string sign_duration = 12;        //签约时长
  string operator = 13;             //审核人员
  uint32 update_time = 14;          //最后更新时间

  uint32 sex=15;
}

// /guild-management/esports/setSkillAuditType
//设置用户技能审核结果
message SetUserSkillAuditTypeRequest {
  uint32 guild_id = 1;
  uint32 uid = 2;
  string audit_token = 3;
  uint32 audit_type = 4;        // 审核结果 see esport-role.proto EsportAuditType
  string reason = 5;            //原因
  repeated string audit_token_list = 6;  //批量审核
}

message SetUserSkillAuditTypeResponse {
}
/*ESport skill end*/

/*Esport guild info begin*/
// EsportPractitionerBaseInfo 电竞从业者基础信息
message EsportPractitionerBaseInfo{
  uint32 uid = 1;
  string ttid = 2;
  string nickname = 3;
  string username = 4;  // 获取头像用的
  uint32 sign_start_ts = 5;  // 签约开始时间
  uint32 sign_end_ts = 6;  // 签约结束时间
}

// EsportPractitionerDailyInfo
message EsportPractitionerDailyInfo{
  uint32 ts = 1;  // 时间
  EsportPractitionerBaseInfo base_info = 2;
  uint32 guild_id = 3; // 公会id
  uint32 order_amt = 4;  // 订单总金额(T豆)（数据部数据）
  uint32 order_cnt = 5;  // 订单总数（数据部数据）
  uint32 served_user_cnt = 6; // 服务人数（数据部数据）
  uint32 new_pay_user_cnt = 7; // 新客人数（数据部数据）
  uint32 new_repay_user_cnt = 8; // 复购人数（数据部数据）
}

// /guild-management/esports/GetEsportPractitionerDaily
// 获取电竞从业者日信息列表
message GetEsportPractitionerDailyRequest {
  uint32 begin_ts = 1;
  uint32 end_ts = 2;
  repeated string ttid_list = 3;
  uint32 page = 4;
  uint32 page_size = 5;
  repeated uint32 guild_list = 6;
}

message GetEsportPractitionerDailyResponse {
  repeated EsportPractitionerDailyInfo list = 1;
  uint32 total_cnt = 2;
  uint32 next_page = 3;
}

// EsportPractitionerMonthlyInfo 电竞从业者月信息
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message EsportPractitionerMonthlyInfo{
  uint32 ts = 1;  // 时间
  EsportPractitionerBaseInfo base_info = 2;
  uint32 guild_id = 3; // 公会id
  uint32 order_amt = 4;  // 订单总金额(T豆)（数据部数据）
  uint32 order_cnt = 5;  // 订单总数（数据部数据）
  uint32 order_day_cnt = 6;  // 接单天数（数据部数据）
  uint32 active_day_cnt = 7; // 活跃天数（数据部数据）
  uint32 served_user_cnt = 8; // 服务人数（数据部数据）
  uint32 new_pay_user_cnt = 9; // 新客人数（数据部数据）
  uint32 repay_user_cnt = 10; // 复购人数（数据部数据）
  ViolationsInfo violations_A_info = 11; // A类违规信息（内审数据）
  ViolationsInfo violations_B_info = 12; // B类违规信息（内审数据）
  ViolationsInfo violations_C_info = 13; // C类违规信息（内审数据）
}

// /guild-management/esports/GetEsportPractitionerMonthly
// 获取电竞从业者月信息列表
message GetEsportPractitionerMonthlyRequest {
  uint32 month_ts = 1;
  repeated string ttid_list = 2;
  uint32 page = 3;
  uint32 page_size = 4;
  repeated uint32 guild_list = 5;
}

message GetEsportPractitionerMonthlyResponse {
  repeated EsportPractitionerMonthlyInfo list = 1;
  uint32 total_cnt = 2;
  uint32 next_page = 3;
}

// EsportGameMonthlyStatInfo 电竞游戏月统计信息
message EsportGameMonthlyStatInfo {
  enum GameType { // 游戏类型 详见 esport-skill/esport-skill.proto GAME_TYPE
    Game_Type_Invalid = 0; // 无效
    GAME_TYPE_MOBILE = 1; // 手游
    GAME_TYPE_PC = 2; // 端游
  }
  uint32 month_ts = 1;
  uint32 guild_id = 2;
  string game_name = 3;
  uint32 game_type = 4;
  uint32 order_amt = 5;  // 订单总金额(T豆)（数据部数据）
  uint32 order_cnt = 6;  // 订单总数（数据部数据）
  uint32 coach_cnt = 7; // 指导人数（数据部数据）
  uint32 take_orders_coach_cnt = 8; // 接单指导人数（数据部数据）
  uint32 served_user_cnt = 9; // 服务人数（数据部数据）
}

// /guild-management/esports/GetEsportGameMonthlyStat
// 获取电竞游戏月统计信息
message GetEsportGameMonthlyStatRequest {
  uint32 month_ts = 1;
  repeated uint32 guild_list = 2;
  repeated string game_name_list = 3; // 游戏名称列表
  uint32 page = 4;
  uint32 page_size = 5;
}

message GetEsportGameMonthlyStatResponse {
  repeated EsportGameMonthlyStatInfo list = 1;
  uint32 total_cnt = 2;
  uint32 next_page = 3;
}

message EsportGuildMonthlyStat {
  uint32 month_ts = 1;
  uint32 guild_id = 2;
  uint32 order_amt = 4;  // 订单总金额(T豆)（数据部数据）
  uint32 order_cnt = 5;  // 订单总数（数据部数据）
  uint32 served_user_cnt = 6; // 服务人数（数据部数据）
  uint32 coach_cnt = 7; // 指导人数（数据部数据）
  uint32 new_coach_cnt = 8; // 新增指导人数（数据部数据）
}

// /guild-management/esports/GetEsportGuildMonthlyStat
// 获取电竞公会月统计信息
message GetEsportGuildMonthlyStatRequest {
  uint32 month_start_ts = 1;
  uint32 month_end_ts = 2;
  repeated uint32 guild_list = 3;
  uint32 page = 4;
  uint32 page_size = 5;
}

message GetEsportGuildMonthlyStatResponse {
  repeated EsportGuildMonthlyStat list = 1;
  uint32 total_cnt = 2;
  uint32 next_page = 3;
  map<uint32, string> guild_name_map = 4;
}

// 电竞大神数据
message EsportPractitionerData{
  uint32 begin_ts = 1;  //时间
  uint32 end_ts = 2;   // 周的结束时间
  string ttid = 3;
  string nickname = 4; // 昵称
  string level = 5;  // 当前等级
  string online_ts = 6;  // 在线时长 单位 分钟
  string order_ts = 7;  // 秒接单时长 单位 分钟
  string order_amt = 8;  // 完成订单总额(元)
  string order_cnt = 9;  // 完成订单局数
  string served_user_cnt = 10; // 完成订单用户数
  string divide_reward = 11;  // 当前分成奖励
  string viewed_user_cnt = 12; // 大神访客数
  string  im_user_cnt = 13;  // 访客互动数
  string im_user_ratio = 14;  // 访客互动率
  string ave_reply_ts = 15;  // 平均回复时长 单位 秒
  string valid_order_day_cnt = 16;  // 有效接单天数
  string key_user_viewed_cnt = 17;  // 新用户访客数
  string key_user_im_cnt = 18;   // 新用户访客互动数
  string key_user_im_ratio = 19;  // 新用户访客互动率
  string key_user_served_cnt = 20; // 新用户完单用户数
  string key_user_enter_channel_cnt = 21;   // 完单新用户进房数
  string key_user_channel_consume_cnt= 22;    //新用户进房打赏人数
  string key_user_channel_amount = 23;   //新用户进房打赏流水（元）
  string exposure_cnt = 24;  // 曝光数
  string user_enter_channel_cnt = 25;   // 完单进房用户数
  string user_channel_consume_cnt= 26;    //完单进房打赏用户数
  string user_channel_amount = 27;   //完单进房打赏流水（元）
}


//电竞查询时间维度
enum EsportTimeDimType {
  EsportTimeDimType_Invalid = 0;  //无效
  EsportTimeDimType_Day = 1;   //日维度
  EsportTimeDimType_Week = 2;   //周维度
  EsportTimeDimType_Month = 3;   //月维度
}

//获取公会的电竞大神数据 /esports/GetEsportPractitionerData
message GetEsportPractitionerDataReq {
  uint32 time_type = 1;  //查询维度 see EsportTimeDimType
  uint32 begin_ts = 2;  //开始时间
  uint32 end_ts = 3;  //结束时间
  uint32 guild_id = 4;
  string tid = 5;   //不为空，指定ttid查询
  uint32 page = 6;   //页码 从1开始
  uint32 page_size = 7;  //页数
}
message GetEsportPractitionerDataResp{
  repeated EsportPractitionerData data_list = 1;
  uint32 total_cnt = 2;
  uint32 next_page = 3;
}


// 公会整体经营数据
message EsportGuildData{
  uint32 begin_ts = 1;  //时间
  uint32 end_ts = 2;   // 周的结束时间
  string active_coach = 3;  // 有效活跃大神数
  string new_coach = 4;  // 新增大神数
  string order_coach = 5;   // 完单大神数
  string order_amt = 6;  // 完成订单总额(元)
  string tip_amount = 7;  // 打赏总流水 元
  string order_cnt = 8;  // 完成订单局数
  string served_user_cnt = 9; // 完成订单用户数
  string served_user_enter_channel_cnt = 10;  //完单进房用户数
  string served_user_enter_channel_concume_cnt = 11;  //完单进房打赏用户人数
  string served_user_enter_channel_amount = 12;  //完单进房打赏流水（元）
  string key_user_served_cnt = 13;  //完单新用户数
  string key_user_served_enter_channel_cnt = 14; //新用户进房数
  string key_user_enter_channel_concume_cnt = 15;  //新用户进房打赏人数
  string key_user_enter_channel_amount = 16;  //新用户打赏流水（元）
  string new_user_channel_amount_after = 17;  //新用户后续打赏流水（元）
}

// 获取电竞公会整体经营数据 /esports/GetEsportGuildData
message GetEsportGuildDataReq{
  uint32 time_type = 1;  //查询维度 see EsportTimeDimType
  uint32 begin_ts = 2;  //开始时间
  uint32 end_ts = 3;  //结束时间
  uint32 guild_id = 4;
  uint32 page = 5;   //页码 从1开始
  uint32 page_size = 6;  //页数
}
message GetEsportGuildDataResp{
  repeated EsportGuildData data_list = 1;
  uint32 total_cnt = 2;
  uint32 next_page = 3;
}


//电竞公会厅数据报表
message EsportChannelData{
  uint32 begin_ts = 1;  //时间
  uint32 end_ts = 2;   // 周的结束时间
  string view_id = 3;  //房间ID
  string enter_cnt = 4;  // 进房人数
  string tip_cnt = 5;  // 进房打赏人数
  string tip_amount = 6;  // 打赏金额
  string served_user_enter_channel_cnt = 7; // 完单进房人数
  string served_user_enter_channel_tip_cnt = 8;  //完单进房打赏人数
  string served_user_enter_channel_tip_amount = 9;  //完单进房打赏金额
  string key_user_served_enter_channel_cnt = 14; //新用户进房数
  string key_user_enter_channel_concume_cnt = 15;  //新用户进房打赏人数
  string key_user_enter_channel_amount = 16;  //新用户打赏流水（元）
  string coach_income = 17;   //大神打赏金额（元）
  string coach_income_ratio = 18;  // 大神流水占比
}

// 获取电竞公会厅数据报表 /esports/GetEsportChannelData
message GetEsportChannelDataReq{
  uint32 time_type = 1;  //查询维度 see EsportTimeDimType
  uint32 begin_ts = 2;  //开始时间
  uint32 end_ts = 3;  //结束时间
  uint32 guild_id = 4;
  string view_id = 5;  //房间ID，不为空指定查询
  uint32 page = 6;   //页码 从1开始
  uint32 page_size = 7;  //页数
}
message GetEsportChannelDataResp{
  repeated EsportChannelData data_list = 1;
  uint32 total_cnt = 2;
  uint32 next_page = 3;
}


/*Esport guild info end*/


//获取用户访问白鲸后台访问token
message GetWhiteWhaleAccessTokenRequest {
}
message GetWhiteWhaleAccessTokenResponse {
  string access_token = 1;
  uint32 guild_id = 2;  // 公会id
  uint32 expire_ts = 3; // 过期时间戳
}

//公会合作库类型
enum CooperationType {
  CooperationTypeInvalid = 0; // 无效
  CooperationTypeAmuse = 1; // 娱乐房
  CooperationTypeYuyin = 2; // 语音房
  CooperationTypeEsport= 4; // 电竞房
}
//获取公会合作库信息
message GetGuildCooperationInfoRequest {
}
message GetGuildCooperationInfoResponse {
  uint32 cooperation_types = 1;   // 合作类型 see CooperationType. 按位存储
}


// 获取多人互动接档数据列表
message GetMultiPractitionerScheduleDataReq {
  enum TimeDimType {
    TimeDimType_Invalid = 0;  //无效
    TimeDimType_Hour = 1;   //小时维度
    TimeDimType_Day = 2;   //日维度
    TimeDimType_Week = 3;   //周维度
    TimeDimType_Month = 4;   //月维度
  }

  repeated uint32 channel_id_list = 1;
  string date_start = 2;
  string date_end = 3;
  repeated uint32 hour_list = 4; // 0 ~ 23
  repeated string ttid_list = 6;
  uint32 offset = 7;
  uint32 limit = 8;
  uint32 guild_id = 9;
  uint32 time_dim_type = 10;  // 查询维度 see TimeDimType
}
message MultiPractitionerScheduleInfo {
  string date = 1;
  uint32 channel_id = 2;
  string channel_view_id = 3;
  string channel_name = 4;
  string channel_tag = 15; // 房间标签
  string hour = 5;
  uint32 uid = 7;
  string ttid = 8;
  string nickname = 9;
  string username = 16;
  bool is_employed = 10;
  string valid_duration_0 = 11; // 0麦接档时长（分钟）
  string valid_duration_other = 12; // 非0麦接档时长（分钟）
  string receive_gift_total = 13; // 接档期间收礼（元）
  string receive_hundred_gift_total = 14; // 收到百元礼物总值（元）
}
message GetMultiPractitionerScheduleDataResp {
  repeated MultiPractitionerScheduleInfo schedule_info_list = 1;
  uint32 total = 2;
  uint64 last_update_time = 3; // 最后更新时间
  uint32 time_dim_type = 4;  // 查询维度 see TimeDimType 透传
}

// 获取多人互房间动任职信息列表
message GetMultiChannelEmploymentInfoListReq {
  // 全量返回，前端分页
  uint32 guild_id = 1;
}
message MultiChannelEmploymentInfo {
  message UserInfo {
    uint32 uid = 1;
    string ttid = 2;
    string nickname = 3;
    bool is_contract = 4; // 是否签约
    string username = 5;
  }
  uint32 channel_id = 1;
  string channel_view_id = 2;
  string channel_name = 3;
  repeated UserInfo channel_admin = 4; // 厅管信息
  uint32 employee_num = 5; // 任职成员数量
  repeated UserInfo employee_list = 6; // 任职成员列表
}
message GetMultiChannelEmploymentInfoListResp {
  repeated MultiChannelEmploymentInfo employment_info_list = 1;
  uint32 total = 2;
}

// 获取多人互房间动任职信息
message GetMultiChannelEmploymentInfoReq {
  uint32 channel_id = 1;
  uint32 guild_id = 2;
}
message GetMultiChannelEmploymentInfoResp {
  MultiChannelEmploymentInfo employment_info = 1;
}

// 设置房间任职成员
message SetMultiChannelEmploymentInfoReq {
  uint32 channel_id = 1;
  repeated uint32 employee_uid_list = 2; // 任职成员UID
  uint32 guild_id = 3;
}
message SetMultiChannelEmploymentInfoResp {

}
// 设置房间厅管
message SetMultiChannelAdminInfoReq {
  uint32 channel_admin_uid = 1; // 厅管UID
  repeated uint32 channel_id_list = 2; // 房间ID
  uint32 guild_id = 3;
}
message SetMultiChannelAdminInfoResp {

}

// 获取公会多人互动房间列表
message GetMultiChannelListReq {
  uint32 guild_id = 1;
}
message GetMultiChannelListResp {
  message ChannelInfo {
    uint32 channel_id = 1;
    string channel_view_id = 2;
    string channel_name = 3;
  }
  repeated ChannelInfo channel_list = 1;
}

// 搜索多人互动签约成员
message SearchMultiContractMemberReq {
  repeated string ttid = 1;
  uint32 guild_id = 2;
}
message SearchMultiContractMemberResp {
  repeated MultiChannelEmploymentInfo.UserInfo member_list = 1;
}

// 批量设置任职成员
message BatchSetMultiChannelEmploymentInfoReq {
  message Item {
    string channel_view_id = 1;
    string employee_ttid = 2;
  }
  repeated Item employment_list = 1;
  uint32 guild_id = 2;
}
message BatchSetMultiChannelEmploymentInfoResp {
  enum Result {
    ResultInvalid = 0;
    ResultSuccess = 1;
    ResultFailed = 2;
  }
  message Item {
    string channel_view_id = 1;
    string employee_ttid = 2;
    Result ret = 3; // 结果
    string reason = 4; // 失败原因
  }
  repeated Item employment_list = 1;
}

message GetGuildAdminListReq {
  uint32 guild_id = 1;
}
message GetGuildAdminListResp {
  message UserInfo {
    uint32 uid = 1;
    string ttid = 2;
    string nickname = 3;
    string username = 4;
  }
  repeated UserInfo admin_list = 1;
}

message GetChannelListByAdminUidReq {
  uint32 admin_uid = 1;
  uint32 guild_id = 2;
}

message GetChannelListByAdminUidResp {
  message ChannelInfo {
    uint32 channel_id = 1;
    string channel_view_id = 2;
    string channel_name = 3;
  }
  repeated ChannelInfo channel_list = 1;
}

message GeneralFileUploadResp {
  string file_key = 1;
  string file_url = 2;
}

//关键指标类型
enum KpiType {
  Kpi_Type_Invalid = 0;
  Kpi_Type_Guild_Income = 1;  //公会营收
  Kpi_Type_New_Sign_Anchor = 2; // 新签达人数
  Kpi_Type_Live_Anchor = 3;   // 开启听听达人数
  Kpi_Type_Two_Wan_Anchor = 4;  // 收礼≥2万豆达人数
  Kpi_Type_Ten_Wan_Anchor= 5;  // 收礼≥10万豆达人数
  Kpi_Type_Pro_Anchor = 6;  // 专业从业者数
  Kpi_Type_Mature_Anchor = 7;  // 成熟达人数
  Kpi_Type_Pot_Active_Anchor = 8;  //潜力活跃达人数
  Kpi_Type_0_2w_Fee_Anchor = 9;   // 【0,2w豆）达人流水层级
  Kpi_Type_2w_10w_Fee_Anchor = 10;   // 【2w,10w豆）达人流水层级
  Kpi_Type_10w_20w_Fee_Anchor = 11;   // 【10w,20w豆）达人流水层级
  Kpi_Type_20w_50w_Fee_Anchor = 12;   // 【20w,50w豆）达人流水层级
  Kpi_Type_50w_100w_Fee_Anchor = 13;   // 【50w,100w豆）达人流水层级
  Kpi_Type_100w_Up_Fee_Anchor = 14;   // 【【100w,+∞豆）达人流水层级
  Kpi_Type_0_20w_Fee_Anchor = 15;   // 【0,20w豆）达人流水层级
  Kpi_Type_100w_300w_Fee_Anchor = 16;   // 【100w,300w豆）达人流水层级
  Kpi_Type_300w_500w_Fee_Anchor = 17;   // 【300w,500w豆）达人流水层级
  Kpi_Type_500w_Up_Fee_Anchor = 18;   // 【500w,+∞豆）达人流水层级
  Kpi_Type_New_Anchor = 19;  // 新达人
  Kpi_Type_Old_Anchor = 20;  // 老达人
  Kpi_Type_New_Sign_Anchor_Fee = 21;  // 新签达人营收
  Kpi_Type_New_Sign_Anchor_Live = 22;  // 新签达人开启听听人数
  Kpi_Type_New_Sign_Anchor_2_Up_Active_Live = 23;  // 新签达人听听活跃天≥2天人数
  Kpi_Type_New_Sign_Anchor_2w_Up_Fee_Week = 24;  // 周流水≥2w豆新签达人数
  Kpi_Type_Pure_New_Anchor_10w_Up_Fee_Week = 25;  // 周流水≥10w豆纯新达人数
  Kpi_Type_Pure_New_Anchor_Active_Live = 26;  // 纯新活跃达人数
  Kpi_Type_New_Pro_Anchor = 27;  // 新签专业从业者数
  Kpi_Type_New_Mature_Anchor = 28;  // 新签成熟达人数
  Kpi_Type_New_Pot_Active_Anchor = 29;  // 新签潜力活跃达人数
  Kpi_Type_Pure_New_Anchor = 30;  // 纯新达人
  Kpi_Type_Not_Pure_New_Anchor = 31;  // 非纯新达人
}

//经营分析
message BusinessAnalysis {
  uint32 kpi_type = 1;  // see KpiType
  uint64 value = 2;   // 数值
  int64 ratio_value = 3;   // 值环比 正：增加 负：下降 0：持平
  uint32 rank = 4;   // 同层级排行
  int32 ratio_rank = 5;   // 排名环比     0表示持平
  uint32 all_rank = 6;   // 全局排名
  uint64 last_value = 7;   // 上周期数值
  float growth_rate = 8;   // 增长率 正：增加 负：下降 0：持平
  uint64 revenue_value = 9;   // kpi的营收值
  float revenue_ratio = 10;   // kpi的营收占比
  int64 revenue_ratio_value = 11;   // kpi的营收值环比 正：增加 负：下降 0：持平
  float revenue_growth_rate = 12;   // kpi的营收值增长率 正：增加 负：下降 0：持平
  float this_rate = 13;   //本周期率 听听率，活跃率，孵化率
  float last_rate = 14;   //上周期率  听听率，活跃率，孵化率
  float rate_ratio = 15;   // 率环比  
}

// 经营诊断
message BusinessDiagnosis {
  uint32 kpi_type = 1;  // see KpiType
  string diag = 2;  // 诊断结果
}

// 周维度经营分析
// 请求path business-analysis/GetWeekBusinessAnalysis
message GetWeekBusinessAnalysisReq {
  uint32 guild_id = 1; // 公会id
  uint32 begin_ts = 2;  // 开始时间
  uint32 end_ts = 3;   // 结束时间
}
message GetWeekBusinessAnalysisResp {
  repeated BusinessAnalysis analysis_list = 1;  // 指标分析列表
  repeated BusinessDiagnosis diagnosis_list = 2;  // 指标诊断列表
}


// 月维度经营分析
// 请求path business-analysis/GetMonthBusinessAnalysis
message GetMonthBusinessAnalysisReq {
  uint32 guild_id = 1; // 公会id
  uint32 month_ts = 2;  // 月时间
}
message GetMonthBusinessAnalysisResp {
  repeated BusinessAnalysis kpi_list = 1;  // 关键指标列表
  repeated BusinessDiagnosis diagnosis_list = 2;  // 指标诊断列表
}

// 听听营收分析
// 请求path business-analysis/GetRevenueBusinessAnalysis
message GetRevenueBusinessAnalysisReq {
  enum TimeDimType {
     TimeDimType_Invalid = 0;  //无效
     TimeDimType_Week = 1;  //周维度
     TimeDimType_Month = 2;  // 月维度
  }
  uint32 dim_type = 1;  //时间维度 see TimeDimType
  uint32 guild_id = 2; // 公会id
  uint32 ts = 3;  // 周月时间
}
message GetRevenueBusinessAnalysisResp {
  repeated BusinessAnalysis analysis_list = 1;  // 指标列表
  string anchor_business_diag = 2;   // 达人分布经营诊断
  string anchor_item_business_diag = 3;   // 达人分布明细项经营诊断
  string revenue_business_diag = 4;  // 达人收礼层级分发经营诊断
  string revenue_item_business_diag = 5;  // 达人收礼层级分发明细项经营诊断
}

// 听听拉新分析
// 请求path business-analysis/GetRecruitBusinessAnalysis
message GetRecruitBusinessAnalysisReq {
  enum TimeDimType {
     TimeDimType_Invalid = 0;  //无效
     TimeDimType_Week = 1;  //周维度
     TimeDimType_Month = 2;  // 月维度
  }
  uint32 dim_type = 1;  //时间维度 see TimeDimType
  uint32 guild_id = 2; // 公会id
  uint32 ts = 3;  // 周月时间
}
message GetRecruitBusinessAnalysisResp {
  repeated BusinessAnalysis analysis_list = 1;  // 指标列表
  string total_business_diag = 2;  // 总体经营诊断
  string anchor_business_diag = 3;   // 达人分布经营诊断
  string anchor_item_business_diag = 4;   // 达人分布明细项经营诊断
  string revenue_business_diag = 5;  // 达人收礼层级分发经营诊断
  string revenue_item_business_diag = 6;  // 达人收礼层级分发明细项经营诊断
}


enum RankLevel {
  RankLevel_default = 0;
  RankLevel_Low = 1;        // 低
  RankLevel_Middle = 2;      //中
  RankLevel_High = 3;        //高

}

// 公会对标数据
message GuildBenchmarkingData {
  uint32 top_data = 1;            //  层级公会TOP1
  uint32 avg_data = 2;            //  层级平均线
  uint32 self_data = 3;           // 本公会【高亮展示】
  uint32 level = 4;               // 层级内排行等级 RankLevel
  int32 exceed_cnt = 5;           // 对标高一层级公会平均水平 大于0表示超过 小于0表示差距
  uint32 rank = 6;                 // 层级内公会排行
  bool has_high_level = 7;         // 是否有高一层级公会
}

// 获取公会运营能力数据
message GetGuildOperationalCapabilitiesRequest {
  uint32 guild_id = 1;    //公会id
  int64 from_time = 2;    // 开始时间
  int64 to_time = 3;        //结束时间
}

message GetGuildOperationalCapabilitiesResponse {
  string belongs_level_desc = 1;                     //所属层级描述
  GuildBenchmarkingData new_sign_anchor = 2;                //  新签达人
  GuildBenchmarkingData enabled_live_new_user = 3;           // 开启听听新签达人数
  GuildBenchmarkingData new_active_anchor = 4;                  // 新活跃达人数
  GuildBenchmarkingData new_sign_pro_anchor = 5;               //新签达人专从人数
  GuildBenchmarkingData enabled_live_user = 6;                        //开启听听达人数
  GuildBenchmarkingData pro_anchor = 7;                   //专业从业者数
  GuildBenchmarkingData mature_anchor = 8;                        // 成熟达人数
  GuildBenchmarkingData pot_active_anchor = 9;            // 潜力活跃达人数
}

// 公会运营水平
enum GuildOperationalSummaryLevel {
  GuildOperationalSummaryLevel_Default = 0;         //未定义
  GuildOperationalSummaryLevel_Poor = 1;         //总体经营水平差
  GuildOperationalSummaryLevel_Ordinary = 2;     //总体经营水平一般
  GuildOperationalSummaryLevel_Median = 3;       //总体经营水平中等
  GuildOperationalSummaryLevel_Good = 4;         //总体经营水平良好
  GuildOperationalSummaryLevel_Excellent = 5;     //总体经营水平优秀
}

// 获取公会总体经营
message GetGuildOperationalSummaryRequest {
  uint32 guild_id = 1;    //公会id
  int64 from_time = 2;    // 开始时间
  int64 to_time = 3;        //结束时间
}

message GetGuildOperationalSummaryResponse {
  uint32 level = 1;         //总体经营水平 GuildOperationalSummaryLevel
  string operational_level_desc = 2;             //经营水平描述
  string operational_strengths_desc = 3;         //经营强项描述
  string operational_weaknesses_desc = 4;        //经营弱项描述
  string operational_level_desc_detail_content = 5;     // 经营水平描述内容
  string operational_level_desc_detail_title = 6;     // 经营水平描述标题
  string operational_level_desc_detail_calculate = 7;     // 经营水平描述计算公式
}


// 课程类型
enum CurriculumType {
  CurriculumType_Invalid = 0; // 无效
  CurriculumType_Expert = 1; // 专业知识
  CurriculumType_Courseware = 2; // 能力课件
}
// 知识标题信息
message CurriculumMessage {
  string curriculum_title = 1;  //知识标题
  string curriculum_desc = 2;   //只是简介
  string file_type = 3;        //文件类型
  string file_url = 4;           //资源链接
}

// 获取知识标题课程列表
message GetCurriculumMessageListRequest {
  uint32 type = 1; // 课程类型 CurriculumType
  uint32 guild_id = 2; 
}

message GetCurriculumMessageListResponse {
  repeated CurriculumMessage curriculum_list = 1; //课题知识列表
}

// 公会业务类型
message GuildBusiness {
  string name = 1; // 公会名称
  uint32 type = 2; // 公会类型
  bool available = 3; // 是否可用
}

// 获取公会信息
message TopGuildInfo {
  uint32 guild_id = 1; // 公会id
  uint32 short_id = 2;  // 公会短id
  string name = 3; // 公会名称
  string guild_tag = 4; // 公会简介
  string owner_name = 5; // 公会所有者名称
  string owner_account = 6; // 公会所有者账号
  string owner_alias = 7; // 公会所有者别名
  uint32 owner_uid = 8; // 公会所有者uid
  repeated GuildBusiness business_list = 9; // 公会业务列表

  //
  uint32 rank = 10; // 排名
  string recommend_tag = 11;    // 推荐标签
  string ability_tag = 12;      // 能力标签
  string honor_title = 13;      // 荣誉称号
  int64 from_time = 14;         // 开始时间
  int64 to_time = 15;           // 结束时间
}

// 获取推荐置顶公会列表
message GetRecommendTopGuildListReq {

}

message GetRecommendTopGuildListResp {
  repeated TopGuildInfo guild_list = 1; // 公会列表
}


//解约方式信息 废弃，使用v2版本
message CancelContractTypeInfo {
  uint32 cancel_type = 1; // 解约方式 see anchorcontract-go.proto的 CancelContractType
  uint32 tilte = 2;  //解约方式标题
  uint32 desc = 3;   // 解约方式描述 返回html格式
  uint32 is_select = 4;   // 是否选择
}


//解约方式信息
message CancelContractTypeInfoV2 {
  uint32 cancel_type = 1; // 解约方式 see anchorcontract-go.proto的 CancelContractType
  string tilte = 2;  //解约方式标题
  string desc = 3;   // 解约方式描述 返回html格式
  bool is_select = 4;   // 是否选择
  bool is_default = 5;  // 是否默认
}


//从业者解约方式
message PracCancelContractTypeInfo {
  uint32 prac_type = 1;  //从业者类型 see anchorcontract-go.proto的 ContractWorkerType
  string time_desc = 2;  //时间描述文案
  repeated CancelContractTypeInfo info_list = 3;  //解约方式列表 废弃
  string prac_type_desc = 4;  // 从业者类型名称
  repeated CancelContractTypeInfoV2 info_v2_list = 5;  // 解约方式列表
  bool is_can_edit = 6;   // 是否可以编辑
}


//获取公会解约方式列表
message GetCancelContractTypeListReq {
  uint32 guild_id = 1;  // 公会id
}
message GetCancelContractTypeListResp {
  repeated PracCancelContractTypeInfo type_list = 1; // 解约方式列表
}


//设置公会的解约方式
message SetGuildCancelContractTypeReq {
  uint32 guild_id = 1; // 公会id
  uint32 prac_type = 2;  //从业者类型
  repeated uint32 cancel_type_list = 3;  // 选中的解约方式列表
}
message SetGuildCancelContractTypeResp{
}

//子权益
message SignSubRight {
  uint32 id = 1;  // 权益id
  string name = 2;  // 名称
  string icon = 3;  // 图标
  bool is_select = 4;  // 是否选中
  bool is_default = 5;  // 是否默认选中(无法取消)
  string note = 6; // 备注
}

//签约权益
message SignRight {
  uint32 id = 1;
  string name = 2;
  repeated SignSubRight sub_right_list = 3;  // 子权益列表
  string icon = 4; // 图标
}

// 从业者签约权益
message PracSignRight  {
  uint32 prac_type = 1;  //从业者类型 see anchorcontract-go.proto的 ContractWorkerType
  repeated SignRight right_list = 2;  // 权益列表
  string prac_name = 3;  // 从业者名称
}

// 获取公会签约权益列表 /anchor-contract/GetGuildSignRight
message GetGuildSignRightReq {
  uint32 guild_id = 1;
}
message GetGuildSignRightResp {
  repeated PracSignRight right_list = 1;
}

//更新公会签约权益  /anchor-contract/UpdateGuildSignRight
message UpdateGuildSignRightReq {
  uint32 guild_id = 1;
  PracSignRight right = 2;
}
message UpdateGuildSignRightResp {
}



// 获取公会主播人脸核验日明细列表
// 请求path /guild-management/anchor-face-check/batchGetDailyFaceCheckInfo
message BatchGetDailyFaceCheckInfoReq {
  uint32 guild_id = 1; // 公会id
  uint32 identity = 2; // 查询身份类型 see AnchorIdentityType
  uint32 begin_ts = 3; // 查询开始时间
  uint32 end_ts = 4; // 查询结束时间
  uint32 page = 5; // 页码
  uint32 page_size = 6; // 每页数量
  repeated string ttid_list = 7; // 主播ttid列表（选填）
}

message BatchGetDailyFaceCheckInfoResp {
  repeated DailyFaceCheckInfo info_list = 1;
  uint32 total = 2;
}

// DailyFaceCheckInfo 主播人脸核验日明细数据
message DailyFaceCheckInfo {
  string date = 1; // 日期
  string account = 2; // 主播账号
  string nickname = 3; // 主播昵称
  string ttid = 4; // 主播ttid
  int64 sign_start_ts = 5; // 签约开始时间
  int64 sign_end_ts = 6; // 签约结束时间
  string agent_nickname = 7; // 经纪人昵称
  string rule_type = 8; // 从业者标记
  uint32 display_id = 9; // 经营房间ID
  uint32 guild_short_id = 10; // 公会短id
  string room_gift_amt = 11; // 用户在房间收礼金额/元
  string guild_gift_amt = 12; // 公会收礼金额,属于同个公会的所有房间的收礼金额之和/元
  string less_commission_amt = 13; // 预计扣除佣金礼物金额/元
}

// 导出主播人脸核验类型
enum AnchorFaceCheckExportType {
  AnchorFaceCheckExportTypeInvalid = 0;
  AnchorFaceCheckExportTypeDailyDetail = 1; // 日明细数据
  AnchorFaceCheckExportTypeWeeklyDetail = 2; // 周明细数据
  AnchorFaceCheckExportTypeWeeklySum = 3; // 周汇总数据
  AnchorFaceCheckExportTypeDailySum = 4; // 日汇总数据
}

// 导出主播人脸核验数据请求
// 请求path /guild-management/anchor-face-check/exportFaceCheckInfo
message ExportFaceCheckInfoReq {
  uint32 guild_id = 1; // 公会id
  uint32 begin_ts = 2; // 查询开始时间
  uint32 end_ts = 3; // 查询结束时间
  repeated string ttid_list = 4; // 主播ttid列表（选填）
  uint32 export_type = 5; // 导出类型 see AnchorFaceCheckExportType
  uint32 identity = 6; // 查询身份类型 see AnchorIdentityType
}
// 导出主播人脸核验数据响应
message ExportFaceCheckInfoResp {
  string download_url = 1; // 下载链接
}

message GuildOwnerCorpApply {
  string business_license_url = 3;    // 营业执照电子版
  string company_name = 4;             // 公司名称
  string company_code = 5;         // 统一社会信用代码
  string legal_person = 6;       // 法人姓名
  string id_card = 7;            // 法人身份证号码
  string company_account_name = 8; // 公司开户名称
  string bank_account = 9;         // 银行账户
  string bank_branch = 10;       // 开户支行
  string bank_basic_account_url = 11; // 基本存款账户信息表电子版
  string general_taxpayer_url = 12;    // 一般纳税人
  uint32 general_taxpayer_rate = 13;  // 一般纳税人税率
  uint32 small_taxpayer_rate = 14;  // 小规模纳税人税率
  InvokeType invoice_type = 15;        // 发票类型，1普通发票，2增值税专用发票
  string contact_name = 16;        // 联系人
  string contact_phone = 17;       // 联系电话
  repeated string contact_addr = 18;        // 联系地址
  string contact_email = 19;       // 联系email
  string approval_reason = 21;    // 拒绝原因
  int64 approval_time = 22;       // 审批时间
  uint32 status = 23;
  int64 create_at = 24;          // 创建时间
  int64 update_at = 25;          // 更新时间
  string bank_basic_account_key = 26; // 基本存款账户信息表电子版文件key
  string general_taxpayer_key = 27;    // 一般纳税人文件key
  string business_license_key = 28;    // 营业执照电子版文件key
  uint64 id = 29;
  string sign_url = 30; // E签宝签约地址
  string legal_phone = 31; // 法人手机号
}

// 发票类型
enum InvokeType {
  INVOKE_TYPE_UNSPECIFIED = 0;
  INVOKE_TYPE_REGULAR = 1; // 普通发票
  INVOKE_TYPE_VAT_SPECIAL = 2; // 增值税专用发票
}

// 审批状态
enum GuildOwnerCorpApplyStatus {
  GUILD_OWNER_CORP_APPLY_STATUS_UNSPECIFIED = 0;
  GUILD_OWNER_CORP_APPLY_STATUS_SUBMIT = 1; // 审核中
  GUILD_OWNER_CORP_APPLY_STATUS_APPROVED = 2; // 审核通过
  GUILD_OWNER_CORP_APPLY_STATUS_REJECTED = 3; // 审核拒绝
}

// 审批进度
enum MainApplyStatus {
  MAIN_APPLY_STATUS_UNSPECIFIED = 0;
  MAIN_APPLY_STATUS_SUBMIT = 1; // 提交
  MAIN_APPLY_STATUS_FAIL = 2; // 审核拒绝
  MAIN_APPLY_STATUS_E_CONTRACT = 3; // e签宝
  MAIN_APPLY_STATUS_FINISH = 4; // 完成
}

// 提交对公审核信息接口
message SubmitGuildOwnerCorpApplyReq {
  uint32 guild_id = 1;
  GuildOwnerCorpApply apply = 2;
}
message SubmitGuildOwnerCorpApplyResp {
}

// 获取对公审核状态接口
message GetGuildOwnerCorpApplyStatusReq {
  uint32 guild_id = 1;
}
message GetGuildOwnerCorpApplyStatusResp {
  GuildOwnerCorpApply apply = 2;
}

// 查询对公审核记录列表接口
message GetGuildOwnerCorpApplyListReq {
  uint32 guild_id = 1;
  uint32 offset = 2;
  uint32 limit = 3;
}
message GetGuildOwnerCorpApplyListResp {
  repeated SimpleGuildOwnerCorpApply apply_list = 1;
  uint32 total = 2;
}
message SimpleGuildOwnerCorpApply {
  uint64 id = 1;
  string company_name = 2; // 公司名称
  GuildOwnerCorpApplyStatus approval_status = 3;     // 0提交，1审核通过，2审核拒绝
  string approval_reason = 4;    // 拒绝原因
  int64 approval_time = 5;       // 审批时间
  int64 create_at = 6; // 创建时间
}

// 对公审核信息上传接口
message CorpApplyUploadReq {

}
message CorpApplyUploadResp {
  string url = 1;
}

// WeeklyFaceCheckInfo 主播人脸核验周明细数据
message WeeklyFaceCheckInfo {
  string date = 1; // 周日期（周第一天）
  string account = 2; // 主播账号
  string nickname = 3; // 主播昵称
  string ttid = 4; // 主播ttid
  int64 sign_start_ts = 5; // 签约开始时间
  int64 sign_end_ts = 6; // 签约结束时间
  string agent_nickname = 7; // 经纪人昵称
  string rule_type = 8; // 从业者标记
  uint32 display_id = 9; // 经营房间ID
  uint32 guild_short_id = 10; // 公会短id
  string room_gift_amt = 11; // 用户在房间收礼金额/元
  string guild_gift_amt = 12; // 公会收礼金额,属于同个公会的所有房间的收礼金额之和/元
  string less_commission_amt = 13; // 预计扣除佣金礼物金额/元
}

// 获取公会主播人脸核验周明细列表
// 请求path /guild-management/anchor-face-check/batchGetWeeklyFaceCheckInfo
message BatchGetWeeklyFaceCheckInfoReq {
  uint32 guild_id = 1; // 公会id
  uint32 identity = 2; // 查询身份类型 see AnchorIdentityType
  uint32 begin_ts = 3; // 查询开始时间
  uint32 end_ts = 4; // 查询结束时间
  uint32 page = 5; // 页码
  uint32 page_size = 6; // 每页数量
  repeated string ttid_list = 7; // 主播ttid列表（选填）
}

message BatchGetWeeklyFaceCheckInfoResp {
  repeated WeeklyFaceCheckInfo info_list = 1;
  uint32 total = 2;
}

// GuildWeeklySumFaceCheckInfo 主播人脸核验公会每周汇总明细数据
message GuildWeeklySumFaceCheckInfo {
  string date = 1; // 日期 yyyy/mm/dd至yyyy/mm/dd
  string rule_type = 2; // 从业者标记
  string action_user_cnt = 3; // 下发验证人数
  string face_user_cnt = 4; // 实际参与人数
  string not_himself_user_cnt = 5; // 综合非本人数
  string face_ratio = 6; // 验证参与率
  string himself_ratio = 7; // 本人率
  string face_himself_ratio = 8; // 参与验证本人率
  string not_himself_guild_gift_amt = 9; // 综合非本人公会流水
  string less_commission_amt = 10; // 预计扣除佣金（非最终扣款金额）
}

// 获取公会主播人脸核验公会每周汇总
// 请求path /guild-management/guild-anchor-face-check/batchGetGuildWeeklySumFaceCheckInfo
message BatchGetGuildWeeklySumFaceCheckInfoReq {
  uint32 guild_id = 1; // 公会id
  uint32 identity = 2; // 查询身份类型 see AnchorIdentityType
  uint32 begin_ts = 3; // 查询开始时间
  uint32 end_ts = 4; // 查询结束时间
  uint32 page = 5; // 页码
  uint32 page_size = 6; // 每页数量
}

message BatchGetGuildWeeklySumFaceCheckInfoResp {
  repeated GuildWeeklySumFaceCheckInfo info_list = 1;
  uint32 total = 2;
}

// GuildDailySumFaceCheckInfo 主播人脸核验公会每日汇总数据
message GuildDailySumFaceCheckInfo {
  string date = 1; // 日期
  string rule_type = 2; // 从业者标记
  string action_user_cnt = 3; // 下发验证人数
  string face_user_cnt = 4; // 实际参与人数
  string not_himself_user_cnt = 5; // 非本人数
  string face_ratio = 6; // 验证参与率
  string himself_ratio = 7; // 本人率
  string face_himself_ratio = 8; // 参与验证本人率
  string not_himself_guild_gift_amt = 9; // 综合非本人公会流水
  string less_commission_amt = 10; // 预计扣除佣金（非最终扣款金额）
}

// 获取公会主播人脸核验公会每日汇总列表
// 请求path /guild-management/guild-anchor-face-check/batchGetGuildDailySumFaceCheckInfo
message BatchGetGuildDailySumFaceCheckInfoReq {
  uint32 guild_id = 1; // 公会id
  uint32 identity = 2; // 查询身份类型 see AnchorIdentityType
  uint32 begin_ts = 3; // 查询开始时间
  uint32 end_ts = 4; // 查询结束时间
  uint32 page = 5; // 页码
  uint32 page_size = 6; // 每页数量
}

message BatchGetGuildDailySumFaceCheckInfoResp {
  repeated GuildDailySumFaceCheckInfo info_list = 1;
  uint32 total = 2;
}

// 查询婚礼计划
message GetReservedWeddingReq {
  uint32 guild_id = 1; // 公会ID
  repeated uint32 channel_id = 2; // 房间ID
  uint32 page_num = 3; // 页码
  uint32 page_size = 4; // 每页数量
  uint64 start_time = 5; // 开始时间
  uint64 end_time = 6; // 结束时间
  uint32 status = 7; // 状态
}
message GetReservedWeddingResp {
  repeated ReservedWeddingInfo reserved_wedding_list = 1; // 预约婚礼列表
  uint32 total = 2;
}

message ReservedWeddingInfo {
  uint32 wedding_plan_id = 1; // 婚礼计划ID
  uint32 create_time = 2; // 创建时间
  uint32 channel_id = 3; // 房间ID
  string channel_view_id = 4; // 房间view_id
  string channel_name = 5; // 房间名称
  uint32 reserve_start_time = 6; // 预约开始时间
  uint32 reserve_end_time = 7; // 预约结束时间
  uint32 groom_uid = 8; // 新郎ID
  string groom_ttid = 9; // 新郎TTID
  string groom_nickname = 10; // 新郎昵称
  string groom_username = 11; // 新郎昵称
  uint32 bride_uid = 12; // 新娘ID
  string bride_ttid = 13; // 新娘TTID
  string bride_nickname = 14; // 新娘昵称
  string bride_username = 15; // 新娘昵称
  uint32 host_uid = 16; // 主持人ID
  string host_ttid = 17; // 主持人TTID
  string host_nickname = 18; // 主持人昵称
  string host_username = 19; // 主持人昵称
  uint32 buyer_uid = 20; // 预约人ID
  string buyer_ttid = 21; // 预约人TTID
  string buyer_nickname = 22; // 预约人昵称
  uint32 channel_manager_uid = 23; // 客服ID
  string channel_manager_ttid = 24; // 客服TTID
  string channel_manager_nickname = 25; // 客服昵称
  uint32 buy_price = 26; // 购买t豆价格
  bool   is_hot = 27;    // 是否热门
  uint64 wedding_happiness = 28; // 婚礼幸福值
  uint64 wedding_amount = 29; // 婚礼总流水t豆
  bool is_admin_cancel = 30; // 是否管理员取消
}

// 设置主持人
message SetWeddingHostReq {
  uint32 guild_id = 1; // 公会ID
  uint32 wedding_plan_id = 2; // 婚礼计划ID
  uint32 host_uid = 3; // 主持人ID
  uint32 channel_id = 4; // 房间ID
}
message SetWeddingHostResp{}

// 搜索主持人
message SearchWeddingHostReq {
  uint32 guild_id = 1; // 公会ID
  string ttid = 2;
}
message SearchWeddingHostResp {
  uint32 uid = 1;
  string ttid = 2;
  string nickname = 3;
  string username = 4;
}

// 取消婚礼
message CancelWeddingReq {
  uint32 guild_id = 1; // 公会ID
  uint32 wedding_plan_id = 2; // 婚礼计划ID
}
message CancelWeddingResp {
}

// 是否支持预约开关状态
enum AdminReservableSwitch {
  RESERVABLE_SWITCH_UNEXPECTED = 0; // 未知
  RESERVABLE_SWITCH_OPEN = 1; // 开启
  RESERVABLE_SWITCH_CLOSE = 2; // 关闭
}

// 获取婚礼预约时间列表
message GetWeddingReserveTimeListReq {
  uint32 guild_id = 1; // 公会ID
  uint32 channel_id = 2; // 房间ID
  uint64 date = 3; // 查询日期
}
message GetWeddingReserveTimeListResp {
  message WeddingReserveTimeInfo {
    uint64 start_time = 1; // 开始时间
    uint64 end_time = 2; // 结束时间
    AdminReservableSwitch reservable_switch = 3; // 是否支持预约
    bool is_reserved = 4; // 是否已被预约
    uint32 buyer_uid = 5; // 预约人uid
    string buyer_ttid = 6; // 预约人ttid
    string buyer_nickname = 7; // 预约人昵称
  }
  repeated WeddingReserveTimeInfo time_list = 1;
}

// 设置是否支持预约
message SetWeddingReserveSupportReq {
  uint32 guild_id = 1; // 公会ID
  uint32 channel_id = 2; // 房间ID
  uint32 start_time = 3; // 开始时间
  uint32 end_time = 4; // 结束时间
  AdminReservableSwitch reservable_switch = 5; // 是否支持预约
}
message SetWeddingReserveSupportResp {
}

//能力项类型
enum AbilityItemType {
  AbilityItemType_Invalid = 0;  //无效
  AbilityItemType_Revenue = 1;  // 营收
  AbilityItemType_Recruit = 2;  // 招新
  AbilityItemType_Hatch = 3;   // 孵化
  AbilityItemType_Safety = 4;   // 安全
  AbilityItemType_Stability = 5;   // 稳定性
  AbilityItemType_Risk_Resistance = 6;   // 抗风险
}

//能力项
message AbilityInfo {
  uint32 type = 1;   // 能力类型 see AbilityItemType
  uint32 score = 2;   // 分值
}

//月能力项
message MonthAbilityInfo {
  uint32 ts = 1;   //月份
  repeated AbilityInfo info_list = 2;  // 能力项列表
}

//获取公会的能力星图 /multi-business-analysis/GetGuildRadarChart
message GetGuildRadarChartReq {
  uint32 guild_id = 1;
}
message GetGuildRadarChartResp {
  repeated MonthAbilityInfo ability_list = 1;
  string business_diag = 2;  // 经营诊断 直接返回html
}


//环比
message LastRatio {
  int32 ratio = 1; // 正反值 0 表示持平
  string ratio_value = 2;  // 环比值
}

//营收项
message Revenue {
  uint32 ts = 1;  //时间
  string total_fee = 2;  // 公会总流水
  string content_fee = 3;  // 内容品类流水
  string interact_fee = 4;  // 互动品类流水
  string particular_fee = 5;  // 特殊品类流水
  string practitioner_fee = 6;  // 从业者流水
}

//经营-营收项
message BusinessRevenue {
  repeated string diag_list = 1;  // 经营诊断列表，直接返回html
  repeated Revenue rev_list = 2;   //流水列表
}

//招新项
message Recruit{
  uint32 ts = 1;  //时间
  string new_practitioner = 2;  // 新增从业者数
  string new_practitioner_income = 3;  // 新增从业者收入
  string new_valid_practitioner = 4;  // 新增有效从业者数
  string new_valid_practitioner_income = 5;  // 新增有效从业者收入
  string new_pro_practitioner = 6;  // 新增专业从业者数
  string new_pro_practitioner_income = 7;  //新增专业从业者收入
}

// 经营-招新项
message BusinessRecruit {
  repeated string diag_list = 1;  // 经营诊断列表，直接返回html
  repeated Recruit recruit_list = 2;  //招新列表
  LastRatio new_practitioner_ratio = 3;  // 新增从业者数环比 0 持平
  LastRatio new_practitioner_income_ratio= 4;  // 新增从业者收入环比 0 持平
  LastRatio new_valid_practitioner_ratio = 5;  // 新增有效从业者数环比 0 持平
  LastRatio new_valid_practitioner_income_ratio = 6;  // 新增有效从业者收入环比 0 持平
  LastRatio new_pro_practitioner_ratio = 7;  // 新增专业从业者数环比 0 持平
  LastRatio new_pro_practitioner_income_ratio = 8;  //新增专业从业者收入环比 0 持平
  string new_practitioner_income_ave = 9;  // 新增从业者人均收入
  string new_valid_practitioner_income_ave = 10;  // 新增有效从业者人均收入
  string new_pro_practitioner_income_ave = 11;  //新增专业从业者均收入
}

//孵化项
message Hatch{
  uint32 ts = 1;  //时间
  string sign_practitioner = 2; // 签约从业者数
  string sign_practitioner_income = 3; // 签约从业者收入
  string sign_practitioner_remain = 4; // 签约从业者留存数
  string new_practitioners_retention= 5; // 上月新增从业者数留存数
  string new_practitioner_retention_income= 6; // 上月新增留存从业者收礼值
  string pro_practitioner = 7; // 专业从业者数
  string pro_practitioner_income = 8; // 专业从业者收入
  string pro_practitioner_remain = 9; // 专业从业者留存数
}

// 经营-孵化项
message BusinessHatch{
  repeated string diag_list = 1;  // 经营诊断列表，直接返回html
  repeated Hatch hatch_list = 2;  //孵化列表
  LastRatio sign_practitioner_ratio = 3; // 签约从业者数环比 0 持平
  LastRatio sign_practitioner_income_ratio = 4; // 签约从业者收入环比 0 持平
  LastRatio new_practitioners_retention_ratio = 5; // 上月新增从业者数留存数环比 0 持平
  LastRatio new_practitioner_retention_income_ratio = 6; // 上月新增留存从业者收礼值环比 0 持平
  LastRatio pro_practitioner_ratio = 7; // 专业从业者数环比 0 持平
  LastRatio pro_practitioner_income_ratio = 8; // 专业从业者收入环比 0 持平
  string sign_practitioner_income_ave = 9; // 签约从业者人均收入
  string new_practitioners_retention_ave = 10; // 上月新增留存从业者人均收入
  string  pro_practitioner_income_ave = 11; // 专业从业者人均收入
}



//安全项
message Safety{
  uint32 ts = 1;  //时间
  string vio_ratio = 2;  //日均违规率
  string vio_practitioner = 3; // 违规从业者数
  string face_pass_ratio = 4; //人脸验证通过率
  string face_pass_ratio_old = 5;  // 老签约成员 人脸验证通过率
  string face_pass_ratio_new = 6;  // 新签约成员 人脸验证通过率
}

// 经营-安全项
message BusinessSafety{
  repeated string diag_list = 1;  // 经营诊断列表，直接返回html
  repeated Safety safety_list = 2;  //安全列表
  LastRatio vio_ratio_last_ratio = 3;  //日均违规率环比 0 持平
  LastRatio vio_practitioner_ratio = 4; // 违规从业者数环比 0 持平
  LastRatio face_pass_ratio = 5; //人脸验证通过率环比 0 持平
  map<string, float> vio_detail_list = 6; //违规明细占比 违规类型 -》 占比
}


//房间经营信息
message GuildChannelBusinessData {
  uint32 cid = 1;  //房间cid
  string view_id = 2;  // 房间显示id
  string name = 3;  //房间名称
  string tag = 4;  // 房间标签
  string fee = 5;  //房间流水
  LastRatio fee_ratio = 6; // 房间流水环比 0 持平
  string sign_income = 7; // 签约成员收礼流水
  LastRatio sign_income_last_ratio = 8; // 签约成员收礼流水环比 0 持平
  string sign_income_ratio = 9; // 签约成员收礼流水占比
  LastRatio sign_income_ratio_last_ratio = 10; // 签约成员收礼流水占比环比 0 持平
  string valid_live_cnt = 11;  //有效开厅天数
  LastRatio valid_live_cnt_ratio = 12;  //有效开厅天数环比 0 持平
  string base_live_cnt = 13;   //基础开厅天数
  LastRatio base_live_cnt_ratio = 14;   //基础开厅天数环比 0 持平
  string new_practitioner = 15;  // 新增从业者数
  LastRatio new_practitioner_ratio = 16;  // 新增从业者数环比 0 持平
  string new_practitioners_retention= 17;  // 上月新增从业者留存数
  LastRatio new_practitioners_retention_ratio = 18;  // 上月新增从业者留存数环比 0 持平
  string pro_practitioner = 19;  // 专业从业者数
  LastRatio pro_practitioner_ratio = 20;  // 专业从业者数环比 0 持平
  uint32 ts = 21;   //时间
}


//稳定项
message Stability{
  uint32 ts = 1;  //时间
  string channel_cnt = 2;  // 经营房间数
  string ave_fee = 3;   // 单厅平均流水
  string content_channel_cnt = 4;  // 内容房间数
  string content_ave_fee = 5;  // 内容房间平均流水
  string interact_channel_cnt = 6;  // 互动品类房间数
  string interact_ave_fee = 7;  // 互动品类房间平均流水
  string particular_channel_cnt = 8;  // 特殊品类房间数
  string particular_ave_fee = 9;  // 特殊品类房间平均流水
}

// 经营-稳定项
message BusinessStability{
  repeated string diag_list = 1;  // 经营诊断列表，直接返回html
  repeated Stability stability_list = 2;  //稳定列表
  repeated GuildChannelBusinessData top_channel_list = 3;  // top房间列表
  repeated string guild_channel_id_list = 4;  // 公会所有房间id列表
}


//抗风险项
message RiskResistance {
  uint32 ts = 1;  //时间
  string lucky_gift_fee = 2;  //幸运礼物流水
  string lucky_gift_ratio = 3;  //幸运礼物占比
  string pkg_gift_fee = 4;  // 背包礼物流水
  string pkg_gift_ratio = 5; // 背包礼物占比
  string other_ratio = 6;  // 其他流水占比
  string tbean_gift_ratio = 7; // 纯豆礼物流水占比
}

//经营-抗风险项
message BusinessRiskResistance {
  repeated string diag_list = 1;  // 经营诊断列表，直接返回html
  repeated RiskResistance risk_list = 2;  //抗风险列表
  LastRatio lucky_gift_fee_ratio = 3;  //幸运礼物流水环比 0 持平
  LastRatio pkg_gift_fee_ratio = 4;  // 背包礼物流水环比 0 持平
}


//获取公会能力项经营诊断 /multi-business-analysis/GetGuildAbilityBusinessDiag
message GetGuildAbilityBusinessDiagReq{
  enum TimeDimType {
    TimeDimType_Invalid = 0;  //无效
    TimeDimType_Week = 1;  //周维度
    TimeDimType_Month = 2;  // 月维度
  }
  uint32 dim_type = 1;  // see TimeDimType
  uint32 ability_item_type = 2;  // see AbilityItemType
  uint32 begin_ts = 3;
  uint32 end_ts = 4;
  uint32 guild_id = 5;
}
message GetGuildAbilityBusinessDiagResp{
  BusinessRevenue revenue = 1;  // 经营项
  BusinessRecruit recruit = 2;  // 招新项
  BusinessHatch hatch = 3;  // 孵化项
  BusinessSafety safety = 4; // 安全项
  BusinessStability stability = 5;  //稳定项
  BusinessRiskResistance risk_resistance = 6;  //抗风险项
}

//获取公会房间经营信息 /multi-business-analysis/GetGuildChannelBusinessData
message GetGuildChannelBusinessDataReq{
  enum TimeDimType {
    TimeDimType_Invalid = 0;  //无效
    TimeDimType_Week = 1;  //周维度
    TimeDimType_Month = 2;  // 月维度
  }
  uint32 guild_id = 1;
  string channel_view_id = 2; //房间id
  uint32 ts = 3;  //周月时间
  uint32 dim_type = 4;  // 时间维度 see TimeDimType
}
message GetGuildChannelBusinessDataResp{
  repeated GuildChannelBusinessData channel_list = 1;  //房间经营信息列表
}



// 平台资讯banner
message BannerInfo {
  string img_url = 1;
  string jump_url = 2;  //跳转url
}

//公会重点数据类型
enum GuildKeyDataType {
  GuildKeyDataType_Invalid = 0;
  GuildKeyDataType_Fee = 1;  // 公会流水
  GuildKeyDataType_Run_Channel = 2;  // 经营房间
  GuildKeyDataType_New_Sign = 3;  // 新增签约
  GuildKeyDataType_New_Cancel_Sign = 4;  // 新增解约
}

//公会多人互动重点数据
message GuildMultiKeyData {
  uint32 type = 1;  // see GuildKeyDataType
  string this_value = 2;  // 本周期数据
  string last_value = 3;  // 上周期数据
  LastRatio last_ratio = 4;  // 环比
}

// 公会top公开厅
message GuildMultiTopChannel {
  string view_id = 1;  // 房间id
  string name = 2;  //房间名称
  string fee = 3;  //房间流水
}

//获取公会平台资讯banner /home/<USER>
message GetBannerInfoReq{
  uint32 guild_id = 1;
}
message GetBannerInfoResp{
  repeated BannerInfo banner_list = 1;  //平台资讯banner
}

//公会多人互动重点数据 /home/<USER>
message GetGuildMultiKeyDataReq{
  uint32 guild_id = 1;
}
message GetGuildMultiKeyDataResp{
  repeated GuildMultiKeyData multi_key_list = 1;  // 公会多人互动重点数据
}


//获取公会top公开厅 /home/<USER>
message GetGuildMultiTopChannelReq{
  uint32 guild_id = 1;
}
message GetGuildMultiTopChannelResp{
  repeated GuildMultiTopChannel top_list = 1;  // 公会top公开厅
}

