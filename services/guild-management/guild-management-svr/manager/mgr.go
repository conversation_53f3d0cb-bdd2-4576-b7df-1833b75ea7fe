package manager

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"gitlab.ttyuyin.com/avengers/tyr/pkg/cluster/timer/task/tasks"
	account_go "golang.52tt.com/clients/account-go"
	"golang.52tt.com/clients/anchorcontract-go"
	apicenter "golang.52tt.com/clients/apicenter/apiserver"
	"golang.52tt.com/clients/channel"
	"golang.52tt.com/clients/channel-live-stats"
	"golang.52tt.com/clients/obsgateway"
	"golang.52tt.com/clients/seqgen/v2"
	"golang.52tt.com/clients/sign-anchor-stats"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	config2 "golang.52tt.com/services/guild-management/guild-management-svr/conf/ttconfig/guild_management_dyconfig"
	config "golang.52tt.com/services/guild-management/guild-management-svr/conf/ttconfig/sign_anchor_stats"
	"google.golang.org/grpc"
	"time"

	pb "golang.52tt.com/protocol/services/guild-management-svr"
	"golang.52tt.com/services/guild-management/guild-management-svr/cache"
	"golang.52tt.com/services/guild-management/guild-management-svr/conf"
	"golang.52tt.com/services/guild-management/guild-management-svr/mysql"

	"gitlab.ttyuyin.com/avengers/tyr/pkg/cluster/timer"
	"golang.52tt.com/clients/account"
	"golang.52tt.com/clients/guild"
	guild_cooperation "golang.52tt.com/clients/guild-cooperation"
	im_api "golang.52tt.com/clients/im-api"
	"golang.52tt.com/clients/public"
	timeline "golang.52tt.com/clients/timeline"
	imPB "golang.52tt.com/protocol/app/im"
	revenueBasePb "golang.52tt.com/protocol/app/revenue_base"
	anchorContractPb "golang.52tt.com/protocol/services/anchorcontract-go"
)

var (
	DatabaseMysqlErr       = errors.New("数据库错误")
	UserAlreadyAssignedErr = errors.New("该账号已被分配")

	TTUid uint32 = 10000

	GuildManageLoginUrl = "https://appcdn.52tt.com/web/frontend-web-assist-commission-pc/index.html"
	InviteExpireTs      = 48 * 60 * 60

	GuildVioDailyPushMsg = "尊敬的会长你好，截止目前，本周累计核心违规数量为%d起，累计核心违规人数%d人，昨日新增核心违规%d起，新增核心违规人数%d人"
	GuildVioWeekPushMsg  = "尊敬的会长你好，截止目前，本月累计核心违规数量为%d起，累计核心违规人数%d人，上周新增核心违规%d起，新增核心违规人数%d人"

	GuildFaceCheckDailyPushMsg  = "尊敬的会长您好，您的公会在【%+v】累计疑似非本人接档人数为：【%+v人】，请督促相关人员在收到从业者认证人脸弹窗时及时完成验证"
	GuildFaceCheckWeeklyPushMsg = "尊敬的会长您好，您的公会在【%+v】期间累计疑似非本人接档人数为：【%+v人】，请督促相关人员在收到从业者认证人脸弹窗时及时完成验证"

	GuildFuWuHaoName = "会长服务号"
)

var mapVioId2Is = map[string]bool{
	"17": true, "1": true, "18": true, "363": true, "373": true, "16": true, "44": true, "345": true, "277": true, //严禁违反国家法律法规
	"45": true,             //违反平台规则
	"23": true, "34": true, //涉及涉黄低俗内容
	"377": true, "409": true, // 涉及玩法违规内容
	"43": true, "350": true, // 涉及辱骂诋毁内容
}

var mapAgentType2Msg = map[uint32]string{
	uint32(pb.AgentType_AgentBroker):                           "经纪人",
	uint32(pb.AgentType_AgentAdmin):                            "管理员",
	uint32(pb.AgentType_AgentBroker | pb.AgentType_AgentAdmin): "经纪人｜管理员",
}

var (
	mapScene2TTL = map[pb.VerifyScene]uint32{
		pb.VerifyScene_VerifySceneAddAgent: 600,
		pb.VerifyScene_VerifySceneDelAgent: 600,
	}
)

type GuildManageMgr struct {
	mysqlDb             *mysql.Store
	liveStatsCli        *channellivestats.Client
	anchorContractCli   *anchorcontract_go.Client
	cacheCli            *cache.Cache
	seqgenCli           seqgen.IClient
	accountCli          account.IClient
	channelCli          channel.IClient
	timelineCli         timeline.IClient
	guildCli            guild.IClient
	apiCenterClient     apicenter.IClient
	timer               *timer.Timer
	publicCli           public.IClient
	apiCli              apicenter.IClient
	guildCooperationCli *guild_cooperation.Client
	signAnchorStatsCli  sign_anchor_stats.IClient
	imApiCli            im_api.IClient

	signDyConfig *config.SignAnchorStatsConfig
	dyConf       *config2.GuildManagementDyconfigConfig

	accountGoCli account_go.IClient
	obsCli       obsgateway.IClient
}

func NewGuildManageMgr(sc *conf.ServiceConfigT) (*GuildManageMgr, error) {
	ctx := context.Background()

	// 初始化动态配置
	statsConfig, err := config.InitSignAnchorStatsConfig()
	if err != nil {
		log.Errorf("Failed to InitSignAnchorStatsConfig err:%v", err)
		return nil, err
	}

	dyConfig, err := config2.InitGuildManagementDyconfigConfig()
	if err != nil {
		log.Errorf("Failed to InitGuildManagementDyconfigConfig err:%v", err)
		return nil, err
	}

	mysqlDb, err := mysql.NewMysql(sc.GetMysqlConfig().ConnectionString(), sc.GetMysqlConfig().MaxIdleConns, sc.GetMysqlConfig().MaxIdleConns)
	if err != nil {
		log.Errorf("NewGuildManageMgr NewMysql failed sc:%v err:%v", sc, err)
		return nil, err
	}

	redisClient := sc.GetRedisConfig().NewDefualtGoRedisClient()
	cacheCli, err := cache.NewCache(redisClient)
	if nil != err {
		log.Errorf("NewClientPool Failed to NewCache, err: %+v", err)
		return nil, err
	}

	liveStatsCli, err := channellivestats.NewClient()
	if err != nil {
		log.Errorf("NewGuildManageMgr channellivestats.NewClient() failed sc:%v err:%v", sc, err)
		return nil, err
	}

	anchorContractCli, _ := anchorcontract_go.NewClient()
	seqgenCli_ := seqgen.NewIClient()
	accountCli_ := account.NewIClient()
	channelCli_ := channel.NewIClient()
	timelineCli_ := timeline.NewIClient()
	guildCli_ := guild.NewClient()
	apiCenterClient := apicenter.NewIClient()
	publicCli_ := public.NewClient()
	apiCli_ := apicenter.NewClient()
	imApiCli_ := im_api.NewIClient()
	guildCooperationCli_ := guild_cooperation.NewClient(grpc.WithBlock())

	signAnchorStatsCli_ := sign_anchor_stats.NewClient()
	accountGoCli_ := account_go.NewIClient()
	obsCli_, _ := obsgateway.NewClient()

	// 大小进程的定时器，同一个任务只会在一个节点上执行
	timerD, err := timer.NewTimerD(ctx, "guild-management-svr", timer.WithV6RedisCmdable(redisClient), timer.WithTTL(10*time.Second))
	if err != nil {
		log.Errorf("Failed to NewTimerD err:%v", err)
		return nil, err
	}

	mgr := &GuildManageMgr{
		mysqlDb:             mysqlDb,
		liveStatsCli:        liveStatsCli,
		anchorContractCli:   anchorContractCli,
		cacheCli:            cacheCli,
		seqgenCli:           seqgenCli_,
		accountCli:          accountCli_,
		channelCli:          channelCli_,
		timelineCli:         timelineCli_,
		guildCli:            guildCli_,
		apiCenterClient:     apiCenterClient,
		publicCli:           publicCli_,
		apiCli:              apiCli_,
		imApiCli:            imApiCli_,
		guildCooperationCli: guildCooperationCli_,
		signAnchorStatsCli:  signAnchorStatsCli_,
		timer:               timerD,
		signDyConfig:        statsConfig,
		dyConf:              dyConfig,
		accountGoCli:        accountGoCli_,
		obsCli:              obsCli_,
	}

	//coroutine.FixIntervalExec(mgr.TimerProcExpiredAgentInvite, time.Second*10)

	timerD.AddIntervalTask("TimerProcExpiredAgentInvite", time.Second*10, timer.BuildFromLambda(func(ctx context.Context) {
		mgr.TimerProcExpiredAgentInvite()
	}))

	timerD.AddIntervalTask("TimerProcGuildVioDailyPush", time.Minute*5, tasks.FuncTask(mgr.TimerProcGuildVioDailyPush))

	timerD.AddIntervalTask("TimerProcGuildVioWeekPush", time.Minute*5, tasks.FuncTask(mgr.TimerProcGuildVioWeekPush))

	timerD.AddIntervalTask("TimerProcGuildFaceCheckDailyPush", time.Minute*1, tasks.FuncTask(mgr.TimerProcGuildFaceCheckDailyPush))

	timerD.AddIntervalTask("TimerProcGuildFaceCheckWeeklyPush", time.Minute*1, tasks.FuncTask(mgr.TimerProcGuildFaceCheckWeeklyPush))

	return mgr, nil
}

func (m *GuildManageMgr) ShutDown() {
	//coroutine.StopAll()
	m.timer.Stop()
}

func (m *GuildManageMgr) GetGuildAgentList(ctx context.Context, in *pb.GetGuildAgentListReq) (*pb.GetGuildAgentListResp, error) {
	out := &pb.GetGuildAgentListResp{}

	log.DebugWithCtx(ctx, "GetGuildAgentList begin in:%v", in)
	agentList, err := m.mysqlDb.GetGuildAgentList(in.GetGuildId(), in.GetAgentType(), in.GetUidList())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildAgentList failed in:%v err:%v", in, err)
		return out, DatabaseMysqlErr
	}

	for _, agent := range agentList {
		permission := &pb.GuildManagePermission{
			GuildManageMenuPer: &pb.MenuPermission{},
			LiveDataPermission: &pb.LiveDataPermission{},
			FunctionPer:        &pb.FunctionPermission{},
			DataPer:            &pb.DataPermission{},
		}

		if agent.Permission != "" {
			err = json.Unmarshal([]byte(agent.Permission), permission)
			if err != nil {
				log.ErrorWithCtx(ctx, "GetGuildAgentList json.Unmarshal failed agent:%v err:%v", agent, err)
				continue
			}
		}

		tmpInfo := &pb.GuildAgent{
			Uid:        agent.AgentUid,
			Permission: permission,
			AddTs:      agent.BrokerAddTs,
			Guild_Id:   agent.GuildId,
			AgentType:  agent.AgentType,
			AdminAddTs: agent.AdminAddTs,
		}

		out.AgentList = append(out.AgentList, tmpInfo)
	}

	log.DebugWithCtx(ctx, "GetGuildAgentList end in:%v out:%v", in, out)
	return out, nil
}

func (m *GuildManageMgr) AddGuildAgent(ctx context.Context, in *pb.AddGuildAgentReq, isSendTTMsg bool) (*pb.AddGuildAgentResp, error) {
	out := &pb.AddGuildAgentResp{}
	nowTs := uint32(time.Now().Unix())

	log.InfoWithCtx(ctx, "AddGuildAgent begin in:%v nowTs:%d isSendTTMsg:%v", in, nowTs, isSendTTMsg)

	permission := &pb.GuildManagePermission{
		GuildManageMenuPer: &pb.MenuPermission{},
		FunctionPer:        &pb.FunctionPermission{},
		DataPer:            &pb.DataPermission{},
	}

	bytePermission, err := json.Marshal(permission)
	if err != nil {
		log.ErrorWithCtx(ctx, "AddGuildAgent json.Marshal failed in:%v err:%v", in, err)
		return out, err
	}

	var brokerAddTs uint32
	var adminAddTs uint32
	if in.GetAgentType()&uint32(pb.AgentType_AgentBroker) > 0 {
		brokerAddTs = nowTs
	}

	if in.GetAgentType()&uint32(pb.AgentType_AgentAdmin) > 0 {
		adminAddTs = nowTs
	}

	err = m.mysqlDb.AddGuildAgent(in.GetAgentUid(), in.GetGuildId(), in.GetAgentType(), brokerAddTs, adminAddTs, string(bytePermission))
	if err != nil {
		log.ErrorWithCtx(ctx, "AddGuildAgent failed in:%v err:%v", in, err)
		return out, DatabaseMysqlErr
	}

	if isSendTTMsg {
		agentString := ""
		if in.GetAgentType()&uint32(pb.AgentType_AgentBroker) > 0 && in.GetAgentType()&uint32(pb.AgentType_AgentAdmin) > 0 {
			agentString = "经纪人和管理员"
		} else {
			if in.GetAgentType()&uint32(pb.AgentType_AgentBroker) > 0 {
				agentString = "经纪人"
			}
			if in.GetAgentType()&uint32(pb.AgentType_AgentAdmin) > 0 {
				agentString = "管理员"
			}
		}

		content := fmt.Sprintf("你的账号已被设置为公会经营管理后台的%s，可通过该TT账号和密码登录链接：https://appcdn.52tt.com/web/frontend-web-assist-commission-pc/index.html", agentString)
		m.SendOfficialTTMsg(ctx, []uint32{in.GetAgentUid()}, content)
	}

	log.InfoWithCtx(ctx, "AddGuildAgent end in:%v out:%v", in, out)
	return out, nil
}

func (m *GuildManageMgr) DelGuildAgent(ctx context.Context, in *pb.DelGuildAgentReq) (*pb.DelGuildAgentResp, error) {
	out := &pb.DelGuildAgentResp{}

	log.InfoWithCtx(ctx, "DelGuildAgent begin in:%v", in)

	agentStr := ""
	if in.GetAgentType()&uint32(pb.AgentType_AgentBroker) > 0 && in.GetAgentType()&uint32(pb.AgentType_AgentAdmin) > 0 {
		agentStr = "经纪人和管理员"
	} else {
		if in.GetAgentType()&uint32(pb.AgentType_AgentBroker) > 0 {
			agentStr = "经纪人"
		}
		if in.GetAgentType()&uint32(pb.AgentType_AgentAdmin) > 0 {
			agentStr = "管理员"
		}
	}

	pushMsg := fmt.Sprintf("你的账号的公会经营管理后台%s权限已被解除", agentStr)

	err := m.DelGuildAgentByType(ctx, in.GetGuildId(), in.GetAgentUid(), in.GetAgentType(), pushMsg)
	if err != nil {
		log.ErrorWithCtx(ctx, "DelGuildAgent DelGuildAgentByType failed in:%v err:%v", in, err)
		return out, DatabaseMysqlErr
	}

	log.InfoWithCtx(ctx, "DelGuildAgent end in:%v out:%v", in, out)
	return out, nil
}

func (m *GuildManageMgr) DelGuildAgentByType(ctx context.Context, guildId, agentUid, agentType uint32, pushMsg string) error {
	if agentType == 0 {
		return nil
	}

	agentList, err := m.mysqlDb.GetGuildAgentList(guildId, agentType, []uint32{agentUid})
	if err != nil {
		log.ErrorWithCtx(ctx, "DelGuildAgentByType GetGuildAgentList failed %d %d %d err:%v", guildId, agentUid, agentType, err)
		return DatabaseMysqlErr
	}

	if len(agentList) == 0 {
		return nil
	}

	if agentType&uint32(pb.AgentType_AgentBroker) > 0 {
		// 先删除直播数据统计所有主播的该经纪人信息, anchorList 为空表示清除所有
		_, serr := m.liveStatsCli.UpdateGuildAnchorAgentInfo(ctx, guildId, agentUid, []uint32{})
		if serr != nil {
			log.ErrorWithCtx(ctx, "DelGuildAgentByType UpdateGuildAnchorAgentInfo failed %d %d %d err:%v", guildId, agentUid, agentType, serr)
			return serr
		}

		// 删除签约身份表所有主播的该经纪人信息， anchorList 为空表示清除所有
		_, serr = m.anchorContractCli.UpdateSignedAnchorAgentId(ctx, guildId, agentUid, uint32(anchorContractPb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_RADIO_LIVE), []uint32{})
		if serr != nil {
			log.ErrorWithCtx(ctx, "DelGuildAgentByType UpdateSignedAnchorAgentId failed %d %d %d err:%v", guildId, agentUid, agentType, serr)
			return serr
		}

		err = m.mysqlDb.DelAgentAllAnchor(agentUid)
		if err != nil {
			log.ErrorWithCtx(ctx, "DelGuildAgentByType DelAgentAllAnchor failed %d %d %d err:%v", guildId, agentUid, agentType, err)
			return DatabaseMysqlErr
		}

		log.DebugWithCtx(ctx, "DelGuildAgentByType DelAgentAllAnchor broker %d %d %d in:%v", guildId, agentUid, agentType)
	}

	agentInfo := agentList[0]
	permission := &pb.GuildManagePermission{
		GuildManageMenuPer: &pb.MenuPermission{},
		LiveDataPermission: &pb.LiveDataPermission{},
		FunctionPer:        &pb.FunctionPermission{},
		DataPer:            &pb.DataPermission{},
	}

	if agentInfo.Permission != "" {
		err = json.Unmarshal([]byte(agentInfo.Permission), permission)
		if err != nil {
			log.ErrorWithCtx(ctx, "DelGuildAgentByType DelAgentAllAnchor json.Unmarshal failed %d %d %d agent:%v err:%v", guildId, agentUid, agentType, agentInfo, err)
			return err
		}
	}

	resPermission := &pb.GuildManagePermission{
		GuildManageMenuPer: &pb.MenuPermission{},
		LiveDataPermission: &pb.LiveDataPermission{},
		FunctionPer:        &pb.FunctionPermission{},
		DataPer:            &pb.DataPermission{},
	}

	if agentInfo.AgentType^agentType == uint32(pb.AgentType_AgentBroker) {
		resPermission.GuildManageMenuPer = &pb.MenuPermission{
			GuildLiveDataMenuPer: permission.GuildManageMenuPer.GuildLiveDataMenuPer,
			FlowCardMenuPer:      permission.GuildManageMenuPer.FlowCardMenuPer,
		}
		resPermission.FunctionPer = &pb.FunctionPermission{
			LiveTotalData:      permission.FunctionPer.LiveTotalData,
			LiveTaskData:       permission.FunctionPer.LiveTaskData,
			AnchorData:         permission.FunctionPer.AnchorData,
			AnchorPracAnalysis: permission.FunctionPer.AnchorPracAnalysis,
			FlowCard:           permission.FunctionPer.FlowCard,
		}
		resPermission.DataPer = &pb.DataPermission{
			LiveStats:  permission.DataPer.LiveStats,
			AnchorData: permission.DataPer.AnchorData,
			AnchorPrac: permission.DataPer.AnchorPrac,
		}
	}

	if agentInfo.AgentType^agentType == uint32(pb.AgentType_AgentAdmin) {
		resPermission = permission
	}

	bytePermission, err := json.Marshal(resPermission)
	if err != nil {
		log.ErrorWithCtx(ctx, "DelGuildAgentByType json.Marshal failed %d %d %d err:%v", guildId, agentUid, agentType, err)
		return err
	}

	newGuildId := guildId
	if agentInfo.AgentType^agentType == 0 {
		newGuildId = 0
	}

	err = m.mysqlDb.DelGuildAgentByType(agentUid, guildId, newGuildId, agentType, string(bytePermission))
	if err != nil {
		log.ErrorWithCtx(ctx, "DelGuildAgentByType failed %d %d %d err:%v", guildId, agentUid, agentType, err)
		return DatabaseMysqlErr
	}

	m.SendOfficialTTMsg(ctx, []uint32{agentUid}, pushMsg)

	log.InfoWithCtx(ctx, "DelGuildAgentByType end %d %d %d ", guildId, agentUid, agentType)
	return nil
}

func (m *GuildManageMgr) UpdateAgentDataPermission(ctx context.Context, in *pb.UpdateAgentDataPermissionReq) (*pb.UpdateAgentDataPermissionResp, error) {
	out := &pb.UpdateAgentDataPermissionResp{}

	log.InfoWithCtx(ctx, "UpdateAgentDataPermission begin in:%v", in)

	bytePermisson, _ := json.Marshal(in.GetPermission())
	err := m.mysqlDb.UpdateGuildAgentLiveDataPermission(in.GetAgentUid(), in.GetGuildId(), string(bytePermisson))
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateAgentDataPermission failed in:%v err:%v", in, err)
		return out, DatabaseMysqlErr
	}

	log.InfoWithCtx(ctx, "UpdateAgentDataPermission end in:%v out:%v", in, out)
	return out, nil
}

func (m *GuildManageMgr) GetAgentGuild(ctx context.Context, in *pb.GetAgentGuildReq) (*pb.GetAgentGuildResp, error) {
	out := &pb.GetAgentGuildResp{}

	log.DebugWithCtx(ctx, "GetAgentGuild begin in:%v", in)

	info, err := m.mysqlDb.GetAgentGuild(in.GetAgentUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAgentGuild failed in:%v err:%v", in, err)
		return out, DatabaseMysqlErr
	}

	permission := &pb.GuildManagePermission{
		GuildManageMenuPer: &pb.MenuPermission{},
		LiveDataPermission: &pb.LiveDataPermission{},
		FunctionPer:        &pb.FunctionPermission{},
		DataPer:            &pb.DataPermission{},
	}

	if info.Permission != "" {
		err = json.Unmarshal([]byte(info.Permission), permission)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetAgentGuild json.Unmarshal failed in:%v per:%v", info, err)
			return out, err
		}
	}

	out.Info = &pb.GuildAgent{
		Uid:        info.AgentUid,
		Permission: permission,
		AddTs:      info.BrokerAddTs,
		Guild_Id:   info.GuildId,
		AgentType:  info.AgentType,
		AdminAddTs: info.AdminAddTs,
	}

	log.DebugWithCtx(ctx, "GetAgentGuild end in:%v out:%v", in, out)
	return out, nil
}

func (m *GuildManageMgr) GetAgentAnchorList(ctx context.Context, in *pb.GetAgentAnchorListReq) (*pb.GetAgentAnchorListResp, error) {
	out := &pb.GetAgentAnchorListResp{}

	log.DebugWithCtx(ctx, "GetAgentAnchorList begin in:%v", in)
	infoList, err := m.mysqlDb.GetAgentAnchorList(in.GetAgentUid(), (in.GetPage()-1)*in.GetPageSize(), in.GetPageSize())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAgentAnchorList failed in:%v err:%v", in, err)
		return out, DatabaseMysqlErr
	}

	totalCnt, err := m.mysqlDb.GetAgentAnchorTotalCnt(in.GetAgentUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAgentAnchorList GetAgentAnchorTotalCnt failed in:%v err:%v", in, err)
		return out, DatabaseMysqlErr
	}

	for _, info := range infoList {
		tmpInfo := &pb.AgentAnchorInfo{
			AnchorUid: info.AnchorUid,
			AgentUid:  info.AgentUid,
		}

		out.InfoList = append(out.InfoList, tmpInfo)
	}

	out.NextPage = in.GetPage() + 1
	out.TotalCnt = totalCnt
	if len(out.InfoList) < int(in.GetPageSize()) {
		out.NextPage = 0
	}

	log.DebugWithCtx(ctx, "GetAgentAnchorList end in:%v out:%v", in, out)
	return out, nil
}

func (m *GuildManageMgr) GetAgentAnchor(ctx context.Context, in *pb.GetAgentAnchorReq) (*pb.GetAgentAnchorResp, error) {
	out := &pb.GetAgentAnchorResp{}

	log.DebugWithCtx(ctx, "GetAgentAnchor begin in:%v", in)

	info, err := m.mysqlDb.GetAgentAnchor(in.GetAgentUid(), in.GetAnchorUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAgentAnchor failed in:%v err:%v", in, err)
		return out, DatabaseMysqlErr
	}

	out.Info = &pb.AgentAnchorInfo{
		AnchorUid: info.AnchorUid,
		AgentUid:  info.AgentUid,
	}

	log.DebugWithCtx(ctx, "GetAgentAnchor end in:%v out:%v", in, out)
	return out, nil
}

func (m *GuildManageMgr) GetAnchorAgent(ctx context.Context, in *pb.GetAnchorAgentReq) (*pb.GetAnchorAgentResp, error) {
	out := &pb.GetAnchorAgentResp{}

	log.DebugWithCtx(ctx, "GetAnchorAgent begin in:%v", in)

	info, err := m.mysqlDb.GetAnchorAgent(in.GetAnchorUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAnchorAgent failed in:%v err:%v", in, err)
		return out, DatabaseMysqlErr
	}

	out.Info = &pb.AgentAnchorInfo{
		AnchorUid: info.AnchorUid,
		AgentUid:  info.AgentUid,
	}

	log.DebugWithCtx(ctx, "GetAnchorAgent end in:%v out:%v", in, out)
	return out, nil
}

func (m *GuildManageMgr) AddAgentAnchor(ctx context.Context, in *pb.AddAgentAnchorReq) (*pb.AddAgentAnchorResp, error) {
	out := &pb.AddAgentAnchorResp{}

	log.InfoWithCtx(ctx, "AddAgentAnchor begin in:%v", in)

	for _, anchorUid := range in.GetUidList() {
		err := m.mysqlDb.AddAgentAnchor(anchorUid, in.GetAgentUid())
		if err != nil {
			log.ErrorWithCtx(ctx, "AddAgentAnchor failed agentUid:%v anchorUid:%v err:%v", in.GetAgentUid(), anchorUid, err)
			errMsg := DatabaseMysqlErr.Error()
			out.ErrList = append(out.ErrList, &pb.AnchorErrorMsg{
				Uid:    anchorUid,
				ErrMsg: errMsg,
			})
		}
	}

	mapUid2Err := make(map[uint32]bool, 0)
	for _, errList := range out.ErrList {
		mapUid2Err[errList.GetUid()] = true
	}

	tmpUidList := make([]uint32, 0)
	for index, anchorUid := range in.GetUidList() {
		if _, ok := mapUid2Err[anchorUid]; !ok {
			tmpUidList = append(tmpUidList, anchorUid)
			if len(tmpUidList) == 50 || index == len(in.GetUidList())-1 {
				_, serr := m.liveStatsCli.UpdateGuildAnchorAgentInfo(ctx, in.GetGuildId(), in.GetAgentUid(), tmpUidList)
				if serr != nil {
					log.ErrorWithCtx(ctx, "AddAgentAnchor UpdateGuildAnchorAgentInfo failed in:%v uidList:%v err:%v", in, tmpUidList, serr)
				}

				// 删除签约身份表所有主播的该经纪人信息， anchorList 为空表示清除所有
				_, serr = m.anchorContractCli.UpdateSignedAnchorAgentId(ctx, in.GetGuildId(), in.GetAgentUid(), uint32(anchorContractPb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_RADIO_LIVE), tmpUidList)
				if serr != nil {
					log.ErrorWithCtx(ctx, "AddAgentAnchor UpdateSignedAnchorAgentId failed in:%v err:%v", in, serr)
					return out, serr
				}

				tmpUidList = tmpUidList[0:0]
			}
		}
	}

	log.InfoWithCtx(ctx, "AddAgentAnchor end in:%v out:%v", in, out)
	return out, nil
}

func (m *GuildManageMgr) DelAgentAnchor(ctx context.Context, in *pb.DelAgentAnchorReq) (*pb.DelAgentAnchorResp, error) {
	out := &pb.DelAgentAnchorResp{}

	log.InfoWithCtx(ctx, "DelAgentAnchor begin in:%v", in)

	err := m.mysqlDb.DelAgentAnchor(in.GetAgentUid(), in.GetUidList())
	if err != nil {
		log.ErrorWithCtx(ctx, "DelAgentAnchor failed in:%v err:%v", in, err)
		return out, DatabaseMysqlErr
	}

	tmpUidList := make([]uint32, 0)
	for index, anchorUid := range in.GetUidList() {
		tmpUidList = append(tmpUidList, anchorUid)
		if len(tmpUidList) == 50 || index == len(in.GetUidList())-1 {
			_, serr := m.liveStatsCli.UpdateGuildAnchorAgentInfo(ctx, in.GetGuildId(), 0, tmpUidList)
			if serr != nil {
				log.ErrorWithCtx(ctx, "DelAgentAnchor UpdateGuildAnchorAgentInfo failed in:%v uidList:%v err:%v", in, tmpUidList, serr)
			}

			_, serr = m.anchorContractCli.UpdateSignedAnchorAgentId(ctx, in.GetGuildId(), 0, uint32(anchorContractPb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_RADIO_LIVE), tmpUidList)
			if serr != nil {
				log.ErrorWithCtx(ctx, "DelAgentAnchor UpdateSignedAnchorAgentId failed in:%v err:%v", in, serr)
				return out, serr
			}

			tmpUidList = tmpUidList[0:0]
		}
	}

	log.InfoWithCtx(ctx, "DelAgentAnchor end in:%v out:%v", in, out)
	return out, nil
}

func (m *GuildManageMgr) CheckIsNeedVerify(ctx context.Context, in *pb.CheckIsNeedVerifyReq) (*pb.CheckIsNeedVerifyResp, error) {
	out := &pb.CheckIsNeedVerifyResp{}

	isNeed, err := m.cacheCli.CheckIsNeedVerify(in.GetUid(), in.GetScene())
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckIsNeedVerify failed in:%v err:%v", in, err)
		return out, err
	}
	out.IsNeed = isNeed

	log.InfoWithCtx(ctx, "CheckIsNeedVerify end in:%v out:%v", in, out)
	return out, nil
}

func (m *GuildManageMgr) SetVerifyFlag(ctx context.Context, in *pb.SetVerifyFlagReq) (*pb.SetVerifyFlagResp, error) {
	out := &pb.SetVerifyFlagResp{}

	if ttl, ok := mapScene2TTL[pb.VerifyScene(in.GetScene())]; ok {
		err := m.cacheCli.SetVerifyFlag(in.GetUid(), in.GetScene(), ttl)
		if err != nil {
			log.ErrorWithCtx(ctx, "SetVerifyFlag failed in:%v err:%v", in, err)
			return out, err
		}
	}

	log.InfoWithCtx(ctx, "SetVerifyFlag end in:%v out:%v", in, out)
	return out, nil
}

func (m *GuildManageMgr) sendInviteMsg(ctx context.Context, inviteInfo mysql.GuildAgentInvite, inviteStatus revenueBasePb.InviteStatusType, procDesc, procHighLight string) error {
	mapId2User, sErr := m.accountCli.BatGetUserByUid(ctx, TTUid, inviteInfo.InviteUid, inviteInfo.InvitedUid)
	if sErr != nil {
		log.ErrorWithCtx(ctx, "sendInviteMsg BatGetUserByUid failed inviteInfo:%v sErr:%v", inviteInfo, sErr)
		return sErr
	}

	guildInfo, sErr := m.guildCli.GetGuild(ctx, inviteInfo.GuildId)
	if sErr != nil {
		log.ErrorWithCtx(ctx, "sendInviteMsg GetGuild failed inviteInfo:%v sErr:%v", inviteInfo, sErr)
		return sErr
	}

	guildId := inviteInfo.GuildId
	if guildInfo.GetShortId() != 0 {
		guildId = guildInfo.GetShortId()
	}

	var fromMsgId, toMsgId uint64 = 0, 0
	if inviteStatus != revenueBasePb.InviteStatusType_INVITE_STATUS_TYPE_PROCESSING {
		tmpFromMsgId, tmpToMsgId, err := m.cacheCli.GetImMsgId(ctx, inviteInfo.Id)
		if err != nil {
			log.ErrorWithCtx(ctx, "sendInviteMsg GetImMsgId inviteInfo in:%v err:%v", inviteInfo, err)
			return err
		}
		fromMsgId = tmpFromMsgId
		toMsgId = tmpToMsgId
	}

	inviteMsg := &revenueBasePb.GuildManageRoleInviteMsg{
		Id:     inviteInfo.Id,
		Status: inviteStatus,
		Title:  "邀请",
		Content: fmt.Sprintf("%s(TTID:%s)邀请您成为%s公会(ID:%d)的 %s 角色",
			mapId2User[inviteInfo.InviteUid].GetNickname(), mapId2User[inviteInfo.InviteUid].GetAlias(), guildInfo.GetName(), guildId, mapAgentType2Msg[inviteInfo.AgentType]),
		ContentHighLight:  mapAgentType2Msg[inviteInfo.AgentType],
		ProcDesc:          procDesc,
		ProcDescHighLight: procHighLight,
	}

	msgByte, err := proto.Marshal(inviteMsg)
	if err != nil {
		log.ErrorWithCtx(ctx, "sendInviteMsg proto.Marshal failed inviteInfo:%v err:%v", inviteInfo, err)
		return err
	}

	fromMsgId, toMsgId, err = m.SendCustomizeImMsg(ctx, mapId2User[TTUid], mapId2User[inviteInfo.InvitedUid], fromMsgId, toMsgId, msgByte, msgByte,
		uint32(imPB.IM_MSG_TYPE_GUILD_MANAGE_ROLE_INVITE_MSG))
	if err != nil {
		log.ErrorWithCtx(ctx, "sendInviteMsg SendCustomizeImMsg failed inviteInfo:%v err:%v", inviteInfo, err)
		return err
	}

	if inviteStatus == revenueBasePb.InviteStatusType_INVITE_STATUS_TYPE_PROCESSING {
		// 记录msgId
		err = m.cacheCli.RecordImMsgId(ctx, inviteInfo.Id, fromMsgId, toMsgId)
		if err != nil {
			log.ErrorWithCtx(ctx, "sendInviteMsg RecordImMsgId failed  inviteInfo:%v err:%v", inviteInfo, err)
			return err
		}
	}

	log.InfoWithCtx(ctx, "sendInviteMsg end inviteInfo:%v inviteMsg:%v fromMsgId:%d toMsgId:%d", inviteInfo, inviteMsg, fromMsgId, toMsgId)
	return nil
}

func (m *GuildManageMgr) SendGuildAgentInvite(ctx context.Context, req *pb.SendGuildAgentInviteReq) (*pb.SendGuildAgentInviteResp, error) {
	out := &pb.SendGuildAgentInviteResp{}

	nowTm := time.Now()
	id, err := m.mysqlDb.InviteGuildAgent(req.GetGuildId(), req.GetInviteUid(), req.GetInvitedUid(), req.GetAgentType(),
		uint32(pb.InviteStatusType_INVITE_STATUS_TYPE_PROCESSING), nowTm)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendGuildAgentInvite failed in:%v err:%v", req, err)
		return out, err
	}

	expireTm := nowTm.Add(time.Second * time.Duration(InviteExpireTs))
	procDesc := fmt.Sprintf("请在%04d-%02d-%02d %02d时%02d分前提交反馈，逾期邀请失效不可操作；接受邀请获得公会任意角色后如需解约需要自行与会长协商；",
		expireTm.Year(), expireTm.Month(), expireTm.Day(), expireTm.Hour(), expireTm.Minute())

	err = m.sendInviteMsg(ctx, mysql.GuildAgentInvite{
		Id:         id,
		GuildId:    req.GetGuildId(),
		InviteUid:  req.GetInviteUid(),
		InvitedUid: req.GetInvitedUid(),
		AgentType:  req.GetAgentType(),
		UpdateTime: nowTm,
		CreateTime: nowTm,
	}, revenueBasePb.InviteStatusType_INVITE_STATUS_TYPE_PROCESSING, procDesc, "")
	if err != nil {
		log.ErrorWithCtx(ctx, "SendGuildAgentInvite sendInviteMsg failed in:%v err:%v", req, err)
	}

	log.InfoWithCtx(ctx, "SendGuildAgentInvite end in:%v out:%v id:%d", req, out, id)
	return out, nil
}

func (m *GuildManageMgr) GetGuildAgentInviteList(ctx context.Context, req *pb.GetGuildAgentInviteListReq) (*pb.GetGuildAgentInviteListResp, error) {
	out := &pb.GetGuildAgentInviteListResp{}

	if req.GetPage() <= 0 {
		log.InfoWithCtx(ctx, "GetGuildAgentInviteList failed in:%v err:%v", req, "Page is invalid")
		return out, nil
	}

	inviteList, err := m.mysqlDb.GetGuildAgentInviteList(req.GetGuildId(), req.GetInviteStatus(), req.GetUidList(), (req.GetPage()-1)*req.GetPageSize(), req.GetPageSize())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildAgentInviteList failed in:%v err:%v", req, err)
		return out, protocol.NewExactServerError(nil, status.ErrRepositoryFailed)
	}

	totalCnt, err := m.mysqlDb.GetGuildAgentInviteTotalCnt(req.GetGuildId(), req.GetInviteStatus(), req.GetUidList())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildAgentInviteList GetGuildAgentInviteTotalCnt failed in:%v err:%v", req, err)
		return out, protocol.NewExactServerError(nil, status.ErrRepositoryFailed)
	}

	for _, invite := range inviteList {
		tmpInfo := &pb.GuildAgentInvite{
			Id:           invite.Id,
			Uid:          invite.InvitedUid,
			Ts:           uint32(invite.CreateTime.Unix()),
			AgentType:    invite.AgentType,
			InviteStatus: invite.InviteStatus,
		}

		out.InviteList = append(out.InviteList, tmpInfo)
	}

	out.NextPage = req.GetPage() + 1
	out.TotalCnt = totalCnt
	if len(out.InviteList) < int(req.GetPageSize()) {
		out.NextPage = 0
	}

	log.DebugWithCtx(ctx, "GetGuildAgentInviteList end in:%v out:%v", req, out)
	return out, nil
}

func (m *GuildManageMgr) CancelGuildAgentInvite(ctx context.Context, req *pb.CancelGuildAgentInviteReq) (*pb.CancelGuildAgentInviteResp, error) {
	out := &pb.CancelGuildAgentInviteResp{}

	//获取邀请信息
	inviteInfo, err := m.mysqlDb.GetAgentInviteById(req.GetId())
	if err != nil {
		log.ErrorWithCtx(ctx, "CancelGuildAgentInvite GetAgentInviteById failed in:%v err:%v", req, err)
		return out, protocol.NewExactServerError(nil, status.ErrRepositoryFailed)
	}

	rowCnt, err := m.mysqlDb.UpdateGuildAgentInviteStatus(req.GetId(), uint32(pb.InviteStatusType_INVITE_STATUS_TYPE_PROCESSING), uint32(pb.InviteStatusType_INVITE_STATUS_TYPE_CANCEL))
	if err != nil {
		log.ErrorWithCtx(ctx, "CancelGuildAgentInvite failed in:%v err:%v", req, err)
		return out, protocol.NewExactServerError(nil, status.ErrRepositoryFailed)
	}

	if rowCnt > 0 {
		procDesc := "邀请已被撤回，不可操作"
		err = m.sendInviteMsg(ctx, inviteInfo, revenueBasePb.InviteStatusType_INVITE_STATUS_TYPE_INVALID, procDesc, "")
		if err != nil {
			log.ErrorWithCtx(ctx, "SendGuildAgentInvite sendInviteMsg failed in:%v err:%v", req, err)
		}
	}

	log.InfoWithCtx(ctx, "CancelGuildAgentInvite end in:%v out:%v", req, out)
	return out, nil
}

func (m *GuildManageMgr) ProcGuildAgentInvite(ctx context.Context, req *pb.ProcGuildAgentInviteReq) (*pb.ProcGuildAgentInviteResp, error) {
	resp := &pb.ProcGuildAgentInviteResp{}

	//获取邀请信息
	inviteInfo, err := m.mysqlDb.GetAgentInviteById(req.GetId())
	if err != nil {
		log.ErrorWithCtx(ctx, "ProcGuildAgentInvite  GetAgentInviteById failed in:%v err:%v", req, err)
		return resp, protocol.NewExactServerError(nil, status.ErrRepositoryFailed)
	}

	if inviteInfo.InvitedUid != req.GetUid() {
		log.ErrorWithCtx(ctx, "ProcGuildAgentInvite invalid uid in:%v inviteInfo:%v", req, inviteInfo)
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	if inviteInfo.InviteStatus == uint32(pb.InviteStatusType_INVITE_STATUS_TYPE_CANCEL) {
		log.ErrorWithCtx(ctx, "ProcGuildAgentInvite invite has been canceled in:%v inviteInfo:%v", req, inviteInfo)
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "您的邀请已被撤回")
	}

	contractInfo, err := m.anchorContractCli.GetUserContractCacheInfo(ctx, req.GetUid(), req.GetUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "ProcGuildAgentInvite GetUserContractCacheInfo failed in:%v err:%v", req, err)
		return resp, err
	}

	if contractInfo.GetContract().GetGuildId() != inviteInfo.GuildId {
		log.ErrorWithCtx(ctx, "ProcGuildAgentInvite invalid guildId in:%v contractInfo:%v", req, contractInfo)

		rowCnt, err := m.mysqlDb.UpdateGuildAgentInviteStatus(req.GetId(), uint32(pb.InviteStatusType_INVITE_STATUS_TYPE_PROCESSING), uint32(pb.InviteStatusType_INVITE_STATUS_TYPE_CANCEL_SIGN))
		if err != nil {
			log.ErrorWithCtx(ctx, "ProcGuildAgentInvite UpdateGuildAgentInviteStatus failed in:%v err:%v", req, err)
			return resp, protocol.NewExactServerError(nil, status.ErrRepositoryFailed)
		}

		if rowCnt > 0 {
			m.sendInviteMsg(ctx, inviteInfo, revenueBasePb.InviteStatusType_INVITE_STATUS_TYPE_INVALID, "邀请已失效，不可操作", "")
		}

		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "您的账号不符合要求")
	}

	//更新邀请状态
	if req.GetIsAccept() {
		rowCnt, err := m.mysqlDb.UpdateGuildAgentInviteStatus(req.GetId(), uint32(pb.InviteStatusType_INVITE_STATUS_TYPE_PROCESSING), uint32(pb.InviteStatusType_INVITE_STATUS_TYPE_ACCEPT))
		if err != nil {
			log.ErrorWithCtx(ctx, "ProcGuildAgentInvite UpdateGuildAgentInviteStatus failed in:%v err:%v", req, err)
			return resp, protocol.NewExactServerError(nil, status.ErrRepositoryFailed)
		}

		if rowCnt > 0 {
			_, err = m.AddGuildAgent(ctx, &pb.AddGuildAgentReq{
				GuildId:    inviteInfo.GuildId,
				AgentUid:   inviteInfo.InvitedUid,
				Permission: nil,
				AgentType:  inviteInfo.AgentType,
			}, false)
			if err != nil {
				log.ErrorWithCtx(ctx, "ProcGuildAgentInvite AddGuildAgent failed in:%v err:%v", req, err)
			}

			procDes := fmt.Sprintf("您已接受邀请，可使用账号密码登录会长服务后台%s", GuildManageLoginUrl)
			m.sendInviteMsg(ctx, inviteInfo, revenueBasePb.InviteStatusType_INVITE_STATUS_TYPE_ACCEPT, procDes, GuildManageLoginUrl)
		}
	} else {
		rowCnt, err := m.mysqlDb.UpdateGuildAgentInviteStatus(req.GetId(), uint32(pb.InviteStatusType_INVITE_STATUS_TYPE_PROCESSING), uint32(pb.InviteStatusType_INVITE_STATUS_TYPE_REJECT))
		if err != nil {
			log.ErrorWithCtx(ctx, "ProcGuildAgentInvite UpdateGuildAgentInviteStatus failed in:%v err:%v", req, err)
			return resp, protocol.NewExactServerError(nil, status.ErrRepositoryFailed)
		}

		if rowCnt > 0 {
			procDes := "您已拒绝邀请，后续需要增加角色可由会长再次发起邀请"
			m.sendInviteMsg(ctx, inviteInfo, revenueBasePb.InviteStatusType_INVITE_STATUS_TYPE_REJECT, procDes, "")
		}
	}

	log.InfoWithCtx(ctx, "ProcGuildAgentInvite end req:%v resp:%v", req, resp)
	return resp, nil
}

func (m *GuildManageMgr) AddVioExportRecord(ctx context.Context, req *pb.AddVioExportRecordReq) (*pb.AddVioExportRecordResp, error) {
	resp := &pb.AddVioExportRecordResp{}

	err := m.mysqlDb.AddVioExportRecord(&mysql.VioExportRecord{
		GuildId: req.GetGuildId(),
		Uid:     req.GetUid(),
		BeginTs: req.GetBeginTs(),
		EndTs:   req.GetEndTs(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "AddVioExportRecord failed in:%v err:%v", req, err)
		return resp, protocol.NewExactServerError(nil, status.ErrRepositoryFailed)
	}

	log.InfoWithCtx(ctx, "AddVioExportRecord end in:%v resp:%v", req, resp)
	return resp, nil
}

// GetGuildAdminList 获取管理员用户列表
func (m *GuildManageMgr) GetGuildAdminList(ctx context.Context, in *pb.GetGuildAdminListReq) (*pb.GetGuildAdminListResp, error) {
	out := &pb.GetGuildAdminListResp{}
	log.DebugWithCtx(ctx, "GetGuildAdminList begin in:%v", in)
	agentList, err := m.mysqlDb.GetAdminList(in.GetGuildId())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildAdminList GetAdminList failed in:%v err:%v", in, err)
		return out, DatabaseMysqlErr
	}
	adminList := make([]uint32, 0)
	for _, agent := range agentList {
		adminList = append(adminList, agent.AgentUid)
	}
	out.AdminList = adminList
	log.InfoWithCtx(ctx, "GetGuildAdminList end in:%v out:%v", in, out)
	return out, nil
}

// GetBannerInfo 获取公会平台资讯banner
func (m *GuildManageMgr) GetBannerInfo(ctx context.Context, req *pb.GetBannerInfoReq) (*pb.GetBannerInfoResp, error) {
	resp := &pb.GetBannerInfoResp{}

	for _, banner := range m.dyConf.GetBannerConfList() {
		resp.BannerList = append(resp.BannerList, &pb.BannerInfo{
			ImgUrl:  banner.ImgUrl,
			JumpUrl: banner.JumpUrl,
		})
	}

	log.DebugWithCtx(ctx, "GetBannerInfo begin in:%v", req)
	return resp, nil
}
