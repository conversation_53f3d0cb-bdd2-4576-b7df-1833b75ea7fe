package manager

import (
	"context"
	"encoding/json"
	"fmt"
	"golang.52tt.com/pkg/log"
	anchorcontract_go "golang.52tt.com/protocol/services/anchorcontract-go"
	"time"
)

func (m *AnchorContractMgr) AutoPassOrRejectMultiPlayer() {
	log.Infof("AutoPassOrRejectMultiPlayer")
	list, err := m.store.GetNotHandleContractApplyList([]uint32{uint32(anchorcontract_go.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_MULTIPLAYER)},
		[]uint32{uint32(anchorcontract_go.APPLY_SIGN_STATUS_APPLY_SIGN_STATUS_OFFICIAL_HANDLING)})
	if err != nil {
		log.Errorf("GetNotHandleContractApplyList err=%v", err)
		return
	}

	tNow := time.Now()

	type Extra struct {
		TagId uint32 `json:"tag_id"`
	}

	for _, record := range list {
		log.Infof("AutoPassOrRejectMultiPlayer record=%+v", *record)
		extra := &anchorcontract_go.MultiAnchorExtra{}
		err = json.Unmarshal([]byte(record.Extra), extra)
		if err != nil {
			log.Errorf("genApplyRecordPb fail to Unmarshal. err:%v", err)
			continue
		}

		fe := &Extra{}
		err = json.Unmarshal([]byte(record.FormExtra), fe)
		if err != nil {
			log.Errorf("genApplyRecordPb fail to Unmarshal. err:%v", err)
			continue
		}

		if record.Age < 45 && record.RechargeNum <= 5000 && len(extra.LoginOtherUids) == 0 {
			//可以直接通过，已有功能，在会长审批后，就自动判断 CheckIfNeedSysQuickHandle
			/*err = m.OfficialHandleApplySignV2(context.Background(), record.Uid, fe.TagId, record.Id, record.GuildId, anchorcontract_go.SIGN_ANCHOR_IDENTITY(record.IdentityType),
				anchorcontract_go.HANDLE_SIGN_APPLY_OPR_HANDLE_SIGN_APPLY_OPR_ACCEPT, "system")
			if err != nil {
				log.Errorf("GetNotHandleContractApplySignV2 err:%v", err)
				continue
			}*/
		} else {
			//需要手动处理
			if record.ApplyTime.Before(tNow.Add(-time.Duration(m.dyConfig.GetAutoPassOrRejectMultiPlayerTs()) * time.Second)) {
				//申请时间在12小时前的，则直接拒绝
				log.Infof("before OfficialHandleApplySignV2 record.ApplyTime=%v, tNow=%v, GetAutoPassOrRejectMultiPlayerTs=%v", record.ApplyTime, tNow, m.dyConfig.GetAutoPassOrRejectMultiPlayerTs())
				msg := ""
				if record.Age >= 45 {
					msg = "账号身份信息异常，签约申请不通过，请使用个人真实身份信息实名签约公会，感谢配合。如有疑问可联系客服或官方账号80051。"
				} else if record.RechargeNum > 5000 {
					msg = "您的账号属于消费账号，不符合多人互动身份签约要求，签约申请不通过。\n该实名30天内无法再次发起多人互动身份申请，感谢您的支持与谅解"
				} else {
					msg = "账号存在小号签约公会，且与申请中的公会ID不一致，有违反签约规则风险，签约申请不通过。\n该实名30天内无法再次发起多人互动身份申请，如再次申请需停止账号共享或将其他签约账号解约，感谢配合"
				}
				//alter table tbl_contract_apply_v2 modify column remarks varchar(1024)
				err = m.OfficialHandleApplySignV2(context.Background(), record.Uid, fe.TagId, record.Id, record.GuildId, anchorcontract_go.SIGN_ANCHOR_IDENTITY(record.IdentityType),
					anchorcontract_go.HANDLE_SIGN_APPLY_OPR_HANDLE_SIGN_APPLY_OPR_REJECT, "system", msg)
				if err != nil {
					log.Errorf("GetNotHandleContractApplySignV2 err:%v", err)
					continue
				}
				err = m.SendOfficialTTMsg(record.Uid, msg)
				if err != nil {
					log.Errorf("SendOfficialTTMsg fail err:%v", err)
					continue
				}
				ctx, cancel := context.WithTimeout(context.Background(), time.Second*3)
				guildData, sErr := m.guildCli.GetGuild(ctx, record.GuildId)

				if sErr != nil {
					log.Errorf("GetGuildData fail err:%v", sErr)
					continue
				}

				userData, sErr := m.accountCli.GetUserByUid(ctx, record.Uid)
				if sErr != nil {
					log.Errorf("GetUserByUid fail err:%v", sErr)
					continue
				}
				cancel()

				ownerMsg := fmt.Sprintf("用户昵称%s（%s）的多人互动成员身份申请被驳回，原因：%s", userData.Nickname, userData.Alias, msg)

				err = m.SendOfficialTTMsg(guildData.Owner, ownerMsg)
				if err != nil {
					log.Errorf("SendOfficialTTMsg fail err:%v", err)
					continue
				}
			}
		}
	}
}
