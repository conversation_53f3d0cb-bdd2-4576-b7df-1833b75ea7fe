package post_hot_arith

import (
	"math"
	"time"

	pb "golang.52tt.com/protocol/services/ugc/post_examine"
)

/*//热度值计算方式

黄菲菲  11:33:43
互动热度值= 0.6 * 浏览量UV + 3 * 赞 + 5* 评论（仅计算第一次评论量）
上热门的互动热度值至少>300


由于前期的内容还比较少，时间衰减周期暂时定为10天；

统一 /0.6


热门需要 500

评论 9
点赞 5
浏览 1

}*/

const (
	commentVal  = 9
	attitudeVal = 5
	visitVal    = 1
	shareVal    = 6
)

func GetInteractByBehavior(behaviorType pb.RecordPostInteractReq_BehaviorType) int32 {

	switch behaviorType {
	case pb.RecordPostInteractReq_Comment:
		return commentVal
	case pb.RecordPostInteractReq_Attitude:
		return attitudeVal
	case pb.RecordPostInteractReq_Visit:
		return visitVal
	case pb.RecordPostInteractReq_Share:
		return shareVal
	}

	return 0
}

//互动热度计算
func CalInteractVal(commentCount, likeCount, visitorCount, shareCount uint32, N float64) uint32 {
	val := visitorCount*visitVal + attitudeVal*likeCount + commentVal*commentCount + shareCount*shareVal
	return uint32(float64(val) / N)
}

//互动值约束
func InteractValConstraint(interval int32, createAt uint32, so float64) int32 {

	if interval <= 3000 {
		return interval
	}
	return 3000 + int32(DecrementTimeDecay(uint32(interval)-3000, createAt, so))
}

//时间衰减计算 渐减 v3
func DecrementTimeDecay(base, createAt uint32, so float64) uint32 {

	nowHour := time.Now().Unix() / 3600
	createHour := int64(createAt) / 3600

	per := float64(nowHour - createHour)

	if per < 0 {
		return base
	}

	return uint32(float64(base) * math.Pow(math.E, -so*per))
}

func CalculateExtra(originVal, createAt, targetVal uint32, so float64) int32 {
	nowHour := time.Now().Unix() / 3600
	createHour := int64(createAt) / 3600

	per := float64(nowHour - createHour)

	base := float64(targetVal) / (math.Pow(math.E, -so*per))

	return int32(base) - int32(originVal)

}
