package chair_game

import (
    "context"
    "golang.52tt.com/services/channel-wedding-minigame/internal/model/chair-game/cache"
    "golang.52tt.com/services/channel-wedding-minigame/internal/model/chair-game/store"
    "testing"
    "time"
    "github.com/golang/mock/gomock"
    "golang.52tt.com/services/channel-wedding-minigame/internal/conf"
)

func TestMgr_CheckRoundEndHandle(t *testing.T) {
    initTestMgr(t)
    roundEndgameInfo := &cache.ChairGameInfo{
        Cid:             testCid,
        WeddingId:       testWeddingId,
        GameId:          1,
        CurRound:        1,
        ChairNum:        3,
        StartTime:       now.Add(-10 * time.Second).UnixMilli(),
        EndTime:         now.Add(1 * time.Second).UnixMilli(),
        CurRoundPlayers: []uint32{1, 2, 3, 4},
        GamePlayers:     gamePlayers,
        Reward:          reward,
        GameOverTime:    0, // 未结束
    }

    overGameInfo := &cache.ChairGameInfo{
        Cid:             testCid,
        WeddingId:       testWeddingId,
        GameId:          1,
        CurRound:        1,
        ChairNum:        3,
        StartTime:       now.Add(-10 * time.Second).UnixMilli(),
        EndTime:         now.Add(-1 * time.Second).UnixMilli(),
        CurRoundPlayers: []uint32{1, 2, 3, 4},
        GamePlayers:     gamePlayers,
        Reward:          reward,
        GameOverTime:    0, // 未结束
    }
    winners := []uint32{1, 2, 3}
    oneWinner := []uint32{1}

    type args struct {
        ctx context.Context
        cid uint32
    }
    tests := []struct {
        name     string
        initFunc func()
        args     args
        wantErr  bool
    }{
        {
            name: "游戏已结束-已处理",
            initFunc: func() {
                mockCache.EXPECT().GetChairGameInfo(ctx, testCid).Return(&cache.ChairGameInfo{
                    GameId:       1,
                    GameOverTime: 1,
                }, nil)
            },
            args: args{
                ctx: context.Background(),
                cid: testCid,
            },
            wantErr: false,
        },
        {
            name: "轮次结束",
            initFunc: func() {
                mockCache.EXPECT().GetChairGameInfo(ctx, testCid).Return(roundEndgameInfo, nil)
                mockCache.EXPECT().GetChairGameGrabQueue(ctx, gomock.Any(), gomock.Any(), gomock.Any()).Return(winners, nil)
                mockCache.EXPECT().GetRedisServerTime(ctx).Return(now, nil).AnyTimes()
                mockBc.EXPECT().GetHostStartButtonDuration().Return(int64(10)).AnyTimes()
                mockBc.EXPECT().GetRewardInfoListByPackId(gomock.Any()).Return([]*conf.RewardInfo{
                    reward,
                }).AnyTimes()
                mockBc.EXPECT().GetChairGameBackSender().Return(&conf.BackSenderConf{
                    AppId: 1,
                    Sign:  "1",
                }).AnyTimes()

                mockCache.EXPECT().GetChairGameInfo(gomock.Any(), gomock.Any()).Return(roundEndgameInfo, nil)
                mockCache.EXPECT().GetChairGameGrabQueue(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(winners, nil)
                mockBc.EXPECT().GetAutoStartNextRoundDuration().Return(int64(10)).AnyTimes()
                mockCache.EXPECT().AddChairGameTimerQueue(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
                ayLayMock.EXPECT().ChairGameInfoChannelNotify(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
            },
            args: args{
                ctx: context.Background(),
                cid: testCid,
            },
            wantErr: false,
        },
        {
            name: "游戏结束",
            initFunc: func() {
                mockCache.EXPECT().GetChairGameInfo(gomock.Any(), testCid).Return(overGameInfo, nil).AnyTimes()
                mockCache.EXPECT().GetChairGameGrabQueue(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(oneWinner, nil).AnyTimes()
                mockCache.EXPECT().GetRedisServerTime(gomock.Any()).Return(now, nil).AnyTimes()
                mockBc.EXPECT().GetHostStartButtonDuration().Return(int64(10)).AnyTimes()
                mockBc.EXPECT().GetRewardInfoListByPackId(gomock.Any()).Return([]*conf.RewardInfo{
                    reward,
                }).AnyTimes()
                mockBc.EXPECT().GetChairGameBackSender().Return(&conf.BackSenderConf{
                    AppId: 1,
                    Sign:  "1",
                }).AnyTimes()

                mockStore.EXPECT().Transaction(ctx, gomock.Any()).Return(nil)

                mockCache.EXPECT().LockUpdateChairGameInfo(ctx, gomock.Any(), gomock.Any()).Return(true, nil)
                mockCache.EXPECT().UnlockUpdateChairGameInfo(ctx, gomock.Any()).Return(nil).AnyTimes()
                mockCache.EXPECT().GetChairGameInfo(gomock.Any(), testCid).Return(overGameInfo, nil).AnyTimes()
                mockCache.EXPECT().SetChairGameInfo(ctx, gomock.Any()).Return(nil)
                mockCache.EXPECT().AddChairGameSettleQueue(ctx, gomock.Any(), gomock.Any()).Return(nil)

                mockCache.EXPECT().DelChairGameEnrollList(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
                ayLayMock.EXPECT().ChairGameInfoChannelNotify(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
                ayLayMock.EXPECT().KickOutMicSpace(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
            },
            args: args{
                ctx: context.Background(),
                cid: testCid,
            },
            wantErr: false,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := testMgr
            if tt.initFunc != nil {
                tt.initFunc()
            }
            if err := m.CheckRoundEndHandle(tt.args.ctx, tt.args.cid); (err != nil) != tt.wantErr {
                t.Errorf("CheckRoundEndHandle() error = %v, wantErr %v", err, tt.wantErr)
            }
        })
    }
}

func TestMgr_ReissueAwardHandle(t *testing.T) {
    initTestMgr(t)
    tests := []struct {
        name     string
        initFunc func()
    }{
        {
            name: "success",
            initFunc: func() {
                mockBc.EXPECT().GetReissueMaxIntervalHour().Return(uint32(1))
                mockStore.EXPECT().GetAwardRecordByStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]*store.AwardRecord{
                    {
                        ID:       1,
                        OrderID:  "11",
                        GameId:   1,
                        Uid:      1,
                        GiftID:   "1",
                        GiftType: conf.GiftTypePackage,
                        Amount:   1,
                    },
                }, nil).AnyTimes()

                mockStore.EXPECT().GetGameLogByGameId(gomock.Any(), gomock.Any()).Return(&store.GameLog{
                    ID: 1,
                }, nil).AnyTimes()
                ayLayMock.EXPECT().AwardPackage(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
                mockStore.EXPECT().UpdateAwardStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
            },
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := testMgr
            if tt.initFunc != nil {
                tt.initFunc()
            }
            m.ReissueAwardHandle()
        })
    }
}

func TestMgr_autoStartGrabHandle(t *testing.T) {
    initTestMgr(t)
    gameRound := &cache.ChairGameRound{
        Cid:      testCid,
        GameId:   testGameId,
        CurRound: 1,
    }

    testNow := time.Now()
    gameInfo := &cache.ChairGameInfo{
        Cid:             testCid,
        WeddingId:       testWeddingId,
        GameId:          1,
        CurRound:        1,
        ChairNum:        3,
        StartTime:       testNow.Add(-1 * time.Second).UnixMilli(),
        EndTime:         now.Add(100 * time.Second).UnixMilli(),
        CurRoundPlayers: []uint32{1, 2, 3, 4},
        GamePlayers:     gamePlayers,
        Reward:          reward,
        GameOverTime:    0, // 未结束
    }

    gameInfoNotStart := &cache.ChairGameInfo{
        Cid:             testCid,
        WeddingId:       testWeddingId,
        GameId:          1,
        CurRound:        1,
        ChairNum:        3,
        StartTime:       0, // 未开抢
        EndTime:         0,
        CurRoundPlayers: []uint32{1, 2, 3, 4},
        GamePlayers:     gamePlayers,
        Reward:          reward,
        GameOverTime:    0, // 未结束
    }

    type args struct {
        ctx      context.Context
        gameInfo *cache.ChairGameRound
    }
    tests := []struct {
        name     string
        initFunc func()
        args     args
        wantErr  bool
    }{
        {
            name: "获取锁失败",
            initFunc: func() {
                mockCache.EXPECT().LockUpdateChairGameInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil)
            },
            args: args{
                ctx:      ctx,
                gameInfo: gameRound,
            },
            wantErr: true,
        },
        {
            name: "游戏已开始-无需处理",
            initFunc: func() {
                mockCache.EXPECT().LockUpdateChairGameInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(true, nil)
                mockCache.EXPECT().UnlockUpdateChairGameInfo(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
                mockCache.EXPECT().GetChairGameInfo(gomock.Any(), gomock.Any()).Return(gameInfo, nil)
                mockCache.EXPECT().GetChairGameGrabQueue(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
                mockCache.EXPECT().GetRedisServerTime(gomock.Any()).Return(now, nil)
            },
            args: args{
                ctx:      ctx,
                gameInfo: gameRound,
            },
            wantErr: false,
        },
        {
            name: "定时器开抢成功",
            initFunc: func() {
                mockCache.EXPECT().LockUpdateChairGameInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(true, nil)
                mockCache.EXPECT().UnlockUpdateChairGameInfo(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
                mockCache.EXPECT().GetChairGameInfo(gomock.Any(), gomock.Any()).Return(gameInfoNotStart, nil)
                mockCache.EXPECT().GetChairGameGrabQueue(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
                mockCache.EXPECT().GetRedisServerTime(gomock.Any()).Return(testNow, nil)

                mockBc.EXPECT().GetChairGameGrabDuration().Return(int64(100)).AnyTimes()
                mockCache.EXPECT().SetChairGameInfo(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
                mockCache.EXPECT().AddChairGameTimerQueue(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

                mockBc.EXPECT().GetHostStartButtonDuration().Return(int64(10)).AnyTimes()
                mockBc.EXPECT().GetRewardInfoListByPackId(gomock.Any()).Return([]*conf.RewardInfo{
                    reward,
                }).AnyTimes()
                mockBc.EXPECT().GetChairGameBackSender().Return(&conf.BackSenderConf{
                    AppId: 1,
                    Sign:  "1",
                }).AnyTimes()
                mockCache.EXPECT().GetChairGameInfo(gomock.Any(), gomock.Any()).Return(gameInfoNotStart, nil)
                mockCache.EXPECT().GetChairGameGrabQueue(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
                mockCache.EXPECT().GetRedisServerTime(gomock.Any()).Return(testNow, nil)

                ayLayMock.EXPECT().ChairGameInfoChannelNotify(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
            },
            args: args{
                ctx:      ctx,
                gameInfo: gameRound,
            },
            wantErr: false,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := testMgr
            if tt.initFunc != nil {
                tt.initFunc()
            }
            if err := m.autoStartGrabHandle(tt.args.ctx, tt.args.gameInfo); (err != nil) != tt.wantErr {
                t.Errorf("autoStartGrabHandle() error = %v, wantErr %v", err, tt.wantErr)
            }
        })
    }
}

func TestMgr_autoStartNextRoundHandle(t *testing.T) {
    initTestMgr(t)
    type args struct {
        ctx      context.Context
        gameInfo *cache.ChairGameRound
    }
    tests := []struct {
        name     string
        initFunc func()
        args     args
        wantErr  bool
    }{
        // TODO: Add test cases.
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := testMgr
            if tt.initFunc != nil {
                tt.initFunc()
            }
            if err := m.autoStartNextRoundHandle(tt.args.ctx, tt.args.gameInfo); (err != nil) != tt.wantErr {
                t.Errorf("autoStartNextRoundHandle() error = %v, wantErr %v", err, tt.wantErr)
            }
        })
    }
}

func TestMgr_endGrabQueueHandle(t *testing.T) {
    initTestMgr(t)
    type args struct {
        ctx      context.Context
        gameInfo *cache.ChairGameRound
    }
    tests := []struct {
        name     string
        initFunc func()
        args     args
        wantErr  bool
    }{
        // TODO: Add test cases.
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := testMgr
            if tt.initFunc != nil {
                tt.initFunc()
            }
            if err := m.endGrabQueueHandle(tt.args.ctx, tt.args.gameInfo); (err != nil) != tt.wantErr {
                t.Errorf("endGrabQueueHandle() error = %v, wantErr %v", err, tt.wantErr)
            }
        })
    }
}

func TestMgr_pendingTimeoutGameCheck(t *testing.T) {
    initTestMgr(t)
    type args struct {
        ctx  context.Context
        game *store.GameLog
    }
    tests := []struct {
        name     string
        initFunc func()
        args     args
        wantErr  bool
    }{
        // TODO: Add test cases.
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := testMgr
            if err := m.pendingTimeoutGameCheck(tt.args.ctx, tt.args.game); (err != nil) != tt.wantErr {
                t.Errorf("pendingTimeoutGameCheck() error = %v, wantErr %v", err, tt.wantErr)
            }
        })
    }
}

func TestMgr_settlementQueueHandle(t *testing.T) {
    initTestMgr(t)
    tests := []struct {
        name     string
        initFunc func()
    }{
        {
            name: "no settleInfo to handle",
            initFunc: func() {
                mockCache.EXPECT().LockTimer(gomock.Any(), gomock.Any(), gomock.Any()).Return(true, nil)
                mockCache.EXPECT().UnlockTimer(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
                mockCache.EXPECT().GetChairGameSettleQueueByScore(gomock.Any(), gomock.Any()).Return(nil, nil)
            },
        },

        {
            name: "game fail",
            initFunc: func() {
                mockCache.EXPECT().LockTimer(gomock.Any(), gomock.Any(), gomock.Any()).Return(true, nil)
                mockCache.EXPECT().UnlockTimer(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
                mockCache.EXPECT().GetChairGameSettleQueueByScore(gomock.Any(), gomock.Any()).Return([]string{
                    "{\"cid\":2,\"gameId\":1,\"payOrderId\":\"456\",\"awardOrderId\":\"\",\"gameResult\":2,\"weddingId\":0}",
                }, nil)
                chairGameAwardMock.EXPECT().ConsumeRollBack(gomock.Any(), gomock.Any()).Return(nil)
                mockCache.EXPECT().DelChairGameSettleQueue(gomock.Any(), gomock.Any()).Return(nil)
            },
        },

        {
            name: "game success",
            initFunc: func() {
                mockCache.EXPECT().LockTimer(gomock.Any(), gomock.Any(), gomock.Any()).Return(true, nil)
                mockCache.EXPECT().UnlockTimer(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
                mockCache.EXPECT().GetChairGameSettleQueueByScore(gomock.Any(), gomock.Any()).Return([]string{
                    "{\"cid\":2,\"gameId\":1,\"payOrderId\":\"456\",\"awardOrderId\":\"123\",\"gameResult\":1,\"weddingId\":0}",
                }, nil)
                chairGameAwardMock.EXPECT().ConsumeCommit(gomock.Any(), gomock.Any()).Return(testUid, "dealToken", nil)
                mockStore.EXPECT().GetAwardRecordByOrderId(gomock.Any(), gomock.Any()).Return(&store.AwardRecord{
                    OrderID:  "123",
                    GameId:   1,
                    Uid:      1,
                    GiftID:   "1",
                    GiftType: 1, // 这里简单测发包裹礼物
                    Amount:   1,
                }, nil)
                mockStore.EXPECT().GetGameLogByGameId(gomock.Any(), gomock.Any()).Return(&store.GameLog{
                    ID: 1,
                }, nil)
                ayLayMock.EXPECT().AwardPackage(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
                mockStore.EXPECT().UpdateAwardStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
                mockCache.EXPECT().DelChairGameSettleQueue(gomock.Any(), gomock.Any()).Return(nil)
            },
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := testMgr
            if tt.initFunc != nil {
                tt.initFunc()
            }
            m.settlementQueueHandle()
        })
    }
}
func TestMgr_timeoutGameHandle(t *testing.T) {
    initTestMgr(t)
    tests := []struct {
        name     string
        initFunc func()
    }{
        // TODO: Add test cases.
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := testMgr
            if tt.initFunc != nil {
                tt.initFunc()
            }
            m.timeoutGameHandle()
        })
    }
}

func TestMgr_sendGift(t *testing.T) {
    initTestMgr(t)
    type args struct {
        ctx       context.Context
        v         *store.AwardRecord
        payUid    uint32
        dealToken string
    }
    tests := []struct {
        name     string
        initFunc func()
        args     args
        wantErr  bool
    }{
        {
            name: "发送礼物架礼物-need 2 get deal_token",
            initFunc: func() {
                mockStore.EXPECT().GetGameLogByGameId(gomock.Any(), gomock.Any()).Return(&store.GameLog{
                    ID: 1,
                }, nil)
                chairGameAwardMock.EXPECT().ConsumeCommit(gomock.Any(), gomock.Any()).Return(testUid+1, "dealToken", nil)
                ayLayMock.EXPECT().SendCommonShelfPresent(gomock.Any(), gomock.Any()).Return(nil)
                mockStore.EXPECT().UpdateAwardStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
            },
            args: args{
                ctx: ctx,
                v: &store.AwardRecord{
                    OrderID:   "11111",
                    GameId:    1,
                    Uid:       testUid,
                    GiftID:    "1",
                    GiftType:  conf.GiftTypeNormal,
                    GiftWorth: 10,
                    Amount:    1,
                },
                payUid:    0,
                dealToken: "",
            },
            wantErr: false,
        },
        {
            name: "发送幸运礼物",
            initFunc: func() {
                mockStore.EXPECT().GetGameLogByGameId(gomock.Any(), gomock.Any()).Return(&store.GameLog{
                    ID: 1,
                }, nil)
                chairGameAwardMock.EXPECT().ConsumeCommit(gomock.Any(), gomock.Any()).Return(testUid+1, "dealToken", nil)
                ayLayMock.EXPECT().SendMagicSpiritGiftToUser(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
                mockStore.EXPECT().UpdateAwardStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
            },
            args: args{
                ctx: ctx,
                v: &store.AwardRecord{
                    OrderID:   "11111",
                    GameId:    1,
                    Uid:       testUid,
                    GiftID:    "1",
                    GiftType:  conf.GiftTypeMagicSpirit,
                    GiftWorth: 10,
                    Amount:    1,
                },
                payUid:    0,
                dealToken: "",
            },
            wantErr: false,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := testMgr
            if tt.initFunc != nil {
                tt.initFunc()
            }
            if err := m.sendGift(tt.args.ctx, tt.args.v, tt.args.payUid, tt.args.dealToken); (err != nil) != tt.wantErr {
                t.Errorf("sendGift() error = %v, wantErr %v", err, tt.wantErr)
            }
        })
    }
}
