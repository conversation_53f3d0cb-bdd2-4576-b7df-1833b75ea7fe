package conf

//go:generate quicksilver-cli test interface ../conf
//go:generate mockgen -destination=./mocks/business_config.go -package=mocks golang.52tt.com/services/channel-ext-game/channel-ext-game-http-logic/internal/conf IBusinessConfManager

import (
    "crypto/md5"
    "encoding/json"
    "fmt"
    "io/ioutil"
    "os"
    "time"

    "gitlab.ttyuyin.com/tt-infra/tyr/log"
)

const (
    BusinessConfPath = "/data/oss/conf-center/tt/"
    BusinessConfFile = "channel-ext-game.json"
)

type AwardInfo struct {
    ID         uint32 `json:"id"`
    RewardId   string `json:"reward_id"`
    RewardType uint32 `json:"reward_type"`
    Worth      uint32 `json:"worth"` // T豆
}

type GameAppConf struct {
    AppID     string `json:"app_id"`
    AppSecret string `json:"app_secret"`

    // 奖励列表
    AwardList []*AwardInfo `json:"award_list"` // 奖品列表
}

var LastConfMd5Sum [md5.Size]byte

type BusinessConf struct {
    GameAppConfMap      map[string]*GameAppConf `json:"game_app_conf_map"`
    SessionKeepDuration uint32                  `json:"session_keep_duration"`
    RoomIconUrlFormat   string                  `json:"room_icon_url_format"` // 房间图标url格式串
    DefaultRoomIcon     string                  `json:"default_room_icon"`    // 默认房间图标
    IsTestMode          bool                    `json:"is_test_mode"`         // 是否测试模式
    JscodeWhiteList     []string                `json:"jscode_white_list"`    // js_code白名单
}

func (c *BusinessConf) Parse(configFile string) (isChange bool, err error) {
    defer func() {
        if e := recover(); e != nil {
            err = fmt.Errorf("Failed to parse config: %v \n", e)
        }
    }()

    data, err := ioutil.ReadFile(configFile)
    if err != nil {
        return false, err
    }

    md5Sum := md5.Sum(data)
    if md5Sum == LastConfMd5Sum {
        isChange = false
        return
    }

    err = json.Unmarshal(data, &c)
    if err != nil {
        return false, err
    }

    err = c.CheckConf()
    if err != nil {
        return false, err
    }

    LastConfMd5Sum = md5Sum

    log.Infof("BusinessConf : %+v", c)
    return true, nil
}

type BusinessConfManager struct {
    Done chan interface{}
    //mutex sync.RWMutex
    conf *BusinessConf
}

func NewBusinessConfManager() (*BusinessConfManager, error) {
    businessConf := &BusinessConf{}

    businessConfFilePath := BusinessConfPath + BusinessConfFile
    if devBusinessConfPath := os.Getenv("DEV_BUSINESS_CONF_PATH"); devBusinessConfPath != "" {
        businessConfFilePath = devBusinessConfPath + BusinessConfFile
    }
    _, err := businessConf.Parse(businessConfFilePath)
    if err != nil {
        log.Errorf("NewBusinessConfManager fail to Parse BusinessConf err:%v", err)
        return nil, err
    }

    confMgr := &BusinessConfManager{
        conf: businessConf,
        Done: make(chan interface{}),
    }

    go confMgr.Watch(businessConfFilePath)

    return confMgr, nil
}

func (bm *BusinessConfManager) Reload(file string) error {
    businessConf := &BusinessConf{}

    isChange, err := businessConf.Parse(file)
    if err != nil {
        log.Errorf("NewBusinessConfManager fail to Parse BusinessConf err:%v", err)
        return err
    }

    if isChange {
        //bm.mutex.Lock()
        bm.conf = businessConf
        //bm.mutex.Unlock()

        log.Infof("Reload %+v", businessConf)
    }

    return nil
}

func (bm *BusinessConfManager) Watch(file string) {
    log.Infof("Watch start. file:%s", file)

    for {
        select {
        case _, ok := <-bm.Done:
            if !ok {
                log.Infof("Watch done")
                return
            }

        case <-time.After(30 * time.Second):
            log.Debugf("Watch check change")

            err := bm.Reload(file)
            if err != nil {
                log.Errorf("Watch Reload fail. file:%s, err:%v", file, err)
            }
        }
    }
}

func (bm *BusinessConfManager) Close() {
    close(bm.Done)
}

func (c *BusinessConf) CheckConf() error {
    return nil
}

func (c *GameAppConf) GetAwardInfo(Id uint32, RewardType uint32) (*AwardInfo, bool) {
    for _, award := range c.AwardList {
        if award.ID == Id && award.RewardType == RewardType {
            return award, true
        }
    }
    return nil, false
}

// GetGameAppConfByAppid return conf, exist
func (bm *BusinessConfManager) GetGameAppConfByAppid(appid string) (*GameAppConf, bool) {
    if bm.conf == nil || bm.conf.GameAppConfMap == nil {
        return nil, false
    }
    if appConf, ok := bm.conf.GameAppConfMap[appid]; ok {
        return appConf, true
    }
    return nil, false
}

func (bm *BusinessConfManager) GetSessionKeepDuration() uint32 {
    if bm.conf == nil || bm.conf.SessionKeepDuration == 0 {
        // 默认3600秒
        return 3600
    }
    return bm.conf.SessionKeepDuration
}

func (bm *BusinessConfManager) GetRoomIconUrlFormat() string {
    if bm.conf == nil || bm.conf.RoomIconUrlFormat == "" {
        // 默认房间图标url格式串
        return ""
    }
    return bm.conf.RoomIconUrlFormat
}

func (bm *BusinessConfManager) GetDefaultRoomIcon() string {
    if bm.conf == nil || bm.conf.DefaultRoomIcon == "" {
        // 默认房间图标
        return ""
    }
    return bm.conf.DefaultRoomIcon
}

func (bm *BusinessConfManager) IsTestMode() bool {
    if bm.conf == nil {
        return false
    }
    return bm.conf.IsTestMode
}

func (bm *BusinessConfManager) GetChannelIconUrlFormat() string {
    if bm.conf == nil || bm.conf.RoomIconUrlFormat == "" {
        // 默认头像url格式串
        return ""
    }
    return bm.conf.RoomIconUrlFormat
}

// CheckJscodeWhiteList 检查jscode 是否是白名单
func (bm *BusinessConfManager) CheckJscodeWhiteList(jscode string) bool {
    if bm.conf == nil || len(bm.conf.JscodeWhiteList) == 0 {
        return false
    }
    for _, jscodeWhite := range bm.conf.JscodeWhiteList {
        if jscodeWhite == jscode {
            return true
        }
    }
    return false
}
