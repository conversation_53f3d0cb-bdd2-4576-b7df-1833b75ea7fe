package internal

import (
    "context"
    "crypto/md5" //#nosec
    "encoding/base64"
    "errors"
    "github.com/dgrijalva/jwt-go"
    "gitlab.ttyuyin.com/avengers/tyr/core/service/http"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    "golang.52tt.com/pkg/protocol"
    "golang.52tt.com/protocol/common/status"
    channelExtGame "golang.52tt.com/protocol/services/channel-ext-game"
    channelExtGameHttp "golang.52tt.com/services/channel-ext-game/channel-ext-game-http-logic/internal/model"
    "google.golang.org/grpc/codes"
    "sort"
    "strings"
    "time"
)

// CommonReq 通用请求
type CommonReq struct {
    Appid   string `json:"appid"`
    RoomId  string `json:"room_id"`
    OpenId  string `json:"open_id"`
    Session string `json:"session"`
}

// AuthInterceptor 鉴权接口
func (s *Server) AuthInterceptor() http.Interceptor {
    return func(ctx context.Context, w http.ResponseWriter, r *http.Request, next http.HandleFunc) {
        if s.bc.IsTestMode() {
            next(r.Context(), w, r)
            return
        }
        var req = &CommonReq{}
        err := http.RepeatableReadJSON(r, req)
        if err != nil {
            log.ErrorWithCtx(ctx, "AuthInterceptor failed to ReadJSON. body [%s], err %v", r.RequestURI, err)
            ServeAPIJsonWithError(w, ErrBadRequestParams, "bad request")
            return
        }

        // 1.检查appid是否存在
        _, ok := s.bc.GetGameAppConfByAppid(req.Appid)
        if !ok {
            log.ErrorWithCtx(ctx, "AuthInterceptor failed to GetGameAppConfByAppid. req:%+v", req)
            ServeAPIJsonWithError(w, ErrBadAppid, "bad appid")
            return
        }

        // 截取掉前边的 /ext-game
        path := r.URL.Path[len(BasePath):]
        // 不需要额外检查的接口
        if check, ok := s.passCheckPathMap[path]; check && ok {
            log.DebugWithCtx(ctx, "pass check, req:%+v", req)
            next(r.Context(), w, r)
            return
        }

        // 2.校验session
        claims, err := s.checkSession(ctx, req.Session)
        if err != nil {
            log.ErrorWithCtx(ctx, "AuthInterceptor failed to checkSession. req:%+v, err:%v", req, err)
            e := protocol.ToServerError(err)
            ServeAPIJsonWithError(w, e.Code(), e.Message())
            return
        }

        // 3.检查信息是否匹配
        if claims.OpenId != req.OpenId || claims.Appid != req.Appid {
            log.ErrorWithCtx(ctx, "checkReq failed, info not match, claims:%+v, req:%+v", claims, req)
            ServeAPIJsonWithError(w, ErrInvalidSession, "session not match")
            return
        }

        // pass
        log.DebugWithCtx(ctx, "check success, claims:%+v, req:%+v", claims, req)
        next(r.Context(), w, r)
    }
}

func (s *Server) checkSession(ctx context.Context, session string) (*Claims, error) {
    validateSession, valid, err := parseSession(session)
    if err != nil {
        log.ErrorWithCtx(ctx, "checkSession failed to parseSession. %s, err:%v", session, err)
        return nil, err
    }

    if validateSession == nil || !valid {
        log.ErrorWithCtx(ctx, "checkSession failed to parseSession. %s", session)
        return nil, protocol.NewExactServerError(codes.OK, ErrInvalidSession, "invalid session")
    }

    return validateSession, nil
}

func (s *Server) GetSession(ctx context.Context, w http.ResponseWriter, r *http.Request) {
    type GetAccessSessionReq struct {
        Appid     string `json:"appid"`
        OpenId    string `json:"open_id"`
        JsCode    string `json:"js_code"`
        RoomId    string `json:"room_id"`
        AppSecret string `json:"app_secret"`
    }
    req := &GetAccessSessionReq{}
    out := &channelExtGameHttp.GetAccessTokenResp{}
    defer func() {
        log.InfoWithCtx(ctx, "GetSession req:%+v, out:%+v", req, out)
    }()
    err := http.RepeatableReadJSON(r, req)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetSession failed to ReadJSON. %s, err %v", r.RequestURI, err)
        ServeAPIJsonWithError(w, ErrBadRequestParams, BadRequestDesc)
        return
    }

    cfg, hasCfg := s.bc.GetGameAppConfByAppid(req.Appid)
    if !hasCfg {
        log.ErrorWithCtx(ctx, "GetAccessToken failed to GetGameAppConfByAppid. req:%+v, err:%v", req)
        ServeAPIJsonWithError(w, ErrBadAppid, BadAppidDesc)
        return
    }

    if req.AppSecret != cfg.AppSecret {
        log.ErrorWithCtx(ctx, "GetAccessToken failed. bad secret, cfg.AppSecret:%s, req:%+v, err:%v", cfg.AppSecret, req)
        ServeAPIJsonWithError(w, ErrBadAppSecret, BadSecretDesc)
        return
    }

    // 校验jsCode
    authInfoResp, err := s.ChannelExtGameCli.GetAuthInfoByJsCode(ctx, &channelExtGame.GetAuthInfoByJsCodeReq{
        JsCode: req.JsCode,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetSession failed to GetAuthInfoByJsCode. %s, err %v", r.RequestURI, err)
        e := protocol.ToServerError(err)
        if e.Code() == status.ErrRequestParamInvalid {
            ServeAPIJsonWithError(w, ErrBadRequestParams, "JsCode无效")
            return
        }
        ServeAPIJsonWithError(w, e.Code(), e.Message())
        return
    }

    if authInfoResp.GetOpenid() != req.OpenId || authInfoResp.GetAppid() != req.Appid {
        log.ErrorWithCtx(ctx, "GetSession JsCode not match, authInfoResp:%+v, req:%+v", authInfoResp, req)
        ServeAPIJsonWithError(w, ErrBadRequestParams, "jsCode not match")
        return
    }

    // 记录channelDisplayId 在session中，便于后续使用
    session, err := s.GrantToken(req.Appid, req.OpenId, authInfoResp.GetChannelViewId(), s.bc.GetSessionKeepDuration(),
        authInfoResp.GetMarketId(), authInfoResp.GetClientType())
    if err != nil {
        log.ErrorWithCtx(ctx, "GetSession failed to GrantToken. %s, err %v", r.RequestURI, err)
        ServeAPIJsonWithError(w, ErrSystemInternal, InternalErrDesc)
        return
    }
    out.Session = session
    out.SessionExpired = s.bc.GetSessionKeepDuration()

    if !s.bc.CheckJscodeWhiteList(req.JsCode) {
        // 生成session成功后注销jsCode
        _, err = s.ChannelExtGameCli.CancelUserExtGameJsCode(ctx, &channelExtGame.CancelUserExtGameJsCodeReq{
            JsCode: req.JsCode,
        })
        if err != nil {
            log.WarnWithCtx(ctx, "GetSession failed to CancelUserExtGameJsCode. req:%+v, err:%v", req, err)
        }
    }

    ServePB2JSON(w, out)
    return
}

func (s *Server) ReNewSession(ctx context.Context, w http.ResponseWriter, r *http.Request) {
    req := &channelExtGameHttp.SessionRenewReq{}
    out := &channelExtGameHttp.SessionRenewResp{}
    defer func() {
        log.InfoWithCtx(ctx, "SessionRenew req:%+v, out:%+v", req, out)
    }()
    err := http.RepeatableReadJSON(r, req)
    if err != nil {
        log.ErrorWithCtx(ctx, "SessionRenew failed to ReadJSON. %s, err %v", r.RequestURI, err)
        ServeAPIJsonWithError(w, ErrBadRequestParams, BadRequestDesc)
        return
    }

    cfg, ok := s.bc.GetGameAppConfByAppid(req.GetAppid())
    if !ok {
        log.ErrorWithCtx(ctx, "SessionRenew failed to GetGameAppConfByAppid. req:%+v", req)
        ServeAPIJsonWithError(w, ErrBadAppid, "bad appid")
        return
    }

    // 检查secret
    if req.AppSecret != cfg.AppSecret {
        log.ErrorWithCtx(ctx, "AuthInterceptor failed, bad secret, cfg:%+v, req:%+v", cfg, req)
        ServeAPIJsonWithError(w, ErrBadAppSecret, "bad secret")
    }

    // interceptor通过了，session一定是有效的
    claims, _, _ := parseSession(req.GetSession())
    session, err := s.GrantToken(claims.Appid, claims.OpenId, claims.RoomId, s.bc.GetSessionKeepDuration(), claims.MarketId, claims.ClientType)
    if err != nil {
        log.ErrorWithCtx(ctx, "SessionRenew failed to GrantToken. %s, err %v", r.RequestURI, err)
        ServeAPIJsonWithError(w, ErrSystemInternal, InternalErrDesc)
        return
    }
    out.Session = session
    out.SessionExpired = s.bc.GetSessionKeepDuration()

    ServePB2JSON(w, out)
    return
}

type Claims struct {
    Type       uint32 `json:"typ"`
    Appid      string `json:"appid"`
    RoomId     string `json:"room_id"`
    OpenId     string `json:"open_id"`
    ClientType uint32 `json:"client_type"`
    MarketId   uint32 `json:"market_id"`
    jwt.StandardClaims
}

type Token = jwt.Token

var hmacSignKey = []byte("GeLAvKkjQiozR9eKBt3H5VBcmh7Ete5E")

func Decode(tokenStr string) (*Token, *Claims, error) {
    claims := new(Claims)
    token, err := jwt.ParseWithClaims(tokenStr, claims, func(token *jwt.Token) (interface{}, error) {
        switch token.Method.Alg() {
        case "HS256", "HS384", "HS512":
            return hmacSignKey, nil
        default:
            return nil, errors.New("unsupported algorithm")
        }
    })

    return token, claims, err
}

func parseSession(sessionStr string) (*Claims, bool, error) {
    if sessionStr == "" {
        log.Errorf("parseSession failed to get access-session [%s]", sessionStr)
        return nil, false, protocol.NewExactServerError(codes.OK, ErrInvalidSession, "invalid session")
    }
    token, webToken, err := Decode(sessionStr)
    if err != nil {
        log.Errorf("parseSession Decode err %+v, str %s", err, sessionStr)
        if strings.Contains(err.Error(), "token is expired") {
            return webToken, false, protocol.NewExactServerError(codes.OK, ErrInvalidSession, "session expired")
        } else {
            return webToken, false, protocol.NewExactServerError(codes.OK, ErrInvalidSession, "session invalid")
        }
    }

    if token == nil {
        log.WarnWithCtx(context.Background(), "parseSession token is nil, str %s", sessionStr)
        return webToken, false, nil
    }

    return webToken, token.Valid, nil
}

// SessionCheck session检查接口,供游戏方检查在线状态，过了auth拦截器后，session一定是有效的
func (s *Server) SessionCheck(ctx context.Context, w http.ResponseWriter, r *http.Request) {
    type SessionCheckReq struct {
        Session string `json:"session"`
        Appid   string `json:"appid"`
        OpenId  string `json:"open_id"`
        RoomId  string `json:"room_id"`
    }
    req := &SessionCheckReq{}
    err := http.RepeatableReadJSON(r, req)
    if err != nil {
        log.ErrorWithCtx(ctx, "SessionCheck fail to RepeatableReadJSON. err:%v", err)
        ServeAPIJsonWithError(w, ErrBadRequestParams, BadRequestDesc)
        return
    }

    resp := &channelExtGameHttp.SessionCheckResp{}
    defer func() {
        log.DebugWithCtx(ctx, "SessionCheck req:%+v, resp:%+v", req, resp)
    }()
    resp.CheckResult = true
    ServePB2JSON(w, resp)
}

func (s *Server) GrantToken(appId, openId string, roomId string, ttl, marketId, clientType uint32) (string, error) {
    issuedAt := time.Now()
    expiresAt := issuedAt.Add(time.Duration(ttl) * time.Second)

    claims := jwt.MapClaims{
        "type":        "JWT",
        "typ":         1,
        "appid":       appId,
        "iat":         issuedAt.Unix(),
        "exp":         expiresAt.Unix(),
        "iss":         WebTokenJwtIssuer,
        "room_id":     roomId,
        "open_id":     openId,
        "market_id":   marketId,
        "client_type": clientType,
    }
    token := jwt.NewWithClaims(jwt.SigningMethodHS512, claims)
    signedToken, err := token.SignedString(hmacSignKey)
    if err != nil {
        log.Errorf("GrantToken appId %s SignedString err %+v", appId, err)
        return "", err
    }
    return signedToken, nil
}

/*
*
比如：

	header := map[string]string{
	             "x-nonce-str": "123456",
	             "x-timestamp": "456789",
	             "x-roomid":    "268",
	             "x-msg-type":  "live_gift",
	     }
	bodyStr := "abc123你好"
	secret := "123abc"

rawData为：x-msg-type=live_gift&x-nonce-str=123456&x-serial=268&x-timestamp=456789abc123你好123abc
signature为：PDcKhdlsrKEJif6uMKD2dw==
*/
func signature(header map[string]string, bodyStr, secret string) string {
    keyList := make([]string, 0, 4)
    for key := range header {
        keyList = append(keyList, key)
    }
    sort.Slice(keyList, func(i, j int) bool {
        return keyList[i] < keyList[j]
    })
    kvList := make([]string, 0, 4)
    for _, key := range keyList {
        kvList = append(kvList, key+"="+header[key])
    }
    urlParams := strings.Join(kvList, "&")
    rawData := urlParams + bodyStr + secret
    md5Result := md5.Sum([]byte(rawData)) // #nosec
    return base64.StdEncoding.EncodeToString(md5Result[:])
}

func (s *Server) ValidateSignature(ctx context.Context, r *http.Request, appId string) bool {
    body, err := http.ReadBodyBytes(r)
    if err != nil {
        return false
    }

    cfg, exist := s.bc.GetGameAppConfByAppid(appId)
    if !exist {
        log.ErrorWithCtx(ctx, "ValidateSignatureV2 failed to GetGameAppConfByAppid. appId:%+v, err:%v", appId)
        return false
    }

    headerMap := make(map[string]string)

    headerMap["x-nonce-str"] = r.Header.Get("x-nonce-str")
    headerMap["x-timestamp"] = r.Header.Get("x-timestamp")

    log.DebugWithCtx(ctx, "ValidateSignatureV2 headerMap:%+v, body:%s appsecret:%s", headerMap, string(body), cfg.AppSecret)
    genSign := signature(headerMap, string(body), cfg.AppSecret)
    log.DebugWithCtx(ctx, "ValidateSignature genSign:%s, r.Header.Get(x-signature):%s", genSign, r.Header.Get("x-signature"))
    return genSign == r.Header.Get("x-signature")
}
