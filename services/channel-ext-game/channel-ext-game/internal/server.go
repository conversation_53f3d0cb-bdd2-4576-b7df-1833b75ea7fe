package internal

import (
    "context"
    "gitlab.ttyuyin.com/avengers/tyr/core/log"
    mysqlConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/mysql/connect"
    redisConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/redis/connect"
    "golang.52tt.com/pkg/protocol"
    channelApp "golang.52tt.com/protocol/app/channel"
    "golang.52tt.com/protocol/common/status"
    pb "golang.52tt.com/protocol/services/channel-ext-game"
    "golang.52tt.com/protocol/services/demo/echo"
    reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"
    UnifiedPayCallback "golang.52tt.com/protocol/services/unified_pay/cb"
    "golang.52tt.com/services/channel-ext-game/channel-ext-game/internal/conf"
    anti_corruption_layer "golang.52tt.com/services/channel-ext-game/channel-ext-game/internal/model/anti-corruption-layer"
    auth_manager "golang.52tt.com/services/channel-ext-game/channel-ext-game/internal/model/auth-manager"
    common_mgr "golang.52tt.com/services/channel-ext-game/channel-ext-game/internal/model/common-mgr"
    consume_and_reward "golang.52tt.com/services/channel-ext-game/channel-ext-game/internal/model/consume-and-reward"
    context0 "golang.org/x/net/context"
    "google.golang.org/grpc/codes"
    "time"
    "golang.52tt.com/services/channel-ext-game/channel-ext-game/internal/kfk-producer"
)

var (
    ReconcileMaxDuration = 24 * time.Hour // 对账最大可查询范围时长
    ErrTimeRangeOver     = protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "time range over")
)

type Server struct {
    bc                  conf.IBusinessConfManager
    consumeAndRewardMgr consume_and_reward.IMgr
    acLayer             anti_corruption_layer.IMgr
    authManager         auth_manager.ISessionManager
    commonMgr           common_mgr.IMgr

    kfkPub kfk_producer.IKafkaProducer
}

func (s *Server) SimpleQueryOpenid(c context0.Context, req *pb.SimpleQueryOpenidReq) (*pb.SimpleQueryOpenidResp, error) {
    out := &pb.SimpleQueryOpenidResp{}
    defer func() {
        log.DebugWithCtx(c, "SimpleQueryOpenid req:%+v, resp: %+v", req, out)
    }()
    openId, err := s.authManager.SimpleQueryOpenid(c, req.GetUid(), req.GetTtid())
    if err != nil {
        log.ErrorWithCtx(c, "SimpleQueryOpenid fail to SimpleQueryOpenid, err:%v", err)
        return nil, err
    }
    out.Openid = openId
    return out, nil
}

func (s *Server) BatchGetUidByOpenIds(c context0.Context, req *pb.BatchGetUidByOpenIdsReq) (*pb.BatchGetUidByOpenIdsResp, error) {
    out := &pb.BatchGetUidByOpenIdsResp{}
    defer func() {
        log.DebugWithCtx(c, "BatchGetUidByOpenIds req:%+v, resp: %+v", req, out)
    }()
    uidMap, err := s.authManager.BatchGetUidByOpenIds(c, req.GetOpenIdList())
    if err != nil {
        log.ErrorWithCtx(c, "BatchGetUidByOpenIds fail to BatchGetUidByOpenIds, err:%v", err)
        return out, err
    }
    out.UidMap = uidMap
    return out, nil
}

func (s *Server) GetExtGameInfoList(c context.Context, req *pb.GetExtGameInfoListReq) (*pb.GetExtGameInfoListResp, error) {
    out := &pb.GetExtGameInfoListResp{}
    defer func() {
        log.DebugWithCtx(c, "GetExtGameInfoList req:%+v, resp: %+v", req, out)
    }()

    confMap := s.bc.GetGameAppConfMap()
    for _, cfg := range confMap {
        info, err := s.commonMgr.GetDownloadInfo(c, cfg.AppID)
        if err != nil {
            log.ErrorWithCtx(c, "GetExtGameInfoList fail to GetDownloadInfo, err:%v", err)
            return out, err
        }
        gameInfo := &pb.ExtGameInfo{
            GameId:  info.Id,
            Name:    info.Name,
            Content: info.Content,
            Version: info.Version,
            Build:   info.Build,
            Zip:     info.Zip,
            H5Url:   info.H5Url,
            FullUrl: info.FullUrl,
            Md5:     info.Md5,
            Size:    info.Size,
            AppId:   cfg.AppID,
        }

        if cfg.IsUseLinkLoad {
            gameInfo = &pb.ExtGameInfo{
                Name:  info.Name,
                H5Url: cfg.H5URL,
                AppId: cfg.AppID,
            }
        }

        out.GameInfo = append(out.GameInfo, gameInfo)
    }

    return out, nil
}

func (s *Server) GetAuthInfoByJsCode(c context.Context, req *pb.GetAuthInfoByJsCodeReq) (*pb.GetAuthInfoByJsCodeResp, error) {
    out := &pb.GetAuthInfoByJsCodeResp{}
    defer func() {
        log.DebugWithCtx(c, "GetAuthInfoByJsCode req:%+v, resp: %+v", req, out)
    }()
    authInfo, err := s.authManager.GetAuthInfoByJsCode(c, req.GetJsCode())
    if err != nil {
        log.ErrorWithCtx(c, "GetAuthInfoByJsCode fail to GetAuthInfoByJsCode, err:%v", err)
        return out, err
    }
    out.Openid = authInfo.OpenId
    out.ChannelViewId = authInfo.ChannelViewId
    out.Appid = authInfo.AppId
    out.MarketId = authInfo.MarketId
    out.ClientType = authInfo.ClientType
    return out, nil
}

func NewServer(ctx context.Context, cfg *conf.StartConfig) (*Server, error) {
    log.Infof("server startup with cfg: %+v", *cfg)

    redisClient, err := redisConnect.NewClient(ctx, cfg.RedisConfig)
    if err != nil {
        log.ErrorWithCtx(ctx, "NewServer fail to redisConnect.NewClient, %+v, err:%v", cfg.RedisConfig, err)
        return nil, err
    }

    mysqlDBCli, err := mysqlConnect.NewClient(ctx, cfg.MysqlConfig)
    if err != nil {
        log.ErrorWithCtx(ctx, "NewServer fail to mysqlConnect.NewClient, %+v, err:%v", cfg.MysqlConfig, err)
        return nil, err
    }
    readOnlyDBCli, err := mysqlConnect.NewClient(ctx, cfg.MysqlReadOnlyConfig)
    if err != nil {
        log.ErrorWithCtx(ctx, "NewServer fail to mysqlConnect.NewClient, %+v, err:%v", cfg.MysqlConfig, err)
        return nil, err
    }

    bc, err := conf.NewBusinessConfManager()
    if err != nil {
        return nil, err
    }

    acl, err := anti_corruption_layer.NewMgr(bc)
    if err != nil {
        log.ErrorWithCtx(ctx, "NewServer fail to anti_corruption_layer.NewMgr, err:%v", err)
        return nil, err
    }

    accessMgr, err := common_mgr.NewMgr(mysqlDBCli, redisClient, bc, acl)
    if err != nil {
        log.ErrorWithCtx(ctx, "NewServer fail to access_mgr.NewMgr, err:%v", err)
        return nil, err
    }

    consumeAndRewardMgr, err := consume_and_reward.NewMgr(mysqlDBCli, redisClient, bc, acl)
    if err != nil {
        log.ErrorWithCtx(ctx, "NewServer fail to user_virtual_avatar.NewMgr, err:%v", err)
        return nil, err
    }

    authManager, err := auth_manager.NewSessionManager(redisClient, acl, mysqlDBCli, readOnlyDBCli)
    if err != nil {
        log.ErrorWithCtx(ctx, "NewServer fail to auth_manager.NewSessionManager, err:%v", err)
        return nil, err
    }

    kfkPub, err := kfk_producer.NewKafkaProducer(cfg)
    if err != nil {
        return nil, err
    }

    s := &Server{
        bc:                  bc,
        consumeAndRewardMgr: consumeAndRewardMgr,
        acLayer:             acl,
        authManager:         authManager,
        commonMgr:           accessMgr,
        kfkPub:              kfkPub,
    }

    return s, nil
}

func (s *Server) ShutDown() {
    s.consumeAndRewardMgr.Stop()
    s.acLayer.Stop()
}

func (s *Server) Echo(ctx context.Context, req *echo.StringMessage) (*echo.StringMessage, error) {
    return req, nil
}

func (s *Server) Notify(ctx context.Context, in *UnifiedPayCallback.PayNotify) (*UnifiedPayCallback.PayNotifyResponse, error) {
    out := &UnifiedPayCallback.PayNotifyResponse{}
    op, err := s.consumeAndRewardMgr.Callback(ctx, in.GetUid(), in.GetOutTradeNo())
    if err != nil {
        log.Errorf("Notify fail to Callback. in:%+v,err:%v", in, err)
        return out, err
    }

    out.Op = op
    out.Confirmed = true

    return out, nil
}

func (s *Server) GetUserExtGameJsCode(ctx context.Context, req *pb.GetUserExtGameJsCodeReq) (*pb.GetUserExtGameJsCodeResp, error) {
    out := &pb.GetUserExtGameJsCodeResp{}
    defer func() {
        log.InfoWithCtx(ctx, "GetUserExtGameJsCode req:%+v, resp: %+v", req, out)
    }()

    gameConf, _ := s.bc.GetGameAppConfByAppid(req.GetAppId())

    if gameConf == nil || !gameConf.Switch {
        log.ErrorWithCtx(ctx, "GetUserExtGameJsCode gameConf.switch is false")
        return out, protocol.NewExactServerError(codes.OK, status.ErrChannelExtGameGetJscodeFail, "游戏维护中")
    }

    jsCode, openId, err := s.authManager.GenJsCode(ctx, req)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUserExtGameJsCode fail to GenJsCode, err:%v", err)
        return out, err
    }
    out.JsCode = jsCode
    out.Openid = openId
    return out, nil
}

func (s *Server) ExtGameConsume(ctx context.Context, req *pb.ExtGameConsumeReq) (*pb.ExtGameConsumeResp, error) {
    out := &pb.ExtGameConsumeResp{}
    var err error
    uid := req.GetUid()
    if uid == 0 {
        // 通过openid获取uid
        uid, err = s.authManager.GetUidByOpenId(ctx, req.GetOpenid())
        if err != nil {
            log.ErrorWithCtx(ctx, "ExtGameConsume fail to GetUidByOpenId, err:%v", err)
            return out, err
        }
    }

    payOrderId, err := s.consumeAndRewardMgr.Consume(ctx, uid, req)
    if err != nil {
        log.ErrorWithCtx(ctx, "ExtGameConsume fail to Consume, err:%v", err)
        return out, err
    }

    // kfk pub
    s.kfkPub.ProduceTBeanConsumeEvent(uid, req.GetAmount(), uint32(time.Now().Unix()), payOrderId)
    return out, nil
}

func (s *Server) ExtGameAward(ctx context.Context, req *pb.ExtGameAwardReq) (*pb.ExtGameAwardResp, error) {
    out := &pb.ExtGameAwardResp{}
    var err error

    if req.GetAmount() == 0 {
        // 参数错误
        log.ErrorWithCtx(ctx, "ExtGameAward fail to check param, amount:%d", req.GetAmount())
        return out, nil
    }

    uid := req.GetUserId()
    if uid == 0 {
        // 通过openid获取uid
        uid, err = s.authManager.GetUidByOpenId(ctx, req.GetOpenid())
        if err != nil || uid == 0 {
            log.ErrorWithCtx(ctx, "ExtGameConsume fail to GetUidByOpenId, uid:%d err:%v", uid, err)
            return out, err
        }
    }

    return out, s.consumeAndRewardMgr.SendAward(ctx, uid, req)
}

func (s *Server) CancelUserExtGameJsCode(c context.Context, req *pb.CancelUserExtGameJsCodeReq) (*pb.CancelUserExtGameJsCodeResp, error) {
    out := &pb.CancelUserExtGameJsCodeResp{}
    defer func() {
        log.InfoWithCtx(c, "CancelUserExtGameJsCode req:%+v, resp: %+v", req, out)
    }()
    err := s.authManager.CancelJsCode(c, req.GetJsCode())
    if err != nil {
        log.ErrorWithCtx(c, "CancelUserExtGameJsCode fail to CancelJsCode, err:%v", err)
        return out, err
    }
    return out, nil
}

func (s *Server) GetUidByOpenid(c context.Context, req *pb.GetUidByOpenidReq) (*pb.GetUidByOpenidResp, error) {
    out := &pb.GetUidByOpenidResp{}
    defer func() {
        log.DebugWithCtx(c, "GetUidByOpenid req:%+v, resp: %+v", req, out)
    }()
    uid, err := s.authManager.GetUidByOpenId(c, req.GetOpenid())
    if err != nil {
        log.ErrorWithCtx(c, "GetUidByOpenid fail to GetUidByOpenId, err:%v", err)
        return out, err
    }
    out.Uid = uid
    return out, nil
}

func (s *Server) GetUserExtGameOpenid(c context.Context, req *pb.GetUserExtGameOpenidReq) (*pb.GetUserExtGameOpenidResp, error) {
    out := &pb.GetUserExtGameOpenidResp{}
    defer func() {
        log.DebugWithCtx(c, "GetUserExtGameOpenid req:%+v, resp: %+v", req, out)
    }()
    openId, err := s.authManager.CreateOpenIdIfNotExists(c, req.GetUid())
    if err != nil {
        log.ErrorWithCtx(c, "GetUserExtGameOpenid fail to CreateOpenIdIfNotExists, err:%v", err)
        return out, err
    }
    out.Openid = openId
    return out, nil
}

func (s *Server) CheckUserGameAccess(ctx context.Context, req *pb.CheckUserGameAccessReq) (*pb.CheckUserGameAccessResp, error) {
    out := &pb.CheckUserGameAccessResp{}
    log.DebugWithCtx(ctx, "CheckUserGameAccess req:%+v", req)

    if s.bc.NeedToCheckChannelWithe() && s.bc.GetChannelWhiteKeyPrefix() != "" {
        // 检查是否在白名单房间
        ok := s.commonMgr.CheckIfInWhiteList(ctx, req.GetChannelId(), s.bc.GetChannelWhiteKeyPrefix())
        if !ok {
            log.WarnWithCtx(ctx, "CheckUserGameAccess fail to CheckIfInWhiteList, channelId:%d", req.GetChannelId())
            return out, nil
        }
    }

    isInGroup, isFaceAuth, err := s.commonMgr.CheckIfUserInUserGroup(ctx, req.GetUid())
    if err != nil {
        log.ErrorWithCtx(ctx, "CheckUserGameAccess fail to CheckIfUserInUserGroup, err:%v", err)
        return out, err
    }

    if !isFaceAuth {
        log.InfoWithCtx(ctx, "CheckUserGameAccess fail to isFaceAuth:false")
        return out, nil
    }

    if isInGroup {
        out.Access = true
        return out, nil
    }

    if s.bc.GetUserWhiteKeyPrefix() == "" {
        out.Access = true
        return out, nil
    }

    // 检查是否在白名单
    out.Access = s.commonMgr.CheckIfInWhiteList(ctx, req.GetUid(), s.bc.GetUserWhiteKeyPrefix())
    return out, nil
}

func (s *Server) CheckChannelBlackList(c context.Context, req *pb.CheckChannelBlackListReq) (*pb.CheckChannelBlackListResp, error) {
    out := &pb.CheckChannelBlackListResp{}

    out.IsBlack = s.bc.CheckIfInBlackChannel(req.GetChannelId())
    return out, nil
}

func (s *Server) GetWhiteChannelRandomly(c context.Context, req *pb.GetWhiteChannelRandomlyReq) (*pb.GetWhiteChannelRandomlyResp, error) {
    out := &pb.GetWhiteChannelRandomlyResp{}

    cidList, err := s.commonMgr.GetWhiteChannelRandomly(c, 5)
    if err != nil {
        log.ErrorWithCtx(c, "GetWhiteChannelRandomly fail to GetWhiteChannelRandomly, err:%v", err)
        return out, err
    }

    out.CidList = cidList
    return out, nil
}

func (s *Server) GetConsumeTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error) {
    out := &reconcile_v2.CountResp{}
    beginTime := time.Unix(in.GetBeginTime(), 0)
    endTime := time.Unix(in.GetEndTime(), 0)
    if beginTime.After(endTime) || beginTime.Add(ReconcileMaxDuration).Before(endTime) {
        return out, ErrTimeRangeOver
    }

    var err error
    out, err = s.consumeAndRewardMgr.GetConsumeTotalCount(ctx, beginTime, endTime)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetConsumeTotalCount fail to GetConsumeTotalCountInfo. beginTime:%v, err:%v", beginTime, err)
        return out, err
    }

    return out, nil
}

func (s *Server) GetConsumeOrderIds(ctx context.Context, req *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error) {
    out := &reconcile_v2.OrderIdsResp{}
    beginTime := time.Unix(req.GetBeginTime(), 0)
    endTime := time.Unix(req.GetEndTime(), 0)
    if beginTime.After(endTime) || beginTime.Add(ReconcileMaxDuration).Before(endTime) {
        return out, ErrTimeRangeOver
    }

    var err error
    out, err = s.consumeAndRewardMgr.GetConsumeOrderIds(ctx, beginTime, endTime)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetConsumeOrderIds fail to GetConsumeOrderIds. beginTime:%v, err:%v", beginTime, err)
        return out, err
    }

    return out, nil
}

func (s *Server) GetAwardTotalCount(ctx context.Context, req *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error) {
    out := &reconcile_v2.CountResp{}
    beginTime := time.Unix(req.GetBeginTime(), 0)
    endTime := time.Unix(req.GetEndTime(), 0)
    if beginTime.After(endTime) || beginTime.Add(ReconcileMaxDuration).Before(endTime) {
        return out, ErrTimeRangeOver
    }
    var err error

    out, err = s.consumeAndRewardMgr.GetAwardTotalCount(ctx, beginTime, endTime)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetAwardTotalCount fail to GetAwardTotalCount. beginTime:%v, err:%v", beginTime, err)
        return out, err
    }
    return out, nil
}

func (s *Server) GetAwardOrderIds(ctx context.Context, req *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error) {
    out := &reconcile_v2.OrderIdsResp{}
    beginTime := time.Unix(req.GetBeginTime(), 0)
    endTime := time.Unix(req.GetEndTime(), 0)
    if beginTime.After(endTime) || beginTime.Add(ReconcileMaxDuration).Before(endTime) {
        return out, ErrTimeRangeOver
    }
    var err error

    out, err = s.consumeAndRewardMgr.GetAwardOrderIds(ctx, beginTime, endTime)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetAwardOrderIds fail to GetAwardOrderIds. beginTime:%v, err:%v", beginTime, err)
        return out, err
    }

    return out, nil
}

func (s *Server) SendPlatformMsg(ctx context.Context, req *pb.SendPlatformMsgReq) (*pb.SendPlatformMsgResp, error) {
    out := &pb.SendPlatformMsgResp{}

    if len(req.GetOpenIdList()) == 0 {
        log.WarnWithCtx(ctx, "SendPlatformMsg fail to SendPlatformMsg. msgList is empty")
        return out, nil
    }

    uidMap, err := s.authManager.BatchGetUidByOpenIds(ctx, req.GetOpenIdList())
    if err != nil {
        log.ErrorWithCtx(ctx, "SendPlatformMsg fail to BatchGetUidByOpenIds. openIds:%v, err:%v", req.GetOpenIdList(), err)
        return out, err
    }
    uidList := make([]uint32, 0, len(uidMap))
    for _, uid := range uidMap {
        uidList = append(uidList, uid)
    }

    err = s.commonMgr.AddToSendMsgQueue(ctx, req.GetAppId(), req.GetTemplateId(), req.GetSendType(), uidList)
    if err != nil {
        log.ErrorWithCtx(ctx, "SendPlatformMsg fail to AddToSendMsgQueue. appId:%v, err:%v", req.GetAppId(), err)
        return out, err
    }

    log.DebugWithCtx(ctx, "SendPlatformMsg success. req:%v", req)
    return out, nil
}

func (s *Server) RemoveWhiteList(c context.Context, req *pb.RemoveWhiteListReq) (*pb.RemoveWhiteListResp, error) {
    out := &pb.RemoveWhiteListResp{}

    if req.GetChannelId() != 0 {
        _ = s.commonMgr.RemoveFromWhiteList(c, req.GetChannelId(), s.bc.GetChannelWhiteKeyPrefix())
    }

    if req.GetUid() != 0 {
        _ = s.commonMgr.RemoveFromWhiteList(c, req.GetUid(), s.bc.GetUserWhiteKeyPrefix())
    }

    return out, nil
}

func (s *Server) AddWhiteList(c context.Context, req *pb.AddWhiteListReq) (*pb.AddWhiteListResp, error) {
    out := &pb.AddWhiteListResp{}

    if len(req.GetCidList()) > 0 {
        _ = s.commonMgr.AddWhiteList(c, req.GetCidList(), s.bc.GetChannelWhiteKeyPrefix())
    }

    if len(req.GetUidList()) > 0 {
        _ = s.commonMgr.AddWhiteList(c, req.GetUidList(), s.bc.GetUserWhiteKeyPrefix())
    }

    log.InfoWithCtx(c, "AddWhiteList success. req:%v", req)
    return out, nil
}

func (s *Server) CheckWhiteList(c context.Context, req *pb.CheckWhiteListReq) (*pb.CheckWhiteListResp, error) {
    out := &pb.CheckWhiteListResp{}

    if req.GetChannelId() != 0 {
        out.CidWhite = s.commonMgr.CheckIfInWhiteList(c, req.GetChannelId(), s.bc.GetChannelWhiteKeyPrefix())
    }

    if req.GetUid() != 0 {
        out.UidWhite = s.commonMgr.CheckIfInWhiteList(c, req.GetUid(), s.bc.GetUserWhiteKeyPrefix())
    }

    return out, nil
}

func (s *Server) TestChannelCommonHighLightIm(c context.Context, req *pb.TestChannelCommonHighLightImReq) (*pb.TestChannelCommonHighLightImResp, error) {
    out := &pb.TestChannelCommonHighLightImResp{}
    if req.GetUid() == 0 {
        return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
    }

    err := s.acLayer.SendChannelPublicMsg(c, req.GetUid(), &channelApp.CommonHighLightChannelIm{
        Uid:              req.GetUid(),
        Cid:              req.GetCid(),
        Content:          req.GetContent(),
        HighLightContent: req.GetHighLightContent(),
        JumpUrl:          req.GetJumpUrl(),
        FontColor:        req.GetFontColor(),
        BorderColor:      req.GetBorderColor(),
        HighLightColor:   req.GetHighLightColor(),
        BackgroundColor:  req.GetBackgroundColor(),
    })

    if err != nil {
        log.ErrorWithCtx(c, "TestChannelCommonHighLightIm fail to SendChannelPublicMsg. req:%v, err:%v", req, err)
        return out, err
    }

    log.InfoWithCtx(c, "TestChannelCommonHighLightIm success. req:%v", req)
    return out, nil
}

func (s *Server) DailyConsumeTotalCntPush(c context.Context, req *pb.DailyConsumeTotalCntPushReq) (*pb.DailyConsumeTotalCntPushResp, error) {
    out := &pb.DailyConsumeTotalCntPushResp{}
    log.InfoWithCtx(c, "DailyConsumeTotalCntPush req:%v", req)

    if req.GetBeginTs() == 0 || req.GetEndTs() == 0 {
        return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
    }

    _, _, err := s.consumeAndRewardMgr.TimeRangeConsumeHandle(c, time.Unix(req.GetBeginTs(), 0), time.Unix(req.GetEndTs(), 0), req.GetTitle())
    if err != nil {
        log.ErrorWithCtx(c, "DailyConsumeTotalCntPush fail to DailyReportConsumeHandle. req:%v, err:%v", req, err)
        return out, err
    }

    return out, nil
}
