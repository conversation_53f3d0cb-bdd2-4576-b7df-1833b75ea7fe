package auth_manager

import (
    "context"
    "fmt"
    "github.com/golang/mock/gomock"
    channel_ext_game "golang.52tt.com/protocol/services/channel-ext-game"
    "testing"
)

func TestSessionManager_GenJsCode(t *testing.T) {
    m := newSessionManagerHelper(t)
    m.GetStore().EXPECT().GetOpenIdByUid(gomock.Any(), gomock.Any()).Return("abc", nil)
    m.GetCache().EXPECT().BindJsCodeInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
    fmt.Println(m.GenJsCode(context.Background(), &channel_ext_game.GetUserExtGameJsCodeReq{
        Uid:           1,
        GameId:        3,
        ChannelViewId: "123",
    }))
}

func Test_generateJSCode(t *testing.T) {
    fmt.Println(generateJSCode("123_456"))
}
