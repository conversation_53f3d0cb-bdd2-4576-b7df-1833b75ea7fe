package cache

import (
    "context"
    "fmt"
    "strconv"
    "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"
    "golang.52tt.com/pkg/log"
    "math/big"
    "crypto/rand"
)

/*
房间白名单
数据结构：set
*/

const (
    modPilot = 10
)

func gen<PERSON>ey(keyPrefix string, cid uint32) string {
    return keyPrefix + fmt.Sprintf("%d", cid%modPilot)
}

// CheckIfInWhiteList 查询cid是否在房间白名单中
func (c *Cache) CheckIfInWhiteList(ctx context.Context, keyPrefix string, cid uint32) (bool, error) {
    key := gen<PERSON>ey(keyPrefix, cid)
    ok, err := c.cmder.SIsMember(ctx, key, cid).Result()
    return ok, err
}

// RemoveFromWhiteList 将指定cid从白名单移除
func (c *Cache) RemoveFromWhiteList(ctx context.Context, keyPrefix string, cid uint32) error {
    key := genKey(keyPrefix, cid)
    _, err := c.cmder.SRem(ctx, key, cid).Result()
    return err
}

// AddToWhiteList 新增房间白名单
func (c *Cache) AddToWhiteList(ctx context.Context, keyPrefix string, cidList []uint32) error {
    // 先按mod Pilot 进行分组
    cidsMap := make(map[uint32][]interface{})
    for _, cid := range cidList {
        cidsMap[cid%modPilot] = append(cidsMap[cid%modPilot], cid)
    }

    for keyPilot, cids := range cidsMap {
        key := genKey(keyPrefix, keyPilot)
        _, err := c.cmder.SAdd(ctx, key, cids...).Result()
        if err != nil {
            log.ErrorWithCtx(ctx, "AddToWhiteList fail 2 cids:%v", cids)
            continue
        }
        log.InfoWithCtx(ctx, "AddToWhiteList key:%s success cids:%v", key, cids)
    }

    return nil
}

// GetWhiteListRandomly 随机获取n个白名单房间
func (c *Cache) GetWhiteListRandomly(ctx context.Context, keyPrefix string, n uint32) ([]uint32, error) {
    if n == 0 {
        return []uint32{}, nil
    }

    var cids []uint32
    remaining := int(n)

    indicesMap := make(map[uint32]struct{})
    cnt := 0
    for remaining > 0 && cnt < modPilot {
        // 生成【0-99】的随机数
        randNum, err := rand.Int(rand.Reader, big.NewInt(modPilot))
        if err != nil {
            return nil, fmt.Errorf("failed to generate random number: %w", err)
        }
        if _, ok := indicesMap[uint32(randNum.Int64())]; ok {
            continue
        }
        indicesMap[uint32(randNum.Int64())] = struct{}{}
        cnt++

        key := genKey(keyPrefix, uint32(randNum.Int64()))
        log.DebugWithCtx(ctx, "GetWhiteListRandomly get from:%s", key)
        members, err := c.cmder.SRandMemberN(ctx, key, int64(remaining)).Result()
        if err != nil {
            if err == redis.Nil {
                continue
            }
            return nil, err
        }
        for _, member := range members {
            cid, err := strconv.ParseUint(member, 10, 32)
            if err != nil {
                log.ErrorWithCtx(ctx, "failed to parse member: %v", err)
                continue
            }
            cids = append(cids, uint32(cid))
            remaining--
            if remaining == 0 {
                break
            }
        }
    }

    // 如果没有获取到足够的元素，返回已获取的元素
    return cids, nil
}

/*
   // 生成 0 到 99 的随机数顺序 10个随机数
   indices := make([]int, modPilot)
   for i := range indices {
      indices[i] = i
   }

   // 使用 crypto/rand 打乱 indices 的顺序
   for i := len(indices) - 1; i > 0; i-- {
      j, err := rand.Int(rand.Reader, big.NewInt(int64(i+1)))
      if err != nil {
          return nil, fmt.Errorf("failed to generate random number: %w", err)
      }
      indices[i], indices[j.Int64()] = indices[j.Int64()], indices[i]
   }

   for _, index := range indices {
      if remaining == 0 {
          break
      }

      key := genKey(keyPrefix, uint32(index))

      // 从当前 key 中随机获取一些元素
      members, err := c.cmder.SRandMemberN(ctx, key, int64(remaining)).Result()
      if err != nil {
          if err == redis.Nil {
              continue
          }
          return nil, err
      }

      for _, member := range members {
          cid, err := strconv.ParseUint(member, 10, 32)
          if err != nil {
              log.ErrorWithCtx(ctx, "failed to parse member: %v", err)
              continue
          }
          cids = append(cids, uint32(cid))
          remaining--
          if remaining == 0 {
              break
          }
      }
   }
*/
