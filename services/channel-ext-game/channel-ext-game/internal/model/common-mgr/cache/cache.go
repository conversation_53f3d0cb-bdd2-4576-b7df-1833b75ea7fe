package cache

import (
    "context"
    "encoding/json"
    "errors"
    "fmt"
    "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"
    "golang.52tt.com/pkg/log"
    "time"
)

type GameDownloadInfo struct {
    Id      uint32 `json:"id"`
    Name    string `json:"name"`
    Content string `json:"content"`
    Version string `json:"version"`
    Build   uint32 `json:"build"`
    Zip     string `json:"zip"`
    H5Url   string `json:"h5Url"`
    FullUrl string `json:"fullUrl"`
    Md5     string `json:"md5"`
    Size    uint32 `json:"size"`
}

//go:generate quicksilver-cli test interface ../cache
//go:generate mockgen -destination=../mocks/cache.go -package=mocks golang.52tt.com/services/channel-ext-game/channel-ext-game/internal/model/common-mgr/cache ICache
const (
    ExtGameUserEntryPrefix = "ext_game_user_entry_"
)

type Cache struct {
    cmder redis.Cmdable
}

func NewCache(client redis.Cmdable) *Cache {
    c := &Cache{
        cmder: client,
    }
    return c
}

func (c *Cache) Close() error {
    return c.cmder.(redis.Client).Close()
}

func (c *Cache) GetRedisClient() redis.Cmdable {
    return c.cmder
}

func genUserEntryKey(uid uint32) string {
    return fmt.Sprintf("%s%d", ExtGameUserEntryPrefix, uid)
}

type UserEntry struct {
    InGroup  bool `json:"in_group"`
    FaceAuth bool `json:"face_auth"`
}

// SetUserEntry 设置用户开关记录
func (c *Cache) SetUserEntry(ctx context.Context, uid uint32, inGroup, faceAuth bool, ttl time.Duration) error {
    key := genUserEntryKey(uid)
    val, err := json.Marshal(&UserEntry{
        InGroup:  inGroup,
        FaceAuth: faceAuth,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "SetUserEntry Marshal err:%v", err)
        return err
    }

    _, err = c.cmder.Set(ctx, key, string(val), ttl).Result()
    if err != nil {
        log.ErrorWithCtx(ctx, "SetUserEntry Set err:%v", err)
        return err
    }

    log.InfoWithCtx(ctx, "SetUserEntry uid:%d,inGroup:%v,faceAuth:%v", uid, inGroup, faceAuth)
    return nil
}

func (c *Cache) GetUserEntry(ctx context.Context, uid uint32) (bool, *UserEntry, error) {
    key := genUserEntryKey(uid)
    val, err := c.cmder.Get(ctx, key).Result()
    if err != nil {
        if errors.Is(err, redis.Nil) {
            log.WarnWithCtx(ctx, "GetUserEntry key:%s not exist", key)
            return false, nil, nil
        }
        log.ErrorWithCtx(ctx, "GetUserEntry err:%v", err)
    }

    entry := &UserEntry{}
    err = json.Unmarshal([]byte(val), entry)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUserEntry Unmarshal err:%v", err)
        return false, nil, err
    }

    return true, entry, nil
}

func getGameDownloadInfoKey(appid string) string {
    return fmt.Sprintf("ext_game_download_info_%s", appid)
}

func (c *Cache) UpdateGameDownloadInfo(ctx context.Context, appid string, info *GameDownloadInfo) error {
    key := getGameDownloadInfoKey(appid)
    val, err := json.Marshal(info)
    if err != nil {
        log.ErrorWithCtx(ctx, "UpdateGameDownloadInfo Marshal info:%+v,err:%v", info, err)
        return err
    }
    _, err = c.cmder.Set(ctx, key, string(val), 0).Result()
    if err != nil {
        log.ErrorWithCtx(ctx, "UpdateGameDownloadInfo info:%+v,err:%v", info, err)
        return err
    }

    log.InfoWithCtx(ctx, "UpdateGameDownloadInfo info:%+v", info)
    return nil
}

func (c *Cache) GetGameDownloadInfo(ctx context.Context, appid string) (*GameDownloadInfo, error) {
    key := getGameDownloadInfoKey(appid)
    val, err := c.cmder.Get(ctx, key).Result()
    if err != nil {
        if errors.Is(err, redis.Nil) {
            log.WarnWithCtx(ctx, "GetGameDownloadInfo key:%s not exist", key)
            return nil, nil
        }
        log.ErrorWithCtx(ctx, "GetGameDownloadInfo err:%v", err)
        return nil, err
    }

    info := &GameDownloadInfo{}
    err = json.Unmarshal([]byte(val), info)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetGameDownloadInfo Unmarshal err:%v", err)
        return nil, err
    }
    log.DebugWithCtx(ctx, "GetGameDownloadInfo info:%+v", info)
    return info, nil
}
