package common_mgr

//go:generate quicksilver-cli test interface ../common-mgr
//go:generate mockgen -destination=./mocks/common-mgr.go -package=mocks golang.52tt.com/services/channel-ext-game/channel-ext-game/internal/model/common-mgr IMgr

import (
    "context"
    "gitlab.ttyuyin.com/avengers/tyr/pkg/cluster/timer"
    "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
    "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    "golang.52tt.com/services/channel-ext-game/channel-ext-game/internal/conf"
    "golang.52tt.com/services/channel-ext-game/channel-ext-game/internal/model/common-mgr/cache"
    "golang.52tt.com/services/channel-ext-game/channel-ext-game/internal/model/common-mgr/store"
    "net/http"
    "sync"
    "time"
    anti_corruption_layer "golang.52tt.com/services/channel-ext-game/channel-ext-game/internal/model/anti-corruption-layer"
    ttc_proxy "golang.52tt.com/clients/ttc-proxy"
    ttc_proxy_pb "golang.52tt.com/protocol/services/ttc-proxy"
)

type Mgr struct {
    store store.IStore
    cache cache.ICache
    bc    conf.IBusinessConfManager

    httpCli     *http.Client
    acLayerMgr  anti_corruption_layer.IMgr
    ttcProxyCli ttc_proxy.IClient

    wg       sync.WaitGroup
    shutDown chan struct{}
    timeD    *timer.Timer
}

func NewMgr(s mysql.DBx, cacheClient redis.Cmdable, bc conf.IBusinessConfManager, acLayerMgr anti_corruption_layer.IMgr) (*Mgr, error) {
    mysqlStore := store.NewStore(s)
    redisCli := cache.NewCache(cacheClient)

    ttcProxyCli, err := ttc_proxy.NewClient()
    if err != nil {
        log.Errorf("NewMgr fail to NewClient. err:%v", err)
        return nil, err
    }

    httpCli := &http.Client{
        Timeout: time.Second * 5, // 请求超时时间
        Transport: &http.Transport{
            MaxIdleConns:        100,              // 最大空闲连接
            MaxConnsPerHost:     200,              // 每个pod最多多少链接
            IdleConnTimeout:     60 * time.Second, // 空闲连接的超时时间
            MaxIdleConnsPerHost: 100,              // 每个host保持的空闲连接数
        },
    }

    m := &Mgr{
        store:      mysqlStore,
        cache:      redisCli,
        bc:         bc,
        httpCli:    httpCli,
        acLayerMgr: acLayerMgr,
        shutDown:   make(chan struct{}),

        ttcProxyCli: ttcProxyCli,
    }
    if err := m.setupTimer(); err != nil {
        log.Errorf("NewMgr fail to setupTimer. err:%v", err)
        return nil, err
    }
    return m, nil
}

func (m *Mgr) Stop() {
    _ = m.cache.Close()
    _ = m.store.Close()
    m.shutDown <- struct{}{}
    m.wg.Wait()
    m.timeD.Stop()
}

func (m *Mgr) CheckIfUserInUserGroup(ctx context.Context, uid uint32) (isInGroup bool, isFaceAuth bool, err error) {
    if uid == 0 {
        return false, false, nil
    }

    // 先查缓存
    exist, result, err := m.cache.GetUserEntry(ctx, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "cache.GetIfUserInGroupFlag fail, uid:%d, err:%v", uid, err)
        //return false, err
    }

    if exist {
        return result.InGroup, result.FaceAuth, nil
    }

    groupList := m.bc.GetUserGroupList()
    if len(groupList) == 0 {
        isInGroup = true
    }

    if len(groupList) > 0 {
        // 查人群包接口
        isInGroup, err = m.checkIfUserInGroup(ctx, uid, groupList)
        if err != nil {
            log.ErrorWithCtx(ctx, "CheckIfUserInUserGroup fail, uid:%d, err:%v", uid, err)
            return false, false, nil // 忽略错误，再检查一下白名单用户
        }
    }

    // 最好多检查一下用户实名
    realNameResp, err := m.ttcProxyCli.GetUserRealNameAuthInfoV2(ctx, uint64(uid), false, false)
    if err != nil {
        log.ErrorWithCtx(ctx, "InviteOrder GetUserRealNameAuthInfoV2, uid:%d, err:%v", uid, err)
    }
    // 检查是否二档实名
    for _, v := range realNameResp.GetAuthList() {
        if v.GetAuthType() == uint32(ttc_proxy_pb.AuthType_ENUM_AUTH_TYPE_FACE) &&
            v.GetAuthStauts() == uint32(ttc_proxy_pb.EAuthStatus_ENUM_AUTH_PASS) {
            isFaceAuth = true
            break
        }
    }

    // 设置缓存
    if err := m.cache.SetUserEntry(ctx, uid, isInGroup, isFaceAuth, time.Duration(m.bc.GetUserEntryCacheSec())*time.Second); err != nil {
        log.ErrorWithCtx(ctx, "cache.SetUserEntry fail, uid:%d, err:%v", uid, err)
    }

    return
}

func (m *Mgr) GetDownloadInfo(ctx context.Context, appid string) (*cache.GameDownloadInfo, error) {
    return m.cache.GetGameDownloadInfo(ctx, appid)
}

func (m *Mgr) AddToSendMsgQueue(ctx context.Context, appId string, templateId, sendType uint32, uidList []uint32) error {
    msgList := make([]*cache.SendMsg, 0, len(uidList))
    for _, v := range uidList {
        msg := &cache.SendMsg{
            AppId: appId,
            //SendType:   sendType,
            TemplateId: templateId,
            Uid:        v,
        }
        msgList = append(msgList, msg)
    }

    err := m.cache.BatAddSendMsgQueue(ctx, msgList)
    if err != nil {
        log.ErrorWithCtx(ctx, "AddToSendMsgQueue fail to BatAddSendMsgQueue. err:%v", err)
        return err
    }

    return nil
}

// CheckIfInWhiteList 判断房间是否白名单房间
func (m *Mgr) CheckIfInWhiteList(ctx context.Context, id uint32, keyPrefix string) bool {

    log.DebugWithCtx(ctx, "CheckIfInWhiteList. id:%d, keyPrefix:%s", id, keyPrefix)
    ok, err := m.cache.CheckIfInWhiteList(ctx, keyPrefix, id)
    if err != nil {
        log.ErrorWithCtx(ctx, "CheckIfInWhiteList fail to CheckIfInWhiteChannel. err:%v", err)
        return false
    }

    return ok
}

// RemoveFromWhiteList 移除白名单
func (m *Mgr) RemoveFromWhiteList(ctx context.Context, id uint32, keyPrefix string) error {

    err := m.cache.RemoveFromWhiteList(ctx, keyPrefix, id)
    if err != nil {
        log.ErrorWithCtx(ctx, "RemoveWhiteChannel fail to RemoveFromWhiteList. err:%v", err)
        return err
    }

    return nil
}

// GetWhiteChannelRandomly 随机获取房间白名单
func (m *Mgr) GetWhiteChannelRandomly(ctx context.Context, n uint32) ([]uint32, error) {
    cidList, err := m.cache.GetWhiteListRandomly(ctx, m.bc.GetChannelWhiteKeyPrefix(), n)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetWhiteChannelRandomly fail to GetCidFromWhiteListRandomly. err:%v", err)
        return nil, err
    }

    return cidList, nil
}

// AddWhiteList 批量新增白名单
func (m *Mgr) AddWhiteList(ctx context.Context, idList []uint32, keyPrefix string) error {
    err := m.cache.AddToWhiteList(ctx, keyPrefix, idList)
    if err != nil {
        log.ErrorWithCtx(ctx, "AddWhiteList fail to AddToWhiteList. err:%v", err)
        return err
    }

    return nil
}
