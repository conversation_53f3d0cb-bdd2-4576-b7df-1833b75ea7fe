package consume_and_reward

import (
    "gitlab.ttyuyin.com/avengers/tyr/pkg/cluster/timer"
    "context"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    "time"
    "golang.52tt.com/services/channel-ext-game/channel-ext-game/internal/model/consume-and-reward/store"
    "gitlab.ttyuyin.com/avengers/tyr/pkg/cluster/timer/task/tasks"
    "golang.52tt.com/services/tt-rev/common/feishu"
    "fmt"
    protogrpc "golang.52tt.com/pkg/protocol/grpc"
    "github.com/google/uuid"
)

const (
    ReissueAwardBatCount = 100
)

func (m *Mgr) startTimer() error {
    var err error
    m.timerD, err = timer.NewTimerD(context.Background(),
        "channel-ext-game",
        timer.WithV8RedisCmdable(m.cache.GetRedisClient()))
    if err != nil {
        log.Errorf("startTimer NewTimerD err:%v", err)
        return err
    }

    // 补单
    if err = m.timerD.AddCronTask("@every 5s", "ReissueAwardHandle", tasks.FuncTask(func(ctx context.Context) {
        m.ReissueAwardHandle()
    })); err != nil {
        log.Errorf("startTimer AddCronTask handleVAExpired err:%v", err)
        return err
    }

    // 每日定时推送前一日的财务数据
    if err = m.timerD.AddCronTask("0 * * * * *", "DailyReportConsume", tasks.FuncTask(func(ctx context.Context) {
        m.dailyReportConsume()
    })); err != nil {
        log.Errorf("startTimer AddCronTask handleVAExpired err:%v", err)
        return err
    }

    // 每日定时推送前一小时的财务数据
    if err = m.timerD.AddCronTask("0 * * * * *", "DailyReportConsume", tasks.FuncTask(func(ctx context.Context) {
        m.hourConsumeReport()
    })); err != nil {
        log.Errorf("startTimer AddCronTask handleVAExpired err:%v", err)
        return err
    }

    m.timerD.Start()
    return nil
}

// ReissueAwardHandle 奖励补发handle
func (m *Mgr) ReissueAwardHandle() {
    ctx, cancel := context.WithTimeout(protogrpc.WithServiceInfo(context.Background(), &protogrpc.ServiceInfo{RequestID: uuid.New().String()}), 20*time.Second)
    defer cancel()

    now := time.Now()
    // 最早的检查时间
    intervalHour := time.Duration(m.bc.GetReissueMaxIntervalHour())
    timeAfter := time.Now().Add(-intervalHour * time.Hour)

    tblTime := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.Local)
    // 最早的tblTime
    earliestTblTime := time.Date(timeAfter.Year(), timeAfter.Month(), 1, 0, 0, 0, 0, time.Local)

    awardList := make([]*store.AwardRecord, 0)
    for {
        if tblTime.Before(earliestTblTime) {
            break
        }
        // 获取待补发的奖励列表
        list, err := m.store.GetAwardRecordByStatus(ctx, now, store.AwardStatusInit, ReissueAwardBatCount, timeAfter, now)
        if err != nil {
            log.ErrorWithCtx(ctx, "ReissueAwardHandle fail to GetAwardRecordByStatus. err:%v", err)
            return
        }

        awardList = append(awardList, list...)
        if len(list) >= ReissueAwardBatCount {
            break
        }

        // 本月数据不够，取上个月
        tblTime = tblTime.AddDate(0, -1, 0)
    }

    // 批量补发奖励
    err := m.BatSendAward(ctx, awardList)
    if err != nil {
        log.ErrorWithCtx(ctx, "ReissueAwardHandle fail to BatSendAward. err:%v", err)
        return
    }
}

func (m *Mgr) dailyReportConsume() {
    ctx, cancel := context.WithTimeout(protogrpc.WithServiceInfo(context.Background(), &protogrpc.ServiceInfo{RequestID: uuid.New().String()}), 30*time.Second)
    defer cancel()

    now := time.Now()
    log.DebugWithCtx(ctx, "DailyReportConsume now:%v", now)

    if now.Hour() != 0 || now.Minute() != 0 {
        return
    }

    beginTime := time.Date(now.Year(), now.Month(), now.Day()-1, 0, 0, 0, 0, now.Location())
    endTime := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
    _, _, err := m.reportConsumeHandle(ctx, beginTime, endTime, true, func() string {
        return fmt.Sprintf("日期：%s", beginTime.Format("2006-01-02"))
    }, "昨日数据")
    if err != nil {
        log.ErrorWithCtx(ctx, "DailyReportConsumeHandle fail to reportConsumeHandle. err:%v", err)
        return
    }

    return
}

func (m *Mgr) hourConsumeReport() {
    ctx, cancel := context.WithTimeout(protogrpc.WithServiceInfo(context.Background(), &protogrpc.ServiceInfo{RequestID: uuid.New().String()}), 30*time.Second)
    defer cancel()

    now := time.Now()
    if now.Minute() != 0 {
        return
    }

    endTime := time.Date(now.Year(), now.Month(), now.Day(), now.Hour(), 0, 0, 0, now.Location())
    beginTime := endTime.Add(-time.Hour)
    _, _, err := m.reportConsumeHandle(ctx, beginTime, endTime, true, func() string {
        return fmt.Sprintf("%s：%d点", beginTime.Format("2006-01-02"), beginTime.Hour())
    }, "小时数据")
    if err != nil {
        log.ErrorWithCtx(ctx, "DailyReportConsumeHandle fail to reportConsumeHandle. err:%v", err)
        return
    }

    return
}

// 公共逻辑函数
func (m *Mgr) reportConsumeHandle(
    ctx context.Context,
    beginTime, endTime time.Time,
    pushLark bool,
    titleLine func() string,
    title string,
) (uint32, uint32, error) {

    stCount, err := m.store.GetConsumeStCountByTime(ctx, beginTime, endTime)
    if err != nil {
        log.ErrorWithCtx(ctx, "reportConsumeHandle fail to GetConsumeTotalCount. err:%v", err)
        return 0, 0, err
    }

    packageCnt, dressCnt, totalCnt, err := m.store.GetAwardCountByTimeRange(ctx, beginTime, endTime)
    if err != nil {
        log.ErrorWithCtx(ctx, "reportConsumeHandle fail to GetAwardCountByTimeRange. err:%v", err)
        return 0, 0, err
    }

    if pushLark {
        var rmbValue float64
        if stCount.Worth > 0 {
            rmbValue = float64(stCount.Worth) / 100.0
        }

        var awardRmbValue float64
        if totalCnt.Worth > 0 {
            awardRmbValue = float64(totalCnt.Worth) / 100.0
        }
        lineStr := []string{
            titleLine(),
            fmt.Sprintf("消费订单数：%d", stCount.Count),
            fmt.Sprintf("消费豆豆数：%d", stCount.Worth),
            fmt.Sprintf("消费金额：%.2f元", rmbValue),
            fmt.Sprintf("消费人数：%d", stCount.People),
            fmt.Sprintf(""), // \n
            fmt.Sprintf("奖励数据："),
            fmt.Sprintf("奖励订单总数：%d，包裹奖励订单数：%d，装扮奖励订单数：%d", totalCnt.Count, packageCnt.Count, dressCnt.Count),
            fmt.Sprintf("奖励金额：%.2f元", awardRmbValue),
            fmt.Sprintf("奖励总人数：%d，包裹奖励人数：%d，装扮奖励人数：%d", totalCnt.People, packageCnt.People, dressCnt.People),
        }

        err = m.sendFeiShuMsg(title, lineStr, "")
        if err != nil {
            log.WarnWithCtx(ctx, "reportConsumeHandle failed to sendFeiShuMsg. err:%v", err)
            // 可选：不影响主流程，改为 Warn 并继续
        }
    }

    return uint32(stCount.Count), uint32(stCount.Worth), nil
}

func (m *Mgr) sendFeiShuMsg(title string, textLines []string, atUser string) error {
    lineList := make([][]*feishu.LineMem, 0, len(textLines))
    for _, text := range textLines {
        line := []*feishu.LineMem{
            {Tag: "text", Text: text},
        }
        lineList = append(lineList, line)
    }

    if atUser != "" {
        line := []*feishu.LineMem{
            {Tag: "at", UserId: atUser},
        }
        lineList = append(lineList, line)
    }

    if m.bc.GetFeiShuRobotUrl() == "" {
        return nil
    }

    return feishu.SendFeiShuRichMsg(m.bc.GetFeiShuRobotUrl(), title, lineList)
}
