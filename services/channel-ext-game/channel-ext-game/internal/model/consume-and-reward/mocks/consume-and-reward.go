// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/channel-ext-game/channel-ext-game/internal/model/consume-and-reward (interfaces: IMgr)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"
	time "time"

	gomock "github.com/golang/mock/gomock"
	channel_ext_game "golang.52tt.com/protocol/services/channel-ext-game"
	reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"
	cb "golang.52tt.com/protocol/services/unified_pay/cb"
	store "golang.52tt.com/services/channel-ext-game/channel-ext-game/internal/model/consume-and-reward/store"
)

// MockIMgr is a mock of IMgr interface.
type MockIMgr struct {
	ctrl     *gomock.Controller
	recorder *MockIMgrMockRecorder
}

// MockIMgrMockRecorder is the mock recorder for MockIMgr.
type MockIMgrMockRecorder struct {
	mock *MockIMgr
}

// NewMockIMgr creates a new mock instance.
func NewMockIMgr(ctrl *gomock.Controller) *MockIMgr {
	mock := &MockIMgr{ctrl: ctrl}
	mock.recorder = &MockIMgrMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIMgr) EXPECT() *MockIMgrMockRecorder {
	return m.recorder
}

// BatSendAward mocks base method.
func (m *MockIMgr) BatSendAward(arg0 context.Context, arg1 []*store.AwardRecord) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatSendAward", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatSendAward indicates an expected call of BatSendAward.
func (mr *MockIMgrMockRecorder) BatSendAward(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatSendAward", reflect.TypeOf((*MockIMgr)(nil).BatSendAward), arg0, arg1)
}

// Callback mocks base method.
func (m *MockIMgr) Callback(arg0 context.Context, arg1 uint32, arg2 string) (cb.Op, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Callback", arg0, arg1, arg2)
	ret0, _ := ret[0].(cb.Op)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Callback indicates an expected call of Callback.
func (mr *MockIMgrMockRecorder) Callback(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Callback", reflect.TypeOf((*MockIMgr)(nil).Callback), arg0, arg1, arg2)
}

// Consume mocks base method.
func (m *MockIMgr) Consume(arg0 context.Context, arg1 uint32, arg2 *channel_ext_game.ExtGameConsumeReq) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Consume", arg0, arg1, arg2)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Consume indicates an expected call of Consume.
func (mr *MockIMgrMockRecorder) Consume(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Consume", reflect.TypeOf((*MockIMgr)(nil).Consume), arg0, arg1, arg2)
}

// GetAwardOrderIds mocks base method.
func (m *MockIMgr) GetAwardOrderIds(arg0 context.Context, arg1, arg2 time.Time) (*reconcile_v2.OrderIdsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAwardOrderIds", arg0, arg1, arg2)
	ret0, _ := ret[0].(*reconcile_v2.OrderIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAwardOrderIds indicates an expected call of GetAwardOrderIds.
func (mr *MockIMgrMockRecorder) GetAwardOrderIds(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAwardOrderIds", reflect.TypeOf((*MockIMgr)(nil).GetAwardOrderIds), arg0, arg1, arg2)
}

// GetAwardTotalCount mocks base method.
func (m *MockIMgr) GetAwardTotalCount(arg0 context.Context, arg1, arg2 time.Time) (*reconcile_v2.CountResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAwardTotalCount", arg0, arg1, arg2)
	ret0, _ := ret[0].(*reconcile_v2.CountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAwardTotalCount indicates an expected call of GetAwardTotalCount.
func (mr *MockIMgrMockRecorder) GetAwardTotalCount(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAwardTotalCount", reflect.TypeOf((*MockIMgr)(nil).GetAwardTotalCount), arg0, arg1, arg2)
}

// GetConsumeOrderIds mocks base method.
func (m *MockIMgr) GetConsumeOrderIds(arg0 context.Context, arg1, arg2 time.Time) (*reconcile_v2.OrderIdsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetConsumeOrderIds", arg0, arg1, arg2)
	ret0, _ := ret[0].(*reconcile_v2.OrderIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetConsumeOrderIds indicates an expected call of GetConsumeOrderIds.
func (mr *MockIMgrMockRecorder) GetConsumeOrderIds(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetConsumeOrderIds", reflect.TypeOf((*MockIMgr)(nil).GetConsumeOrderIds), arg0, arg1, arg2)
}

// GetConsumeTotalCount mocks base method.
func (m *MockIMgr) GetConsumeTotalCount(arg0 context.Context, arg1, arg2 time.Time) (*reconcile_v2.CountResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetConsumeTotalCount", arg0, arg1, arg2)
	ret0, _ := ret[0].(*reconcile_v2.CountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetConsumeTotalCount indicates an expected call of GetConsumeTotalCount.
func (mr *MockIMgrMockRecorder) GetConsumeTotalCount(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetConsumeTotalCount", reflect.TypeOf((*MockIMgr)(nil).GetConsumeTotalCount), arg0, arg1, arg2)
}

// ReissueAwardHandle mocks base method.
func (m *MockIMgr) ReissueAwardHandle() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "ReissueAwardHandle")
}

// ReissueAwardHandle indicates an expected call of ReissueAwardHandle.
func (mr *MockIMgrMockRecorder) ReissueAwardHandle() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReissueAwardHandle", reflect.TypeOf((*MockIMgr)(nil).ReissueAwardHandle))
}

// SendAward mocks base method.
func (m *MockIMgr) SendAward(arg0 context.Context, arg1 uint32, arg2 *channel_ext_game.ExtGameAwardReq) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendAward", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendAward indicates an expected call of SendAward.
func (mr *MockIMgrMockRecorder) SendAward(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendAward", reflect.TypeOf((*MockIMgr)(nil).SendAward), arg0, arg1, arg2)
}

// Stop mocks base method.
func (m *MockIMgr) Stop() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Stop")
}

// Stop indicates an expected call of Stop.
func (mr *MockIMgrMockRecorder) Stop() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stop", reflect.TypeOf((*MockIMgr)(nil).Stop))
}

// TimeRangeConsumeHandle mocks base method.
func (m *MockIMgr) TimeRangeConsumeHandle(arg0 context.Context, arg1, arg2 time.Time, arg3 string) (uint32, uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TimeRangeConsumeHandle", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(uint32)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// TimeRangeConsumeHandle indicates an expected call of TimeRangeConsumeHandle.
func (mr *MockIMgrMockRecorder) TimeRangeConsumeHandle(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TimeRangeConsumeHandle", reflect.TypeOf((*MockIMgr)(nil).TimeRangeConsumeHandle), arg0, arg1, arg2, arg3)
}
