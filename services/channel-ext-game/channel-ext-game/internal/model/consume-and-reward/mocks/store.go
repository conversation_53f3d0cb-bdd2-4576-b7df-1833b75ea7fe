// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/channel-ext-game/channel-ext-game/internal/model/consume-and-reward/store (interfaces: IStore)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"
	time "time"

	gomock "github.com/golang/mock/gomock"
	mysql "gitlab.ttyuyin.com/tyr/x/middleware/mysql"
	store "golang.52tt.com/services/channel-ext-game/channel-ext-game/internal/model/consume-and-reward/store"
)

// MockIStore is a mock of IStore interface.
type MockIStore struct {
	ctrl     *gomock.Controller
	recorder *MockIStoreMockRecorder
}

// MockIStoreMockRecorder is the mock recorder for MockIStore.
type MockIStoreMockRecorder struct {
	mock *MockIStore
}

// NewMockIStore creates a new mock instance.
func NewMockIStore(ctrl *gomock.Controller) *MockIStore {
	mock := &MockIStore{ctrl: ctrl}
	mock.recorder = &MockIStoreMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIStore) EXPECT() *MockIStoreMockRecorder {
	return m.recorder
}

// BatchInsertAwardRecord mocks base method.
func (m *MockIStore) BatchInsertAwardRecord(arg0 context.Context, arg1 []*store.AwardRecord, arg2 time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchInsertAwardRecord", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchInsertAwardRecord indicates an expected call of BatchInsertAwardRecord.
func (mr *MockIStoreMockRecorder) BatchInsertAwardRecord(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchInsertAwardRecord", reflect.TypeOf((*MockIStore)(nil).BatchInsertAwardRecord), arg0, arg1, arg2)
}

// ChangeConsumeRecordPayInfo mocks base method.
func (m *MockIStore) ChangeConsumeRecordPayInfo(arg0 context.Context, arg1 time.Time, arg2 uint32, arg3 []uint32, arg4 uint32, arg5, arg6 string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ChangeConsumeRecordPayInfo", arg0, arg1, arg2, arg3, arg4, arg5, arg6)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ChangeConsumeRecordPayInfo indicates an expected call of ChangeConsumeRecordPayInfo.
func (mr *MockIStoreMockRecorder) ChangeConsumeRecordPayInfo(arg0, arg1, arg2, arg3, arg4, arg5, arg6 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChangeConsumeRecordPayInfo", reflect.TypeOf((*MockIStore)(nil).ChangeConsumeRecordPayInfo), arg0, arg1, arg2, arg3, arg4, arg5, arg6)
}

// Close mocks base method.
func (m *MockIStore) Close() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Close")
	ret0, _ := ret[0].(error)
	return ret0
}

// Close indicates an expected call of Close.
func (mr *MockIStoreMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIStore)(nil).Close))
}

// CreateAwardTable mocks base method.
func (m *MockIStore) CreateAwardTable(arg0 context.Context, arg1 time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateAwardTable", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateAwardTable indicates an expected call of CreateAwardTable.
func (mr *MockIStoreMockRecorder) CreateAwardTable(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateAwardTable", reflect.TypeOf((*MockIStore)(nil).CreateAwardTable), arg0, arg1)
}

// GetAwardCountByTimeRange mocks base method.
func (m *MockIStore) GetAwardCountByTimeRange(arg0 context.Context, arg1, arg2 time.Time) (*store.StCount, *store.StCount, *store.StCount, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAwardCountByTimeRange", arg0, arg1, arg2)
	ret0, _ := ret[0].(*store.StCount)
	ret1, _ := ret[1].(*store.StCount)
	ret2, _ := ret[2].(*store.StCount)
	ret3, _ := ret[3].(error)
	return ret0, ret1, ret2, ret3
}

// GetAwardCountByTimeRange indicates an expected call of GetAwardCountByTimeRange.
func (mr *MockIStoreMockRecorder) GetAwardCountByTimeRange(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAwardCountByTimeRange", reflect.TypeOf((*MockIStore)(nil).GetAwardCountByTimeRange), arg0, arg1, arg2)
}

// GetAwardOrderIds mocks base method.
func (m *MockIStore) GetAwardOrderIds(arg0 context.Context, arg1, arg2, arg3 time.Time, arg4 []uint32) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAwardOrderIds", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAwardOrderIds indicates an expected call of GetAwardOrderIds.
func (mr *MockIStoreMockRecorder) GetAwardOrderIds(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAwardOrderIds", reflect.TypeOf((*MockIStore)(nil).GetAwardOrderIds), arg0, arg1, arg2, arg3, arg4)
}

// GetAwardRecordByStatus mocks base method.
func (m *MockIStore) GetAwardRecordByStatus(arg0 context.Context, arg1 time.Time, arg2, arg3 uint32, arg4, arg5 time.Time) ([]*store.AwardRecord, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAwardRecordByStatus", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].([]*store.AwardRecord)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAwardRecordByStatus indicates an expected call of GetAwardRecordByStatus.
func (mr *MockIStoreMockRecorder) GetAwardRecordByStatus(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAwardRecordByStatus", reflect.TypeOf((*MockIStore)(nil).GetAwardRecordByStatus), arg0, arg1, arg2, arg3, arg4, arg5)
}

// GetAwardTotalCountInfo mocks base method.
func (m *MockIStore) GetAwardTotalCountInfo(arg0 context.Context, arg1, arg2, arg3 time.Time, arg4 []uint32) (*store.StCount, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAwardTotalCountInfo", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*store.StCount)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAwardTotalCountInfo indicates an expected call of GetAwardTotalCountInfo.
func (mr *MockIStoreMockRecorder) GetAwardTotalCountInfo(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAwardTotalCountInfo", reflect.TypeOf((*MockIStore)(nil).GetAwardTotalCountInfo), arg0, arg1, arg2, arg3, arg4)
}

// GetConsumeOrderIds mocks base method.
func (m *MockIStore) GetConsumeOrderIds(arg0 context.Context, arg1, arg2 time.Time) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetConsumeOrderIds", arg0, arg1, arg2)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetConsumeOrderIds indicates an expected call of GetConsumeOrderIds.
func (mr *MockIStoreMockRecorder) GetConsumeOrderIds(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetConsumeOrderIds", reflect.TypeOf((*MockIStore)(nil).GetConsumeOrderIds), arg0, arg1, arg2)
}

// GetConsumeRecordByPayOrderId mocks base method.
func (m *MockIStore) GetConsumeRecordByPayOrderId(arg0 context.Context, arg1 time.Time, arg2 string) (*store.ConsumeRecord, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetConsumeRecordByPayOrderId", arg0, arg1, arg2)
	ret0, _ := ret[0].(*store.ConsumeRecord)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetConsumeRecordByPayOrderId indicates an expected call of GetConsumeRecordByPayOrderId.
func (mr *MockIStoreMockRecorder) GetConsumeRecordByPayOrderId(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetConsumeRecordByPayOrderId", reflect.TypeOf((*MockIStore)(nil).GetConsumeRecordByPayOrderId), arg0, arg1, arg2)
}

// GetConsumeStCountByTime mocks base method.
func (m *MockIStore) GetConsumeStCountByTime(arg0 context.Context, arg1, arg2 time.Time) (*store.StCount, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetConsumeStCountByTime", arg0, arg1, arg2)
	ret0, _ := ret[0].(*store.StCount)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetConsumeStCountByTime indicates an expected call of GetConsumeStCountByTime.
func (mr *MockIStoreMockRecorder) GetConsumeStCountByTime(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetConsumeStCountByTime", reflect.TypeOf((*MockIStore)(nil).GetConsumeStCountByTime), arg0, arg1, arg2)
}

// GetConsumeTotalCountInfo mocks base method.
func (m *MockIStore) GetConsumeTotalCountInfo(arg0 context.Context, arg1, arg2 time.Time) (*store.StCount, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetConsumeTotalCountInfo", arg0, arg1, arg2)
	ret0, _ := ret[0].(*store.StCount)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetConsumeTotalCountInfo indicates an expected call of GetConsumeTotalCountInfo.
func (mr *MockIStoreMockRecorder) GetConsumeTotalCountInfo(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetConsumeTotalCountInfo", reflect.TypeOf((*MockIStore)(nil).GetConsumeTotalCountInfo), arg0, arg1, arg2)
}

// InsertExtGameConsumeRecord mocks base method.
func (m *MockIStore) InsertExtGameConsumeRecord(arg0 context.Context, arg1 *store.ConsumeRecord) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InsertExtGameConsumeRecord", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// InsertExtGameConsumeRecord indicates an expected call of InsertExtGameConsumeRecord.
func (mr *MockIStoreMockRecorder) InsertExtGameConsumeRecord(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertExtGameConsumeRecord", reflect.TypeOf((*MockIStore)(nil).InsertExtGameConsumeRecord), arg0, arg1)
}

// Transaction mocks base method.
func (m *MockIStore) Transaction(arg0 context.Context, arg1 func(mysql.Txx) error) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Transaction", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Transaction indicates an expected call of Transaction.
func (mr *MockIStoreMockRecorder) Transaction(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Transaction", reflect.TypeOf((*MockIStore)(nil).Transaction), arg0, arg1)
}

// UpdateAwardStatus mocks base method.
func (m *MockIStore) UpdateAwardStatus(arg0 context.Context, arg1 time.Time, arg2 string, arg3 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAwardStatus", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateAwardStatus indicates an expected call of UpdateAwardStatus.
func (mr *MockIStoreMockRecorder) UpdateAwardStatus(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAwardStatus", reflect.TypeOf((*MockIStore)(nil).UpdateAwardStatus), arg0, arg1, arg2, arg3)
}
