package consume_and_reward

import (
    "context"
    "github.com/golang/mock/gomock"
    "sync"
    "testing"
    "golang.52tt.com/services/channel-ext-game/channel-ext-game/internal/model/consume-and-reward/mocks"
    acLaymocks "golang.52tt.com/services/channel-ext-game/channel-ext-game/internal/model/anti-corruption-layer/mocks"
    bcMocks "golang.52tt.com/services/channel-ext-game/channel-ext-game/internal/conf/mocks"
    account_mocks "golang.52tt.com/clients/mocks/account"
    unified_pay_mocks "golang.52tt.com/clients/mocks/unified_pay"
    "golang.52tt.com/services/channel-ext-game/channel-ext-game/internal/conf"
    pb "golang.52tt.com/protocol/services/channel-ext-game"
    "reflect"
    "time"
    reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"
    "golang.52tt.com/services/channel-ext-game/channel-ext-game/internal/model/consume-and-reward/store"
    "golang.52tt.com/pkg/protocol"
    "fmt"
    "golang.52tt.com/clients/account"
    "google.golang.org/grpc/codes"
    "golang.52tt.com/protocol/common/status"
)

var (
    testMgr *Mgr

    ctx       = context.Background()
    mockStore *mocks.MockIStore
    mockCache *mocks.MockICache
    mockBc    *bcMocks.MockIBusinessConfManager
    ayLayMock *acLaymocks.MockIMgr

    accountClient    *account_mocks.MockIClient
    unifiedPayClient *unified_pay_mocks.MockIClient

    testUid = uint32(1)
)

func initTestMgr(t *testing.T) {
    ctrl := gomock.NewController(t)
    mockStore = mocks.NewMockIStore(ctrl)
    mockCache = mocks.NewMockICache(ctrl)
    mockBc = bcMocks.NewMockIBusinessConfManager(ctrl)
    ayLayMock = acLaymocks.NewMockIMgr(ctrl)

    accountClient = account_mocks.NewMockIClient(ctrl)
    unifiedPayClient = unified_pay_mocks.NewMockIClient(ctrl)

    testMgr = &Mgr{
        store:      mockStore,
        cache:      mockCache,
        bc:         mockBc,
        acLayerMgr: ayLayMock,
        wg:         sync.WaitGroup{},
        shutDown:   make(chan struct{}),

        accountCli:    accountClient,
        unifiedPayCli: unifiedPayClient,
    }
}

func TestMgr_Consume(t *testing.T) {
    initTestMgr(t)
    type args struct {
        ctx context.Context
        uid uint32
        in  *pb.ExtGameConsumeReq
    }
    tests := []struct {
        name     string
        initFunc func()
        args     args
        wantErr  bool
    }{
        {
            name: "success",
            initFunc: func() {
                mockBc.EXPECT().GetGameAppConfByAppid(gomock.Any()).Return(&conf.GameAppConf{
                    PayAppId: "TT_ZDXX",
                }, true)
                mockStore.EXPECT().GetConsumeRecordByPayOrderId(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, false, nil)
                unifiedPayClient.EXPECT().PresetFreeze(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(uint32(0), nil)
                mockStore.EXPECT().InsertExtGameConsumeRecord(gomock.Any(), gomock.Any()).Return(nil)

                accountClient.EXPECT().GetUser(gomock.Any(), gomock.Any()).Return(&account.User{
                    Uid: testUid,
                }, nil).AnyTimes()
                unifiedPayClient.EXPECT().UnfreezeAndConsume(gomock.Any(), gomock.Any()).Return("2024-12-27 00:00:00", "xxxx", nil).AnyTimes()
                mockStore.EXPECT().ChangeConsumeRecordPayInfo(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(true, nil).AnyTimes()
            },
            args: args{
                ctx: ctx,
                uid: testUid,
                in: &pb.ExtGameConsumeReq{
                    Openid:  "1111",
                    Appid:   "1",
                    OrderId: "1",
                    Amount:  1,
                },
            },
            wantErr: false,
        },
        {
            name: "fail at gameConf not exit",
            initFunc: func() {
                mockBc.EXPECT().GetGameAppConfByAppid(gomock.Any()).Return(nil, false)
            },
            args: args{
                ctx: ctx,
                uid: testUid,
                in:  &pb.ExtGameConsumeReq{},
            },
            wantErr: true,
        },
        {
            name: "fail at presetFreeze",
            initFunc: func() {
                mockBc.EXPECT().GetGameAppConfByAppid(gomock.Any()).Return(&conf.GameAppConf{
                    PayAppId: "TT_ZDXX",
                }, true)
                mockStore.EXPECT().GetConsumeRecordByPayOrderId(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, false, nil)
                unifiedPayClient.EXPECT().PresetFreeze(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(uint32(0), protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "GameId不存在"))
            },
            args: args{
                ctx: nil,
            },
            wantErr: true,
        },
        {
            name: "fail at Store",
            initFunc: func() {
                mockBc.EXPECT().GetGameAppConfByAppid(gomock.Any()).Return(&conf.GameAppConf{
                    PayAppId: "TT_ZDXX",
                }, true)
                mockStore.EXPECT().GetConsumeRecordByPayOrderId(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, false, nil)
                unifiedPayClient.EXPECT().PresetFreeze(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(uint32(0), nil)
                mockStore.EXPECT().InsertExtGameConsumeRecord(gomock.Any(), gomock.Any()).Return(fmt.Errorf("err"))
            },
            args: args{
                ctx: ctx,
                uid: testUid,
                in:  &pb.ExtGameConsumeReq{},
            },
            wantErr: true,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := testMgr
            if tt.initFunc != nil {
                tt.initFunc()
            }
            if _, err := m.Consume(tt.args.ctx, tt.args.uid, tt.args.in); (err != nil) != tt.wantErr {
                t.Errorf("Consume() error = %v, wantErr %v", err, tt.wantErr)
            }
        })
    }
}

func TestMgr_SendAward(t *testing.T) {
    initTestMgr(t)
    type args struct {
        ctx context.Context
        uid uint32
        in  *pb.ExtGameAwardReq
    }
    tests := []struct {
        name     string
        initFunc func()
        args     args
        wantErr  bool
    }{
        {
            name: "success",
            initFunc: func() {
                mockBc.EXPECT().GetGameAppConfByAppid(gomock.Any()).Return(&conf.GameAppConf{
                    AwardList: []*conf.AwardInfo{
                        {
                            RewardId:   "1",
                            RewardType: 1,
                            Worth:      1,
                        },
                    },
                }, true)

                mockStore.EXPECT().BatchInsertAwardRecord(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
                ayLayMock.EXPECT().AwardPackage(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
            },
            args: args{
                ctx: ctx,
                uid: testUid,
                in: &pb.ExtGameAwardReq{
                    OrderId:    "11",
                    RewardId:   "1",
                    RewardType: 1,
                    Amount:     1,
                },
            },
            wantErr: false,
        },
        {
            name: "fail at gameConf not exit",
            initFunc: func() {
                mockBc.EXPECT().GetGameAppConfByAppid(gomock.Any()).Return(nil, false)
            },
            args: args{
                ctx: ctx,
                uid: testUid,
                in:  &pb.ExtGameAwardReq{},
            },
            wantErr: true,
        },
        {
            name: "dress award",
            initFunc: func() {
                mockBc.EXPECT().GetGameAppConfByAppid(gomock.Any()).Return(&conf.GameAppConf{
                    AwardList: []*conf.AwardInfo{
                        {
                            RewardId:   "1",
                            RewardType: 8,
                            Worth:      0,
                        },
                    },
                }, true)
                mockStore.EXPECT().BatchInsertAwardRecord(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
                ayLayMock.EXPECT().AwardDress(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
            },
            args: args{
                ctx: ctx,
                uid: testUid,
                in: &pb.ExtGameAwardReq{
                    RewardId:   "1",
                    RewardType: 8,
                    Amount:     0,
                },
            },
            wantErr: false,
        },
        {
            name: "fail at award not exit",
            initFunc: func() {
                mockBc.EXPECT().GetGameAppConfByAppid(gomock.Any()).Return(&conf.GameAppConf{
                    AwardList: []*conf.AwardInfo{
                        {
                            RewardId:   "1",
                            RewardType: 8,
                            Worth:      0,
                        },
                    },
                }, true)
            },
            args: args{
                ctx: ctx,
                uid: testUid,
                in: &pb.ExtGameAwardReq{
                    RewardId:   "2",
                    RewardType: 2,
                    Amount:     1,
                },
            },
            wantErr: true,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := testMgr
            if tt.initFunc != nil {
                tt.initFunc()
            }
            if err := m.SendAward(tt.args.ctx, tt.args.uid, tt.args.in); (err != nil) != tt.wantErr {
                t.Errorf("SendAward() error = %v, wantErr %v", err, tt.wantErr)
            }
        })
    }
}

func TestMgr_GetAwardOrderIds(t *testing.T) {
    // 跨月时间
    beginTime := time.Date(2024, 12, 31, 23, 24, 0, 0, time.Local)
    endTime := beginTime.Add(time.Minute)
    initTestMgr(t)
    type args struct {
        ctx       context.Context
        beginTime time.Time
        endTime   time.Time
    }
    tests := []struct {
        name     string
        initFunc func()
        args     args
        want     *reconcile_v2.OrderIdsResp
        wantErr  bool
    }{
        {
            name: "success",
            initFunc: func() {
                mockStore.EXPECT().GetAwardOrderIds(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]string{}, nil).AnyTimes()
            },
            args: args{
                ctx:       ctx,
                beginTime: beginTime,
                endTime:   endTime,
            },
            want: &reconcile_v2.OrderIdsResp{
                OrderIds: []string{},
            },
            wantErr: false,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := testMgr
            if tt.initFunc != nil {
                tt.initFunc()
            }
            got, err := m.GetAwardOrderIds(tt.args.ctx, tt.args.beginTime, tt.args.endTime)
            if (err != nil) != tt.wantErr {
                t.Errorf("GetAwardOrderIds() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
            if !reflect.DeepEqual(got, tt.want) {
                t.Errorf("GetAwardOrderIds() got = %v, want %v", got, tt.want)
            }
        })
    }
}

func TestMgr_GetAwardTotalCount(t *testing.T) {
    // 跨月时间
    beginTime := time.Date(2024, 12, 31, 23, 24, 0, 0, time.Local)
    endTime := beginTime.Add(time.Minute)
    initTestMgr(t)
    type args struct {
        ctx       context.Context
        beginTime time.Time
        endTime   time.Time
    }
    tests := []struct {
        name     string
        initFunc func()
        args     args
        want     *reconcile_v2.CountResp
        wantErr  bool
    }{
        {
            name: "success",
            initFunc: func() {
                mockStore.EXPECT().GetAwardTotalCountInfo(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&store.StCount{}, nil).AnyTimes()
            },
            args: args{
                ctx:       ctx,
                beginTime: beginTime,
                endTime:   endTime,
            },
            want:    &reconcile_v2.CountResp{},
            wantErr: false,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := testMgr
            if tt.initFunc != nil {
                tt.initFunc()
            }
            got, err := m.GetAwardTotalCount(tt.args.ctx, tt.args.beginTime, tt.args.endTime)
            if (err != nil) != tt.wantErr {
                t.Errorf("GetAwardTotalCount() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
            if !reflect.DeepEqual(got, tt.want) {
                t.Errorf("GetAwardTotalCount() got = %v, want %v", got, tt.want)
            }
        })
    }
}

func TestMgr_GetConsumeOrderIds(t *testing.T) {
    // 跨月时间
    beginTime := time.Date(2024, 12, 31, 23, 24, 0, 0, time.Local)
    endTime := beginTime.Add(time.Minute)
    initTestMgr(t)
    type args struct {
        ctx       context.Context
        beginTime time.Time
        endTime   time.Time
    }
    tests := []struct {
        name     string
        initFunc func()
        args     args
        want     *reconcile_v2.OrderIdsResp
        wantErr  bool
    }{
        {
            name: "success",
            initFunc: func() {
                mockStore.EXPECT().GetConsumeOrderIds(gomock.Any(), gomock.Any(), gomock.Any()).Return([]string{}, nil).AnyTimes()
            },
            args: args{
                ctx:       ctx,
                beginTime: beginTime,
                endTime:   endTime,
            },
            want: &reconcile_v2.OrderIdsResp{
                OrderIds: []string{},
            },
            wantErr: false,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := testMgr
            if tt.initFunc != nil {
                tt.initFunc()
            }
            got, err := m.GetConsumeOrderIds(tt.args.ctx, tt.args.beginTime, tt.args.endTime)
            if (err != nil) != tt.wantErr {
                t.Errorf("GetConsumeOrderIds() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
            if !reflect.DeepEqual(got, tt.want) {
                t.Errorf("GetConsumeOrderIds() got = %v, want %v", got, tt.want)
            }
        })
    }
}

func TestMgr_GetConsumeTotalCount(t *testing.T) {
    // 跨月时间
    beginTime := time.Date(2024, 12, 31, 23, 24, 0, 0, time.Local)
    endTime := beginTime.Add(time.Minute)
    initTestMgr(t)
    type args struct {
        ctx       context.Context
        beginTime time.Time
        endTime   time.Time
    }
    tests := []struct {
        name     string
        initFunc func()
        args     args
        want     *reconcile_v2.CountResp
        wantErr  bool
    }{
        {
            name: "success",
            initFunc: func() {
                mockStore.EXPECT().GetConsumeTotalCountInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(&store.StCount{}, nil).AnyTimes()
            },
            args: args{
                ctx:       ctx,
                beginTime: beginTime,
                endTime:   endTime,
            },
            want:    &reconcile_v2.CountResp{},
            wantErr: false,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := testMgr
            if tt.initFunc != nil {
                tt.initFunc()
            }
            got, err := m.GetConsumeTotalCount(tt.args.ctx, tt.args.beginTime, tt.args.endTime)
            if (err != nil) != tt.wantErr {
                t.Errorf("GetConsumeTotalCount() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
            if !reflect.DeepEqual(got, tt.want) {
                t.Errorf("GetConsumeTotalCount() got = %v, want %v", got, tt.want)
            }
        })
    }
}
