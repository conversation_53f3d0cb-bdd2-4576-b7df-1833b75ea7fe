package store

import (
    "time"
    "fmt"
    "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    "context"
)

const (
    AwardStatusInit = iota
    AwardStatusSuccess
)

var createAwardTbl = `CREATE TABLE IF NOT EXISTS %s (
    id int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id 自增',
    order_id varchar(128) DEFAULT NULL COMMENT '奖励订单号',
    source_order_id varchar(128) DEFAULT NULL COMMENT '外部奖励订单号',
    game_app_id varchar(64) DEFAULT NULL COMMENT '第三方游戏app_id',
    
    uid int(10) unsigned NOT NULL COMMENT 'uid',
    openid varchar(128) DEFAULT NULL COMMENT '用户游戏openid',
    gift_id varchar(128) NOT NULL default '0' COMMENT '奖励礼物id',
    gift_type int(10) unsigned NOT NULL default '0' COMMENT '礼物类型 pb.ItemType',
    gift_worth int(10) unsigned NOT NULL default '0' COMMENT '奖励价值',
    amount int(10) unsigned NOT NULL default '0' COMMENT '发放数量',
    
    status tinyint unsigned NOT NULL COMMENT '状态 0-待发奖 1-发奖成功',
    
    award_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    ctime timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    mtime timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    PRIMARY KEY (id),
    UNIQUE KEY idx_order_id (order_id),
    INDEX idx_uid (uid),
    INDEX idx_status (status),
    INDEX idx_award_time (award_time)
)engine=InnoDB default charset=utf8 COMMENT "奖励发放记录表";`

func GenAwardTblName(roundTime time.Time) string {
    return fmt.Sprintf("channel_ext_game_award_%s", roundTime.Format("200601"))
}

type AwardRecord struct {
    ID            uint32 `db:"id"`
    OrderID       string `db:"order_id"`
    SourceOrderID string `db:"source_order_id"`
    GameAppId     string `db:"game_app_id"`

    Uid      uint32 `db:"uid"`
    OpenId   string `db:"openid"`
    GiftID   string `db:"gift_id"`
    GiftType uint32 `db:"gift_type"`
    //GiftName  string `db:"gift_name"`
    GiftWorth uint32 `db:"gift_worth"`
    Amount    uint32 `db:"amount"`

    Status uint32 `db:"status"`

    AwardTime  time.Time `db:"award_time"`
    CreateTime time.Time `db:"ctime"`
    UpdateTime time.Time `db:"mtime"`
}

// CreateAwardTable 创建奖励表
func (s *Store) CreateAwardTable(ctx context.Context, roundTime time.Time) error {
    _, err := s.db.ExecContext(ctx, fmt.Sprintf(createAwardTbl, GenAwardTblName(roundTime)))
    if err != nil {
        log.ErrorWithCtx(ctx, "CreateAwardTable fail. err %v", err)
        return err
    }

    return nil
}

// BatchInsertAwardRecord 批量插入奖励记录
func (s *Store) BatchInsertAwardRecord(ctx context.Context, records []*AwardRecord, awardTime time.Time) error {
    if len(records) == 0 {
        return nil
    }

    if len(records) > 50 {
        log.ErrorWithCtx(ctx, "BatchInsertAwardRecord too many records %d", len(records))
        return fmt.Errorf("too many records %d", len(records))
    }

    query := fmt.Sprintf("insert into %s (order_id,source_order_id,game_app_id,uid,openid,gift_id,gift_type,gift_worth,amount,status,award_time) values",
        GenAwardTblName(awardTime))
    params := make([]interface{}, 0, len(records))
    for i, record := range records {
        if i != 0 {
            query += ","
        }
        query += "(?,?,?,?,?,?,?,?,?,?,?)"
        params = append(params, record.OrderID, record.SourceOrderID, record.GameAppId, record.Uid, record.OpenId, record.GiftID, record.GiftType, record.GiftWorth, record.Amount,
            record.Status, record.AwardTime)
    }

    _, err := s.db.ExecContext(ctx, query, params...)
    if err != nil {
        // 主键重复
        if mysql.IsMySQLError(err, 1062) {
            return fmt.Errorf("1062 duplicate key")
        }
        // 表不存在
        if mysql.IsMySQLError(err, 1146) {
            // 表不存在，建表
            err = s.CreateAwardTable(ctx, awardTime)
            if err != nil {
                log.ErrorWithCtx(ctx, "BatchInsertAwardRecord fail to CreateAwardTable. err %v", err)
                return err
            }

            _, err = s.db.ExecContext(ctx, query, params...)
            if err == nil {
                return nil
            }
        }
        log.ErrorWithCtx(ctx, "BatchInsertAwardRecord fail. err %v", err)
        return err
    }

    return nil
}

// UpdateAwardStatus 更新奖励状态
func (s *Store) UpdateAwardStatus(ctx context.Context, awardTime time.Time, orderID string, status uint32) error {
    query := fmt.Sprintf("update %s set status=? where order_id=?", GenAwardTblName(awardTime))
    _, err := s.db.ExecContext(ctx, query, status, orderID)
    if err != nil {
        log.ErrorWithCtx(ctx, "UpdateAwardStatus fail. err %v", err)
        return err
    }

    return nil
}

// GetAwardRecordByStatus 根据状态获取奖励记录
func (s *Store) GetAwardRecordByStatus(ctx context.Context, tblTime time.Time, status, limit uint32, beginTime, endTime time.Time) ([]*AwardRecord, error) {
    records := make([]*AwardRecord, 0)
    if limit == 0 {
        return records, nil
    }

    query := fmt.Sprintf("select id,order_id,source_order_id,game_app_id,uid,gift_id,gift_type,gift_worth,amount,status,award_time "+
        " from %s where award_time>=? and award_time<? and status=? limit ?", GenAwardTblName(tblTime))

    err := s.db.SelectContext(ctx, &records, query, beginTime, endTime, status, limit)
    if err != nil {
        // 表不存在
        if mysql.IsMySQLError(err, 1146) || mysql.IsNoRowsError(err) {
            return records, nil
        }
        log.ErrorWithCtx(ctx, "GetAwardRecordByStatus fail. err %v", err)
        return records, err
    }

    return records, nil
}

func (s *Store) GetAwardTotalCountInfo(ctx context.Context, tblTime, beginTime, endTime time.Time, giftTypeList []uint32) (*StCount, error) {
    out := &StCount{}
    temp := "SELECT COUNT(1) as count, IFNULL(SUM(gift_worth),0) as worth FROM %s WHERE award_time >= ? AND award_time < ? AND gift_type in (%s) AND status=1"

    query := fmt.Sprintf(temp, GenAwardTblName(tblTime), genParamJoinStr(giftTypeList))

    err := s.db.GetContext(ctx, out, query, beginTime, endTime)
    if err != nil {
        if mysql.IsMySQLError(err, 1146) || mysql.IsNoRowsError(err) {
            return out, nil
        }
        log.ErrorWithCtx(ctx, "GetAwardCountByTimeRange fail. queryMonthTime:%v, err:%v", beginTime, err)
        return out, err
    }
    return out, nil
}

func (s *Store) GetAwardOrderIds(ctx context.Context, tblTime, beginTime, endTime time.Time, giftTypeList []uint32) ([]string, error) {
    list := make([]string, 0)
    temp := "SELECT order_id FROM %s WHERE award_time >= ? AND award_time < ? AND gift_type in (%s) AND status=1"

    query := fmt.Sprintf(temp, GenAwardTblName(tblTime), genParamJoinStr(giftTypeList))
    err := s.db.SelectContext(ctx, &list, query, beginTime, endTime)
    if err != nil {
        if mysql.IsMySQLError(err, 1146) || mysql.IsNoRowsError(err) {
            return list, nil
        }
        log.ErrorWithCtx(ctx, "GetAwardOrderIds fail. queryMonthTime:%v, err:%v", beginTime, err)
        return list, err
    }

    return list, nil
}

func (s *Store) GetAwardCountByTimeRange(ctx context.Context, beginTime, endTime time.Time) (*StCount, *StCount, *StCount, error) {
    query := "SELECT COUNT(1) as count, IF(SUM(gift_worth),SUM(gift_worth),0) as worth,COUNT(DISTINCT uid) as people  FROM %s WHERE award_time >= ? AND award_time < ? and status=1 and gift_type<=2"
    query = fmt.Sprintf(query, GenAwardTblName(beginTime))

    var err error
    packageAward := &StCount{}
    dressAward := &StCount{}
    totalCnt := &StCount{}

    err = s.db.GetContext(ctx, packageAward, query, beginTime, endTime)
    if err != nil {
        if !mysql.IsMySQLError(err, 1146) || !mysql.IsNoRowsError(err) {
            log.ErrorWithCtx(ctx, "GetAwardCountByTimeRange fail. queryMonthTime:%v, err:%v", beginTime, err)
            return nil, nil, nil, err
        }
    }

    query = "SELECT COUNT(1) as count, IF(SUM(gift_worth),SUM(gift_worth),0) as worth,COUNT(DISTINCT uid) as people FROM %s WHERE award_time >= ? AND award_time < ? and status=1 and gift_type>2"
    query = fmt.Sprintf(query, GenAwardTblName(beginTime))
    err = s.db.GetContext(ctx, dressAward, query, beginTime, endTime)
    if err != nil {
        if !mysql.IsMySQLError(err, 1146) || !mysql.IsNoRowsError(err) {
            log.ErrorWithCtx(ctx, "GetAwardCountByTimeRange fail. queryMonthTime:%v, err:%v", beginTime, err)
            return nil, nil, nil, err
        }
    }

    query = fmt.Sprintf("SELECT COUNT(1) as count, IF(SUM(gift_worth),SUM(gift_worth),0) as worth,COUNT(DISTINCT uid) as people FROM %s WHERE award_time >= ? AND award_time < ? and status=1", GenAwardTblName(beginTime))
    err = s.db.GetContext(ctx, totalCnt, query, beginTime, endTime)
    if err != nil {
        if !mysql.IsMySQLError(err, 1146) || !mysql.IsNoRowsError(err) {
            log.ErrorWithCtx(ctx, "GetAwardCountByTimeRange fail. queryMonthTime:%v, err:%v", beginTime, err)
            return nil, nil, nil, err
        }
    }

    return packageAward, dressAward, totalCnt, nil
}
