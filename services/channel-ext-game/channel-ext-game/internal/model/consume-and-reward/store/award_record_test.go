package store

import (
    "testing"
    "time"
)

func TestStore_GetAwardCountByTimeRange(t *testing.T) {
    now := time.Now()
    endTime := time.Date(now.Year(), now.Month(), now.Day(), now.Hour(), 0, 0, 0, now.Location())
    beginTime := endTime.Add(-time.Hour)

    packageCnt, dressCnt, peopleCnt, err := testStore.GetAwardCountByTimeRange(ctx, beginTime, endTime)
    if err != nil {
        t.Fatal(err)
    }

    t.Logf("packageCnt:%+v, dressCnt:%+v, peopleCnt:%+v", packageCnt, dressCnt, peopleCnt)
}
