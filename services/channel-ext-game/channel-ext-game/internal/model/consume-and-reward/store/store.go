package store

import (
    "context"
    "fmt"
    "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    "strings"
)

//go:generate quicksilver-cli test interface ../store
//go:generate mockgen -destination=../mocks/store.go -package=mocks golang.52tt.com/services/channel-ext-game/channel-ext-game/internal/model/consume-and-reward/store IStore

type Store struct {
    db mysql.DBx
}

func NewStore(db mysql.DBx) *Store {
    s := &Store{
        db: db,
    }

    return s
}

func (s *Store) Close() error {
    return s.db.Close()
}

func (s *Store) Transaction(ctx context.Context, f func(tx mysql.Txx) error) error {
    tx, err := s.db.Beginx()
    if err != nil {
        log.ErrorWithCtx(ctx, "Transaction Beginx fail err %v", err)
        return err
    }

    err = f(tx)
    if err != nil {
        log.ErrorWithCtx(ctx, "Transaction fail err %v", err)
        _ = tx.Rollback()
        return err
    }

    return tx.Commit()
}

func genParamJoinStr(list []uint32) string {
    strList := make([]string, 0, len(list))
    for _, i := range list {
        strList = append(strList, fmt.Sprint(i))
    }

    return strings.Join(strList, ",")
}

type StCount struct {
    Count int64 `db:"count"`
    Worth int64 `db:"worth"`

    People int64 `db:"people"`
}
