// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/channel-ext-game/channel-ext-game/internal/kfk-producer (interfaces: IKafkaProducer)

// Package mocks is a generated GoMock package.
package mocks

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockIKafkaProducer is a mock of IKafkaProducer interface.
type MockIKafkaProducer struct {
	ctrl     *gomock.Controller
	recorder *MockIKafkaProducerMockRecorder
}

// MockIKafkaProducerMockRecorder is the mock recorder for MockIKafkaProducer.
type MockIKafkaProducerMockRecorder struct {
	mock *MockIKafkaProducer
}

// NewMockIKafkaProducer creates a new mock instance.
func NewMockIKafkaProducer(ctrl *gomock.Controller) *MockIKafkaProducer {
	mock := &MockIKafkaProducer{ctrl: ctrl}
	mock.recorder = &MockIKafkaProducerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIKafkaProducer) EXPECT() *MockIKafkaProducerMockRecorder {
	return m.recorder
}

// Close mocks base method.
func (m *MockIKafkaProducer) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIKafkaProducerMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIKafkaProducer)(nil).Close))
}

// ProduceTBeanConsumeEvent mocks base method.
func (m *MockIKafkaProducer) ProduceTBeanConsumeEvent(arg0, arg1, arg2 uint32, arg3 string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "ProduceTBeanConsumeEvent", arg0, arg1, arg2, arg3)
}

// ProduceTBeanConsumeEvent indicates an expected call of ProduceTBeanConsumeEvent.
func (mr *MockIKafkaProducerMockRecorder) ProduceTBeanConsumeEvent(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProduceTBeanConsumeEvent", reflect.TypeOf((*MockIKafkaProducer)(nil).ProduceTBeanConsumeEvent), arg0, arg1, arg2, arg3)
}
