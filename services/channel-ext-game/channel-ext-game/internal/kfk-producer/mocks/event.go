// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/channel-ext-game/channel-ext-game/internal/event (interfaces: IKafkaProduce)

// Package mocks is a generated GoMock package.
package mocks

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockIKafkaProduce is a mock of IKafkaProduce interface.
type MockIKafkaProduce struct {
	ctrl     *gomock.Controller
	recorder *MockIKafkaProduceMockRecorder
}

// MockIKafkaProduceMockRecorder is the mock recorder for MockIKafkaProduce.
type MockIKafkaProduceMockRecorder struct {
	mock *MockIKafkaProduce
}

// NewMockIKafkaProduce creates a new mock instance.
func NewMockIKafkaProduce(ctrl *gomock.Controller) *MockIKafkaProduce {
	mock := &MockIKafkaProduce{ctrl: ctrl}
	mock.recorder = &MockIKafkaProduceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIKafkaProduce) EXPECT() *MockIKafkaProduceMockRecorder {
	return m.recorder
}

// Close mocks base method.
func (m *MockIKafkaProduce) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIKafkaProduceMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIKafkaProduce)(nil).Close))
}

// ProduceTBeanConsumeEvent mocks base method.
func (m *MockIKafkaProduce) ProduceTBeanConsumeEvent(arg0, arg1, arg2 uint32, arg3 string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "ProduceTBeanConsumeEvent", arg0, arg1, arg2, arg3)
}

// ProduceTBeanConsumeEvent indicates an expected call of ProduceTBeanConsumeEvent.
func (mr *MockIKafkaProduceMockRecorder) ProduceTBeanConsumeEvent(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProduceTBeanConsumeEvent", reflect.TypeOf((*MockIKafkaProduce)(nil).ProduceTBeanConsumeEvent), arg0, arg1, arg2, arg3)
}
