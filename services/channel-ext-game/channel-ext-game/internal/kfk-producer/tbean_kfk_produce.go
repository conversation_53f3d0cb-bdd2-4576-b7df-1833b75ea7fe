package kfk_producer

//go:generate quicksilver-cli test interface ../kfk-producer
//go:generate mockgen -destination=./mocks/producer.go -package=mocks golang.52tt.com/services/channel-ext-game/channel-ext-game/internal/kfk-producer IKafkaProducer

import (
    "context"
    "gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
    "gitlab.ttyuyin.com/tt-infra/middleware/kafka"
    "gitlab.ttyuyin.com/tt-infra/middleware/kafka/publisher"
    "golang.52tt.com/pkg/log"
    "golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafkatbean"
    "strconv"
    "golang.52tt.com/services/channel-ext-game/channel-ext-game/internal/conf"
)

type KafkaProducer struct {
    tBeanProducer publisher.Publisher
    topic         string
}

func NewKafkaProducer(sc *conf.StartConfig) (*KafkaProducer, error) {
    kfkCfg := kafka.DefaultConfig()

    tBeanProducerPub, err := kafka.NewAsyncPublisher(sc.TBeanKfk.BrokerList(), kfkCfg,
        publisher.WithTopicRegisterHandler(sc.TBeanKfk.TopicList()))
    if err != nil {
        log.Errorf("NewKafkaProducer NewAsyncPublisher virtualImageUserPub failed, err:%v", err)
        return nil, err
    }

    return &KafkaProducer{
        tBeanProducer: tBeanProducerPub,
        topic:         sc.TBeanKfk.TopicList()[0],
    }, nil
}

func (k *KafkaProducer) Close() {
    k.tBeanProducer.Close()
}

func (k *KafkaProducer) ProduceTBeanConsumeEvent(uid, value uint32, timestamp uint32, order string) {
    source := "「闯关专家」游戏内消费"
    event := &kafkatbean.TBeanConsumeEvent{
        Uid:       uid,
        Value:     value,
        Timestamp: timestamp,
        Source:    source,
        OrderId:   order,
    }

    data, err := proto.Marshal(event)
    if err != nil {
        log.Errorf("Marshal err %+v info %+v", err, event)
        return
    }

    key := strconv.FormatUint(uint64(event.Uid), 10)

    res := k.tBeanProducer.Publish(context.Background(), &publisher.ProducerMessage{
        Topic: k.topic,
        Key:   publisher.StringEncoder(key),
        Value: publisher.ByteEncoder(data),
    })
    if res.Err != nil {
        log.Errorf("ProduceTBeanConsumeEvent Publish failed, err: %v, event:%s", err, event.String())
    }

    log.Infof("topic %s produce info %+v", k.topic, event)
}
