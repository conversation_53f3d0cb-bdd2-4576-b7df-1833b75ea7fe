// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/channel-ext-game/channel-ext-game/internal/conf (interfaces: IBusinessConfManager)

// Package mocks is a generated GoMock package.
package mocks

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	conf "golang.52tt.com/services/channel-ext-game/channel-ext-game/internal/conf"
)

// MockIBusinessConfManager is a mock of IBusinessConfManager interface.
type MockIBusinessConfManager struct {
	ctrl     *gomock.Controller
	recorder *MockIBusinessConfManagerMockRecorder
}

// MockIBusinessConfManagerMockRecorder is the mock recorder for MockIBusinessConfManager.
type MockIBusinessConfManagerMockRecorder struct {
	mock *MockIBusinessConfManager
}

// NewMockIBusinessConfManager creates a new mock instance.
func NewMockIBusinessConfManager(ctrl *gomock.Controller) *MockIBusinessConfManager {
	mock := &MockIBusinessConfManager{ctrl: ctrl}
	mock.recorder = &MockIBusinessConfManagerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIBusinessConfManager) EXPECT() *MockIBusinessConfManagerMockRecorder {
	return m.recorder
}

// CheckIfInBlackChannel mocks base method.
func (m *MockIBusinessConfManager) CheckIfInBlackChannel(arg0 uint32) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckIfInBlackChannel", arg0)
	ret0, _ := ret[0].(bool)
	return ret0
}

// CheckIfInBlackChannel indicates an expected call of CheckIfInBlackChannel.
func (mr *MockIBusinessConfManagerMockRecorder) CheckIfInBlackChannel(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckIfInBlackChannel", reflect.TypeOf((*MockIBusinessConfManager)(nil).CheckIfInBlackChannel), arg0)
}

// Close mocks base method.
func (m *MockIBusinessConfManager) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIBusinessConfManagerMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIBusinessConfManager)(nil).Close))
}

// GetChannelWhiteKeyPrefix mocks base method.
func (m *MockIBusinessConfManager) GetChannelWhiteKeyPrefix() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelWhiteKeyPrefix")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetChannelWhiteKeyPrefix indicates an expected call of GetChannelWhiteKeyPrefix.
func (mr *MockIBusinessConfManagerMockRecorder) GetChannelWhiteKeyPrefix() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelWhiteKeyPrefix", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetChannelWhiteKeyPrefix))
}

// GetDownloadInfoUrl mocks base method.
func (m *MockIBusinessConfManager) GetDownloadInfoUrl(arg0 string) string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDownloadInfoUrl", arg0)
	ret0, _ := ret[0].(string)
	return ret0
}

// GetDownloadInfoUrl indicates an expected call of GetDownloadInfoUrl.
func (mr *MockIBusinessConfManagerMockRecorder) GetDownloadInfoUrl(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDownloadInfoUrl", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetDownloadInfoUrl), arg0)
}

// GetDspLpmApiServerHost mocks base method.
func (m *MockIBusinessConfManager) GetDspLpmApiServerHost() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDspLpmApiServerHost")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetDspLpmApiServerHost indicates an expected call of GetDspLpmApiServerHost.
func (mr *MockIBusinessConfManagerMockRecorder) GetDspLpmApiServerHost() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDspLpmApiServerHost", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetDspLpmApiServerHost))
}

// GetExtGameVersionSwitch mocks base method.
func (m *MockIBusinessConfManager) GetExtGameVersionSwitch(arg0, arg1 uint32) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetExtGameVersionSwitch", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExtGameVersionSwitch indicates an expected call of GetExtGameVersionSwitch.
func (mr *MockIBusinessConfManagerMockRecorder) GetExtGameVersionSwitch(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExtGameVersionSwitch", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetExtGameVersionSwitch), arg0, arg1)
}

// GetFeiShuRobotUrl mocks base method.
func (m *MockIBusinessConfManager) GetFeiShuRobotUrl() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFeiShuRobotUrl")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetFeiShuRobotUrl indicates an expected call of GetFeiShuRobotUrl.
func (mr *MockIBusinessConfManagerMockRecorder) GetFeiShuRobotUrl() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFeiShuRobotUrl", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetFeiShuRobotUrl))
}

// GetGameAppConfByAppid mocks base method.
func (m *MockIBusinessConfManager) GetGameAppConfByAppid(arg0 string) (*conf.GameAppConf, bool) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGameAppConfByAppid", arg0)
	ret0, _ := ret[0].(*conf.GameAppConf)
	ret1, _ := ret[1].(bool)
	return ret0, ret1
}

// GetGameAppConfByAppid indicates an expected call of GetGameAppConfByAppid.
func (mr *MockIBusinessConfManagerMockRecorder) GetGameAppConfByAppid(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGameAppConfByAppid", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetGameAppConfByAppid), arg0)
}

// GetGameAppConfMap mocks base method.
func (m *MockIBusinessConfManager) GetGameAppConfMap() map[string]*conf.GameAppConf {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGameAppConfMap")
	ret0, _ := ret[0].(map[string]*conf.GameAppConf)
	return ret0
}

// GetGameAppConfMap indicates an expected call of GetGameAppConfMap.
func (mr *MockIBusinessConfManagerMockRecorder) GetGameAppConfMap() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGameAppConfMap", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetGameAppConfMap))
}

// GetMsgByTemplateId mocks base method.
func (m *MockIBusinessConfManager) GetMsgByTemplateId(arg0 string, arg1 uint32) string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMsgByTemplateId", arg0, arg1)
	ret0, _ := ret[0].(string)
	return ret0
}

// GetMsgByTemplateId indicates an expected call of GetMsgByTemplateId.
func (mr *MockIBusinessConfManagerMockRecorder) GetMsgByTemplateId(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMsgByTemplateId", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetMsgByTemplateId), arg0, arg1)
}

// GetMsgFrequencyLimit mocks base method.
func (m *MockIBusinessConfManager) GetMsgFrequencyLimit(arg0 string, arg1 uint32) uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMsgFrequencyLimit", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	return ret0
}

// GetMsgFrequencyLimit indicates an expected call of GetMsgFrequencyLimit.
func (mr *MockIBusinessConfManagerMockRecorder) GetMsgFrequencyLimit(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMsgFrequencyLimit", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetMsgFrequencyLimit), arg0, arg1)
}

// GetReissueMaxIntervalHour mocks base method.
func (m *MockIBusinessConfManager) GetReissueMaxIntervalHour() uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetReissueMaxIntervalHour")
	ret0, _ := ret[0].(uint32)
	return ret0
}

// GetReissueMaxIntervalHour indicates an expected call of GetReissueMaxIntervalHour.
func (mr *MockIBusinessConfManagerMockRecorder) GetReissueMaxIntervalHour() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetReissueMaxIntervalHour", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetReissueMaxIntervalHour))
}

// GetTemplateId2MsgConf mocks base method.
func (m *MockIBusinessConfManager) GetTemplateId2MsgConf(arg0 string, arg1 uint32) *conf.MsgConf {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTemplateId2MsgConf", arg0, arg1)
	ret0, _ := ret[0].(*conf.MsgConf)
	return ret0
}

// GetTemplateId2MsgConf indicates an expected call of GetTemplateId2MsgConf.
func (mr *MockIBusinessConfManagerMockRecorder) GetTemplateId2MsgConf(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTemplateId2MsgConf", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetTemplateId2MsgConf), arg0, arg1)
}

// GetUserEntryCacheSec mocks base method.
func (m *MockIBusinessConfManager) GetUserEntryCacheSec() uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserEntryCacheSec")
	ret0, _ := ret[0].(uint32)
	return ret0
}

// GetUserEntryCacheSec indicates an expected call of GetUserEntryCacheSec.
func (mr *MockIBusinessConfManagerMockRecorder) GetUserEntryCacheSec() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserEntryCacheSec", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetUserEntryCacheSec))
}

// GetUserGroupList mocks base method.
func (m *MockIBusinessConfManager) GetUserGroupList() []string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserGroupList")
	ret0, _ := ret[0].([]string)
	return ret0
}

// GetUserGroupList indicates an expected call of GetUserGroupList.
func (mr *MockIBusinessConfManagerMockRecorder) GetUserGroupList() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserGroupList", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetUserGroupList))
}

// GetUserWhiteKeyPrefix mocks base method.
func (m *MockIBusinessConfManager) GetUserWhiteKeyPrefix() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserWhiteKeyPrefix")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetUserWhiteKeyPrefix indicates an expected call of GetUserWhiteKeyPrefix.
func (mr *MockIBusinessConfManagerMockRecorder) GetUserWhiteKeyPrefix() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserWhiteKeyPrefix", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetUserWhiteKeyPrefix))
}

// GetWhiteChannelRandomly mocks base method.
func (m *MockIBusinessConfManager) GetWhiteChannelRandomly() uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWhiteChannelRandomly")
	ret0, _ := ret[0].(uint32)
	return ret0
}

// GetWhiteChannelRandomly indicates an expected call of GetWhiteChannelRandomly.
func (mr *MockIBusinessConfManagerMockRecorder) GetWhiteChannelRandomly() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWhiteChannelRandomly", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetWhiteChannelRandomly))
}

// NeedToCheckChannelWithe mocks base method.
func (m *MockIBusinessConfManager) NeedToCheckChannelWithe() bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NeedToCheckChannelWithe")
	ret0, _ := ret[0].(bool)
	return ret0
}

// NeedToCheckChannelWithe indicates an expected call of NeedToCheckChannelWithe.
func (mr *MockIBusinessConfManagerMockRecorder) NeedToCheckChannelWithe() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NeedToCheckChannelWithe", reflect.TypeOf((*MockIBusinessConfManager)(nil).NeedToCheckChannelWithe))
}

// Reload mocks base method.
func (m *MockIBusinessConfManager) Reload(arg0 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Reload", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// Reload indicates an expected call of Reload.
func (mr *MockIBusinessConfManagerMockRecorder) Reload(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Reload", reflect.TypeOf((*MockIBusinessConfManager)(nil).Reload), arg0)
}

// Watch mocks base method.
func (m *MockIBusinessConfManager) Watch(arg0 string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Watch", arg0)
}

// Watch indicates an expected call of Watch.
func (mr *MockIBusinessConfManagerMockRecorder) Watch(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Watch", reflect.TypeOf((*MockIBusinessConfManager)(nil).Watch), arg0)
}
