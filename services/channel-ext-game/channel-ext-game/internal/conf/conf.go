package conf

import (
    mysqlConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/mysql/connect"
    redisConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/redis/connect"
    "golang.52tt.com/pkg/config"
)

// send_type: 0-平台私信， 1-房间公屏， 2-平台私信+房间公屏
const (
    SendTypeAssistantMsg        = 1
    SendTypeRoomMsg             = 2
    SendTypeAssistantAndRoomMsg = 3
)

type StartConfig struct {
    // from config file
    RedisConfig         *redisConnect.RedisConfig `json:"redis"`
    MysqlConfig         *mysqlConnect.MysqlConfig `json:"mysql"`
    MysqlReadOnlyConfig *mysqlConnect.MysqlConfig `json:"readonly_mysql"`
    TBeanKfk            *config.KafkaConfig       `json:"t_bean_kfk"`
}
