package conf

import(
)

type IBusinessConfManager interface {
	CheckIfInBlackChannel(channelId uint32) bool
	Close() 
	GetChannelWhiteKeyPrefix() string
	GetDownloadInfoUrl(appid string) string
	GetDspLpmApiServerHost() string
	GetExtGameVersionSwitch(marketId, clientVersion uint32) (bool,error)
	GetFeiShuRobotUrl() string
	GetGameAppConfByAppid(appid string) (*GameAppConf,bool)
	GetGameAppConfMap() map[string]*GameAppConf
	GetMsgByTemplateId(appId string, templateId uint32) string
	GetMsgFrequencyLimit(appId string, sendType uint32) uint32
	GetReissueMaxIntervalHour() uint32
	GetTemplateId2MsgConf(appId string, templateId uint32) *MsgConf
	GetUserEntryCacheSec() uint32
	GetUserGroupList() []string
	GetUserWhiteKeyPrefix() string
	GetWhiteChannelRandomly() uint32
	NeedToCheckChannelWithe() bool
	Reload(file string) error
	Watch(file string) 
}


type IBusinessConf interface {
	CheckConf() error
	Parse(configFile string) (isChange bool,err error)
}


type IGameAppConf interface {
	GetAwardInfo(RewardId string, RewardType uint32) (*AwardInfo,bool)
}

