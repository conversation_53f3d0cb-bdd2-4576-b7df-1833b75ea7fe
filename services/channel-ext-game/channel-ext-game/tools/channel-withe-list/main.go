package main

import (
    "fmt"
    "io/ioutil"
    redisConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/redis/connect"
    "encoding/json"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    "time"
    "os"
    "strconv"
    "context"
    "github.com/tealeg/xlsx"
    "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"
)

/*
实现房间白名单功能
1. 从excel、csv 文件中读取房间id列表，然后通过redis存储，通过redis查询，然后通过redis的set集合实现白名单功能
其中为了防止形成热key/大key，以channelId mod 10 为维度，分散存储到10个key中

2. 实现随机获取白名单房间的功能（每次获取5个）
*/

var cmder redis.Cmdable

type ServiceConfigT struct {
    RedisConfig    *redisConnect.RedisConfig `json:"redis"`
    FilePath       string                    `json:"file_path"`
    RedisKeyPrefix string                    `json:"redis_key_prefix"`
}

func (sc *ServiceConfigT) Parse(configFile string) (err error) {
    defer func() {
        if e := recover(); e != nil {
            err = fmt.Errorf("Failed to parse config: %v \n", e)
        }
    }()

    data, err := ioutil.ReadFile(configFile)
    if err != nil {
        return err
    }
    err = json.Unmarshal(data, &sc)
    if err != nil {
        return err
    }

    log.Infof("ServiceConfigT:RedisConfig:%+v\n", sc.RedisConfig)
    return
}

func main() {
    sc := &ServiceConfigT{}
    err := sc.Parse("/home/<USER>/channel-ext-game/channel-ext-game.json")
    if err != nil {
        log.Errorf("Parse fail. err:%v", err)
        return
    }

    now := time.Now()
    if len(os.Args) >= 2 {
        ts, err := strconv.ParseUint(os.Args[1], 10, 64)
        if err == nil {
            now = time.Unix(int64(ts), 0)
        } else {
            log.Errorf("ParseUint fail. %s, err:%v", os.Args[1], err)
        }
    }
    log.Infof("now:%v", now)

    ctx := context.Background()

    redisClient, err := redisConnect.NewClient(ctx, sc.RedisConfig)
    if err != nil {
        log.ErrorWithCtx(ctx, "NewServer fail to redisConnect.NewClient, %+v, err:%v", sc.RedisConfig, err)
        return
    }
    cmder = redisClient

    // 读取xlsx/ csv文件
    readFromFile(ctx, sc.FilePath, sc.RedisKeyPrefix)
}

func readFromFile(ctx context.Context, filePath, keyPrefix string) {
    log.Infof("readFromFile filePath:%s", filePath)
    // 读取 Excel 文件
    f, err := xlsx.OpenFile(filePath)
    if err != nil {
        log.Fatalf("readFromFile OpenFile fail: %v", err)
    }

    roomIDs := make([]int, 0)
    for _, row := range f.Sheets[0].Rows {
        if row.Cells != nil && len(row.Cells) > 0 {
            roomID, err := strconv.Atoi(row.Cells[0].String())
            if err != nil {
                log.Errorf("readFromFile Atoi fail: %v", err)
                continue
            }
            roomIDs = append(roomIDs, roomID)
        }
    }

    // 将房间 ID 按 mod 100 分类存储
    channelIdMap := make(map[int][]interface{})
    for _, roomID := range roomIDs {
        mod := roomID % 10
        channelIdMap[mod] = append(channelIdMap[mod], roomID)
    }

    // 将每个分类的房间 ID 存储到 Redis Set 中
    for hash, ids := range channelIdMap {
        if len(ids) == 0 {
            continue
        }
        key := fmt.Sprintf("%s%d", keyPrefix, hash)
        storeRoomIDsInRedis(ctx, ids, key)
    }

    log.Infof("readFromFile count:%d", len(roomIDs))
}

func storeRoomIDsInRedis(ctx context.Context, roomIDs []interface{}, key string) {
    log.InfoWithCtx(ctx, "storeRoomIDsInRedis len(roomIDs):%d keyPrefix:%s", len(roomIDs), key)

    // 查询key 的set的元素个数
    count, err := cmder.SCard(ctx, key).Result()
    if err != nil {
        log.Errorf("storeRoomIDsInRedis SCard fail: err:%v", err)
    }
    log.Infof("storeRoomIDsInRedis Before set key:%s count:%d", key, count)

    // 每次 存储 1000 个房间 ID
    for i := 0; i < len(roomIDs); i += 1000 {
        end := i + 1000
        if end > len(roomIDs) {
            end = len(roomIDs)
        }
        roomIDSlice := roomIDs[i:end]

        // 将房间 ID 存储到 Redis Set 中
        err := cmder.SAdd(ctx, key, roomIDSlice...).Err()
        if err != nil {
            log.Errorf("storeRoomIDsInRedis fail: err:%v", err)
        }
    }

    // 查询key 的set的元素个数
    count, err = cmder.SCard(ctx, key).Result()
    if err != nil {
        log.Errorf("storeRoomIDsInRedis SCard fail: err:%v", err)
    }
    log.Infof("storeRoomIDsInRedis After set key:%s count:%d", key, count)
}
