package server

import (
	"context"
	"errors"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/presentprivilege"
	"golang.52tt.com/services/present-privilege/conf"
	"golang.52tt.com/services/present-privilege/manager"
	context0 "golang.org/x/net/context"
)

type PresentPrivilegeServerServer struct {
	//sc   *conf.ServiceConfigT
	mgr         manager.IManager
	treasureMgr manager.ITreasureManager
}

func NewPresentPrivilegeServerServer(ctx context.Context, cfg config.Configer) (*PresentPrivilegeServerServer, error) {

	sc := &conf.ServiceConfigT{}

	cfgPath := ctx.Value("configfile").(string)
	if cfgPath == "" {
		return nil, errors.New("configfile not exist")
	}
	err := sc.Parse(cfgPath)
	if err != nil {
		return nil, err
	}

	mgr, err := manager.NewMgr(ctx, sc)
	if nil != err {
		return nil, err
	}

	treasureMgr, err := manager.NewTreasureManager(ctx, sc)
	if nil != err {
		return nil, err
	}
	svr := &PresentPrivilegeServerServer{
		mgr:         mgr,
		treasureMgr: treasureMgr,
	}
	return svr, nil
}

func (s *PresentPrivilegeServerServer) AddPrivilege(ctx context.Context, req *pb.AddPrivilegeReq) (out *pb.AddPrivilegeResp, err error) {

	out = &pb.AddPrivilegeResp{}

	//log.DebugWithCtx(ctx, "AddPrivilege req:%+v",req)

	err = s.mgr.AddCondition(ctx, req.GetOrderId(), req.GetUidList(), req.GetPrivilege())

	if nil != err {
		log.ErrorWithCtx(ctx, "AddPrivilege req:%+v err:%v", req, err)
		return out, err
	}

	log.DebugWithCtx(ctx, "AddPrivilege req:%+v out:%v", req, out)

	return out, nil
}

func (s *PresentPrivilegeServerServer) GetPrivilegeList(ctx context.Context, req *pb.GetPrivilegeListReq) (out *pb.GetPrivilegeListResp, err error) {
	out = &pb.GetPrivilegeListResp{}
	privilegeList, count, err := s.mgr.GetConditionsListCache(ctx, req.GetOff(), req.GetLimit())
	if nil != err {
		return out, err
	}

	out.PrivilegeList = privilegeList
	out.Total = count

	log.DebugWithCtx(ctx, "GetPrivilegeList req:%+v out:%v", req, out)

	return out, nil
}

func (s *PresentPrivilegeServerServer) DelPrivilegeList(ctx context.Context, req *pb.DelPrivilegeListReq) (out *pb.DelPrivilegeListResp, err error) {
	out = &pb.DelPrivilegeListResp{}
	s.mgr.DelConditions(ctx, req.GetId())

	log.DebugWithCtx(ctx, "DelPrivilegeList req:%+v", req)

	return out, nil
}

func (s *PresentPrivilegeServerServer) GetPresentPrivilege(ctx context.Context, req *pb.GetPresentPrivilegeReq) (out *pb.GetPresentPrivilegeResp, err error) {

	out = &pb.GetPresentPrivilegeResp{
		MapConditionValue: make(map[string]uint64),
	}

	privilegeList, mapCondVal, err := s.mgr.GetPresentPrivilege(ctx, req.GetUid())
	if nil != err {
		return out, err
	}
	out.PrivilegeList = privilegeList
	for k, v := range mapCondVal {
		out.MapConditionValue[k] = v
	}
	log.DebugWithCtx(ctx, "GetPresentPrivilege req:%+v out:%+v", req, out)

	return out, nil
}

func (s *PresentPrivilegeServerServer) CheckPrivilege(ctx context.Context, req *pb.CheckPrivilegeReq) (out *pb.CheckPrivilegeResp, err error) {
	out = &pb.CheckPrivilegeResp{}

	log.DebugWithCtx(ctx, "CheckPrivilege req:%v", req)

	err = s.mgr.CheckPrivilege(ctx, req.GetPrivilege())
	if nil != err {
		log.ErrorWithCtx(ctx, "CheckPrivilege err:%v", err)
	}

	return out, err
}

func (s *PresentPrivilegeServerServer) AddTreasurePrivilege(ctx context0.Context, req *pb.AddTreasurePrivilegeReq) (*pb.AddTreasurePrivilegeResp, error) {
	return s.treasureMgr.AddTreasurePrivilege(ctx, req)
}

func (s *PresentPrivilegeServerServer) DelTreasurePrivilegeList(ctx context0.Context, req *pb.DelTreasurePrivilegeListReq) (*pb.DelTreasurePrivilegeListResp, error) {
	out := &pb.DelTreasurePrivilegeListResp{}
	err := s.treasureMgr.DelTreasureConditions(ctx, req.GetId())

	return out, err
}

func (s *PresentPrivilegeServerServer) GetTreasurePrivilegeList(ctx context0.Context, req *pb.GetTreasurePrivilegeListReq) (out *pb.GetTreasurePrivilegeListResp, err error) {
	out = &pb.GetTreasurePrivilegeListResp{}
	out.PrivilegeList, out.Total, err = s.treasureMgr.GetTreasureConditions(ctx, req.GetOff(), req.GetLimit())

	return out, err
}

func (s *PresentPrivilegeServerServer) GetTreasurePrivilege(ctx context0.Context, req *pb.GetTreasurePrivilegeReq) (*pb.GetTreasurePrivilegeResp, error) {
	return s.treasureMgr.GetTreasurePrivilege(ctx, req)
}

func (s *PresentPrivilegeServerServer) CheckTreasurePrivilege(c context0.Context, req *pb.CheckTreasurePrivilegeReq) (*pb.CheckTreasurePrivilegeResp, error) {
	return &pb.CheckTreasurePrivilegeResp{}, nil
}

func (s *PresentPrivilegeServerServer) GetConditionVal(ctx context.Context, req *pb.GetConditionValReq) (resp *pb.GetConditionValResp, err error) {
	return s.treasureMgr.GetConditionVal(ctx, req)
}

func (s *PresentPrivilegeServerServer) GetTreasurePrivilegeHistory(ctx context.Context, req *pb.GetTreasurePrivilegeHistoryReq) (*pb.GetTreasurePrivilegeHistoryResp, error) {
	return s.treasureMgr.GetTreasurePrivilegeHistory(ctx, req)
}

func (s *PresentPrivilegeServerServer) AddConditionValForTest(ctx context.Context, req *pb.AddConditionValForTestReq) (*pb.AddConditionValForTestResp, error) {
	err := s.treasureMgr.AddConditionVal(ctx, req.GetTyp(), req.GetUid(), req.GetVal(), req.GetTime())
	return &pb.AddConditionValForTestResp{}, err
}

func (s *PresentPrivilegeServerServer) GetNowPrivilegePeople(ctx context.Context, req *pb.GetNowPrivilegePeopleReq) (*pb.GetNowPrivilegePeopleResp, error) {
	resp := &pb.GetNowPrivilegePeopleResp{}
	resp.UserMap = s.treasureMgr.GetNowPrivilegePeople(ctx)

	return resp, nil
}

func (s *PresentPrivilegeServerServer) ShutDown() {
	log.DebugWithCtx(context.Background(), "ShutDown")
}
