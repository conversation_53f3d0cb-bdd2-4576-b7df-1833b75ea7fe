package conf

import (
	"context"
	"encoding/json"
	"fmt"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	"io/ioutil"
	"sync"
)

type ServiceConfigT struct {
	StoreConfig           *config.MysqlConfig `json:"mysql"`
	RedisConfig           *config.RedisConfig `json:"redis"`
	VoteRedisConfig       *config.RedisConfig `json:"vote_redis"` // 用来记票的redis
	PresentKafkaConfig    *config.KafkaConfig `json:"present_kafka"`
	PresentOldKafkaConfig *config.KafkaConfig `json:"present_old_kafka"`
	LiveKafkaConfig       *config.KafkaConfig `json:"live_kafka"`
	ChannelKafkaConfig    *config.KafkaConfig `json:"channel_kafka"`
	MicKafkaConfig        *config.KafkaConfig `json:"mic_kafka"`
	VoteKafkaConfig       *config.KafkaConfig `json:"vote_kafka"`
	VoteChangeKafkaConfig *config.KafkaConfig `json:"vote_change_kafka"`
	NoLivePkLimit         bool                `json:"no_live_pk_limit"` //
	LivePkWhiteList       []uint32            `json:"live_pk_white_list"`
	PkStatsRecord         []uint32            `json:"pk_stats_record"`
	VoteCntLimit          uint32              `json:"vote_cnt_limit"`
	Mutex                 sync.Mutex
}

const (
	Debug      = "debug"
	Testing    = "testing"
	Staging    = "staging"
	Production = "production"
	cfgFile    = "/data/oss/conf-center/tt/vote-pk.json"
)

var Environment = Debug

func (sc *ServiceConfigT) Parse(ctx context.Context, cfgPath string) (err error) {
	defer func() {
		if e := recover(); e != nil {
			err = fmt.Errorf("Failed to parse config: %v \n", e)
		}
	}()
	data, err := ioutil.ReadFile(cfgPath)
	// 解析配置中的json
	err = json.Unmarshal(data, &sc)
	if err != nil {
		log.Errorf("channel-vote-pk-go : Unmarshal config error, ERR : %v", err)
		return err
	}

	sc.Mutex = sync.Mutex{}
	sc.Load()

	return
}

func (sc *ServiceConfigT) Load() {
	sc.Mutex.Lock()
	defer sc.Mutex.Unlock()

	data, err := ioutil.ReadFile(cfgFile)
	if err != nil {
		return
	}
	err = json.Unmarshal(data, &sc)

	log.Infof("load conf :%+v %v", sc, err)
}
