package event

import (
	"context"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka/subscriber"
	"golang.52tt.com/clients/channel"
	channellivefans "golang.52tt.com/clients/channel-live-fans"
	channellivemission "golang.52tt.com/clients/channel-live-mission"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/protocol/app"
	channelPB "golang.52tt.com/protocol/app/channel"
	channel_live_mission "golang.52tt.com/protocol/services/channel-live-mission"
	"golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafkapresent"
	"golang.52tt.com/services/channel-live-mission/conf"
	"golang.52tt.com/services/channel-live-mission/mongo"
	comKafka "golang.52tt.com/services/common/kafka"
	"time"
)

const topicPresentEvent = "present_event_v2"

type PresentEventSub struct {
	kafkaSub   subscriber.Subscriber
	mDao       *mongo.MongoDao
	fansCli    *channellivefans.Client
	missionCli *channellivemission.Client
	channelCli *channel.Client
}

func NewPresentEventSub(kfkConf *config.KafkaConfig, mgo *mongo.MongoDao) (*PresentEventSub, error) {

	/*
		conf := sarama.NewConfig()
		conf.ClientID = clientId
		conf.Consumer.Offsets.Initial = sarama.OffsetNewest
		conf.Consumer.Return.Errors = true

		kafkaSub, err := event.NewKafkaSub(topicPresentEvent, brokers, groupId, topics, conf)
		if err != nil {
			log.Errorf("Failed to create kafka-subscriber %+v", err)
			return nil, err
		}
	*/

	fansCli, _ := channellivefans.NewClient()
	missionCli, _ := channellivemission.NewClient()
	channelCli := channel.NewClient()

	sub := &PresentEventSub{
		fansCli:    fansCli,
		missionCli: missionCli,
		channelCli: channelCli,
		mDao:       mgo,
	}

	kafkaSub, err := comKafka.NewEventLinkSubscriber(kfkConf, subscriber.ProcessorContextFunc(sub.handlerEvent))
	if err != nil {
		log.Errorf("NewKnightGroupSub Failed to create kafka-subscriber %+v", err)
		return nil, err
	}

	//sub.SetMessageProcessor(sub.handlerEvent)
	sub.kafkaSub = kafkaSub
	return sub, nil
}

func (s *PresentEventSub) Close() {
	s.fansCli.Close()
	s.missionCli.Close()
	s.kafkaSub.Stop()
}

func (s *PresentEventSub) handlerEvent(ctx context.Context, msg *subscriber.ConsumerMessage) (error, bool) {
	log.Debugf("handlerEvent topic:%s", msg.Topic)

	switch msg.Topic {
	case topicPresentEvent:
		return s.handlerPresentEvent(msg)
	}
	return nil, false
}

func (s *PresentEventSub) handlerPresentEvent(msg *subscriber.ConsumerMessage) (error, bool) {
	presentEvent := &kafkapresent.PresentEvent{}
	err := proto.Unmarshal(msg.Value, presentEvent)
	if err != nil {
		log.Errorf(" handlerPresentEvent Failed to proto.Unmarshal err(%v)", err)
		return err, false
	}

	log.Debugf("handlerPresentEvent %+v", presentEvent)

	// 不是语音直播房 不进行处理
	if presentEvent.GetChannelType() != uint32(channelPB.ChannelType_RADIO_LIVE_CHANNEL_TYPE) {
		return nil, true
	}

	uid := presentEvent.GetUid()
	targetUid := presentEvent.GetTargetUid()

	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	channelInfo, err := s.channelCli.GetChannelSimpleInfo(ctx, 0, presentEvent.GetChannelId())
	if err != nil {
		log.Errorf(" handlerPresentEvent Failed to GetChannelSimpleInfo event:%v err(%v)", presentEvent, err)
		return err, true
	}

	// 检查是否是此语音直播房的主播
	if channelInfo.GetBindId() != targetUid {
		log.Debugf("handlerPresentEvent %+v targetUser isn't actor.", presentEvent)
		return nil, true
	}

	totalPrice := presentEvent.GetPrice() * presentEvent.GetItemCount()

	if presentEvent.GetPriceType() != 2 { //非T豆礼物, 只处理粉丝团礼物任务
		if presentEvent.GetTagType() == uint32(app.PresentTagType_PRESENT_TAG_FANS_LEVEL) {
			resp, err := s.fansCli.CheckUserIsFans(ctx, targetUid, uid)
			if err != nil {
				log.Errorf(" handlerPresentEvent red Failed to GetFansInfo event:%v err(%v)", presentEvent, err)
				return err, true
			}
			if resp.GetIsFans() {
				_, err = s.missionCli.HandleFansMission(ctx, uid, presentEvent.GetChannelId(), targetUid, conf.FmissionSendFansGift, 1)
				if err != nil {
					log.ErrorWithCtx(ctx, "handlerPresentEvent red HandleFansMission send Fans gift event:%v, err(%v)", presentEvent, err)
				}
			}
		}
		return nil, false //非T豆礼物，不再往下走了
	}

	// 互动游戏流水不算在主播收入
	if presentEvent.GetTagType() != uint32(app.PresentTagType_PRESENT_TAG_CHANNEL_GAME) {
		// 增加收入记录
		err = s.mDao.IncrActorIncome(targetUid, totalPrice, int64(presentEvent.GetSendTime()))
		if err != nil {
			log.Errorf(" handlerPresentEvent Failed to IncrActorIncome event:%v err(%v)", presentEvent, err)
			//return err, false
		}
	}

	// 非加团礼物
	if presentEvent.GetTagType() != uint32(app.PresentTagType_PRESENT_TAG_ADD_GROUP) {

		// 处理用户任务相关
		_, err = s.missionCli.HandleUserMission(ctx, uid, presentEvent.GetChannelId(), conf.UMissionSendAnyGift, 1)
		if err != nil {
			log.Errorf(" handlerPresentEvent Failed to HandleUserMission event:%v err(%v)", presentEvent, err)
			//return err, false
		}

		if totalPrice >= 10000 {
			_, err = s.missionCli.HandleUserMission(ctx, uid, presentEvent.GetChannelId(), conf.UMissionSendBigGift, 1)
			if err != nil {
				log.Errorf(" handlerPresentEvent Failed to HandleUserMission event:%v err(%v)", presentEvent, err)
				//return err, false
			}
		}

		// 检查是否粉丝主播关系
		resp, err := s.fansCli.CheckUserIsFans(ctx, targetUid, uid)
		if err != nil {
			log.Errorf(" handlerPresentEvent Failed to GetFansInfo event:%v err(%v)", presentEvent, err)
			return err, false
		}

		if resp.GetIsFans() {
			missionResp, err := s.missionCli.HandleFansMission(ctx, uid, presentEvent.GetChannelId(), targetUid, conf.FMissionSendGift, totalPrice)
			if err != nil {
				log.Errorf(" handlerPresentEvent Failed to HandleFansMission event:%v err(%v)", presentEvent, err)
				//return err, false
			}

			log.Debugf("handlerPresentEvent uid:%d, actorUid:%d, status %d", uid, targetUid, missionResp.GetStatus())
			if missionResp.GetStatus() == uint32(channel_live_mission.MissionStatus_Finish) {
				log.Debugf("handlerPresentEvent uid:%d, actorUid:%d, status1 %d", uid, targetUid, missionResp.GetStatus())
				s.fansCli.SetFansMissionFinishStatus(ctx, targetUid, uid)
			}

			if presentEvent.GetTagType() == uint32(app.PresentTagType_PRESENT_TAG_FANS_LEVEL) {
				_, err = s.missionCli.HandleFansMission(ctx, uid, presentEvent.GetChannelId(), targetUid, conf.FmissionSendFansGift, 1)
				if err != nil {
					log.ErrorWithCtx(ctx, "handlerPresentEvent HandleFansMission send Fans gift event:%v, err(%v)", presentEvent, err)
				}
			}
		}

		log.Debugf("handlerPresentEvent uid:%d, actorUid:%d, isFans:%v", uid, targetUid, resp.GetIsFans())
	}

	return nil, true
}
