package event

import (
	"context"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka/subscriber"
	"golang.52tt.com/pkg/config"
	rankV2 "golang.52tt.com/services/rank/channel-online-rank/internal/rank-v2/mgr"
	"golang.52tt.com/services/rank/channel-online-rank/internal/rpc"
	"time"

	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"golang.52tt.com/pkg/log"
	ga "golang.52tt.com/protocol/app/channel"
	kfkpb "golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafkatbean"
)

type TBeanConsumeEventLinkSub struct {
	tBeanEvent subscriber.Subscriber
	handle     func(ctx context.Context, e *kfkpb.TBeanConsumeEvent) error
}

func NewTBeanConsumeEventLinkSub(ctx context.Context, kfkConf *config.KafkaConfig, handle func(ctx context.Context, e *kfkpb.TBeanConsumeEvent) error) (*TBeanConsumeEventLinkSub, error) {
	e := TBeanConsumeEventLinkSub{}
	cfg := kafka.DefaultConfig()
	cfg.ClientID = kfkConf.ClientID
	cfg.Consumer.Offsets.Initial = kafka.OffsetNewest
	cfg.Consumer.Return.Errors = true
	kafkaSub, err := kafka.NewSubscriber(kfkConf.BrokerList(), cfg, subscriber.WithMaxRetryTimes(5))
	if err != nil {
		log.ErrorWithCtx(ctx, "NewTBeanConsumeEventLinkSub NewSubscriber err", err)
		panic(err)
	}
	err = kafkaSub.SubscribeContext(kfkConf.GroupID, kfkConf.TopicList(), subscriber.ProcessorContextFunc(e.handlerTBeanConsumeEvent))
	if err != nil {
		log.ErrorWithCtx(ctx, "NewTBeanConsumeEventLinkSub SubscribeContext err", err)
		panic(err)
	}
	e.tBeanEvent = kafkaSub
	e.handle = handle
	return &e, nil
}

func (s *TBeanConsumeEventLinkSub) Close() {
	_ = s.tBeanEvent.Stop()
}

func (s *TBeanConsumeEventLinkSub) handlerTBeanConsumeEvent(ctx context.Context, msg *subscriber.ConsumerMessage) (error, bool) {

	ctx, cancel := context.WithTimeout(context.Background(), time.Second*5)
	defer cancel()

	tbeanConsumeEv := &kfkpb.TBeanConsumeEvent{}
	err := proto.Unmarshal(msg.Value, tbeanConsumeEv)
	if err != nil {
		log.ErrorWithCtx(ctx, "handlerTBeanConsumeEvent Failed to proto.Unmarshal %+v", err)
		return err, false
	}

	log.InfoWithCtx(ctx, "handlerTBeanConsumeEvent event:%+v", *tbeanConsumeEv)

	if tbeanConsumeEv.GetChannelId() == 0 || tbeanConsumeEv.GetUid() == 0 {
		return nil, false
	}

	sourceType := kfkpb.TBeanConsumeSourceType(tbeanConsumeEv.GetSourceType())
	if !(sourceType == kfkpb.TBeanConsumeSourceType_ENUM_WEREWOLF_BUY_IDENTITY || sourceType == kfkpb.TBeanConsumeSourceType_ENUM_WEREWOLF_BUY_TIME) {
		return nil, false
	}

	cInfo, serr := rpc.ChannelCli.GetChannelSimpleInfo(ctx, 0, tbeanConsumeEv.GetChannelId())
	if nil != serr {
		log.Errorf("handlerTBeanConsumeEvent GetChannelSimpleInfo fail %v, ev=%+v", err, tbeanConsumeEv)
		return nil, false
	}
	rankUid, isDelay, err := rankV2.Inst().GetRankUid(ctx, tbeanConsumeEv.GetUid(), tbeanConsumeEv.GetChannelId(), ga.ChannelType(cInfo.GetChannelType()))
	if nil != err {
		log.Errorf("handlerTBeanConsumeEvent GetRankUid fail %v, ev=%+v", err, tbeanConsumeEv)
		return nil, false
	}
	rankV2.Inst().HandleTbeanEvent(tbeanConsumeEv, rankUid, cInfo.GetChannelType(), isDelay)

	return nil, false
}
