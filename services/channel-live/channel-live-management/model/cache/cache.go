package cache

import (
	"github.com/go-redis/redis"
	"time"
)

type Cache struct {
	redisClient *redis.Client
}

func NewCache(r *redis.Client) (*Cache, error) {
	p := &Cache{redisClient: r}
	return p, nil
}

func (c *Cache) GetPushFlag(pushKey string, expireTs time.Duration) (bool, error) {
	success, err := c.redisClient.SetNX(pushKey, "1", expireTs).Result()
	if err != nil {
		return false, err
	}

	return success, nil
}
