{"server.grpcListen": ":80", "server.adminListen": ":8078", "redis": {"host": "redis-test-tc-bj-tt-cpp-06.database.svc.cluster.local", "port": 6379}, "mysql": {"host": "medalgo-mysql.database.svc.cluster.local", "port": 3306, "database": "appsvr", "charset": "utf8", "user_name": "godman", "password": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "scheme_kafka_config": {"brokers": "hobby-channel-kafka-broker-01.database.svc.cluster.local:9092,hobby-channel-kafka-broker-02.database.svc.cluster.local:9092,hobby-channel-kafka-broker-03.database.svc.cluster.local:9092", "topics": "switch_channel_scheme", "group_id": "time-present", "client_id": "time-present"}}