package internal

import (
    "context"
    "fmt"
    "github.com/golang/protobuf/proto"
    "gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channel_operate_permission_mgr"
    "golang.52tt.com/pkg/log"
    "golang.52tt.com/pkg/protocol/grpc"
    "golang.52tt.com/protocol/app"
    channelPb "golang.52tt.com/protocol/app/channel"
    channel_scheme "golang.52tt.com/protocol/app/channel-scheme"
    "golang.52tt.com/protocol/common/status"
    channel_operate_permission_checker "golang.52tt.com/protocol/services/channel-operate-permission-checker"
    pb "golang.52tt.com/protocol/services/channel-wedding"
    channel_wedding_minigame "golang.52tt.com/protocol/services/channel-wedding-minigame"
    "golang.52tt.com/services/channel-wedding/internal/model/comm"
    "time"
)

const PermissionErrCode = int32(status.ErrRequestParamInvalid)

func fillErrResp(errCode int32, errMsg string) *channel_operate_permission_checker.CheckOperatePermissionResp {
    return &channel_operate_permission_checker.CheckOperatePermissionResp{
        PermissionErrCode: errCode,
        PermissionErrMsg:  errMsg,
    }
}

func (s *Server) CheckOperatePermission(ctx context.Context, req *channel_operate_permission_checker.CheckOperatePermissionReq) (resp *channel_operate_permission_checker.CheckOperatePermissionResp, err error) {
    resp = &channel_operate_permission_checker.CheckOperatePermissionResp{}
    if req.GetSchemeDetailType() != uint32(channel_scheme.SchemeDetailType_SCHEME_DETAIL_TYPE_WEDDING) {
        return resp, nil
    }

    cid := req.GetCid()
    weddingInfo, err := s.weddingProcess.GetChannelWeddingInfo(ctx, req.GetOptUid(), cid)
    if err != nil {
        log.ErrorWithCtx(ctx, "CheckOperatePermission fail to GetChannelWeddingInfo failed, req:%+v, err:%v",
            req, err)
        return resp, err
    }

    defer func() {
        log.DebugWithCtx(ctx, "CheckOperatePermission, req:%+v, resp:%+v, err:%v", req, resp, err)
    }()

    switch channel_operate_permission_mgr.ChannelOperate(req.GetOpt()) {
    case channel_operate_permission_mgr.ChannelOperate_CHANNEL_OPERATE_TAKE_MIC,
        channel_operate_permission_mgr.ChannelOperate_TAKE_CHANGE_MIC:
        // 抱麦权限检查
        return s.checkTakeUserHoldMicPermission(ctx, weddingInfo, req)

    case channel_operate_permission_mgr.ChannelOperate_HOLD_MIC,
        channel_operate_permission_mgr.ChannelOperate_CHANGE_MIC:
        // 上麦权限检查
        return s.checkUserHoldMicPermission(ctx, weddingInfo, req)

    case channel_operate_permission_mgr.ChannelOperate_QUEUE_MIC:
        // 排麦权限检查
        return s.CheckQueueMicPermission(ctx, weddingInfo, req)

    case channel_operate_permission_mgr.ChannelOperate_KICK_MIC:
        // 踢麦权限检查
        return s.checkUserKickMicPermission(ctx, weddingInfo, req)

    case channel_operate_permission_mgr.ChannelOperate_MUTE:
        // 禁言权限检查
        return s.checkMuteUserPermission(ctx, weddingInfo, req)

    case channel_operate_permission_mgr.ChannelOperate_SET_MIC_STATUS:
        // 锁麦权限检查
        return s.checkUserLockMicPermission(ctx, weddingInfo, req)

    case channel_operate_permission_mgr.ChannelOperate_SET_CUR_BACKGROUND:
        // 设置房间背景权限检查
        return s.checkSetBackgroundPermission(ctx, weddingInfo, req)

    case channel_operate_permission_mgr.ChannelOperate_CHANNEL_OPERATE_ENTER:
        // 进入房间权限检查
        return s.checkUserEnterChannelPermission(ctx, req)
    }

    return resp, nil
}

func (s *Server) checkUserEnterChannelPermission(c context.Context, req *channel_operate_permission_checker.CheckOperatePermissionReq) (*channel_operate_permission_checker.CheckOperatePermissionResp, error) {
    resp := &channel_operate_permission_checker.CheckOperatePermissionResp{}
    groupId := s.bc.GetWeddingLpmGroupId()
    if groupId == 0 {
        return resp, nil
    }

    uid := req.GetOptUid()
    entityIdStr := fmt.Sprint(uid)

    // 设置短超时时间
    ctx, cancel := grpc.NewContextWithInfoTimeout(c, 300*time.Millisecond)
    defer cancel()

    groupMatchMap, err := s.lpmProxy.GetGroupMatchMap(ctx, []string{entityIdStr}, []int32{groupId})
    if err != nil {
        log.ErrorWithCtx(ctx, "checkUserEnterChannelPermission fail to GetGroupMatchMap failed, req:%+v, err:%v",
            req, err)

        // lpm接口报错时，拦截进房
        return fillErrResp(PermissionErrCode, "全新玩法内测中，敬请期待～"), nil
    }

    if groupMatchMap[entityIdStr] {
        // 特定人群包用户可以进婚礼房间
        return resp, nil
    }

    return fillErrResp(PermissionErrCode, "全新玩法内测中，敬请期待～"), nil
}

func (s *Server) checkUserHoldMicPermission(ctx context.Context, weddingInfo *pb.WeddingInfo, req *channel_operate_permission_checker.CheckOperatePermissionReq) (*channel_operate_permission_checker.CheckOperatePermissionResp, error) {
    resp := &channel_operate_permission_checker.CheckOperatePermissionResp{}
    micInfo := &channel_operate_permission_checker.MicOperateExtendInfo{}
    err := proto.Unmarshal(req.GetExtendPbData(), micInfo)
    if err != nil {
        log.ErrorWithCtx(ctx, "checkUserHoldMicPermission fail to Unmarshal failed, req:%+v, err:%v",
            req, err)
        return resp, err
    }

    if micInfo.GetMicId() == comm.HostMicId {
        return s.checkUserHoldHostMicPermission(ctx, weddingInfo, req)
    }

    return s.checkUserHoldCommMicPermission(ctx, weddingInfo, req, false)
}

func (s *Server) checkTakeUserHoldMicPermission(ctx context.Context, weddingInfo *pb.WeddingInfo, req *channel_operate_permission_checker.CheckOperatePermissionReq) (*channel_operate_permission_checker.CheckOperatePermissionResp, error) {
    resp := &channel_operate_permission_checker.CheckOperatePermissionResp{}

    micInfo := &channel_operate_permission_checker.MicOperateExtendInfo{}
    err := proto.Unmarshal(req.GetExtendPbData(), micInfo)
    if err != nil {
        log.ErrorWithCtx(ctx, "checkTakeUserHoldMicPermission fail to Unmarshal failed, req:%+v, err:%v",
            req, err)
        return resp, err
    }

    if micInfo.GetMicId() == comm.HostMicId {
        return s.checkUserHoldHostMicPermission(ctx, weddingInfo, req)
    } else {
        return s.checkUserHoldCommMicPermission(ctx, weddingInfo, req, true)
    }
}

func (s *Server) checkUserVirtualImageInuse(ctx context.Context, uid uint32) (bool, error) {
    uid2List, err := s.acLayer.GetUserVirtualImageInuseMap(ctx, []uint32{uid})
    if err != nil {
        log.ErrorWithCtx(ctx, "checkUserVirtualImageInuse fail to GetUserVirtualImageInuseMap failed, uid:%d, err:%v",
            uid, err)
        return false, err
    }

    if len(uid2List[uid]) == 0 {
        return false, nil
    }

    /*
       display, err := s.acLayer.CheckUserVirtualImageMicDisplay(ctx, uid)
       if err != nil {
           log.ErrorWithCtx(ctx, "checkUserVirtualImageInuse fail to CheckUserVirtualImageMicDisplay failed, uid:%d, err:%v",
               uid, err)
           return false, err
       }

       if !display {
           return false, nil
       }
    */

    return true, nil
}

// checkUserHoldHostMicPermission 主持麦权限检查
func (s *Server) checkUserHoldHostMicPermission(ctx context.Context, weddingInfo *pb.WeddingInfo, req *channel_operate_permission_checker.CheckOperatePermissionReq) (*channel_operate_permission_checker.CheckOperatePermissionResp, error) {
    resp := &channel_operate_permission_checker.CheckOperatePermissionResp{}
    micUid := req.GetTargetUid()

    if req.GetChannelType() == uint32(channelPb.ChannelType_TEMP_KH_CHANNEL_TYPE) {
        // 免费婚礼房不支持上主持人麦位
        return fillErrResp(PermissionErrCode, "本场婚礼的主持为系统司仪小T哦~"), nil
    }

    user, err := s.acLayer.GetUserProfile(ctx, micUid)
    if err != nil {
        log.ErrorWithCtx(ctx, "checkUserHoldHostMicPermission fail to proto.Unmarshal failed, req:%+v, err:%v",
            req, err)
        return resp, err
    }

    if user.GetPrivilege().GetType() == uint32(app.EUserPrivilegeType_ENUM_USER_PRIVILEGE_UKW) {
        return fillErrResp(PermissionErrCode, "需要关闭神秘人且穿戴虚拟形象才能上麦哦~"), nil
    }

    if s.isChannelGamePlayer(ctx, req.GetCid(), micUid, 0) {
        return fillErrResp(PermissionErrCode, "游戏中不允许换麦位哦"), nil
    }

    if weddingInfo.GetStageInfo().GetCurrStage() != 0 {
        if micUid == weddingInfo.GetBride().GetUid() {
            return fillErrResp(PermissionErrCode, "新娘，婚礼中暂不支持站在嘉宾位哦~"), nil
        }
        if micUid == weddingInfo.GetGroom().GetUid() {
            return fillErrResp(PermissionErrCode, "新郎，婚礼中暂不支持站在嘉宾位哦~"), nil
        }

        // 婚礼过程中，其他情况不限制上主持麦
        return resp, nil
    }

    // 检查是否穿戴虚拟形象
    inuseVirtualImage, err := s.checkUserVirtualImageInuse(ctx, micUid)
    if err != nil {
        log.ErrorWithCtx(ctx, "checkUserHoldHostMicPermission fail to checkUserVirtualImageInuse failed, req:%V, err:%v",
            req, err)
        return resp, err
    }

    if !inuseVirtualImage {
        return fillErrResp(PermissionErrCode, "为虚拟形象穿戴服装才能上麦哦~"), nil
    }

    return resp, nil
}

// checkUserHoldCommMicPermission 普通麦权限检查
func (s *Server) checkUserHoldCommMicPermission(ctx context.Context, weddingInfo *pb.WeddingInfo, req *channel_operate_permission_checker.CheckOperatePermissionReq,
    hostOperate bool) (*channel_operate_permission_checker.CheckOperatePermissionResp, error) {
    resp := &channel_operate_permission_checker.CheckOperatePermissionResp{}
    micUid := req.GetTargetUid()

    user, err := s.acLayer.GetUserProfile(ctx, micUid)
    if err != nil {
        log.ErrorWithCtx(ctx, "checkUserHoldCommMicPermission fail to proto.Unmarshal failed, req:%+v, err:%v",
            req, err)
        return resp, err
    }

    if user.GetPrivilege().GetType() == uint32(app.EUserPrivilegeType_ENUM_USER_PRIVILEGE_UKW) {
        return fillErrResp(PermissionErrCode, "需要关闭神秘人且穿戴虚拟形象才能上麦哦~"), nil
    }

    needCheckVI := true // 是否需要检查虚拟形象，默认需要
    if weddingInfo.GetStageInfo().GetCurrStage() != 0 &&
        (micUid == weddingInfo.GetBride().GetUid() || micUid == weddingInfo.GetGroom().GetUid()) {
        // 新人在婚礼过程中，不检查虚拟形象
        needCheckVI = false
    }

    if needCheckVI {
        // 其他情况，检查是否穿戴虚拟形象
        inuseVirtualImage, err := s.checkUserVirtualImageInuse(ctx, micUid)
        if err != nil {
            log.ErrorWithCtx(ctx, "checkUserHoldCommMicPermission fail to checkUserVirtualImageInuse failed, req:%V, err:%v",
                req, err)
            return resp, err
        }

        if !inuseVirtualImage {
            return fillErrResp(PermissionErrCode, "为虚拟形象穿戴服装才能上麦哦~"), nil
        }
    }

    if weddingInfo.GetStageInfo().GetCurrStage() == 0 {
        // 未开始婚礼, 不用检查
        return resp, nil
    }

    micInfo := &channel_operate_permission_checker.MicOperateExtendInfo{}
    err = proto.Unmarshal(req.GetExtendPbData(), micInfo)
    if err != nil {
        log.ErrorWithCtx(ctx, "checkUserHoldCommMicPermission fail to proto.Unmarshal failed, req:%+v, err:%v",
            req, err)
        return resp, err
    }

    micId := micInfo.GetMicId()
    log.DebugWithCtx(ctx, "checkUserHoldCommMicPermission, req:%+v, micInfo:%+v", req, micInfo)

    isPlayer, checkResult := s.checkChannelGamePlayer(ctx, req.GetCid(), micUid, micId)
    if !checkResult {
        errMsg := "游戏中不可以上其他麦位哦"
        if isPlayer {
            if hostOperate {
                errMsg = "游戏中不允许换麦位哦"
            }
        } else {
            errMsg = "游戏中暂不支持本操作~"
        }
        log.ErrorWithCtx(ctx, "checkUserHoldCommMicPermission fail to checkChannelGamePlayer failed, req:%+v, isPlayer:%v, checkResult:%v", req, isPlayer, checkResult)
        return fillErrResp(PermissionErrCode, errMsg), nil
    }

    if micUid == weddingInfo.GetBride().GetUid() {
        // 新娘只能上新娘麦位
        if micId == comm.BrideMicId {
            return resp, nil
        }

        return fillErrResp(PermissionErrCode, "新娘，婚礼中暂不支持站在嘉宾位哦~"), nil

    } else if micUid == weddingInfo.GetGroom().GetUid() {
        // 新郎只能上新郎麦位
        if micId == comm.GroomMicId {
            return resp, nil
        }

        return fillErrResp(PermissionErrCode, "新郎，婚礼中暂不支持站在嘉宾位哦~"), nil

    } else {
        if micId == comm.GroomMicId || micId == comm.BrideMicId {
            return fillErrResp(PermissionErrCode, "婚礼中只有新人才可上此麦位哦~"), nil
        }
    }

    return resp, nil
}

// checkUserKickMicPermission 踢麦权限检查
func (s *Server) checkUserKickMicPermission(ctx context.Context, weddingInfo *pb.WeddingInfo, req *channel_operate_permission_checker.CheckOperatePermissionReq) (*channel_operate_permission_checker.CheckOperatePermissionResp, error) {
    resp := &channel_operate_permission_checker.CheckOperatePermissionResp{}
    if weddingInfo.GetStageInfo().GetCurrStage() == 0 {
        // 未开始婚礼, 不用检查
        return resp, nil
    }

    micUid := req.GetTargetUid()
    /*if micUid == weddingInfo.GetBride().GetUid() || micUid == weddingInfo.GetGroom().GetUid() {
        return fillErrResp(PermissionErrCode, "不支持在婚礼过程中将新人抱下麦哦"), nil
    }*/

    if s.isChannelGamePlayer(ctx, req.GetCid(), micUid, 0) {
        return fillErrResp(PermissionErrCode, "游戏中玩家不允许被抱下麦"), nil
    }

    return resp, nil
}

// checkMuteUserPermission 禁言权限检查
func (s *Server) checkMuteUserPermission(ctx context.Context, weddingInfo *pb.WeddingInfo, req *channel_operate_permission_checker.CheckOperatePermissionReq) (*channel_operate_permission_checker.CheckOperatePermissionResp, error) {
    resp := &channel_operate_permission_checker.CheckOperatePermissionResp{}
    if weddingInfo.GetStageInfo().GetCurrStage() == 0 {
        // 未开始婚礼, 不用检查
        return resp, nil
    }

    micUid := req.GetTargetUid()
    /*if micUid == weddingInfo.GetBride().GetUid() || micUid == weddingInfo.GetGroom().GetUid() {
        return &channel_operate_permission_checker.CheckOperatePermissionResp{
            PermissionErrCode: int32(status.ErrRequestParamInvalid),
            PermissionErrMsg:  "新人不能被禁言~",
        }, nil
    }*/

    if s.isChannelGamePlayer(ctx, req.GetCid(), micUid, 0) {
        return fillErrResp(PermissionErrCode, "不允许禁言游戏中的用户哦"), nil
    }

    return resp, nil
}

// CheckQueueMicPermission 排麦申请权限检查
func (s *Server) CheckQueueMicPermission(ctx context.Context, weddingInfo *pb.WeddingInfo, req *channel_operate_permission_checker.CheckOperatePermissionReq) (*channel_operate_permission_checker.CheckOperatePermissionResp, error) {
    resp := &channel_operate_permission_checker.CheckOperatePermissionResp{}
    //if weddingInfo.GetStageInfo().GetCurrStage() == 0 {
    //    // 未开始婚礼, 不用检查
    //    return resp, nil
    //}

    micUid := req.GetOptUid()

    user, err := s.acLayer.GetUserProfile(ctx, micUid)
    if err != nil {
        log.ErrorWithCtx(ctx, "CheckQueueMicPermission fail to proto.Unmarshal failed, req:%+v, err:%v",
            req, err)
        return resp, err
    }

    if user.GetPrivilege().GetType() == uint32(app.EUserPrivilegeType_ENUM_USER_PRIVILEGE_UKW) {
        return fillErrResp(PermissionErrCode, "需要关闭神秘人且穿戴虚拟形象才能上麦哦~"), nil
    }

    // 检查是否穿戴虚拟形象
    inuseVirtualImage, err := s.checkUserVirtualImageInuse(ctx, micUid)
    if err != nil {
        log.ErrorWithCtx(ctx, "CheckQueueMicPermission fail to checkUserVirtualImageInuse failed, req:%V, err:%v",
            req, err)
        return resp, err
    }

    if !inuseVirtualImage {
        return fillErrResp(PermissionErrCode, "为虚拟形象穿戴服装才能上麦哦~"), nil
    }

    return resp, nil
}

/*
   enum EMicrSpaceState {
       MIC_SPACE_NOMAL = 1;   // 正常
       MIC_SPACE_DISABLE = 2; // 锁麦位 此时麦位不能用
       MIC_SPACE_MUTE = 3;    // 麦位禁言 可以上麦但是不能说话
   }
*/
// 锁麦权限检查
func (s *Server) checkUserLockMicPermission(ctx context.Context, weddingInfo *pb.WeddingInfo, req *channel_operate_permission_checker.CheckOperatePermissionReq) (*channel_operate_permission_checker.CheckOperatePermissionResp, error) {
    resp := &channel_operate_permission_checker.CheckOperatePermissionResp{}
    if weddingInfo.GetStageInfo().GetCurrStage() == 0 {
        // 未开始婚礼, 不用检查
        return resp, nil
    }

    micInfo := &channel_operate_permission_checker.MicOperateExtendInfo{}
    err := proto.Unmarshal(req.GetExtendPbData(), micInfo)
    if err != nil {
        log.ErrorWithCtx(ctx, "checkUserHoldCommMicPermission fail to proto.Unmarshal failed, req:%+v, err:%v",
            req, err)
        return resp, err
    }

    if micInfo.GetMicState() != uint32(channelPb.MicrSpace_MIC_SPACE_DISABLE) {
        // 非锁麦操作不用限制
        return resp, nil
    }

    micId := micInfo.GetMicId()

    /*if weddingInfo.GetStageInfo().GetCurrStage() > 0 {
        if micId == comm.HostMicId || micId == comm.BrideMicId || micId == comm.GroomMicId {
            return fillErrResp(PermissionErrCode, "司仪、新人麦位不允许锁麦哦~"), nil
        }
    }*/

    //if micId > comm.GameMicIdBegin{
    //    return &channel_operate_permission_checker.CheckOperatePermissionResp{
    //        PermissionErrCode: int32(status.ErrRequestParamInvalid),
    //        PermissionErrMsg:  "不允许锁游戏麦哦~",
    //    }, nil
    //}

    if s.isChannelGamePlayer(ctx, req.GetCid(), req.GetTargetUid(), micId) {
        return fillErrResp(PermissionErrCode, "不允许锁游戏中的用户哦~"), nil
    }

    return resp, nil
}

func (s *Server) checkSetBackgroundPermission(ctx context.Context, weddingInfo *pb.WeddingInfo, req *channel_operate_permission_checker.CheckOperatePermissionReq) (*channel_operate_permission_checker.CheckOperatePermissionResp, error) {
    // 婚礼房暂不支持切换房间背景
    return fillErrResp(PermissionErrCode, "婚礼房暂不支持切换房间背景"), nil
}

func (s *Server) getCurChannelGamePlayers(ctx context.Context, channelId uint32) (uid2MicMap map[uint32]uint32, mic2UidMap map[uint32]uint32, err error) {
    uid2MicMap = make(map[uint32]uint32)
    mic2UidMap = make(map[uint32]uint32)
    playersResp, err := s.weddingMiniGameCli.GetCurChairGamePlayers(ctx, &channel_wedding_minigame.GetCurChairGamePlayersRequest{
        ChannelId: channelId,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "getCurChannelMicInfo fail to GetChairGamePlayers failed, channelId:%v,  err:%v",
            channelId, err)
        return
    }

    for _, v := range playersResp.GetPlayers() {
        uid2MicMap[v.GetUid()] = v.GetMicId()
        mic2UidMap[v.GetMicId()] = v.GetUid()
    }

    log.DebugWithCtx(ctx, "getCurChannelMicInfo uid2MicMap:%v, mic2UidMap:%v", uid2MicMap, mic2UidMap)
    return
}

func (s *Server) isChannelGamePlayer(ctx context.Context, channelId uint32, uid, micId uint32) bool {
    uid2MicMap, mic2UidMap, err := s.getCurChannelGamePlayers(ctx, channelId)
    if err != nil {
        return false
    }

    _, uidOk := uid2MicMap[uid]
    _, micOk := mic2UidMap[micId]

    return uidOk || micOk
}

func (s *Server) checkChannelGamePlayer(ctx context.Context, channelId uint32, uid, targetMic uint32) (isPlayer bool, checkResult bool) {
    uid2MicMap, mic2UidMap, err := s.getCurChannelGamePlayers(ctx, channelId)
    if err != nil {
        return
    }

    // 游戏玩家不能换麦
    if micId, ok := uid2MicMap[uid]; ok {
        if targetMic != micId {
            return true, false
        }

        return true, true

    } else {

        // 非游戏玩家不能上游戏麦
        if player, ok := mic2UidMap[targetMic]; ok {
            if uid != player {
                return false, false
            }
        } else {
            if targetMic >= comm.GameMicIdBegin {
                return false, false
            }
        }

        return false, true
    }
}
