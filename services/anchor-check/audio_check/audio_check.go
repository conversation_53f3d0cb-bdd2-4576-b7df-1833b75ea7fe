package audio_check

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net"
	"net/http"
	"strings"
	"time"

	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/web/metrics"
)

// https://q9jvw0u5f5.feishu.cn/docx/T74pdpKNEogvBRxY3yAcXcVenqP

const (
	Enroll_Reg   = 1 // 1：注册
	Enroll_Check = 0 // 0：识别

	LABEL_INVALID  = "INVALID"
	LABEL_CHEATING = "CHEATING"
	LABEL_PASS     = "PASS"
)

var (
	DefaultHTTPClient = &http.Client{ // 这里默认不设置 http.Timeout, 在外部使用ctx设置超时
		Transport: &http.Transport{
			Proxy: http.ProxyFromEnvironment,
			DialContext: (&net.Dialer{
				Timeout:   3 * time.Second, // tcp建立连接超时
				KeepAlive: 30 * time.Second,
			}).DialContext,
			MaxIdleConns:        100,
			MaxIdleConnsPerHost: 100,
			IdleConnTimeout:     time.Duration(90) * time.Second,
		},
	}
)

type AudioCheckReq struct {
	Mp3Path string `json:"mp3_file"`
	Uid     string `json:"uid"`
	Enroll  uint32 `json:"enroll"`
}

type ExtraInfo struct {
	VociePercentageTimeRange []TimeRange `json:"vocie_percentage_time_range,omitempty"`
	CheatInfo                *CheatInfo  `json:"live_audio_cheat_info,omitempty"`
}

type TimeRange struct {
	BeginTs uint32 `json:"begin_ts"`
	EndTs   uint32 `json:"end_ts"`
}

type CheatInfo struct {
	Uid     uint32  `json:"uid"`
	Score   float64 `json:"score"`
	Mp3Path string  `json:"mp3_path"`
}

type CheatInfoRaw struct {
	Uid     string `json:"uid"`
	Score   string `json:"score"`
	Mp3Path string `json:"mp3_path"`
}

type AudioCheckResp struct {
	Status int    `json:"status"`
	Msg    string `json:"msg"`
	Result struct {
		Lablel                   string         `json:"label"`
		SentenceLength           int            `json:"sentence_length"`
		VociePercentage          string         `json:"vocie_percentage"`
		VociePercentageTimeRange []TimeRange    `json:"vocie_percentage_time_range"`
		CheatInfo                []CheatInfoRaw `json:"cheat_info"`
	} `json:"result"`
}

type LiveAudioCheckResp struct {
	Status int    `json:"status"`
	Msg    string `json:"msg"`
	Result struct {
		Lablel    string `json:"label"`
		CheatInfo struct {
			Score   string `json:"score"`
			Mp3Path string `json:"mp3_path"`
		} `json:"cheat_info"`
	} `json:"result"`
}

func Register(ctx context.Context, url, mp3Path string, uid uint32) (
	*AudioCheckResp, error) {
	req := &AudioCheckReq{
		Mp3Path: mp3Path,
		Uid:     fmt.Sprintf("%d", uid),
		Enroll:  Enroll_Reg,
	}
	resp := &AudioCheckResp{}
	bin, _ := json.Marshal(req)
	//url := c.getUrl(audioType)

	body, err := httpPost(ctx, url, bin)
	if err != nil {
		log.Errorf("Register fail %v, req=%+v", err, req)
		return resp, err
	}

	err = json.Unmarshal(body, resp)
	if err != nil {
		log.Errorf("Register json.Unmarshal fail %v, req=%+v, body=%s", err, req, body)
		return resp, err
	}

	log.Infof("Register done. url=%q req=%s, body=%s resp=%+v", url, string(bin), body, resp)
	return resp, nil
}

func GetCheckAudioResult(ctx context.Context, url, mp3Path string, uid uint32) (*AudioCheckResp, error) {
	req := &AudioCheckReq{
		Mp3Path: mp3Path,
		Uid:     fmt.Sprintf("%d", uid),
		Enroll:  Enroll_Check,
	}
	resp := &AudioCheckResp{}
	bin, _ := json.Marshal(req)

	body, err := httpPost(ctx, url, bin)
	if err != nil {
		log.Errorf("GetCheckAudioResult fail %v, url=%s, req=%+v, bin=%s", err, url, req, bin)
		return resp, err
	}

	err = json.Unmarshal(body, resp)
	if err != nil {
		log.Errorf("GetCheckAudioResult json.Unmarshal fail %v, url=%s, req=%s, body=%s", err, url, string(bin), body)
		return resp, err
	}

	log.Infof("GetCheckAudioResult done. url=%q req=%s, body=%s resp=%+v", url, string(bin), body, resp)
	return resp, nil
}

func GetLiveAudioResult(ctx context.Context, url, mp3Path string, uid uint32) (*LiveAudioCheckResp, error) {
	req := &AudioCheckReq{
		Mp3Path: mp3Path,
		Uid:     fmt.Sprintf("%d", uid),
		Enroll:  Enroll_Check,
	}
	resp := &LiveAudioCheckResp{}
	bin, _ := json.Marshal(req)

	body, err := httpPost(ctx, url, bin)
	if err != nil {
		log.Errorf("GetLiveAudioResult fail %v, url=%s req=%s", err, url, string(bin))
		return resp, err
	}

	err = json.Unmarshal(body, resp)
	if err != nil {
		log.Errorf("GetLiveAudioResult json.Unmarshal fail %v, url=%s, req=%s, body=%s", err, url, string(bin), body)
		return resp, err
	}

	log.Infof("GetLiveAudioResult done. url=%q req=%s, body=%s resp=%+v", url, string(bin), body, resp)
	return resp, nil
}

func GetLiveAudioSourcekUrl(urlPrefix string, httpTimeOutSec uint32, uid uint32, startDate, endDate string) (list []string, err error) {
	list = []string{}
	url := fmt.Sprintf("%s&start_date=%s&end_date=%s&uid=%d", urlPrefix, startDate, endDate, uid)

	httpReq, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(httpTimeOutSec)*time.Second)
	defer cancel()

	resp, err := DefaultHTTPClient.Do(httpReq.WithContext(ctx))
	if err != nil {
		return
	}

	defer resp.Body.Close()
	if resp.StatusCode != 200 {
		log.Errorf("GetLiveAudioSourcekUrl fail resp %s, url=%q", resp.Status, url)
		err = fmt.Errorf("http resp %s", resp.Status)
		return
	}
	body, _ := ioutil.ReadAll(resp.Body)

	info := &struct {
		Data struct {
			Data []struct {
				Audiourl string `json:"audiourl"`
			} `json:"data"`
		} `json:"data"`
	}{}

	err = json.Unmarshal(body, info)
	if err != nil {
		log.Errorf("GetLiveAudioSourcekUrl json.Unmarshal fail %s, body=%s", err, body)
		return
	}

	if len(info.Data.Data) < 1 {
		return
	}

	// ['https://hw-bj-zt-meida-audio-prod.obs.cn-north-4.myhuaweicloud.com/mp3/swin-309660093-P8-1680002932472-1680018716.mp3']
	str := strings.ReplaceAll(info.Data.Data[0].Audiourl, "[", "")
	str = strings.ReplaceAll(str, "]", "")
	str = strings.ReplaceAll(str, "'", "")
	str = strings.ReplaceAll(str, " ", "")

	list = strings.Split(str, ",")
	if len(list) == 1 && list[0] == "" {
		list = []string{}
	}

	log.Infof("GetLiveAudioSourcekUrl uid=%d, url=%q, total=%d, list=%+v", uid, url, len(list), list)
	return
}

func RemoveCheckAudioUrl(ctx context.Context, requestUrl string, uid uint32) (*AudioCheckResp, error) {
	bin := fmt.Sprintf(`{"uid":"%d"}`, uid)
	resp := &AudioCheckResp{}

	body, err := httpPost(ctx, requestUrl, []byte(bin))
	if err != nil {
		log.Errorf("RemoveCheckAudioUrl fail %v, url=%s req=%s", err, requestUrl, string(bin))
		return resp, err
	}

	err = json.Unmarshal(body, resp)
	if err != nil {
		log.Errorf("RemoveCheckAudioUrl json.Unmarshal fail %v, url=%s, req=%s, body=%s", err, requestUrl, string(bin), body)
		return resp, err
	}

	log.Infof("RemoveCheckAudioUrl done. url=%q req=%q, body=%s resp=%+v", requestUrl, string(bin), body, resp)
	return resp, nil
}

func httpPost(ctx context.Context, url string, reqBody []byte) (body []byte, err error) {

	httpReq, err := http.NewRequest("POST", url, bytes.NewReader(reqBody))
	if err != nil {
		return
	}

	beginTime := time.Now()
	defer func() {
		metrics.ReportMetrics( // 上报
			httpReq.Method,        // HTTP 方法
			httpReq.URL.Path,      // 路径名
			http.StatusOK,         // HTTP 状态码
			time.Since(beginTime), // 耗时
		)
	}()

	//httpReq.Header.Add("Connection", "close")
	resp, err := DefaultHTTPClient.Do(httpReq.WithContext(ctx))
	if err != nil {
		return
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		err = fmt.Errorf("url: %q, http code: %d", url, resp.StatusCode)
		return
	}
	body, err = ioutil.ReadAll(resp.Body)
	if err != nil {
		return
	}
	return
}
