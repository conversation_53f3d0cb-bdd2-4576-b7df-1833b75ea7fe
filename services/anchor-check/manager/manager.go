package manager

import (
	"context"
	"fmt"
	"time"

	"github.com/go-redis/redis"
	_ "github.com/go-sql-driver/mysql"
	"github.com/jinzhu/gorm"
	anchorlevel "golang.52tt.com/clients/anchor-level"
	apicenter "golang.52tt.com/clients/apicenter/apiserver"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/timer"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/anchor-check"
	apiPB "golang.52tt.com/protocol/services/apicenter/apiserver"
	"golang.52tt.com/services/anchor-check/cache"
	"golang.52tt.com/services/anchor-check/conf"
	"golang.52tt.com/services/anchor-check/model"
	"golang.52tt.com/services/anchor-check/utils"
	"golang.52tt.com/services/anchor-check/zego"
	"golang.52tt.com/services/notify"
)

const (
	logCacheDelAnchorCheckWhiteErr = "cache.DelAnchorCheckWhite err=%+v"
	logSendTTMsgErr                = "SendTTMsg err=%+v"
	upgradeUrl                     = "https://activity.52tt.com/yy-act/2022/zhuboruz22112/"
	//mainUrl                        = "tt://m.52tt.com/home?main_pos=0&vice_pos=1"
)

type AnchorCheckMgr struct {
	cache             cache.IRedisCache
	store             model.IStore
	zegoClient        zego.IZegoClient
	apiClient         apicenter.IClient
	anchorLevelClient anchorlevel.IClient
	timerD            *timer.Timer
	conf.ISDyConfigHandler
}

func NewAnchorCheckMgr(cfg config.Configer, redisCli *redis.Client) (*AnchorCheckMgr, error) {
	mysqlConfig := new(config.MysqlConfig)
	mysqlConfig.Read(cfg, "mysql")
	mysqlAddr := mysqlConfig.Description()
	db, err := gorm.Open("mysql", mysqlAddr)
	if err != nil {
		log.Fatalln(err)
		return nil, err
	}
	db.LogMode(true)
	db.DB().SetMaxIdleConns(mysqlConfig.MaxIdleConns)
	db.DB().SetMaxOpenConns(mysqlConfig.MaxOpenConns)
	store := model.NewStore(db)

	c := cache.NewCache(redisCli)
	urlPre := cfg.String("zego::url_pre")
	appId, _ := cfg.Int("zego::app_id")
	serverSecret := cfg.String("zego::server_secret")
	region := cfg.String("zego::region")
	bucket := cfg.String("zego::bucket")
	accessKeyId := cfg.String("zego::access_key_id")
	accessKeySecret := cfg.String("zego::access_key_secret")
	isTest, _ := cfg.Bool("zego::is_test")
	maxRecordTime, _ := cfg.Int("zego::max_record_time")
	if maxRecordTime == 0 {
		maxRecordTime = 600
	}
	log.Debugf("%s, %d, %s, %s, %s, %s, %s, %d", urlPre, appId, serverSecret, region, bucket, accessKeyId, accessKeySecret, maxRecordTime)
	zegoClient := &zego.ZegoClient{
		UrlPre:          urlPre,
		AppId:           uint32(appId),
		ServerSecret:    serverSecret,
		Region:          region,
		Bucket:          bucket,
		AccessKeyId:     accessKeyId,
		AccessKeySecret: accessKeySecret,
		IsTest:          isTest,
		MaxRecordTime:   maxRecordTime,
	}
	apiClient := apicenter.NewClient()
	anchorLevelClient, _ := anchorlevel.NewClient()
	dyconfig := conf.NewConfigHandler(conf.DyconfigPath)
	if err := dyconfig.Start(); err != nil {
		log.Errorf("dyconfig.Start() fail %v", err)
		return nil, err
	}
	m := &AnchorCheckMgr{
		store: store, cache: c, zegoClient: zegoClient, apiClient: apiClient,
		anchorLevelClient: anchorLevelClient, ISDyConfigHandler: dyconfig,
	}
	return m, nil
}

func (mgr *AnchorCheckMgr) GetStore() model.IStore {
	return mgr.store
}

func (mgr *AnchorCheckMgr) Init() error {
	err := mgr.store.Init()
	if err != nil {
		log.Errorf("AnchorCheckMgr Init fail %v", err)
		return err
	}
	return nil
}

func (mgr *AnchorCheckMgr) ShutDown() {
	mgr.store.Close()
	mgr.cache.Close()
	mgr.timerD.Stop()
}

func (mgr *AnchorCheckMgr) SendTTMsg(ctx context.Context, uid uint32, content, hlight, url string) error {
	log.InfoWithCtx(ctx, "SendTTMsg to uid=%d %q %q %q", uid, content, hlight, url)
	msg := new(apiPB.ImMsg)
	msg.ImType = &apiPB.ImType{
		SenderType:   uint32(apiPB.IM_SENDER_TYPE_IM_SENDER_NORMAL),
		ReceiverType: uint32(apiPB.IM_RECEIVER_TYPE_IM_RECEIVER_USER),
	}

	msg.FromUid = 10000 // TT语音助手

	msg.ToIdList = []uint32{uid}
	msg.ImContent = &apiPB.ImContent{}

	if url == "" {
		msg.ImContent.TextNormal = &apiPB.ImTextNormal{
			Content: content,
		}
		msg.ImType.ContentType = uint32(apiPB.IM_CONTENT_TYPE_IM_CONTENT_TEXT)
	} else {
		msg.ImContent.TextHlUrl = &apiPB.ImTextWithHighlightUrl{
			Content:    content,
			Hightlight: hlight,
			Url:        url,
		}
		msg.ImType.ContentType = uint32(apiPB.IM_CONTENT_TYPE_IM_CONTENT_TEXT_WITH_HL_URL)
	}

	msg.Platform = apiPB.Platform_UNSPECIFIED
	msg.AppPlatform = "all"

	err := mgr.apiClient.SendImMsg(context.Background(), uid, protocol.TT, []*apiPB.ImMsg{msg}, true)

	if err != nil {
		log.ErrorWithCtx(ctx, "SendTTMsg get fail uid:%d err:%v", uid, err)
	}

	totalPre, ok := notify.NotifySync(uid, notify.ImMsgV2)

	log.InfoWithCtx(ctx, "SendTTMsg uid:%d totalPre:%v ok:%v", uid, totalPre, ok)
	return nil
}

func (mgr *AnchorCheckMgr) StartRecord(ctx context.Context, uid uint32, roomId string, streamId string, sourceType uint32) (string, error) {
	taskId, err := mgr.zegoClient.StartRecord(ctx, roomId, streamId)
	if err != nil {
		log.ErrorWithCtx(ctx, "zegoClient.StartRecord err=%+v", err)
		return "", protocol.NewServerError(status.ErrAnchorCheckStartRecordErr)
	}

	err = mgr.store.InsertAnchorCheck(taskId, roomId, streamId, uid, sourceType)
	if err != nil {
		log.ErrorWithCtx(ctx, "store.InsertAnchorCheck err=%+v", err)
		return "", protocol.NewServerError(status.ErrAnchorCheckDbErr)
	}
	return taskId, nil
}

func (mgr *AnchorCheckMgr) StopRecord(ctx context.Context, uid uint32, taskId string, submit bool) error {
	data := mgr.store.GetAnchorCheckData(taskId)
	if data.Uid != uid {
		return protocol.NewServerError(status.ErrAnchorCheckStatusErr)
	}

	if data.Status != model.AnchorCheckStatusInit {
		return protocol.NewServerError(status.ErrAnchorCheckStatusErr)
	}

	err := mgr.zegoClient.StopRecord(ctx, taskId)
	if err != nil {
		log.ErrorWithCtx(ctx, "zegoClient.StopRecord err=%+v", err)
		return protocol.NewServerError(status.ErrAnchorCheckStopRecordErr)
	}

	acStatus := model.AnchorCheckStatusCancel
	if submit {
		acStatus = model.AnchorCheckStatusSubmit
	}
	err = mgr.store.UpdateStatus(taskId, uint8(acStatus))
	if err != nil {
		log.ErrorWithCtx(ctx, "store.UpdateStatus err=%+v", err)
		return protocol.NewServerError(status.ErrAnchorCheckDbErr)
	}

	return nil
}

func (mgr *AnchorCheckMgr) CheckInWhite(ctx context.Context, uid uint32) (*pb.WhiteData, error) {
	out := &pb.WhiteData{}

	info, exist, err := mgr.cache.GetAnchorCheckWhiteV2(uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckInWhite GetAnchorCheckWhiteV2 fail %v, uid=%d", err, uid)
		return out, err
	}
	log.DebugfWithCtx(ctx, "CheckInWhite uid=%d, createTime=%d, sourceType=%d, exist=%v",
		uid, info.CreateTime, info.SourceType, exist)

	if !exist {
		data, err := mgr.store.GetFirstAnchorCheckWhiteData(uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "CheckInWhite GetFirstAnchorCheckWhiteData fail %v, uid=%d", err, uid)
			return out, err
		}

		err = mgr.cache.SetAnchorCheckWhiteV2(uid, int64(data.CreateTime), pb.AnchorCheckSourceType(data.SourceType))
		if err != nil {
			log.ErrorWithCtx(ctx, "cache.SetAnchorCheckWhite err=%+v", err)
			return out, err
		}
		info = &pb.WhiteDetailInfo{Uid: uid, CreateTime: data.CreateTime, SourceType: data.SourceType}
		log.DebugfWithCtx(ctx, "CheckInWhite reload uid=%d, createTime=%d, sourceType=%d", uid, info.CreateTime, info.SourceType)
	}

	remainTs := uint32(0)
	if info.CreateTime > 0 {
		expireTime := utils.GetAnchorCheckExpireTime(int64(info.CreateTime))
		remainTs = uint32(expireTime - time.Now().Unix())
	}

	out.ExpireTime = remainTs
	out.SourceType = info.SourceType
	log.DebugfWithCtx(ctx, "CheckInWhite uid=%d, remainTs=%d, SourceType=%d", uid, remainTs, out.SourceType)
	return out, nil

}

func (mgr *AnchorCheckMgr) SubmitWhite(ctx context.Context, uid uint32) error {

	err := mgr.store.UpdateAllAnchorCheckWhiteStatus(uid, model.AnchorCheckWhiteStatusSubmit)
	if err != nil {
		log.ErrorWithCtx(ctx, "store.UpdateAnchorCheckWhiteStatus err=%+v", err)
		return err
	}
	err = mgr.cache.DelAnchorCheckWhiteV2(uid)
	if err != nil {
		log.ErrorWithCtx(ctx, logCacheDelAnchorCheckWhiteErr, err)
		return err
	}
	return nil
}

func (mgr *AnchorCheckMgr) SetWhite(ctx context.Context, uid uint32, operator string) error {
	err := mgr.cache.DelAnchorCheckWhiteV2(uid)
	if err != nil {
		log.ErrorWithCtx(ctx, logCacheDelAnchorCheckWhiteErr, err)
		return err
	}
	err = mgr.store.InsertAnchorCheckWhite(uid, operator)
	if err != nil {
		log.ErrorWithCtx(ctx, "store.InsertAnchorCheckWhite err=%+v", err)
		return err
	}

	err = mgr.cache.DelAnchorCheckWhiteV2(uid)
	if err != nil {
		log.ErrorWithCtx(ctx, logCacheDelAnchorCheckWhiteErr, err)
		return err
	}

	msg := mgr.ISDyConfigHandler.GetAnchorCheckImMsg().CheckBegin
	err = mgr.SendTTMsg(ctx, uid, msg.Content, msg.Highlight, msg.Url)
	if err != nil {
		log.ErrorWithCtx(ctx, logSendTTMsgErr, err)
	}

	return nil
}

func (mgr *AnchorCheckMgr) GetMaxRecordTime() int {
	return mgr.zegoClient.GetMaxRecordTime()
}

func (mgr *AnchorCheckMgr) GetCheckList(req *pb.AnchorCheckGetCheckListReq) ([]*model.AnchorCheck, uint32) {

	list := mgr.store.GetAnchorCheckList(req)
	count := mgr.store.GetAnchorCheckCount(req)
	return list, count
}

func (mgr *AnchorCheckMgr) SetCheckData(ctx context.Context, info *model.AnchorCheck) error {

	var taskId, remark, operator string = info.TaskId, info.Remark, info.Operator
	var cyScore, sxScore, yzScore, kcScore uint32 = info.CyScore, info.SxScore, info.YzScore, info.KcScore

	log.InfoWithCtx(ctx, "SetCheckData taskId=%s, cyScore=%d, sxScore=%d, yzScore=%d, kcScore=%d",
		taskId, cyScore, sxScore, yzScore, kcScore)
	sum := cyScore + sxScore + yzScore + kcScore

	checkStatus := model.AnchorCheckStatusPass
	if cyScore < 4 || sum < 7 {
		checkStatus = model.AnchorCheckStatusNotPass
	}
	level := utils.GetLevel(cyScore, sxScore, yzScore, kcScore)

	orgData := mgr.store.GetAnchorCheckData(taskId)
	if orgData.Uid == 0 {
		log.ErrorWithCtx(ctx, "task id=%s not exist", taskId)
		return protocol.NewServerError(status.ErrAnchorCheckStatusErr)
	}
	log.InfoWithCtx(ctx, "SetCheckData GetAnchorCheckData=%+v", orgData)

	log.InfoWithCtx(ctx, "SetCheckData uid=%d level=%s", orgData.Uid, level)

	switch level {
	case "S", "A", "B", "C":
		{
			_, err := mgr.anchorLevelClient.SetAnchorCheckPass(ctx, orgData.Uid, level)
			if err != nil {
				log.ErrorWithCtx(ctx, "SetAnchorCheckPass err=%v", err)
				return err
			}
			log.InfoWithCtx(ctx, "SetCheckData SetAnchorCheckPass uid=%d level=%s", orgData.Uid, level)
		}
	default:
	}

	scoreInfo := &model.AnchorCheck{
		CyScore:   cyScore,
		SxScore:   sxScore,
		YzScore:   yzScore,
		KcScore:   kcScore,
		Remark:    remark,
		Status:    uint8(checkStatus),
		Operator:  operator,
		CheckType: uint32(pb.CheckType_CheckType_Manual), // 人工评分
	}

	log.InfoWithCtx(ctx, "SetCheckData store.UpdateScores %+v", scoreInfo)
	err := mgr.store.UpdateScores(taskId, scoreInfo)
	if err != nil {
		return err
	}

	if orgData.SourceType == uint32(pb.AnchorCheckSourceType_E_CHECK_SOURCE_UPGRADE) {
		_ = mgr.SendTTMsg(ctx, orgData.Uid, fmt.Sprintf("亲爱的达人：您本次的考核升级结果为%s级（才艺分：%d分，声线分：%d分，音质分：%d分，"+
			"控场分：%d分，总分：%d分），具体评分规则：%s", level, cyScore, sxScore, yzScore, kcScore, sum, upgradeUrl),
			upgradeUrl, upgradeUrl)
		log.InfoWithCtx(ctx, "SetCheckData taskId=%q, uid=%d is upgrade.", orgData.TaskId, orgData.Uid)
		return nil
	}

	cTime := time.Unix(int64(orgData.CreateTime), 0)
	switch level {
	case "S", "A", "B", "C", "D":
		if orgData.Status < model.AnchorCheckStatusPass { // 首次评分
			msg := mgr.ISDyConfigHandler.GetAnchorCheckImMsg().FirstSetScore[level]
			content := fmt.Sprintf(msg.Content, cTime.Month(), cTime.Day(), level, cyScore, sxScore, yzScore, kcScore, sum)
			err = mgr.SendTTMsg(ctx, orgData.Uid, content, msg.Highlight, msg.Url)
		} else { // 修改评分
			msg := mgr.ISDyConfigHandler.GetAnchorCheckImMsg().ChangeSetScore[level]
			content := fmt.Sprintf(msg.Content, cTime.Month(), cTime.Day(), level, cyScore, sxScore, yzScore, kcScore, sum)
			err = mgr.SendTTMsg(ctx, orgData.Uid, content, msg.Highlight, msg.Url)
		}
	case "E":
		mgr.handleLevelE(orgData, info)
	default:
		log.ErrorWithCtx(ctx, "level no match, level=%s", level)
		return protocol.NewServerError(status.ErrAnchorCheckStatusErr)
	}
	if err != nil {
		log.ErrorWithCtx(ctx, logSendTTMsgErr, err)
	}

	return nil
}

func (mgr *AnchorCheckMgr) handleLevelE(orgData, info *model.AnchorCheck) {

	var cyScore, sxScore, yzScore, kcScore uint32 = info.CyScore, info.SxScore, info.YzScore, info.KcScore
	sum := cyScore + sxScore + yzScore + kcScore

	ctx, cancel := context.WithTimeout(context.Background(), time.Second*3)
	defer cancel()
	var err error
	level := "E"
	cTime := time.Unix(int64(orgData.CreateTime), 0)
	if sum > 0 {
		if orgData.Status < model.AnchorCheckStatusPass { // 首次评分
			msg := mgr.ISDyConfigHandler.GetAnchorCheckImMsg().FirstSetScore[level]
			content := fmt.Sprintf(msg.Content, cTime.Month(), cTime.Day(), level, cyScore, sxScore, yzScore, kcScore, sum)
			err = mgr.SendTTMsg(ctx, orgData.Uid, content, msg.Highlight, msg.Url)
		} else { // 修改评分
			msg := mgr.ISDyConfigHandler.GetAnchorCheckImMsg().ChangeSetScore[level]
			content := fmt.Sprintf(msg.Content, cTime.Month(), cTime.Day(), level, cyScore, sxScore, yzScore, kcScore, sum)
			err = mgr.SendTTMsg(ctx, orgData.Uid, content, msg.Highlight, msg.Url)
		}
	} else {
		levelE0 := "E0"
		if orgData.Status < model.AnchorCheckStatusPass { // 首次评分
			msg := mgr.ISDyConfigHandler.GetAnchorCheckImMsg().FirstSetScore[levelE0]
			content := fmt.Sprintf(msg.Content, cTime.Month(), cTime.Day(), level, cyScore, sxScore, yzScore, kcScore, sum)
			err = mgr.SendTTMsg(ctx, orgData.Uid, content, msg.Highlight, msg.Url)
		} else { // 修改评分
			msg := mgr.ISDyConfigHandler.GetAnchorCheckImMsg().ChangeSetScore[levelE0]
			content := fmt.Sprintf(msg.Content, cTime.Month(), cTime.Day(), level, cyScore, sxScore, yzScore, kcScore, sum)
			err = mgr.SendTTMsg(ctx, orgData.Uid, content, msg.Highlight, msg.Url)
		}
	}
	if err != nil {
		log.Errorf("handleLevelE fail %v", err)
	}
}

func (mgr *AnchorCheckMgr) SyncZegoMp3() {
	list := mgr.store.GetAllSubmitNoFileUrlList(time.Now().Unix())
	log.Infof("SyncZegoMp3 GetAllSubmitNoFileUrlList total=%d", len(list))

	for _, elem := range list {
		recordStatus, err := mgr.zegoClient.DescribeRecordStatus(elem.TaskId)
		if err != nil {
			log.Errorf("SyncZegoMp3 zego.DescribeRecordStatus fail %v", err)
			continue
		}
		log.Debugf("SyncZegoMp3 DescribeRecordStatus TaskId=%s, resp=%+v", elem.TaskId, recordStatus)

		if recordStatus.Data.Status == zego.ZegoRecordOver {

			beginTime := recordStatus.Data.RecordBeginTimestamp / 1000
			endTime := recordStatus.Data.RecordEndTimestamp / 1000

			mgr.UpdateAllRecordFiles(&recordStatus.Data.RecordFiles, elem.TaskId, uint32(beginTime), uint32(endTime))
		} else {
			log.Infof("SyncZegoMp3 task_id=%s status=%d", elem.TaskId, recordStatus.Data.Status)
		}
	}
}

func (mgr *AnchorCheckMgr) UpdateAllRecordFiles(list *[]zego.RecordFileData, taskId string, beginTime uint32, endTime uint32) {
	for _, fileData := range *list {
		if fileData.Status == zego.ZegoUploadSuccess {
			err := mgr.store.UpdateFileUrl(taskId, fileData.FileUrl, beginTime, endTime)
			if err != nil {
				log.Errorf("UpdateAllRecordFiles store.UpdateFileUrl fail %v, taskId=%q", err, taskId)
			}
		} else {
			log.Infof("UpdateAllRecordFiles task_id=%s file status=%d", taskId, fileData.Status)
		}
	}
}

// 发送白名单过期的提醒
func (mgr *AnchorCheckMgr) SendWhiteExpireMsg(ctx context.Context) {
	tNow := time.Now()
	if tNow.Hour() == 0 {
		success, err := mgr.cache.SetSendWhiteExpireMsgDate(tNow.Format("20060102"))
		if err != nil {
			log.Errorln(err)
			return
		}
		if !success {
			log.Infoln("SendWhiteExpireMsg not success ")
			return
		}

		expireTime := utils.GetCreateTimeAtExpireTime(tNow.Unix())
		list := mgr.store.GetExpireListWithTimeRange(uint32(expireTime-86400), uint32(expireTime))
		msg := mgr.ISDyConfigHandler.GetAnchorCheckImMsg().CheckNoUpload
		log.Infof("SendWhiteExpireMsg total=%d msg=%s", len(list), msg)
		for _, elem := range list {
			err = mgr.SendTTMsg(ctx, elem.Uid, msg.Content, msg.Highlight, msg.Url)
			if err != nil {
				log.ErrorWithCtx(ctx, logSendTTMsgErr, err)
			}
			err = mgr.cache.DelAnchorCheckWhiteV2(elem.Uid)
			if err != nil {
				log.ErrorWithCtx(ctx, logCacheDelAnchorCheckWhiteErr, err)
			}
		}
	}
}

// 发送即将过期提醒，每日8点判断16个小时后要到期的
func (mgr *AnchorCheckMgr) SendWillExpireMsg(ctx context.Context) {
	tNow := time.Now()
	if tNow.Hour() == 8 {
		success, err := mgr.cache.SetSendWhiteWillExpireMsgDate(tNow.Format("20060102"))
		if err != nil {
			log.Errorln(err)
			return
		}
		if !success {
			log.Infoln("SendWillExpireMsg not success ")
			return
		}

		expireTime := utils.GetCreateTimeAtExpireTime(tNow.Unix() + 16*3600)
		list := mgr.store.GetExpireListWithTimeRange(uint32(expireTime-86400), uint32(expireTime))
		msg := mgr.ISDyConfigHandler.GetAnchorCheckImMsg().CheckLess16h
		log.Infof("SendWillExpireMsg total=%d msg=%s", len(list), msg)
		for _, elem := range list {
			err = mgr.SendTTMsg(ctx, elem.Uid, msg.Content, msg.Highlight, msg.Url)
			if err != nil {
				log.Errorln(err)
			}
		}
	}
}

func (mgr *AnchorCheckMgr) GetExpireListWithTimeRange(beginTime uint32, endTime uint32) []uint32 {
	uidList := []uint32{}
	beginTime = uint32(utils.GetCreateTimeAtExpireTime(int64(beginTime - 1))) //要把时间减1，可以覆盖到3天前1天
	endTime = uint32(utils.GetCreateTimeAtExpireTime(int64(endTime)))
	list := mgr.store.GetExpireListWithTimeRange(beginTime, endTime)
	for _, elem := range list {
		uidList = append(uidList, elem.Uid)
	}
	return uidList
}

func (mgr *AnchorCheckMgr) GetLastCreateScore(uid uint32) *model.AnchorCheck {
	return mgr.store.GetLastCreateScore(uid)
}

func (mgr *AnchorCheckMgr) GetWhiteList(ctx context.Context, offset uint32, limit uint32, uid uint32, beginTime uint32,
	endTime uint32, status int32) ([]model.AnchorCheckWhite, uint32) {
	list := mgr.store.GetAllWhiteWithTimeRange(beginTime, endTime, offset, limit, uid, status)
	count := mgr.store.GetAllWhiteCountWithTimeRange(beginTime, endTime, uid, status)
	return list, count
}

// 查看历史考核结果
func (m *AnchorCheckMgr) GetAnchorCheckHistory(ctx context.Context, in *pb.GetAnchorCheckHistoryReq) (*pb.GetAnchorCheckHistoryResp, error) {
	out := &pb.GetAnchorCheckHistoryResp{}
	uid := in.Uid
	list, err := m.store.GetAnchorCheckHistory(uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAnchorCheckHistory fail %v uid %d", err, uid)
		return out, err
	}
	for _, elem := range list {
		out.List = append(out.List, &pb.AnchorCheckData{
			TaskId:              elem.TaskId,
			Uid:                 elem.Uid,
			FileUrl:             elem.FileUrl,
			CyScore:             elem.CyScore,
			SxScore:             elem.SxScore,
			YzScore:             elem.YzScore,
			KcScore:             elem.KcScore,
			Status:              uint32(elem.Status),
			BeginTime:           elem.BeginTime,
			EndTime:             elem.EndTime,
			Remark:              elem.Remark,
			CreateTime:          elem.CreateTime,
			Operator:            elem.Operator,
			Level:               utils.GetLevel(elem.CyScore, elem.SxScore, elem.YzScore, elem.KcScore),
			UpdateTime:          elem.UpdateTime,
			SentenceLength:      elem.SentenceLength,
			VociePercentage:     elem.VociePercentage,
			AudioCheckScore:     elem.AudioCheckScore,
			LiveAudioCheckScore: elem.LiveAudioCheckScore,
			CheckType:           elem.CheckType,
			SourceType:          elem.SourceType,
		})
	}
	log.DebugWithCtx(ctx, "GetAnchorCheckHistory end uid=%d out=%+v", uid, out)
	return out, nil
}

// 获取最近一次考核升级申请时间
func (m *AnchorCheckMgr) GetLastAnchorCheckUpgrade(ctx context.Context, in *pb.UidReq) (*pb.GetLastAnchorCheckUpgradeResp, error) {
	uid := in.Uid
	out := &pb.GetLastAnchorCheckUpgradeResp{}

	createTime, err := m.store.GetLastAnchorCheckUpgrade(nil, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetLastAnchorCheckUpgrade fail %v, uid=%d", err, uid)
		return out, nil
	}
	out.ApplyTs = createTime
	log.DebugfWithCtx(ctx, "GetLastAnchorCheckUpgrade uid=%d, ApplyTs=%d",
		uid, out.ApplyTs)
	return out, nil
}

// 申请考核升级
func (m *AnchorCheckMgr) ApplyAnchorCheckUpgrade(ctx context.Context, in *pb.UidReq) (*pb.ApplyAnchorCheckUpgradeReqResp, error) {
	uid := in.Uid
	out := &pb.ApplyAnchorCheckUpgradeReqResp{}

	remainDay, err := m.store.InsertAnchorCheckUpgrade(ctx, uid, time.Now())
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplyAnchorCheckUpgrade InsertAnchorCheckUpgrade fail %v, uid=%d", err, uid)
		return out, err
	}

	out.RemainDay = remainDay
	log.InfoWithCtx(ctx, "ApplyAnchorCheckUpgrade uid=%d, remainDay=%d", uid, out.RemainDay)

	// “你已成功申请主播升级考核，需在即日起三天内开播，并在直播间内上传直播音频以完成考核 立即开播>>”（跳转至娱乐tab）
	if remainDay == 0 {
		msg := m.ISDyConfigHandler.GetAnchorCheckImMsg().CheckUpgrade
		m.SendTTMsg(ctx, uid, msg.Content, msg.Highlight, msg.Url)
	}

	err = m.cache.DelAnchorCheckWhiteV2(uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplyAnchorCheckUpgrade DelAnchorCheckWhiteV2 fail %v, uid=%d", err, in.Uid)
	}

	return out, nil
}
