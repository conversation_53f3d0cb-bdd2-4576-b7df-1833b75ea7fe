package manager

import (
	"context"
	"errors"
	backpackBase "golang.52tt.com/clients/backpack-base"
	numeric_go "golang.52tt.com/clients/numeric-go"
	numericPB "golang.52tt.com/protocol/services/numeric-go"
	"reflect"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"golang.52tt.com/clients/account"
	apicenter "golang.52tt.com/clients/apicenter/apiserver"
	backpackSender "golang.52tt.com/clients/backpack-sender"
	"golang.52tt.com/clients/channel"
	channelmsgexpress "golang.52tt.com/clients/channel-msg-express"
	"golang.52tt.com/clients/darkserver"
	"golang.52tt.com/clients/nobility"
	PushNotification "golang.52tt.com/clients/push-notification/v2"
	"golang.52tt.com/clients/seqgen/v2"
	unifiedPay "golang.52tt.com/clients/unified_pay"
	userPresent "golang.52tt.com/clients/userpresent"
	ukw "golang.52tt.com/clients/you-know-who"
	"golang.52tt.com/pkg/protocol"
	channelredpacketlogic "golang.52tt.com/protocol/app/channel-red-packet-logic"
	accountPB "golang.52tt.com/protocol/services/accountsvr"
	backpackPB "golang.52tt.com/protocol/services/backpack-base"
	channel_red_packet "golang.52tt.com/protocol/services/channel-red-packet"
	pb "golang.52tt.com/protocol/services/channel-red-packet"
	channelsvr "golang.52tt.com/protocol/services/channelsvr"
	"golang.52tt.com/protocol/services/youknowwho"
	"golang.52tt.com/services/channel-red-packet/channel-red-packet/cache"
	"golang.52tt.com/services/channel-red-packet/channel-red-packet/conf"
	"golang.52tt.com/services/channel-red-packet/channel-red-packet/mysql"

	darkserverpb "golang.52tt.com/protocol/services/darkserver"
)

func TestRedPacketMgr_HandleRPSettleAward(t *testing.T) {
	genMgrMock(t)

	order := &mysql.RedPacketOrder{RedPacketId: 1}
	cfg := &pb.RedPacketConf{
		BeginTime: uint32(time.Now().Unix() - 86400),
		EndTime:   uint32(time.Now().Unix() + 86400),
	}
	rpConf := &mysql.RedPacketConf{
		TotalPrice:  10000,
		GiftConfOpt: `{"GiftList":[{"GiftId":1271,"Name":"1","BgId":1006,"Price":10000}]}`,
	}
	/*
		list := []uint32{1, 2}
		BlackUserListResp := &darkserverpb.BatchGetBlackUserListResp{
			UidBehaviorList: []*darkserverpb.UserBehaviorInfo{
				{
					Uid:    1,
					Status: true,
				},
				{
					Uid:    2,
					Status: true,
				},
			},
		}
		businessId := uint32(1)
		limit := uint32(11)
		NumericResp := &numericPB.AddUserNumericResp{}
		user := &accountPB.UserResp{}
		usermp := map[uint32]*accountPB.UserResp{}
		cTimeStr := "!"
		uwkInfos := map[uint32]*youknowwho.UKWPersonInfo{}
		channelInfo := &channelsvr.ChannelSimpleInfo{}
		PackageItemCfg := &backpackPB.GetPackageItemCfgResp{}
		award_secret_key := "ffkywmynfehcdbqq"

	*/

	pbList := []*channel_red_packet.RedPacketInfo{}

	gomock.InOrder(
		mockStore.EXPECT().GetRedPacketOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(order, true, nil),
		mockCache.EXPECT().GetRedPacketConfById(gomock.Any()).Return(cfg, true, nil),
		mockStore.EXPECT().GetNotDelRedPacketConfById(gomock.Any()).Return(rpConf, false, nil),
		mockCache.EXPECT().DelChannelRedPacketList(gomock.Any()).Return(nil),
		mockCache.EXPECT().GetChannelRedPacketList(gomock.Any()).Return(pbList, true, nil),
		mockCache.EXPECT().DelUKWCacheInfo(gomock.Any(), gomock.Any()).Return(nil),

		/*
			mockCache.EXPECT().GetRedPacketClickCntRankList(gomock.Any(), gomock.Any(), gomock.Any()).Return(list, nil),
			mockdarkCli.EXPECT().BatchBlackUserListCheck(gomock.Any(), gomock.Any()).Return(BlackUserListResp, nil),
			mockBusinessConf.EXPECT().GetRPAwardBusinessInfo().Return(businessId, award_secret_key),
			mockBusinessConf.EXPECT().GetRPInvalidAwardUserLimit().Return(limit),
			mockStore.EXPECT().Transaction(gomock.Any(), gomock.Any()).Return(nil),
			mocknumericCli.EXPECT().AddUserNumeric(gomock.Any(), gomock.Any()).Return(NumericResp, nil),
			mockapiCenterCli.EXPECT().NotifyGrowInfoSync(gomock.Any(), gomock.Any()).Return(nil),
			mocknobilityCli.EXPECT().AddUserNobilityValue(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil),
			mockStore.EXPECT().UpdateRedPacketOrderStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(true, nil),
			mockaccountCli.EXPECT().GetUser(gomock.Any(), gomock.Any()).Return(user, nil),
			mockBusinessConf.EXPECT().GetRPPayAppId().Return("1"),

			mockunifiedPayCli.EXPECT().UnfreezeAndConsume(gomock.Any(), gomock.Any()).Return(cTimeStr, "1", nil),

			mockStore.EXPECT().UpdateRedPacketTbeanTime(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil),
			mockStore.EXPECT().UpdateRedPacketAwardTbeanTime(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil),
			mockaccountCli.EXPECT().GetUsersMap(gomock.Any(), gomock.Any()).Return(usermp, nil),

			mockukwCli.EXPECT().BatchGetUKWPersonInfoOnly(gomock.Any(), gomock.Any()).Return(uwkInfos, nil),
			mockCache.EXPECT().GetUKWCacheInfo(gomock.Any(), gomock.Any()).Return(nil, nil),
			mockchannelCli.EXPECT().GetChannelSimpleInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(channelInfo, nil),
			mockbackpackCli.EXPECT().GetPackageItemCfg(gomock.Any(), gomock.Any(), gomock.Any()).Return(PackageItemCfg, nil),
			mockBusinessConf.EXPECT().GetRPAwardBusinessInfo().Return(businessId, award_secret_key),

			// 异步
			mockBusinessConf.EXPECT().GetRPAwardBusinessInfo().Return(businessId, award_secret_key),
			mockbpSendCli.EXPECT().SendBackpackWithRiskControl(gomock.Any(), gomock.Any()).Return(nil, nil),
			mockStore.EXPECT().UpdateRedPacketAwardDone(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil),

			mockCache.EXPECT().DelChannelRedPacketList(gomock.Any()).Return(nil),
			mockCache.EXPECT().GetChannelRedPacketList(gomock.Any()).Return(pbList, true, nil),
			mockCache.EXPECT().DelUKWCacheInfo(gomock.Any(), gomock.Any()).Return(nil),


				mockStore.EXPECT().UpdateRedPacketAwardDone(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(true, nil),
				mockCache.EXPECT().DelChannelRedPacketList(gomock.Any()).Return(nil),

				mockStore.EXPECT().UpdateRedPacketAwardDone(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(true, nil),

				mockCache.EXPECT().DelChannelRedPacketList(gomock.Any()).Return(nil),
				mockCache.EXPECT().GetChannelRedPacketList(gomock.Any()).Return(pbList, true, nil),

				mockapiCenterCli.EXPECT().SendImMsg(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
				mockCache.EXPECT().DelUKWCacheInfo(gomock.Any(), gomock.Any()).Return(nil),
		*/
	)

	type fields struct {
		shutDown      chan interface{}
		sc            conf.IServiceConfigT
		BusinessConf  conf.IBusinessConfManager
		Cache         cache.IRedPacketCache
		Store         mysql.IStore
		apiCenterCli  apicenter.IClient
		seqCli        seqgen.IClient
		pushCli       PushNotification.IClient
		accountCli    account.IClient
		channelMsgCli channelmsgexpress.IClient
		presentCli    userPresent.IClient
		backpackCli   backpackBase.IClient
		bpSendCli     backpackSender.IClient
		numericCli    numeric_go.IClient
		nobilityCli   nobility.IClient
		darkCli       darkserver.IClient
		unifiedPayCli unifiedPay.IClient
		ukwCli        ukw.IClient
		channelCli    channel.IClient
	}
	type args struct {
		ctx     context.Context
		orderId string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "HandleRPSettleAward",
			fields: fields{
				shutDown:     make(chan interface{}),
				sc:           mockSc,
				BusinessConf: mockBusinessConf,
				Cache:        mockCache,
				Store:        mockStore,
				apiCenterCli: mockapiCenterCli,
				//seqCli:        mockseqCli,
				pushCli:       mockpushCli,
				accountCli:    mockaccountCli,
				channelMsgCli: mockchannelMsgCli,
				presentCli:    mockpresentCli,
				backpackCli:   mockbackpackCli,
				bpSendCli:     mockbpSendCli,
				numericCli:    mocknumericCli,
				nobilityCli:   mocknobilityCli,
				darkCli:       mockdarkCli,
				unifiedPayCli: mockunifiedPayCli,
				ukwCli:        mockukwCli,
				channelCli:    mockchannelCli,
			},
			args: args{
				ctx:     context.TODO(),
				orderId: "1",
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &RedPacketMgr{
				shutDown:      tt.fields.shutDown,
				sc:            tt.fields.sc,
				BusinessConf:  tt.fields.BusinessConf,
				Cache:         tt.fields.Cache,
				Store:         tt.fields.Store,
				apiCenterCli:  tt.fields.apiCenterCli,
				seqCli:        tt.fields.seqCli,
				pushCli:       tt.fields.pushCli,
				accountCli:    tt.fields.accountCli,
				channelMsgCli: tt.fields.channelMsgCli,
				presentCli:    tt.fields.presentCli,
				backpackCli:   tt.fields.backpackCli,
				bpSendCli:     tt.fields.bpSendCli,
				numericGoCli:  tt.fields.numericCli,
				nobilityCli:   tt.fields.nobilityCli,
				darkCli:       tt.fields.darkCli,
				unifiedPayCli: tt.fields.unifiedPayCli,
				ukwCli:        tt.fields.ukwCli,
				channelCli:    tt.fields.channelCli,
			}
			if err := m.HandleRPSettleAward(tt.args.ctx, tt.args.orderId); (err != nil) != tt.wantErr {
				t.Errorf("RedPacketMgr.HandleRPSettleAward() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

//func TestRedPacketMgr_handleInvalidRP(t *testing.T) {
//
//	genMgrMock(t)
//
//	user := &accountPB.UserResp{}
//	channelInfo := &channelsvr.ChannelSimpleInfo{}
//
//	gomock.InOrder(
//		mockaccountCli.EXPECT().GetUser(gomock.Any(), gomock.Any()).Return(user, nil),
//		mockCache.EXPECT().GetUKWCacheInfo(gomock.Any(), gomock.Any()).Return(nil, nil),
//		mockchannelCli.EXPECT().GetChannelSimpleInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(channelInfo, nil),
//		mockpushCli.EXPECT().PushMulticasts(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
//		mockStore.EXPECT().UpdateRedPacketOrderStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(true, nil),
//		mockBusinessConf.EXPECT().GetRPPayAppId().Return("1"),
//		mockunifiedPayCli.EXPECT().UnFreezeAndRefund(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
//		mockapiCenterCli.EXPECT().SendImMsg(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
//	)
//
//	orderInfo := &pb.RedPacketInfo{
//		OrderId: "rp1",
//		RedPacketConf: &pb.RedPacketConf{
//			TotalPrice: 1,
//		},
//		SenderUid:   1,
//		ChannelId:   1,
//		OutsideTime: 1,
//	}
//
//	type fields struct {
//		shutDown      chan interface{}
//		sc            conf.IServiceConfigT
//		BusinessConf  conf.IBusinessConfManager
//		Cache         cache.IRedPacketCache
//		Store         mysql.IStore
//		apiCenterCli  apicenter.IClient
//		seqCli        seqgen.IClient
//		pushCli       PushNotification.IClient
//		accountCli    account.IClient
//		channelMsgCli channelmsgexpress.IClient
//		presentCli    userPresent.IClient
//		backpackCli   backpackBase.IClient
//		bpSendCli     backpackSender.IClient
//		numericCli    numeri.IClient
//		nobilityCli   nobility.IClient
//		darkCli       darkserver.IClient
//		unifiedPayCli unifiedPay.IClient
//		ukwCli        ukw.IClient
//		channelCli    channel.IClient
//	}
//	type args struct {
//		ctx       context.Context
//		orderInfo *pb.RedPacketInfo
//	}
//	tests := []struct {
//		name    string
//		fields  fields
//		args    args
//		wantErr bool
//	}{
//		{
//			name: "handleInvalidRP",
//			fields: fields{
//				shutDown:     make(chan interface{}),
//				sc:           mockSc,
//				BusinessConf: mockBusinessConf,
//				Cache:        mockCache,
//				Store:        mockStore,
//				apiCenterCli: mockapiCenterCli,
//				//seqCli:        mockseqCli,
//				pushCli:       mockpushCli,
//				accountCli:    mockaccountCli,
//				channelMsgCli: mockchannelMsgCli,
//				presentCli:    mockpresentCli,
//				backpackCli:   mockbackpackCli,
//				bpSendCli:     mockbpSendCli,
//				numericCli:    mocknumericCli,
//				nobilityCli:   mocknobilityCli,
//				darkCli:       mockdarkCli,
//				unifiedPayCli: mockunifiedPayCli,
//				ukwCli:        mockukwCli,
//				channelCli:    mockchannelCli,
//			},
//			args: args{
//				ctx:       context.TODO(),
//				orderInfo: orderInfo,
//			},
//			wantErr: false,
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			m := &RedPacketMgr{
//				shutDown:      tt.fields.shutDown,
//				sc:            tt.fields.sc,
//				BusinessConf:  tt.fields.BusinessConf,
//				Cache:         tt.fields.Cache,
//				Store:         tt.fields.Store,
//				apiCenterCli:  tt.fields.apiCenterCli,
//				seqCli:        tt.fields.seqCli,
//				pushCli:       tt.fields.pushCli,
//				accountCli:    tt.fields.accountCli,
//				channelMsgCli: tt.fields.channelMsgCli,
//				presentCli:    tt.fields.presentCli,
//				backpackCli:   tt.fields.backpackCli,
//				bpSendCli:     tt.fields.bpSendCli,
//				numericCli:    tt.fields.numericCli,
//				nobilityCli:   tt.fields.nobilityCli,
//				darkCli:       tt.fields.darkCli,
//				unifiedPayCli: tt.fields.unifiedPayCli,
//				ukwCli:        tt.fields.ukwCli,
//				channelCli:    tt.fields.channelCli,
//			}
//			if err := m.handleInvalidRP(tt.args.ctx, tt.args.orderInfo); (err != nil) != tt.wantErr {
//				t.Errorf("RedPacketMgr.handleInvalidRP() error = %v, wantErr %v", err, tt.wantErr)
//			}
//		})
//	}
//}

func TestRedPacketMgr_GetAwardList(t *testing.T) {

	genMgrMock(t)

	orderInfo := &pb.RedPacketInfo{}
	rpConf := &mysql.RedPacketConf{
		//GiftConfOpt: `{"GiftList":[{"GiftId":1271,"Name":"1","BgId":1006,"Price":10000}]}`,
	}
	rpConf1 := &mysql.RedPacketConf{
		Id:          1,
		GiftConfOpt: `{"GiftList":[{"GiftId":1271,"Name":"1","BgId":1006,"Price":10000}]}`,
	}
	rpConf2 := &mysql.RedPacketConf{
		Id:          1,
		GiftConfOpt: `{"GiftList":[{"GiftId":1271,"Name":"1","BgId":1006,"Price":10000}]}`,
		TotalPrice:  10000,
	}
	mapGiftId2GiftInfo := map[uint32]*mysql.GiftAwardInfo{}
	now := time.Now()

	awardList := make([]*mysql.RedPacketAwardLog, 0)
	awardOptList := make([]*channelredpacketlogic.RedPacketAwardOpt, 0)

	list := []uint32{1}
	darkrsp := &darkserverpb.BatchGetBlackUserListResp{}
	err := protocol.NewExactServerError(nil, -2, "!")

	gomock.InOrder(

		mockCache.EXPECT().GetRedPacketClickCntRankList(gomock.Any(), gomock.Any(), gomock.Any()).Return(list, errors.New("1")),

		mockCache.EXPECT().GetRedPacketClickCntRankList(gomock.Any(), gomock.Any(), gomock.Any()).Return(list, nil),
		mockdarkCli.EXPECT().BatchBlackUserListCheck(gomock.Any(), gomock.Any()).Return(darkrsp, err),
	)

	type fields struct {
		shutDown      chan interface{}
		sc            conf.IServiceConfigT
		BusinessConf  conf.IBusinessConfManager
		Cache         cache.IRedPacketCache
		Store         mysql.IStore
		apiCenterCli  apicenter.IClient
		seqCli        seqgen.IClient
		pushCli       PushNotification.IClient
		accountCli    account.IClient
		channelMsgCli channelmsgexpress.IClient
		presentCli    userPresent.IClient
		backpackCli   backpackBase.IClient
		bpSendCli     backpackSender.IClient
		numericCli    numeric_go.IClient
		nobilityCli   nobility.IClient
		darkCli       darkserver.IClient
		unifiedPayCli unifiedPay.IClient
		ukwCli        ukw.IClient
		channelCli    channel.IClient
	}
	type args struct {
		ctx                context.Context
		orderInfo          *pb.RedPacketInfo
		rpConf             *mysql.RedPacketConf
		mapGiftId2GiftInfo map[uint32]*mysql.GiftAwardInfo
		now                time.Time
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []*mysql.RedPacketAwardLog
		want1   []*channelredpacketlogic.RedPacketAwardOpt
		wantErr bool
	}{
		{
			name: "GetAwardList",
			fields: fields{
				shutDown:     make(chan interface{}),
				sc:           mockSc,
				BusinessConf: mockBusinessConf,
				Cache:        mockCache,
				Store:        mockStore,
				apiCenterCli: mockapiCenterCli,
				//seqCli:        mockseqCli,
				pushCli:       mockpushCli,
				accountCli:    mockaccountCli,
				channelMsgCli: mockchannelMsgCli,
				presentCli:    mockpresentCli,
				backpackCli:   mockbackpackCli,
				bpSendCli:     mockbpSendCli,
				numericCli:    mocknumericCli,
				nobilityCli:   mocknobilityCli,
				darkCli:       mockdarkCli,
				unifiedPayCli: mockunifiedPayCli,
				ukwCli:        mockukwCli,
				channelCli:    mockchannelCli,
			},
			args: args{
				ctx:                context.TODO(),
				orderInfo:          orderInfo,
				rpConf:             rpConf,
				mapGiftId2GiftInfo: mapGiftId2GiftInfo,
				now:                now,
			},

			want:    awardList,
			want1:   awardOptList,
			wantErr: true,
		},

		{
			name: "GetAwardList",
			fields: fields{
				shutDown:     make(chan interface{}),
				sc:           mockSc,
				BusinessConf: mockBusinessConf,
				Cache:        mockCache,
				Store:        mockStore,
				apiCenterCli: mockapiCenterCli,
				//seqCli:        mockseqCli,
				pushCli:       mockpushCli,
				accountCli:    mockaccountCli,
				channelMsgCli: mockchannelMsgCli,
				presentCli:    mockpresentCli,
				backpackCli:   mockbackpackCli,
				bpSendCli:     mockbpSendCli,
				numericCli:    mocknumericCli,
				nobilityCli:   mocknobilityCli,
				darkCli:       mockdarkCli,
				unifiedPayCli: mockunifiedPayCli,
				ukwCli:        mockukwCli,
				channelCli:    mockchannelCli,
			},
			args: args{
				ctx:                context.TODO(),
				orderInfo:          orderInfo,
				rpConf:             rpConf1,
				mapGiftId2GiftInfo: mapGiftId2GiftInfo,
				now:                now,
			},

			want:    awardList,
			want1:   awardOptList,
			wantErr: true,
		},

		{
			name: "GetAwardList",
			fields: fields{
				shutDown:     make(chan interface{}),
				sc:           mockSc,
				BusinessConf: mockBusinessConf,
				Cache:        mockCache,
				Store:        mockStore,
				apiCenterCli: mockapiCenterCli,
				//seqCli:        mockseqCli,
				pushCli:       mockpushCli,
				accountCli:    mockaccountCli,
				channelMsgCli: mockchannelMsgCli,
				presentCli:    mockpresentCli,
				backpackCli:   mockbackpackCli,
				bpSendCli:     mockbpSendCli,
				numericCli:    mocknumericCli,
				nobilityCli:   mocknobilityCli,
				darkCli:       mockdarkCli,
				unifiedPayCli: mockunifiedPayCli,
				ukwCli:        mockukwCli,
				channelCli:    mockchannelCli,
			},
			args: args{
				ctx:                context.TODO(),
				orderInfo:          orderInfo,
				rpConf:             rpConf2,
				mapGiftId2GiftInfo: mapGiftId2GiftInfo,
				now:                now,
			},

			want:    awardList,
			want1:   awardOptList,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &RedPacketMgr{
				shutDown:      tt.fields.shutDown,
				sc:            tt.fields.sc,
				BusinessConf:  tt.fields.BusinessConf,
				Cache:         tt.fields.Cache,
				Store:         tt.fields.Store,
				apiCenterCli:  tt.fields.apiCenterCli,
				seqCli:        tt.fields.seqCli,
				pushCli:       tt.fields.pushCli,
				accountCli:    tt.fields.accountCli,
				channelMsgCli: tt.fields.channelMsgCli,
				presentCli:    tt.fields.presentCli,
				backpackCli:   tt.fields.backpackCli,
				bpSendCli:     tt.fields.bpSendCli,
				numericGoCli:  tt.fields.numericCli,
				nobilityCli:   tt.fields.nobilityCli,
				darkCli:       tt.fields.darkCli,
				unifiedPayCli: tt.fields.unifiedPayCli,
				ukwCli:        tt.fields.ukwCli,
				channelCli:    tt.fields.channelCli,
			}
			got, got1, err := m.GetAwardList(tt.args.ctx, tt.args.orderInfo, tt.args.rpConf, tt.args.mapGiftId2GiftInfo, tt.args.now)
			if (err != nil) != tt.wantErr {
				t.Errorf("RedPacketMgr.GetAwardList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("RedPacketMgr.GetAwardList() got = %v, want %v", got, tt.want)
			}
			if !reflect.DeepEqual(got1, tt.want1) {
				t.Errorf("RedPacketMgr.GetAwardList() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func TestRedPacketMgr_genAwardPbList(t *testing.T) {

	genMgrMock(t)

	mapGiftId2Cnt := map[uint32]uint32{
		1: 0,
		2: 1,
	}
	mapUid2GiftId2Cnt := map[uint32]map[uint32]uint32{
		1: mapGiftId2Cnt,
	}
	mapGiftId2GiftInfo := map[uint32]*mysql.GiftAwardInfo{
		2: {BgId: 1, Price: 1},
	}
	orderInfo := &pb.RedPacketInfo{}
	now := time.Now()

	RedPacketAwardLoglist := []*mysql.RedPacketAwardLog{
		{},
	}
	RedPacketAwardOpt := []*channelredpacketlogic.RedPacketAwardOpt{
		{
			Uid: 1,
			AwardList: []*channelredpacketlogic.GiftAwardInfo{
				{
					GiftId: 2,
					Count:  1,
				},
			},
			AwardPrice: 1,
		},
	}

	award_secret_key := "ffkywmynfehcdbqq"
	businessId := uint32(1)

	gomock.InOrder(
		mockBusinessConf.EXPECT().GetRPAwardBusinessInfo().Return(businessId, award_secret_key),
	)

	type fields struct {
		shutDown      chan interface{}
		sc            conf.IServiceConfigT
		BusinessConf  conf.IBusinessConfManager
		Cache         cache.IRedPacketCache
		Store         mysql.IStore
		apiCenterCli  apicenter.IClient
		seqCli        seqgen.IClient
		pushCli       PushNotification.IClient
		accountCli    account.IClient
		channelMsgCli channelmsgexpress.IClient
		presentCli    userPresent.IClient
		backpackCli   backpackBase.IClient
		bpSendCli     backpackSender.IClient
		numericCli    numeric_go.IClient
		nobilityCli   nobility.IClient
		darkCli       darkserver.IClient
		unifiedPayCli unifiedPay.IClient
		ukwCli        ukw.IClient
		channelCli    channel.IClient
	}
	type args struct {
		mapUid2GiftId2Cnt  map[uint32]map[uint32]uint32
		mapGiftId2GiftInfo map[uint32]*mysql.GiftAwardInfo
		orderInfo          *pb.RedPacketInfo
		now                time.Time
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   []*mysql.RedPacketAwardLog
		want1  []*channelredpacketlogic.RedPacketAwardOpt
	}{
		{
			name: "genAwardPbList",
			fields: fields{
				shutDown:     make(chan interface{}),
				sc:           mockSc,
				BusinessConf: mockBusinessConf,
				Cache:        mockCache,
				Store:        mockStore,
				apiCenterCli: mockapiCenterCli,
				//seqCli:        mockseqCli,
				pushCli:       mockpushCli,
				accountCli:    mockaccountCli,
				channelMsgCli: mockchannelMsgCli,
				presentCli:    mockpresentCli,
				backpackCli:   mockbackpackCli,
				bpSendCli:     mockbpSendCli,
				numericCli:    mocknumericCli,
				nobilityCli:   mocknobilityCli,
				darkCli:       mockdarkCli,
				unifiedPayCli: mockunifiedPayCli,
				ukwCli:        mockukwCli,
				channelCli:    mockchannelCli,
			},
			args: args{
				mapUid2GiftId2Cnt:  mapUid2GiftId2Cnt,
				mapGiftId2GiftInfo: mapGiftId2GiftInfo,
				orderInfo:          orderInfo,
				now:                now,
			},
			want:  RedPacketAwardLoglist,
			want1: RedPacketAwardOpt,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &RedPacketMgr{
				shutDown:      tt.fields.shutDown,
				sc:            tt.fields.sc,
				BusinessConf:  tt.fields.BusinessConf,
				Cache:         tt.fields.Cache,
				Store:         tt.fields.Store,
				apiCenterCli:  tt.fields.apiCenterCli,
				seqCli:        tt.fields.seqCli,
				pushCli:       tt.fields.pushCli,
				accountCli:    tt.fields.accountCli,
				channelMsgCli: tt.fields.channelMsgCli,
				presentCli:    tt.fields.presentCli,
				backpackCli:   tt.fields.backpackCli,
				bpSendCli:     tt.fields.bpSendCli,
				numericGoCli:  tt.fields.numericCli,
				nobilityCli:   tt.fields.nobilityCli,
				darkCli:       tt.fields.darkCli,
				unifiedPayCli: tt.fields.unifiedPayCli,
				ukwCli:        tt.fields.ukwCli,
				channelCli:    tt.fields.channelCli,
			}
			got, got1 := m.genAwardPbList(tt.args.mapUid2GiftId2Cnt, tt.args.mapGiftId2GiftInfo, tt.args.orderInfo, tt.args.now)
			if !reflect.DeepEqual(got, tt.want) {
				//t.Errorf("RedPacketMgr.genAwardPbList() got = %v, want %v", got, tt.want)
			}
			if !reflect.DeepEqual(got1, tt.want1) {
				t.Errorf("RedPacketMgr.genAwardPbList() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func TestRedPacketMgr_RecordAwardInfo(t *testing.T) {

	genMgrMock(t)

	orderInfo := &pb.RedPacketInfo{}
	awardList := []*mysql.RedPacketAwardLog{}

	gomock.InOrder(
		mockStore.EXPECT().Transaction(gomock.Any(), gomock.Any()).Return(nil),
	)

	type fields struct {
		shutDown      chan interface{}
		sc            conf.IServiceConfigT
		BusinessConf  conf.IBusinessConfManager
		Cache         cache.IRedPacketCache
		Store         mysql.IStore
		apiCenterCli  apicenter.IClient
		seqCli        seqgen.IClient
		pushCli       PushNotification.IClient
		accountCli    account.IClient
		channelMsgCli channelmsgexpress.IClient
		presentCli    userPresent.IClient
		backpackCli   backpackBase.IClient
		bpSendCli     backpackSender.IClient
		numericCli    numeric_go.IClient
		nobilityCli   nobility.IClient
		darkCli       darkserver.IClient
		unifiedPayCli unifiedPay.IClient
		ukwCli        ukw.IClient
		channelCli    channel.IClient
	}
	type args struct {
		ctx       context.Context
		orderInfo *pb.RedPacketInfo
		awardList []*mysql.RedPacketAwardLog
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "RecordAwardInfo",
			fields: fields{
				shutDown:     make(chan interface{}),
				sc:           mockSc,
				BusinessConf: mockBusinessConf,
				Cache:        mockCache,
				Store:        mockStore,
				apiCenterCli: mockapiCenterCli,
				//seqCli:        mockseqCli,
				pushCli:       mockpushCli,
				accountCli:    mockaccountCli,
				channelMsgCli: mockchannelMsgCli,
				presentCli:    mockpresentCli,
				backpackCli:   mockbackpackCli,
				bpSendCli:     mockbpSendCli,
				numericCli:    mocknumericCli,
				nobilityCli:   mocknobilityCli,
				darkCli:       mockdarkCli,
				unifiedPayCli: mockunifiedPayCli,
				ukwCli:        mockukwCli,
				channelCli:    mockchannelCli,
			},
			args: args{
				orderInfo: orderInfo,
				awardList: awardList,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &RedPacketMgr{
				shutDown:      tt.fields.shutDown,
				sc:            tt.fields.sc,
				BusinessConf:  tt.fields.BusinessConf,
				Cache:         tt.fields.Cache,
				Store:         tt.fields.Store,
				apiCenterCli:  tt.fields.apiCenterCli,
				seqCli:        tt.fields.seqCli,
				pushCli:       tt.fields.pushCli,
				accountCli:    tt.fields.accountCli,
				channelMsgCli: tt.fields.channelMsgCli,
				presentCli:    tt.fields.presentCli,
				backpackCli:   tt.fields.backpackCli,
				bpSendCli:     tt.fields.bpSendCli,
				numericGoCli:  tt.fields.numericCli,
				nobilityCli:   tt.fields.nobilityCli,
				darkCli:       tt.fields.darkCli,
				unifiedPayCli: tt.fields.unifiedPayCli,
				ukwCli:        tt.fields.ukwCli,
				channelCli:    tt.fields.channelCli,
			}
			if err := m.RecordAwardInfo(tt.args.ctx, tt.args.orderInfo, tt.args.awardList); (err != nil) != tt.wantErr {
				t.Errorf("RedPacketMgr.RecordAwardInfo() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestRedPacketMgr_handleAward(t *testing.T) {

	genMgrMock(t)

	orderInfo := &pb.RedPacketInfo{}
	awardOptList := []*channelredpacketlogic.RedPacketAwardOpt{
		{
			Uid:        1,
			AwardPrice: 1,
		},
	}
	mapGiftId2GiftInfo := map[uint32]*mysql.GiftAwardInfo{}
	now := time.Now()
	usermp := map[uint32]*accountPB.UserResp{}
	uwkInfos := map[uint32]*youknowwho.UKWPersonInfo{}
	channelInfo := &channelsvr.ChannelSimpleInfo{}

	gomock.InOrder(
		mockaccountCli.EXPECT().GetUsersMap(gomock.Any(), gomock.Any()).Return(usermp, nil),
		// GetYkwShortTimeOutMs
		mockBusinessConf.EXPECT().GetYkwShortTimeOutMs().Return(time.Second),

		mockukwCli.EXPECT().BatchGetUKWPersonInfoOnly(gomock.Any(), gomock.Any()).Return(uwkInfos, nil),
		mockCache.EXPECT().GetUKWCacheInfo(gomock.Any(), gomock.Any()).Return(nil, nil),
		mockchannelCli.EXPECT().GetChannelSimpleInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(channelInfo, nil),
	)

	type fields struct {
		shutDown      chan interface{}
		sc            conf.IServiceConfigT
		BusinessConf  conf.IBusinessConfManager
		Cache         cache.IRedPacketCache
		Store         mysql.IStore
		apiCenterCli  apicenter.IClient
		seqCli        seqgen.IClient
		pushCli       PushNotification.IClient
		accountCli    account.IClient
		channelMsgCli channelmsgexpress.IClient
		presentCli    userPresent.IClient
		backpackCli   backpackBase.IClient
		bpSendCli     backpackSender.IClient
		numericCli    numeric_go.IClient
		nobilityCli   nobility.IClient
		darkCli       darkserver.IClient
		unifiedPayCli unifiedPay.IClient
		ukwCli        ukw.IClient
		channelCli    channel.IClient
	}
	type args struct {
		ctx                context.Context
		orderInfo          *pb.RedPacketInfo
		awardOptList       []*channelredpacketlogic.RedPacketAwardOpt
		mapGiftId2GiftInfo map[uint32]*mysql.GiftAwardInfo
		now                time.Time
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "handleAward",
			fields: fields{
				shutDown:     make(chan interface{}),
				sc:           mockSc,
				BusinessConf: mockBusinessConf,
				Cache:        mockCache,
				Store:        mockStore,
				apiCenterCli: mockapiCenterCli,
				//seqCli:        mockseqCli,
				pushCli:       mockpushCli,
				accountCli:    mockaccountCli,
				channelMsgCli: mockchannelMsgCli,
				presentCli:    mockpresentCli,
				backpackCli:   mockbackpackCli,
				bpSendCli:     mockbpSendCli,
				numericCli:    mocknumericCli,
				nobilityCli:   mocknobilityCli,
				darkCli:       mockdarkCli,
				unifiedPayCli: mockunifiedPayCli,
				ukwCli:        mockukwCli,
				channelCli:    mockchannelCli,
			},
			args: args{
				orderInfo:          orderInfo,
				awardOptList:       awardOptList,
				mapGiftId2GiftInfo: mapGiftId2GiftInfo,
				now:                now,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &RedPacketMgr{
				shutDown:      tt.fields.shutDown,
				sc:            tt.fields.sc,
				BusinessConf:  tt.fields.BusinessConf,
				Cache:         tt.fields.Cache,
				Store:         tt.fields.Store,
				apiCenterCli:  tt.fields.apiCenterCli,
				seqCli:        tt.fields.seqCli,
				pushCli:       tt.fields.pushCli,
				accountCli:    tt.fields.accountCli,
				channelMsgCli: tt.fields.channelMsgCli,
				presentCli:    tt.fields.presentCli,
				backpackCli:   tt.fields.backpackCli,
				bpSendCli:     tt.fields.bpSendCli,
				numericGoCli:  tt.fields.numericCli,
				nobilityCli:   tt.fields.nobilityCli,
				darkCli:       tt.fields.darkCli,
				unifiedPayCli: tt.fields.unifiedPayCli,
				ukwCli:        tt.fields.ukwCli,
				channelCli:    tt.fields.channelCli,
			}
			if err := m.handleAward(tt.args.ctx, tt.args.orderInfo, tt.args.awardOptList, tt.args.mapGiftId2GiftInfo, tt.args.now); (err != nil) != tt.wantErr {
				t.Errorf("RedPacketMgr.handleAward() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

//func TestRedPacketMgr_awardUser(t *testing.T) {
//
//	genMgrMock(t)
//
//	orderInfo := &pb.RedPacketInfo{}
//	opt := &channelredpacketlogic.RedPacketAwardOpt{}
//	opt2 := &channelredpacketlogic.RedPacketAwardOpt{
//		AwardList: []*channelredpacketlogic.GiftAwardInfo{
//			{
//				GiftId: 1,
//				Count:  1,
//			},
//			{
//				GiftId: 2,
//				Count:  1,
//			},
//		},
//	}
//	mapGiftId2GiftInfo := map[uint32]*mysql.GiftAwardInfo{
//		1: {
//			GiftId: 1,
//			Name:   "1",
//		},
//	}
//	now := time.Now()
//	award_secret_key := "ffkywmynfehcdbqq"
//	businessId := uint32(1)
//
//	gomock.InOrder(
//		mockBusinessConf.EXPECT().GetRPAwardBusinessInfo().Return(businessId, award_secret_key),
//		mockBusinessConf.EXPECT().GetRPAwardBusinessInfo().Return(businessId, award_secret_key),
//		mockbpSendCli.EXPECT().SendBackpackWithRiskControl(gomock.Any(), gomock.Any()).Return(nil, nil),
//		mockStore.EXPECT().UpdateRedPacketAwardDone(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil),
//
//		mockapiCenterCli.EXPECT().SendImMsg(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
//
//		mockpushCli.EXPECT().PushToUsers(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
//	)
//
//	type fields struct {
//		shutDown      chan interface{}
//		sc            conf.IServiceConfigT
//		BusinessConf  conf.IBusinessConfManager
//		Cache         cache.IRedPacketCache
//		Store         mysql.IStore
//		apiCenterCli  apicenter.IClient
//		seqCli        seqgen.IClient
//		pushCli       PushNotification.IClient
//		accountCli    account.IClient
//		channelMsgCli channelmsgexpress.IClient
//		presentCli    userPresent.IClient
//		backpackCli   backpackBase.IClient
//		bpSendCli     backpackSender.IClient
//		numericCli    numeri.IClient
//		nobilityCli   nobility.IClient
//		darkCli       darkserver.IClient
//		unifiedPayCli unifiedPay.IClient
//		ukwCli        ukw.IClient
//		channelCli    channel.IClient
//	}
//	type args struct {
//		orderInfo          *pb.RedPacketInfo
//		opt                *channelredpacketlogic.RedPacketAwardOpt
//		mapGiftId2GiftInfo map[uint32]*mysql.GiftAwardInfo
//		now                time.Time
//	}
//	tests := []struct {
//		name    string
//		fields  fields
//		args    args
//		wantErr bool
//	}{
//		{
//			name: "AddUserRichVal",
//			fields: fields{
//				shutDown:     make(chan interface{}),
//				sc:           mockSc,
//				BusinessConf: mockBusinessConf,
//				Cache:        mockCache,
//				Store:        mockStore,
//				apiCenterCli: mockapiCenterCli,
//				//seqCli:        mockseqCli,
//				pushCli:       mockpushCli,
//				accountCli:    mockaccountCli,
//				channelMsgCli: mockchannelMsgCli,
//				presentCli:    mockpresentCli,
//				backpackCli:   mockbackpackCli,
//				bpSendCli:     mockbpSendCli,
//				numericCli:    mocknumericCli,
//				nobilityCli:   mocknobilityCli,
//				darkCli:       mockdarkCli,
//				unifiedPayCli: mockunifiedPayCli,
//				ukwCli:        mockukwCli,
//				channelCli:    mockchannelCli,
//			},
//			args: args{
//				orderInfo:          orderInfo,
//				opt:                opt,
//				mapGiftId2GiftInfo: mapGiftId2GiftInfo,
//				now:                now,
//			},
//			wantErr: false,
//		},
//		{
//			name: "AddUserRichVal",
//			fields: fields{
//				shutDown:     make(chan interface{}),
//				sc:           mockSc,
//				BusinessConf: mockBusinessConf,
//				Cache:        mockCache,
//				Store:        mockStore,
//				apiCenterCli: mockapiCenterCli,
//				//seqCli:        mockseqCli,
//				pushCli:       mockpushCli,
//				accountCli:    mockaccountCli,
//				channelMsgCli: mockchannelMsgCli,
//				presentCli:    mockpresentCli,
//				backpackCli:   mockbackpackCli,
//				bpSendCli:     mockbpSendCli,
//				numericCli:    mocknumericCli,
//				nobilityCli:   mocknobilityCli,
//				darkCli:       mockdarkCli,
//				unifiedPayCli: mockunifiedPayCli,
//				ukwCli:        mockukwCli,
//				channelCli:    mockchannelCli,
//			},
//			args: args{
//				orderInfo:          orderInfo,
//				opt:                opt2,
//				mapGiftId2GiftInfo: mapGiftId2GiftInfo,
//				now:                now,
//			},
//			wantErr: false,
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			m := &RedPacketMgr{
//				shutDown:      tt.fields.shutDown,
//				sc:            tt.fields.sc,
//				BusinessConf:  tt.fields.BusinessConf,
//				Cache:         tt.fields.Cache,
//				Store:         tt.fields.Store,
//				apiCenterCli:  tt.fields.apiCenterCli,
//				seqCli:        tt.fields.seqCli,
//				pushCli:       tt.fields.pushCli,
//				accountCli:    tt.fields.accountCli,
//				channelMsgCli: tt.fields.channelMsgCli,
//				presentCli:    tt.fields.presentCli,
//				backpackCli:   tt.fields.backpackCli,
//				bpSendCli:     tt.fields.bpSendCli,
//				numericCli:    tt.fields.numericCli,
//				nobilityCli:   tt.fields.nobilityCli,
//				darkCli:       tt.fields.darkCli,
//				unifiedPayCli: tt.fields.unifiedPayCli,
//				ukwCli:        tt.fields.ukwCli,
//				channelCli:    tt.fields.channelCli,
//			}
//			if err := m.awardUser(tt.args.orderInfo, tt.args.opt, tt.args.mapGiftId2GiftInfo, tt.args.now); (err != nil) != tt.wantErr {
//				t.Errorf("RedPacketMgr.awardUser() error = %v, wantErr %v", err, tt.wantErr)
//			}
//		})
//	}
//}

func TestRedPacketMgr_AddUserRichVal(t *testing.T) {

	genMgrMock(t)

	NumericResp := &numericPB.AddUserNumericResp{}
	err := protocol.NewServerError(-2, "!")

	gomock.InOrder(
		mocknumericCli.EXPECT().AddUserNumeric(gomock.Any(), gomock.Any()).Return(NumericResp, nil),
		mockapiCenterCli.EXPECT().NotifyGrowInfoSync(gomock.Any(), gomock.Any()).Return(err),
		mocknobilityCli.EXPECT().AddUserNobilityValue(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil),

		mocknumericCli.EXPECT().AddUserNumeric(gomock.Any(), gomock.Any()).Return(NumericResp, err),

		mocknumericCli.EXPECT().AddUserNumeric(gomock.Any(), gomock.Any()).Return(NumericResp, nil),
		mockapiCenterCli.EXPECT().NotifyGrowInfoSync(gomock.Any(), gomock.Any()).Return(err),
		mocknobilityCli.EXPECT().AddUserNobilityValue(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, err),
	)

	type fields struct {
		shutDown      chan interface{}
		sc            conf.IServiceConfigT
		BusinessConf  conf.IBusinessConfManager
		Cache         cache.IRedPacketCache
		Store         mysql.IStore
		apiCenterCli  apicenter.IClient
		seqCli        seqgen.IClient
		pushCli       PushNotification.IClient
		accountCli    account.IClient
		channelMsgCli channelmsgexpress.IClient
		presentCli    userPresent.IClient
		backpackCli   backpackBase.IClient
		bpSendCli     backpackSender.IClient
		numericCli    numeric_go.IClient
		nobilityCli   nobility.IClient
		darkCli       darkserver.IClient
		unifiedPayCli unifiedPay.IClient
		ukwCli        ukw.IClient
		channelCli    channel.IClient
	}
	type args struct {
		ctx     context.Context
		orderId string
		uid     uint32
		val     uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "AddUserRichVal",
			fields: fields{
				shutDown:     make(chan interface{}),
				sc:           mockSc,
				BusinessConf: mockBusinessConf,
				Cache:        mockCache,
				Store:        mockStore,
				apiCenterCli: mockapiCenterCli,
				//seqCli:        mockseqCli,
				pushCli:       mockpushCli,
				accountCli:    mockaccountCli,
				channelMsgCli: mockchannelMsgCli,
				presentCli:    mockpresentCli,
				backpackCli:   mockbackpackCli,
				bpSendCli:     mockbpSendCli,
				numericCli:    mocknumericCli,
				nobilityCli:   mocknobilityCli,
				darkCli:       mockdarkCli,
				unifiedPayCli: mockunifiedPayCli,
				ukwCli:        mockukwCli,
				channelCli:    mockchannelCli,
			},
			args: args{
				ctx:     context.TODO(),
				orderId: "1",
				uid:     1,
				val:     1,
			},
			wantErr: false,
		},

		{
			name: "AddUserRichVal",
			fields: fields{
				shutDown:     make(chan interface{}),
				sc:           mockSc,
				BusinessConf: mockBusinessConf,
				Cache:        mockCache,
				Store:        mockStore,
				apiCenterCli: mockapiCenterCli,
				//seqCli:        mockseqCli,
				pushCli:       mockpushCli,
				accountCli:    mockaccountCli,
				channelMsgCli: mockchannelMsgCli,
				presentCli:    mockpresentCli,
				backpackCli:   mockbackpackCli,
				bpSendCli:     mockbpSendCli,
				numericCli:    mocknumericCli,
				nobilityCli:   mocknobilityCli,
				darkCli:       mockdarkCli,
				unifiedPayCli: mockunifiedPayCli,
				ukwCli:        mockukwCli,
				channelCli:    mockchannelCli,
			},
			args: args{
				ctx:     context.TODO(),
				orderId: "1",
				uid:     1,
				val:     1,
			},
			wantErr: true,
		},

		{
			name: "AddUserRichVal",
			fields: fields{
				shutDown:     make(chan interface{}),
				sc:           mockSc,
				BusinessConf: mockBusinessConf,
				Cache:        mockCache,
				Store:        mockStore,
				apiCenterCli: mockapiCenterCli,
				//seqCli:        mockseqCli,
				pushCli:       mockpushCli,
				accountCli:    mockaccountCli,
				channelMsgCli: mockchannelMsgCli,
				presentCli:    mockpresentCli,
				backpackCli:   mockbackpackCli,
				bpSendCli:     mockbpSendCli,
				numericCli:    mocknumericCli,
				nobilityCli:   mocknobilityCli,
				darkCli:       mockdarkCli,
				unifiedPayCli: mockunifiedPayCli,
				ukwCli:        mockukwCli,
				channelCli:    mockchannelCli,
			},
			args: args{
				ctx:     context.TODO(),
				orderId: "1",
				uid:     1,
				val:     1,
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &RedPacketMgr{
				shutDown:      tt.fields.shutDown,
				sc:            tt.fields.sc,
				BusinessConf:  tt.fields.BusinessConf,
				Cache:         tt.fields.Cache,
				Store:         tt.fields.Store,
				apiCenterCli:  tt.fields.apiCenterCli,
				seqCli:        tt.fields.seqCli,
				pushCli:       tt.fields.pushCli,
				accountCli:    tt.fields.accountCli,
				channelMsgCli: tt.fields.channelMsgCli,
				presentCli:    tt.fields.presentCli,
				backpackCli:   tt.fields.backpackCli,
				bpSendCli:     tt.fields.bpSendCli,
				numericGoCli:  tt.fields.numericCli,
				nobilityCli:   tt.fields.nobilityCli,
				darkCli:       tt.fields.darkCli,
				unifiedPayCli: tt.fields.unifiedPayCli,
				ukwCli:        tt.fields.ukwCli,
				channelCli:    tt.fields.channelCli,
			}
			if err := m.AddUserRichVal(tt.args.ctx, tt.args.orderId, tt.args.uid, tt.args.val); (err != nil) != tt.wantErr {
				t.Errorf("RedPacketMgr.AddUserRichVal() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestRedPacketMgr_getPackageFinTime(t *testing.T) {

	genMgrMock(t)

	err := protocol.NewServerError(-2, "!")
	PackageItemCfg := &backpackPB.GetPackageItemCfgResp{
		ItemCfgList: []*backpackPB.PackageItemCfg{
			{
				FinTime: 1,
			},
		},
	}

	gomock.InOrder(
		mockbackpackCli.EXPECT().GetPackageItemCfg(gomock.Any(), gomock.Any(), gomock.Any()).Return(PackageItemCfg, err),
		mockbackpackCli.EXPECT().GetPackageItemCfg(gomock.Any(), gomock.Any(), gomock.Any()).Return(PackageItemCfg, nil),
	)

	type fields struct {
		shutDown      chan interface{}
		sc            conf.IServiceConfigT
		BusinessConf  conf.IBusinessConfManager
		Cache         cache.IRedPacketCache
		Store         mysql.IStore
		apiCenterCli  apicenter.IClient
		seqCli        seqgen.IClient
		pushCli       PushNotification.IClient
		accountCli    account.IClient
		channelMsgCli channelmsgexpress.IClient
		presentCli    userPresent.IClient
		backpackCli   backpackBase.IClient
		bpSendCli     backpackSender.IClient
		numericCli    numeric_go.IClient
		nobilityCli   nobility.IClient
		darkCli       darkserver.IClient
		unifiedPayCli unifiedPay.IClient
		ukwCli        ukw.IClient
		channelCli    channel.IClient
	}
	type args struct {
		ctx  context.Context
		bgId uint32
	}
	tests := []struct {
		name         string
		fields       fields
		args         args
		wantExpireTs uint32
	}{
		{
			name: "getPackageFinTime",
			fields: fields{
				shutDown:     make(chan interface{}),
				sc:           mockSc,
				BusinessConf: mockBusinessConf,
				Cache:        mockCache,
				Store:        mockStore,
				apiCenterCli: mockapiCenterCli,
				//seqCli:        mockseqCli,
				pushCli:       mockpushCli,
				accountCli:    mockaccountCli,
				channelMsgCli: mockchannelMsgCli,
				presentCli:    mockpresentCli,
				backpackCli:   mockbackpackCli,
				bpSendCli:     mockbpSendCli,
				numericCli:    mocknumericCli,
				nobilityCli:   mocknobilityCli,
				darkCli:       mockdarkCli,
				unifiedPayCli: mockunifiedPayCli,
				ukwCli:        mockukwCli,
				channelCli:    mockchannelCli,
			},
			args: args{
				ctx:  context.TODO(),
				bgId: 1,
			},
			wantExpireTs: 0,
		},
		{
			name: "getPackageFinTime",
			fields: fields{
				shutDown:     make(chan interface{}),
				sc:           mockSc,
				BusinessConf: mockBusinessConf,
				Cache:        mockCache,
				Store:        mockStore,
				apiCenterCli: mockapiCenterCli,
				//seqCli:        mockseqCli,
				pushCli:       mockpushCli,
				accountCli:    mockaccountCli,
				channelMsgCli: mockchannelMsgCli,
				presentCli:    mockpresentCli,
				backpackCli:   mockbackpackCli,
				bpSendCli:     mockbpSendCli,
				numericCli:    mocknumericCli,
				nobilityCli:   mocknobilityCli,
				darkCli:       mockdarkCli,
				unifiedPayCli: mockunifiedPayCli,
				ukwCli:        mockukwCli,
				channelCli:    mockchannelCli,
			},
			args: args{
				ctx:  context.TODO(),
				bgId: 1,
			},
			wantExpireTs: 1,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &RedPacketMgr{
				shutDown:      tt.fields.shutDown,
				sc:            tt.fields.sc,
				BusinessConf:  tt.fields.BusinessConf,
				Cache:         tt.fields.Cache,
				Store:         tt.fields.Store,
				apiCenterCli:  tt.fields.apiCenterCli,
				seqCli:        tt.fields.seqCli,
				pushCli:       tt.fields.pushCli,
				accountCli:    tt.fields.accountCli,
				channelMsgCli: tt.fields.channelMsgCli,
				presentCli:    tt.fields.presentCli,
				backpackCli:   tt.fields.backpackCli,
				bpSendCli:     tt.fields.bpSendCli,
				numericGoCli:  tt.fields.numericCli,
				nobilityCli:   tt.fields.nobilityCli,
				darkCli:       tt.fields.darkCli,
				unifiedPayCli: tt.fields.unifiedPayCli,
				ukwCli:        tt.fields.ukwCli,
				channelCli:    tt.fields.channelCli,
			}
			if gotExpireTs := m.getPackageFinTime(tt.args.ctx, tt.args.bgId); gotExpireTs != tt.wantExpireTs {
				t.Errorf("RedPacketMgr.getPackageFinTime() = %v, want %v", gotExpireTs, tt.wantExpireTs)
			}
		})
	}
}
