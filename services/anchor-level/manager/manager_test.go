package manager

import (
	"golang.52tt.com/services/anchor-level/utils"
	"reflect"
	"testing"
	"time"

	"golang.52tt.com/clients/account"
	anchorcheck "golang.52tt.com/clients/anchor-check"
	anchorcontract_go "golang.52tt.com/clients/anchorcontract-go"
	apicenter "golang.52tt.com/clients/apicenter/apiserver"
	"golang.52tt.com/clients/channel"
	channellivefans "golang.52tt.com/clients/channel-live-fans"
	channellivemgr "golang.52tt.com/clients/channel-live-mgr"
	channellivestats "golang.52tt.com/clients/channel-live-stats"
	channellottery "golang.52tt.com/clients/channel-lottery"
	channel_recommend_svr "golang.52tt.com/clients/channel-recommend-svr"
	channelvotepkgo "golang.52tt.com/clients/channel-vote-pk-go"
	"golang.52tt.com/clients/channelbackground"
	"golang.52tt.com/clients/entertainmentrecommendback"
	"golang.52tt.com/clients/public"
	userol "golang.52tt.com/clients/user-online"
	"golang.52tt.com/services/anchor-level/conf"
	"golang.52tt.com/services/anchor-level/report"
	"golang.52tt.com/services/anchor-level/store"
)

// go test -timeout 30s -run ^TestNewAnchorLevelMgr$ golang.52tt.com/services/anchor-level/manager -v -count=1
func TestNewAnchorLevelMgr(t *testing.T) {

	now := time.Now()
	tm := now.AddDate(0, 0, -6)
	t.Log(tm)
	tm2 := tm.AddDate(0, 0, -1)
	t.Log(tm2)
	startTs := utils.GetThisWeekBeginTime(tm).AddDate(0, 0, -7).Unix()
	t.Log(startTs)
	return

	settleMonth, _ := time.Parse("2006-01", "2024-01")
	nowMonthBegin := time.Date(settleMonth.Year(), settleMonth.Month()+1, 1, 0, 0, 0, 0, time.Local)
	nowMontnEnd := time.Date(settleMonth.Year(), settleMonth.Month()+1+1, 1, 0, 0, 0, 0, time.Local).Add(-time.Second)

	t.Log(nowMonthBegin)
	t.Log(nowMontnEnd)
	return

	/*cfg, err := config.NewConfig("json", "../anchor-level.json")
	if err != nil {
		log.Errorln("Failed to init ServerConfig from file:")
		return
	}



	env := cfg.String("env")
	push, _ := cfg.Bool("push")
	warnFeishuUrl := cfg.String("warn_feishu_url")

	reporter := report.NewFeiShuReporterV2(warnFeishuUrl, env, push)

	mgr, err := NewAnchorLevelMgr(cfg, reporter)
	if err != nil {
		log.Fatalln(err)
		return
	}
	t.Log(mgr)
	
	*/
}

func TestAnchorLevelMgr_ShutDown(t *testing.T) {
	type fields struct {
		store                      store.IStore
		dyconfig                   conf.ISDyConfigHandler
		LocalCache                 *LocalCache
		channelLiveMgr             channellivemgr.IClient
		channelLiveStats           channellivestats.IClient
		channelRecommendSvr        channel_recommend_svr.IClient
		entertainmentRecommendBack entertainmentrecommendback.IClient
		anchorcontractGoClient     anchorcontract_go.IClient
		reporter                   report.IFeishuReporterV2
		accountCli                 account.IClient
		channelbackgroundCli       channelbackground.IClient
		channelLotteryCli          channellottery.IClient
		channelLiveFansCli         channellivefans.IClient
		channelVotePkGoCli         channelvotepkgo.IClient
		publicCli                  public.IClient
		apiCli                     apicenter.IClient
		userOlCli                  userol.IClient
		channelCli                 channel.IClient
		anchorCheckCli             anchorcheck.IClient
	}
	tests := []struct {
		name   string
		fields fields
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &AnchorLevelMgr{
				store:                      tt.fields.store,
				dyconfig:                   tt.fields.dyconfig,
				LocalCache:                 tt.fields.LocalCache,
				channelLiveMgr:             tt.fields.channelLiveMgr,
				channelLiveStats:           tt.fields.channelLiveStats,
				channelRecommendSvr:        tt.fields.channelRecommendSvr,
				entertainmentRecommendBack: tt.fields.entertainmentRecommendBack,
				anchorcontractGoClient:     tt.fields.anchorcontractGoClient,
				reporter:                   tt.fields.reporter,
				accountCli:                 tt.fields.accountCli,
				channelbackgroundCli:       tt.fields.channelbackgroundCli,
				channelLotteryCli:          tt.fields.channelLotteryCli,
				channelLiveFansCli:         tt.fields.channelLiveFansCli,
				channelVotePkGoCli:         tt.fields.channelVotePkGoCli,
				publicCli:                  tt.fields.publicCli,
				apiCli:                     tt.fields.apiCli,
				userOlCli:                  tt.fields.userOlCli,
				channelCli:                 tt.fields.channelCli,
				anchorCheckCli:             tt.fields.anchorCheckCli,
			}
			m.ShutDown()
		})
	}
}

func TestAnchorLevelMgr_GetStore(t *testing.T) {
	type fields struct {
		store                      store.IStore
		dyconfig                   conf.ISDyConfigHandler
		LocalCache                 *LocalCache
		channelLiveMgr             channellivemgr.IClient
		channelLiveStats           channellivestats.IClient
		channelRecommendSvr        channel_recommend_svr.IClient
		entertainmentRecommendBack entertainmentrecommendback.IClient
		anchorcontractGoClient     anchorcontract_go.IClient
		reporter                   report.IFeishuReporterV2
		accountCli                 account.IClient
		channelbackgroundCli       channelbackground.IClient
		channelLotteryCli          channellottery.IClient
		channelLiveFansCli         channellivefans.IClient
		channelVotePkGoCli         channelvotepkgo.IClient
		publicCli                  public.IClient
		apiCli                     apicenter.IClient
		userOlCli                  userol.IClient
		channelCli                 channel.IClient
		anchorCheckCli             anchorcheck.IClient
	}
	tests := []struct {
		name   string
		fields fields
		want   store.IStore
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &AnchorLevelMgr{
				store:                      tt.fields.store,
				dyconfig:                   tt.fields.dyconfig,
				LocalCache:                 tt.fields.LocalCache,
				channelLiveMgr:             tt.fields.channelLiveMgr,
				channelLiveStats:           tt.fields.channelLiveStats,
				channelRecommendSvr:        tt.fields.channelRecommendSvr,
				entertainmentRecommendBack: tt.fields.entertainmentRecommendBack,
				anchorcontractGoClient:     tt.fields.anchorcontractGoClient,
				reporter:                   tt.fields.reporter,
				accountCli:                 tt.fields.accountCli,
				channelbackgroundCli:       tt.fields.channelbackgroundCli,
				channelLotteryCli:          tt.fields.channelLotteryCli,
				channelLiveFansCli:         tt.fields.channelLiveFansCli,
				channelVotePkGoCli:         tt.fields.channelVotePkGoCli,
				publicCli:                  tt.fields.publicCli,
				apiCli:                     tt.fields.apiCli,
				userOlCli:                  tt.fields.userOlCli,
				channelCli:                 tt.fields.channelCli,
				anchorCheckCli:             tt.fields.anchorCheckCli,
			}
			if got := m.GetStore(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("AnchorLevelMgr.GetStore() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestAnchorLevelMgr_GetChannelLiveMgr(t *testing.T) {
	type fields struct {
		store                      store.IStore
		dyconfig                   conf.ISDyConfigHandler
		LocalCache                 *LocalCache
		channelLiveMgr             channellivemgr.IClient
		channelLiveStats           channellivestats.IClient
		channelRecommendSvr        channel_recommend_svr.IClient
		entertainmentRecommendBack entertainmentrecommendback.IClient
		anchorcontractGoClient     anchorcontract_go.IClient
		reporter                   report.IFeishuReporterV2
		accountCli                 account.IClient
		channelbackgroundCli       channelbackground.IClient
		channelLotteryCli          channellottery.IClient
		channelLiveFansCli         channellivefans.IClient
		channelVotePkGoCli         channelvotepkgo.IClient
		publicCli                  public.IClient
		apiCli                     apicenter.IClient
		userOlCli                  userol.IClient
		channelCli                 channel.IClient
		anchorCheckCli             anchorcheck.IClient
	}
	tests := []struct {
		name   string
		fields fields
		want   channellivemgr.IClient
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &AnchorLevelMgr{
				store:                      tt.fields.store,
				dyconfig:                   tt.fields.dyconfig,
				LocalCache:                 tt.fields.LocalCache,
				channelLiveMgr:             tt.fields.channelLiveMgr,
				channelLiveStats:           tt.fields.channelLiveStats,
				channelRecommendSvr:        tt.fields.channelRecommendSvr,
				entertainmentRecommendBack: tt.fields.entertainmentRecommendBack,
				anchorcontractGoClient:     tt.fields.anchorcontractGoClient,
				reporter:                   tt.fields.reporter,
				accountCli:                 tt.fields.accountCli,
				channelbackgroundCli:       tt.fields.channelbackgroundCli,
				channelLotteryCli:          tt.fields.channelLotteryCli,
				channelLiveFansCli:         tt.fields.channelLiveFansCli,
				channelVotePkGoCli:         tt.fields.channelVotePkGoCli,
				publicCli:                  tt.fields.publicCli,
				apiCli:                     tt.fields.apiCli,
				userOlCli:                  tt.fields.userOlCli,
				channelCli:                 tt.fields.channelCli,
				anchorCheckCli:             tt.fields.anchorCheckCli,
			}
			if got := m.GetChannelLiveMgr(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("AnchorLevelMgr.GetChannelLiveMgr() = %v, want %v", got, tt.want)
			}
		})
	}
}
