package store

import (
	"context"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	mysqlConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/mysql/connect"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"time"
)

type Store struct {
	db *gorm.DB
}

func NewStore(ctx context.Context, dbCfg *mysqlConnect.MysqlConfig) (IStore, error) {
	db, err := NewMysqlCli(ctx, dbCfg)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to NewMysqlCli err: (%v)", err)
		return nil, err
	}
	s := &Store{
		db: db,
	}
	go s.InitTable()
	log.InfoWithCtx(ctx, "NewStore OK")
	return s, nil
}

func NewMysqlCli(ctx context.Context, mysqlCfg *mysqlConnect.MysqlConfig) (*gorm.DB, error) {
	db, err := gorm.Open(mysql.Open(mysqlCfg.ConnectionString()), &gorm.Config{
		Logger:                 logger.Default.LogMode(logger.Silent),
		SkipDefaultTransaction: true,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to newMysqlStore err: (%v)", err)
		return nil, err
	}
	sqlDB, err := db.DB()
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to newMysqlStore err: (%v)", err)
		return nil, err
	}
	if mysqlCfg.MaxIdleConns > 0 {
		sqlDB.SetMaxIdleConns(mysqlCfg.MaxIdleConns)
	}
	if mysqlCfg.MaxOpenConns > 0 {
		sqlDB.SetMaxOpenConns(mysqlCfg.MaxOpenConns)
	}

	if err := sqlDB.Ping(); err != nil {
		log.ErrorWithCtx(ctx, "Failed to newMysqlStore ping err: (%v)", err)
		return nil, err
	}
	log.InfoWithCtx(ctx, "NewMysqlCli %v OK", mysqlCfg.ConnectionString())
	return db.Debug(), nil
}

func (s *Store) Close() error {
	return nil
}

func (s *Store) InitTable() {
	s.GenTable()

	godLv := &UserGodLevel{}
	godLv.CreateTable(s.db)

	godKpi := &UserGodLevelKpi{}
	godKpi.CreateTable(s.db)
}

func (s *Store) GenTable() {
	changeRecord := &UserGodLevelChangeRecord{
		KpiUpdateTime: time.Now(),
	}
	changeRecord.CreateTable(s.db)
	changeRecord.KpiUpdateTime = time.Now().AddDate(0, 1, 0)
	changeRecord.CreateTable(s.db)
}

func (s *Store) Transaction(ctx context.Context, f func(tx *gorm.DB) error) error {
	tx := s.db.Begin()
	var err error
	defer func() {
		if err != nil {
			_ = tx.Rollback()
		}
	}()
	err = f(tx)
	if err != nil {
		log.ErrorWithCtx(ctx, "Transaction fail err %v", err)
		_ = tx.Rollback()
		return err
	}
	err = tx.Commit().Error
	return err
}
