package store

import (
	"golang.52tt.com/pkg/config"
	"github.com/jmoiron/sqlx"
	"context"
	"testing"
)

var store *Store
var (
	ctx = context.Background()
)

func newStore(cfg *config.MysqlConfig) (st *Store, err error) {
	db, err := sqlx.Connect("mysql", cfg.ConnectionString())
	if err != nil {
		return nil, err
	}

	st = &Store{
		db: db,
	}

	return
}

func init() {
	cfg := &config.MysqlConfig{
		Host:     "*************",
		Port:     3306,
		Database: "appsvr",
		Charset:  "utf8mb4",
		UserName: "godman",
		Password: "thegodofman",
	}
	store, _ = newStore(cfg)

	_ = store.createSmashPropConfTable()
}

func TestStore_AddSmashPropConf(t *testing.T) {

	err := store.AddSmashPropConf(ctx, &SmashPropConf{
		PropName: "test",
		PropUrl:  "test",
	})
	if err != nil {
		t.Error(err)
	}
}

func TestStore_GetAllSmashPropConf(t *testing.T) {

	out, err := store.GetAllSmashPropConf(ctx)
	if err != nil {
		t.Error(err)
	}

	for _, v := range out {
		t.Log(v)
	}
}
