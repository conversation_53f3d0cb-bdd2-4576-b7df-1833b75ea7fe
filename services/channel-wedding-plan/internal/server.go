package internal

import (
    "context"
    "fmt"
    "gitlab.ttyuyin.com/avengers/tyr/core/log"
    "golang.52tt.com/pkg/protocol"
    "golang.52tt.com/protocol/common/status"
    pb "golang.52tt.com/protocol/services/channel-wedding-plan"
    "golang.52tt.com/protocol/services/demo/echo"
    reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"
    UnifiedPayCallback "golang.52tt.com/protocol/services/unified_pay/cb"
    "golang.52tt.com/services/channel-wedding-plan/internal/conf"
    "golang.52tt.com/services/channel-wedding-plan/internal/mgr"
    "golang.52tt.com/services/channel-wedding-plan/internal/rpc"
    context0 "golang.org/x/net/context"
    "google.golang.org/grpc/codes"

    "golang.52tt.com/services/channel-wedding-plan/internal/cache"

    "golang.52tt.com/services/channel-wedding-plan/internal/event"
    "golang.52tt.com/services/channel-wedding-plan/internal/store"
    "golang.52tt.com/services/tt-rev/common/goroutineex"
    "time"
)

var (
    ErrParamInValid      = protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
    ReconcileMaxDuration = 3 * 24 * time.Hour // 对账最大可查询范围时长
    ErrTimeRangeOver     = protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "time range over")
)

func NewServer(ctx context.Context, cfg *conf.StartConfig) (*Server, error) {
    log.Infof("server startup with cfg: %+v", *cfg)

    s := &Server{}

    cache_, err := cache.NewCache(ctx, cfg.RedisConfig)
    if nil != err {
        log.ErrorWithCtx(ctx, "init redis fail, err: %v", err)
        return nil, err
    }
    s.cache = cache_

    store_, err := store.NewStore(ctx, cfg.MongoConfig)
    if nil != err {
        log.ErrorWithCtx(ctx, "init mongo fail, err: %v", err)
        return nil, err
    }
    s.dao = store_

    s.bc, err = conf.NewBusinessDyConf()
    if nil != err {
        log.ErrorWithCtx(ctx, "init business dynamic config fail, err: %v", err)
        return nil, err
    }

    s.rpc = rpc.NewRpc()
    s.mgr = mgr.NewWeddingPlanMgr(s.dao, s.cache, s.rpc, s.bc)

    kfk, err := event.NewKafkaEvent(cfg, s.mgr)
    if nil != err {
        log.ErrorWithCtx(ctx, "init kafka fail, err: %v", err)
        return nil, err
    }
    s.kfk = kfk

    return s, nil
}

type Server struct {
    cache *cache.Cache
    dao   *store.Store
    mgr   *mgr.WeddingPlanMgr
    bc    *conf.BusinessDyConf
    rpc   *rpc.Rpc

    kfk *event.KafkaEvent
}

func (s *Server) BatchGetWeddingRole(c context0.Context, request *pb.BatchGetWeddingRoleRequest) (*pb.BatchGetWeddingRoleResponse, error) {
    out := &pb.BatchGetWeddingRoleResponse{}
    defer func() {
        log.DebugWithCtx(c, "BatchGetWeddingRole response: %+v, request: %+v", out, request)
    }()
    if request.GetUidList() == nil || request.GetPlanId() == 0 {
        log.ErrorWithCtx(c, "BatchGetWeddingRole fail to get uidList or planId, request: %+v", request)
        return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
    }
    WeddingRoleMap, err := s.mgr.BatchGetWeddingRole(c, request.GetPlanId(), request.GetUidList())
    if err != nil {
        log.ErrorWithCtx(c, "BatchGetWeddingRole fail to BatchGetWeddingRole. req: %+v, err:%v", request, err)
        return out, err
    }
    out.WeddingRoleMap = WeddingRoleMap
    return out, nil
}

func (s *Server) RevokePropose(c context0.Context, request *pb.RevokeProposeRequest) (*pb.RevokeProposeResponse, error) {
    out := &pb.RevokeProposeResponse{}
    defer func() {
        log.DebugWithCtx(c, "RevokePropose response: %+v, request: %+v", out, request)
    }()

    if request.GetUid() == 0 {
        log.ErrorWithCtx(c, "RevokePropose fail to get uid")
        return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
    }
    err := s.mgr.RevokePropose(c, request.GetUid())
    if err != nil {
        log.ErrorWithCtx(c, "RevokePropose fail to RevokePropose. req: %+v, err:%v", request, err)
        return out, err
    }
    return out, nil
}

func (s *Server) GetChannelReserveTimeSectionConf(ctx context.Context, request *pb.GetChannelReserveTimeSectionConfRequest) (*pb.GetChannelReserveTimeSectionConfResponse, error) {
    resp, err := s.mgr.GetChannelReserveTimeSectionConf(ctx, request)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetChannelReserveTimeSectionConf fail to GetChannelReserveTimeSectionConf. req:  %+v, err:%v", request, err)
    }
    return resp, err
}

func (s *Server) SetAdminChannelReserveTimeSectionSwitch(ctx context.Context, request *pb.SetAdminChannelReserveTimeSectionSwitchRequest) (*pb.SetAdminChannelReserveTimeSectionSwitchResponse, error) {
    resp, err := s.mgr.SetAdminChannelReserveTimeSectionSwitch(ctx, request)
    if err != nil {
        log.ErrorWithCtx(ctx, "SetAdminChannelReserveTimeSectionSwitch fail to SetAdminChannelReserveTimeSectionSwitch. req:  %+v, err:%v", request, err)
    }
    return resp, err
}

func (s *Server) AdminCancelWedding(ctx context.Context, request *pb.AdminCancelWeddingRequest) (*pb.AdminCancelWeddingResponse, error) {
    resp := &pb.AdminCancelWeddingResponse{}
    bErr := protocol.NewExactServerError(codes.OK, status.ErrChannelWeddingPlanCommonError, "婚礼即将开始/婚礼中不允许取消哦")

    plan, err := s.mgr.GetWeddingPlanByIdNoCheck(ctx, request.GetWeddingPlanId(), false)
    if err != nil {
        log.ErrorWithCtx(ctx, "AdminCancelWedding fail to GetWeddingPlanByIdNoCheck, request: %+v, err: %v", request, err)
        return resp, nil
    }

    if plan.ReserveInfo == nil {
        return resp, nil
    }

    if plan.Status != store.WeddingPlanStatusInit {
        return resp, bErr
    }
    reserveSt := time.Unix(int64(plan.ReserveInfo.StartTime), 0)
    if time.Now().Add(time.Hour).After(reserveSt) {
        return resp, bErr
    }

    err = s.mgr.CancelWedding(ctx, &pb.CancelWeddingRequest{
        Uid:           plan.BuyerUid,
        WeddingPlanId: request.GetWeddingPlanId(),
    }, false)
    if err != nil {
        log.ErrorWithCtx(ctx, "AdminCancelWedding fail to CancelWedding, request: %+v, err: %v", request, err)
        return resp, err
    }

    err = s.mgr.MarkAdminCancel(ctx, request.GetWeddingPlanId())
    if err != nil {
        log.ErrorWithCtx(ctx, "AdminCancelWedding fail to MarkAdminCancel, request: %+v, err: %v", request, err)
        return resp, err
    }

    channelInfo, err := s.rpc.ChannelCli.GetChannelSimpleInfo(ctx, 0, plan.ReserveInfo.ChannelId)
    if err != nil {
        log.ErrorWithCtx(ctx, "AdminCancelWedding fail to GetChannelSimpleInfo, request: %+v, err: %v", request, err)
        return resp, err
    }

    s.mgr.SendTTAssistantText(ctx, plan.BuyerUid,
        fmt.Sprintf("您在“%s”殿堂房间预约的于%s举行的婚礼已被自动取消，婚礼预约金将自动退还至原帐号，有问题可以和殿堂房的管理员和平台客服进一步沟通哦～", channelInfo.GetName(), reserveSt.Format("01月02日")), "", "")
    return resp, nil
}

func (s *Server) BatchHotWeddingPlan(ctx context.Context, request *pb.BatchHotWeddingPlanRequest) (*pb.BatchHotWeddingPlanResponse, error) {
    resp := &pb.BatchHotWeddingPlanResponse{
        HotWeddingMap: make(map[uint32]bool),
    }

    for _, planId := range request.GetWeddingPlanId() {
        planInfo, err := s.mgr.GetWeddingPlanByIdNoCheck(ctx, planId, request.GetWithCache())
        if err != nil {
            log.ErrorWithCtx(ctx, "batch hot wedding plan fail, err: %v, planId: %+v", err)
            continue
        }
        resp.HotWeddingMap[planId] = planInfo.IsHot
    }
    log.DebugWithCtx(ctx, "BatchHotWeddingPlan success, request: %+v, response: %+v", request, resp)
    return resp, nil
}

func checkReconcileTime(req *reconcile_v2.TimeRangeReq) (time.Time, time.Time, error) {
    beginTime := time.Unix(req.GetBeginTime(), 0)
    endTime := time.Unix(req.GetEndTime(), 0)
    if beginTime.After(endTime) || beginTime.Add(ReconcileMaxDuration).Before(endTime) {
        return beginTime, endTime, ErrTimeRangeOver
    }

    return beginTime, endTime, nil
}

func (s *Server) GetTBeanTotalCount(c context.Context, in *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error) {
    out := &reconcile_v2.CountResp{}
    beginTime, endTime, err := checkReconcileTime(in)
    if err != nil {
        log.WarnWithCtx(c, "GetTBeanTotalCount fail to checkReconcileTime. in:%+v, err:%v", in, err)
        return out, err
    }
    out, err = s.mgr.GetConsumeTotalCount(c, beginTime, endTime)
    if err != nil {
        log.ErrorWithCtx(c, "GetConsumeTotalCount fail to GetConsumeTotalCountInfo. beginTime:%v, err:%v", beginTime, err)
        return out, err
    }

    return out, nil
}

func (s *Server) GetTBeanOrderIds(c context.Context, in *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error) {
    out := &reconcile_v2.OrderIdsResp{}
    beginTime, endTime, err := checkReconcileTime(in)
    if err != nil {
        log.WarnWithCtx(c, "GetTBeanTotalCount fail to checkReconcileTime. in:%+v, err:%v", in, err)
        return out, err
    }
    out, err = s.mgr.GetConsumeOrderIds(c, beginTime, endTime)
    if err != nil {
        log.ErrorWithCtx(c, "GetConsumeOrderIds fail to GetConsumeOrderIds. beginTime:%v, err:%v", beginTime, err)
        return out, err
    }

    return out, nil
}
func (s *Server) GetMyWeddingRole(ctx context.Context, request *pb.GetMyWeddingRoleRequest) (*pb.GetMyWeddingRoleResponse, error) {
    resp, err := s.mgr.GetMyWeddingRole(ctx, request)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetMyWeddingRole fail to GetMyWeddingRole. req: %+v, err:%v", request, err)
        return resp, err
    }

    return resp, nil
}

func (s *Server) ManualNotifyWeddingStart(c context0.Context, request *pb.ManualNotifyWeddingStartRequest) (*pb.ManualNotifyWeddingStartResponse, error) {
    resp := &pb.ManualNotifyWeddingStartResponse{}
    weddingPlan, err := s.mgr.GetWeddingPlanByIdNoCheck(c, request.WeddingPlanId, false)
    if err != nil {
        log.ErrorWithCtx(c, "ManualNotifyWeddingStart fail to GetWeddingPlanById. req: %+v, err:%v", request, err)
        return resp, err
    }

    err = s.mgr.ManualNotifyGroomAndBridWeddingStarting(c, weddingPlan)
    if err != nil {
        log.ErrorWithCtx(c, "ManualNotifyWeddingStart fail to ManualNotifyGroomAndBridWeddingStarting. req: %+v, err:%v", request, err)
        return resp, err
    }

    return resp, nil
}

func (s *Server) GetUserDivorceStatus(c context0.Context, request *pb.GetUserDivorceStatusRequest) (*pb.GetUserDivorceStatusResponse, error) {
    out := &pb.GetUserDivorceStatusResponse{}
    defer func() {
        log.DebugWithCtx(c, "GetUserDivorceStatus response: %+v, request: %+v", out, request)
    }()
    if request.GetUid() == 0 {
        log.ErrorWithCtx(c, "GetUserDivorceStatus req.uid is 0, request: %+v", request)
        return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
    }

    out, err := s.mgr.GetUserDivorceStatus(c, request.Uid)
    if err != nil {
        log.ErrorWithCtx(c, "GetUserDivorceStatus fail to GetDivorceStatus. req: %+v, err:%v", request, err)
        return out, err
    }
    return out, nil
}

func (s *Server) Notify(c context0.Context, notify *UnifiedPayCallback.PayNotify) (*UnifiedPayCallback.PayNotifyResponse, error) {
    out := &UnifiedPayCallback.PayNotifyResponse{}
    defer func() {
        log.InfoWithCtx(c, "Notify response: %+v, request: %+v", out, notify)
    }()
    op, err := s.mgr.Callback(c, notify.GetUid(), notify.GetOutTradeNo(), mgr.CallbackSourceNotify)
    if err != nil {
        log.ErrorWithCtx(c, "Notify fail to Callback. err:%v, request:%+v", err, notify)
        return out, err
    }
    out.Op = op
    return out, nil
}

func (s *Server) GetThemeCfgById(c context0.Context, request *pb.GetThemeCfgByIdRequest) (*pb.GetThemeCfgByIdResponse, error) {
    return &pb.GetThemeCfgByIdResponse{}, nil
}

func (s *Server) ApplyEndWeddingRelation(c context0.Context, request *pb.ApplyEndWeddingRelationRequest) (*pb.ApplyEndWeddingRelationResponse, error) {
    out := &pb.ApplyEndWeddingRelationResponse{}
    defer func() {
        log.InfoWithCtx(c, "ApplyEndWeddingRelation response: %+v, request: %+v", out, request)
    }()
    out, err := s.mgr.ApplyEndWeddingRelation(c, request.GetUid())
    if err != nil {
        log.ErrorWithCtx(c, "ApplyEndWeddingRelation fail to ApplyEndWeddingRelation. err:%v, request:%+v", err, request)
        return out, err
    }
    return out, nil
}

func (s *Server) CancelEndWeddingRelation(c context0.Context, request *pb.CancelEndWeddingRelationRequest) (*pb.CancelEndWeddingRelationResponse, error) {
    out := &pb.CancelEndWeddingRelationResponse{}
    defer func() {
        log.InfoWithCtx(c, "CancelEndWeddingRelation response: %+v, request: %+v", out, request)
    }()
    err := s.mgr.CancelEndWeddingRelation(c, request.GetUid())
    if err != nil {
        log.ErrorWithCtx(c, "CancelEndWeddingRelation fail to CancelEndWeddingRelation. err:%v, request:%+v", err, request)
        return out, err
    }
    return out, nil
}

func (s *Server) BuyWedding(c context0.Context, request *pb.BuyWeddingRequest) (*pb.BuyWeddingResponse, error) {
    out := &pb.BuyWeddingResponse{}
    defer func() {
        log.InfoWithCtx(c, "BuyWedding response: %+v, request: %+v", out, request)
    }()
    id, err := s.mgr.BuyWedding(c, request)
    if err != nil {
        log.ErrorWithCtx(c, "BuyWedding fail to BuyWedding. err:%v, request:%+v", err, request)
        return out, err
    }
    out.WeddingPlanId = id
    return out, nil
}

func (s *Server) CancelWedding(c context0.Context, request *pb.CancelWeddingRequest) (*pb.CancelWeddingResponse, error) {
    out := &pb.CancelWeddingResponse{}
    defer func() {
        log.InfoWithCtx(c, "CancelWedding response: %+v, request: %+v", out, request)
    }()
    return out, s.mgr.CancelWedding(c, request, true)
}

func (s *Server) GetMyWeddingInfo(c context0.Context, request *pb.GetMyWeddingInfoRequest) (*pb.GetMyWeddingInfoResponse, error) {
    out := &pb.GetMyWeddingInfoResponse{}
    defer func() {
        log.InfoWithCtx(c, "GetMyWeddingInfo response: %+v, request: %+v", out, request)
    }()
    return s.mgr.GetMyWeddingInfo(c, request)
}

func (s *Server) GetAllThemeCfg(c context0.Context, request *pb.GetAllThemeCfgRequest) (*pb.GetAllThemeCfgResponse, error) {
    out := &pb.GetAllThemeCfgResponse{}
    defer func() {
        log.InfoWithCtx(c, "GetAllThemeCfg response: %+v, request: %+v", out, request)
    }()
    return s.mgr.GetAllThemeCfg(c)
}

func (s *Server) AddThemeCfg(c context0.Context, request *pb.AddThemeCfgRequest) (*pb.AddThemeCfgResponse, error) {
    out := &pb.AddThemeCfgResponse{}
    defer func() {
        log.InfoWithCtx(c, "AddThemeCfg response: %+v, request: %+v", out, request)
    }()
    return out, s.mgr.AddThemeCfg(c, request)
}

func (s *Server) UpdateThemeCfg(c context0.Context, request *pb.UpdateThemeCfgRequest) (*pb.UpdateThemeCfgResponse, error) {
    out := &pb.UpdateThemeCfgResponse{}
    defer func() {
        log.InfoWithCtx(c, "UpdateThemeCfg response: %+v, request: %+v", out, request)
    }()

    if request.GetThemeCfg().GetThemeId() == 0 {
        log.ErrorWithCtx(c, "UpdateThemeCfg themeId is 0, request: %+v", request)
        return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "themeId is 0")
    }
    return out, s.mgr.UpdateThemeCfg(c, request)
}

func (s *Server) DeleteThemeCfg(c context0.Context, request *pb.DeleteThemeCfgRequest) (*pb.DeleteThemeCfgResponse, error) {
    out := &pb.DeleteThemeCfgResponse{}
    defer func() {
        log.InfoWithCtx(c, "DeleteThemeCfg response: %+v, request: %+v", out, request)
    }()

    if request.GetThemeId() == 0 {
        log.ErrorWithCtx(c, "DeleteThemeCfg themeId is 0, request: %+v", request)
        return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "themeId is 0")
    }

    return out, s.mgr.DeleteThemeCfg(c, request)
}

func (s *Server) ShutDown() {
    _ = s.cache.Close()

    _ = s.dao.Close(context.Background())
}

func (s *Server) Echo(ctx context.Context, req *echo.StringMessage) (*echo.StringMessage, error) {
    return req, nil
}

func (s *Server) PageGetComingWeddingList(ctx context.Context, in *pb.PageGetComingWeddingListRequest) (*pb.PageGetComingWeddingListResponse, error) {
    return s.mgr.PageGetComingWeddingList(ctx, in)
}

func (s *Server) BatGetWeddingInfoById(ctx context.Context, in *pb.BatGetWeddingInfoByIdRequest) (*pb.BatGetWeddingInfoByIdResponse, error) {
    out := &pb.BatGetWeddingInfoByIdResponse{}
    weddingInfoMap, err := s.mgr.BatGetWeddingInfoById(ctx, in.WeddingPlanIdList)
    if err != nil {
        log.ErrorWithCtx(ctx, "BatGetWeddingInfoById err: %v, in: %+v", err, in)
    } else {
        out.WeddingInfoMap = weddingInfoMap
        log.DebugWithCtx(ctx, "BatGetWeddingInfoById success, in: %+v, out: %+v", in, out)
    }
    return out, err
}

func (s *Server) SubscribeWedding(ctx context.Context, in *pb.SubscribeWeddingRequest) (*pb.SubscribeWeddingResponse, error) {
    out := &pb.SubscribeWeddingResponse{}
    err := s.mgr.SubscribeWedding(ctx, in.Uid, in.WeddingPlanId)
    if err != nil {
        log.ErrorWithCtx(ctx, "SubscribeWedding err: %v, in: %+v", err, in)
    } else {
        log.InfoWithCtx(ctx, "SubscribeWedding success, in: %+v", in)
    }
    return out, err
}

func (s *Server) BatGetWeddingSubscribeStatus(ctx context.Context, in *pb.BatGetWeddingSubscribeStatusRequest) (*pb.BatGetWeddingSubscribeStatusResponse, error) {
    out := &pb.BatGetWeddingSubscribeStatusResponse{}
    statusMap, err := s.mgr.BatGetWeddingSubscribeStatus(ctx, in.Uid, in.WeddingPlanIdList)
    if err != nil {
        log.ErrorWithCtx(ctx, "BatGetWeddingSubscribeStatus err: %v, in: %+v", err, in)
    } else {
        out.SubscribeStatusMap = statusMap
        log.DebugWithCtx(ctx, "BatGetWeddingSubscribeStatus success, in: %+v, out: %+v", in, out)
    }
    return out, err
}

func (s *Server) GetWeddingBigScreen(ctx context.Context, in *pb.GetWeddingBigScreenRequest) (*pb.GetWeddingBigScreenResponse, error) {
    out := &pb.GetWeddingBigScreenResponse{}
    bigScreenList, err := s.mgr.GetWeddingBigScreen(ctx, in.WeddingPlanId)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetWeddingBigScreen err: %v, in: %+v", err, in)
    } else {
        out.BigScreenList = bigScreenList
        log.DebugWithCtx(ctx, "GetWeddingBigScreen success, in: %+v, out: %+v", in, out)
    }
    return out, err
}

func (s *Server) SaveWeddingBigScreen(ctx context.Context, in *pb.SaveWeddingBigScreenRequest) (*pb.SaveWeddingBigScreenResponse, error) {
    return &pb.SaveWeddingBigScreenResponse{}, s.mgr.SaveWeddingBigScreen(ctx, in)
}

func (s *Server) UpdateWeddingBigScreenReviewStatus(ctx context.Context, in *pb.UpdateWeddingBigScreenReviewStatusRequest) (*pb.UpdateWeddingBigScreenReviewStatusResponse, error) {
    return &pb.UpdateWeddingBigScreenReviewStatusResponse{}, s.mgr.UpdateWeddingBigScreenReviewStatus(ctx, in)
}

func (s *Server) SetUserRelationHideStatus(c context.Context, request *pb.SetUserRelationHideStatusRequest) (*pb.SetUserRelationHideStatusResponse, error) {
    out := &pb.SetUserRelationHideStatusResponse{}

    if request.GetUid() == 0 || request.GetOpType() == 0 {
        return out, nil
    }

    err := s.mgr.SetRelationHideSwitch(c, request.Uid, request.OpType == uint32(pb.HideOpType_HIDE_OP_TYPE_HIDE))
    if err != nil {
        log.ErrorWithCtx(c, "SetUserRelationHideStatus fail to SetRelationHideSwitch. req: %+v, err:%v", request, err)
        return out, err
    }

    out.HideStatus = request.GetOpType()
    log.InfoWithCtx(c, "SetUserRelationHideStatus req: %+v, err:%v", request, err)
    return out, nil
}

func (s *Server) GetMarriageStatus(c context.Context, request *pb.GetMarriageStatusRequest) (*pb.GetMarriageStatusResponse, error) {
    out := &pb.GetMarriageStatusResponse{}

    // 获取婚姻关系信息
    relationInfo, err := s.mgr.GetUserMarriageInfo(c, request.Uid)
    if err != nil {
        log.ErrorWithCtx(c, "GetMarriageStatus fail to GetUserMarriageInfo. uid:%d, err:%v", request.Uid, err)
        return out, err
    }

    if relationInfo == nil {
        return out, nil
    }

    out.RelationInfo = relationInfo

    // 获取用户隐藏开关状态
    isHide, err := s.mgr.GetRelationHideSwitch(c, request.Uid)
    if err != nil {
        log.ErrorWithCtx(c, "GetMarriageStatus fail to GetRelationHideSwitch. uid:%d, err:%v", request.Uid, err)
        return out, err
    }
    out.IsHide = isHide

    // 获取离婚冻结期相关信息
    statusResp, err := s.mgr.GetUserDivorceStatus(c, request.Uid)
    if err != nil {
        log.ErrorWithCtx(c, "GetMarriageStatus fail to GetUserDivorceStatus. uid:%d, err:%v", request.Uid, err)
        return out, err
    }
    out.AutoDivorceDay = statusResp.GetAutoDivorceDay()
    out.DivorceDeadline = statusResp.GetDivorceDeadline()

    return out, nil
}

func (s *Server) TestWeddingAnniversaryPopup(c context0.Context, request *pb.TestWeddingAnniversaryPopupRequest) (*pb.TestWeddingAnniversaryPopupResponse, error) {
    out := &pb.TestWeddingAnniversaryPopupResponse{}
    now := time.Now()
    nowDay := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local)

    if request.GetOpType() != pb.TestWeddingAnniversaryPopupRequest_OP_TYPE_PUSH {
        if request.GetTargetTime() < nowDay.Unix() {
            log.ErrorWithCtx(c, "TestWeddingAnniversaryPopup ErrParamInValid req:%+v", request)
            return out, ErrParamInValid
        }
    }

    targetDayTime := time.Unix(request.GetTargetTime(), 0)

    switch request.GetOpType() {
    case pb.TestWeddingAnniversaryPopupRequest_OP_TYPE_GEN_POPUP:
        goroutineex.GoroutineWithTimeoutCtx(c, 5*time.Second, func(ctx context.Context) {
            s.mgr.GenNextDayAnniversaryPopupInfo(c, targetDayTime)
        })

    case pb.TestWeddingAnniversaryPopupRequest_OP_TYPE_ONLINE_EVENT:
        err := s.mgr.UserOnlineEventHandle(c, request.GetUid(), targetDayTime)
        if err != nil {
            log.ErrorWithCtx(c, "TestWeddingAnniversaryPopup UserOnlineEventHandle failed,  req:%+v, err:%v", request, err)
            return out, err
        }
    case pb.TestWeddingAnniversaryPopupRequest_OP_TYPE_PUSH:
        err := s.mgr.PushAnniversaryPopup(c, request.GetUid(), int(request.GetDay()))
        if err != nil {
            log.ErrorWithCtx(c, "TestWeddingAnniversaryPopup PushAnniversaryPopup failed,  req:%+v, err:%v", request, err)
            return out, err
        }

    default:
        log.ErrorWithCtx(c, "TestWeddingAnniversaryPopup ErrParamInValid req:%+v", request)
        return out, ErrParamInValid
    }

    log.InfoWithCtx(c, "TestWeddingAnniversaryPopup success, req:%+v", request)
    return out, nil
}
