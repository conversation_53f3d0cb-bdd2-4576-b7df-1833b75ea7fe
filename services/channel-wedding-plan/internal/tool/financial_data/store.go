package main

import (
    "context"
    "database/sql"
    "fmt"
    mysqlConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/mysql/connect"
    "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mongo"
    "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
    "go.mongodb.org/mongo-driver/bson"
    "golang.52tt.com/pkg/config"
    "golang.52tt.com/pkg/log"
    "golang.52tt.com/services/channel-wedding-plan/internal/store"
    "strconv"
    "strings"
    "time"
)

type financialStore struct {
    db        *mongo.ClientImpl
    orderColl *mongo.MongoCollectionImpl
    planColl  *mongo.MongoCollectionImpl
    mysqlCli  mysql.DBx
}

func newFinancialStore(cfg *config.MongoConfig, mysqlCfg *mysqlConnect.MysqlConfig) *financialStore {
    client, err := mongo.NewClient(context.Background(), cfg.OptionsForReplicaSet())
    if err != nil {
        panic(err)
    }

    coll := client.CreateMongoCollection(cfg.Database, "wedding_order")
    planColl := client.CreateMongoCollection(cfg.Database, "wedding_plan")

    dbCli, err := mysqlConnect.NewClient(context.Background(), mysqlCfg)
    if err != nil {
        panic(err)
    }

    return &financialStore{
        db:        client,
        orderColl: coll,
        planColl:  planColl,
        mysqlCli:  dbCli,
    }
}

//WeddingStatusInit     = iota
//WeddingStatusFreezing // wedding 已冻结
//WeddingStatusCancel   // wedding 提前取消（退款）
//WeddingStatusReserved // wedding 已预约（如果超时未举办，不退款）
//WeddingStatusConfirm  // 已确认
//WeddingStatusRollback // 已回滚

type summaryStu struct {
    ThemeId        uint64 `json:"theme_id"`
    TotalAmount    uint64 `json:"total_amount"`
    TotalOrderCnt  uint64 `json:"total_order_cnt"`
    RefundAmount   uint64 `json:"refund_amount"`
    RefundOrderCnt uint64 `json:"refund_order_cnt"`
    FinishAmount   uint64 `json:"finish_amount"`
    FinishOrderCnt uint64 `json:"finish_order_cnt"`
    FreezingCnt    uint64 `json:"freezing_cnt"`
    FreezingAmount uint64 `json:"freezing_amount"`
}

func (s *financialStore) GetSummary(ctx context.Context, beginTime, endTime time.Time) map[uint32]*summaryStu {
    todayFilter := bson.M{
        "$gte": beginTime.Unix(),
        "$lt":  endTime.Unix(),
    }

    commonFilter := bson.M{
        "wedding_price_type": 2,
        "create_ts":          todayFilter,
    }
    // totalFilter gen from commonFilter, add more filter
    totalFilter := commonFilter
    totalFilter["wedding_status"] = bson.M{"$in": []int{1, 2, 3, 4, 5}}

    totalAmountMap, err := s.getSum(ctx, totalFilter)
    if err != nil {
        panic(err)
    }

    fmt.Println(totalAmountMap)
    // total order cnt
    totalOrderCntMap, err := s.getOrderCnt(ctx, totalFilter)
    if err != nil {
        panic(err)
    }

    // refund filter
    refundFilter := commonFilter
    refundFilter["wedding_status"] = bson.M{"$in": []int{5}}
    refundAmountMap, err := s.getSum(ctx, refundFilter)
    if err != nil {
        panic(err)
    }

    // refund order cnt
    refundOrderCntMap, err := s.getOrderCnt(ctx, refundFilter)
    if err != nil {
        panic(err)
    }

    // finish filter
    finishFilter := commonFilter
    finishFilter["wedding_status"] = bson.M{"$in": []int{4}}
    finishAmountMap, err := s.getSum(ctx, finishFilter)
    if err != nil {
        panic(err)
    }

    // finish order cnt
    finishOrderCntMap, err := s.getOrderCnt(ctx, finishFilter)
    if err != nil {
        panic(err)
    }

    // freezing filter
    freezingFilter := commonFilter
    freezingFilter["wedding_status"] = bson.M{"$in": []int{1, 3}}
    freezingAmountMap, err := s.getSum(ctx, freezingFilter)
    if err != nil {
        panic(err)
    }

    freezingCntMap, err := s.getOrderCnt(ctx, freezingFilter)
    if err != nil {
        panic(err)
    }

    ret := make(map[uint32]*summaryStu)
    for k, _ := range totalAmountMap {
        ret[k] = &summaryStu{
            ThemeId:        uint64(k),
            TotalAmount:    totalAmountMap[k],
            TotalOrderCnt:  totalOrderCntMap[k],
            RefundAmount:   refundAmountMap[k],
            RefundOrderCnt: refundOrderCntMap[k],
            FinishAmount:   finishAmountMap[k],
            FinishOrderCnt: finishOrderCntMap[k],
            FreezingAmount: freezingAmountMap[k],
            FreezingCnt:    freezingCntMap[k],
        }
    }
    return ret
}

func (s *financialStore) getSum(ctx context.Context, totalFilter bson.M) (map[uint32]uint64, error) {
    pipeline := []bson.M{
        {"$match": totalFilter},
        {"$group": bson.M{
            "_id": "$theme_id",
            "totalAmount": bson.M{
                "$sum": "$wedding_price",
            },
        }},
    }

    // 获取销售流水
    cursor, err := s.orderColl.Collection.Aggregate(ctx, pipeline)
    if err != nil {
        panic(err)
    }
    defer cursor.Close(ctx)

    var result []bson.M
    if err := cursor.All(ctx, &result); err != nil {
        panic(err)
    }
    if len(result) == 0 {
        return nil, nil
    }
    totalAmount := make(map[uint32]uint64)
    for _, item := range result {
        totalAmount[uint32(item["_id"].(int64))] = uint64(item["totalAmount"].(int64))
    }
    return totalAmount, nil
}

func (s *financialStore) getOrderCnt(ctx context.Context, filter bson.M) (map[uint32]uint64, error) {
    pipeline := []bson.M{
        {"$match": filter},
        {"$group": bson.M{
            "_id": "$theme_id",
            "cnt": bson.M{
                "$sum": 1,
            },
        }},
    }
    cursor, err := s.orderColl.Collection.Aggregate(ctx, pipeline)
    if err != nil {
        return nil, err // 改为返回错误
    }
    defer cursor.Close(ctx)

    var result []bson.M
    if err := cursor.All(ctx, &result); err != nil {
        return nil, err // 改为返回错误
    }

    totalAmount := make(map[uint32]uint64)
    for _, item := range result {
        themeId, ok := item["_id"].(int64)
        if !ok {
            log.ErrorWithCtx(ctx, "getOrderCnt: unexpected type for _id: %v", item["_id"])
            continue
        }
        cnt, ok := item["cnt"].(int32)
        if !ok {
            log.ErrorWithCtx(ctx, "getOrderCnt: unexpected type for cnt: %v", item["cnt"])
            continue
        }
        totalAmount[uint32(themeId)] = uint64(cnt)
    }
    return totalAmount, nil
}

func (s *financialStore) getAllOrderDetail(ctx context.Context, beginTime, endTime time.Time) ([]*store.WeddingOrder, error) {
    timeFiler := bson.M{
        "$gte": beginTime.Unix(),
        "$lt":  endTime.Unix(),
    }
    filter := bson.M{
        "create_ts":          timeFiler,
        "wedding_price_type": 2,
        "wedding_status":     bson.M{"$gt": store.WeddingStatusInit},
    }

    res, err := s.orderColl.Collection.Find(ctx, filter)
    if err != nil {
        log.InfoWithCtx(ctx, "getOrderDetail error: %v, filter: %v", err, filter)
        return nil, err
    }

    var rs []*store.WeddingOrder
    for res.Next(ctx) {
        var order store.WeddingOrder
        if err := res.Decode(&order); err != nil {
            log.InfoWithCtx(ctx, "getOrderDetail Decode error: %v", err)
            return nil, err
        }
        rs = append(rs, &order)
    }
    return rs, nil
}

func (s *financialStore) getOrderPlanByIdList(ctx context.Context, idList []uint64) ([]*store.WeddingPlan, error) {
    filter := bson.M{
        "_id": bson.M{"$in": idList},
    }
    res, err := s.planColl.Collection.Find(ctx, filter)
    if err != nil {
        log.InfoWithCtx(ctx, "getOrderDetail error: %v, filter: %v", err, filter)
        return nil, err
    }

    var rs []*store.WeddingPlan
    for res.Next(ctx) {
        var order store.WeddingPlan
        if err := res.Decode(&order); err != nil {
            log.InfoWithCtx(ctx, "getOrderDetail Decode error: %v", err)
            return nil, err
        }
        rs = append(rs, &order)
    }
    return rs, nil
}

func (s *financialStore) getOrderInfoWithPlan(ctx context.Context, beginTime, endTime time.Time) ([]*orderInfoWithPlan, error) {
    orderDetail, err := s.getAllOrderDetail(ctx, beginTime, endTime)
    if err != nil {
        return nil, err
    }
    planIdList := make([]uint64, 0, len(orderDetail))
    for _, order := range orderDetail {
        planIdList = append(planIdList, uint64(order.Id))
    }

    planList, err := s.getOrderPlanByIdList(ctx, planIdList)
    if err != nil {
        return nil, err
    }
    planMap := make(map[uint64]*store.WeddingPlan)
    for _, plan := range planList {
        planMap[uint64(plan.ID)] = plan
    }

    ret := make([]*orderInfoWithPlan, 0, len(orderDetail))
    for _, order := range orderDetail {
        ret = append(ret, &orderInfoWithPlan{
            OrderInfo: order,
            PlanInfo:  planMap[uint64(order.Id)],
        })
    }
    return ret, nil
}

func (s *financialStore) getRecordTimeByWeddingIds(ctx context.Context, weddingIds []uint32) (map[uint32]int64, error) {
    if len(weddingIds) == 0 {
        return nil, nil
    }

    stringIdList := make([]string, 0, len(weddingIds))
    for _, planId := range weddingIds {
        stringIdList = append(stringIdList, strconv.Itoa(int(planId)))
    }

    sql := fmt.Sprintf("SELECT wedding_id, wedding_time FROM channel_wedding_records WHERE wedding_id IN (%s)", strings.Join(stringIdList, ","))
    rows, err := s.mysqlCli.QueryContext(ctx, sql)
    if err != nil {
        return nil, err
    }
    defer rows.Close()

    recordMap := make(map[uint32]int64)
    for rows.Next() {
        var planId uint32
        var weddingTime time.Time
        if err := rows.Scan(&planId, &weddingTime); err != nil {
            return nil, err
        }
        recordMap[planId] = weddingTime.Unix()
    }

    if len(recordMap) != len(weddingIds) {
        log.Infoln("getRecordTimeByWeddingIds len(recordMap) != len(weddingIds),len(recordMap):", len(recordMap), "len(weddingIds):", len(weddingIds))
    } else {
        log.Infoln("getRecordTimeByWeddingIds len(recordMap) == len(weddingIds),len(recordMap):", len(recordMap), "len(weddingIds):", len(weddingIds))
    }
    return recordMap, nil
}

type PresentInfo struct {
    WeddingId uint32    `db:"wedding_id"`
    GiftId    uint32    `db:"gift_id"`
    Status    uint32    `db:"status"`
    SendTime  time.Time `db:"ctime"`
}

func (s *financialStore) getPresentRecordInfo(ctx context.Context, monthTime time.Time) (map[uint32]*PresentInfo, error) {
    sqlStr := fmt.Sprintf("SELECT wedding_id, gift_id, status, ctime FROM channel_wedding_reserve_present_%04d%02d", monthTime.Year(), monthTime.Month())
    log.Infof("getPresentRecordInfo sql: %s", sqlStr)
    rows, err := s.mysqlCli.QueryContext(ctx, sqlStr)
    if err != nil {
        return nil, err
    }
    defer rows.Close()

    presentMap := make(map[uint32]*PresentInfo)
    for rows.Next() {
        var presentInfo PresentInfo
        var mTime sql.NullTime
        if err := rows.Scan(&presentInfo.WeddingId, &presentInfo.GiftId, &presentInfo.Status, &mTime); err != nil {
            return nil, err
        }
        // 如果 mTime.Valid 为 false，则设置为零值
        if mTime.Valid {
            presentInfo.SendTime = mTime.Time
        } else {
            presentInfo.SendTime = time.Time{}
        }
        presentMap[presentInfo.WeddingId] = &presentInfo
    }
    return presentMap, nil
}

type RawGiftInfo struct {
    WeddingId uint32 `db:"wedding_id"`
    GiftId    uint32 `db:"gift_id"`
    Price     uint32 `db:"price"`
}

func (s *financialStore) getGiftSentSuccessCntByTime(ctx context.Context, monthTime time.Time) ([]*RawGiftInfo, error) {
    sql := fmt.Sprintf("SELECT gift_id, wedding_id FROM channel_wedding_reserve_present_%04d%02d where status = 1", monthTime.Year(), monthTime.Month())
    rows, err := s.mysqlCli.QueryContext(ctx, sql)
    if err != nil {
        return nil, err
    }
    defer rows.Close()

    giftInfoList := make([]*RawGiftInfo, 0)
    for rows.Next() {
        var giftInfo RawGiftInfo
        if err := rows.Scan(&giftInfo.GiftId, &giftInfo.WeddingId); err != nil {
            return nil, err
        }
        giftInfoList = append(giftInfoList, &giftInfo)
    }
    return giftInfoList, nil
}

func (s *financialStore) getWeddingId2PlanIdMap(ctx context.Context, planIdList []uint32) (map[uint32]uint32, error) {
    if len(planIdList) == 0 {
        return nil, nil
    }
    stringIdList := make([]string, 0, len(planIdList))
    for _, planId := range planIdList {
        stringIdList = append(stringIdList, strconv.Itoa(int(planId)))
    }
    sql := fmt.Sprintf("SELECT id, plan_id FROM channel_wedding_schedule WHERE plan_id IN (%s)", strings.Join(stringIdList, ","))
    rows, err := s.mysqlCli.QueryContext(ctx, sql)
    if err != nil {
        return nil, err
    }

    defer rows.Close()
    weddingId2PlanId := make(map[uint32]uint32)
    for rows.Next() {
        var id uint32
        var planId uint32
        if err := rows.Scan(&id, &planId); err != nil {
            return nil, err
        }
        weddingId2PlanId[id] = planId
    }
    if len(weddingId2PlanId) != len(planIdList) {
        log.Infoln("getWeddingId2PlanIdMap len(weddingId2PlanId) != len(planIdList),len(weddingId2PlanId):", len(weddingId2PlanId), "len(planIdList):", len(planIdList))
    }
    return weddingId2PlanId, nil
}
