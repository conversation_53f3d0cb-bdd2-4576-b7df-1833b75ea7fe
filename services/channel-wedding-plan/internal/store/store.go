package store

//go:generate quicksilver-cli test interface ../store
//go:generate mockgen -destination=../mocks/store.go -package=mocks golang.52tt.com/services/channel-wedding-plan/internal/store IStore

import (
	"context"
	"errors"
	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mongo"
	"go.mongodb.org/mongo-driver/bson"
	mongo_driver "go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
)

type Store struct {
	client                       *mongo.ClientImpl
	counter                      *mongo.MongoCollectionImpl
	weddingPlanColl              *mongo.MongoCollectionImpl
	weddingGuestInviteColl       *mongo.MongoCollectionImpl
	weddingCfgColl               *mongo.MongoCollectionImpl
	weddingSubscribeColl         *mongo.MongoCollectionImpl
	weddingOrderCollection       *mongo.MongoCollectionImpl
	weddingProposeCollection     *mongo.MongoCollectionImpl
	marriageRelationCollection   *mongo.MongoCollectionImpl
	relationHideSwitchCollection *mongo.MongoCollectionImpl
	weddingReserveImCollection *mongo.MongoCollectionImpl
	channelBanTimeCollection   *mongo.MongoCollectionImpl
	ChannelReserveTimeSectionConfCollection *mongo.MongoCollectionImpl
}

const (
	bizNameWeddingCfg   = "wedding_cfg"
	bizNameWeddingOrder = "wedding_order"
	counter             = "counter"
	weddingGuestInvite  = "wedding_guest_invite"
	weddingPlan         = "wedding_plan"
	weddingSubscribe    = "wedding_subscribe"
	weddingPropose      = "wedding_propose"
	marriageRelation    = "marriage_relation"
	relationHideSwitch  = "relation_hide_switch"
	weddingReserveIm     = "wedding_reserve_im"
	channelBanTime      = "channel_ban_time"
	channelReserveTimeSectionConf = "channel_reserve_time_section_conf"
)

func NewStore(ctx context.Context, cfg *config.MongoConfig) (*Store, error) {
	client, err := mongo.NewClient(ctx, cfg.OptionsForPrimaryPreferred())
	if err != nil {
		return nil, err
	}

	c := &Store{
		client: client,
	}

	dbName := cfg.Database
	c.counter = client.CreateMongoCollection(dbName, counter)
	c.weddingPlanColl = client.CreateMongoCollection(dbName, weddingPlan)
	c.weddingGuestInviteColl = client.CreateMongoCollection(dbName, weddingGuestInvite)
	c.weddingCfgColl = client.CreateMongoCollection(dbName, bizNameWeddingCfg)
	c.weddingSubscribeColl = client.CreateMongoCollection(dbName, weddingSubscribe)
	c.weddingOrderCollection = client.CreateMongoCollection(dbName, bizNameWeddingOrder)
	c.weddingProposeCollection = client.CreateMongoCollection(dbName, weddingPropose)
	c.marriageRelationCollection = client.CreateMongoCollection(dbName, marriageRelation)
	c.relationHideSwitchCollection = client.CreateMongoCollection(dbName, relationHideSwitch)
	c.weddingReserveImCollection = client.CreateMongoCollection(dbName, weddingReserveIm)
	c.channelBanTimeCollection = client.CreateMongoCollection(dbName, channelBanTime)
	c.ChannelReserveTimeSectionConfCollection = client.CreateMongoCollection(dbName, channelReserveTimeSectionConf)

	c.ensureIndex(ctx)
	return c, nil
}

func (s *Store) Close(ctx context.Context) error {
	return s.client.Close(ctx)
}

func (s *Store) ensureIndex(ctx context.Context) {
	s.ensureIndex4WeddingSubscribeRecord(ctx)
	s.ensureIndex4WeddingOrder(ctx)
	s.ensureIndexRelationHideSwitch(ctx)
	_ = s.WeddingPlanEnsureIndex(ctx)
	s.ensureIndexMarriageRelation(ctx)
	s.ensureIndexProposeInfo(ctx)
	s.ensureIndexMarriageRelation(ctx)
	s.ensureIndexWeddingReserveIM(ctx)
	s.ensureIndexChannelBanTime(ctx)
	s.ensureChannelReserveTimeSectionConfIndex(ctx)
}

// IncCounter 自增计数器
func (s *Store) IncCounter(ctx context.Context, bizName string) uint32 {
	rs := s.counter.Collection.FindOneAndUpdate(ctx, bson.M{"_id": bizName}, bson.M{"$inc": bson.M{"seq": 1}}, options.FindOneAndUpdate().SetUpsert(true))
	if rs.Err() != nil && !errors.Is(rs.Err(), mongo_driver.ErrNoDocuments) {
		log.Errorf("IncCounter err: %v", rs.Err())
		return 0
	}
	var counter struct {
		Seq uint32 `bson:"seq"`
	}

	if err := rs.Decode(&counter); err != nil && !errors.Is(err, mongo_driver.ErrNoDocuments) {
		log.Errorf("IncCounter err: %v", err)
		return 0
	}
	return counter.Seq + 1
}
