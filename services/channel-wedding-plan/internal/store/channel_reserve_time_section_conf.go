package store

import (
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"golang.52tt.com/pkg/log"
	"time"
)

const (
	ChannelReserveTimeSectionConfTypeClose = 1
	ChannelReserveTimeSectionConfTypeOpen = 2
)

type ChannelReserveTimeSectionConf struct {
	ID uint32 `bson:"_id"`
	ChannelId uint32 `bson:"channel_id"`
	ReserveSt uint32 `bson:"reserve_st"`
	ReserveEt uint32 `bson:"reserve_et"`
	Type uint32 `bson:"type"`
	IsDelete uint32 `bson:"is_delete"`
	CreateTime uint32 `bson:"create_time"`
	UpdateTime uint32 `bson:"update_time"`
}

func (s *Store) genChannelReserveTimeSectionConfId(ctx context.Context) uint32 {
	return s.IncCounter(context.Background(), channelReserveTimeSectionConf)
}

func (s *Store) ensureChannelReserveTimeSectionConfIndex(ctx context.Context) {
	indexes := []mongo.IndexModel{
		{
			Keys: bson.D{
				{Key: "channel_id", Value: 1},
				{Key: "reserve_st", Value: 1},
				{Key: "reserve_et", Value: 1},
				{Key: "type", Value: 1},
			},
			Options: options.Index().SetUnique(true),
		},
		{
			// 过期索引
			Keys: bson.D{
				{"create_time", 1},
			},
			Options: options.Index().SetExpireAfterSeconds(30*24*60*60), // 30天过期
		},
	}

	err := s.ChannelReserveTimeSectionConfCollection.CreateIndexes(ctx, indexes)
	if err != nil {
		log.ErrorWithCtx(ctx, "ensureChannelReserveTimeSectionConfIndex error: %v", err)
	}
}

func (s *Store) GetOneDayChannelReserveTimeSectionConf(ctx context.Context, channelId uint32, targetDay uint32) ([]*ChannelReserveTimeSectionConf, error) {
	nextDay := targetDay + 24*60*60
	filter := bson.M{
		"channel_id": channelId,
		"reserve_st": bson.M{"$gte": targetDay},
		"reserve_et": bson.M{"$lt": nextDay},
	}

	cursor, err := s.ChannelReserveTimeSectionConfCollection.Collection.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var rs []*ChannelReserveTimeSectionConf
	for cursor.Next(ctx) {
		item := &ChannelReserveTimeSectionConf{}
		if err := cursor.Decode(item); err != nil {
			return nil, err
		}

		rs = append(rs, item)
	}

	return rs, err
}

func (s *Store) InsertCloseChannelReserveTimeSectionConf(ctx context.Context, channelId uint32, reserveSt, reserveEt uint32) error {

	item := &ChannelReserveTimeSectionConf{
		ID: s.genChannelReserveTimeSectionConfId(ctx),
		ChannelId: channelId,
		ReserveSt: reserveSt,
		ReserveEt: reserveEt,
		Type: ChannelReserveTimeSectionConfTypeClose,
		IsDelete: 0,
		CreateTime: uint32(time.Now().Unix()),
		UpdateTime: uint32(time.Now().Unix()),
	}

	_, err := s.ChannelReserveTimeSectionConfCollection.Collection.InsertOne(ctx, item)
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return nil
		}
	}
	return err
}

func (s *Store) DeleteCloseChannelReserveTimeSectionConf(ctx context.Context, channelId uint32, reserveSt, reserveEt uint32) error {
	filter := bson.M{
		"channel_id": channelId,
		"reserve_st": reserveSt,
		"reserve_et": reserveEt,
		"type": ChannelReserveTimeSectionConfTypeClose,
	}

	_, err := s.ChannelReserveTimeSectionConfCollection.Collection.DeleteOne(ctx, filter)
	return err
}
