package store

import (
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"golang.52tt.com/pkg/log"
	"time"
)

type ChannelBanTime struct {
	ID             string `bson:"_id"`
	ChannelId      uint32 `bson:"channel_id"` // 渠道id
	DateTime       uint32 `bson:"date_time"` // 日期时间戳
	StartTime      uint32 `bson:"start_time"` // 开始时间戳
	EndTime        uint32 `bson:"end_time"` // 结束时间戳
	CreateTime     uint32 `bson:"create_time"`
}

func (s *Store) ensureIndexChannelBanTime(ctx context.Context) {
	index := mongo.IndexModel{
		Keys:    bson.D{{Key: "channel_id", Value: 1}, {Key: "date_time", Value: 1}},
		Options: options.Index().SetUnique(true),
	}

	err := s.channelBanTimeCollection.CreateIndexes(ctx, []mongo.IndexModel{index})
	if err != nil {
		log.ErrorWithCtx(ctx, "ensure index channel_ban_time failed: %v", err)
	}
}

func (s *Store) InsertChannelBanTime(ctx context.Context, channelId uint32, dateTime, startTime, endTime uint32) error {
	_, err := s.channelBanTimeCollection.Collection.InsertOne(ctx, ChannelBanTime{
		ChannelId:  channelId,
		DateTime:   dateTime,
		StartTime:  startTime,
		EndTime:    endTime,
		CreateTime: uint32(time.Now().Unix()),
	})

	return err
}

func (s *Store) DelChannelBanTime(ctx context.Context, id string) error {
	_, err := s.channelBanTimeCollection.Collection.DeleteOne(ctx, bson.M{"_id": id})
	return err
}

func (s *Store) GetChannelBanTimeByDateTime(ctx context.Context, channelId uint32, dateTime uint32) ([]*ChannelBanTime, error) {
	cursor, err := s.channelBanTimeCollection.Collection.Find(ctx, bson.M{"channel_id": channelId, "date_time": dateTime})
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	rs := make([]*ChannelBanTime, 0)
	for cursor.Next(ctx) {
		var plan ChannelBanTime
		if err := cursor.Decode(&plan); err != nil {
			return nil, err
		}
		rs = append(rs, &plan)
	}

	return rs, err
}
