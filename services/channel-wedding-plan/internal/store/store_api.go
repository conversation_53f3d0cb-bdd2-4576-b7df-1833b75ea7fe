package store

import(
	context "context"
	time "time"
	primitive "go.mongodb.org/mongo-driver/bson/primitive"
	pb "golang.52tt.com/protocol/services/channel-wedding-plan"
)

type IStore interface {
	AddProposeInfo(ctx context.Context, proposeInfo *ProposeInfo) (primitive.ObjectID,error)
	AddWeddingBigScreen(ctx context.Context, weddingPlanId uint32, item *BigScreenItem) error
	BatGetWeddingPlan(ctx context.Context, idList []uint32) ([]*WeddingPlan,error)
	BatGetWeddingSubscribeStatus(ctx context.Context, uid uint32, weddingPlanIdList []uint32) (map[uint32]bool,error)
	BatchClearReserveInfo(ctx context.Context, planIdList []uint32) error
	BatchGetMarriageRelationByUidList(ctx context.Context, uidList []uint32) (map[uint32]uint32,error)
	BatchGetWeddingOrderByPlanIdList(ctx context.Context, planIdList []uint32) ([]*WeddingOrder,error)
	BatchGetWeddingReserveIMByIdList(ctx context.Context, idList []uint32) ([]*WeddingReserveIM,error)
	BatchUpdateWeddingPlanStatus(ctx context.Context, idList []uint32, status uint32) error
	ChangeConsumeRecordPayInfo(ctx context.Context, outsideTime time.Time, uid uint32, oldStatus []uint32, newStatus uint32, orderId, dealToken, tBeanTimeStr string) (bool,error)
	Close(ctx context.Context) error
	CountWeddingPlanByChannelIdReserveTime(ctx context.Context, channelIds []uint32, startTime, endTime int64) (int64,error)
	CreateThemeCfg(ctx context.Context, cfg *pb.AddThemeCfgRequest) error
	DelChannelBanTime(ctx context.Context, id string) error
	DelWeddingBigScreen(ctx context.Context, weddingPlanId uint32, imgUrl string) error
	DeleteCloseChannelReserveTimeSectionConf(ctx context.Context, channelId uint32, reserveSt, reserveEt uint32) error
	DeleteMarriageRelation(ctx context.Context, fromUid, targetUid uint32) error
	DeleteThemeCfg(ctx context.Context, themeId uint32) error
	Divorce(ctx context.Context, fromUid, targetUid uint32) error
	GenWeddingPlanId(ctx context.Context) uint32
	GenWeddingReserveIMId(ctx context.Context) uint32
	GetAllStartupWeddingPlan(ctx context.Context, startTime int64) ([]*WeddingPlan,error)
	GetChannelBanTimeByDateTime(ctx context.Context, channelId uint32, dateTime uint32) ([]*ChannelBanTime,error)
	GetConsumeOrderIds(ctx context.Context, beginTime, endTime time.Time) ([]string,error)
	GetConsumeTotalCountInfo(ctx context.Context, beginTime, endTime time.Time) (*StCount,error)
	GetGoingPlan(ctx context.Context, uid uint32) (*WeddingPlan,error)
	GetGoingWeddingBridesmaidInvite(ctx context.Context, uid, st uint32) ([]*WeddingGuestInvite,error)
	GetMarriageRelation(ctx context.Context, fromUid, targetUid uint32) (*MarriageRelation,error)
	GetMarriageRelationByIdList(ctx context.Context, idList []string) ([]*MarriageRelation,error)
	GetMarriageRelationByUid(ctx context.Context, uid uint32) (*MarriageRelation,error)
	GetMyProposeInfo(ctx context.Context, uid, status uint32) (*ProposeInfo,error)
	GetNoChannelFreeWeddingPlanByTimeRange(ctx context.Context, st, et uint32, limit int64) ([]*WeddingPlan,error)
	GetOneDayChannelReserveTimeSectionConf(ctx context.Context, channelId uint32, targetDay uint32) ([]*ChannelReserveTimeSectionConf,error)
	GetOrderList(ctx context.Context, orderStatus []uint32, limit int64) ([]*WeddingOrder,error)
	GetProposeInfoByID(ctx context.Context, id string) (propose *ProposeInfo,err error)
	GetProposeInfoByUidAndStatus(ctx context.Context, uid, status uint32) (list []*ProposeInfo,err error)
	GetRelationHideSwitch(ctx context.Context, uid uint32) (bool,uint32,error)
	GetSendProposeInfoByUid(ctx context.Context, uid uint32) (propose *ProposeInfo,err error)
	GetTimoutProposeList(ctx context.Context) (proposeList []*ProposeInfo,err error)
	GetValidWeddingOrderCnt(ctx context.Context, priceType uint32, startTs int64, uid uint32) (uint32,error)
	GetWeddingGuestInviteById(ctx context.Context, inviteId uint32) (*WeddingGuestInvite,error)
	GetWeddingGuestInviteByWeddingPlanId(ctx context.Context, weddingPlanId uint32) ([]*WeddingGuestInvite,error)
	GetWeddingGuestInviteByWeddingPlanIdGuestType(ctx context.Context, weddingPlanId uint32, guestType []uint32) ([]*WeddingGuestInvite,error)
	GetWeddingGuestInviteByWeddingPlanIdGuestTypeUidStatus(ctx context.Context, weddingPlanId, guestType, uid uint32, status []uint32) (*WeddingGuestInvite,error)
	GetWeddingGuestInviteWaitingByInviteIdUid(ctx context.Context, inviteId, uid uint32) (*WeddingGuestInvite,error)
	GetWeddingGuestInviteWaitingByWeddingPlanIdUid(ctx context.Context, weddingPlanId, uid uint32) ([]*WeddingGuestInvite,error)
	GetWeddingOrderById(ctx context.Context, orderId string) (*WeddingOrder,error)
	GetWeddingOrderByPlanId(ctx context.Context, planId uint32) (*WeddingOrder,error)
	GetWeddingPlan(ctx context.Context, weddingPlanId uint32) (*WeddingPlan,error)
	GetWeddingPlanByChannelIdTimeRange(ctx context.Context, channelId, startTime, endTime uint32) ([]*WeddingPlan,error)
	GetWeddingPlanByEndTimeTimeRangeAndStatus(ctx context.Context, startTime, endTime, limit uint32, status []uint32) ([]*WeddingPlan,error)
	GetWeddingPlanById(ctx context.Context, weddingPlanId uint32) (*WeddingPlan,error)
	GetWeddingPlanByThemeTypeTimeRange(ctx context.Context, themeType uint32, startTime, endTime, channelId uint32) ([]*WeddingPlan,error)
	GetWeddingPlanByTimeRangeStatus(ctx context.Context, st, et, limit uint32, status []uint32) ([]*WeddingPlan,error)
	GetWeddingReserveIM(ctx context.Context, id uint32) (*WeddingReserveIM,error)
	GetWeddingReserveIMByCreateTimeType(ctx context.Context, ts uint32, msgType uint32) ([]*WeddingReserveIM,error)
	GetWeddingReserveIMByReserveTimeType(ctx context.Context, ts uint32, msgType uint32) ([]*WeddingReserveIM,error)
	GetWeddingSubscribeList(ctx context.Context, weddingPlanId uint32) ([]uint32,error)
	IncCounter(ctx context.Context, bizName string) uint32
	InsertChannelBanTime(ctx context.Context, channelId uint32, dateTime, startTime, endTime uint32) error
	InsertCloseChannelReserveTimeSectionConf(ctx context.Context, channelId uint32, reserveSt, reserveEt uint32) error
	InsertMarriageRelation(ctx context.Context, fromUid, targetUid uint32) error
	InsertWeddingGuestInvite(ctx context.Context, invite *WeddingGuestInvite) (uint32,error)
	InsertWeddingOrder(ctx context.Context, record *WeddingOrder) error
	InsertWeddingPlan(ctx context.Context, plan *WeddingPlan) error
	InsertWeddingReserveIM(ctx context.Context, reserveIm *WeddingReserveIM) error
	InsertWeddingSubscribeRecord(ctx context.Context, record *WeddingSubscribeRecord) error
	MarkAdminCancel(ctx context.Context, planId uint32) error
	PageGetWeddingList(ctx context.Context, limit, offset, startTs int64) ([]*WeddingPlan,error)
	PageWeddingPlanByChannelIdReserveTime(ctx context.Context, channelIds []uint32, page, pageSize, sortType int, startTime, endTime int64) ([]*WeddingPlan,error)
	PushWeddingBridesmaid(ctx context.Context, weddingPlanId uint32, bridesmaid *WeddingGuest) error
	PushWeddingFriends(ctx context.Context, weddingPlanId uint32, friend *WeddingGuest) error
	PushWeddingGroomsman(ctx context.Context, weddingPlanId uint32, groomsman *WeddingGuest) error
	SetWeddingGuestInvitePlanFinish(ctx context.Context, weddingPlanId uint32) error
	UpdateHolePlanInviteStatus(ctx context.Context, weddingPlanId, status uint32) error
	UpdateOrder2FreezeAndInitPlan(ctx context.Context, outsideTime time.Time, record *WeddingOrder, reserveInfo *ReserveInfo, isHot bool) error
	UpdateOrderStatus(ctx context.Context, planId uint32, oldStatus []uint32, newStatus uint32) (match bool,err error)
	UpdateProposeInfoStatusByID(ctx context.Context, id string, status uint32) error
	UpdateThemeCfg(ctx context.Context, pbCfg *pb.ThemeCfg) error
	UpdateWeddingBigScreenReviewStatus(ctx context.Context, weddingPlanId, reviewStatus uint32, imgUrl string) error
	UpdateWeddingBridesmaid(ctx context.Context, weddingPlanId uint32, bridesmaid []*WeddingGuest) error
	UpdateWeddingFriends(ctx context.Context, weddingPlanId uint32, friends []*WeddingGuest) error
	UpdateWeddingGroomsman(ctx context.Context, weddingPlanId uint32, groomsman []*WeddingGuest) error
	UpdateWeddingGuestInviteStatus(ctx context.Context, uid, inviteId uint32, status uint32) error
	UpdateWeddingGuestInviteStatusByWeddingPlanIdGuestTypeUid(ctx context.Context, weddingPlanId, guestType, uid uint32, status uint32) error
	UpdateWeddingPlanHost(ctx context.Context, weddingPlanId uint32, hostUid uint32) error
	UpdateWeddingPlanReserveInfo(ctx context.Context, weddingPlanId uint32, reserveInfo *ReserveInfo) error
	UpdateWeddingPlanStatusStrict(ctx context.Context, weddingPlanId, oldStatus, newStatus uint32) error
	UpdateWeddingReserveIMStatus(ctx context.Context, idList []uint32, status uint32) error
	UpdateWeddingReserveInfo(ctx context.Context, weddingPlanId uint32, reserveInfo *ReserveInfo) error
	UpsertRelationHideSwitch(ctx context.Context, uid uint32, status uint32) error
	WeddingPlanEnsureIndex(ctx context.Context) error
}

