package store

import (
    "context"
    "errors"
    "go.mongodb.org/mongo-driver/bson"
    "go.mongodb.org/mongo-driver/bson/primitive"
    "go.mongodb.org/mongo-driver/mongo"
    "golang.52tt.com/pkg/log"
    pbLogic "golang.52tt.com/protocol/app/channel_wedding_logic"
    "time"
)

// ProposeInfo 求婚函信息
type ProposeInfo struct {
    ID                primitive.ObjectID `bson:"_id,omitempty"`        // 求婚函id
    FromUid           uint32             `bson:"from_uid"`             // 发起人uid
    TargetUid         uint32             `bson:"target_uid"`           // 接收人uid
    TargetSex         int32              `bson:"target_sex"`           // 接收人性别
    Status            uint32             `bson:"status"`               // 求婚函当前状态  see pb.
    Tips              string             `bson:"tips"`                 // 求婚函tips
    FromServerMsgId   uint64             `bson:"from_server_msg_id"`   // 求婚函信息的id，撤回用
    TargetServerMsgId uint64             `bson:"target_server_msg_id"` // 求婚函信息的id，撤回用
    CreateTime        time.Time          `bson:"create_time"`          // 求婚函发出时间
    UpdateTime        time.Time          `bson:"update_time"`          // 求婚函最后状态变更时间
    EndTime           time.Time          `bson:"end_time"`             // 求婚函截止时间
    ExpiredDays       uint32             `bson:"expired_days"`         // 求婚函过期天数
}

func (s *Store) ensureIndexProposeInfo(ctx context.Context) {
    err := s.weddingProposeCollection.CreateIndexes(ctx, []mongo.IndexModel{
        {
            Keys: bson.D{
                {Key: "from_uid", Value: 1},
                {Key: "target_uid", Value: 1},
                {Key: "create_time", Value: 1},
                {Key: "end_time", Value: 1},
            },
        },
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "weddingProposeCollection error: %v", err)
    }
}

// AddProposeInfo 添加求婚函信息
func (s *Store) AddProposeInfo(ctx context.Context, proposeInfo *ProposeInfo) (primitive.ObjectID, error) {
    res, err := s.weddingProposeCollection.Collection.InsertOne(ctx, proposeInfo)
    if err != nil {
        log.ErrorWithCtx(ctx, "AddProposeInfo:%v error: %v", proposeInfo, err)
        return primitive.NilObjectID, err
    }
    log.DebugWithCtx(ctx, "AddProposeInfo proposeInfo:%v, InsertedID:%v", proposeInfo, res.InsertedID)
    return res.InsertedID.(primitive.ObjectID), err
}

// GetProposeInfoByID 根据uid获取求婚函信息
func (s *Store) GetProposeInfoByID(ctx context.Context, id string) (propose *ProposeInfo, err error) {
    objID, err := primitive.ObjectIDFromHex(id)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetProposeInfoByID:%v error: %v", id, err)
        return nil, err
    }

    query := bson.M{"_id": objID}

    err = s.weddingProposeCollection.Collection.FindOne(ctx, query).Decode(&propose)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetProposeInfoByID:%v error: %v", id, err)
        if errors.Is(err, mongo.ErrNoDocuments) {
            return nil, nil
        }
        return nil, err
    }
    return propose, err
}

// UpdateProposeInfoStatusByID 更新求婚函信息
func (s *Store) UpdateProposeInfoStatusByID(ctx context.Context, id string, status uint32) error {
    objID, err := primitive.ObjectIDFromHex(id)
    if err != nil {
        log.ErrorWithCtx(ctx, "UpdateProposeInfoStatusByID:%v error: %v", id, err)
        return err
    }

    query := bson.M{"_id": objID}
    update := bson.M{"$set": bson.M{"status": status}}
    _, err = s.weddingProposeCollection.Collection.UpdateOne(ctx, query, update)
    if err != nil {
        log.ErrorWithCtx(ctx, "UpdateProposeInfoStatusByID:%v error: %v", id, err)
    }
    return err
}

func (s *Store) GetMyProposeInfo(ctx context.Context, uid, status uint32) (*ProposeInfo, error) {
    query := bson.M{
        "from_uid": uid,
        "status":   status,
    }
    var propose *ProposeInfo
    err := s.weddingProposeCollection.Collection.FindOne(ctx, query).Decode(&propose)

    if err != nil {
        if errors.Is(err, mongo.ErrNoDocuments) {
            log.DebugWithCtx(ctx, "GetMyProposeInfo uid:%d not found", uid)
            return nil, nil
        }
        log.ErrorWithCtx(ctx, "GetMyProposeInfo:%v error: %v", uid, err)
    }
    return propose, nil
}

// GetProposeInfoByUid 根据uid获取求婚函信息
func (s *Store) GetProposeInfoByUidAndStatus(ctx context.Context, uid, status uint32) (list []*ProposeInfo, err error) {
    var proposeList []*ProposeInfo
    query := bson.M{"$or": []bson.M{
        {"$and": []bson.M{{"from_uid": uid}, {"status": status}}},
        {"$and": []bson.M{{"target_uid": uid}, {"status": status}}},
    }}

    cursor, err := s.weddingProposeCollection.Collection.Find(ctx, query)
    if err != nil {
        if errors.Is(err, mongo.ErrNoDocuments) {
            log.DebugWithCtx(ctx, "GetProposeInfoBy Uid:%d not found, status:%d", uid, status)
            return nil, nil
        }
        log.ErrorWithCtx(ctx, "GetProposeInfoBy Uid:%v error: %v, status:%d", uid, err, status)
    }

    defer cursor.Close(ctx)
    for cursor.Next(ctx) {
        var propose ProposeInfo
        err = cursor.Decode(&propose)
        if err != nil {
            log.ErrorWithCtx(ctx, "GetTimoutProposeList error: %v", err)
            return proposeList, err
        }
        proposeList = append(proposeList, &propose)
    }
    return proposeList, err
}

// GetSendProposeInfoByUid 根据uid获取求婚函信息
func (s *Store) GetSendProposeInfoByUid(ctx context.Context, uid uint32) (propose *ProposeInfo, err error) {
    query := bson.M{"from_uid": uid, "status": uint32(pbLogic.ProposeStatus_PROPOSE_STATUS_INVITED)}
    err = s.weddingProposeCollection.Collection.FindOne(ctx, query).Decode(&propose)
    if err != nil {
        if errors.Is(err, mongo.ErrNoDocuments) {
            log.DebugWithCtx(ctx, "GetSendProposeInfoByUid uid:%d not found", uid)
            return nil, nil
        }
        log.ErrorWithCtx(ctx, "GetSendProposeInfoByUid:%v error: %v", uid, err)
    }
    return propose, err
}

// GetTimoutProposeList 获取过期的求婚函列表
func (s *Store) GetTimoutProposeList(ctx context.Context) (proposeList []*ProposeInfo, err error) {
    query := bson.M{"status": uint32(pbLogic.ProposeStatus_PROPOSE_STATUS_INVITED), "end_time": bson.M{"$lt": time.Now()}}
    cursor, err := s.weddingProposeCollection.Collection.Find(ctx, query)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetTimoutProposeList error: %v", err)
        return proposeList, err
    }

    defer cursor.Close(ctx)
    for cursor.Next(ctx) {
        var propose ProposeInfo
        err = cursor.Decode(&propose)
        if err != nil {
            log.ErrorWithCtx(ctx, "GetTimoutProposeList error: %v", err)
            return proposeList, err
        }
        proposeList = append(proposeList, &propose)
    }
    return proposeList, err
}
