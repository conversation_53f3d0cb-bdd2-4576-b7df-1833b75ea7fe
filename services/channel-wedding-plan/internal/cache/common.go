package cache

import (
    "context"
    "golang.52tt.com/pkg/log"
    "golang.52tt.com/pkg/protocol"
    "golang.52tt.com/protocol/common/status"
    "google.golang.org/grpc/codes"
    "time"
)

const (
    LockPrefix = "channel_wedding_plan:lock:"
)

func (c *Cache) TryLock(ctx context.Context, key, val string, timeOutList ...time.Duration) error {
    timeOut := 5 * time.Minute
    if len(timeOutList) > 0 {
        timeOut = timeOutList[0]
    }
    for i := 0; i < 3; i++ {
        res, err := c.cmder.SetNX(ctx, LockPrefix+key, val, timeOut).Result()
        if err != nil {
            return err
        }
        if res {
            log.InfoWithCtx(ctx, "TryLock success, key:%s val:%s", key, val)
            return nil
        }
        time.Sleep(50 * time.Millisecond)
    }
    return protocol.NewExactServerError(codes.OK, status.ErrChannelWeddingPlanCommonError, "服务器繁忙，请重试~")
}

func (c *Cache) Unlock(ctx context.Context, key, val string) error {
    nowVal := c.cmder.Get(ctx, LockPrefix+key).Val()
    if val == nowVal {
        return c.cmder.Del(ctx, LockPrefix+key).Err()
    }
    return nil
}
