package cache

import(
	context "context"
	redis "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"
	time "time"
	store "golang.52tt.com/services/channel-wedding-plan/internal/store"
)

type ICache interface {
	AddDivideTimeout(ctx context.Context, uid uint32) (bool,error)
	BatSetWeddingAnniversaryPopupInfo(ctx context.Context, anniversaryDay int, dayTime time.Time, cpList []*AnniversaryCp) error
	BatchGetUserDivorceTimeout(ctx context.Context, uidList []uint32) (map[uint32]uint64,error)
	CheckNextIfAnniversaryPopupInfoExist(ctx context.Context, anniversaryDay int, t time.Time) (bool,error)
	Close() error
	DelAnniversaryPopupInfoByUid(ctx context.Context, uid uint32, anniversaryDay int, t time.Time) error
	DelRelationHideSwitch(ctx context.Context, uid uint32) error
	DelUserConsultReserveTimeSection(ctx context.Context, uid, cid uint32, reserveSt, reserveEt uint32) error
	DelWeddingAnniversaryPopupFlag(ctx context.Context, uid uint32, t time.Time) error
	DelWeddingPlan(ctx context.Context, weddingPlanId uint32) error
	GetAllAnniversaryPopupInfo(ctx context.Context, anniversaryDay int, t time.Time) (cpMap map[uint32]uint32,err error)
	GetAnniversaryPopupByUid(ctx context.Context, uid uint32, anniversaryDay int, t time.Time) (cpUid uint32,err error)
	GetDivideTimeout(ctx context.Context, timeOut int64) ([]uint32,error)
	GetMyDivideTimeout(ctx context.Context, uid uint32) (uint64,error)
	GetRedisClient() redis.Cmdable
	GetRelationHideSwitch(ctx context.Context, uid uint32) (bool,uint32,error)
	GetUserConsultReserveManagerUid(ctx context.Context, uid, cid uint32) (uint32,error)
	GetUserConsultReserveTimeSection(ctx context.Context, uid, cid uint32, reserveSt, reserveEt uint32) (uint32,error)
	GetWeddingPlan(ctx context.Context, weddingPlanId uint32) (*store.WeddingPlan,error)
	RemoveDivideTimeout(ctx context.Context, uid uint32) (bool,error)
	SetRelationHideSwitch(ctx context.Context, uid uint32, status uint32) error
	SetUserConsultReserveManagerUid(ctx context.Context, uid, cid, managerUid uint32, expire time.Duration) error
	SetUserConsultReserveTimeSection(ctx context.Context, uid, cid uint32, reserveSt, reserveEt, managerUid uint32, expire time.Duration) error
	SetWeddingAnniversaryPopupFlag(ctx context.Context, uid uint32, t time.Time) (bool,error)
	SetWeddingPlan(ctx context.Context, weddingPlanId uint32, weddingPlan *store.WeddingPlan) error
	TryLock(ctx context.Context, key, val string, timeOutList ...time.Duration) error
	Unlock(ctx context.Context, key, val string) error
}

