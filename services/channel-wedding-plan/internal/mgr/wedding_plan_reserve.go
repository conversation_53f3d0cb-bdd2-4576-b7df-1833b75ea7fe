package mgr

import (
    "context"
    "fmt"
    "github.com/google/uuid"
    "golang.52tt.com/pkg/log"
    "golang.52tt.com/pkg/protocol"
    grpcProtocol "golang.52tt.com/pkg/protocol/grpc"
    imPB "golang.52tt.com/protocol/app/im"
    "golang.52tt.com/protocol/common/status"
    pb "golang.52tt.com/protocol/services/channel-wedding-plan"
    imApiPB "golang.52tt.com/protocol/services/im-api"
    tt_rev_channel_mode_mgr "golang.52tt.com/protocol/services/tt-rev-channel-mode-mgr"
    "golang.52tt.com/services/channel-wedding-plan/internal/store"
    "google.golang.org/grpc/codes"
    "sort"
    "strconv"
    "strings"
    "time"
)

func (m *WeddingPlanMgr) GetMyWeddingReserveInfo(ctx context.Context, weddingPlanId uint32) (*store.ReserveInfo, error) {
    serviceInfo, ok := grpcProtocol.ServiceInfoFromContext(ctx)
    if !ok {
        return nil, fmt.Errorf("GetMyWeddingReserveInfo fail to get serviceInfo")
    }
    weddingPlan, err := m.GetWeddingPlanById(ctx, weddingPlanId, serviceInfo.UserID)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetMyWeddingReserveInfo fail to GetWeddingPlanById. err:%v", err)
        return nil, err
    }
    if weddingPlan == nil {
        log.ErrorWithCtx(ctx, "GetMyWeddingReserveInfo weddingPlan is nil")
        return nil, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
    }
    return weddingPlan.ReserveInfo, err
}

func (m *WeddingPlanMgr) GetWeddingPlanById(ctx context.Context, weddingPlanId, uid uint32) (*store.WeddingPlan, error) {
    weddingPlan, err := m.st.GetWeddingPlanById(ctx, weddingPlanId)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetMyWeddingReserveInfo fail to GetWeddingPlanById. err:%v", err)
        return nil, err
    }
    if weddingPlan == nil {
        log.ErrorWithCtx(ctx, "GetMyWeddingReserveInfo weddingPlan is nil")
        return nil, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
    }
    if !(weddingPlan.GroomUid == uid || weddingPlan.BrideUid == uid) {
        log.ErrorWithCtx(ctx, "GetMyWeddingReserveInfo weddingPlan is nil or not belong to user")
        return nil, fmt.Errorf("weddingPlan is nil or not belong to user")
    }

    return weddingPlan, nil
}

// GetWeddingPlanByIdNoCheck 通过 weddingPlanId 获取婚礼预约信息
func (m *WeddingPlanMgr) GetWeddingPlanByIdNoCheck(ctx context.Context, weddingPlanId uint32, withCache bool) (*store.WeddingPlan, error) {
    if withCache {
        weddingPlan, err := m.cache.GetWeddingPlan(ctx, weddingPlanId)
        if err != nil {
            log.ErrorWithCtx(ctx, "GetWeddingPlanByIdNoCheck fail to GetWeddingPlan. err:%v", err)
            return nil, err
        }
        if weddingPlan != nil {
            return weddingPlan, nil
        }
    }

    plan, err := m.st.GetWeddingPlanById(ctx, weddingPlanId)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetWeddingPlanByIdNoCheck fail to GetWeddingPlanById. err:%v", err)
        return nil, err
    }
    if plan == nil {
        log.ErrorWithCtx(ctx, "GetWeddingPlanByIdNoCheck weddingPlan is nil")
        return nil, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
    }

    err = m.cache.SetWeddingPlan(ctx, weddingPlanId, plan)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetWeddingPlanByIdNoCheck fail to SetWeddingPlan. err:%v", err)
        return nil, err
    }
    return plan, nil
}

func (m *WeddingPlanMgr) CheckWeddingPlanByChannelIdAndTimeRange(ctx context.Context, channelId, startTime, endTime uint32) (bool, error) {
    serviceInfo, ok := grpcProtocol.ServiceInfoFromContext(ctx)
    if !ok {
        log.ErrorWithCtx(ctx, "CheckWeddingPlanByChannelIdAndTimeRange fail to get serviceInfo")
        return false, fmt.Errorf("CheckWeddingPlanByChannelIdAndTimeRange fail to get serviceInfo")
    }
    weddingPlan, err := m.st.GetWeddingPlanByChannelIdTimeRange(ctx, channelId, startTime, endTime)
    if err != nil {
        log.ErrorWithCtx(ctx, "CheckWeddingPlanByChannelIdAndTimeRange fail to GetWeddingPlanByChannelIdTimeRange. err:%v", err)
        return false, err
    }

    if len(weddingPlan) > 1 { // 该时段有两个以上预约, 无法预约
        return false, nil
    }

    if len(weddingPlan) == 0 { // 该时段无预约, 可以预约
        return true, nil
    }

    // 该时段有一个预约, 看看是不是自己的
    if weddingPlan[0].GroomUid == serviceInfo.UserID || weddingPlan[0].BrideUid == serviceInfo.UserID { // 只有一个,且是自己的, 可以预约
        return true, nil
    }

    return false, nil
}

func getTimeZero(t time.Time) time.Time {
    return time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, t.Location())
}

func (m *WeddingPlanMgr) GetWeddingReserveInfo(ctx context.Context, req *pb.GetWeddingReserveInfoRequest) (*pb.GetWeddingReserveInfoResponse, error) {
    resp := &pb.GetWeddingReserveInfoResponse{}
    channelRcmd := false
    if req.GetReserveDate() == 0 && req.GetChannelId() > 0 {
        channelRcmd = true // 房间快速进房, 仅找该房间最早能预约的
    }
    serviceInfo, ok := grpcProtocol.ServiceInfoFromContext(ctx)
    if !ok {
        log.ErrorWithCtx(ctx, "GetWeddingReserveInfo fail to get serviceInfo")
        return nil, fmt.Errorf("GetWeddingReserveInfo fail to get serviceInfo")
    }

    now := time.Now()
    startTime := time.Date(now.Year(), now.Month(), now.Day(), now.Hour(), 0, 0, 0, now.Location())      // 当前小时
    if req.GetReserveDate() > uint32(startTime.Unix()) {
        startTime = time.Unix(int64(req.GetReserveDate()), 0)
    }
    endTime := startTime.Add(time.Minute * time.Duration(m.bc.GetConfig().ReserveConf.MaxReservableMin)) // 最晚可预约时间

    endTime = getTimeZero(endTime)

    // 获取近期付费婚礼预约信息
    recentReserveInfo, err := m.st.GetWeddingPlanByThemeTypeTimeRange(ctx, req.ThemeType, uint32(startTime.Unix()), uint32(endTime.Unix()), 0)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetWeddingReserveInfo fail to GetWeddingPlanByThemeTypeTimeRange. err:%v", err)
        return nil, err
    }

    // 获取有权限的房间列表
    permChannelId, err := m.GetWeddingChannelId(ctx)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetWeddingReserveInfo fail to GetWeddingChannelId. err:%v", err)
        return nil, err
    }
    if len(permChannelId) == 0 {
        log.WarnWithCtx(ctx, "GetWeddingReserveInfo permChannelId is empty")
        return resp, nil
    }

    for _, cid := range permChannelId {
        cinfo := &pb.ChannelInfo{
            ChannelId:  cid,
            ManagerUid: m.getChannelManagerUid(ctx, serviceInfo.UserID, cid),
        }
        resp.ChannelInfo = append(resp.ChannelInfo, cinfo)
    }

    // 筛选最早可预约房间
    // 使用一张表记录预约情况
    reservedTable := make(map[int64][]uint32)
    for _, item := range recentReserveInfo {
        st := time.Unix(int64(item.ReserveInfo.StartTime), 0)
        et := time.Unix(int64(item.ReserveInfo.EndTime), 0)
        for st.Before(et) { // 把预约时长划分最小单位, 标记每个最小单位目前的预约情况
            reservedTable[st.Unix()] = append(reservedTable[st.Unix()], item.ReserveInfo.ChannelId)
            st = st.Add(time.Minute * time.Duration(m.bc.GetConfig().ReserveConf.ReserveTimeSectionMinimum))
        }
    }

    if req.GetReserveDate() == 0 { // 没选时间时候推荐逻辑
        // 根据可预约时段从前往后推,找到第一个空闲时段,房间
        searchSt := startTime
        for ; searchSt.Before(endTime); searchSt = searchSt.Add(time.Duration(m.bc.GetReserveTimeSectionDurationConf(searchSt)) * time.Minute) {
            if resp.CurReserveDate > 0 {
                break // 已经找到最早可预约时段
            }

            searchPartEt := searchSt.Add(time.Minute * time.Duration(m.bc.GetReserveTimeSectionDurationConf(searchSt))) // 预约时段结束时间
            // 检查当前预约时段是否在可预约时间范围内
            if !m.checkReserveSectionValid(searchSt, req.ThemeType) {
                continue
            }

            // 检查每个预约时段中最小单位时段的预约情况
            innerSearchSt := searchSt
            innerSearchEt := searchPartEt
            reservedChannelId := make([]uint32, 0)
            for innerSearchSt.Before(innerSearchEt) {
                reservedChannelId = append(reservedChannelId, reservedTable[innerSearchSt.Unix()]...)
                innerSearchSt = innerSearchSt.Add(time.Duration(m.bc.GetConfig().ReserveConf.ReserveTimeSectionMinimum) * time.Minute)
            }

            reservedChannelIdMap := make(map[uint32]struct{})
            for _, channelId := range reservedChannelId {
                reservedChannelIdMap[channelId] = struct{}{}
            }
            for _, cinfo := range resp.ChannelInfo {
                if _, ok := reservedChannelIdMap[cinfo.ChannelId]; !ok {
                    if channelRcmd && cinfo.ChannelId != req.ChannelId {
                        continue // 如果是房间快速进房, 找到其他房间的最早可用时段, 不返回
                    }
                    resp.CurReserveDate = uint32(getTimeZero(searchSt).Unix())
                    break
                }
            }
        }
        if resp.CurReserveDate == 0 { // 找不到可预约时段
            resp.CurReserveDate = uint32(getTimeZero(startTime).Unix())
        }
    } else {
        resp.CurReserveDate = req.ReserveDate
    }

    // 统计当天各房间预约情况, 按照空闲时段由多到少排序
    channelTargetDayReserveCnt := make(map[uint32]int)
    targetDayStart := time.Unix(int64(resp.CurReserveDate), 0)
    targetDayEnd := targetDayStart.Add(time.Hour * 24)
    for targetDayStart.Before(targetDayEnd) {
        reservedList, ok := reservedTable[targetDayStart.Unix()]
        if ok {
            for _, item := range reservedList {
                channelTargetDayReserveCnt[item]++
            }
        }
        targetDayStart = targetDayStart.Add(time.Minute * time.Duration(m.bc.GetConfig().ReserveConf.ReserveTimeSectionMinimum))
    }
    sort.Slice(resp.ChannelInfo, func(i, j int) bool {
        if channelTargetDayReserveCnt[resp.ChannelInfo[i].ChannelId] == channelTargetDayReserveCnt[resp.ChannelInfo[j].ChannelId] {
            return resp.ChannelInfo[i].ChannelId < resp.ChannelInfo[j].ChannelId
        }
        return channelTargetDayReserveCnt[resp.ChannelInfo[i].ChannelId] < channelTargetDayReserveCnt[resp.ChannelInfo[j].ChannelId]
    })
    if req.GetChannelId() == 0 { // 如果用户不选订房间, 则推荐最空闲的房间
        resp.CurChannelId = resp.ChannelInfo[0].ChannelId
    } else {
        resp.CurChannelId = req.ChannelId
    }


    // 标记已预约的时段
    reservedTimeSection := make(map[uint32]*store.WeddingPlan)
    for _, item := range recentReserveInfo {
        if item.ReserveInfo == nil || item.ReserveInfo.ChannelId != resp.CurChannelId {
            continue
        }
        st := time.Unix(int64(item.ReserveInfo.StartTime), 0)
        et := time.Unix(int64(item.ReserveInfo.EndTime), 0)
        for st.Before(et) {
            reservedTimeSection[uint32(st.Unix())] = item
            st = st.Add(time.Minute * time.Duration(m.bc.GetConfig().ReserveConf.ReserveTimeSectionMinimum))
        }
    }

    // 生成预约字段信息
    reservableTimeStart := time.Unix(int64(resp.CurReserveDate), 0)
    reservableTimeEnd := reservableTimeStart.Add(time.Hour * 24)
    for reservableTimeStart.Before(reservableTimeEnd) {
        partEt := reservableTimeStart.Add(time.Minute * time.Duration(m.bc.GetReserveTimeSectionDurationConf(reservableTimeStart)))
        if !m.checkReserveSectionValid(reservableTimeStart, req.ThemeType) {
            reservableTimeStart = partEt
            continue
        }
        innerSearchSt := reservableTimeStart
        innerSearchEt := partEt
        isReserved := false
        targetPlan := &store.WeddingPlan{}
        for innerSearchSt.Before(innerSearchEt) {
            if _, ok := reservedTimeSection[uint32(innerSearchSt.Unix())]; ok {
                isReserved = true
                targetPlan = reservedTimeSection[uint32(innerSearchSt.Unix())]
                break
            }
            innerSearchSt = innerSearchSt.Add(time.Minute * time.Duration(m.bc.GetConfig().ReserveConf.ReserveTimeSectionMinimum))
        }

        resp.ReserveTimeInfo = append(resp.ReserveTimeInfo, &pb.ReserveTimeInfo{
            ReserveTimeSection: reservableTimeStart.Format("15:04") + "-" + partEt.Format("15:04"),
            IsFullyReserved:    isReserved,
            GroomUid:           targetPlan.GroomUid,
            BrideUid:           targetPlan.BrideUid,
            IsHot:              targetPlan.IsHot || m.isHotTimeSection(resp.CurChannelId, reservableTimeStart),
        })
        reservableTimeStart = partEt
    }

    resp.MinReserveDate = uint32(getTimeZero(now.Add(time.Duration(m.bc.GetConfig().ReserveConf.MinReservableMin) * time.Minute)).Unix())
    resp.MaxReserveDate = uint32(getTimeZero(now.Add(time.Duration(m.bc.GetConfig().ReserveConf.MaxReservableMin) * time.Minute)).Unix())
    return resp, nil
}

func (m *WeddingPlanMgr) GetFreeWeddingReserveInfo(ctx context.Context, req *pb.GetWeddingReserveInfoRequest) (*pb.GetWeddingReserveInfoResponse, error) {
    resp := &pb.GetWeddingReserveInfoResponse{}

    now := time.Now()
    startTime := time.Date(now.Year(), now.Month(), now.Day(), now.Hour(), 0, 0, 0, now.Location())                 // 当前小时
    endTime := startTime.Add(time.Minute * time.Duration(m.bc.GetConfig().ReserveConf.FreeWeddingMaxReservableMin))
    if req.GetReserveDate() > 0 {
        startTime = time.Unix(int64(req.GetReserveDate()), 0)
        endTime = startTime.Add(time.Hour * 24)
    }
    endTime = getTimeZero(endTime)

    resp.CurReserveDate = uint32(getTimeZero(startTime).Unix())

    // 生成预约字段信息
    reservableTimeStart := startTime
    reservableTimeEnd := getTimeZero(reservableTimeStart.Add(time.Hour * 24))
    for reservableTimeStart.Before(reservableTimeEnd) {
        partEt := reservableTimeStart.Add(time.Minute * time.Duration(m.bc.GetConfig().ReserveConf.FreeWeddingReserveTimeSectionDuration))
        if !m.checkReserveSectionValid(reservableTimeStart, req.ThemeType) {
            reservableTimeStart = partEt
            continue
        }
        resp.ReserveTimeInfo = append(resp.ReserveTimeInfo, &pb.ReserveTimeInfo{
            ReserveTimeSection: reservableTimeStart.Format("15:04") + "-" + partEt.Format("15:04"),
        })
        reservableTimeStart = partEt
    }

    resp.MinReserveDate = uint32(getTimeZero(now.Add(time.Duration(m.bc.GetConfig().ReserveConf.FreeWeddingMinReservableMin) * time.Minute)).Unix())
    resp.MaxReserveDate = uint32(getTimeZero(now.Add(time.Duration(m.bc.GetConfig().ReserveConf.FreeWeddingMaxReservableMin) * time.Minute)).Unix())
    return resp, nil
}

func (m *WeddingPlanMgr) checkReserveSectionValid(sectionSt time.Time, themeType uint32) bool {
    // 判断可预约日期
    reserveableDateSt := time.Now().Add(time.Duration(m.bc.GetConfig().ReserveConf.MinReservableMin) * time.Minute).Truncate(time.Minute)
    reserveableDateEt := reserveableDateSt.Add(time.Duration(m.bc.GetConfig().ReserveConf.MaxReservableMin) * time.Minute)
    reserveTimeSectionDuration := m.bc.GetReserveTimeConf(sectionSt)
    if themeType == store.ThemeTypeFree {
        // 判断可预约日期
        reserveableDateSt = time.Now().Add(time.Minute * time.Duration(m.bc.GetConfig().ReserveConf.FreeWeddingMinReservableMin)).Truncate(time.Minute)
        reserveableDateEt = reserveableDateSt.Add(time.Minute * time.Duration(m.bc.GetConfig().ReserveConf.FreeWeddingMaxReservableMin))
        reserveTimeSectionDuration = m.bc.GetConfig().ReserveConf.FreeWeddingReserveTimeLimit
    }

    if sectionSt.Before(reserveableDateSt) || sectionSt.After(reserveableDateEt) {
         return false
    }

    // 判断可预约时段
    if sectionSt.Before(time.Now()) {
        return false // 不能预约过去的时间
    }
    // 计算当天秒数, 判断是否在可预约区间内
    searchStSecond := uint32(sectionSt.Hour()*3600 + sectionSt.Minute()*60 + sectionSt.Second())
    validTimeSection := false
    for _, item := range reserveTimeSectionDuration {
        limitItem := strings.Split(item, "-")
        if len(limitItem) < 2 {
            continue
        }
        limitMin := countSeconds(limitItem[0])
        limitMax := countSeconds(limitItem[1])
        if searchStSecond >= limitMin && searchStSecond < limitMax {
            validTimeSection = true
            break
        }

    }
    return validTimeSection
}

func (m *WeddingPlanMgr) IsHotTimeSection(ctx context.Context, cid uint32, sectionSt time.Time) bool {
    // 判断是否在热门时段
    if m.isHotTimeSection(cid, sectionSt) {
        return true
    }
    return false
}

func (m *WeddingPlanMgr) isHotTimeSection(cid uint32, sectionSt time.Time) bool {
    // 如果时段距今不足热门时段失效时间, 直接判为普通时段
    if time.Now().Add(time.Hour * time.Duration(m.bc.GetConfig().ReserveConf.ChannelReserveHotTimeLostNearlyHour)).After(sectionSt) {
        return false
    }

    // 判断是否在热门时段
    cidStr := strconv.FormatUint(uint64(cid), 10)
    hotTimeSection := m.bc.GetChannelReserveHotTimeConf(sectionSt, cidStr)
    if len(hotTimeSection) == 0 {
        return false
    }

    searchStSecond := uint32(sectionSt.Hour()*3600 + sectionSt.Minute()*60 + sectionSt.Second())
    for _, item := range hotTimeSection {
        limitItem := strings.Split(item, "-")
        if len(limitItem) < 2 {
            continue
        }
        limitMin := countSeconds(limitItem[0])
        limitMax := countSeconds(limitItem[1])
        if searchStSecond >= limitMin && searchStSecond < limitMax {
            return true
        }
    }
    return false
}

func (m *WeddingPlanMgr) UpdateWeddingReserve(ctx context.Context, themeType uint32, weddingPlanId uint32, reserveChannelId uint32, reserveSt, reserveEt, changeTimes uint32) error {
    // 加锁
    lockKey := fmt.Sprintf("%d_%s", weddingPlanId, uuid.New().String())
    err := m.cache.TryLock(ctx, "UpdateWeddingReserve", lockKey)
    if err != nil {
        log.ErrorWithCtx(ctx, "UpdateWeddingReserve fail to TryLock. err:%v", err)
        return err
    }
    defer m.cache.Unlock(ctx, "UpdateWeddingReserve", lockKey)

    // 判断房间权限
    if themeType == store.ThemeTypePay {
        err = m.checkChannelPerm(ctx, reserveChannelId)
        if err != nil {
            log.ErrorWithCtx(ctx, "UpdateWeddingReserve fail to checkChannelPerm. err:%v", err)
            return err
        }
    }

    // 检查是否限制预约时间
    reserveStTime := time.Unix(int64(reserveSt), 0)
    reserveEtTime := time.Unix(int64(reserveEt), 0)
    if !m.checkReserveSectionValid(reserveStTime, themeType) ||
        !m.checkReserveSectionValid(reserveEtTime.Add(-time.Second), themeType) {
        log.ErrorWithCtx(ctx, "UpdateWeddingReserve reserve time invalid")
        return protocol.NewExactServerError(codes.OK, status.ErrChannelWeddingPlanCommonError, "不支持选择本时段哦~")
    }

    _, err = m.st.UpdateOrderStatus(ctx, weddingPlanId, []uint32{store.WeddingStatusFreezing}, store.WeddingStatusReserved)
    if err != nil {
        log.ErrorWithCtx(ctx, "UpdateWeddingReserve fail to UpdateOrderStatus. err:%v", err)
        return err
    }

    return m.st.UpdateWeddingReserveInfo(ctx, weddingPlanId, &store.ReserveInfo{
        ChannelId:   reserveChannelId,
        StartTime:   reserveSt,
        EndTime:     reserveEt,
        ChangeTimes: changeTimes,
    })
}

func (m *WeddingPlanMgr) checkChannelPerm(ctx context.Context, reserveChannelId uint32) error {
    // 检查房间权限
    permChannelId, err := m.GetWeddingChannelId(ctx)
    if err != nil {
        log.ErrorWithCtx(ctx, "UpdateWeddingReserve fail to GetWeddingChannelId. err:%v", err)
        return err
    }
    channelHasPerm := false
    for _, item := range permChannelId {
        if reserveChannelId == item {
            channelHasPerm = true
            break
        }
    }
    if !channelHasPerm {
        log.ErrorWithCtx(ctx, "UpdateWeddingReserve reserveChannelId not has permission")
        return protocol.NewExactServerError(codes.OK, status.ErrChannelWeddingPlanCommonError, "不支持选择本房间哦~")
    }

    // 检查房间封禁
    checkChannelResp, err := m.rpc.RiskMngApiCli.CheckChannel(ctx, "channel-wedding", []uint32{reserveChannelId})
    if err != nil {
        log.ErrorWithCtx(ctx, "UpdateWeddingReserve fail to CheckChannel. err:%v", err)
        return err
    }
    if checkChannelResp[reserveChannelId] {
        log.ErrorWithCtx(ctx, "UpdateWeddingReserve reserveChannelId is banned")
        return protocol.NewExactServerError(codes.OK, status.ErrChannelWeddingPlanCommonError, "预约失败，该房间暂不支持预约")
    }

    return nil
}

func (m *WeddingPlanMgr) GetWeddingChannelId(ctx context.Context) ([]uint32, error) {
    channelIdResp, err := m.rpc.ChannelModeMgrCli.BatchGetChannelIdByLayoutType(ctx, &tt_rev_channel_mode_mgr.BatchGetChannelIdByLayoutTypeReq{
        LayoutType: 34,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetWeddingChannelId fail to BatchGetChannelIdByLayoutType. err:%v", err)
        return nil, err
    }

    return channelIdResp.ChannelId, nil
}

func (m *WeddingPlanMgr) genReserveTimeSection() []string {
    reserveTimeLimit := m.bc.GetReserveTimeConf(time.Now())
    reserveTimeSectionLong := m.bc.GetReserveTimeSectionDurationConf(time.Now())
    var reserveTimeSection []string
    for _, timeLimit := range reserveTimeLimit {
        timeLimitArr := strings.Split(timeLimit, "-")
        startTimeStr, endTimeStr := timeLimitArr[0], timeLimitArr[1]
        startTime, _ := time.Parse("15:04", startTimeStr)
        endTime, _ := time.Parse("15:04", endTimeStr)
        if endTime.Hour() == 0 && endTime.Minute() == 0 {
            endTime = endTime.Add(time.Hour * 24)
        }
        for startTime.Before(endTime) {
            partEndTime := startTime.Add(time.Minute * time.Duration(reserveTimeSectionLong))
            reserveTimeSection = append(reserveTimeSection, startTime.Format("15:04")+"-"+partEndTime.Format("15:04"))
            startTime = startTime.Add(time.Minute * time.Duration(reserveTimeSectionLong))
        }
    }

    return reserveTimeSection
}

func (m *WeddingPlanMgr) TranReserveTimeSection2Time(reserveDate uint32, reserveTimeSection []string) (uint32, uint32, error) {
    if len(reserveTimeSection) == 0 {
        return 0, 0, fmt.Errorf("reserveTimeSection is empty")
    }

    startStr := strings.Split(reserveTimeSection[0], "-")[0]
    endStr := strings.Split(reserveTimeSection[len(reserveTimeSection)-1], "-")[1]

    startTime := countSeconds(startStr)
    endTime := countSeconds(endStr)
    if endTime == 0 { // 24点
        endTime = 3600 * 24
    }
    if endTime < startTime {
        return 0, 0, fmt.Errorf("endTime is less than startTime")
    }
    return reserveDate + startTime, reserveDate + endTime, nil
}

func countSeconds(hourMinute string) uint32 {
    timeArr := strings.Split(hourMinute, ":")
    if len(timeArr) != 2 {
        return 0
    }
    hour, _ := strconv.Atoi(timeArr[0])
    minute, _ := strconv.Atoi(timeArr[1])
    return uint32(hour*3600 + minute*60)
}

func (m *WeddingPlanMgr) PageWeddingPlanByChannelId(ctx context.Context, channelId []uint32, page, pageSize, sortType int, startTime, endTime int64) (uint32, []*store.WeddingPlan, error) {
    total, err := m.st.CountWeddingPlanByChannelIdReserveTime(ctx, channelId, startTime, endTime)
    if err != nil {
        log.ErrorWithCtx(ctx, "PageWeddingPlanByChannelId fail to CountWeddingPlanByChannelIdReserveTime. err:%v", err)
        return 0, nil, err
    }

    data, err := m.st.PageWeddingPlanByChannelIdReserveTime(ctx, channelId, page, pageSize, sortType, startTime, endTime)
    if err != nil {
        log.ErrorWithCtx(ctx, "PageWeddingPlanByChannelId fail to PageWeddingPlanByChannelIdReserveTime. err:%v", err)
        return 0, nil, err
    }

    return uint32(total), data, nil
}

func (m *WeddingPlanMgr) UpdateWeddingHost(ctx context.Context, weddingPlanId uint32, hostUid uint32) error {
    return m.st.UpdateWeddingPlanHost(ctx, weddingPlanId, hostUid)
}

func (m *WeddingPlanMgr) UpdateWeddingPlanStatusFinish(ctx context.Context, weddingPlanId uint32, isForce bool) error {
    if !isForce { // 非强制更新需要校验前置状态
        err := m.st.UpdateWeddingPlanStatusStrict(ctx, weddingPlanId, store.WeddingPlanStatusReserved, store.WeddingPlanStatusFinish)
        if err != nil {
            log.ErrorWithCtx(ctx, "UpdateWeddingPlanStatusFinish fail to UpdateWeddingPlanStatusStrict. weddingPlanId: %d, err:%v", weddingPlanId, err)
            return err
        }
    } else {
        err := m.st.BatchUpdateWeddingPlanStatus(ctx, []uint32{weddingPlanId}, store.WeddingPlanStatusFinish)
        if err != nil {
            log.ErrorWithCtx(ctx, "UpdateWeddingPlanStatusFinish fail to BatchUpdateWeddingPlanStatus. weddingPlanId: %d, err:%v", weddingPlanId, err)
            return err
        }
    }

    err := m.st.SetWeddingGuestInvitePlanFinish(ctx, weddingPlanId)
    if err != nil {
        log.ErrorWithCtx(ctx, "UpdateWeddingPlanStatusFinish fail to SetWeddingGuestInvitePlanFinish. weddingPlanId: %d, err:%v", weddingPlanId, err)
        return err
    }

    return nil
}

func (m *WeddingPlanMgr) ReserveChangeNotifyAdmin(ctx context.Context, uid uint32, oldWeddingPlan, weddingPlan *store.WeddingPlan, isChange bool) error {
    if weddingPlan.ThemeType == store.ThemeTypeFree {
        return nil
    }

    // 获取需要通知房间相关信息
    sendUidList, channelName, err := m.getChannelNotifyInfo(ctx, weddingPlan.ReserveInfo.ChannelId)
    if err != nil {
        log.ErrorWithCtx(ctx, "reserveNotify fail to getChannelNotifyInfo. err:%v", err)
        return err
    }

    // 发送im消息
    text := fmt.Sprintf("[你有新的婚礼预约订单] 我约了“%s”在 %s 开始的婚礼，请及时安排婚礼哦~", channelName, time.Unix(int64(weddingPlan.ReserveInfo.StartTime), 0).Format("01月02日15:04:05"))
    if isChange && weddingPlan.ReserveInfo.ChannelId == oldWeddingPlan.ReserveInfo.ChannelId { // 更新时间,不更新房间
        if weddingPlan.ReserveInfo.StartTime == oldWeddingPlan.ReserveInfo.StartTime { // 时间相同,不通知
            return nil
        }

        text = fmt.Sprintf("[婚礼预约调整] 我更新了原定于%s在“%s”开始的婚礼预约，调整为%s开始哦~",
            time.Unix(int64(oldWeddingPlan.ReserveInfo.StartTime), 0).Format("01月02日15:04:05"),
            channelName,
            time.Unix(int64(weddingPlan.ReserveInfo.StartTime), 0).Format("01月02日15:04:05"))
    }
    for _, sendUid := range sendUidList {
        err = m.SendSysTextMutual(ctx, uid, sendUid, text, text)
        if err != nil {
            log.ErrorWithCtx(ctx, "reserveNotify fail to send1v1Text. uid:%d, sendUid: %d, err:%v", uid, sendUid, err)
        }
    }

    // 如果房间也更新了, 要给之前的房管发一条取消
    if isChange && weddingPlan.ReserveInfo.ChannelId != oldWeddingPlan.ReserveInfo.ChannelId {
        sendUidList, channelName, err = m.getChannelNotifyInfo(ctx, oldWeddingPlan.ReserveInfo.ChannelId)
        if err != nil {
            log.ErrorWithCtx(ctx, "reserveNotify fail to getChannelNotifyInfo. err:%v", err)
            return err
        }
        fromText := fmt.Sprintf("对方取消了%s在贵厅的婚礼预订~", time.Unix(int64(oldWeddingPlan.ReserveInfo.StartTime), 0).Format("01月02日15:04:05"))
        toText := fmt.Sprintf("你取消了%s在此婚礼房的婚礼预订~", time.Unix(int64(oldWeddingPlan.ReserveInfo.StartTime), 0).Format("01月02日15:04:05"))
        for _, sendUid := range sendUidList {
            err = m.SendSysTextMutual(ctx, uid, sendUid, fromText, toText)
            if err != nil {
                log.ErrorWithCtx(ctx, "reserveNotify fail to send1v1Text. uid:%d, sendUid: %d, err:%v", uid, sendUid, err)
            }
        }
    }

    return nil
}

func (m *WeddingPlanMgr) getChannelNotifyInfo(ctx context.Context, channelId uint32) ([]uint32, string, error) {
    sendUidList := make([]uint32, 0)

    // 房间管理员
    channelAdmin, err := m.rpc.ChannelCli.GetChannelAdmin(ctx, 0, channelId)
    if err != nil {
        log.ErrorWithCtx(ctx, "reserveNotify fail to GetChannelAdmin. err:%v", err)
        return sendUidList, "", err
    }
    for _, item := range channelAdmin {
        sendUidList = append(sendUidList, item.GetUid())
    }

    // 公会会长
    channelInfo, err := m.rpc.ChannelCli.GetChannelSimpleInfo(ctx, 0, channelId)
    if err != nil {
        log.ErrorWithCtx(ctx, "reserveNotify fail to GetChannelSimpleInfo. err:%v", err)
        return sendUidList, "", err

    }
    guildInfo, err := m.rpc.GuildCli.GetGuild(ctx, channelInfo.GetBindId())
    if err != nil {
        log.ErrorWithCtx(ctx, "reserveNotify fail to GetGuild. err:%v", err)
        return sendUidList, "", err

    }
    sendUidList = append(sendUidList, guildInfo.GetOwner())
    exists := make(map[uint32]struct{})
    for _, item := range sendUidList {
        exists[item] = struct{}{}
    }
    sendUidList = make([]uint32, 0)
    for item := range exists {
        sendUidList = append(sendUidList, item)
    }

    return sendUidList, channelInfo.GetName(), nil
}

//func (m *WeddingPlanMgr) send1v1Text(ctx context.Context, fromUser, toUser *app.UserProfile, text string) error {
//    _, err := m.rpc.ImApiCli.Send1V1Text(ctx, &imapi.Send1V1TextReq{
//        From:      &imapi.User{
//            Uid:      fromUser.GetUid(),
//            Username: fromUser.GetAccount(),
//            Nickname: fromUser.GetNickname(),
//        },
//        To:        &imapi.User{
//            Uid:      toUser.GetUid(),
//            Username: toUser.GetAccount(),
//            Nickname: toUser.GetNickname(),
//        },
//        Text:      &imapi.Text{
//            Content:   text,
//            Highlight: "",
//            Url:       "",
//        },
//    })
//    if err != nil {
//        log.ErrorWithCtx(ctx, "reserveNotify fail to Send1V1Text. err:%v", err)
//        return err
//    }
//    return nil
//}

func (m *WeddingPlanMgr) SendSysTextMutual(ctx context.Context, fromUid, toUid uint32, fromText, toText string) error {
    if fromUid == toUid {
        return nil
    }

    // 发送系统文案
    sendReq := &imApiPB.Send1V1ExtMsgReq{
        From: &imApiPB.User{
            Uid: fromUid,
        },
        To: &imApiPB.User{
            Uid: toUid,
        },
        Msg: &imApiPB.ExtMsg{
            MsgType:       uint32(imPB.IM_MSG_TYPE_SYSTEM_NOTIFY),
            Content:       fromText,
            MsgSourceType: uint32(imPB.MsgSourceType_MSG_SOURCE_FROM_ESPORT),
        },
        Opt: &imApiPB.SendOption{
            IgnoreFrom:     true,
            HasMsgRedpoint: imApiPB.SendOption_BOOL_TRUE,
            HasMsgExposure: imApiPB.SendOption_BOOL_TRUE,
        },
        Namespace: "WEDDING-PLAN",
    }
    _, err := m.rpc.ImApiCli.Send1V1ExtMsg(ctx, sendReq)
    if err != nil {
        log.ErrorWithCtx(ctx, "Send1V1ExtMsg, req: %+v, err: %v", sendReq, err)
    }

    sendReq.From.Uid = toUid
    sendReq.To.Uid = fromUid
    sendReq.Msg.Content = toText
    _, err = m.rpc.ImApiCli.Send1V1ExtMsg(ctx, sendReq)
    if err != nil {
        log.ErrorWithCtx(ctx, "Send1V1ExtMsg, req: %+v, err: %v", sendReq, err)
    }
    log.InfoWithCtx(ctx, "Send1V1ExtMsg, fromUid: %d, toUid: %d", fromUid, toUid)
    return err
}

func (m *WeddingPlanMgr) ReserveChangeNotifyFriends(ctx context.Context, uid uint32, weddingPlan *store.WeddingPlan) error {
    // 通知另一方新人, 伴郎伴娘,亲友团
    cUserprofile, uerr := m.rpc.UserprofileCli.GetUserProfileV2(ctx, uid, false)
    if uerr != nil {
        log.ErrorWithCtx(ctx, "reserveNotify fail to GetUserProfileV2. err:%v", uerr)
        return uerr
    }
    ttLink := fmt.Sprintf(m.bc.GetConfig().ReserveConf.ReserveChangeImTTLink, cUserprofile.GetAccount())
    data := struct {
        NewTime     string
        ConfirmLink string
    }{
        NewTime:     time.Unix(int64(weddingPlan.ReserveInfo.StartTime), 0).Format("01月02日 15:04"),
        ConfirmLink: ttLink,
    }
    xmlContent, rErr := renderTmpl(ctx, "ChangeReserveIm", m.bc.GetChangeReserveImXml(), data)
    if rErr != nil {
        log.ErrorWithCtx(ctx, "reserveNotify fail to renderTmpl. err:%v", rErr)
        return rErr
    }
    other := weddingPlan.GroomUid
    if uid == weddingPlan.GroomUid {
        other = weddingPlan.BrideUid
    }
    xmlSendUidList := []uint32{other}
    for _, item := range weddingPlan.Groomsman {
        xmlSendUidList = append(xmlSendUidList, item.Uid)
    }
    for _, item := range weddingPlan.Bridesmaid {
        xmlSendUidList = append(xmlSendUidList, item.Uid)
    }
    for _, item := range weddingPlan.Friends {
        xmlSendUidList = append(xmlSendUidList, item.Uid)
    }

    for _, sendUid := range xmlSendUidList {
        err := m.sendImCommonXmlMsgNoMsgId(ctx, uid, sendUid, xmlContent, "婚礼时间调整")
        if err != nil {
            log.ErrorWithCtx(ctx, "reserveNotify fail to sendImCommonXmlMsgNoMsgId. err:%v", err)
        }
    }

    return nil
}

