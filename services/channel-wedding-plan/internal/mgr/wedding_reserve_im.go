package mgr

import (
	"context"
	"crypto/rand"
	"fmt"
	"github.com/golang/protobuf/proto"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	grpcProtocol "golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/protocol/app/channel_wedding_logic"
	imPB "golang.52tt.com/protocol/app/im"
	"golang.52tt.com/protocol/common/status"
	channel_wedding_conf "golang.52tt.com/protocol/services/channel-wedding-conf"
	pb "golang.52tt.com/protocol/services/channel-wedding-plan"
	imApiPB "golang.52tt.com/protocol/services/im-api"
	"golang.52tt.com/protocol/services/imstrangergo"
	"golang.52tt.com/services/channel-wedding-plan/internal/store"
	comctx "golang.52tt.com/services/tt-rev/common/ctx"
	"google.golang.org/grpc/codes"
	"math/big"
	"strconv"
	"time"
)

func (m *WeddingPlanMgr) ConsultWeddingReserve(ctx context.Context, request *pb.ConsultWeddingReserveRequest) (*pb.ConsultWeddingReserveResponse, error) {
	resp := &pb.ConsultWeddingReserveResponse{}
	serviceInfo, ok := grpcProtocol.ServiceInfoFromContext(ctx)
	if !ok {
		return nil, fmt.Errorf("GetMyWeddingReserveInfo fail to get serviceInfo")
	}
	log.DebugWithCtx(ctx, "ConsultWeddingReserve, req: %+v", request)

	reserveSt, reserveEt, err := m.TranReserveTimeSection2Time(request.GetReserveDate(), request.GetReserveTime())
	if err != nil {
		log.ErrorWithCtx(ctx, "ConsultWeddingReserve fail to TranReserveTimeSection2Time. req: %+v, err:%v", request, err)
		return resp, err
	}
	if reserveSt < uint32(time.Now().Unix()) {
		return resp, protocol.NewExactServerError(codes.OK, status.ErrChannelWeddingPlanCommonError, "不支持选择本时段哦~")
	}

	// 是否被预约
	canDo, err := m.CheckWeddingPlanByChannelIdAndTimeRange(ctx, request.GetChannelId(), reserveSt, reserveEt)
	if err != nil {
		log.ErrorWithCtx(ctx, "ChangeWeddingReserveInfo fail to CheckWeddingPlanByChannelIdAndTimeRange. err:%v", err)
		return resp, err
	}
	if !canDo {
		log.WarnWithCtx(ctx, "ChangeWeddingReserveInfo fail, channel is not available")
		return resp, protocol.NewExactServerError(codes.OK, status.ErrChannelWeddingMinigameCommonErr, "该时段已被预约，请重新选择时间")
	}
	channelInfoResp, gErr := m.rpc.ChannelCli.GetChannelSimpleInfo(ctx, 0, request.GetChannelId())
	if gErr != nil {
		log.ErrorWithCtx(ctx, "ConsultWeddingReserve fail to GetChannelSimpleInfo. req: %+v, err:%v", request, gErr)
		return resp, gErr
	}

	// 获取新人信息
	marriageInfo, err  := m.GetUserMarriageInfo(ctx, serviceInfo.UserID)
	if err != nil {
		log.ErrorWithCtx(ctx, "ConsultWeddingReserve fail to GetUserMarriageInfo. req: %+v, err:%v", request, err)
		return resp, nil
	}
	if marriageInfo == nil {
		log.ErrorWithCtx(ctx, "ConsultWeddingReserve fail to GetUserMarriageInfo. req: %+v, err:%v", request, err)
		return resp, protocol.NewExactServerError(codes.OK, status.ErrChannelWeddingPlanCommonError, "要有结婚对象才能预约婚礼哦~")
	}

	// 获取解除关系状态
	groomDivideTimeout, err := m.cache.GetMyDivideTimeout(ctx, marriageInfo.GetUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "ConsultWeddingReserve fail to GetMyDivideTimeout. err:%v, req:%+v", err, request)
		return resp, err
	}
	if groomDivideTimeout > 0 {
		log.ErrorWithCtx(ctx, "ConsultWeddingReserve fail to GetMyDivideTimeout. err:%v, req:%+v", err, request)
		return resp, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "解除关系期间不支持预约婚礼哦")
	}

	managerUid := m.getChannelManagerUid(ctx, serviceInfo.UserID, request.GetChannelId())
	if request.GetManagerUid() != managerUid && request.GetManagerUid() != 0 { // 如果跟前端传入的不一致, 使用前端的, 因为前端还要跳转到im
		managerUid = request.GetManagerUid()
	}

	// 判断该时段是否咨询过当前客服
	consultedManagerUid, err := m.cache.GetUserConsultReserveTimeSection(ctx, serviceInfo.UserID, request.GetChannelId(), reserveSt, reserveEt)
	if err != nil {
		log.ErrorWithCtx(ctx, "ConsultWeddingReserve fail to GetUserConsultReserveTimeSection. req: %+v, err:%v", request, err)
		return resp, err
	}
	defer func() { // 标记该时段咨询过, 1h, 并且重新续租该客服的咨询关系
		err = m.cache.SetUserConsultReserveTimeSection(ctx,
			serviceInfo.UserID, request.GetChannelId(), reserveSt, reserveEt, managerUid, 60*time.Minute)
		if err != nil {
			log.WarnWithCtx(ctx, "ConsultWeddingReserve fail to SetUserConsultReserveTimeSection. req: %+v, err:%v", request, err)
		}

		if err := m.cache.SetUserConsultReserveManagerUid(ctx, serviceInfo.UserID, request.GetChannelId(), managerUid, 6*60*time.Minute); err != nil {
			log.WarnWithCtx(ctx, "ConsultWeddingReserve fail to SetUserConsultReserveManagerUid. uid: %d, cid: %d, err:%v", serviceInfo.UserID, request.GetChannelId(), err)
		}
	}()
	if consultedManagerUid == managerUid {
		log.DebugWithCtx(ctx, "ConsultWeddingReserve. req: %+v, err: has been consult", request)
		return resp, nil
	}

	uidList := []uint32{marriageInfo.GetUid(), marriageInfo.GetPartnerUid(), managerUid}
	userProfileResp, err := m.rpc.UserprofileCli.BatchGetUserProfileV2(ctx, uidList, false)
	if err != nil {
		log.ErrorWithCtx(ctx, "ConsultWeddingReserve fail to BatchGetUserProfileV2. req: %+v, err:%v", request, err)
		return resp, err
	}
	groom := userProfileResp[marriageInfo.GetUid()]
	bride := userProfileResp[marriageInfo.GetPartnerUid()]
	if userProfileResp[marriageInfo.GetUid()].GetSex() == 0 {
		groom = userProfileResp[marriageInfo.GetPartnerUid()]
		bride = userProfileResp[marriageInfo.GetUid()]
	}

	msgId := m.st.GenWeddingReserveIMId(ctx)
	consultMsg := &channel_wedding_logic.ConsultWeddingReserveIMMsg{
		ReserveInfo: &channel_wedding_logic.ReserveInfo{
			ChannelName: channelInfoResp.GetName(),
			StartTime:   reserveSt,
			EndTime:     reserveEt,
			ThemeId:     request.GetThemeId(),
			ChannelId:   request.GetChannelId(),
			ReserveDate: request.GetReserveDate(),
			ReserveTime: request.GetReserveTime(),
		},
		Groom:      groom,
		Bride:      bride,
		IsArranged: false,
		Status:     uint32(channel_wedding_logic.WeddingReserveIMStatus_WEDDING_RESERVE_IM_STATUS_VALID),
		MsgId:      msgId,
	}
	msgBytes, _ := proto.Marshal(consultMsg)
	fromServerMsgId, _ := m.rpc.GenerateSequence(ctx, serviceInfo.UserID)
	targetServerMsgId, _ := m.rpc.GenerateSequence(ctx, managerUid)

	// 写入im发送数据
	err = m.st.InsertWeddingReserveIM(ctx, &store.WeddingReserveIM{
		ID:                msgId,
		FromUid:           serviceInfo.UserID,
		TargetUid:         managerUid,
		FromServerMsgId:   fromServerMsgId,
		TargetServerMsgId: targetServerMsgId,
		ReserveInfo:       &store.ReserveImReserveInfo{
			ChannelId:   request.GetChannelId(),
			StartTime:   reserveSt,
			EndTime:     reserveEt,
			ThemeId:     request.GetThemeId(),
		},
		MsgBytes:          msgBytes,
		MsgType:           store.WeddingReserveIMTypeConsult,
		CreateTime:        time.Now(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "ConsultWeddingReserve fail to InsertWeddingReserveIM. req: %+v, err:%v", request, err)
		return resp, err
	}
	_, err = m.rpc.ImApiCli.SendCommonMsg(ctx, &imApiPB.SendCommonMsgReq{
		From: &imApiPB.Entity{
			Type: imApiPB.Entity_USER,
			Id:   serviceInfo.UserID,
		},
		To: &imApiPB.Entity{
			Type: imApiPB.Entity_USER,
			Id:   managerUid,
		},
		Msg: &imApiPB.CommonMsg{
			MsgType: uint32(imPB.IM_MSG_TYPE_WEDDING_CONSULT_RESERVE_IM_MSG),
			Ext:     msgBytes,
			FromSeq: &imApiPB.SeqInfo{
				SvrMsgId: fromServerMsgId,
			},
			ToSeq: &imApiPB.SeqInfo{
				SvrMsgId: targetServerMsgId,
			},
			Content: "[婚礼预约] 想约贵厅的婚礼",
		},
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "ConsultWeddingReserve fail to SendCommonMsg. req: %+v, err:%v", request, err)
		return resp, err
	}
	log.DebugWithCtx(ctx, "ConsultWeddingReserve, fromUid: %d, targetUid: %d, arrangeMsg: %+v", serviceInfo.UserID, managerUid,consultMsg)

	nobilitiPrivilegeResp, err := m.rpc.ImStrangeGoCli.CheckNobilityPrivilege(ctx, &imstrangergo.CheckNobilityPrivilegeReq{
		Uid:       managerUid,
		TargetUid: serviceInfo.UserID,
	})
	if err != nil {
		log.WarnWithCtx(ctx, "ConsultWeddingReserve fail to CheckNobilityPrivilege. req: %+v, err:%v", request, err)
	}
	if nobilitiPrivilegeResp.GetIsLimit() {
		return resp, protocol.NewExactServerError(codes.OK,
			status.ErrChannelWeddingConsultGnobilityIntercept, "您已开启陌生人消息限制，客服可能无法回复消息给您，建议打开消息限制哦~")
	}

	return resp, nil
}

func (m *WeddingPlanMgr) getChannelManagerUid(ctx context.Context, uid, cid uint32) uint32 {
	managerUid, err := m.cache.GetUserConsultReserveManagerUid(context.Background(), uid, cid)
	if err != nil {
		log.WarnWithCtx(ctx, "getChannelManagerUid fail to GetUserConsultReserveManagerUid. uid: %d, cid: %d, err:%v", uid, cid, err)
	}
	if managerUid != 0 {
		return managerUid
	}

	cidStr := strconv.FormatUint(uint64(cid), 10)
	cMgrList := m.bc.GetConfig().ReserveConf.ChannelManager[cidStr]
	if len(cMgrList) == 0 {
		cMgrList = m.bc.GetConfig().ReserveConf.ChannelManager["0"]
	}
	if len(cMgrList) == 0 {
		log.WarnWithCtx(ctx, "getChannelManagerUid fail to get ChannelManager. uid: %d, cid: %d", uid, cid)
		return 0
	}
	tmpCMgrList := make([]uint32, 0, len(cMgrList))
	for _, mgrUid := range cMgrList {
		if mgrUid == uid {
			continue
		}
		tmpCMgrList = append(tmpCMgrList, mgrUid)
	}
	cMgrList = tmpCMgrList

	max := len(cMgrList)
	r, _ := rand.Int(rand.Reader, big.NewInt(int64(max)))
	managerUid = cMgrList[int(r.Int64())]

	return managerUid
}

func (m *WeddingPlanMgr) ArrangeWeddingReserve(ctx context.Context, request *pb.ArrangeWeddingReserveRequest) (*pb.ArrangeWeddingReserveResponse, error) {
	resp := &pb.ArrangeWeddingReserveResponse{}

	serviceInfo, ok := grpcProtocol.ServiceInfoFromContext(ctx)
	if !ok {
		return resp, fmt.Errorf("GetMyWeddingReserveInfo fail to get serviceInfo")
	}
	log.DebugWithCtx(ctx, "ArrangeWeddingReserve, req: %+v", request)

	reserveSt, reserveEt, err := m.TranReserveTimeSection2Time(request.GetReserveDate(), request.GetReserveTime())
	if err != nil {
		log.ErrorWithCtx(ctx, "ArrangeWeddingReserve fail to TranReserveTimeSection2Time. req: %+v, err:%v", request, err)
		return resp, err
	}

	channelInfo, err := m.rpc.ChannelCli.GetChannelSimpleInfo(ctx, 0, request.GetChannelId())
	if err != nil {
		log.ErrorWithCtx(ctx, "ArrangeWeddingReserve fail to GetChannelSimpleInfo. req: %+v, err:%v", request, err)
		return resp, err
	}

	themeCfg, err := m.rpc.WeddingConfCli.GetThemeCfg(ctx, &channel_wedding_conf.GetThemeCfgReq{
		ThemeId: request.GetThemeId(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "ArrangeWeddingReserve fail to GetThemeCfg. req: %+v, err:%v", request, err)
		return resp, err
	}

	price, giftId := m.getThemePriceAndGift(request.GetIsHot(), request.GetGiftId(), themeCfg.GetThemeCfg())
	giftInfoResp, err := m.rpc.PresentCli.GetPresentConfigById(ctx, giftId)
	if err != nil {
		log.ErrorWithCtx(ctx, "ArrangeWeddingReserve fail to GetPresentConfigById. req: %+v, err:%v", request, err)
		return resp, err
	}

	// 获取求婚信息
	marriageInfo, err := m.GetUserMarriageInfo(ctx, request.GetTargetUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "ArrangeWeddingReserve fail to GetUserMarriageInfo. req: %+v, err:%v", request, err)
		return resp, err
	}
	if marriageInfo == nil {
		log.ErrorWithCtx(ctx, "ArrangeWeddingReserve fail to GetUserMarriageInfo. req: %+v, err:%v", request, err)
		return resp, protocol.NewExactServerError(codes.OK, status.ErrChannelWeddingPlanCommonError, "要有结婚对象才能安排婚礼哦~")
	}

	msgId := m.st.GenWeddingReserveIMId(ctx)
	giftList :=  []*channel_wedding_logic.WeddingGiftInfo{
		{
			GiftIcon: giftInfoResp.GetItemConfig().GetIconUrl(),
			GiftName: giftInfoResp.GetItemConfig().GetName(),
			GiftDesc: fmt.Sprintf("价值%d豆", giftInfoResp.GetItemConfig().GetPrice()),
		},
	}
	if request.IsHot {
		giftList = append(giftList, &channel_wedding_logic.WeddingGiftInfo{
			GiftIcon: "https://obs-cdn.52tt.com/tt/fe-moss/tt-server/20250427133800_30498823.png",
			GiftName: "热门婚礼·标识",
			GiftDesc: "房间排序靠前",
		})
	}
	arrangeMsg := &channel_wedding_logic.ArrangeWeddingReserveIMMsg{
		ReserveInfo: &channel_wedding_logic.ReserveInfo{
			ChannelId:   request.GetChannelId(),
			ChannelName: channelInfo.GetName(),
			StartTime:   reserveSt,
			EndTime:     reserveEt,
			IsHot:       request.IsHot,
			GiftId:      giftId,
			ReserveDate: request.GetReserveDate(),
			ReserveTime: request.GetReserveTime(),
			ThemeId:     request.GetThemeId(),
			ThemeName:   themeCfg.GetThemeCfg().GetThemeName(),
		},
		Price:       price,
		GiftList:    giftList,
		IsPaid:      false,
		Status:      uint32(channel_wedding_logic.WeddingReserveIMStatus_WEDDING_RESERVE_IM_STATUS_VALID),
		MsgId:       msgId,
		PayValidHour: 2,
	}
	msgBytes, _ := proto.Marshal(arrangeMsg)
	fromServerMsgId, _ := m.rpc.GenerateSequence(ctx, serviceInfo.UserID)
	targetServerMsgId, _ := m.rpc.GenerateSequence(ctx, request.GetTargetUid())
	err = m.st.InsertWeddingReserveIM(ctx, &store.WeddingReserveIM{
		ID:                msgId,
		FromUid:           serviceInfo.UserID,
		TargetUid:         request.GetTargetUid(),
		FromServerMsgId:   fromServerMsgId,
		TargetServerMsgId: targetServerMsgId,
		MsgBytes:          msgBytes,
		MsgType:           store.WeddingReserveImTypeArrange,
		ReserveInfo:       &store.ReserveImReserveInfo{
			ChannelId:   request.GetChannelId(),
			StartTime:   reserveSt,
			EndTime:     reserveEt,
			ThemeId:     request.GetThemeId(),
			BuyUid:      marriageInfo.GetUid(),
			TargetUid:   marriageInfo.GetPartnerUid(),
		},
		CreateTime:        time.Now(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "ArrangeWeddingReserve fail to InsertWeddingReserveIM. req: %+v, err:%v", request, err)
		return resp, err
	}
	_, err = m.rpc.ImApiCli.SendCommonMsg(ctx, &imApiPB.SendCommonMsgReq{
		From: &imApiPB.Entity{
			Type: imApiPB.Entity_USER,
			Id:   serviceInfo.UserID,
		},
		To: &imApiPB.Entity{
			Type: imApiPB.Entity_USER,
			Id:   request.GetTargetUid(),
		},
		Msg: &imApiPB.CommonMsg{
			MsgType: uint32(imPB.IM_MSG_TYPE_WEDDING_ARRANGE_RESERVE_IM_MSG),
			Ext:     msgBytes,
			FromSeq: &imApiPB.SeqInfo{
				SvrMsgId: fromServerMsgId,
			},
			ToSeq: &imApiPB.SeqInfo{
				SvrMsgId: targetServerMsgId,
			},
			Content: "[婚礼预约] 邀请你预约~",
		},
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "ArrangeWeddingReserve fail to SendCommonMsg. req: %+v, err:%v", request, err)
		return resp, err
	}
	log.DebugWithCtx(ctx, "ArrangeWeddingReserve, fromUid: %d, targetUid: %d, arrangeMsg: %+v", serviceInfo.UserID, request.GetTargetUid(), arrangeMsg)

	// 更新咨询消息状态
	reserveIm, err := m.st.GetWeddingReserveIM(ctx, request.GetSourceMsgId())
	if err != nil {
		log.ErrorWithCtx(ctx, "ArrangeWeddingReserve fail to GetWeddingReserveIM. req: %+v, err:%v", request, err)
		return resp, err
	}
	consultMsg := &channel_wedding_logic.ConsultWeddingReserveIMMsg{}
	err = proto.UnmarshalMerge(reserveIm.MsgBytes, consultMsg)
	if err != nil {
		log.ErrorWithCtx(ctx, "sendConsultImUpdate fail to UnmarshalMerge. reserveIm: %+v, err:%v", reserveIm, err)
		return resp, err
	}
	consultMsg.IsArranged = true
	_ = m.sendConsultImUpdate(ctx, reserveIm, consultMsg)
	// 更新为已完成
	err = m.st.UpdateWeddingReserveIMStatus(ctx, []uint32{reserveIm.ID}, store.WeddingReserveIMStatusSuccess)
	if err != nil {
		log.WarnWithCtx(ctx, "ArrangeWeddingReserve fail to UpdateWeddingReserveIMStatus. sourceMsgId: %d, err:%v", request.GetSourceMsgId(), err)
	}

	// 安排完成后清楚该时段的咨询标记, 可被再次咨询
	if reserveIm.ReserveInfo != nil {
		err = m.cache.DelUserConsultReserveTimeSection(ctx, reserveIm.FromUid, reserveIm.ReserveInfo.ChannelId,
			reserveIm.ReserveInfo.StartTime, reserveIm.ReserveInfo.EndTime)
		if err != nil {
			log.ErrorWithCtx(ctx, "ArrangeWeddingReserve fail to DelUserConsultReserveTimeSection. err:%v", err)
		}

	}
	return resp, nil
}

func (m *WeddingPlanMgr) sendConsultImUpdate(ctx context.Context, reserveIm *store.WeddingReserveIM, consultMsg *channel_wedding_logic.ConsultWeddingReserveIMMsg) error {
	consultMsgBytes, _ := proto.Marshal(consultMsg)
	_, err := m.rpc.ImApiCli.SendCommonMsg(ctx, &imApiPB.SendCommonMsgReq{
		From: &imApiPB.Entity{
			Type: imApiPB.Entity_USER,
			Id:   reserveIm.FromUid,
		},
		To: &imApiPB.Entity{
			Type: imApiPB.Entity_USER,
			Id:   reserveIm.TargetUid,
		},
		Msg: &imApiPB.CommonMsg{
			MsgType: uint32(imPB.IM_MSG_TYPE_WEDDING_CONSULT_RESERVE_IM_MSG),
			Ext:     consultMsgBytes,
			FromSeq: &imApiPB.SeqInfo{
				SvrMsgId: reserveIm.FromServerMsgId,
			},
			ToSeq: &imApiPB.SeqInfo{
				SvrMsgId: reserveIm.TargetServerMsgId,
			},
			Content: "[婚礼预约] 想约贵厅的婚礼",
		},
	})
	if err != nil {
		log.WarnWithCtx(ctx, "ArrangeWeddingReserve fail to SendCommonMsg. reserveIm: %+v, opUid: %d, err: %v", reserveIm, reserveIm.TargetUid, err)
		return err
	}
	log.DebugWithCtx(ctx, "sendConsultImUpdate, fromUid: %d, targetUid: %d, consultMsg: %+v", reserveIm.FromUid, reserveIm.TargetUid, consultMsg)
	return nil
}


func (m *WeddingPlanMgr) sendArrangeImUpdate(c context.Context, reserveIm *store.WeddingReserveIM, arrangeMsg *channel_wedding_logic.ArrangeWeddingReserveIMMsg) error {
	msgBytes, _ := proto.Marshal(arrangeMsg)
	_, err := m.rpc.ImApiCli.SendCommonMsg(c, &imApiPB.SendCommonMsgReq{
		From: &imApiPB.Entity{
			Type: imApiPB.Entity_USER,
			Id:   reserveIm.FromUid,
		},
		To: &imApiPB.Entity{
			Type: imApiPB.Entity_USER,
			Id:   reserveIm.TargetUid,
		},
		Msg: &imApiPB.CommonMsg{
			MsgType: uint32(imPB.IM_MSG_TYPE_WEDDING_ARRANGE_RESERVE_IM_MSG),
			Ext:     msgBytes,
			FromSeq: &imApiPB.SeqInfo{
				SvrMsgId: reserveIm.FromServerMsgId,
			},
			ToSeq: &imApiPB.SeqInfo{
				SvrMsgId: reserveIm.TargetServerMsgId,
			},
			Content: "[婚礼预约] 邀请你预约~",
		},
	})
	if err != nil {
		log.ErrorWithCtx(c, "sendArrangeImUpdate fail to SendCommonMsg. fromUid: %d, targetUid: %d, err:%v", reserveIm.FromUid, reserveIm.TargetUid, err)
		return err
	}
	log.DebugWithCtx(c, "sendArrangeImUpdate, fromUid: %d, targetUid: %d, consultMsg: %+v", reserveIm.FromUid, reserveIm.TargetUid, arrangeMsg)

	return nil
}


func (m *WeddingPlanMgr) AutoExpireConsultArrangeIm(ctx context.Context) {
	ctx, cancel := comctx.WithTimeout(5*time.Minute)
	defer cancel()
	// 获取过期的咨询消息

	st := uint32(time.Now().Unix())
	updateIdList := make([]uint32, 0)
	reserveIm, err := m.st.GetWeddingReserveIMByReserveTimeType(ctx, st, 0)
	if err != nil {
		log.ErrorWithCtx(ctx, "AutoExpireConsultIm fail to GetWeddingReserveIMByReserveTimeType. err:%v", err)
		return
	}

	for _, item := range reserveIm {
		if item.MsgType == store.WeddingReserveIMTypeConsult {
			consultMsg := &channel_wedding_logic.ConsultWeddingReserveIMMsg{}
			err = proto.Unmarshal(item.MsgBytes, consultMsg)
			if err != nil {
				log.ErrorWithCtx(ctx, "AutoExpireConsultIm fail to UnmarshalMerge. reserveIm: %+v, err:%v", item, err)
				continue
			}
			consultMsg.Status = uint32(channel_wedding_logic.WeddingReserveIMStatus_WEDDING_RESERVE_IM_STATUS_EXPIRED)
			_ = m.sendConsultImUpdate(ctx, item, consultMsg)
		} else if item.MsgType == store.WeddingReserveImTypeArrange {
			consultMsg := &channel_wedding_logic.ArrangeWeddingReserveIMMsg{}
			err = proto.Unmarshal(item.MsgBytes, consultMsg)
			if err != nil {
				log.ErrorWithCtx(ctx, "AutoExpireConsultIm fail to UnmarshalMerge. reserveIm: %+v, err:%v", item, err)
				continue
			}
			consultMsg.Status = uint32(channel_wedding_logic.WeddingReserveIMStatus_WEDDING_RESERVE_IM_STATUS_EXPIRED)
			_ = m.sendArrangeImUpdate(ctx, item, consultMsg)
		} else {
			// do nothing
		}

		updateIdList = append(updateIdList, item.ID)
	}

	// 更新状态
	err = m.st.UpdateWeddingReserveIMStatus(ctx, updateIdList, store.WeddingReserveIMStatusExpired)
	if err != nil {
		log.ErrorWithCtx(ctx, "AutoExpireConsultIm fail to UpdateWeddingReserveIMStatus. err:%v", err)
	}
	log.DebugWithCtx(ctx, "AutoExpireConsultIm successfully, updateIdList: %+v", updateIdList)
}

func (m *WeddingPlanMgr) AutoExpirePayArrangeIm(ctx context.Context) {
	ctx, cancel := comctx.WithTimeout(5*time.Minute)
	defer cancel()
	// 获取过期的预约安排消息

	st := uint32(time.Now().Add(-time.Duration(m.bc.GetArrangeReserveImPayValidTime())*time.Minute ).Unix())
	updateIdList := make([]uint32, 0)
	reserveIm, err := m.st.GetWeddingReserveIMByCreateTimeType(ctx, st, store.WeddingReserveImTypeArrange)
	if err != nil {
		log.ErrorWithCtx(ctx, "AutoExpirePayArrangeIm fail to GetWeddingReserveIMByReserveTimeType. err:%v", err)
		return
	}

	for _, item := range reserveIm {
		arrangeMsg := &channel_wedding_logic.ArrangeWeddingReserveIMMsg{}
		err = proto.Unmarshal(item.MsgBytes, arrangeMsg)
		if err != nil {
			log.ErrorWithCtx(ctx, "AutoExpirePayArrangeIm fail to UnmarshalMerge. reserveIm: %+v, err:%v", item, err)
			continue
		}
		arrangeMsg.Status = uint32(channel_wedding_logic.WeddingReserveIMStatus_WEDDING_RESERVE_IM_STATUS_EXPIRED)
		_ = m.sendArrangeImUpdate(ctx, item, arrangeMsg)

		updateIdList = append(updateIdList, item.ID)
	}

	// 更新状态
	err = m.st.UpdateWeddingReserveIMStatus(ctx, updateIdList, store.WeddingReserveIMStatusExpired)
	if err != nil {
		log.ErrorWithCtx(ctx, "AutoExpirePayArrangeIm fail to UpdateWeddingReserveIMStatus. err:%v", err)
	}
	log.DebugWithCtx(ctx, "AutoExpirePayArrangeIm successfully, updateIdList: %+v", updateIdList)
}

func (m *WeddingPlanMgr) BatchGetWeddingReserveIMByIdList(ctx context.Context, idList []uint32) ([]*store.WeddingReserveIM, error) {
	return m.st.BatchGetWeddingReserveIMByIdList(ctx, idList)
}