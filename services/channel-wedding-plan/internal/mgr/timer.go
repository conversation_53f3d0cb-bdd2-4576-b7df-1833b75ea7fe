package mgr

import (
    "context"
    "gitlab.ttyuyin.com/avengers/tyr/pkg/cluster/timer"
    "gitlab.ttyuyin.com/avengers/tyr/pkg/cluster/timer/task/tasks"
    "golang.52tt.com/pkg/log"
    "time"
)

func (m *WeddingPlanMgr) setupTimer() error {
    // 创建定时器
    ctx, cancel := context.WithTimeout(context.Background(), 10*time.Minute)
    defer cancel()

    // 大小进程的定时器，同一个任务只会在一个节点上执行
    timerD, err := timer.NewTimerD(ctx, "channel-wedding-plan", timer.WithV8RedisCmdable(m.cache.GetRedisClient()), timer.WithTTL(10*time.Second))
    if err != nil {
        log.Errorf("setupTimer fail to NewTimerD. err:%v", err)
        return err
    }

    // 定时申请临时房
    timerD.AddIntervalTask("AllocTempChannel", 5*time.Second, tasks.FuncTask(m.AllocTempChannel))
    // 自动预约房间
    timerD.AddIntervalTask("ReserveChannel", 5*time.Second, tasks.FuncTask(m.ReserveChannel))
    // 婚礼准备开始新郎新娘通知
    err = timerD.AddCronTask("0 * * * * *", "NotifyGroomAndBrideWeddingStarting", tasks.FuncTask(m.NotifyGroomAndBrideWeddingStarting))
    if err != nil {
        log.ErrorWithCtx(ctx, "NotifyGroomAndBrideWeddingStarting fail to AddCronTask. err:%v", err)
        return err
    }
    // 婚礼开始新人伴郎伴娘通知
    err = timerD.AddCronTask("0 * * * * *",  "NotifyWeddingStartingNow", tasks.FuncTask(m.NotifyWeddingStartingNow))
    if err != nil {
        log.ErrorWithCtx(ctx, "NotifyWeddingStartingNow fail to AddCronTask. err:%v", err)
        return err
    }
    // 婚礼邀请失效
    err = timerD.AddCronTask("@every 5s", "SetInviteCancel", tasks.FuncTask(m.SetInviteCancel))
    if err != nil {
        log.ErrorWithCtx(ctx, "SetInviteCancel fail to AddCronTask. err:%v", err)
        return err
    }
    // 婚礼完成
    err = timerD.AddCronTask("@every 5s", "SetWeddingPlanFinish", tasks.FuncTask(m.SetWeddingPlanFinish))
    if err != nil {
        log.ErrorWithCtx(ctx, "SetWeddingPlanFinish fail to AddCronTask. err:%v", err)
        return err
    }

    // 处理离婚超时
    err = timerD.AddCronTask("@every 5s", "HandleDivorceTimeout", tasks.FuncTask(m.HandleDivorceTimeout))
    if err != nil {
        log.ErrorWithCtx(ctx, "HandleDivorceTimeout fail to AddCronTask. err:%v", err)
        return err
    }

    // 处理过期的绑定邀请
    if err = timerD.AddCronTask("@every 5s", "HandleTimeOutPropose", tasks.FuncTask(func(ctx context.Context) {
        m.HandleTimeOutPropose()
    })); err != nil {
        log.Errorf("startTimer AddCronTask HandleTimeOutPropose err:%v", err)
        return err
    }

    // 生成下一天的纪念日弹窗信息
    timerD.AddIntervalTask("GenNextDayAnniversaryPopupInfo", time.Hour, tasks.FuncTask(func(ctx context.Context) {
        now := time.Now()
        m.GenNextDayAnniversaryPopupInfo(ctx, now)
    }))

    // 每小时兜底给在线用户发送一次纪念日弹窗
    timerD.AddIntervalTask("SendAnniversaryPopupToOnlineUser", time.Hour, tasks.FuncTask(func(ctx context.Context) {
        now := time.Now()
        m.BatCheckAnniversaryPopupOnlineUser(ctx, now)
    }))

    // 检查订单状态
    timerD.AddIntervalTask("FixOrderStatus", time.Second*10, tasks.FuncTask(func(ctx context.Context) {
        m.FixOrderStatus(ctx)
    }))

    // 婚礼开始通知亲友团和关注者
    if err = timerD.AddCronTask("0 * * * * *", "handleWeddingStartFriendsNotify", tasks.FuncTask(func(ctx context.Context) {
        m.handleWeddingStartFriendsNotify(ctx)
    })); err != nil {
        log.Errorf("startTimer AddCronTask handleWeddingStartFriendsNotify err:%v", err)
        return err
    }

    // 咨询/安排im失效
    err = timerD.AddCronTask("@every 5s", "AutoExpireConsultArrangeIm", tasks.FuncTask(m.AutoExpireConsultArrangeIm))
    if err != nil {
        log.ErrorWithCtx(ctx, "AutoExpireConsultArrangeIm fail to AddCronTask. err:%v", err)
        return err
    }
    // 安排im支付超时失效
    err = timerD.AddCronTask("@every 5s", "AutoExpirePayArrangeIm", tasks.FuncTask(m.AutoExpirePayArrangeIm))
    if err != nil {
        log.ErrorWithCtx(ctx, "AutoExpirePayArrangeIm fail to AddCronTask. err:%v", err)
        return err
    }
    timerD.Start()

    m.timerD = timerD
    return nil
}
