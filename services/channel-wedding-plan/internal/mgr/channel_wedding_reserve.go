package mgr

import (
	"context"
	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/channel-wedding-plan"
	"golang.52tt.com/services/channel-wedding-plan/internal/store"
	"strings"
	"time"
)

func (m *WeddingPlanMgr) GetChannelReservedInfo(ctx context.Context, req *pb.GetChannelReservedInfoRequest) (*pb.GetChannelReservedInfoResponse, error) {
	resp := &pb.GetChannelReservedInfoResponse{}
	if req.GetReserveDate() == 0 {
		req.ReserveDate = uint32(getTimeZero(time.Now()).Unix())
	}

	startTime := time.Unix(int64(req.GetReserveDate()), 0)
	endTime := startTime.Add(time.Hour * 24) // 最早可预约时间

	// 获取近期付费婚礼预约信息
	recentReserveInfo, err := m.st.GetWeddingPlanByThemeTypeTimeRange(ctx, req.GetThemeType(), uint32(startTime.Unix()), uint32(endTime.Unix()), req.GetChannelId())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelReservedInfo fail to GetWeddingPlanByThemeTypeTimeRange. err:%v", err)
		return nil, err
	}

	// 标记已预约的时段
	reservedTimeSection := make(map[uint32]*store.WeddingPlan)
	for _, item := range recentReserveInfo {
		if item.Status >= store.WeddingPlanStatusFinish {
			continue
		}

		if req.GetThemeType() == 0 {
			req.ThemeType = item.ThemeType
		}
		if item.ReserveInfo == nil {
			continue
		}
		st := time.Unix(int64(item.ReserveInfo.StartTime), 0)
		et := time.Unix(int64(item.ReserveInfo.EndTime), 0)
		for st.Before(et) {
			reservedTimeSection[uint32(st.Unix())] = item
			st = st.Add(time.Minute * time.Duration(m.bc.GetConfig().ReserveConf.ReserveTimeSectionMinimum))
		}
	}

	if req.ThemeType == 0 {
		req.ThemeType = store.ThemeTypePay
	}

	// 生成预约字段信息
	for startTime.Before(endTime) {
		partEt := startTime.Add(time.Minute * time.Duration(m.bc.GetReserveTimeSectionDurationConf(startTime)))
		if partEt.Before(time.Now()) {
			startTime = partEt
			continue
		}
		if !m.checkChannelReserveSectionValid(startTime, req.ThemeType) {
			startTime = partEt
			continue
		}
		innerSearchSt := startTime
		innerSearchEt := partEt
		targetWeddingPlan := &store.WeddingPlan{}
		for innerSearchSt.Before(innerSearchEt) {
			if _, ok := reservedTimeSection[uint32(innerSearchSt.Unix())]; ok {
				targetWeddingPlan = reservedTimeSection[uint32(innerSearchSt.Unix())]
				break
			}
			innerSearchSt = innerSearchSt.Add(time.Minute * time.Duration(m.bc.GetConfig().ReserveConf.ReserveTimeSectionMinimum))
		}

		resp.ReserveTimeInfoList = append(resp.ReserveTimeInfoList, &pb.ChannelReserveTimeInfo{
			ReserveTime: startTime.Format("15:04") + "-" + partEt.Format("15:04"),
			GroomUid:    targetWeddingPlan.GroomUid,
			BrideUid:    targetWeddingPlan.BrideUid,
			IsHot:       targetWeddingPlan.IsHot||m.isHotTimeSection(req.GetChannelId(), startTime),
			ThemeId:     targetWeddingPlan.ThemeId,
		})
		startTime = partEt
	}

	minReservableMin := time.Duration(m.bc.GetConfig().ReserveConf.MinReservableMin)
	maxReservableMin := time.Duration(m.bc.GetConfig().ReserveConf.MaxReservableMin)
	if req.ThemeType == store.ThemeTypeFree {
		minReservableMin = time.Duration(m.bc.GetConfig().ReserveConf.FreeWeddingMinReservableMin)
		maxReservableMin = time.Duration(m.bc.GetConfig().ReserveConf.FreeWeddingMaxReservableMin)
	}
	resp.MinReserveDate = uint32(getTimeZero(time.Now().Add(minReservableMin * time.Minute)).Unix())
	resp.MaxReserveDate = uint32(getTimeZero(time.Now().Add(maxReservableMin * time.Minute)).Unix())
	return resp, nil
}

func (m *WeddingPlanMgr) checkChannelReserveSectionValid(sectionSt time.Time, themeType uint32) bool {
	reserveTimeSectionDuration := m.bc.GetReserveTimeConf(sectionSt)
	if themeType == store.ThemeTypeFree {
		reserveTimeSectionDuration = m.bc.GetConfig().ReserveConf.FreeWeddingReserveTimeLimit
	}

	// 计算当天秒数, 判断是否在可预约区间内
	searchStSecond := uint32(sectionSt.Hour()*3600 + sectionSt.Minute()*60 + sectionSt.Second())
	validTimeSection := false
	for _, item := range reserveTimeSectionDuration {
		limitItem := strings.Split(item, "-")
		if len(limitItem) < 2 {
			continue
		}
		limitMin := countSeconds(limitItem[0])
		limitMax := countSeconds(limitItem[1])
		if searchStSecond >= limitMin && searchStSecond < limitMax {
			validTimeSection = true
			break
		}

	}
	return validTimeSection
}

func (m *WeddingPlanMgr) GetChannelReserveTimeSectionConf(ctx context.Context, request *pb.GetChannelReserveTimeSectionConfRequest) (*pb.GetChannelReserveTimeSectionConfResponse, error) {
	resp := &pb.GetChannelReserveTimeSectionConfResponse{}

	startTime := getTimeZero(time.Unix(int64(request.GetDate()), 0))
	endTime := startTime.Add(time.Hour * 24)

	if request.GetChannelId() == 0 {
		return resp, nil
	}

	// 查看该房间当天的预约情况
	reserveInfoList, err := m.st.GetWeddingPlanByThemeTypeTimeRange(ctx, store.ThemeTypePay, uint32(startTime.Unix()), uint32(endTime.Unix()), request.GetChannelId())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelReserveTimeSectionConf fail to GetWeddingPlanByThemeTypeTimeRange. err:%v", err)
		return resp, err
	}

	// 记录被预约的时段的预约人
	reservedMap := make(map[uint32]uint32)
	for _, item := range reserveInfoList {
		if item.Status >= store.WeddingPlanStatusFinish {
			continue
		}

		if item.ReserveInfo == nil {
			continue
		}

		st := time.Unix(int64(item.ReserveInfo.StartTime), 0)
		et := time.Unix(int64(item.ReserveInfo.EndTime), 0)
		for st.Before(et) {
			reservedMap[uint32(st.Unix())] = item.BuyerUid
			st = st.Add(time.Minute * time.Duration(m.bc.GetConfig().ReserveConf.ReserveTimeSectionMinimum))
		}
	}

	// 查看当天的预约时段配置情况
	reserveTimeSectionConf, err := m.st.GetOneDayChannelReserveTimeSectionConf(ctx, request.GetChannelId(), request.GetDate())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelReserveTimeSectionConf fail to GetOneDayChannelReserveTimeSectionConf. err:%v", err)
		return resp, err
	}

	reserveTimeSectionConfMap := make(map[uint32]*store.ChannelReserveTimeSectionConf)
	for _, item := range reserveTimeSectionConf {
		reserveTimeSectionConfMap[item.ReserveSt] = item
	}

	for startTime.Before(endTime) {
		partEt := startTime.Add(time.Minute * time.Duration(m.bc.GetReserveTimeSectionDurationConf(startTime)))
		if partEt.Before(time.Now()) {
			startTime = partEt
			continue
		}

		item := &pb.AdminChannelReserveTimeSectionInfo{
			ReserveSt: uint32(startTime.Unix()),
			ReserveEt: uint32(partEt.Unix()),
			ReservableSwitch: uint32(pb.AdminReservableSwitch_RESERVABLE_SWITCH_OPEN),
		}

		innerSearchSt := startTime
		innerSearchEt := partEt
		for innerSearchSt.Before(innerSearchEt) {
			if _, ok := reservedMap[uint32(innerSearchSt.Unix())]; ok {
				item.ReserveUid = reservedMap[uint32(innerSearchSt.Unix())]
				break
			}
			innerSearchSt = innerSearchSt.Add(time.Minute * time.Duration(m.bc.GetConfig().ReserveConf.ReserveTimeSectionMinimum))
		}

		if conf, ok := reserveTimeSectionConfMap[uint32(startTime.Unix())]; ok {
			if conf.ReserveEt == item.ReserveEt && conf.Type == store.ChannelReserveTimeSectionConfTypeClose {
				item.ReservableSwitch = uint32(pb.AdminReservableSwitch_RESERVABLE_SWITCH_CLOSE)
			}
		}

		resp.ReserveTimeSectionInfoList = append(resp.ReserveTimeSectionInfoList, item)
		startTime = partEt
	}


	return resp, nil
}

func (m *WeddingPlanMgr) SetAdminChannelReserveTimeSectionSwitch(ctx context.Context, request *pb.SetAdminChannelReserveTimeSectionSwitchRequest) (*pb.SetAdminChannelReserveTimeSectionSwitchResponse, error) {
	resp := &pb.SetAdminChannelReserveTimeSectionSwitchResponse{}
	if request.GetReservableSwitch() == uint32(pb.AdminReservableSwitch_RESERVABLE_SWITCH_CLOSE) {
		err := m.st.InsertCloseChannelReserveTimeSectionConf(ctx, request.GetChannelId(), request.GetReserveSt(), request.GetReserveEt())
		if err != nil {
			log.ErrorWithCtx(ctx, "SetAdminChannelReserveTimeSectionSwitch fail to InsertCloseChannelReserveTimeSectionConf. request: %+v, err:%v", request, err)
			return resp, err
		}
	} else {
		err := m.st.DeleteCloseChannelReserveTimeSectionConf(ctx, request.GetChannelId(), request.GetReserveSt(), request.GetReserveEt())
		if err != nil {
			log.ErrorWithCtx(ctx, "SetAdminChannelReserveTimeSectionSwitch fail to DeleteCloseChannelReserveTimeSectionConf. request: %+v, err:%v", request, err)
			return resp, err
		}
	}

	return resp, nil
}