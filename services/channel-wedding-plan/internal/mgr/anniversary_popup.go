package mgr

import (
    "context"
    "fmt"
    "github.com/golang/protobuf/proto"
    "github.com/google/uuid"
    "gitlab.ttyuyin.com/bizFund/bizFund/pkg/protocol"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    push "golang.52tt.com/clients/push-notification/v2"
    protogrpc "golang.52tt.com/pkg/protocol/grpc"
    pushPb "golang.52tt.com/protocol/app/push"
    channel_wedding "golang.52tt.com/protocol/services/channel-wedding"
    push_notification "golang.52tt.com/protocol/services/push-notification/v2"
    user_online "golang.52tt.com/protocol/services/user-online"
    "golang.52tt.com/services/channel-wedding-plan/internal/cache"
    "strings"
    "time"
)

const (
    AnniversaryPopupText = "今天是我们的结婚%s纪念日，想和你再举行一次婚礼~"
)

// GenNextDayAnniversaryPopupInfo 生成下一天的纪念日弹窗信息
func (m *WeddingPlanMgr) GenNextDayAnniversaryPopupInfo(c context.Context, now time.Time) {
    ctx, cancel := context.WithTimeout(protogrpc.WithServiceInfo(context.Background(), &protogrpc.ServiceInfo{RequestID: uuid.New().String()}), 30*time.Minute)
    defer cancel()
    nextDayTime := time.Date(now.Year(), now.Month(), now.Day()+1, 1, 0, 0, 0, time.Local)

    processAnniversaryDay := func(targetTime time.Time) {
        for _, v := range m.bc.GetAnniversaryDayList() {
            exist, err := m.cache.CheckNextIfAnniversaryPopupInfoExist(ctx, v, targetTime)
            if err != nil {
                log.ErrorWithCtx(ctx, "GenNextDayAnniversaryPopupInfo CheckNextIfAnniversaryPopupInfoExist failed err:%v", err)
                continue
            }
            if exist {
                log.InfoWithCtx(ctx, "GenNextDayAnniversaryPopupInfo day:%d already exist time:%v", v, now)
                continue
            }

            if err := m.genDayAnniversaryPopupInfo(ctx, v, targetTime); err != nil {
                log.ErrorWithCtx(ctx, "GenNextDayAnniversaryPopupInfo genDayAnniversaryPopupInfo failed err:%v", err)
            }
        }
    }

    // 先检查生成当天的纪念弹窗信息
    processAnniversaryDay(now)

    // 提前生成下一天可能要推送的纪念日弹窗信息
    processAnniversaryDay(nextDayTime)
}

func (m *WeddingPlanMgr) genDayAnniversaryPopupInfo(ctx context.Context, day int, nextDayTime time.Time) error {
    // 计算wedding day
    weddingDay := time.Date(nextDayTime.Year(), nextDayTime.Month(), nextDayTime.Day()-day, 0, 0, 0, 0, time.Local)
    weddingNextDay := weddingDay.AddDate(0, 0, 1)

    // 1. 批量获取weddingDay当天举办了婚礼的信息列表
    scheduleResp, err := m.rpc.ChannelWeddingSvrCli.GetWeddingRecordByTimeRange(ctx, &channel_wedding.GetWeddingRecordByTimeRangeReq{
        BeginTime: weddingDay.Unix(),
        EndTime:   weddingNextDay.Unix(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "genDayAnniversaryPopupInfo GetWeddingScheduleList failed err:%v", err)
        return err
    }

    cpMap := make(map[string]struct{})
    relationIdList := make([]string, 0)
    for _, v := range scheduleResp.GetCertificateList() {
        relationId := genMarriageRelationId(v.BrideUid, v.GroomUid)
        if _, ok := cpMap[relationId]; !ok {
            cpMap[relationId] = struct{}{}
            relationIdList = append(relationIdList, relationId)
        }
    }

    // 2. 通过relationIds 查询cp当前的婚姻关系
    relationList, err := m.st.GetMarriageRelationByIdList(ctx, relationIdList)
    if err != nil {
        log.ErrorWithCtx(ctx, "genDayAnniversaryPopupInfo GetMarriageRelationByIdList failed err:%v", err)
        return err
    }

    rawCpList := make([]*cache.AnniversaryCp, 0)
    for _, v := range relationList {
        if v.CreateTime.After(weddingNextDay) {
            // 在纪念日之后建立关系的，不需要推送
            log.InfoWithCtx(ctx, "genDayAnniversaryPopupInfo v:%v relation is newer than weddingDay,pass", v)
            continue
        }
        rawCpList = append(rawCpList, &cache.AnniversaryCp{
            UidA: v.FromUid,
            UidB: v.TargetUid,
        })
    }

    // 3. 将可能需要推送的cp塞入缓存中
    err = m.cache.BatSetWeddingAnniversaryPopupInfo(ctx, day, nextDayTime, rawCpList)
    if err != nil {
        log.ErrorWithCtx(ctx, "genDayAnniversaryPopupInfo BatSetWeddingAnniversaryPopupInfo failed err:%v", err)
        return err
    }

    return nil
}

func genMarriageRelationId(brideUid, groomUid uint32) string {
    if brideUid < groomUid {
        return fmt.Sprintf("%d_%d", brideUid, groomUid)
    } else {
        return fmt.Sprintf("%d_%d", groomUid, brideUid)
    }
}

func (m *WeddingPlanMgr) BatCheckAnniversaryPopupOnlineUser(c context.Context, now time.Time) {
    ctx, cancel := context.WithTimeout(protogrpc.WithServiceInfo(context.Background(), &protogrpc.ServiceInfo{RequestID: uuid.New().String()}), 30*time.Minute)
    defer cancel()

    for _, day := range m.bc.GetAnniversaryDayList() {
        cpMap, err := m.cache.GetAllAnniversaryPopupInfo(ctx, day, now)
        if err != nil {
            log.ErrorWithCtx(ctx, "BatCheckAnniversaryPopupOnlineUser GetAllAnniversaryPopupInfo failed err:%v", err)
            continue
        }

        // 批量判断在线用户
        uids := make([]uint32, 0)
        for k, v := range cpMap {
            if k == 0 || v == 0 {
                continue
            }
            uids = append(uids, v)
        }

        if len(uids) == 0 {
            log.InfoWithCtx(ctx, "BatCheckAnniversaryPopupOnlineUser day:%d no user to push, time:%v", day, now)
            continue
        }

        onlineList, err := m.rpc.UserOnlineCli.BatchGetLatestOnlineInfo(ctx, uids)
        if err != nil {
            log.ErrorWithCtx(ctx, "BatCheckAnniversaryPopupOnlineUser BatchGetLatestOnlineInfo failed err:%v", err)
            continue
        }

        onlineUserList := make([]uint32, 0)
        for _, v := range onlineList {
            if v.OnlineType == user_online.OnlineType_ONLINE_TYPE_ONLINE {
                onlineUserList = append(onlineUserList, v.Uid)
            }
        }

        for _, v := range onlineUserList {
            _ = m.sendAnniversaryPopup(ctx, v, cpMap[v], day, now)
        }
    }
}

func (m *WeddingPlanMgr) UserOnlineEventHandle(ctx context.Context, uid uint32, now time.Time) error {
    var anniversaryDay int
    var cpUid uint32
    var err error
    for _, v := range m.bc.GetAnniversaryDayList() {
        anniversaryDay = v
        cpUid, err = m.cache.GetAnniversaryPopupByUid(ctx, uid, v, now)
        if err != nil {
            log.ErrorWithCtx(ctx, "UserOnlineEventHandle CheckIfNeedToPushAnniversaryPopup failed err:%v", err)
            return err
        }
        if cpUid != 0 {
            break // 提前结束
        }
    }
    if cpUid == 0 {
        return nil
    }

    err = m.sendAnniversaryPopup(ctx, uid, cpUid, anniversaryDay, now)
    if err != nil {
        log.ErrorWithCtx(ctx, "UserOnlineEventHandle sendAnniversaryPopup failed err:%v", err)
        return err
    }

    log.InfoWithCtx(ctx, "UserOnlineEventHandle time:%v uid:%d cpUid:%d anniversaryDay:%d", now, uid, cpUid, anniversaryDay)
    return nil
}

func (m *WeddingPlanMgr) sendAnniversaryPopup(ctx context.Context, uid, cpUid uint32, anniversaryDay int, now time.Time) error {
    // 1.记录推送标记
    ok, err := m.cache.SetWeddingAnniversaryPopupFlag(ctx, uid, now)
    if err != nil {
        log.ErrorWithCtx(ctx, "SendAnniversaryPopup SetWeddingAnniversaryPopupFlag failed err:%v", err)
        return err
    }

    if !ok {
        // 已推送
        return nil
    }

    // 2.检查关系
    relations, err := m.st.GetMarriageRelationByIdList(ctx, []string{genMarriageRelationId(uid, cpUid)})
    if err != nil {
        _ = m.cache.DelWeddingAnniversaryPopupFlag(ctx, uid, now)
        log.ErrorWithCtx(ctx, "SendAnniversaryPopup GetMarriageRelationByUid failed err:%v", err)
        return err
    }

    weddingDay := time.Date(now.Year(), now.Month(), now.Day()-anniversaryDay, 0, 0, 0, 0, time.Local)
    if len(relations) < 1 || relations[0].CreateTime.After(weddingDay.AddDate(0, 0, 1)) {
        log.InfoWithCtx(ctx, "sendAnniversaryPopup uid:%d cpUid:%d relation is newer than weddingDay", uid, cpUid)
        _ = m.cache.DelWeddingAnniversaryPopupFlag(ctx, uid, now)
        _ = m.cache.DelAnniversaryPopupInfoByUid(ctx, uid, anniversaryDay, now)
        return nil
    }

    // 3.确认当天是否是纪念日
    isAnniversary, err := m.isAnniversaryDay(ctx, uid, cpUid, weddingDay, relations[0].CreateTime)
    if err != nil {
        log.ErrorWithCtx(ctx, "SendAnniversaryPopup isAnniversaryDay failed err:%v", err)
        _ = m.cache.DelWeddingAnniversaryPopupFlag(ctx, uid, now)
        return err
    }
    if !isAnniversary {
        log.InfoWithCtx(ctx, "sendAnniversaryPopup uid:%d cpUid:%d today is not anniversary day", uid, cpUid)
        _ = m.cache.DelWeddingAnniversaryPopupFlag(ctx, uid, now)
        _ = m.cache.DelAnniversaryPopupInfoByUid(ctx, uid, anniversaryDay, now)
        return nil
    }

    // 4.当天有没有已预约的婚礼
    //scheduleResp, err := m.rpc.ChannelWeddingSvrCli.GetWeddingScheduleList(ctx, &channel_wedding.GetWeddingScheduleListReq{
    //    MinBeginTime: time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local).Unix(),
    //    MaxBeginTime: time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 0, time.Local).Unix(),
    //    Uid:          uid,
    //})
    //if err != nil {
    //    log.ErrorWithCtx(ctx, "sendAnniversaryPopup GetWeddingScheduleList failed err:%v", err)
    //    _ = m.cache.DelWeddingAnniversaryPopupFlag(ctx, uid, now)
    //    return err
    //}
    //if len(scheduleResp.ScheduleList) > 0 {
    //    log.InfoWithCtx(ctx, "sendAnniversaryPopup uid:%d cpUid:%d today has wedding schedule:%v", uid, cpUid, scheduleResp.GetScheduleList()[0])
    //    _ = m.cache.DelWeddingAnniversaryPopupFlag(ctx, uid, now)
    //    return nil
    //}
    // 4. 检查当天没有预约中的婚礼
    plan, err := m.st.GetGoingPlan(ctx, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "SendAnniversaryPopup fail to GetGoingPlan. err:%v, uid:%v", uid, err)
        return err
    }
    if plan != nil && plan.ID != 0 {
        log.ErrorWithCtx(ctx, "SendAnniversaryPopup has going plan(planId:%d). do not push uid:%v", plan.ID, uid)
        return nil
    }

    // 5.推送纪念日弹窗
    err = m.PushAnniversaryPopup(ctx, uid, anniversaryDay)
    if err != nil {
        log.ErrorWithCtx(ctx, "SendAnniversaryPopup PushAnniversaryPopup failed err:%v", err)
        _ = m.cache.DelWeddingAnniversaryPopupFlag(ctx, uid, now)
        return err
    }

    return nil
}

func (m *WeddingPlanMgr) isAnniversaryDay(ctx context.Context, uidA uint32, uidB uint32, weddingDay, relationCTime time.Time, ) (bool, error) {
    certificateResp, err := m.rpc.ChannelWeddingSvrCli.GetUserWeddingCertificate(ctx, &channel_wedding.GetUserWeddingCertificateReq{
        UidA:      uidA,
        UidB:      uidB,
        BeginTime: relationCTime.Unix(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "isAnniversaryDay GetUserWeddingCertificate failed err:%v", err)
        return false, err
    }

    if certificateResp.GetWeddingCertificate().GetWeddingTime() == 0 {
        return false, nil
    }

    // 检查结婚时间是否对的上
    actualWeddingTime := time.Unix(certificateResp.GetWeddingCertificate().GetWeddingTime(), 0)
    if weddingDay.Format("2006-01-02") != actualWeddingTime.Format("2006-01-02") {
        return false, nil
    }

    return true, nil
}

func (m *WeddingPlanMgr) PushAnniversaryPopup(ctx context.Context, uid uint32, day int) error {
    xmlStr := m.bc.GetAnniversaryPopupXml()
    textPlaceholder := "{{customize_text}}"
    dayDesc, ok := m.bc.GetConfig().AnniversaryDayDesc[day]
    if !ok {
        dayDesc = fmt.Sprintf("%d天", day)
    }

    xmlStr = strings.Replace(xmlStr, textPlaceholder, fmt.Sprintf(AnniversaryPopupText, dayDesc), 1)

    opt := &pushPb.CommonTopRichTextDialogNotify{
        Content:       xmlStr,
        AnnounceScope: uint32(pushPb.CommBreakingNewsBaseOpt_OUTSIDE_CHANNEL),
        Duration:      24 * 3600, // 24小时
        Scene: "wedding-plan",
    }

    notification := buildNotification(opt, uint32(pushPb.PushMessage_COMMON_TOP_RICH_TEXT_DIALOG_NOTIFY), "结婚纪念弹窗")

    err := m.rpc.PushCli.PushToUsers(ctx, []uint32{uid}, notification)
    if err != nil {
        log.ErrorWithCtx(ctx, "PushAnniversaryPopup PushToUsers err: %v", err)
        return err
    }
    log.InfoWithCtx(ctx, "PushAnniversaryPopup success, uid: %d day:%d", uid, day)
    return nil
}

func buildNotification(inputMsg proto.Message, cmd uint32, labelString string) *push_notification.CompositiveNotification {
    msg, _ := proto.Marshal(inputMsg)

    pushMessage := &pushPb.PushMessage{
        Cmd:     cmd,
        Content: msg,
    }
    pushMessageBytes, _ := proto.Marshal(pushMessage)

    return &push_notification.CompositiveNotification{
        Sequence:           uint32(time.Now().Unix()),
        TerminalTypeList:   []uint32{protocol.MobileAndroidTT, protocol.MobileIPhoneTT},
        TerminalTypePolicy: push.DefaultPolicy,
        AppId:              0,
        ProxyNotification: &push_notification.ProxyNotification{
            Type:      uint32(push_notification.ProxyNotification_PUSH),
            Payload:   pushMessageBytes,
            PushLabel: labelString,
        },
    }
}
