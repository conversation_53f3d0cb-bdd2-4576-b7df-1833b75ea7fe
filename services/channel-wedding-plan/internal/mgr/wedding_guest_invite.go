package mgr

import (
    "context"
    "fmt"
    "github.com/google/uuid"
    "golang.52tt.com/pkg/log"
    "golang.52tt.com/pkg/protocol"
    grpcProtocol "golang.52tt.com/pkg/protocol/grpc"
    imPB "golang.52tt.com/protocol/app/im"
    "golang.52tt.com/protocol/common/status"
    channel_wedding "golang.52tt.com/protocol/services/channel-wedding"
    imApiPB "golang.52tt.com/protocol/services/im-api"
    "golang.52tt.com/services/channel-wedding-plan/internal/rpc"
    "golang.52tt.com/services/channel-wedding-plan/internal/store"
    "google.golang.org/grpc/codes"
    "time"
)

func (m *WeddingPlanMgr) GetGroomsmanAndBridesmaid(ctx context.Context, weddingPlanId uint32) (groomsman, bridesmaid []*store.WeddingGuest, err error) {
    weddingPlan, err := m.st.GetWeddingPlanById(ctx, weddingPlanId)
    if err != nil {
        return nil, nil, err
    }
    if weddingPlan == nil {
        return nil, nil, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
    }
    return weddingPlan.Groomsman, weddingPlan.Bridesmaid, nil
}

func (m *WeddingPlanMgr) GetWeddingFriends(ctx context.Context, weddingPlanId uint32) (friends []*store.WeddingGuest, err error) {
    weddingPlan, err := m.st.GetWeddingPlanById(ctx, weddingPlanId)
    if err != nil {
        return nil, err
    }
    if weddingPlan == nil {
        return nil, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
    }
    return weddingPlan.Friends, nil
}

func (m *WeddingPlanMgr) GetWeddingInvite(ctx context.Context, weddingPlanId uint32, guestType []uint32) (
    invite []*store.WeddingGuestInvite,
    accept []*store.WeddingGuestInvite,
    reject []*store.WeddingGuestInvite,
    err error) {
    var inviteList []*store.WeddingGuestInvite
    inviteList, err = m.st.GetWeddingGuestInviteByWeddingPlanIdGuestType(ctx, weddingPlanId, guestType)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetWeddingInvite fail to GetWeddingGuestInviteByWeddingPlanIdGuestType. err:%v", err)
        return
    }

    for _, v := range inviteList {
        switch v.InviteStatus {
        case store.InviteStatusWaiting:
            invite = append(invite, v)
        case store.InviteStatusAccepted:
            accept = append(accept, v)
        case store.InviteStatusRejected:
            reject = append(reject, v)
        }
    }
    return
}

func (m *WeddingPlanMgr) InviteWeddingGuest(ctx context.Context, weddingPlanId, guestType, uid, inviteUid uint32) error {
    // 加锁
    lockKey := fmt.Sprintf("%d_%s", weddingPlanId, uuid.New().String())
    err := m.cache.TryLock(ctx, "InviteWeddingGuest", lockKey)
    if err != nil {
        log.ErrorWithCtx(ctx, "InviteWeddingGuest.TryLock, weddingPlanId: %d, err: %d", weddingPlanId, err)
        return err
    }
    defer m.cache.Unlock(ctx, "InviteWeddingGuest", lockKey)

    // 检查是否到达邀请上限
    if err := m.checkGuestSite(ctx, uid, inviteUid, weddingPlanId, guestType); err != nil {
        log.ErrorWithCtx(ctx, "InviteWeddingGuest.checkGuestSite, weddingPlanId: %d, err: %+v", err, err)
        return err
    }

    // 检查当前是否有邀请
    existInvite, err := m.st.GetWeddingGuestInviteByWeddingPlanIdGuestTypeUidStatus(ctx, weddingPlanId, guestType, uid,
        []uint32{store.InviteStatusWaiting, store.InviteStatusAccepted})
    if err != nil {
        log.ErrorWithCtx(ctx, "InviteWeddingGuest.GetWeddingGuestInviteByWeddingPlanIdUid, weddingPlanId: %d, err: %d", weddingPlanId, err)
        return err
    }
    if existInvite != nil {
        return protocol.NewExactServerError(codes.OK, status.ErrChannelWeddingPlanCommonError, "已经邀请过了")
    }

    fromServerMsgId, err := m.rpc.GenerateSequence(ctx, inviteUid)
    if err != nil {
        log.ErrorWithCtx(ctx, "InviteWeddingGuest.GenerateSequence, err: %d", err)
        return err
    }
    targetServerMsgIdMsgId, err := m.rpc.GenerateSequence(ctx, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "InviteWeddingGuest.GenerateSequence, err: %d", err)
        return err
    }

    invite := &store.WeddingGuestInvite{
        WeddingPlanId:     weddingPlanId,
        Uid:               uid,
        InviteUid:         inviteUid,
        GuestType:         guestType,
        InviteStatus:      uint32(store.InviteStatusWaiting),
        FromServerMsgId:   fromServerMsgId,
        TargetServerMsgId: targetServerMsgIdMsgId,
        CreateTime:        time.Now().Unix(),
        UpdateTime:        0,
    }
    inviteId, err := m.st.InsertWeddingGuestInvite(ctx, invite)
    if err != nil {
        log.ErrorWithCtx(ctx, "InviteWeddingGuest.InsertWeddingGuestInvite, weddingPlanId: %d, err: %+v", weddingPlanId, err)
        return err
    }

    // 亲友团无需同意, 直接加入
    if guestType == store.GuestTypeFriend {
        err = m.HandleWeddingInvite(ctx, inviteId, uid, store.InviteStatusAccepted)
        if err != nil {
            log.ErrorWithCtx(ctx, "InviteWeddingGuest.HandleWeddingInvite, inviteId: %d, err: %+v", inviteId, err)
            return err
        }
    }

    // 发送im
    ttLink := fmt.Sprintf(m.bc.GetConfig().InviteIMConfig.InviteTTLink, invite.InviteId, invite.GuestType)
    xmlContent := fmt.Sprintf(rpc.IMXMLImgTemplate, m.getInviteImImg(invite.GuestType), ttLink)
    outsideText := getInviteOutsideText(invite.GuestType)
    err = m.rpc.SendIMCommonXmlMsg(ctx, invite.InviteUid, invite.Uid, fromServerMsgId, targetServerMsgIdMsgId, xmlContent, outsideText)
    if err != nil {
        log.ErrorWithCtx(ctx, "InviteWeddingGuest.SendIMCommonXmlMsg, inviteId: %d, err: %+v", invite.InviteId, err)
    }
    log.DebugWithCtx(ctx, "InviteWeddingGuest.SendIMCommonXmlMsg, formUid: %d, toUid: %d, xmlContent: %s", invite.InviteUid, invite.Uid, xmlContent)

    return nil
}

func getInviteOutsideText(guestType uint32) string {
    switch guestType {
    case store.GuestTypeGroomsman:
        return "[婚礼]邀请你来做我的伴郎"
    case store.GuestTypeBridesmaid:
        return "[婚礼]邀请你来做我的伴娘"
    case store.GuestTypeFriend:
        return "[婚礼]邀请你来我的婚礼"
    default:
        return ""
    }
}

func (m *WeddingPlanMgr) getInviteImImg(guestType uint32) string {
    switch guestType {
    case store.GuestTypeGroomsman:
        return m.bc.GetConfig().InviteIMConfig.GroomsmanInviteImg
    case store.GuestTypeBridesmaid:
        return m.bc.GetConfig().InviteIMConfig.BridesmaidInviteImg
    case store.GuestTypeFriend:
        return m.bc.GetConfig().InviteIMConfig.FriendInviteImg
    default:
        return ""
    }
}

func (m *WeddingPlanMgr) checkGuestSite(ctx context.Context, uid, inviteUid, weddingPlanId, guestType uint32) error {
    weddingPlan, err := m.GetWeddingPlanById(ctx, weddingPlanId, inviteUid)
    if err != nil {
        log.ErrorWithCtx(ctx, "InviteWeddingGuest.checkGuestSite, weddingPlanId: %d, err: %+v", err)
        return err
    }

    if weddingPlan.Status >= store.WeddingPlanStatusFinish {
        return protocol.NewExactServerError(codes.OK, status.ErrChannelWeddingPlanCommonError, "婚礼已结束")
    }

    if uid == weddingPlan.GroomUid || uid == weddingPlan.BrideUid {
        return protocol.NewExactServerError(codes.OK, status.ErrChannelWeddingPlanCommonError, "新郎新娘无法邀请")
    }

    if guestType == store.GuestTypeGroomsman || guestType == store.GuestTypeBridesmaid {
        userprofile, err := m.rpc.UserprofileCli.GetUserProfileV2(ctx, uid, false)
        if err != nil {
            log.ErrorWithCtx(ctx, "InviteWeddingGuest.GetUserProfileV2, uid: %d, err: %d", uid, err)
            return err
        }

        if userprofile.GetSex() == 0 && guestType == store.GuestTypeGroomsman {
            return protocol.NewExactServerError(codes.OK, status.ErrChannelWeddingPlanCommonError, "性别不符，不支持成为ta的伴郎")
        }

        if userprofile.GetSex() == 1 && guestType == store.GuestTypeBridesmaid {
            return protocol.NewExactServerError(codes.OK, status.ErrChannelWeddingPlanCommonError, "性别不符，不支持成为ta的伴娘哦")
        }

        if userprofile.GetSex() == 0 && len(weddingPlan.Bridesmaid) >= 4 {
            return protocol.NewExactServerError(codes.OK, status.ErrChannelWeddingPlanCommonError, "伴娘人数已达上限")
        }

        if userprofile.GetSex() == 1 && len(weddingPlan.Groomsman) >= 4 {
            return protocol.NewExactServerError(codes.OK, status.ErrChannelWeddingPlanCommonError, "伴郎人数已达上限")
        }
    } else {
        if len(weddingPlan.Friends) >= m.bc.GetConfig().GuestInviteConf.MaxWeddingFriendNum {
            return protocol.NewExactServerError(codes.OK, status.ErrChannelWeddingPlanCommonError, "亲友团人数已达上限")
        }
    }

    return nil
}

func (m *WeddingPlanMgr) DelWeddingGuest(ctx context.Context, weddingPlanId, guestType, delUid uint32) error {
    serviceInfo, ok := grpcProtocol.ServiceInfoFromContext(ctx)
    if !ok {
        log.ErrorWithCtx(ctx, "DelWeddingGuest get serviceInfo fail")
        return nil
    }

    weddingPlan, err := m.GetWeddingPlanById(ctx, weddingPlanId, serviceInfo.UserID)
    if err != nil {
        log.ErrorWithCtx(ctx, "DelWeddingGuest.GetWeddingPlanById, weddingPlanId: %d, err: %+v", weddingPlanId, err)
        return err
    }

    if weddingPlan.Status == store.WeddingPlanStatusReserved && (guestType == store.GuestTypeGroomsman ||
        guestType == store.GuestTypeBridesmaid) { // 已预约房间不能修改伴郎伴娘, 会影响服装下发逻辑
        return protocol.NewExactServerError(codes.OK, status.ErrChannelWeddingPlanCommonError, "婚礼中不支持删除伴郎/伴娘哦~")
    }

    switch guestType {
    case store.GuestTypeGroomsman:
        newGroomsman := excludeWeddingGuest(weddingPlan.Groomsman, delUid)
        err := m.st.UpdateWeddingGroomsman(ctx, weddingPlanId, newGroomsman)
        if err != nil {
            log.ErrorWithCtx(ctx, "DelWeddingGuest.UpdateWeddingGroomsman, weddingPlanId: %d, err: %+v", weddingPlanId, err)
            return err
        }
    case store.GuestTypeBridesmaid:
        newBridesmaid := excludeWeddingGuest(weddingPlan.Bridesmaid, delUid)
        err := m.st.UpdateWeddingBridesmaid(ctx, weddingPlanId, newBridesmaid)
        if err != nil {
            log.ErrorWithCtx(ctx, "DelWeddingGuest.UpdateWeddingBridesmaid, weddingPlanId: %d, err: %+v", weddingPlanId, err)
            return err
        }
    case store.GuestTypeFriend:
        newFriends := excludeWeddingGuest(weddingPlan.Friends, delUid)
        err := m.st.UpdateWeddingFriends(ctx, weddingPlanId, newFriends)
        if err != nil {
            log.ErrorWithCtx(ctx, "DelWeddingGuest.UpdateWeddingFriends, weddingPlanId: %d, err: %+v", weddingPlanId, err)
            return err
        }
    default:
        // do nothing...
    }

    // 更新邀请状态, 置为已删除, 不影响再邀请
    invite, err := m.st.GetWeddingGuestInviteByWeddingPlanIdGuestTypeUidStatus(ctx, weddingPlanId, guestType, delUid, []uint32{store.InviteStatusAccepted})
    if err != nil {
        log.ErrorWithCtx(ctx, "DelWeddingGuest.GetWeddingGuestInviteByWeddingPlanIdUid, weddingPlanId: %d, err: %+v", weddingPlanId, err)
        return err
    }
    if invite == nil {
        return protocol.NewExactServerError(codes.OK, status.ErrChannelWeddingPlanCommonError, "未被邀请")
    }
    err = m.st.UpdateWeddingGuestInviteStatus(ctx, delUid, invite.InviteId, store.InviteStatusDeleted)
    if err != nil {
        log.ErrorWithCtx(ctx, "DelWeddingGuest.GetWeddingGuestInviteByWeddingPlanIdUid, inviteId: %d, err: %+v", invite.InviteId, err)
        return err
    }

    userprofile, err := m.rpc.UserprofileCli.BatchGetUserProfileV2(ctx, []uint32{invite.InviteUid, delUid}, false)
    if err != nil {
        log.ErrorWithCtx(ctx, "DelWeddingGuest.BatchGetUserProfileV2, err: %v", err)
        return err
    }

    idenStr := "伴郎"
    if guestType == store.GuestTypeBridesmaid {
        idenStr = "伴娘"
    }
    femaleWord := "她"
    if userprofile[delUid].GetSex() == 1 {
        femaleWord = "他"
    }
    // 发送系统文案
    sendReq := &imApiPB.Send1V1ExtMsgReq{
        From: &imApiPB.User{
            Uid: invite.InviteUid,
        },
        To: &imApiPB.User{
            Uid: delUid,
        },
        Msg: &imApiPB.ExtMsg{
            MsgType:       uint32(imPB.IM_MSG_TYPE_SYSTEM_NOTIFY),
            Content:       fmt.Sprintf("抱歉, %s取消了你的%s身份", userprofile[invite.InviteUid].GetNickname(), idenStr),
            MsgSourceType: uint32(imPB.MsgSourceType_MSG_SOURCE_FROM_ESPORT),
        },
        Opt: &imApiPB.SendOption{
            IgnoreFrom:     true,
            HasMsgRedpoint: imApiPB.SendOption_BOOL_TRUE,
            HasMsgExposure: imApiPB.SendOption_BOOL_TRUE,
        },
        Namespace: "WEDDING-PLAN",
    }
    _, err = m.rpc.ImApiCli.Send1V1ExtMsg(ctx, sendReq)
    if err != nil {
        log.ErrorWithCtx(ctx, "Send1V1ExtMsg, req: %+v, err: %v", sendReq, err)
    }

    sendReq.From.Uid = delUid
    sendReq.To.Uid = invite.InviteUid
    sendReq.Msg.Content = fmt.Sprintf("抱歉, 你取消了%s的%s身份", femaleWord, idenStr)
    _, err = m.rpc.ImApiCli.Send1V1ExtMsg(ctx, sendReq)
    if err != nil {
        log.ErrorWithCtx(ctx, "Send1V1ExtMsg, req: %+v, err: %v", sendReq, err)
    }

    return nil
}

func excludeWeddingGuest(origin []*store.WeddingGuest, excludeUid uint32) []*store.WeddingGuest {
    rs := make([]*store.WeddingGuest, 0)
    for _, item := range origin {
        if item.Uid == excludeUid {
            continue
        }
        rs = append(rs, item)
    }
    return rs
}

func (m *WeddingPlanMgr) HandleWeddingInvite(ctx context.Context, inviteId, uid, inviteStatus uint32) error {
    // 检查是否被邀请
    noInviteErr := protocol.NewExactServerError(codes.OK, status.ErrChannelWeddingPlanCommonError, "邀请已失效")
    invite, err := m.st.GetWeddingGuestInviteWaitingByInviteIdUid(ctx, inviteId, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "HandleWeddingInvite.GetWeddingGuestInviteWaitingByInviteIdUid, inviteId: %d, err: %+v", inviteId, err)
        return noInviteErr
    }
    weddingPlanId := invite.WeddingPlanId

    // 获取婚礼信息
    weddingPlan, err := m.st.GetWeddingPlanById(ctx, weddingPlanId)
    if err != nil {
        log.ErrorWithCtx(ctx, "HandleWeddingInvite.GetWeddingPlanById, weddingPlanId: %d, err: %+v", weddingPlanId, err)
        return err
    }
    if weddingPlan == nil || weddingPlan.ReserveInfo == nil {
        log.ErrorWithCtx(ctx, "HandleWeddingInvite.GetWeddingPlanById, weddingPlanId: %d, err: %+v", weddingPlanId, err)
        return protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
    }
    if weddingPlan.Status == store.WeddingPlanStatusFinish {
        log.ErrorWithCtx(ctx, "HandleWeddingInvite.GetWeddingPlanById, weddingPlanId: %d, err: %+v", weddingPlanId, err)
        return protocol.NewExactServerError(codes.OK, status.ErrChannelWeddingPlanCommonError, "婚礼已结束")
    }
    if weddingPlan.Status >= store.WeddingPlanStatusCancel {
        log.ErrorWithCtx(ctx, "HandleWeddingInvite.GetWeddingPlanById, weddingPlanId: %d, err: %+v", weddingPlanId, err)
        return protocol.NewExactServerError(codes.OK, status.ErrChannelWeddingPlanCommonError, "婚礼已失效")
    }

    if inviteStatus == store.InviteStatusAccepted {
        // 性别不符要置为失效
        userprofile, err := m.rpc.UserprofileCli.GetUserProfileV2(ctx, uid, false)
        if err != nil {
            log.ErrorWithCtx(ctx, "HandleWeddingInvite.GetUserProfileV2, uid: %d, err: %d", uid, err)
            return err
        }
        var invalidSexErr error
        if userprofile.GetSex() == 0 && invite.GuestType == store.GuestTypeGroomsman {
            invalidSexErr = protocol.NewExactServerError(codes.OK, status.ErrChannelWeddingPlanCommonError, "性别不符，不支持成为ta的伴郎")
        }
        if userprofile.GetSex() == 1 && invite.GuestType == store.GuestTypeBridesmaid {
            invalidSexErr = protocol.NewExactServerError(codes.OK, status.ErrChannelWeddingPlanCommonError, "性别不符，不支持成为ta的伴娘哦")
        }
        if invalidSexErr != nil {
            err := m.st.UpdateWeddingGuestInviteStatus(ctx, uid, inviteId, store.InviteStatusCanceled)
            if err != nil {
                log.WarnWithCtx(ctx, "HandleWeddingInvite.UpdateWeddingGuestInviteStatus, inviteId: %d, err: %+v", inviteId, err)
            }
            ttLink := fmt.Sprintf(m.bc.GetConfig().InviteIMConfig.InviteTTLink, invite.InviteId, invite.GuestType)
            xmlContent := fmt.Sprintf(rpc.IMXMLImgTemplate, m.bc.GetConfig().InviteIMConfig.FailureInviteImg, ttLink)
            err = m.rpc.SendIMCommonXmlMsg(ctx, invite.InviteUid, invite.Uid, invite.FromServerMsgId, invite.TargetServerMsgId, xmlContent, "[邀请已失效]")
            if err != nil {
                log.WarnWithCtx(ctx, "HandleWeddingInvite.SendIMCommonXmlMsg, inviteId: %d, err: %+v", invite.InviteId, err)
            }
            return invalidSexErr
        }

        // 检查是否到达邀请上限
        if err := m.checkGuestSite(ctx, invite.Uid, invite.InviteUid, weddingPlanId, invite.GuestType); err != nil {
            log.ErrorWithCtx(ctx, "HandleWeddingInvite.checkGuestSite, weddingPlanId: %d, err: %+v", weddingPlanId, err)
            return err
        }

        switch invite.GuestType {
        case store.GuestTypeGroomsman:
            newGroomsman := append(weddingPlan.Groomsman, &store.WeddingGuest{Uid: invite.Uid, InviteUid: invite.InviteUid, CreateTime: uint32(time.Now().Unix())})
            err := m.st.UpdateWeddingGroomsman(ctx, weddingPlanId, newGroomsman)
            if err != nil {
                log.ErrorWithCtx(ctx, "HandleWeddingInvite.UpdateWeddingGroomsman, weddingPlanId: %d, err: %+v", weddingPlanId, err)
                return err
            }
        case store.GuestTypeBridesmaid:
            newBridesmaid := append(weddingPlan.Bridesmaid, &store.WeddingGuest{Uid: invite.Uid, InviteUid: invite.InviteUid, CreateTime: uint32(time.Now().Unix())})
            err := m.st.UpdateWeddingBridesmaid(ctx, weddingPlanId, newBridesmaid)
            if err != nil {
                log.ErrorWithCtx(ctx, "HandleWeddingInvite.UpdateWeddingBridesmaid, weddingPlanId: %d, err: %+v", weddingPlanId, err)
                return err
            }
        case store.GuestTypeFriend:
            newFriends := append(weddingPlan.Friends, &store.WeddingGuest{Uid: invite.Uid, InviteUid: invite.InviteUid, CreateTime: uint32(time.Now().Unix())})
            err := m.st.UpdateWeddingFriends(ctx, weddingPlanId, newFriends)
            if err != nil {
                log.ErrorWithCtx(ctx, "HandleWeddingInvite.UpdateWeddingFriends, weddingPlanId: %d, err: %+v", weddingPlanId, err)
                return err
            }
        default:
            // do nothing...
        }

        log.InfoWithCtx(ctx, "HandleWeddingInvite.HandleWeddingInvite, invite: %+d, weddingPlan: %+v", invite, weddingPlan)
        if invite.GuestType != store.GuestTypeFriend && weddingPlan.Status == store.WeddingPlanStatusReserved {
            _, err := m.rpc.ChannelWeddingSvrCli.ReportWeddingBridesmaidMan(ctx, &channel_wedding.ReportWeddingBridesmaidManReq{
                PlanId:            weddingPlan.ID,
                Cid:               weddingPlan.ReserveInfo.ChannelId,
                BridesmaidUidList: []uint32{invite.Uid},
                BuyerUid:          weddingPlan.BuyerUid,
            })
            if err != nil {
                log.WarnWithCtx(ctx, "HandleWeddingInvite.ReportWeddingBridesmaidMan, weddingPlanId: %d, err: %+v", weddingPlanId, err)
            }
        }
    }

    // 更新状态
    err = m.st.UpdateWeddingGuestInviteStatus(ctx, uid, inviteId, inviteStatus)
    if err != nil {
        log.ErrorWithCtx(ctx, "HandleWeddingInvite.UpdateWeddingGuestInviteStatus, inviteId: %d, err: %+v", inviteId, err)
        return err
    }

    // 发送im
    if invite.GuestType != store.GuestTypeFriend {
        ttLink := fmt.Sprintf(m.bc.GetConfig().InviteIMConfig.InviteTTLink, invite.InviteId, invite.GuestType)
        imImg := m.bc.GetConfig().InviteIMConfig.AcceptInviteImg
        outsideText := "已同意"
        if inviteStatus == store.InviteStatusRejected {
            imImg = m.bc.GetConfig().InviteIMConfig.RefuseInviteImg
            outsideText = "已拒绝"
        }
        xmlContent := fmt.Sprintf(rpc.IMXMLImgTemplate, imImg, ttLink)
        err = m.sendImCommonXmlMsgNoMsgId(ctx, invite.Uid, invite.InviteUid, xmlContent, outsideText)
        if err != nil {
            log.ErrorWithCtx(ctx, "HandleWeddingInvite.SendIMCommonXmlMsg, inviteId: %d, err: %+v", invite.InviteId, err)
        }

    }

    return nil
}

func (m *WeddingPlanMgr) GetWeddingGuestInviteByWeddingPlanIdGuestTypeUid(ctx context.Context, weddingPlanId, guestType, uid uint32) (*store.WeddingGuestInvite, error) {
    return m.st.GetWeddingGuestInviteByWeddingPlanIdGuestTypeUidStatus(ctx, weddingPlanId, guestType, uid, []uint32{
        store.InviteStatusWaiting, store.InviteStatusAccepted, store.InviteStatusRejected, store.InviteStatusCanceled})
}

func (m *WeddingPlanMgr) GetWeddingGuestInviteById(ctx context.Context, inviteId uint32) (*store.WeddingGuestInvite, error) {
    return m.st.GetWeddingGuestInviteById(ctx, inviteId)
}

func (m *WeddingPlanMgr) HandleGroomsmanAndBridesmaidModifySex(ctx context.Context, uid uint32) error {
    log.InfoWithCtx(ctx, "HandleGroomsmanAndBridesmaidModifySex, uid: %d", uid)

    maxReservableMin := m.bc.GetConfig().ReserveConf.MaxReservableMin
    if m.bc.GetConfig().ReserveConf.FreeWeddingMaxReservableMin > maxReservableMin {
        maxReservableMin = m.bc.GetConfig().ReserveConf.FreeWeddingMaxReservableMin
    }
    searchSt := time.Now().Add(-time.Duration(maxReservableMin) * time.Minute).Unix()
    inviteList, err := m.st.GetGoingWeddingBridesmaidInvite(ctx, uid, uint32(searchSt))
    if err != nil {
        log.ErrorWithCtx(ctx, "HandleGroomsmanAndBridesmaidModifySex, uid: %d, err: %v", uid, err)
        return err
    }

    for _, item := range inviteList {
        weddingPlan, err := m.st.GetWeddingPlanById(ctx, item.WeddingPlanId)
        if err != nil {
            log.ErrorWithCtx(ctx, "HandleGroomsmanAndBridesmaidModifySex.GetWeddingPlanById, planId: %d, err: %v", item.WeddingPlanId, err)
            continue
        }
        if weddingPlan == nil {
            log.ErrorWithCtx(ctx, "HandleGroomsmanAndBridesmaidModifySex, uid: %d, planId: %d, err: %v", uid, item.WeddingPlanId, err)
            continue
        }

        // 删除伴郎伴娘
        if item.GuestType == store.GuestTypeGroomsman {
            newGroomsman := make([]*store.WeddingGuest, 0)
            for _, groomsmanItem := range weddingPlan.Groomsman {
                if groomsmanItem.Uid == item.Uid {
                    continue
                }
                newGroomsman = append(newGroomsman, groomsmanItem)
            }
            err = m.st.UpdateWeddingGroomsman(ctx, item.WeddingPlanId, newGroomsman)
            if err != nil {
                log.ErrorWithCtx(ctx, "HandleGroomsmanAndBridesmaidModifySex.UpdateWeddingGroomsman, planId: %d, err: %v", item.WeddingPlanId, err)
            }
        }
        if item.GuestType == store.GuestTypeBridesmaid {
            newBridesmaid := make([]*store.WeddingGuest, 0)
            for _, bridesmaidItem := range weddingPlan.Bridesmaid {
                if bridesmaidItem.Uid == item.Uid {
                    continue
                }
                newBridesmaid = append(newBridesmaid, bridesmaidItem)
            }
            err = m.st.UpdateWeddingBridesmaid(ctx, item.WeddingPlanId, newBridesmaid)
            if err != nil {
                log.ErrorWithCtx(ctx, "HandleGroomsmanAndBridesmaidModifySex.UpdateWeddingBridesmaid, planId: %d, err: %v", item.WeddingPlanId, err)
            }
        }

        // 更新邀请状态, im消息
        err = m.st.UpdateWeddingGuestInviteStatus(ctx, item.Uid, item.InviteId, store.InviteStatusCanceled)
        if err != nil {
            log.ErrorWithCtx(ctx, "HandleGroomsmanAndBridesmaidModifySex.UpdateWeddingGuestInviteStatus, inviteId: %d, err:%v", item.InviteId, err)
        }

        ttLink := fmt.Sprintf(m.bc.GetConfig().InviteIMConfig.InviteTTLink, item.InviteId, item.GuestType)
        if item.GuestType == store.GuestTypeFriend { // 亲友团不能点
            ttLink = ""
        }
        xmlContent := fmt.Sprintf(rpc.IMXMLImgTemplate, m.bc.GetConfig().InviteIMConfig.FailureInviteImg, ttLink)
        log.DebugWithCtx(ctx, "HandleGroomsmanAndBridesmaidModifySex xmlContent: %s, inviteUid: %d, uid: %d", xmlContent, item.InviteUid, item.Uid)
        err = m.rpc.SendIMCommonXmlMsg(ctx, item.InviteUid, item.Uid, item.FromServerMsgId, item.TargetServerMsgId, xmlContent, "[邀请已失效]")
        if err != nil {
            log.ErrorWithCtx(ctx, "HandleGroomsmanAndBridesmaidModifySex.SendIMCommonXmlMsg. uid: %d, toUid: %d, xmlContent: %s, err:%v", item.Uid, item.InviteUid, xmlContent, err)
        }

        ttAssistantText := "切换性别成功，您当前的所有%s身份均已自动失效哦~"
        _1v1Text := "此次%s邀请已失效"
        identStr := "伴郎"
        if item.GuestType == store.GuestTypeBridesmaid {
            identStr = "伴娘"
        }
        // tt助手推送
        m.SendTTAssistantText(ctx, item.Uid, fmt.Sprintf(ttAssistantText, identStr), "", "")

        // 推送IM
        err = m.SendSysTextMutual(ctx, item.Uid, item.InviteUid, fmt.Sprintf(_1v1Text, identStr), fmt.Sprintf(_1v1Text, identStr))
        if err != nil {
            log.ErrorWithCtx(ctx, "HandleGroomsmanAndBridesmaidModifySex.SendSysTextMutual, uid: %d, toUid: %d,  err: %v", item.Uid, item.InviteUid, err)
        }
    }

    return nil
}
