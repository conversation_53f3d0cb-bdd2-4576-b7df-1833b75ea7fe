package mgr

import (
    "bytes"
    "context"
    "fmt"
    "gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
    "gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channelol_go"
    push "golang.52tt.com/clients/push-notification/v2"
    "golang.52tt.com/pkg/log"
    "golang.52tt.com/pkg/protocol"
    channelPB "golang.52tt.com/protocol/app/channel"
    "golang.52tt.com/protocol/app/channel_wedding_logic"
    pushPb "golang.52tt.com/protocol/app/push"
    "golang.52tt.com/protocol/common/status"
    channel_wedding "golang.52tt.com/protocol/services/channel-wedding"
    pb "golang.52tt.com/protocol/services/channel-wedding-plan"
    push_notification "golang.52tt.com/protocol/services/push-notification/v2"
    tmp_channel_alloc "golang.52tt.com/protocol/services/tmp-channel-alloc"
    "golang.52tt.com/services/channel-wedding-plan/internal/rpc"
    "golang.52tt.com/services/channel-wedding-plan/internal/store"
    comctx "golang.52tt.com/services/tt-rev/common/ctx"
    "golang.52tt.com/services/tt-rev/common/goroutineex"
    "golang.52tt.com/services/tt-rev/esport/common/collection/transform"
    "text/template"
    "time"
)

const (
    ChannelWeddingSchemeId = 10024
    ChannelWeddingMicMode  = 99
)

func (m *WeddingPlanMgr) AllocTempChannel(_ context.Context) {
    ctx, cancel := comctx.WithTimeout(time.Minute)
    defer cancel()
    st := time.Now()
    defer func() {
        log.Debugf("AllocTempChannel cost: %vms", time.Since(st).Milliseconds())
    }()

    // 查询N分钟后的一分钟区间内数据去分配临时房间
    aheadAllocTempChannel := m.bc.GetConfig().ReserveConf.AheadAllocTempChannel
    startTime := time.Now().Add(-24 * time.Hour) // 前向搜索24h, 兼容测试数据
    endTime := time.Now().Add(time.Duration(aheadAllocTempChannel) * time.Minute)

    for i := 0; i < 100; i++ {
        noChannelWeddingPlanData, err := m.st.GetNoChannelFreeWeddingPlanByTimeRange(ctx, uint32(startTime.Unix()), uint32(endTime.Unix()), 100)
        if err != nil {
            log.ErrorWithCtx(ctx, "AllocTempChannel fail to GetNoChannelFreeWeddingPlanByTimeRange. err:%v", err)
            return
        }
        if len(noChannelWeddingPlanData) == 0 {
            return
        }

        for _, item := range noChannelWeddingPlanData {
            if item.ReserveInfo == nil {
                log.ErrorWithCtx(ctx, "AllocTempChannel weddingPlan.ReserveInfo is nil. weddingPlanId:%v", item.ID)
                continue
            }
            allocReq := &tmp_channel_alloc.AllocReq{
                Operator:            "wedding-room",
                ChannelType:         uint32(channelPB.ChannelType_TEMP_KH_CHANNEL_TYPE),
                SchemeId:            ChannelWeddingSchemeId,
                ChannelName:         "免费婚礼仪式",
                Desc:                "",  // 房间描述
                TopicDetail:         "",  // 房间话题详情
                BackgroundExpireSec: 600, // 房间背景过期时长
                Strategy:            &tmp_channel_alloc.ReleaseStrategy{ExpireSec: 3600 * 10},
            }

            allocResp, err := m.rpc.TmpChannelAllocCli.AllocV2(ctx, allocReq)
            if err != nil {
                log.ErrorWithCtx(ctx, "AllocTempChannel fail to Alloc. err:%v", err)
                continue
            }

            sErr := m.st.UpdateWeddingReserveInfo(ctx, item.ID, &store.ReserveInfo{
                ChannelId:   allocResp.GetChannelId(),
                StartTime:   item.ReserveInfo.StartTime,
                EndTime:     item.ReserveInfo.EndTime,
                ChangeTimes: item.ReserveInfo.ChangeTimes,
            })

            if sErr != nil {
                log.ErrorWithCtx(ctx, "AllocTempChannel fail to UpdateWeddingReserveInfo. err:%v", sErr)
                continue
            }
            log.ErrorWithCtx(ctx, "AllocTempChannel, weddingPlanId: %d, channelId: %d", item.ID, allocResp.GetChannelId())
        }
    }
}

func (m *WeddingPlanMgr) ReserveChannel(_ context.Context) {
    ctx, cancel := comctx.WithTimeout(time.Minute)
    defer cancel()
    st := time.Now()
    defer func() {
        log.Debugf("ReserveChannel cost: %vms", time.Since(st).Milliseconds())
    }()

    aheadReserveTime := m.bc.GetConfig().ReserveConf.AheadReserveTime
    startTime := time.Now()
    endTime := time.Now().Add(time.Duration(aheadReserveTime) * time.Minute)

    for i := 0; i < 100; i++ {
        weddingPlanData, err := m.st.GetWeddingPlanByTimeRangeStatus(ctx, uint32(startTime.Unix()), uint32(endTime.Unix()), 100, []uint32{store.WeddingPlanStatusInit})
        if err != nil {
            log.ErrorWithCtx(ctx, "ReserveChannel fail to GetWeddingPlanByTimeRangeStatus. err:%v", err)
            return
        }

        if len(weddingPlanData) == 0 {
            return
        }

        weddingPlanIdList := make([]uint32, 0, len(weddingPlanData))
        for _, item := range weddingPlanData {
            weddingPlanIdList = append(weddingPlanIdList, item.ID)
        }
        weddingOrderData, err := m.st.BatchGetWeddingOrderByPlanIdList(ctx, weddingPlanIdList)
        if err != nil {
            log.ErrorWithCtx(ctx, "ReserveChannel fail to BatchGetWeddingOrderByPlanIdList. err:%v", err)
        }
        weddingOrderMap := make(map[uint32]*store.WeddingOrder)
        for _, item := range weddingOrderData {
            weddingOrderMap[item.Id] = item
        }

        reserveSuccessIdList := make([]uint32, 0, len(weddingPlanData))
        for _, item := range weddingPlanData {
            if item.ReserveInfo == nil {
                log.ErrorWithCtx(ctx, "ReserveChannel weddingPlan.ReserveInfo is nil. weddingPlanId:%v", item.ID)
                continue
            }
            if item.ReserveInfo.ChannelId == 0 {
                log.ErrorWithCtx(ctx, "ReserveChannel weddingPlan.ReserveInfo.ChannelId is 0. weddingPlanId:%v", item.ID)
                continue
            }
            bmul := make([]uint32, 0, len(item.Groomsman)+len(item.Bridesmaid))
            for _, v := range item.Groomsman {
                bmul = append(bmul, v.Uid)
            }
            for _, v := range item.Bridesmaid {
                bmul = append(bmul, v.Uid)
            }
            weddingOrder, ok := weddingOrderMap[item.ID]
            if !ok {
                log.ErrorWithCtx(ctx, "ReserveChannel fail to get weddingOrder. weddingPlanId:%v", item.ID)
                continue
            }
            reserveReq := &channel_wedding.ReserveWeddingReq{
                Uid:               weddingOrder.BuyerUid,
                Cid:               item.ReserveInfo.ChannelId,
                StartTime:         int64(item.ReserveInfo.StartTime),
                BrideUid:          item.BrideUid,
                GroomUid:          item.GroomUid,
                WeddingThemeId:    item.ThemeId,
                EndTime:           int64(item.ReserveInfo.EndTime),
                PlanId:            item.ID,
                ThemeType:         item.ThemeType,
                BridesmaidUidList: bmul,
            }
            _, err = m.rpc.ChannelWeddingSvrCli.ReserveWedding(ctx, reserveReq)
            if err != nil && !protocol.IsErrorOf(err, status.ErrChannelWeddingReserveDuplicate) {
                log.ErrorWithCtx(ctx, "ReserveChannel fail to ReserveWedding. weddingPlanId: %d, err:%v", item.ID, err)
                continue
            }
            reserveSuccessIdList = append(reserveSuccessIdList, item.ID)
        }

        if len(reserveSuccessIdList) > 0 {
            log.DebugWithCtx(ctx, "ReserveChannel ReserveWedding. reserveSuccessIdList: %+v", reserveSuccessIdList)
            err := m.st.BatchUpdateWeddingPlanStatus(ctx, reserveSuccessIdList, store.WeddingPlanStatusReserved)
            if err != nil {
                log.ErrorWithCtx(ctx, "ReserveChannel fail to BatchUpdateWeddingPlanStatus. err:%v", err)
            }
        }
    }
}

type NotifyGroomAndBrideData struct {
    Title            string
    UserInfo         NotifyUserInfo
    ChannelId        uint32
    TimeRangeStr     string
    TTTimerDuration  uint32
    TTTimerEventLink string
}

type NotifyUserInfo struct {
    Account    string
    HeadImgMd5 string
    Nickname   string
}

func (m *WeddingPlanMgr) NotifyGroomAndBrideWeddingStarting(_ context.Context) {
    ctx, cancel := comctx.WithTimeout(time.Minute)
    defer cancel()
    st := time.Now()
    defer func() {
        log.Debugf("NotifyGroomAndBrideWeddingStarting cost: %vms", time.Since(st).Milliseconds())
    }()

    // 查询N分钟后的一分钟区间内数据去通知新郎新娘婚礼开始
    startTime := time.Now().Add(5 * time.Second).Truncate(time.Minute)
    aheadNotifyTime := m.bc.GetConfig().ReserveConf.AheadNotifyGroomAndBrideTime
    startTime = startTime.Add(time.Duration(aheadNotifyTime) * time.Minute)

    weddingPlanData, err := m.st.GetWeddingPlanByTimeRangeStatus(ctx, uint32(startTime.Unix()), uint32(startTime.Unix()), 100, []uint32{store.WeddingPlanStatusReserved})
    if err != nil {
        log.ErrorWithCtx(ctx, "NotifyGroomAndBrideWeddingStarting fail to GetWeddingPlanByTimeRangeStatus. err:%v", err)
        return
    }

    log.InfoWithCtx(ctx, "NotifyGroomAndBrideWeddingStarting startTime: %v, weddingPlanData: %d", startTime, len(weddingPlanData))

    if len(weddingPlanData) == 0 {
        return
    }

    for _, item := range weddingPlanData {
        log.InfoWithCtx(ctx, "NotifyGroomAndBrideWeddingStarting weddingPlanData: %+v", item)

        err := m.notifyGroomAndBrideWeddingStarting(ctx, item, true, true, true)
        if err != nil {
            log.ErrorWithCtx(ctx, "NotifyGroomAndBrideWeddingStarting fail to notifyGroomAndBrideWeddingStarting. err:%v", err)
        }
    }
}

func (m *WeddingPlanMgr) ManualNotifyGroomAndBridWeddingStarting(ctx context.Context, plan *store.WeddingPlan) error {
    uidList := []uint32{plan.GroomUid, plan.BrideUid}
    for _, item := range plan.Groomsman {
        uidList = append(uidList, item.Uid)
    }
    for _, item := range plan.Bridesmaid {
        uidList = append(uidList, item.Uid)
    }

    for _, uid := range uidList {
        err := m.notifyWeddingStartingPopup(ctx, plan, uid)
        if err != nil {
            log.ErrorWithCtx(ctx, "ManualNotifyGroomAndBridWeddingStarting fail to notify. planId: %d, notifyUid: %d, err:%v", plan.ID, uid, err)
        }
    }

    m.notifyFriendsWeddingStart(ctx, plan)
    return nil
}

func (m *WeddingPlanMgr) notifyGroomAndBrideWeddingStarting(ctx context.Context, item *store.WeddingPlan, groomPopup, bridePopup, im bool) error {
    popupTmpl := m.bc.GetGroomAndBridePopupXml()
    imTmpl := m.bc.GetGroomAndBrideImXml()

    userprofile, err := m.rpc.BatchGetUserProfile(ctx, []uint32{item.GroomUid, item.BrideUid})
    if err != nil {
        log.ErrorWithCtx(ctx, "NotifyGroomAndBrideWeddingStarting fail to BatchGetUserProfileV2. err:%v", err)
        return err
    }

    // 新郎推送
    title := "宝，我们的婚礼开始啦，等你来"
    data := NotifyGroomAndBrideData{
        Title: title,
        UserInfo: NotifyUserInfo{
            Account:    userprofile[item.BrideUid].GetAccount(),
            HeadImgMd5: userprofile[item.BrideUid].GetHeadImgMd5(),
            Nickname:   userprofile[item.BrideUid].GetNickname(),
        },
        ChannelId: item.ReserveInfo.ChannelId,
        TimeRangeStr: fmt.Sprintf("%s %s-%s", time.Unix(int64(item.ReserveInfo.StartTime), 0).Format("01月02日"),
            time.Unix(int64(item.ReserveInfo.StartTime), 0).Format("15:04"),
            time.Unix(int64(item.ReserveInfo.EndTime), 0).Format("15:04")),
    }
    groomPopupXmlContent, rErr := renderTmpl(ctx, "GroomPopup", popupTmpl, data)
    if rErr != nil {
        log.ErrorWithCtx(ctx, "NotifyGroomAndBrideWeddingStarting fail to renderTmpl. err:%v", rErr)
        return rErr
    }
    opt := &pushPb.CommonTopRichTextDialogNotify{
        Content:       groomPopupXmlContent,
        AnnounceScope: uint32(pushPb.CommBreakingNewsBaseOpt_OUTSIDE_CHANNEL),
        Duration:      5, // 5s
        Scene: "wedding-plan",
    }
    notification := buildNotification(opt, uint32(pushPb.PushMessage_COMMON_TOP_RICH_TEXT_DIALOG_NOTIFY), "婚礼开始弹窗")
    if groomPopup {
        pErr := m.rpc.PushCli.PushToUsers(ctx, []uint32{item.GroomUid}, notification)
        if pErr != nil {
            log.ErrorWithCtx(ctx, "NotifyGroomAndBrideWeddingStarting fail to PushToUsers. err:%v", pErr)
        }
        log.InfoWithCtx(ctx, "NotifyGroomAndBrideWeddingStarting groomPopupXmlContent: %s", groomPopupXmlContent)
    }

    //// 新郎IM
    //groomImXmlContent, rErr := renderTmpl(ctx, "GroomIm", imTmpl, data)
    //if rErr != nil {
    //	log.ErrorWithCtx(ctx, "NotifyGroomAndBrideWeddingStarting fail to renderTmpl. err:%v", rErr)
    //}
    //iErr := m.sendImCommonXmlMsgNoMsgId(ctx, item.BrideUid, item.GroomUid, groomImXmlContent, title)
    //if iErr != nil {
    //	log.ErrorWithCtx(ctx, "NotifyGroomAndBrideWeddingStarting fail to sendImCommonXmlMsgNoMsgId. err:%v", iErr)
    //}

    // 新娘推送
    if bridePopup {
        data.UserInfo.Account = userprofile[item.GroomUid].GetAccount()
        data.UserInfo.HeadImgMd5 = userprofile[item.GroomUid].GetHeadImgMd5()
        data.UserInfo.Nickname = userprofile[item.GroomUid].GetNickname()
        bridePopupXmlContent, rErr := renderTmpl(ctx, "BridePopup", popupTmpl, data)
        if rErr != nil {
            log.ErrorWithCtx(ctx, "NotifyGroomAndBrideWeddingStarting fail to renderTmpl. err:%v", rErr)
            return rErr
        }
        opt.Content = bridePopupXmlContent
        notification = buildNotification(opt, uint32(pushPb.PushMessage_COMMON_TOP_RICH_TEXT_DIALOG_NOTIFY), "婚礼开始弹窗")
        pErr := m.rpc.PushCli.PushToUsers(ctx, []uint32{item.BrideUid}, notification)
        if pErr != nil {
            log.ErrorWithCtx(ctx, "NotifyGroomAndBrideWeddingStarting fail to PushToUsers. err:%v", pErr)
        }
        log.InfoWithCtx(ctx, "NotifyGroomAndBrideWeddingStarting bridePopupXmlContent: %s", bridePopupXmlContent)
    }
    // im
    if im {
        brideImXmlContent, rErr := renderTmpl(ctx, "BrideIm", imTmpl, data)
        if rErr != nil {
            log.ErrorWithCtx(ctx, "NotifyGroomAndBrideWeddingStarting fail to renderTmpl. err:%v", rErr)
        }
        iErr := m.sendImCommonXmlMsgNoMsgId(ctx, item.GroomUid, item.BrideUid, brideImXmlContent, title)
        if iErr != nil {
            log.ErrorWithCtx(ctx, "NotifyGroomAndBrideWeddingStarting fail to sendImCommonXmlMsgNoMsgId. err:%v", iErr)
        }
    }
    return nil
}

func (m *WeddingPlanMgr) NotifyWeddingStartingNow(ctx context.Context) {
    ctx, cancel := comctx.WithTimeout(time.Minute)
    defer cancel()
    st := time.Now().Add(5 * time.Second).Truncate(time.Minute)
    //st := time.Unix(1744961400, 0)
    // 同一时间的婚礼只通知一次
    hasBeenNotify := make(map[uint32]struct{})

    StartingNowWedding, err := m.st.GetWeddingPlanByTimeRangeStatus(ctx, uint32(st.Unix()), uint32(st.Unix()), 100, []uint32{store.WeddingPlanStatusReserved})
    if err != nil {
        log.ErrorWithCtx(ctx, "NotifyWeddingStartingNow fail to GetWeddingPlanByTimeRangeStatus. err:%v", err)
        return
    }
    log.DebugWithCtx(ctx, "NotifyWeddingStartingNow st: %d, handle_num: %d", st.Unix(), len(StartingNowWedding))
    if len(StartingNowWedding) == 0 {
        return
    }

    for _, plan := range StartingNowWedding {
        if plan == nil || plan.ReserveInfo == nil {
            log.ErrorWithCtx(ctx, "NotifyWeddingStartingNow weddingPlan is nil. plan:%+v", plan)
            continue
        }

        // 需要通知新人/伴郎/伴娘
        notifyUid := make([]uint32, 0, 2+len(plan.Groomsman)+len(plan.Bridesmaid))
        notifyUid = append(notifyUid, plan.GroomUid, plan.BrideUid)
        hasBeenNotify[plan.GroomUid] = struct{}{}
        hasBeenNotify[plan.BrideUid] = struct{}{}
        for _, v := range plan.Groomsman {
            if _, ok := hasBeenNotify[v.Uid]; ok {
                continue
            }
            notifyUid = append(notifyUid, v.Uid)
            hasBeenNotify[v.Uid] = struct{}{}
        }
        for _, v := range plan.Bridesmaid {
            if _, ok := hasBeenNotify[v.Uid]; ok {
                continue
            }
            notifyUid = append(notifyUid, v.Uid)
            hasBeenNotify[v.Uid] = struct{}{}
        }

        // 查询被通知人当前在房情况
        userChannelIdResp, err := m.rpc.ChannelOlGoCli.BatchGetUserChannelId(ctx, &channelol_go.BatchGetUserChannelIdReq{
            UidList: notifyUid,
        })
        if err != nil {
            log.ErrorWithCtx(ctx, "NotifyWeddingStartingNow fail to BatchGetUserChannelId. err:%v", err)
            continue
        }
        actualNotifyUid := make([]uint32, 0, len(notifyUid))
        for _, v := range notifyUid {
            if userChannelIdResp.GetResults()[v] != plan.ReserveInfo.ChannelId {
                actualNotifyUid = append(actualNotifyUid, v)
            }
        }
        notifyUid = actualNotifyUid
        if len(notifyUid) == 0 {
            log.DebugWithCtx(ctx, "NotifyWeddingStartingNow no need to notify. planId:%d", plan.ID)
            continue
        }

        // 发送婚礼开始弹窗
        for _, uid := range notifyUid {
            err = m.notifyWeddingStartingPopup(ctx, plan, uid)
            if err != nil {
                log.ErrorWithCtx(ctx, "NotifyWeddingStartingNow fail to notify. planId: %d, notifyUid: %d, err:%v", plan.ID, notifyUid, err)
            }
        }
        log.DebugWithCtx(ctx, "NotifyWeddingStartingNow, planId: %v, groomUid: %d, brideUid: %d", plan.ID, plan.GroomUid, plan.BrideUid)
    }
}

// notifyWeddingStartingPopup 通知新人婚礼开始弹窗, 倒计时结束自动进房
func (m *WeddingPlanMgr) notifyWeddingStartingPopup(ctx context.Context, plan *store.WeddingPlan, uid uint32) error {
    popupTmpl := m.bc.GetWeddingStartedNotifyPopupTpl()
    title := "宝，我们的婚礼开始啦，等你来"

    // 发送人
    sendUid := plan.GroomUid
    if uid == plan.GroomUid {
        sendUid = plan.BrideUid
    }
    if uid != plan.GroomUid && uid != plan.BrideUid && plan.BuyerUid != 0 {
        sendUid = plan.BuyerUid
        title = "婚礼要开始啦快来参加吧~"
    }

    userprofile, err := m.rpc.BatchGetUserProfile(ctx, []uint32{plan.GroomUid, plan.BrideUid})
    if err != nil {
        log.ErrorWithCtx(ctx, "notifyWeddingStartingPopup fail to BatchGetUserProfileV2. err:%v", err)
        return err
    }
    endActionLink := fmt.Sprintf("tt://m.52tt.com/channel?channel_id=%d&channel_enter_source=%d", plan.ReserveInfo.ChannelId, channelPB.ChannelEnterReq_ENUM_CHANNEL_ENTER_WEDDING_START_NOTIFY_IM)
    data := NotifyGroomAndBrideData{
        Title: title,
        UserInfo: NotifyUserInfo{
            Account:    userprofile[sendUid].GetAccount(),
            HeadImgMd5: userprofile[sendUid].GetHeadImgMd5(),
            Nickname:   userprofile[sendUid].GetNickname(),
        },
        ChannelId: plan.ReserveInfo.ChannelId,
        TimeRangeStr: fmt.Sprintf("%s-%s", time.Unix(int64(plan.ReserveInfo.StartTime), 0).Format("15:04"),
            time.Unix(int64(plan.ReserveInfo.EndTime), 0).Format("15:04")),
        TTTimerDuration:  5,
        TTTimerEventLink: endActionLink,
    }
    groomPopupXmlContent, rErr := renderTmpl(ctx, "weddingStartingPopupNow", popupTmpl, data)
    if rErr != nil {
        log.ErrorWithCtx(ctx, "notifyWeddingStartingPopup fail to renderTmpl. err:%v", rErr)
        return rErr
    }
    opt := &pushPb.CommonTopRichTextDialogNotify{
        Content:         groomPopupXmlContent,
        AnnounceScope:   uint32(pushPb.CommBreakingNewsBaseOpt_OUTSIDE_CHANNEL+pushPb.CommBreakingNewsBaseOpt_INSIDE_CHANNEL),
        Duration:        10, // 5s
        Scene: "wedding-plan",
    }
    notification := buildNotification(opt, uint32(pushPb.PushMessage_COMMON_TOP_RICH_TEXT_DIALOG_NOTIFY), "婚礼开始弹窗")
    pErr := m.rpc.PushCli.PushToUsers(ctx, []uint32{uid}, notification)
    if pErr != nil {
        log.ErrorWithCtx(ctx, "notifyWeddingStartingPopup fail to PushToUsers. err:%v", pErr)
    }
    log.InfoWithCtx(ctx, "notifyWeddingStartingPopup, toUid: %d, groomPopupXmlContent: %s, opt: %+v", uid, groomPopupXmlContent, opt)
    return nil
}

func renderTmpl(ctx context.Context, name, tpl string, data interface{}) (string, error) {
    var rsBuf bytes.Buffer
    tplI := template.Must(template.New(name).Parse(tpl))
    err := tplI.Execute(&rsBuf, data)
    if err != nil {
        log.ErrorWithCtx(ctx, "RenderCouponInfo", "err", err)
        return "", err
    }

    return rsBuf.String(), nil
}

func (m *WeddingPlanMgr) SetInviteCancel(_ context.Context) {
    ctx, cancel := comctx.WithTimeout(time.Minute)
    defer cancel()
    st := time.Now()
    defer func() {
        log.Debugf("SetInviteCancel cost: %vms", time.Since(st).Milliseconds())
    }()

    // 查询可预约时间内已取消的数据
    startTime := time.Now().Add(-time.Minute) // 前向搜索1min
    endTime := startTime.Add(time.Duration(m.bc.GetConfig().ReserveConf.MaxReservableMin) * time.Minute)

    weddingPlanData, err := m.st.GetWeddingPlanByTimeRangeStatus(ctx, uint32(startTime.Unix()), uint32(endTime.Unix()), 100, []uint32{store.WeddingPlanStatusCancel})
    if err != nil {
        log.ErrorWithCtx(ctx, "SetInviteCancel fail to GetWeddingPlanByTimeRangeStatus. err:%v", err)
        return
    }

    if len(weddingPlanData) == 0 {
        return
    }

    // 更新为已取消状态
    planIdList := make([]uint32, 0, len(weddingPlanData))
    for _, item := range weddingPlanData {
        planIdList = append(planIdList, item.ID)
    }
    err = m.st.BatchUpdateWeddingPlanStatus(ctx, planIdList, store.WeddingPlanStatusCanceled)
    if err != nil {
        log.ErrorWithCtx(ctx, "SetInviteCancel BatchUpdateWeddingPlanStatus, planIdList: %+v, err: %v", planIdList, err)
        return
    }

    for _, item := range weddingPlanData {
        // 更新邀请状态, im消息
        err := m.st.UpdateHolePlanInviteStatus(ctx, item.ID, store.InviteStatusCanceled)
        if err != nil {
            log.ErrorWithCtx(ctx, "SetInviteCancel fail to UpdateHolePlanInviteStatus. err:%v", err)
        }
        log.DebugWithCtx(ctx, "SetInviteCancel UpdateHolePlanInviteStatus. weddingPlanId: %d", item.ID)
        inviteList, err := m.st.GetWeddingGuestInviteByWeddingPlanId(ctx, item.ID)
        if err != nil {
            log.ErrorWithCtx(ctx, "SetInviteCancel fail to GetWeddingGuestInviteByWeddingPlanId. err:%v", err)
            continue
        }
        for _, invite := range inviteList {
            ttLink := fmt.Sprintf(m.bc.GetConfig().InviteIMConfig.InviteTTLink, invite.InviteId, invite.GuestType)
            if invite.GuestType == store.GuestTypeFriend { // 亲友团不能点
                ttLink = ""
            }
            xmlContent := fmt.Sprintf(rpc.IMXMLImgTemplate, m.bc.GetConfig().InviteIMConfig.FailureInviteImg, ttLink)
            log.DebugWithCtx(ctx, "SetInviteCancel xmlContent: %s, inviteUid: %d, uid: %d", xmlContent, invite.InviteUid, invite.Uid)
            err = m.rpc.SendIMCommonXmlMsg(ctx, invite.InviteUid, invite.Uid, invite.FromServerMsgId, invite.TargetServerMsgId, xmlContent, "[邀请已失效]")
            if err != nil {
                log.ErrorWithCtx(ctx, "SetInviteCancel fail to SendIMCommonXmlMsg. err:%v", err)
                continue
            }
        }

        // 通知公会房管
        if item.ThemeType == store.ThemeTypePay && item.ReserveInfo != nil {
            sendUidList, _, err := m.getChannelNotifyInfo(ctx, item.ReserveInfo.ChannelId)
            if err != nil {
                log.ErrorWithCtx(ctx, "reserveNotify fail to getChannelNotifyInfo. err:%v", err)
                continue
            }

            for _, sendUid := range sendUidList {
                fromText := fmt.Sprintf("对方取消了%s在贵厅的婚礼预订~", time.Unix(int64(item.ReserveInfo.StartTime), 0).Format("01月02日15:04:05"))
                toText := fmt.Sprintf("你取消了%s在此婚礼房的婚礼预订~", time.Unix(int64(item.ReserveInfo.StartTime), 0).Format("01月02日15:04:05"))
                err = m.SendSysTextMutual(ctx, item.BuyerUid, sendUid, fromText, toText)
                if err != nil {
                    log.ErrorWithCtx(ctx, "SetInviteCancel fail to SendSysTextMutual. err:%v", err)
                }
            }
        }
    }
}

func (m *WeddingPlanMgr) OnlineWeddingStartNotify(ctx context.Context, uid uint32) {
    log.DebugWithCtx(ctx, "OnlineWeddingStartNotify uid: %d", uid)

    // 用户在房不用处理
    userChannelIdResp, cErr := m.rpc.ChannelOlGoCli.BatchGetUserChannelId(ctx, &channelol_go.BatchGetUserChannelIdReq{
        UidList: []uint32{uid},
    })
    if cErr != nil {
        log.ErrorWithCtx(ctx, "OnlineWeddingStartNotify fail to GetUserChannelId. uid: %d, err:%v", uid, cErr)
        return
    }
    if userChannelIdResp.GetResults()[uid] != 0 {
        log.DebugWithCtx(ctx, "OnlineWeddingStartNotify, uid: %d, userChannelIdResp: %d", uid, userChannelIdResp)
        return
    }

    weddingPlan, err := m.st.GetGoingPlan(ctx, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "OnlineWeddingStartNotify fail to GetGoingPlan. uid: %d, err:%v", uid, err)
        return
    }
    if weddingPlan == nil || weddingPlan.ReserveInfo == nil {
        return
    }

    groomPopup := true
    bridePopup := false
    if weddingPlan.BrideUid == uid {
        groomPopup = false
        bridePopup = true
    }

    if weddingPlan != nil && weddingPlan.ReserveInfo != nil && weddingPlan.ReserveInfo.StartTime <= uint32(time.Now().Unix()) {
        err := m.notifyGroomAndBrideWeddingStarting(ctx, weddingPlan, groomPopup, bridePopup, false)
        if err != nil {
            log.ErrorWithCtx(ctx, "OnlineWeddingStartNotify fail to notifyGroomAndBrideWeddingStarting. uid: %d, err:%v", uid, err)
        }
    }
}

func (m *WeddingPlanMgr) SetWeddingPlanFinish(_ context.Context) {
    ctx, cancel := comctx.WithTimeout(5 * time.Second)
    defer cancel()
    startTime := time.Now().Add(-time.Minute) // 前向搜索1min
    endTime := time.Now()

    for i := 0; i < 100; i++ {
        weddingPlanData, err := m.st.GetWeddingPlanByEndTimeTimeRangeAndStatus(ctx,
            uint32(startTime.Unix()), uint32(endTime.Unix()), 100, []uint32{store.WeddingPlanStatusReserved})
        if err != nil {
            log.ErrorWithCtx(ctx, "SetWeddingPlanFinish fail to GetWeddingPlanByTimeRangeStatus. err:%v", err)
            return
        }

        if len(weddingPlanData) == 0 {
            return
        }

        weddingPlanIdList := make([]uint32, 0, len(weddingPlanData))
        for _, item := range weddingPlanData {
            weddingPlanIdList = append(weddingPlanIdList, item.ID)
        }

        err = m.st.BatchUpdateWeddingPlanStatus(ctx, weddingPlanIdList, store.WeddingPlanStatusFinish)
        if err != nil {
            log.ErrorWithCtx(ctx, "SetWeddingPlanFinish fail to BatchUpdateWeddingPlanStatus. err:%v", err)
        }
        log.DebugWithCtx(ctx, "SetWeddingPlanFinish weddingPlanIdList: %v", weddingPlanIdList)

        // 把婚礼邀请状态改为已完成
        for _, item := range weddingPlanData {
            err := m.st.SetWeddingGuestInvitePlanFinish(ctx, item.ID)
            if err != nil {
                log.ErrorWithCtx(ctx, "SetWeddingPlanFinish fail to SetWeddingGuestInvitePlanFinish. planId: %d, err:%v", item.ID, err)
            }
        }
    }
}

func (m *WeddingPlanMgr) GetWeddingBigScreen(ctx context.Context, weddingPlanId uint32) ([]*pb.BigScreenItem, error) {
    plan, err := m.st.GetWeddingPlan(ctx, weddingPlanId)
    if err != nil {
        return nil, err
    }

    return fillPb4WeddingPlanBigScreenList(plan.BigScreenList), nil
}

func (m *WeddingPlanMgr) SaveWeddingBigScreen(ctx context.Context, in *pb.SaveWeddingBigScreenRequest) error {
    log.InfoWithCtx(ctx, "SaveWeddingBigScreen in: %+v", in)

    if in.IsDel {
        err := m.st.DelWeddingBigScreen(ctx, in.WeddingPlanId, in.ImgUrl)
        if err != nil {
            log.ErrorWithCtx(ctx, "SaveWeddingBigScreen DelWeddingBigScreen err: %v", err)
            return err
        }
    } else {
        err := m.st.AddWeddingBigScreen(ctx, in.WeddingPlanId, &store.BigScreenItem{
            ImgUrl:       in.ImgUrl,
            ReviewStatus: uint32(pb.ReviewStatus_REVIEW_STATUS_REVIEWING),
            UpdateAt:     time.Now(),
            UploadByUid:  in.Uid,
        })
        if err != nil {
            log.ErrorWithCtx(ctx, "SaveWeddingBigScreen AddWeddingBigScreen err: %v", err)
            return err
        }
    }

    goroutineex.GoroutineWithTimeoutCtx(ctx, 5*time.Second, func(ctx context.Context) {
        m.notifyWeddingBigScreenChange(ctx, in.WeddingPlanId)
    })

    return nil
}

func (m *WeddingPlanMgr) UpdateWeddingBigScreenReviewStatus(ctx context.Context, in *pb.UpdateWeddingBigScreenReviewStatusRequest) error {
    log.InfoWithCtx(ctx, "UpdateWeddingBigScreenReviewStatus in: %+v", in)

    if in.ReviewStatus == pb.ReviewStatus_REVIEW_STATUS_PASS {
        err := m.st.UpdateWeddingBigScreenReviewStatus(ctx, in.WeddingPlanId, uint32(in.ReviewStatus), in.ImgUrl)
        if err != nil {
            log.ErrorWithCtx(ctx, "UpdateWeddingBigScreenReviewStatus err: %v", err)
            return err
        }
    } else {
        err := m.st.DelWeddingBigScreen(ctx, in.WeddingPlanId, in.ImgUrl)
        if err != nil {
            log.ErrorWithCtx(ctx, "UpdateWeddingBigScreenReviewStatus DelWeddingBigScreen err: %v", err)
            return err
        }
    }

    goroutineex.GoroutineWithTimeoutCtx(ctx, 5*time.Second, func(ctx context.Context) {
        m.notifyWeddingBigScreenChange(ctx, in.WeddingPlanId)
    })

    return nil
}

func (m *WeddingPlanMgr) notifyWeddingBigScreenChange(ctx context.Context, weddingPlanId uint32) {
    plan, err := m.st.GetWeddingPlan(ctx, weddingPlanId)
    if err != nil {
        log.ErrorWithCtx(ctx, "notifyWeddingBigScreenChange GetWeddingPlan err: %v", err)
        return
    }
    uidList := []uint32{plan.BrideUid, plan.GroomUid}
    imgList := transform.Map(fillPb4WeddingPlanBigScreenList(plan.BigScreenList), func(item *pb.BigScreenItem) *channel_wedding_logic.BigScreenImage {
        return &channel_wedding_logic.BigScreenImage{
            ImgUrl:        item.ImgUrl,
            IsUnderReview: item.ReviewStatus == pb.ReviewStatus_REVIEW_STATUS_REVIEWING,
            UploadByUid:   item.UploadByUid,
        }
    })

    opt := &channel_wedding_logic.WeddingBigScreenChangeNotify{
        WeddingPlanId: weddingPlanId,
        ImgList:       imgList,
        ServerTs:      time.Now().UnixMilli(),
    }
    msg, _ := proto.Marshal(opt)

    pushMessage := &pushPb.PushMessage{
        Cmd:     uint32(pushPb.PushMessage_WEDDING_BIG_SCREEN_CHANGE_NOTIFY),
        Content: msg,
    }
    pushMessageBytes, _ := proto.Marshal(pushMessage)

    notification := &push_notification.CompositiveNotification{
        Sequence:           uint32(time.Now().Unix()),
        TerminalTypeList:   []uint32{protocol.MobileAndroidTT, protocol.MobileIPhoneTT},
        TerminalTypePolicy: push.DefaultPolicy,
        AppId:              0,
        ProxyNotification: &push_notification.ProxyNotification{
            Type:    uint32(push_notification.ProxyNotification_PUSH),
            Payload: pushMessageBytes,
        },
    }

    err = m.rpc.PushCli.PushToUsers(ctx, uidList, notification)
    if err != nil {
        log.ErrorWithCtx(ctx, "notifyWeddingBigScreenChange PushToUsers err: %v", err)
        return
    }

    log.InfoWithCtx(ctx, "notifyWeddingBigScreenChange success, weddingPlanId: %d, uidList: %v, msg: %+v", weddingPlanId, uidList, opt)
}

func (m *WeddingPlanMgr) BatchGetWeddingRole(ctx context.Context, planId uint32, uidList []uint32) (map[uint32]uint32, error) {
    if len(uidList) == 0 || planId == 0 {
        return map[uint32]uint32{}, nil
    }
    planInfo, err := m.st.GetWeddingPlanById(ctx, planId)
    if err != nil {
        log.ErrorWithCtx(ctx, "BatchGetWeddingRole fail to GetWeddingPlanById. planId: %d, err:%v", planId, err)
        return nil, err
    }
    if planInfo == nil {
        return map[uint32]uint32{}, nil
    }

    groomsManMap := map[uint32]struct{}{}
    for _, groomsman := range planInfo.Groomsman {
        groomsManMap[groomsman.Uid] = struct{}{}
    }
    bridesMaidMap := map[uint32]struct{}{}
    for _, bridesmaid := range planInfo.Bridesmaid {
        bridesMaidMap[bridesmaid.Uid] = struct{}{}
    }

    out := make(map[uint32]uint32, len(uidList))

    for _, uid := range uidList {
        if _, ok := groomsManMap[uid]; ok {
            out[uid] = uint32(channel_wedding_logic.WeddingGuestType_WEDDING_GUEST_TYPE_BRIDES)
            continue
        }
        if _, ok := bridesMaidMap[uid]; ok {
            out[uid] = uint32(channel_wedding_logic.WeddingGuestType_WEDDING_GUEST_TYPE_BRIDESMAID)
            continue
        }
        if planInfo.BrideUid == uid {
            out[uid] = uint32(channel_wedding_logic.WeddingGuestType_WEDDING_GUEST_TYPE_BRIDE)
            continue
        }
        if planInfo.GroomUid == uid {
            out[uid] = uint32(channel_wedding_logic.WeddingGuestType_WEDDING_GUEST_TYPE_GROOM)
        }
    }
    return out, nil

}

func (m *WeddingPlanMgr) GetMyWeddingRole(ctx context.Context, req *pb.GetMyWeddingRoleRequest) (*pb.GetMyWeddingRoleResponse, error) {
    resp := &pb.GetMyWeddingRoleResponse{
        InChangeTime: true,
    }

    weddingPlan, err := m.st.GetGoingPlan(ctx, req.GetUid())
    if err != nil {
        log.ErrorWithCtx(ctx, "GetMyWeddingRole fail to GetGoingPlan. req: %+v, err:%v", req, err)
        return resp, nil
    }
    if weddingPlan == nil { // 不是新人
        maxReservableMin := m.bc.GetConfig().ReserveConf.MaxReservableMin
        if m.bc.GetConfig().ReserveConf.FreeWeddingMaxReservableMin > maxReservableMin {
            maxReservableMin = m.bc.GetConfig().ReserveConf.FreeWeddingMaxReservableMin
        }

        // 查看最旧还有效的只有邀请
        validInviteList, err := m.st.GetGoingWeddingBridesmaidInvite(ctx, req.GetUid(), uint32(time.Now().Add(-time.Duration(maxReservableMin)*time.Minute).Unix()))
        if err != nil {
            log.ErrorWithCtx(ctx, "GetMyWeddingRole fail to GetGoingWeddingBridesmaidInvite. req: %+v, err:%v", req, err)
            return resp, nil
        }
        if len(validInviteList) > 0 {
            oldestInvite := validInviteList[0]
            weddingPlan, err = m.GetWeddingPlanByIdNoCheck(ctx, oldestInvite.WeddingPlanId, true)
            if err != nil {
                log.ErrorWithCtx(ctx, "GetMyWeddingRole fail to GetWeddingPlanByIdNoCheck. req: %+v, err:%v", req, err)
                return resp, err
            }

        }
    }

    if weddingPlan != nil {
        resp.WeddingRole = uint32(channel_wedding_logic.WeddingGuestType_WEDDING_GUEST_TYPE_GROOM)
        if weddingPlan.BrideUid == req.GetUid() {
            resp.WeddingRole = uint32(channel_wedding_logic.WeddingGuestType_WEDDING_GUEST_TYPE_BRIDE)
        }
        for _, item := range weddingPlan.Groomsman {
            if item.Uid == req.GetUid() {
                resp.WeddingRole = uint32(channel_wedding_logic.WeddingGuestType_WEDDING_GUEST_TYPE_BRIDES)
                break
            }
        }

        for _, item := range weddingPlan.Bridesmaid {
            if item.Uid == req.GetUid() {
                resp.WeddingRole = uint32(channel_wedding_logic.WeddingGuestType_WEDDING_GUEST_TYPE_BRIDESMAID)
                break
            }

        }

        if weddingPlan.ReserveInfo != nil {
            resp.InChangeTime = time.Now().Add(time.Duration(m.bc.GetConfig().ReserveConf.LimitChangeAheadTime)*time.Hour).Unix() < int64(weddingPlan.ReserveInfo.StartTime)
        }
        return resp, nil
    }

    return resp, nil
}
