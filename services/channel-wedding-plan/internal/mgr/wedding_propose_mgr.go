package mgr

import (
    "context"
    "fmt"
    "golang.52tt.com/pkg/log"
    "golang.52tt.com/pkg/protocol"
    "golang.52tt.com/pkg/protocol/grpc"
    pbLogic "golang.52tt.com/protocol/app/channel_wedding_logic"
    imPB "golang.52tt.com/protocol/app/im"
    "golang.52tt.com/protocol/common/status"
    accountgo "golang.52tt.com/protocol/services/account-go"
    pb "golang.52tt.com/protocol/services/channel-wedding-plan"
    "golang.52tt.com/services/channel-wedding-plan/internal/store"
    "google.golang.org/grpc/codes"
    "os"
    "time"

    "golang.52tt.com/services/tt-rev/common/goroutineex"
)

const (
    IMLetterSuitor  = "https://obs-cdn.52tt.com/tt/fe-moss/tt-server/20250317104850_72619830.png"
    IMLetterAccept  = "https://obs-cdn.52tt.com/tt/fe-moss/tt-server/20250317104850_72768586.png"
    IMLetterRefuse  = "https://obs-cdn.52tt.com/tt/fe-moss/tt-server/20250317104850_72580535.png"
    IMLetterTimeout = "https://obs-cdn.52tt.com/tt/fe-moss/tt-server/20250317104850_72739231.png"
)

func (m *WeddingPlanMgr) SendPropose(ctx context.Context, request *pb.SendProposeRequest) (*pb.SendProposeResponse, error) {
    out := &pb.SendProposeResponse{}
    uid := request.GetFromUid()
    if uid == request.GetTargetUid() {
        log.ErrorWithCtx(ctx, "SendPropose %d uid == request.GetTargetUid()", uid)
        return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "求婚失败，不能向自己求婚")
    }

    // 检查是否已经有结婚对象
    marriageInfo, err := m.st.BatchGetMarriageRelationByUidList(ctx, []uint32{request.GetFromUid(), request.GetTargetUid()})
    if err != nil {
        log.ErrorWithCtx(ctx, "SendPropose req:%v, Failed to get from relation: %v", request, err)
        return out, err
    }

    if marriageInfo[request.GetTargetUid()] > 0 {
        log.ErrorWithCtx(ctx, "SendPropose req:%v, already have marriage marriageInfo: %v", request, marriageInfo)
        return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "求婚失败，对方已有结婚对象哦~")
    }

    if marriageInfo[request.GetFromUid()] > 0 {
        log.ErrorWithCtx(ctx, "SendPropose req:%v, already have marriage marriageInfo: %v", request, marriageInfo)
        return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "求婚失败，你已有结婚对象哦~")
    }

    propose, err := m.st.GetSendProposeInfoByUid(ctx, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "SendPropose req:%v, Failed to get propose info: %v", request, err)
        return out, err
    }

    if propose != nil {
        log.ErrorWithCtx(ctx, "SendPropose req:%v, already have propose propose: %v", request, propose)
        return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "求婚失败，你已有结婚对象哦~")
    }

    usersMap, err := m.rpc.AccountCli.GetUsersMap(ctx, []uint32{request.GetTargetUid(), uid})
    if err != nil {
        log.ErrorWithCtx(ctx, "SendPropose req:%v, Failed to get user info: %v", request, err)
        return out, err
    }

    targetUser := usersMap[request.GetTargetUid()]
    if targetUser.GetSex() == usersMap[uid].GetSex() {
        log.ErrorWithCtx(ctx, "SendPropose req:%v, same sex: %v", request, propose)
        return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "不支持向同性发起求婚哦~")
    }

    fromSvrMsgID, err := m.rpc.GenerateSequence(ctx, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "SendPropose req:%v, Failed to generate svr msg id: %v", request, err)
        return out, err
    }

    targetSvrMsgID, err := m.rpc.GenerateSequence(ctx, request.GetTargetUid())
    if err != nil {
        log.ErrorWithCtx(ctx, "SendPropose req:%v, Failed to generate svr msg id: %v", request, err)
        return out, err
    }

    // 测试环境
    addSecond := m.bc.GetProposeExpireDay() * 24 * 60 * 60
    if os.Getenv("MY_CLUSTER") == "testing" {
        addSecond = m.bc.GetProposeExpireDay() * 60
    }

    now := time.Now().Local()
    proposeInfo := &store.ProposeInfo{
        FromUid:           uid,
        TargetUid:         request.GetTargetUid(),
        Tips:              request.GetTips(),
        Status:            uint32(pbLogic.ProposeStatus_PROPOSE_STATUS_INVITED),
        FromServerMsgId:   fromSvrMsgID,
        TargetServerMsgId: targetSvrMsgID,
        CreateTime:        now,
        UpdateTime:        now,
        EndTime:           now.Add(time.Duration(addSecond) * time.Second),
        ExpiredDays:       m.bc.GetProposeExpireDay(),
        TargetSex:         targetUser.GetSex(),
    }

    proposeId, err := m.st.AddProposeInfo(ctx, proposeInfo)
    if err != nil {
        log.ErrorWithCtx(ctx, "SendPropose req:%v Failed to add propose info: %v", request, err)
        return out, err
    }
    proposeInfo.ID = proposeId

    //发送IM && 推送
    err = m.rpc.SendProposeMsg(ctx, proposeInfo, proposeInfo.FromUid, proposeInfo.TargetUid, proposeInfo.FromServerMsgId, proposeInfo.TargetServerMsgId, IMLetterSuitor, "[我们结婚吧...]")
    log.DebugWithCtx(ctx, "SendPropose uid:%v, request:%v", uid, request)
    out.ProposeId = proposeId.Hex()
    return out, err
}

func (m *WeddingPlanMgr) HandlePropose(ctx context.Context, request *pb.HandleProposeRequest) (*pb.HandleProposeResponse, error) {
    out := &pb.HandleProposeResponse{}
    proposeInfo, err := m.st.GetProposeInfoByID(ctx, request.GetProposeId())
    if err != nil {
        log.ErrorWithCtx(ctx, "HandlePropose req:%+v, Failed to get propose info: %v", request, err)
        return out, err
    }

    if proposeInfo.Status != uint32(pbLogic.ProposeStatus_PROPOSE_STATUS_INVITED) {
        log.ErrorWithCtx(ctx, "HandlePropose req:%+v, propose status:%v", request, proposeInfo.Status)
        return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "失败，当前求婚已失效")
    }

    if request.GetHandleUid() != proposeInfo.TargetUid {
        log.ErrorWithCtx(ctx, "HandlePropose req:%+v, serviceInfo.UserID:%d != proposeInfo.TargetUid:%d", request, request.GetHandleUid(), proposeInfo.TargetUid)
        return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "操作失败")
    }

    //锁住求婚双方 避免重复操作
    err = m.cache.TryLock(ctx, fmt.Sprintf("lock_propose_%d", proposeInfo.FromUid), proposeInfo.ID.Hex(), time.Second*2)
    if err != nil {
        log.ErrorWithCtx(ctx, "HandlePropose req:%+v, Failed to lock propose: %v", request, err)
        return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "操作失败")
    }

    err = m.cache.TryLock(ctx, fmt.Sprintf("lock_propose_%d", proposeInfo.TargetUid), proposeInfo.ID.Hex(), time.Second*2)
    if err != nil {
        log.ErrorWithCtx(ctx, "HandlePropose req:%+v, Failed to lock propose: %v", request, err)
        return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "操作失败")
    }

    var serverError error
    imUrl := IMLetterRefuse
    proposeInfo.Status = uint32(pbLogic.ProposeStatus_PROPOSE_STATUS_FAILED)
    outContent := "[我们结婚吧]很抱歉..."
    if request.GetIsAccept() {
        serverError = m.checkAcceptError(ctx, proposeInfo)
        if serverError == nil {
            // 结婚成功
            imUrl = IMLetterAccept
            proposeInfo.Status = uint32(pbLogic.ProposeStatus_PROPOSE_STATUS_SUCCESS)
            outContent = "[求婚成功]"
            err = m.st.InsertMarriageRelation(ctx, proposeInfo.FromUid, proposeInfo.TargetUid)
            if err != nil {
                log.ErrorWithCtx(ctx, "HandlePropose req:%+v, Failed to insert marriage relation: %v", request, err)
            }

            // 解除关系隐藏开关
            goroutineex.GoroutineWithTimeoutCtx(ctx, 5*time.Second, func(ctx context.Context) {
                _ = m.SetRelationHideSwitch(ctx, proposeInfo.FromUid, false)
                _ = m.SetRelationHideSwitch(ctx, proposeInfo.TargetUid, false)
            })
        } else {
            imUrl = IMLetterTimeout
            proposeInfo.Status = uint32(pbLogic.ProposeStatus_PROPOSE_STATUS_TIMEOUT)
        }
    }

    err = m.st.UpdateProposeInfoStatusByID(ctx, proposeInfo.ID.Hex(), proposeInfo.Status)
    if err != nil {
        log.ErrorWithCtx(ctx, "HandlePropose req:%+v, Failed to update propose info: %v", request, err)
        return out, err
    }

    goroutineex.GoroutineWithTimeoutCtx(ctx, 5*time.Second, func(ctx context.Context) {
        //发送IM && 推送
        msgFromUid := proposeInfo.FromUid
        msgToUid := proposeInfo.TargetUid
        msgFromSeq := proposeInfo.FromServerMsgId
        msgToSeq := proposeInfo.TargetServerMsgId

        // 发送人和接收人  互换
        if proposeInfo.Status != uint32(pbLogic.ProposeStatus_PROPOSE_STATUS_TIMEOUT) {
            msgToSeq, err = m.rpc.GenerateSequence(ctx, proposeInfo.FromUid)
            msgToUid = proposeInfo.FromUid

            msgFromUid = request.GetHandleUid()
            msgFromSeq, err = m.rpc.GenerateSequence(ctx, request.GetHandleUid())
        }

        err = m.rpc.SendProposeMsg(ctx, proposeInfo, msgFromUid, msgToUid, msgFromSeq, msgToSeq, imUrl, outContent)
        if err != nil {
            log.ErrorWithCtx(ctx, "HandlePropose req:%+v, Failed to send propose msg: %v", request, err)
        }

        // 结婚成功后，清除求婚列表
        if proposeInfo.Status == uint32(pbLogic.ProposeStatus_PROPOSE_STATUS_SUCCESS) {
            err = m.DisableUserProposeList(ctx, proposeInfo.TargetUid)
            if err != nil {
                log.ErrorWithCtx(ctx, "HandlePropose req:%+v, Failed to disable propose list: %v", request, err)
            }
        }
    })

    log.DebugWithCtx(ctx, "HandlePropose proposeInfo:%v, request:%v", proposeInfo, request)
    return out, serverError
}

func (m *WeddingPlanMgr) checkAcceptError(ctx context.Context, proposeInfo *store.ProposeInfo) error {
    var err error
    userInfoMap, err := m.rpc.AccountCli.GetUsersMap(ctx, []uint32{proposeInfo.FromUid, proposeInfo.TargetUid})
    if err != nil {
        log.ErrorWithCtx(ctx, "checkAcceptError req:%+v, Failed to get user info: %v", proposeInfo, err)
        return err
    }
    if userInfoMap == nil || len(userInfoMap) != 2 {
        log.ErrorWithCtx(ctx, "checkAcceptError req:%+v, Failed to get user info: %v", proposeInfo, err)
        return protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "用户信息获取失败")
    }
    if userInfoMap[proposeInfo.FromUid].GetSex() == userInfoMap[proposeInfo.TargetUid].GetSex() {
        err := m.st.UpdateProposeInfoStatusByID(ctx, proposeInfo.ID.Hex(), uint32(pbLogic.ProposeStatus_PROPOSE_STATUS_CANCELED))
        if err != nil {
            log.ErrorWithCtx(ctx, "checkAcceptError req:%+v, Failed to update propose info: %v", proposeInfo, err)
            return err
        }
        switch proposeInfo.TargetSex {
        case int32(accountgo.USER_SEX_USER_SEX_MALE):
            return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "性别不符,不支持成为ta的新郎")
        case int32(accountgo.USER_SEX_USER_SEX_FEMALE):
            return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "性别不符,不支持成为ta的新娘")
        default:
            return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "用户信息异常")
        }
    }

    relationMap, err := m.st.BatchGetMarriageRelationByUidList(ctx, []uint32{proposeInfo.FromUid, proposeInfo.TargetUid})
    if err != nil {
        log.ErrorWithCtx(ctx, "checkAcceptError req:%+v, Failed to get from relation: %v", proposeInfo, err)
        return err
    }

    if relationMap[proposeInfo.FromUid] != 0 {
        log.ErrorWithCtx(ctx, "checkAcceptError req:%+v, FromUid:%v", proposeInfo, proposeInfo.FromUid)
        return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "结婚失败，对方当前已有结婚对象")
    }

    if relationMap[proposeInfo.TargetUid] != 0 {
        log.ErrorWithCtx(ctx, "checkAcceptError req:%+v, TargetUid:%v", proposeInfo, proposeInfo.TargetUid)
        return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "结婚失败，当前你已有结婚对象")
    }
    return nil

}

func (m *WeddingPlanMgr) HandleTimeOutPropose() {
    ctx, cancel := context.WithTimeout(context.Background(), 10*time.Minute)
    defer cancel()

    list, err := m.st.GetTimoutProposeList(ctx)
    if err != nil {
        log.ErrorWithCtx(ctx, "HandleTimeOutPropose Failed to get timeout propose list: %v", err)
        return
    }

    for _, proposeInfo := range list {
        if proposeInfo.Status != uint32(pbLogic.ProposeStatus_PROPOSE_STATUS_INVITED) {
            continue
        }

        log.InfoWithCtx(ctx, "HandleTimeOutPropose proposeInfo:%+v", proposeInfo)
        proposeInfo.Status = uint32(pbLogic.ProposeStatus_PROPOSE_STATUS_TIMEOUT)
        err = m.st.UpdateProposeInfoStatusByID(ctx, proposeInfo.ID.Hex(), proposeInfo.Status)
        if err != nil {
            log.ErrorWithCtx(ctx, "HandleTimeOutPropose Failed to update propose info: %v", err)
            continue
        }
        //发送IM && 推送
        err = m.rpc.SendProposeMsg(ctx, proposeInfo, proposeInfo.FromUid, proposeInfo.TargetUid, proposeInfo.FromServerMsgId, proposeInfo.TargetServerMsgId, IMLetterTimeout, "[我们结婚吧]很抱歉...")
        if err != nil {
            log.ErrorWithCtx(ctx, "HandleTimeOutPropose Failed to send propose msg: %v", err)
        }
    }
}

func (m *WeddingPlanMgr) HandleDivorceTimeout(ctx context.Context) {
    timeOut := int64(m.bc.GetConfig().AutoDivorceDay) * 3600 * 24
    if m.bc.GetConfig().IsAutoDivorceTestMode {
        timeOut = 60 * 2 // 测试模式固定2分钟
    }
    uidList, err := m.cache.GetDivideTimeout(ctx, timeOut)
    if err != nil {
        log.ErrorWithCtx(ctx, "HandleDivorceTimeout Failed to get divide timeout: %v", err)
        return
    }
    if len(uidList) == 0 {
        log.DebugWithCtx(ctx, "HandleDivorceTimeout no divide timeout")
        return
    }
    for _, uid := range uidList {
        relation, err := m.GetUserMarriageInfo(ctx, uid)
        if err != nil {
            log.ErrorWithCtx(ctx, "HandleDivorceTimeout Failed to get marriage relation: %v", err)
            continue
        }

        if relation == nil || relation.GetUid() == 0 || relation.GetPartnerUid() == 0 {
            _, err := m.cache.RemoveDivideTimeout(ctx, uid)
            if err != nil {
                log.ErrorWithCtx(ctx, "HandleDivorceTimeout Failed to remove divide timeout: %v, uid:%d", err, uid)
            }
            log.ErrorWithCtx(ctx, "HandleDivorceTimeout relation is nil, relation:%+v, uid:%d", relation, uid)
            continue
        }

        err = m.Divorce(ctx, relation.GetUid(), relation.GetPartnerUid(), true)
        if err != nil {
            log.ErrorWithCtx(ctx, "HandleDivorceTimeout Failed to divorce: %v, relation:%+v, uid:%d", err, relation, uid)
            continue
        }
        log.InfoWithCtx(ctx, "HandleDivorceTimeout success, relation:%+v, uid:%d", relation, uid)
    }
    log.InfoWithCtx(ctx, "HandleDivorceTimeout success, uidList:%v", uidList)
}

func (m *WeddingPlanMgr) Divorce(ctx context.Context, uid, targetUid uint32, needMsg bool) error {
    info, err := m.GetMyWeddingInfo(ctx, &pb.GetMyWeddingInfoRequest{Uid: uid})
    if err != nil {
        log.ErrorWithCtx(ctx, "Divorce fail to GetMyWeddingInfo. uid:%d, err:%v", uid, err)
        return err
    }

    c := grpc.WithServiceInfo(ctx, &grpc.ServiceInfo{UserID: uid})
    if info != nil && info.WeddingPlanId != 0 {
        err = m.CancelWedding(c, &pb.CancelWeddingRequest{Uid: uid, WeddingPlanId: info.GetWeddingPlanId()}, false)
        if err != nil {
            log.ErrorWithCtx(c, "Divorce fail to CancelWedding. uid:%d, err:%v", uid, err)
            return err
        }
    }

    err = m.st.Divorce(c, uid, targetUid)
    if err != nil {
        log.ErrorWithCtx(c, "Divorce fail to divorce. uid:%d, targetUid:%d, err:%v", uid, targetUid, err)
        return err
    }
    userInfoMap, err := m.rpc.AccountCli.GetUsersMap(c, []uint32{uid, targetUid})
    if err != nil {
        log.ErrorWithCtx(c, "Divorce fail to GetUser. uid:%d, err:%v", uid, err)
        return err
    }
    if userInfoMap[uid] == nil || userInfoMap[targetUid] == nil {
        log.ErrorWithCtx(c, "Divorce fail to GetUser. uid:%d, err:%v", uid, err)
        return err
    }
    _, err = m.cache.RemoveDivideTimeout(c, uid)
    if err != nil {
        log.ErrorWithCtx(c, "Divorce fail to RemoveDivideTimeout. uid:%d, err:%v", uid, err)
        return err
    }
    _, err = m.cache.RemoveDivideTimeout(c, targetUid)
    if err != nil {
        log.ErrorWithCtx(c, "Divorce fail to RemoveDivideTimeout. uid:%d, err:%v", targetUid, err)
        return err
    }

    if needMsg {
        _ = m.SendOfficialExtImMsg(c, targetUid, uid, uint32(imPB.IM_MSG_TYPE_SYSTEM_NOTIFY), 0, fmt.Sprintf("你已经成功解除了与%s的结婚关系", userInfoMap[targetUid].GetNickname()), nil)
        _ = m.SendOfficialExtImMsg(c, uid, targetUid, uint32(imPB.IM_MSG_TYPE_SYSTEM_NOTIFY), 0, fmt.Sprintf("%s已经成功解除了与你的结婚关系", userInfoMap[uid].GetNickname()), nil)
    }

    _ = m.DisableUserProposeList(c, uid)

    log.InfoWithCtx(c, "Divorce success, uid:%d, targetUid:%d", uid, targetUid)
    return nil
}

func (m *WeddingPlanMgr) GetUserDivorceStatus(c context.Context, uid uint32) (*pb.GetUserDivorceStatusResponse, error) {
    out := &pb.GetUserDivorceStatusResponse{}
    ts, err := m.cache.GetMyDivideTimeout(c, uid)
    if err != nil {
        log.ErrorWithCtx(c, "GetUserDivorceStatus fail to GetMyDivideTimeout. uid:%d, err:%v", uid, err)
        return out, err
    }
    out.AutoDivorceDay = int64(m.bc.GetConfig().AutoDivorceDay)
    if ts == 0 {
        return out, nil
    }
    out.DivorceDeadline = int64(ts + uint64(m.bc.GetConfig().AutoDivorceDay)*24*60*60)
    return out, nil
}

// GetUserMarriageInfo 获取用户婚姻关系信息
func (m *WeddingPlanMgr) GetUserMarriageInfo(c context.Context, uid uint32) (*pb.MarriageRelationInfo, error) {
    relation, err := m.st.GetMarriageRelationByUid(c, uid)
    if err != nil {
        log.ErrorWithCtx(c, "GetUserMarriageInfo fail to GetMarriageRelationByUid. uid:%d, err:%v", uid, err)
        return nil, err
    }
    if relation == nil {
        return nil, nil
    }

    cpUid := relation.TargetUid
    if relation.TargetUid == uid {
        cpUid = relation.FromUid
    }

    return &pb.MarriageRelationInfo{
        PartnerUid:    cpUid,
        RelationCtime: relation.CreateTime.Unix(),
        Uid:           uid,
    }, nil
}

func (m *WeddingPlanMgr) DisableUserProposeList(ctx context.Context, uid uint32) error {
    proposeList, err := m.st.GetProposeInfoByUidAndStatus(ctx, uid, uint32(pbLogic.ProposeStatus_PROPOSE_STATUS_INVITED))
    if err != nil {
        log.ErrorWithCtx(ctx, "SendPropose req:%v, Failed to get propose info: %v", uid, err)
        return nil
    }

    if len(proposeList) == 0 {
        return nil
    }

    for _, propose := range proposeList {
        if propose.Status != uint32(pbLogic.ProposeStatus_PROPOSE_STATUS_INVITED) {
            return nil
        }

        log.InfoWithCtx(ctx, "DisableUserProposeList proposeInfo:%+v", propose)
        propose.Status = uint32(pbLogic.ProposeStatus_PROPOSE_STATUS_TIMEOUT)
        err = m.st.UpdateProposeInfoStatusByID(ctx, propose.ID.Hex(), propose.Status)
        if err != nil {
            log.ErrorWithCtx(ctx, "DisableUserProposeList Failed to update propose info: %v", err)
            return nil
        }

        err = m.rpc.SendProposeMsg(ctx, propose, propose.FromUid, propose.TargetUid, propose.FromServerMsgId, propose.TargetServerMsgId, IMLetterTimeout, "[我们结婚吧]很抱歉...")
        if err != nil {
            log.ErrorWithCtx(ctx, "DisableUserProposeList Failed to send propose msg: %v", err)
        }
        log.InfoWithCtx(ctx, "DisableUserProposeList proposeInfo:%+v", propose)
    }
    return nil
}

func (m *WeddingPlanMgr) RevokePropose(ctx context.Context, uid uint32) error {
    proposeInfo, err := m.st.GetMyProposeInfo(ctx, uid, uint32(pbLogic.ProposeStatus_PROPOSE_STATUS_INVITED))
    if err != nil {
        log.ErrorWithCtx(ctx, "RevokePropose req:%v, Failed to get propose info: %v", uid, err)
        return err
    }

    if proposeInfo == nil {
        log.ErrorWithCtx(ctx, "RevokePropose uid:%d, not found", uid)
        return protocol.NewExactServerError(codes.OK, status.ErrChannelWeddingPlanCommonError, "操作失败")
    }

    //锁住求婚双方 避免重复操作
    err = m.cache.TryLock(ctx, fmt.Sprintf("lock_propose_%d", proposeInfo.FromUid), proposeInfo.ID.Hex(), time.Second*2)
    if err != nil {
        log.ErrorWithCtx(ctx, "RevokePropose req:%+v, Failed to  lock propose", uid)
        return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "操作失败")
    }

    err = m.cache.TryLock(ctx, fmt.Sprintf("lock_propose_%d", proposeInfo.TargetUid), proposeInfo.ID.Hex(), time.Second*2)
    if err != nil {
        log.ErrorWithCtx(ctx, "RevokePropose req:%+v, Failed to lock propose", uid)
        return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "操作失败")
    }

    err = m.st.UpdateProposeInfoStatusByID(ctx, proposeInfo.ID.Hex(), uint32(pbLogic.ProposeStatus_PROPOSE_STATUS_TIMEOUT))
    if err != nil {
        log.ErrorWithCtx(ctx, "RevokePropose req:%v, Failed to update propose info: %v", uid, err)
        return err
    }
    // 发送系统消息
    // 推送IM
    myText := "你取消了本次求婚"
    theyText := "对方取消了本次求婚"
    err = m.SendSysTextMutual(ctx, uid, proposeInfo.TargetUid, theyText, myText)
    if err != nil {
        log.ErrorWithCtx(ctx, " RevokePropose req:%v, Failed to send propose msg: %v", uid, err)
    } else {
        log.InfoWithCtx(ctx, "RevokePropose send propose msg success, uid:%d, targetUid:%d", uid, proposeInfo.TargetUid)
    }

    proposeInfo.Status = uint32(pbLogic.ProposeStatus_PROPOSE_STATUS_TIMEOUT)
    //发送IM && 推送
    err = m.rpc.SendProposeMsg(ctx, proposeInfo, proposeInfo.FromUid, proposeInfo.TargetUid, proposeInfo.FromServerMsgId, proposeInfo.TargetServerMsgId, IMLetterTimeout, "[我们结婚吧]很抱歉...")
    if err != nil {
        log.ErrorWithCtx(ctx, "HandleTimeOutPropose Failed to send propose msg: %v", err)
    }
    return nil
}
