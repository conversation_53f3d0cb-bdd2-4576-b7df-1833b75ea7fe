package mgr

import (
    "context"
    "errors"
    "fmt"
    "github.com/golang/protobuf/proto"
    "golang.52tt.com/pkg/log"
    "golang.52tt.com/pkg/protocol"
    protocolgrpc "golang.52tt.com/pkg/protocol/grpc"
    "golang.52tt.com/protocol/app/channel_wedding_logic"
    imPB "golang.52tt.com/protocol/app/im"
    "golang.52tt.com/protocol/common/status"
    account_go "golang.52tt.com/protocol/services/account-go"
    channel_wedding_conf "golang.52tt.com/protocol/services/channel-wedding-conf"
    pb "golang.52tt.com/protocol/services/channel-wedding-plan"
    currencyPB "golang.52tt.com/protocol/services/currencysvr"
    unifiedPayPB "golang.52tt.com/protocol/services/unified_pay"
    UnifiedPayCallback "golang.52tt.com/protocol/services/unified_pay/cb"
    "golang.52tt.com/services/channel-wedding-plan/internal/store"
    context0 "golang.org/x/net/context"
    "google.golang.org/grpc/codes"
    "time"
)

const (
    CallbackSourceNotify = iota // 通知回调
    CallbackSourceTimer         // 定时任务
    jumpWeddingBuyPage   = "tt://m.52tt.com/customize_wedding"

    relationAlreadyEnd = "失败，当前关系已解除~"
)

func (m *WeddingPlanMgr) BuyWedding(c context.Context, request *pb.BuyWeddingRequest) (uint32, error) {
    if request.GetThemeId() == 0 || request.GetBuyerUid() == 0 {
        log.ErrorWithCtx(c, "BuyWedding theme id is zero, req:%+v", request)
        return 0, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
    }
    now := time.Now()
    outsideTime := time.Unix(now.Unix(), 0) // 去除 nsec
    log.DebugWithCtx(c, "buyWedding, request: %+v", request)

    themeCfgResp, err := m.rpc.WeddingConfCli.GetThemeCfg(c, &channel_wedding_conf.GetThemeCfgReq{
        ThemeId: request.GetThemeId(),
    })
    if err != nil {
        log.ErrorWithCtx(c, "BuyWedding get theme cfg by id failed, err: %v, req:%+v", err, request)
        return 0, err
    }
    themeCfg := themeCfgResp.GetThemeCfg()
    if themeCfg == nil || themeCfg.ThemeId == 0 || themeCfg.GetPriceInfo().GetPrice() == 0 {
        log.ErrorWithCtx(c, "BuyWedding get theme cfg by id failed, err: %v, req:%+v", err, request)
        return 0, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "配置异常")
    }

    if themeCfg.GetIsDeleted() {
        log.ErrorWithCtx(c, "BuyWedding theme is deleted, req:%+v", request)
        return 0, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "主题已下架")
    }

    previousPlan, err := m.st.GetGoingPlan(c, request.GetBuyerUid())
    if err != nil {
        log.ErrorWithCtx(c, "BuyWedding fail to GetGoingWeddingOrder. err:%v, req:%+v", err, request)
        return 0, err
    }
    if previousPlan != nil && previousPlan.ID != 0 {
        log.ErrorWithCtx(c, "BuyWedding fail to GetGoingWeddingOrder. err:%v, req:%+v", err, request)
        return 0, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "你已经获得了婚礼~去定制吧")
    }

    marriageInfo, err := m.GetUserMarriageInfo(c, request.GetBuyerUid())
    if err != nil {
        log.ErrorWithCtx(c, "BuyWedding fail to GetUserMarriageInfo. err:%v, req:%+v", err, request)
        return 0, err
    }

    if marriageInfo == nil || marriageInfo.GetUid() == 0 || marriageInfo.GetPartnerUid() == 0 {
        log.ErrorWithCtx(c, "BuyWedding fail to GetUserMarriageInfo. err:%v, req:%+v", err, request)
        return 0, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "要有结婚对象才能预约婚礼哦~")
    }

    // get divorce status
    groomDivideTimeout, err := m.cache.GetMyDivideTimeout(c, marriageInfo.GetUid())
    if err != nil {
        log.ErrorWithCtx(c, "BuyWedding fail to GetMyDivideTimeout. err:%v, req:%+v", err, request)
        return 0, err
    }
    if groomDivideTimeout > 0 {
        log.ErrorWithCtx(c, "BuyWedding fail to GetMyDivideTimeout. err:%v, req:%+v", err, request)
        return 0, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "解除关系期间不支持预约婚礼哦")
    }

    brideDivideTimeout, err := m.cache.GetMyDivideTimeout(c, marriageInfo.GetPartnerUid())
    if err != nil {
        log.ErrorWithCtx(c, "BuyWedding fail to GetMyDivideTimeout. err:%v, req:%+v", err, request)
        return 0, err
    }
    if brideDivideTimeout > 0 {
        log.ErrorWithCtx(c, "BuyWedding fail to GetMyDivideTimeout. err:%v, req:%+v", err, request)
        return 0, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "解除关系期间不支持预约婚礼哦")
    }

    // 预约时间
    var reserveSt, reserveEt uint32
    if request.GetBuyReserveInfo() != nil {
        reserveSt, reserveEt, err = m.TranReserveTimeSection2Time(request.GetBuyReserveInfo().GetReserveDate(), request.GetBuyReserveInfo().GetReserveTime())
        if err != nil {
            log.ErrorWithCtx(c, "BuyWedding fail to TranReserveTimeSection2Time. req: %+v, err:%v", request, err)
        }
        // 判断时间预约时间是否合法
        if !m.checkReserveSectionValid(time.Unix(int64(reserveSt), 0), themeCfg.GetPriceInfo().GetPriceType()) {
            log.ErrorWithCtx(c, "BuyWedding fail to checkReserveSectionValid. req: %+v, err:%v", request, err)
            return 0, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "当前时段即将开始，选择其他空闲时段吧~")
        }
        if themeCfg.GetPriceInfo().GetPriceType() == uint32(channel_wedding_logic.WeddingPriceType_WEDDING_PRICE_TYPE_T_BEAN) {
            canDo, err := m.CheckWeddingPlanByChannelIdAndTimeRange(c, request.GetBuyReserveInfo().GetChannelId(), reserveSt, reserveEt)
            if err != nil {
                log.ErrorWithCtx(c, "BuyWedding fail to CheckWeddingPlanByChannelIdAndTimeRange. err:%v", err)
                return 0, err
            }
            if !canDo {
                log.WarnWithCtx(c, "BuyWedding fail, channel is not available")
                return 0, protocol.NewExactServerError(codes.OK, status.ErrChannelWeddingMinigameCommonErr, "该时段已被预约，请重新选择时间")
            }
        }
    }
    st := time.Unix(int64(reserveSt), 0)
    et := time.Unix(int64(reserveEt), 0)

    // 热门婚礼只能走安排预约
    if request.GetBuyReserveInfo().GetIsHot() && request.GetSourceMsgId() == 0 {
        log.ErrorWithCtx(c, "BuyWedding fail to GetWeddingReserveIM. sourceMsgId: %d, err:%v", request.GetSourceMsgId(), err)
        return 0, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "请先咨询客服哦~")
    }

    // 判断购买跟im安排的是否一致
    var reserveIm *store.WeddingReserveIM
    if request.GetSourceMsgId() > 0 {
        reserveIm, err = m.st.GetWeddingReserveIM(c, request.GetSourceMsgId())
        if err != nil {
            log.ErrorWithCtx(c, "BuyWedding fail to GetWeddingReserveIM. sourceMsgId: %d, err:%v", request.GetSourceMsgId(), err)
            return 0, err
        }
        if reserveIm == nil || reserveIm.ReserveInfo == nil {
            log.ErrorWithCtx(c, "BuyWedding fail to GetWeddingReserveIM. sourceMsgId: %d, err:%v", request.GetSourceMsgId(), err)
            return 0, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "邀请信息不存在")
        }
        if reserveIm.ReserveInfo.ChannelId != request.GetBuyReserveInfo().GetChannelId() ||
            reserveIm.ReserveInfo.StartTime != reserveSt || reserveIm.ReserveInfo.EndTime != reserveEt {
            log.ErrorWithCtx(c, "BuyWedding fail to GetWeddingReserveIM. sourceMsgId: %d, err:%v", request.GetSourceMsgId(), err)
            return 0, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "邀请时间不一致")
        }
        if time.Now().Sub(reserveIm.CreateTime) > time.Duration(m.bc.GetArrangeReserveImPayValidTime())*time.Minute {
            log.ErrorWithCtx(c, "BuyWedding fail to GetWeddingReserveIM. sourceMsgId: %d, err:%v", request.GetSourceMsgId(), err)
            return 0, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "此次邀请已过期")
        }
        if reserveIm.ReserveInfo.BuyUid != 0 && (reserveIm.ReserveInfo.BuyUid != request.GetBuyerUid() || reserveIm.ReserveInfo.TargetUid != marriageInfo.GetPartnerUid()) {
            log.ErrorWithCtx(c, "BuyWedding fail to GetWeddingReserveIM. sourceMsgId: %d, err:%v", request.GetSourceMsgId(), err)
            return 0, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "此次邀请已失效")
        }
    }

    // 红钻购买限制
    if themeCfg.GetPriceInfo().GetPriceType() == uint32(channel_wedding_logic.WeddingPriceType_WEDDING_PRICE_TYPE_RED_DIAMOND) && m.bc.GetConfig().BuyLimitCfg.Switch {
        // 获取订单数量
        startTime := time.Now().Unix() - int64(m.bc.GetConfig().BuyLimitCfg.TimeRange)*3600
        cnt, err := m.st.GetValidWeddingOrderCnt(c, uint32(channel_wedding_logic.WeddingPriceType_WEDDING_PRICE_TYPE_RED_DIAMOND), startTime, request.GetBuyerUid())
        if err != nil {
            log.ErrorWithCtx(c, "BuyWedding fail to GetValidWeddingOrderCnt. in:%+v, err:%v", request, err)
            return 0, err
        }
        if cnt >= m.bc.GetConfig().BuyLimitCfg.MaxTimes {
            log.ErrorWithCtx(c, "BuyWedding fail to GetValidWeddingOrderCnt. in:%+v, err:%v, cnt:%d", request, err, cnt)
            return 0, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, m.bc.GetConfig().BuyLimitCfg.LimitText)
        }
    }

    userInfo, err := m.rpc.AccountCli.GetUser(c, marriageInfo.GetUid())
    if err != nil {
        log.ErrorWithCtx(c, "BuyWedding fail to GetUser. err:%v, req:%+v", err, request)
        return 0, err
    }
    var groomUid, brideUid uint32
    if userInfo.GetSex() == int32(account_go.USER_SEX_USER_SEX_FEMALE) {
        groomUid = marriageInfo.GetPartnerUid()
        brideUid = marriageInfo.GetUid()
    } else {
        groomUid = marriageInfo.GetUid()
        brideUid = marriageInfo.GetPartnerUid()
    }

    price, giftId := m.getThemePriceAndGift(request.GetBuyReserveInfo().GetIsHot(), request.GetBuyReserveInfo().GetGiftId(), themeCfg)
    payOrderId := fmt.Sprintf("WEDDING_BUY_%d_%d", request.GetBuyerUid(), now.UnixNano())
    order := &store.WeddingOrder{
        OrderId:          payOrderId,
        GroomUid:         groomUid,
        BrideUid:         brideUid,
        ThemeId:          themeCfg.GetThemeId(),
        BuyerUid:         request.GetBuyerUid(),
        WeddingStatus:    store.WeddingStatusInit,
        WeddingPrice:     price,
        WeddingPriceType: themeCfg.GetPriceInfo().GetPriceType(),
        WeddingGiftId:    giftId,
        SourceMsgId:      request.GetSourceMsgId(),
        CreateTs:         outsideTime.Unix(),
        UpdateTs:         outsideTime.Unix(),
    }

    var weddingKey string
    if order.GroomUid > order.BrideUid {
        weddingKey = fmt.Sprintf("buy_wedding:%d_%d", order.BrideUid, order.GroomUid)
    } else {
        weddingKey = fmt.Sprintf("buy_wedding:%d_%d", order.GroomUid, order.BrideUid)
    }

    err = m.cache.TryLock(c, weddingKey, payOrderId, 2*time.Second)
    if err != nil {
        if e := protocol.ToServerError(err); e != nil && e.Code() == status.ErrChannelWeddingPlanCommonError {
            return 0, protocol.NewExactServerError(codes.OK, status.ErrChannelWeddingPlanCommonError, "你已经获得了婚礼~去定制吧")
        }
        log.ErrorWithCtx(c, "BuyWedding fail to TryLock. err:%v", err)
        return 0, err
    }

    err = m.st.InsertWeddingOrder(c, order)
    if err != nil {
        log.ErrorWithCtx(c, "BuyWedding fail to InsertWeddingRecordAndInitPlan. err:%v", err)
        return 0, err
    }

    switch themeCfg.GetPriceInfo().GetPriceType() {
    case uint32(channel_wedding_logic.WeddingPriceType_WEDDING_PRICE_TYPE_T_BEAN):
        _, err = m.freeze(c, payOrderId, themeCfg.GetThemeName(), request.GetBuyerUid(), price, outsideTime)
        if err != nil {
            log.ErrorWithCtx(c, "BuyWedding fail to freeze. in:%+v, err:%v", request, err)
            return 0, err
        }
    case uint32(channel_wedding_logic.WeddingPriceType_WEDDING_PRICE_TYPE_RED_DIAMOND):

        // 扣红钻
        err = m.rpc.Currency.AddUserCurrency(c, request.GetBuyerUid(), -int32(themeCfg.GetPriceInfo().GetPrice()), payOrderId, "购买婚礼扣除", uint32(currencyPB.ADD_CURRENCY_REASON_BUY_CHANNEL_WEDDING))
        if err != nil {
            log.ErrorWithCtx(c, "BuyWedding fail to AddUserCurrency. in:%+v, err:%v", request, err)
            return 0, err
        }
        _ = m.rpc.ApiClient.NotifyGrowInfoSync(c, request.GetBuyerUid())

    default:
        log.ErrorWithCtx(c, "BuyWedding unknown price type, req:%+v", request)
        return 0, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "配置异常")
    }

    var reserveInfo *store.ReserveInfo
    if request.GetBuyReserveInfo() != nil && reserveSt > 0 && reserveEt > 0 {
        reserveInfo = &store.ReserveInfo{
            ChannelId: request.GetBuyReserveInfo().GetChannelId(),
            StartTime: reserveSt,
            EndTime:   reserveEt,
        }
    }

    // 更新为 freezing 状态
    err = m.st.UpdateOrder2FreezeAndInitPlan(c, outsideTime, order, reserveInfo, request.GetBuyReserveInfo().GetIsHot())
    //err = m.st.UpdateOrder2FreezeAndInitPlan(c, outsideTime, order, reserveInfo, true)
    if err != nil {
        log.ErrorWithCtx(c, "freeze fail to ChangeConsumeRecordPayInfo. uid:%v, orderId:%v, outsideTime:%v, err:%v", request.GetBuyerUid(), payOrderId, outsideTime, err)
        return 0, err
    }

    _, err = m.st.UpdateOrderStatus(c, order.Id, []uint32{store.WeddingStatusFreezing}, store.WeddingStatusReserved)
    if err != nil {
        log.ErrorWithCtx(c, "UpdateWeddingReserve fail to UpdateOrderStatus. err:%v", err)
        return 0, err
    }

    // 推送im消息
    _ = m.SendImMgsToUid(c, request.GetBuyerUid(), marriageInfo.GetPartnerUid(), fmt.Sprintf("我准备了婚礼「%s」，一起去预约和定制婚礼吧~", themeCfg.GetThemeName()),
        "一起去预约和定制婚礼吧~", "tt://m.52tt.com/customize_wedding")

    asyncCtx1, asyncCtx1Cancel := protocolgrpc.NewContextWithInfoTimeout(c, 5*time.Second)
    // 付费婚礼tt助手给购买人发送婚礼购买消息
    if themeCfg.GetPriceInfo().GetPriceType() == uint32(channel_wedding_logic.WeddingPriceType_WEDDING_PRICE_TYPE_T_BEAN) {
        go func(asyncCtx context.Context) {
            defer asyncCtx1Cancel()
            m.sendBoughtTTAssistantMsg(asyncCtx, marriageInfo.GetUid(), request.GetBuyReserveInfo().GetChannelId(), giftId, st, et, request.GetBuyReserveInfo().GetIsHot())
            m.sendBoughtTTAssistantMsg(asyncCtx, marriageInfo.GetPartnerUid(), request.GetBuyReserveInfo().GetChannelId(), giftId, st, et, request.GetBuyReserveInfo().GetIsHot())
        }(asyncCtx1)

    }

    asyncCtx2, asyncCtx2Cancel := protocolgrpc.NewContextWithInfoTimeout(c, 5*time.Second)
    // 更新客服安排时间的im状态, 发送购买成功消息给客服
    go func(asyncCtx context.Context) {
        defer asyncCtx2Cancel()
        if reserveIm == nil {
            return
        }
        log.DebugWithCtx(asyncCtx, "BuyWedding, reserveIm:%+v", reserveIm)
        arrangeMsg := &channel_wedding_logic.ArrangeWeddingReserveIMMsg{}
        err = proto.Unmarshal(reserveIm.MsgBytes, arrangeMsg)
        if err != nil {
            log.ErrorWithCtx(c, "BuyWedding fail to Unmarshal. err:%v", err)
            return
        }
        arrangeMsg.IsPaid = true
        // 发送im
        err = m.sendArrangeImUpdate(asyncCtx, reserveIm, arrangeMsg)
        if err != nil {
            log.ErrorWithCtx(asyncCtx, "BuyWedding fail to updateReserveArrangeIm. sourceMsgId: %d, err:%v", request.GetSourceMsgId(), err)
        }
        // 更新为已完成
        err = m.st.UpdateWeddingReserveIMStatus(asyncCtx, []uint32{reserveIm.ID}, store.WeddingReserveIMStatusSuccess)
        if err != nil {
            log.WarnWithCtx(asyncCtx, "BuyWedding fail to UpdateWeddingReserveIMStatus. sourceMsgId: %d, err:%v", request.GetSourceMsgId(), err)
        }
        text := fmt.Sprintf("已预约 %s-%s", st.Format("01月02日15:04"), et.Format("15:04"))
        err = m.SendOfficialExtImMsg(asyncCtx, reserveIm.TargetUid, reserveIm.FromUid, uint32(imPB.IM_MSG_TYPE_SYSTEM_NOTIFY), 0, text, nil)
        if err != nil {
            log.ErrorWithCtx(asyncCtx, "updateReserveArrangeIm fail to SendOfficialExtImMsg. sourceMsgId: %d, err:%v", request.GetSourceMsgId(), err)
            return
        }
        err = m.SendOfficialExtImMsg(asyncCtx, reserveIm.FromUid, reserveIm.TargetUid, uint32(imPB.IM_MSG_TYPE_SYSTEM_NOTIFY), 0, text, nil)
        if err != nil {
            log.ErrorWithCtx(asyncCtx, "updateReserveArrangeIm fail to SendOfficialExtImMsg. sourceMsgId: %d, err:%v", request.GetSourceMsgId(), err)
            return
        }
    }(asyncCtx2)
    return order.Id, nil
}

func (m *WeddingPlanMgr) sendBoughtTTAssistantMsg(ctx context.Context, buyerUid, cid, giftId uint32, st, et time.Time, isHot bool) error {
    channelInfoResp, err := m.rpc.ChannelCli.GetChannelSimpleInfo(ctx, 0, cid)
    if err != nil {
        log.ErrorWithCtx(ctx, "sendBoughtTTAssistantMsg fail to GetChannelSimpleInfo. buyerUid: %d, cid: %d, err:%v", buyerUid, cid, err)
        return err
    }

    giftInfoResp, pErr := m.rpc.PresentCli.GetPresentConfigById(ctx, giftId)
    if pErr != nil {
        log.ErrorWithCtx(ctx, "sendBoughtTTAssistantMsg fail to GetPresentConfigById. buyerUid: %d, giftId: %d, err:%v", buyerUid, giftId, pErr)
        return pErr
    }
    hotText := "热门时段"
    if !isHot {
        hotText = ""
    }
    m.SendTTAssistantText(ctx, buyerUid, fmt.Sprintf("恭喜成功预约“%s”房间的%s婚礼仪式（%s开始）\n同时婚礼预约方会获得价值%d元的婚礼礼物“%s”，礼物将于婚礼开始后发放并支持送给你的伴侣，敬请期待吧~",
        channelInfoResp.GetName(), hotText, st.Format("01月02日15:04"), giftInfoResp.GetItemConfig().GetPrice()/100, giftInfoResp.GetItemConfig().GetName()), "", "")

    return nil
}

func (m *WeddingPlanMgr) getThemePriceAndGift(isHot bool, reqGiftId uint32, themeCfg *channel_wedding_conf.ThemeCfg) (uint32, uint32) {
    // 用旧价格/普通时段价格兜底
    price := themeCfg.GetPriceInfo().GetPrice()
    giftId := uint32(0)

    if len(themeCfg.GetPriceInfo().GetNormalTimePrice()) > 0 {
        price = themeCfg.GetPriceInfo().GetNormalTimePrice()[0].GetPrice()
        giftId = themeCfg.GetPriceInfo().GetNormalTimePrice()[0].GetGiftId()
    }

    if isHot {
        for _, item := range themeCfg.GetPriceInfo().GetHotTimePrice() {
            if item.GetGiftId() == reqGiftId { // 找到匹配的热门礼物价格配置
                price = item.GetPrice()
                giftId = item.GetGiftId()
            }
        }
    }

    return price, giftId
}

func (m *WeddingPlanMgr) freeze(ctx context.Context, orderId, themeName string, uid, totalPrice uint32, outsideTime time.Time) (uint32, error) {
    // unified-pay 冻结T豆
    timeStr := outsideTime.Format("2006-01-02 15:04:05")
    reason := fmt.Sprintf("购买婚礼「%s」", themeName)

    restBalance, sErr := m.rpc.UnifiedPayCli.PresetFreeze(ctx, uid, totalPrice, m.bc.GetConfig().WeddingPayAppId, orderId, timeStr, reason)
    if sErr != nil {
        log.ErrorWithCtx(ctx, "freeze fail to PresetFreeze. uid:%v, orderId:%v, outsideTime:%v, err:%v", uid, orderId, outsideTime, sErr)
        return restBalance, sErr
    }

    log.Infof("freeze uid:%d, orderId:%s, totalPrice:%d, outsideTime:%v, restBalance:%d", uid, orderId, totalPrice, outsideTime, restBalance)
    return restBalance, nil
}

func (m *WeddingPlanMgr) commit(ctx context.Context, orderId, weddingName string, themeId, uid, cnt, totalPrice uint32, outsideTime time.Time) error {
    var err error
    user, err := m.rpc.AccountCli.GetUser(ctx, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "commit fail to GetUser. uid:%v, orderId:%v, outsideTime:%v, err:%v", uid, orderId, outsideTime, err)
        return err
    }

    reason := fmt.Sprintf("购买婚礼「%s」", weddingName)

    req := &unifiedPayPB.UnfreezeAndConsumeReq{
        AppId:      m.bc.GetConfig().WeddingPayAppId,
        Uid:        uid,
        UserName:   user.GetUsername(),
        ItemId:     themeId,
        ItemName:   reason,
        ItemNum:    cnt,
        ItemPrice:  totalPrice,
        TotalPrice: cnt * totalPrice,
        Platform:   "0",
        OutTradeNo: orderId,
        Notes:      "channel-wedding-plan",
    }

    // unified-pay 确认扣除T豆
    timeStr, dealToken, err := m.rpc.UnifiedPayCli.UnfreezeAndConsume(ctx, req)
    if err != nil {
        // 货币组commit接口在并发时会因锁报错，在这里重试一次
        retryCtx, cancel := context.WithTimeout(context.Background(), 1*time.Second)
        defer cancel()

        timeStr, _, err = m.rpc.UnifiedPayCli.UnfreezeAndConsume(retryCtx, req)
        if err != nil {
            log.ErrorWithCtx(ctx, "commit fail to UnfreezeAndConsume. uid:%v, orderId:%v, outsideTime:%v, err:%v", uid, orderId, outsideTime, err)
            return err
        }
        log.InfoWithCtx(ctx, "commit UnfreezeAndConsume retry . uid:%v, orderId:%v, outsideTime:%v, err:%v", uid, orderId, outsideTime, err)
    } else {
        log.InfoWithCtx(ctx, "commit  UnfreezeAndConsume. uid:%v, orderId:%v, outsideTime:%v, err:%v", uid, orderId, outsideTime, err)
    }

    // 失败也没关系，对账包含这些已扣款状态
    _, err = m.st.ChangeConsumeRecordPayInfo(ctx, outsideTime, uid, []uint32{store.WeddingStatusReserved}, store.WeddingStatusConfirm, orderId, dealToken, timeStr)
    if err != nil {
        log.ErrorWithCtx(ctx, "commit fail to ChangeConsumeRecordPayInfo. uid:%v, orderId:%v, outsideTime:%v, err:%v", uid, orderId, outsideTime, err)
    } else {
        log.Infof("commit uid:%d, orderId:%s, outsideTime:%v, cTime:%v", uid, orderId, outsideTime, timeStr)
    }

    return nil
}

func (m *WeddingPlanMgr) rollback(ctx context.Context, orderId string, uid uint32, sourceStatusList []uint32, outsideTime time.Time, needUpdateStatus bool) error {
    var err error
    if needUpdateStatus {
        // 更新订单状态为回滚
        //sourceStatusList := []uint32{
        //    store.WeddingStatusFreezing,
        //}
        ok, err := m.st.ChangeConsumeRecordPayInfo(ctx, outsideTime, uid, sourceStatusList, store.WeddingStatusRollback, orderId, "", "")
        if err != nil {
            log.ErrorWithCtx(ctx, "rollback fail to ChangeConsumeRecordPayInfo. uid:%v, orderId:%v, outsideTime:%v, err:%v", uid, orderId, outsideTime, err)
            return err
        }

        if !ok {
            err = errors.New("订单状态更新失败")
            log.ErrorWithCtx(ctx, "rollback fail to ChangeConsumeRecordPayInfo. uid:%v, orderId:%v, outsideTime:%v, err:%v", uid, orderId, outsideTime, err)
            return err
        }
    }

    // unified-pay 解冻T豆, 没解冻成功会一直回调
    err = m.rpc.UnifiedPayCli.UnFreezeAndRefund(ctx, uid, m.bc.GetConfig().WeddingPayAppId, orderId)
    if err != nil {
        log.ErrorWithCtx(ctx, "rollback fail to UnFreezeAndRefund. uid:%v, orderId:%v, outsideTime:%v, err:%v", uid, orderId, outsideTime, err)
        return err
    }

    log.Infof("rollback uid:%v, orderId:%v, outsideTime:%v", uid, orderId, outsideTime)
    return nil
}

func (m *WeddingPlanMgr) CancelWedding(c context.Context, request *pb.CancelWeddingRequest, needCheckPermission bool) error {
    var err error
    order, err := m.st.GetWeddingOrderByPlanId(c, request.GetWeddingPlanId())
    if err != nil {
        log.ErrorWithCtx(c, "CancelWedding fail to GetWeddingOrderByPlanId. err:%v, request:%+v", err, request)
        return err
    }

    if needCheckPermission && (order == nil || order.BuyerUid != request.GetUid()) {
        log.ErrorWithCtx(c, "CancelWedding fail to GetWeddingOrderByPlanId. err:%v, request:%+v", err, request)
        return protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "当前仅支持购买婚礼方取消婚礼")
    }

    plan, err := m.GetWeddingPlanByIdNoCheck(c, request.GetWeddingPlanId(), false)
    if err != nil {
        log.ErrorWithCtx(c, "CancelWedding fail to GetWeddingPlanByIdNoCheck. err:%v, request:%+v", err, request)
        return err
    }
    reserveInfo := plan.ReserveInfo

    if reserveInfo != nil {
        nowTs := time.Now()
        if nowTs.After(time.Unix(int64(reserveInfo.StartTime), 0)) {
            log.ErrorWithCtx(c, "CancelWedding fail, wedding already start. err:%v, request:%+v", err, request)
            return protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "婚礼已开始,不支持取消婚礼哦")
        } else if needCheckPermission && nowTs.After(time.Unix(int64(reserveInfo.StartTime), 0).Add(-time.Duration(m.bc.GetConfig().ReserveConf.LimitChangeAheadTime)*time.Hour)) {
            log.ErrorWithCtx(c, "CancelWedding fail, wedding coming soon. err:%v, request:%+v", err, request)
            return protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "婚礼即将开始,不支持取消婚礼哦")
        }
    }

    if order.WeddingPriceType == uint32(channel_wedding_logic.WeddingPriceType_WEDDING_PRICE_TYPE_RED_DIAMOND) {
        err = m.handleRedDiamondRefund(c, order)
        if err != nil {
            log.ErrorWithCtx(c, "CancelWedding fail to handleRedDiamondRefund. err:%v, request:%+v", err, request)
            return err
        }
    } else {
        _, err = m.st.UpdateOrderStatus(c, request.GetWeddingPlanId(), []uint32{
            store.WeddingStatusReserved,
            store.WeddingStatusFreezing},
            store.WeddingStatusCancel)
        if err != nil {
            log.ErrorWithCtx(c, "CancelWedding fail to UpdateOrderStatus. err:%v, request:%+v", err, request)
            return err
        }
        err = m.st.BatchUpdateWeddingPlanStatus(c, []uint32{request.GetWeddingPlanId()}, store.WeddingPlanStatusCancel)
        if err != nil {
            log.ErrorWithCtx(c, "CancelWedding fail to BatchUpdateWeddingPlanStatus. err:%v, request:%", err, request)
            return err
        }
        log.InfoWithCtx(c, "CancelWedding BatchUpdateWeddingPlanStatus success. request:%+v", request)
    }

    marriageInfo, err := m.GetUserMarriageInfo(c, request.GetUid())
    if err != nil {
        log.ErrorWithCtx(c, "CancelWedding fail to GetUserMarriageInfo. err:%v, request:%+v", err, request)
        return err
    }
    ext, _ := proto.Marshal(&imPB.SystemNotifyMsg{
        JumpUrl:          jumpWeddingBuyPage,
        HighlightContent: "预订婚礼页",
    })
    userInfo, err := m.rpc.AccountCli.GetUser(c, request.GetUid())
    if err != nil {
        log.ErrorWithCtx(c, "CancelWedding fail to GetUser. err:%v, request:%+v", err, request)
        return err
    }
    _ = m.SendOfficialExtImMsg(c, marriageInfo.GetPartnerUid(), marriageInfo.GetUid(), uint32(imPB.IM_MSG_TYPE_SYSTEM_NOTIFY), 0, "你取消预订了你们的婚礼预订，需要再次预订的话可以去 预订婚礼页 在购买心仪的婚礼哦~", ext)
    _ = m.SendOfficialExtImMsg(c, marriageInfo.GetUid(), marriageInfo.GetPartnerUid(), uint32(imPB.IM_MSG_TYPE_SYSTEM_NOTIFY), 0, fmt.Sprintf("%s取消预订了你们的婚礼预订，需要再次预订的话可以去 预订婚礼页 在购买心仪的婚礼哦~", userInfo.GetNickname()), ext)

    // 通知客服婚礼取消
    asyncCtx, asyncCtxCancel := protocolgrpc.NewContextWithInfoTimeout(c, 5*time.Second)
    go func(ctx context.Context) {
        defer asyncCtxCancel()
        if reserveInfo == nil {
            return
        }
        if order.SourceMsgId > 0 {
            reserveIm, err := m.st.GetWeddingReserveIM(ctx, order.SourceMsgId)
            if err != nil {
                log.ErrorWithCtx(c, "CancelWedding fail to GetWeddingReserveIM. err: %v, sourceMsgId: %d", err, order.SourceMsgId)
            }
            if reserveIm != nil {
                channelInfo, err := m.rpc.ChannelCli.GetChannelSimpleInfo(ctx, 0, reserveInfo.ChannelId)
                if err != nil {
                    log.ErrorWithCtx(ctx, "CancelWedding fail to GetChannelSimpleInfo, err: %+v, channelId: %d", err, reserveInfo.ChannelId)
                    return
                }
                rst := time.Unix(int64(reserveInfo.StartTime), 0)
                text := fmt.Sprintf("【婚礼取消通知】原本于“%s”房间举行的婚礼（%s开始）已被用户取消，可以安排其他婚礼哦~", channelInfo.GetName(), rst.Format("01月02日15:04"))
                imErr := m.SendSysTextMutual(ctx, reserveIm.TargetUid, reserveIm.FromUid, text, text)
                if imErr != nil {
                    log.ErrorWithCtx(ctx, "CancelWedding fail to SendSysTextMutual, reserveIm: %+v, err: %v", reserveIm, imErr)
                }
            }
        }
    }(asyncCtx)

    log.InfoWithCtx(c, "CancelWedding success. request:%+v", request)
    return nil
}

func genDurationStr(startTime, endTime int64) string {
    // out format: 2025-01-01 15:00-15:30
    return fmt.Sprintf("%s~%s", time.Unix(startTime, 0).Format("2006-01-02 15:04"), time.Unix(endTime, 0).Format("15:04"))
}
func (m *WeddingPlanMgr) GetMyWeddingInfo(c context.Context, request *pb.GetMyWeddingInfoRequest) (*pb.GetMyWeddingInfoResponse, error) {
    plan, err := m.st.GetGoingPlan(c, request.GetUid())
    if err != nil {
        log.ErrorWithCtx(c, "GetMyWeddingInfo fail to GetGoingWeddingOrder. err:%v, request:%+v", err, request)
        return nil, err
    }
    if plan == nil {
        return &pb.GetMyWeddingInfoResponse{}, nil
    }
    return &pb.GetMyWeddingInfoResponse{
        WeddingPlanId: plan.ID,
        ThemeId:       plan.ThemeId,
        BuyerUid:      plan.BuyerUid,
    }, nil
}

func (m *WeddingPlanMgr) GetAllThemeCfg(c context.Context) (*pb.GetAllThemeCfgResponse, error) {
    out := &pb.GetAllThemeCfgResponse{}

    //themeCfgList, err := m.st.GetAllThemeCfg(c)
    //if err != nil {
    //    log.ErrorWithCtx(c, "GetAllThemeCfg fail to GetAllThemeCfg. err:%v", err)
    //    return nil, err
    //}
    //out.ThemeCfg = themeCfgList
    return out, nil
}

func (m *WeddingPlanMgr) AddThemeCfg(c context.Context, request *pb.AddThemeCfgRequest) error {
    return m.st.CreateThemeCfg(c, request)
}

func (m *WeddingPlanMgr) UpdateThemeCfg(c context.Context, request *pb.UpdateThemeCfgRequest) error {
    return m.st.UpdateThemeCfg(c, request.GetThemeCfg())
}

func (m *WeddingPlanMgr) DeleteThemeCfg(c context.Context, request *pb.DeleteThemeCfgRequest) error {
    return m.st.DeleteThemeCfg(c, request.GetThemeId())
}

func (m *WeddingPlanMgr) Callback(ctx context.Context, uid uint32, orderId string, source uint32) (op UnifiedPayCallback.Op, err error) {
    log.DebugWithCtx(ctx, "Callback. uid:%v, orderId:%v, source:%v", uid, orderId, source)
    order, err := m.st.GetWeddingOrderById(ctx, orderId)
    if err != nil {
        log.ErrorWithCtx(ctx, "Callback fail to GetWeddingOrderById. orderId:%v, err:%v, uid:%v", orderId, err, uid)
        return 0, err
    }
    if order == nil || order.Id == 0 {
        log.ErrorWithCtx(ctx, "Callback fail to GetWeddingOrderById. orderId:%v, err:%v, uid:%v", orderId, err, uid)
        return 0, protocol.NewExactServerError(codes.OK, status.ErrChannelWeddingOrderNotExist)
    }

    // 覆盖为订单创建者
    if uid == 0 {
        uid = order.BuyerUid
    }

    outsideTime := order.CreateTs
    orderStatus := order.WeddingStatus

    // get wedding plan
    plan, err := m.st.GetWeddingPlanById(ctx, order.Id)
    if err != nil {
        log.ErrorWithCtx(ctx, "Callback fail to GetWeddingPlanById. order:+%v, err:%v, uid:%v", order, err, uid)
        return 0, protocol.NewExactServerError(codes.OK, status.ErrChannelWeddingOrderNotExist)
    }

    // plan不可能为空， 直接回滚订单
    if plan == nil {
        // WeddingStatusRollback库里处于回滚状态，远端可能尚未回滚，需要再次回滚
        err = m.rollback(ctx, orderId, uid, []uint32{store.WeddingStatusRollback, store.WeddingStatusCancel, store.WeddingStatusReserved, store.WeddingStatusFreezing}, time.Now(), true)
        if err != nil {
            log.ErrorWithCtx(ctx, "Callback fail to rollback. order:%+v, uid:%v", order, uid)
            return 0, err
        }
        log.InfoWithCtx(ctx, "Callback rollback success. order:%+v, uid:%v", order, uid)
        return 0, err
    }

    switch orderStatus {
    case store.WeddingStatusReserved:

        if plan.ReserveInfo == nil {
            log.ErrorWithCtx(ctx, "Callback fail to GetWeddingPlanById. order%+v, err:%v, uid:%v", order, err, uid)
            return 0, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "预约信息为空")
        }
        reserveTime := time.Unix(int64(plan.ReserveInfo.StartTime), 0)
        cfgTimeAhead := time.Duration(m.bc.GetConfig().ReserveConf.LimitChangeAheadTime) * time.Hour
        if time.Now().Add(cfgTimeAhead).After(reserveTime) {
            // commit
            cfgResp, err := m.rpc.WeddingConfCli.GetThemeCfg(ctx, &channel_wedding_conf.GetThemeCfgReq{
                ThemeId: order.ThemeId,
            })
            if err != nil {
                log.ErrorWithCtx(ctx, "Callback fail to GetThemeCfg. order:%+v, err:%v, uid:%v", order, err, uid)
                return 0, err
            }
            cfg := cfgResp.GetThemeCfg()
            if cfg == nil || cfg.ThemeId == 0 {
                log.ErrorWithCtx(ctx, "Callback fail to GetThemeCfg. order:%+v, err:%v, uid:%v", order, err, uid)
                return 0, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "主题不存在")
            }
            err = m.commit(ctx, orderId, cfg.ThemeName, cfg.ThemeId, order.BuyerUid, 1, order.WeddingPrice, time.Unix(outsideTime, 0))
            if err != nil {
                log.ErrorWithCtx(ctx, "Callback fail to commit. order%+v, err:%v, uid:%v", order, err, uid)
                return 0, err
            }
            log.InfoWithCtx(ctx, "Callback commit success. order:%+v, uid:%v, cfgTimeAhead:%v, reserveTime:%v", order, uid, cfgTimeAhead, reserveTime)
            op = UnifiedPayCallback.Op_COMMIT
        } else {
            log.DebugWithCtx(ctx, "Callback not the right time, reserveTime:%v, now:%v, order:%+v, uid:%v", reserveTime, time.Now(), order, uid)
            return op, nil
        }

    case store.WeddingStatusCancel, store.WeddingStatusRollback:
        needUpdateStatus := orderStatus != store.WeddingStatusRollback
        // rollback
        err = m.rollback(ctx, orderId, order.BuyerUid, []uint32{store.WeddingStatusCancel, store.WeddingStatusRollback}, time.Unix(outsideTime, 0), needUpdateStatus)
        if err != nil {
            log.ErrorWithCtx(ctx, "Callback fail to rollback. order:%+v, err:%v, uid:%v", order, err, uid)
            return 0, err
        }
        op = UnifiedPayCallback.Op_ROLLBACK
        log.InfoWithCtx(ctx, "Callback rollback success. order:%+v, uid:%v", order, uid)

    case store.WeddingStatusFreezing:
        createTime := time.Unix(order.CreateTs, 0)
        if time.Now().After(createTime.Add(time.Hour * 24 * time.Duration(m.bc.GetConfig().LongTimeNoReserveDay))) {
            err = m.st.BatchUpdateWeddingPlanStatus(ctx, []uint32{order.Id}, store.WeddingPlanStatusCancel)
            if err != nil {
                log.ErrorWithCtx(ctx, "Callback fail to BatchUpdateWeddingPlanStatus. order:%+v, err:%v, uid:%v", order, err, uid)
                return 0, err
            }
            log.DebugWithCtx(ctx, "Callback BatchUpdateWeddingPlanStatus success. order:%+v, err:%v, uid:%v", order, err, uid)
            // rollback
            err = m.rollback(ctx, orderId, order.BuyerUid, []uint32{store.WeddingStatusFreezing}, time.Unix(outsideTime, 0), true)
            if err != nil {
                log.ErrorWithCtx(ctx, "Callback fail to rollback. order:%+v, err:%v, uid:%v", order, err, uid)
                return 0, err
            }

            op = UnifiedPayCallback.Op_ROLLBACK
        } else {
            log.DebugWithCtx(ctx, "Callback not the right time. order:%+v, op:%v, uid:%v, createTime:%v", order, op, uid, createTime)
            return op, nil
        }

    default:
        log.ErrorWithCtx(ctx, "Callback fail to GetWeddingOrderById. order:%v, err:%v, uid:%v", order, err, uid)
        return 0, protocol.NewExactServerError(codes.OK, status.ErrChannelWeddingOrderNotExist)
    }
    log.InfoWithCtx(ctx, "Callback success. order:%+v, op:%v, uid:%v", order, op, uid)
    return op, nil
}

func (m *WeddingPlanMgr) ApplyEndWeddingRelation(c context0.Context, uid uint32) (*pb.ApplyEndWeddingRelationResponse, error) {
    out := &pb.ApplyEndWeddingRelationResponse{}
    marriage, err := m.GetUserMarriageInfo(c, uid)
    if err != nil {
        log.ErrorWithCtx(c, "ApplyEndWeddingRelation fail to GetMarriageRelationByUid. err:%v, uid:%v", uid, err)
        return out, err
    }
    if marriage == nil {
        log.ErrorWithCtx(c, "ApplyEndWeddingRelation fail to GetMarriageRelationByUid. err:%v, uid:%v", uid, err)
        return out, protocol.NewExactServerError(codes.OK, status.ErrChannelWeddingStatusNotMatch, relationAlreadyEnd)
    }

    plan, err := m.st.GetGoingPlan(c, uid)
    if err != nil {
        log.ErrorWithCtx(c, "ApplyEndWeddingRelation fail to GetGoingPlan. err:%v, uid:%v", uid, err)
        return out, err
    }
    if plan != nil && plan.ID != 0 {
        log.ErrorWithCtx(c, "ApplyEndWeddingRelation fail to GetGoingPlan. err:%v, uid:%v", uid, err)
        return out, protocol.NewExactServerError(codes.OK, status.ErrChannelWeddingPlanCommonError, "当前有未完成婚礼，暂不支持解除关系~")
    }

    isEffect, err := m.cache.AddDivideTimeout(c, uid)
    if err != nil {
        log.ErrorWithCtx(c, "ApplyEndWeddingRelation fail to AddDivideTimeout. err:%v, uid:%v, err:%v", uid, err)
        return out, err
    }
    out.EndRelationshipDeadline = time.Now().Unix() + int64(m.bc.GetConfig().AutoDivorceDay*86400)

    userInfo, err := m.rpc.AccountCli.GetUser(c, uid)
    if err != nil {
        log.ErrorWithCtx(c, "ApplyEndWeddingRelation fail to GetUser. err:%v, uid:%v", uid, err)
        return out, err
    }
    ext, _ := proto.Marshal(&imPB.SystemNotifyMsg{
        JumpUrl:          jumpWeddingBuyPage,
        HighlightContent: "预订婚礼",
    })

    if isEffect {
        _ = m.SendOfficialExtImMsg(c, marriage.GetPartnerUid(), marriage.GetUid(), uint32(imPB.IM_MSG_TYPE_SYSTEM_NOTIFY), 0, "你发起了结婚关系解除，若回心转意，可进入 预订婚礼 在期限时间内随时撤销解除哦~", ext)
        _ = m.SendOfficialExtImMsg(c, marriage.GetUid(), marriage.GetPartnerUid(), uint32(imPB.IM_MSG_TYPE_SYSTEM_NOTIFY), 0, fmt.Sprintf("%s发起了结婚关系解除，%d天后将完成解除，在此期间你们不能购买新的婚礼，并且Ta可随时撤销解除。", userInfo.GetNickname(), m.bc.GetConfig().AutoDivorceDay), nil)
    }

    return out, nil
}

func (m *WeddingPlanMgr) CancelEndWeddingRelation(c context0.Context, uid uint32) error {

    isEffect, err := m.cache.RemoveDivideTimeout(c, uid)
    if err != nil {
        log.ErrorWithCtx(c, "CancelEndWeddingRelation fail to RemoveDivideTimeout. err:%v, uid:%v", uid, err)
        return err
    }

    relationInfo, err := m.GetUserMarriageInfo(c, uid)
    if err != nil {
        log.ErrorWithCtx(c, "CancelEndWeddingRelation fail to GetMarriageRelationByUid. err:%v, uid:%v", uid, err)
        return err
    }

    if relationInfo == nil {
        log.ErrorWithCtx(c, "CancelEndWeddingRelation fail to GetMarriageRelationByUid. err:%v, uid:%v", uid, err)
        return protocol.NewExactServerError(codes.OK, status.ErrChannelWeddingStatusNotMatch, relationAlreadyEnd)
    }
    //ext, _ := proto.Marshal(&imPB.SystemNotifyMsg{
    //    JumpUrl:          jumpWeddingBuyPage,
    //    HighlightContent: "",
    //})
    userInfo, err := m.rpc.AccountCli.GetUser(c, uid)
    if err != nil {
        log.ErrorWithCtx(c, "CancelEndWeddingRelation fail to GetUser. err:%v, uid:%v", uid, err)
        return err
    }

    if isEffect {
        _ = m.SendOfficialExtImMsg(c, relationInfo.GetPartnerUid(), relationInfo.GetUid(), uint32(imPB.IM_MSG_TYPE_SYSTEM_NOTIFY), 0, "你已撤销了结婚关系解除", nil)
        _ = m.SendOfficialExtImMsg(c, relationInfo.GetUid(), relationInfo.GetPartnerUid(), uint32(imPB.IM_MSG_TYPE_SYSTEM_NOTIFY), 0, fmt.Sprintf("%s撤销了结婚关系解除", userInfo.GetNickname()), nil)
    }

    log.InfoWithCtx(c, "CancelEndWeddingRelation success. uid:%v", uid)
    return nil
}

func (m *WeddingPlanMgr) FixOrderStatus(c context.Context) {
    ctx, cancel := protocolgrpc.NewContextWithInfoTimeout(c, time.Minute*5)
    defer cancel()
    // 扫描未处理订单， 包含：冻结中，已预约
    orderList, err := m.st.GetOrderList(ctx, []uint32{store.WeddingStatusFreezing, store.WeddingStatusReserved, store.WeddingStatusCancel}, 1000)
    if err != nil {
        log.ErrorWithCtx(ctx, "FixOrderStatus fail to GetOrderList. err:%v", err)
        return
    }
    for _, order := range orderList {

        if order.WeddingPriceType == uint32(channel_wedding_logic.WeddingPriceType_WEDDING_PRICE_TYPE_RED_DIAMOND) {
            err := m.handleRedDiamondOrder(ctx, order)
            if err != nil {
                log.ErrorWithCtx(ctx, "FixOrderStatus fail to handleRedDiamondOrder. order:%+v, err:%v", order, err)
                continue
            }

            // t豆callback
        } else {
            op, err := m.Callback(ctx, order.BuyerUid, order.OrderId, CallbackSourceTimer)
            if err != nil {
                log.ErrorWithCtx(ctx, "FixOrderStatus fail to Callback. orderId:%v, err:%v", order.OrderId, err)
                continue
            }
            log.DebugWithCtx(ctx, "FixOrderStatus success. orderId:%v, op:%v", order.OrderId, op)
        }
    }
}

func (m *WeddingPlanMgr) handleRedDiamondOrder(ctx context.Context, order *store.WeddingOrder) error {
    // get wedding plan
    plan, err := m.st.GetWeddingPlanById(ctx, order.Id)
    if err != nil {
        log.ErrorWithCtx(ctx, "handleRedDiamondOrder fail to GetWeddingPlanById. orderId:%v, err:%v", order.OrderId, err)
        return err
    }
    // 不可能为空，为空说明数据异常，直接置为已取消
    if plan == nil {
        err = m.handleRedDiamondRefund(ctx, order)
        if err != nil {
            log.ErrorWithCtx(ctx, "handleRedDiamondOrder fail to handleRedDiamondRefund. order:%+v, err:%v", order, err)
            return err
        }
        log.InfoWithCtx(ctx, "handleRedDiamondOrder BatchUpdateWeddingPlanStatus success. order:%+v", order)
        return nil
    }

    // 正常情况下, 红钻订单没有cancel状态，在取消订单时，直接置为rollback状态
    if order.WeddingStatus == store.WeddingStatusFreezing &&
        time.Now().After(time.Unix(order.CreateTs, 0).Add(time.Hour*24*time.Duration(m.bc.GetConfig().LongTimeNoReserveDay))) {
        err = m.handleRedDiamondRefund(ctx, order)
        if err != nil {
            log.ErrorWithCtx(ctx, "handleRedDiamondOrder fail to handleRedDiamondRefund. order:%+v, err:%v", order, err)
            return err
        }
        log.InfoWithCtx(ctx, "handleRedDiamondOrder BatchUpdateWeddingPlanStatus rollback success. orderInfo:%+v", order)
        // 3、 预约时间到了,转为确认状态
    } else if order.WeddingStatus == store.WeddingStatusReserved {
        if plan.ReserveInfo == nil {
            log.ErrorWithCtx(ctx, "handleRedDiamondOrder 预约信息为空. orderId:%v, planInfo:%+v", order.OrderId, plan)
            return nil
        }
        if time.Now().Add(time.Hour * time.Duration(m.bc.GetConfig().ReserveConf.LimitChangeAheadTime)).After(time.Unix(int64(plan.ReserveInfo.StartTime), 0)) {
            _, err := m.st.UpdateOrderStatus(ctx, order.Id, []uint32{store.WeddingStatusReserved}, store.WeddingStatusConfirm)
            if err != nil {
                log.ErrorWithCtx(ctx, "handleRedDiamondOrder fail to UpdateOrderStatus. orderId:%v, err:%v", order.OrderId, err)
                return err
            }
            log.InfoWithCtx(ctx, "handleRedDiamondOrder commit red success. orderId:%v, orderInfo:%+v", order.OrderId, order)
            return err
        } else {
            log.DebugWithCtx(ctx, "handleRedDiamondOrder not the right time. orderId:%v, info:%+v", order.OrderId, order)
        }

    } else {
        log.DebugWithCtx(ctx, "handleRedDiamondOrder not the right time. orderId:%v, info:%+v", order.OrderId, order)
    }
    return nil
}

// handleRedDiamondRefund 回滚红钻，先更新订单状态，再更新婚礼计划状态，最后回滚红钻，防止被用户刷红钻
func (m *WeddingPlanMgr) handleRedDiamondRefund(ctx context.Context, order *store.WeddingOrder) error {
    _, err := m.st.UpdateOrderStatus(ctx, order.Id, []uint32{store.WeddingStatusFreezing, store.WeddingStatusRollback, store.WeddingStatusCancel}, store.WeddingStatusRollback)
    if err != nil {
        log.ErrorWithCtx(ctx, "handleRedDiamondOrder fail to UpdateOrderStatus. orderId:%v, err:%v", order.OrderId, err)
        return err
    }
    err = m.st.BatchUpdateWeddingPlanStatus(ctx, []uint32{order.Id}, store.WeddingPlanStatusCancel)
    if err != nil {
        log.ErrorWithCtx(ctx, "handleRedDiamondOrder fail to BatchUpdateWeddingPlanStatus. orderId:%v, err:%v", order.OrderId, err)
        return err
    }
    sErr := m.rpc.Currency.AddUserCurrency(ctx, order.BuyerUid, int32(order.WeddingPrice), order.OrderId+"rb", "购买婚礼回滚", uint32(currencyPB.ADD_CURRENCY_REASON_BUY_CHANNEL_WEDDING))
    if sErr != nil {
        // 不return 走下边更新订单状态
        if sErr.Code() == status.ErrGrowCurrencyAdded {
            log.DebugWithCtx(ctx, "handleRedDiamondOrder already finish AddUserCurrency. orderId:%s, info:%+v", order.OrderId, order)
        } else {
            log.ErrorWithCtx(ctx, "handleRedDiamondOrder fail to currency.AddUserCurrency. orderId:%s, info:%+v, err:%v", order.OrderId, order, sErr)
            return sErr
        }
    }
    _ = m.rpc.ApiClient.NotifyGrowInfoSync(ctx, order.BuyerUid)

    return nil
}

func (m *WeddingPlanMgr) GetWeddingOrderById(ctx context.Context, planId uint32) (*store.WeddingOrder, error) {
    return m.st.GetWeddingOrderByPlanId(ctx, planId)
}

func (m *WeddingPlanMgr) BatchGetWeddingOrderByPlanIdList(ctx context.Context, planIdList []uint32) ([]*store.WeddingOrder, error) {
    return m.st.BatchGetWeddingOrderByPlanIdList(ctx, planIdList)
}

func (m *WeddingPlanMgr) MarkAdminCancel(ctx context.Context, planId uint32) error {
    return m.st.MarkAdminCancel(ctx, planId)
}
