package main

import (
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/services/channel-recommend/channel-recommend-svr/model"
)

func main() {
	RedisConf := &config.RedisConfig{
		Host:         "*************",
		Port:         6379,
		Protocol:     "tcp",
		PingInterval: 300,
		PoolSize:     1,
	}

	redisClient := RedisConf.NewDefualtGoRedisClient()
	cache, err := model.NewCache(redisClient)
	if nil != err {
		println(err)
		return
	}

	lotteryInfo := &model.ChLotteryInfo{
		LotteryId: 1,
		ChannelId: 10091337,
		AwardCnt:  10,
		BeginTs:   1677230076,
		EndTs:     1677316475,
		GiftInfo: model.GiftInfo{
			GiftId:        963,
			GiftName:      "限时礼物测试2",
			GiftImg:       "https://ga-album-cdnqn.52tt.com/internal-yunying/857e-1850fbd384e.png",
			GiftPrice:     1000,
			GiftPriceType: 2,
		},
	}

	err = cache.AddChLotteryInfo(lotteryInfo)
	if err != nil {
		println(err)
	}

	return
}
