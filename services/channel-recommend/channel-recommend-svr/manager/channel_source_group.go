package manager

import (
	"context"
	"golang.52tt.com/pkg/log"
	channel_recommend_svr "golang.52tt.com/protocol/services/channel-recommend-svr"
	"golang.52tt.com/services/channel-recommend/channel-recommend-svr/model"
	"sort"
	"time"
)

func (m *ChannelRecommendManager) AddChannelGroupResource(ctx context.Context, req *channel_recommend_svr.AddChannelGroupResourceReq) (*channel_recommend_svr.AddChannelGroupResourceResp, error) {

	err := m.clientPool.Store.AddChannelGroupResource(&model.ChannelGroupResource{
		Id:            req.GetInfo().GetId(),
		Name:          req.GetInfo().GetName(),
		SubName:       req.GetInfo().GetSubName(),
		Background:    req.GetInfo().GetBackground(),
		JumpUrl:       req.GetInfo().GetJumpUrl(),
		Rank:          req.GetInfo().GetRank(),
		BeginTs:       req.GetInfo().GetBeginTs(),
		UpdateTs:      uint32(time.Now().Unix()),
		Status:        req.GetInfo().GetStatus(),
		ChannelIdList: req.GetInfo().GetChannelIdList(),
		EndTs:         req.GetInfo().GetEndTs(),
		UrlType:       req.GetInfo().GetUrlType(),
	})

	if err != nil {
		log.ErrorWithCtx(ctx, "AddChannelGroupResource err , req %v , err %v", req, err.Error())
	}

	return &channel_recommend_svr.AddChannelGroupResourceResp{}, err
}

func (m *ChannelRecommendManager) UpdateChannelGroupResource(ctx context.Context, req *channel_recommend_svr.UpdateChannelGroupResourceReq) (*channel_recommend_svr.UpdateChannelGroupResourceResp, error) {

	err := m.clientPool.Store.UpdateChannelGroupResource(req.GetInfo().GetId(), &model.ChannelGroupResource{
		Id:            req.GetInfo().GetId(),
		Name:          req.GetInfo().GetName(),
		SubName:       req.GetInfo().GetSubName(),
		Background:    req.GetInfo().GetBackground(),
		JumpUrl:       req.GetInfo().GetJumpUrl(),
		Rank:          req.GetInfo().GetRank(),
		BeginTs:       req.GetInfo().GetBeginTs(),
		UpdateTs:      uint32(time.Now().Unix()),
		Status:        req.GetInfo().GetStatus(),
		ChannelIdList: req.GetInfo().GetChannelIdList(),
		EndTs:         req.GetInfo().GetEndTs(),
		UrlType:       req.GetInfo().GetUrlType(),
	})

	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateChannelGroupResource err , req %v , err %v", req, err.Error())
	}

	return &channel_recommend_svr.UpdateChannelGroupResourceResp{}, err
}

func (m *ChannelRecommendManager) DelChannelGroupResource(ctx context.Context, req *channel_recommend_svr.DelChannelGroupResourceReq) (*channel_recommend_svr.DelChannelGroupResourceResp, error) {

	err := m.clientPool.Store.DelChannelGroupResource(req.GetId())

	if err != nil {
		log.ErrorWithCtx(ctx, "DelChannelGroupResource err , req %v , err %v", req, err.Error())
	}

	return &channel_recommend_svr.DelChannelGroupResourceResp{}, err
}

func (m *ChannelRecommendManager) GetAllChannelGroupResource(ctx context.Context, req *channel_recommend_svr.GetChannelGroupResourceListReq) (*channel_recommend_svr.GetChannelGroupResourceListResp, error) {
	resp := &channel_recommend_svr.GetChannelGroupResourceListResp{}
	resp.List = make([]*channel_recommend_svr.ChannelGroupResource, 0)

	list, err := m.clientPool.Store.GetAllChannelGroupResource(req.GetPage(), req.GetPageSize(), req.GetId(), req.GetName())

	if err != nil {
		log.ErrorWithCtx(ctx, "GetAllChannelGroupResource err , req %v , err %v", req, err.Error())
		return resp, err
	}

	cnt, err := m.clientPool.Store.GetChannelGroupResourceCount(req.GetId(), req.GetName())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelGroupResourceCount err , req %v , err %v", req, err.Error())
		return resp, err
	}

	for _, item := range list {
		status := getGroupStatus(item)
		if req.GetStatus() != 0 && status != req.GetStatus() {
			cnt -= 1
			continue
		}

		resp.List = append(resp.List, &channel_recommend_svr.ChannelGroupResource{
			Id:            item.Id,
			Name:          item.Name,
			SubName:       item.SubName,
			Background:    item.Background,
			JumpUrl:       item.JumpUrl,
			Rank:          item.Rank,
			BeginTs:       item.BeginTs,
			UpdateTs:      item.UpdateTs,
			Status:        status,
			ChannelIdList: item.ChannelIdList,
			EndTs:         item.EndTs,
			UrlType:       item.UrlType,
		})
	}

	//按id降序排列

	sort.Slice(resp.List, func(i, j int) bool {
		return resp.List[i].Id > resp.List[j].Id
	})

	resp.TotalCnt = cnt

	return resp, err
}

// CheckChannelGroupResource 检查房间组配置有效性
func (m *ChannelRecommendManager) CheckChannelGroupResource(ctx context.Context, req *channel_recommend_svr.CheckChannelGroupResourceReq) (*channel_recommend_svr.CheckChannelGroupResourceResp, error) {
	resp := &channel_recommend_svr.CheckChannelGroupResourceResp{}

	ok, err := m.clientPool.Store.CheckChannelGroupResourceValid(&model.ChannelGroupResource{
		Id:            req.GetInfo().GetId(),
		Name:          req.GetInfo().GetName(),
		SubName:       req.GetInfo().GetSubName(),
		Background:    req.GetInfo().GetBackground(),
		JumpUrl:       req.GetInfo().GetJumpUrl(),
		Rank:          req.GetInfo().GetRank(),
		BeginTs:       req.GetInfo().GetBeginTs(),
		UpdateTs:      uint32(time.Now().Unix()),
		Status:        req.GetInfo().GetStatus(),
		ChannelIdList: req.GetInfo().GetChannelIdList(),
		EndTs:         req.GetInfo().GetEndTs(),
		UrlType:       req.GetInfo().GetUrlType(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckChannelGroupResource err , req %v , err %v", req, err.Error())
		return resp, err
	}

	resp.IsValid = ok
	return resp, nil

}

// 状态  1未生效 2生效中 3已过期
func getGroupStatus(info model.ChannelGroupResource) uint32 {
	now := uint32(time.Now().Unix())
	if info.BeginTs > now {
		return 1
	}
	if info.EndTs < now {
		return 3
	}
	return 2
}
