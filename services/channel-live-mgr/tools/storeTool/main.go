package main

import (
	"context"
	"fmt"
	"github.com/jmoiron/sqlx"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/services/channel-live-mgr/mysql"
)

func main() {
	conf := &config.MysqlConfig{
		Host:     "*************",
		Port:     3306,
		Database: "appsvr",
		UserName: "godman",
		Password: "thegodofman",
		Charset:  "utf8mb4",
	}

	mysqlDb, err := sqlx.Connect("mysql", conf.ConnectionString())
	readonlyMysqlDb, err := sqlx.Connect("mysql", conf.ConnectionString())

	mysqlStore := mysql.NewMysql(mysqlDb, readonlyMysqlDb)

	list, err := mysqlStore.GetVirtualAnchorPerList(context.Background(), 0, 0, 0, 0, 10, false, false)
	totalCnt, err := mysqlStore.GetVirtualAnchorPerTotalCnt(context.Background(), 0, 0)

	fmt.Println(list)
	fmt.Println(err)
	fmt.Println(totalCnt)
}
