package missionhelper

import (
	"context"
	"golang.52tt.com/clients/exp"
	"golang.52tt.com/clients/medalgo"
	"golang.52tt.com/clients/numeric"
	numeric_go "golang.52tt.com/clients/numeric-go"
	"golang.52tt.com/pkg/log"
	ga "golang.52tt.com/protocol/app"
	pbMedalGo "golang.52tt.com/protocol/services/medalgo"
	numericpb "golang.52tt.com/protocol/services/numericsvr"
	"google.golang.org/grpc"
)

type MissionHelper struct {
	expCli       exp.IClient
	medalGoCli   medalgo.IClient
	numericGoCli numeric_go.IClient
	numericCli   numeric.IClient
}

func NewMissionHelper(dopts ...grpc.DialOption) *MissionHelper {
	expCli := exp.NewClient(dopts...)
	medalCli, _ := medalgo.NewClient(dopts...)
	numericGo, _ := numeric_go.NewClient(dopts...)
	numericCli := numeric.NewClient(dopts...)
	return &MissionHelper{
		expCli:       expCli,
		medalGoCli:   medalCli,
		numericGoCli: numericGo,
		numericCli:   numericCli,
	}
}

func (help *MissionHelper) GetUserGrowInfo(ctx context.Context, uid uint32) (resp *ga.GrowInfo, err error) {
	resp = &ga.GrowInfo{}
	_, level, err := help.expCli.GetUserExp(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "MissionHelper.GetUserGrowInfo GetUserExp failed,err:%v", err)
		return resp, err
	}
	medalResp, err := help.medalGoCli.GetUserMedalListWhitTaillight(ctx, pbMedalGo.GetUserMedalListWhitTaillightReq{Uid: uid})
	if err != nil {
		log.ErrorWithCtx(ctx, "MissionHelper.GetUserGrowInfo GetUserMedalListWhitTaillight failed,err:%v", err)
		return resp, err
	}
	resp.Level = level
	if medalResp != nil {
		for _, v := range medalResp.UserMedalList {
			resp.MedalList = append(resp.MedalList, v.MedalId)
		}
		for _, v := range medalResp.TaillightMedalList {
			resp.TaillightMedalList = append(resp.TaillightMedalList, v.MedalId)
		}
	}
	resp.Uid = uid
	return resp, nil
}

func (help *MissionHelper) GrowInfoFillRichAndCharm(ctx context.Context, uid uint32, info *ga.GrowInfo) error {
	if resp, err := help.numericGoCli.GetPersonalNumericV2(ctx, uid); err != nil {
		log.ErrorWithCtx(ctx, "GetPersonalNumericV2 %d err %+v", uid, err)
		return err
	} else {
		info.RichLevel = uint32(resp.Rich / 1000)
		info.CharmLevel = uint32(resp.Charm / 1000)
	}

	if resp, err := help.numericCli.GetPersonalRankingFromLocalCache(ctx, &numericpb.GetPersonalRankingReq{}); err != nil {
		log.ErrorWithCtx(ctx, "GetPersonalRankingFromLocalCache %d err %+v", uid, err)
		return err
	} else {
		info.RichCharmRank = &ga.RichAndCharmTopRank{}

		if resp.GetDayCharmRanking() > 0 && resp.GetDayCharmRanking() <= 50 {
			info.RichCharmRank.CharmDayTopRank = resp.GetDayCharmRanking()
		}

		if resp.GetDayRichRanking() > 0 && resp.GetDayRichRanking() <= 50 {
			info.RichCharmRank.RichDayTopRank = resp.GetDayRichRanking()
		}

		if resp.GetWeekCharmRanking() > 0 && resp.GetWeekCharmRanking() <= 50 {
			info.RichCharmRank.CharmWeekTopRank = resp.GetWeekCharmRanking()
		}

		if resp.GetWeekRichRanking() > 0 && resp.GetWeekRichRanking() <= 50 {
			info.RichCharmRank.RichWeekTopRank = resp.GetWeekRichRanking()
		}
	}

	return nil
}
