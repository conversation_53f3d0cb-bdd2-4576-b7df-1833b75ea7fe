package store

import (
	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
	"time"
	"fmt"
	"context"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
)

const (
	firstRechargeLogTblName = "first_recharge_log_%s"
)

var createFirstRechargeLogTbl = `CREATE TABLE IF NOT EXISTS %s (
    id int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id 自增',
    uid int(10) unsigned NOT NULL COMMENT 'uid',
    order_no varchar(128) DEFAULT NULL COMMENT '充值订单号',
    recharge_price DOUBLE NOT NULL COMMENT '充值金额',
    event_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    award_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,

    PRIMARY KEY (id),
    KEY idx_event_time(event_time)
)engine=InnoDB default charset=utf8 COMMENT "新首充记录月表";`

// GenFirstRechargeLogTblName 生成新首充记录月表名
func GenFirstRechargeLogTblName(eventTime time.Time) string {
	return fmt.Sprintf(firstRechargeLogTblName, eventTime.Format("200601"))
}

// CreateFirstRechargeLogTblTable 新首充记录月表
func (s *Store) CreateFirstRechargeLogTblTable(ctx context.Context, tx mysql.Txx, eventTime time.Time) error {
	_, err := tx.ExecContext(ctx, fmt.Sprintf(createFirstRechargeLogTbl, GenFirstRechargeLogTblName(eventTime)))
	if err != nil {
		log.ErrorWithCtx(ctx, "CreateRiskRejectTable fail. err %v", err)
		return err
	}

	return nil
}

type FirstRechargeLog struct {
	ID            uint32    `db:"id"`
	Uid           uint32    `db:"uid"`
	OrderNo       string    `db:"order_no"`
	RechargePrice float64   `db:"recharge_price"`
	EventTime     time.Time `db:"event_time"`
	AwardTime     time.Time `db:"award_time"`
}

// InsertFirstRechargeLog 插入新首充记录 with tx
func (s *Store) InsertFirstRechargeLog(ctx context.Context, tx mysql.Txx, firstRechargeLog *FirstRechargeLog) error {
	query := `INSERT INTO %s (uid, order_no, recharge_price, event_time, award_time) VALUES (?, ?, ?, ?, ?)`
	_, err := tx.ExecContext(ctx, fmt.Sprintf(query, GenFirstRechargeLogTblName(firstRechargeLog.EventTime)),
		firstRechargeLog.Uid, firstRechargeLog.OrderNo, firstRechargeLog.RechargePrice, firstRechargeLog.EventTime, firstRechargeLog.AwardTime)
	if err != nil {
		// 如果表不存在，建表并重试
		if mysql.IsMySQLError(err, 1146) {
			if err := s.CreateFirstRechargeLogTblTable(ctx, tx, firstRechargeLog.EventTime); err != nil {
				log.ErrorWithCtx(ctx, "CreateFirstRechargeLogTblTable fail. err %v", err)
				return err
			}

			// 重试
			_, err = tx.ExecContext(ctx, fmt.Sprintf(query, GenFirstRechargeLogTblName(firstRechargeLog.EventTime)),
				firstRechargeLog.Uid, firstRechargeLog.OrderNo, firstRechargeLog.RechargePrice, firstRechargeLog.EventTime, firstRechargeLog.AwardTime)
			if err != nil {
				log.ErrorWithCtx(ctx, "InsertFirstRechargeLog fail. err %v", err)
				return err
			}
		}

		log.ErrorWithCtx(ctx, "InsertFirstRechargeLog fail. err %v", err)
		return err
	}
	return nil
}
