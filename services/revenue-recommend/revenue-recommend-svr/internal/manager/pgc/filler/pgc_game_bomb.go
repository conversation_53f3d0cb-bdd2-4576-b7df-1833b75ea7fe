package filler

import (
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/protocol/app/channel"
	"golang.52tt.com/protocol/app/revenue-recommend-logic"
	"golang.52tt.com/services/revenue-recommend/revenue-recommend-svr/internal/client"
	"golang.52tt.com/services/revenue-recommend/revenue-recommend-svr/internal/config"
)

func (f *Filler) getChannelGameBombInfo() {
	cidList := make([]uint32, 0)
	for _, cid := range f.material.mapType2CidList[revenue_recommend_logic.RevenueRecInfo_E_REVENUE_REC_TYPE_PGC] {
		if f.inParam.MapCid2RecInfo[cid].GetTagId() == 2017 {
			cidList = append(cidList, cid)
		}
	}
	if len(cidList) == 0 {
		log.DebugWithCtx(f.inParam.Ctx, "getChannelGameBombInfo no cid f.param:%v", f.inParam)
		return
	}

	f.material.mapCid2GameBombStatus = client.CliInstance.BatGetChannelGameBombInfo(f.inParam.Ctx, f.inParam.OpUid, cidList)
}

func (f *Filler) fillChannelGameBombInfo() {
	for _, rec := range f.outParam.recChannels {
		cid := rec.GetChannelBaseInfo().GetChannelId()
		if f.inParam.MapCid2RecInfo[cid].GetTagId() == 2017 {
			status := f.material.mapCid2GameBombStatus[cid]
			detail := config.DyConfInstance.GetChannelCertInfo(uint32(channel.RealTimeChannelDetail_ENUM_REAL_TIME_DETAIL_TYPE_THROW_BOMB), tranBombStatus(status), &config.ChannelCertExtInfo{
				MainUserSex:      f.GetMainUserSex(cid),
				MicFemaleCount:   f.GetChannelFemaleCnt(cid),
				MicMaleCount:     f.GetChannelMaleCnt(cid),
				RelationshipName: "",
			})
			if detail != nil {
				// 三种休闲玩玩法不要重复
				hasPlay := false
				for i, cDetail := range rec.GetRealTimeChannelDetail() {
					if cDetail.GetDetailType() == uint32(channel.RealTimeChannelDetail_ENUM_REAL_TIME_DETAIL_TYPE_GAME_ADVENTURE) ||
						cDetail.GetDetailType() == uint32(channel.RealTimeChannelDetail_ENUM_REAL_TIME_DETAIL_TYPE_THROW_BOMB) ||
						cDetail.GetDetailType() == uint32(channel.RealTimeChannelDetail_ENUM_REAL_TIME_DETAIL_TYPE_DIGITAL_BOMB) {
						// 已经有了，看下自己如果是进行中就覆盖，否则就不动
						if status != 0 {
							rec.RealTimeChannelDetail[i] = detail
						}
						hasPlay = true
					}
				}

				if !hasPlay {
					rec.RealTimeChannelDetail = append(rec.RealTimeChannelDetail, detail)
				}
			}
		}
	}
}

func tranBombStatus(status uint32) uint32 {
	if status >= 1 {
		// 进行中
		return 1
	} else {
		// 未开始
		return 0
	}
}
