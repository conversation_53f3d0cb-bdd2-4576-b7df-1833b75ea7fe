package server

import (
	"context"
	"crypto/rand"
	"encoding/json"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	presentPB_ "golang.52tt.com/protocol/app/userpresent"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/present-middleware"
	presentPB "golang.52tt.com/protocol/services/userpresent"
	"golang.52tt.com/services/present-middleware/conf"
	"math/big"
	"time"
)

func (s *FellowPresentMiddlewareMgr) BeforeSendPresent(ctx context.Context, inter interface{}, outInter interface{}, sendExtend *baseSendExtend) (err error) {
	// 类型断言
	out, ok := outInter.(*pb.FellowSendPresentResp)
	if !ok {
		log.ErrorWithCtx(ctx, "BeforeSendPresent -- para inter need *FellowSendPresentResp type. ")
		return nil
	}
	in, ok := inter.(*pb.FellowSendPresentReq)
	if !ok {
		log.ErrorWithCtx(ctx, "BeforeSendPresent -- para inter need *FellowSendPresentResp type. ")
		return nil
	}

	if in.ServiceInfo == nil {
		in.ServiceInfo = &pb.ServiceCtrlInfo{}
	}
	sendExtend.nowTs = time.Now()
	out.ItemSource = in.ItemSource

	ctx = s.SetPresentGoCtx(ctx, in.GetSendUid())

	// 预处理，获得送礼要用的相关信息
	TargetUidList := []uint32{in.TargetUid}

	// 先检查是不是需要ban掉的礼物id
	if s.GeneralConfig.GetPresentConfig().BanItemMap != nil {
		if source, ok := s.GeneralConfig.GetPresentConfig().BanItemMap[in.GetItemId()]; ok {
			if source == conf.BanItemTypeFellow || source == conf.BanItemTypeAll {
				return protocol.NewExactServerError(nil, status.ErrUserPresentConfigNotExist)
			}
		}
	}

	//send_method 控制orderid生成，这里用传入的id，就不用管了
	err = s.preSendPresent(ctx, sendExtend.nowTs, in.SendUid,
		TargetUidList, in.ItemId, in.ChannelId, uint32(pb.PresentSendMethodType_PRESENT_TYPE_FELLOW), sendExtend)
	if err != nil {
		log.ErrorWithCtx(ctx, "PresentMiddlewareMgr -- preSendPresent fail. uid:%v c:%v err:%v",
			in.SendUid, 0, err)
		return err
	}

	// 如果传入了订单id，重新整理下订单，不要用通用方法里的
	if in.GetOrderId() != "" {
		sendExtend.orderMap = make(map[string]*PayOrderInfo)
		sendExtend.orderMap[in.GetOrderId()] = &PayOrderInfo{
			targetUid: in.GetTargetUid(),
			orderId:   in.GetOrderId(),
			dealToken: in.GetDealToken(),
		}
		sendExtend.uniqOrderId = in.GetOrderId()
	}

	return err
}

func (s *FellowPresentMiddlewareMgr) SendingPresent(ctx context.Context, inter interface{}, outInter interface{}, sendExtend *baseSendExtend) (err error) {

	// 类型断言
	_, ok := outInter.(*pb.FellowSendPresentResp)
	if !ok {
		log.ErrorWithCtx(ctx, "SendingPresent -- para inter need *FellowSendPresentResp type. ")
		return nil
	}
	in, ok := inter.(*pb.FellowSendPresentReq)
	if !ok {
		log.ErrorWithCtx(ctx, "SendingPresent -- para inter need *FellowSendPresentReq type. ")
		return nil
	}

	sendExtend.extendInfo = &ExtendInfo{targetInfoMap: map[uint32]*TargetExtendInfo{}}
	if in.OrderId == "" {
		//冻结红钻/t豆
		sendExtend.remainTbeans, sendExtend.remainSource, sendExtend.realItemSource, err = s.tryFreeze(ctx, sendExtend, in.Count, in.ItemSource, in.SourceId, uint32(pb.PresentSendMethodType_PRESENT_TYPE_FELLOW))
		if err != nil {
			log.ErrorWithCtx(ctx, "SendingPresent -- tryFreeze fail. uid:%v err:%v",
				in.SendUid, err)
			return err
		}
		sendExtend.extendInfo = &ExtendInfo{targetInfoMap: map[uint32]*TargetExtendInfo{}}
	} else {
		for _, item := range sendExtend.orderMap {
			if item.dealToken == "" {
				continue
			}
			dealToken, err := UpdateDealTokenInfo(ctx, item.dealToken, item.orderId, false)
			if err != nil {
				log.ErrorWithCtx(ctx, "SendPresent UpdateDealTokenInfo fail , err %v , uid %d", err, sendExtend.sendUser.GetUid())
				// deal_token处理失败，暂时不返回错误，防止送礼失败;deal_token相关链路稳定后可以正常返回错误
				//continue
			}
			item.dealToken = dealToken
		}
	}

	sendExtend.nowTs, err = time.ParseInLocation("2006-01-02 15:04:05", in.Ctime, time.Local)
	if err != nil {
		log.InfoWithCtx(ctx, "SendingPresent -- time.Parse fail. uid:%v ctime:%v err:%v",
			in.SendUid, in.Ctime, err)
		sendExtend.nowTs = time.Now()
	}

	ctx = context.WithValue(ctx, IsRecordSenderRichKey, s.isRecordRichMap(ctx, sendExtend.sendUser.GetUid(), sendExtend.targetUserMap))

	//赠送礼物
	sendExtend.sucOrders, err = s.sendPresent(ctx, sendExtend, in.ServiceInfo, in.Count, in.AppId, in.MarketId, uint32(pb.PresentSourceType_PRESENT_SOURCE_FELLOW), in.SendSource, 0, uint32(pb.PresentSendMethodType_PRESENT_TYPE_FELLOW), in.IsOptValid)

	if err != nil {
		log.ErrorWithCtx(ctx, "SendingPresent -- __SendPresent fail. uid:%v ret:%v req:%v",
			in.SendUid, err, in)
		return err
	}
	log.InfoWithCtx(ctx, "after send present extend info : %v", sendExtend.extendInfo)

	if in.OrderId == "" {
		//提交
		_ = s.commitPayOrder(ctx, sendExtend.sucOrders, uint32(pb.PresentSendMethodType_PRESENT_TYPE_FELLOW), in.ItemSource, sendExtend.presentConfig.GetItemConfig().GetPriceType())
	}

	//礼物周边信息处理
	err = s.ProcPresentWatch(ctx, sendExtend, in.Count, true)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendingPresent -- __ProcPresentWatch fail:%v. _req:%v", err, in)
	}

	return err
}

func (s *FellowPresentMiddlewareMgr) AfterSendPresent(ctx context.Context, inter interface{}, outInter interface{}, sendExtend *baseSendExtend) (err error) {

	// 类型断言
	out, ok := outInter.(*pb.FellowSendPresentResp)
	if !ok {
		log.ErrorWithCtx(ctx, "AfterSendPresent -- para inter need *SendPresentResp type. ")
		return nil
	}
	in, ok := inter.(*pb.FellowSendPresentReq)
	if !ok {
		log.ErrorWithCtx(ctx, "AfterSendPresent -- para inter need *SendPresentReq type. ")
		return nil
	}

	//【防小白骚扰】24小时内单笔送出/收到1000元礼物的用户 存redis
	consumeMap := make(map[uint32]uint32, 0)
	for _, order := range sendExtend.sucOrders {
		consumeMap[order.targetUid] = sendExtend.presentConfig.GetItemConfig().GetPrice()
	}

	err = s.SetHighConsume(ctx, consumeMap, in.SendUid)
	if err != nil {
		log.ErrorWithCtx(ctx, "AfterSendPresent -- SetHighConsume err, uid %d ,  err : %v. ", in.SendUid, err)
		return nil
	}

	ctx = context.WithValue(ctx, IsRecordSenderRichKey, s.isRecordRichMap(ctx, sendExtend.sendUser.GetUid(), sendExtend.targetUserMap))

	//送礼后处理，填充动效、推送等
	s.FillRespAndPush(ctx, in, out, sendExtend)
	return err
}

func (s *FellowPresentMiddlewareMgr) FillRespAndPush(ctx context.Context, in *pb.FellowSendPresentReq, out *pb.FellowSendPresentResp, sendExtend *baseSendExtend) {

	presentConfig := sendExtend.presentConfig
	sendUser := sendExtend.sendUser
	//sucUsers := sendExtend.sucUsers
	extendInfo := sendExtend.extendInfo
	nowTs := sendExtend.nowTs

	out.CurTbeans = uint64(sendExtend.remainTbeans)
	out.SourceRemain = sendExtend.remainSource

	//送礼特效与动效
	showEffect, showEffectV2, FlowId := s.getPresentEffect(ctx, presentConfig.GetItemConfig(), in.Count)
	templateId, _ := s.getPresentDynamicEffectTemplate(ctx, sendUser.GetUid(), presentConfig.GetItemConfig(), in.Count)

	// 初始化下out
	out.MsgInfo = &pb.PresentSendMsg{ItemInfo: &pb.PresentSendItemInfo{}}

	out.MsgInfo.ItemInfo.ItemId = presentConfig.GetItemConfig().GetItemId()
	out.MsgInfo.ItemInfo.Count = in.Count
	out.MsgInfo.ItemInfo.ShowEffect = showEffect
	out.MsgInfo.ItemInfo.ShowEffectV2 = showEffectV2
	out.MsgInfo.ItemInfo.FlowId = FlowId
	out.MsgInfo.ItemInfo.IsBatch = true
	out.MsgInfo.ItemInfo.SendType = in.SendType
	out.MsgInfo.ItemInfo.ShowBatchEffect = presentConfig.GetItemConfig().GetPriceType() != uint32(presentPB.PresentPriceType_PRESENT_PRICE_TBEAN)
	out.MsgInfo.ItemInfo.DynamicTemplateId = templateId
	if presentConfig.GetItemConfig().GetPriceType() != uint32(presentPB.PresentPriceType_PRESENT_PRICE_TBEAN) &&
		presentConfig.GetItemConfig().GetExtend().GetShowEffect() == 1 {
		out.MsgInfo.ItemInfo.ShowBatchEffect = presentConfig.GetItemConfig().GetPrice() < 10000
	}

	out.CurTbeans = uint64(sendExtend.remainTbeans)

	// 涂鸦送礼，im用不着，先放着

	log.DebugWithCtx(ctx, "extend info : %v", extendInfo)
	out.MsgInfo.ChannelId = 0
	out.MsgInfo.SendTime = uint64(nowTs.UnixNano() / 1000000)
	out.MsgInfo.SendUid = sendUser.GetUid()
	out.MsgInfo.SendNickname = sendUser.GetNickname()
	out.MsgInfo.SendAccount = sendUser.GetUsername()

	out.MsgInfo.ExtendJson = extendInfo.userExtendJson

	if presentConfig.GetItemConfig().GetExtend().FlowId > 0 {
		out.MsgInfo.ItemInfo.DynamicTemplateId = 0
	}

	if in.GetChannelId() != 0 {
		s.procPushEvent(ctx, sendExtend, in, out)
	}
}

func (s *FellowPresentMiddlewareMgr) procPushEvent(ctx context.Context, sendExtend *baseSendExtend, in *pb.FellowSendPresentReq, out *pb.FellowSendPresentResp) {
	bIsNoPush := false

	itemCfg := sendExtend.presentConfig
	sendUser := sendExtend.sendUser
	channelId := sendExtend.channelSimpleInfo.GetChannelSimple().GetChannelId()
	extendInfo := sendExtend.extendInfo
	sendItemInfo := out.GetMsgInfo().GetItemInfo()
	totalItemCount := in.GetCount()
	timeVal := time.Now()
	channelInfo := sendExtend.channelSimpleInfo.GetChannelSimple()

	//房间人数较多，则丢一部分推送
	if uint32(presentPB.PresentPriceType_PRESENT_PRICE_RED_DIAMOND) == itemCfg.GetItemConfig().GetPriceType() && channelId != 0 {
		tmpCtx, cancel := context.WithTimeout(ctx, time.Millisecond*100)
		defer cancel()
		size, err := s.channelolCli.GetChannelMemberSize(tmpCtx, sendUser.GetUid(), channelId)
		if err != nil {
			log.ErrorWithCtx(ctx, "PresentBatchSendService::procPushEvent GetChannelMemberSize failed. err:%v, uid:%v", err, sendUser.Uid)
		}
		dropRadio := (size - 1000) / 30
		if dropRadio == 0 {
			dropRadio = 1
		}
		if dropRadio > 90 {
			dropRadio = 90
		}
		roll, _ := rand.Int(rand.Reader, big.NewInt(100))
		if size > 1000 && uint32(roll.Int64()) > dropRadio {
			bIsNoPush = true
		}
	}

	targetIndex := 0

	messageItemInfo := presentPB_.PresentSendItemInfo{
		ItemId:            sendItemInfo.ItemId,
		Count:             sendItemInfo.Count,
		ShowBatchEffect:   sendItemInfo.ShowBatchEffect,
		ShowEffect:        sendItemInfo.ShowEffect,
		ShowEffectV2:      sendItemInfo.ShowEffectV2,
		FlowId:            sendItemInfo.FlowId,
		SendType:          sendItemInfo.SendType,
		DynamicTemplateId: sendItemInfo.DynamicTemplateId,
		IsVisibleToSender: true,
	}
	drawPic := &presentPB_.DrawPresentPicture{LineList: []*presentPB_.PresentLine{}}
	if sendItemInfo.DrawPresentPic != nil {

		for _, line := range sendItemInfo.DrawPresentPic.LineList {
			drawLine := &presentPB_.PresentLine{}
			for _, point := range line.PointList {
				drawLine.PointList = append(drawLine.PointList, &presentPB_.PresentPoint{X: point.X, Y: point.Y})
			}
			drawLine.ItemId = line.ItemId
			drawPic.LineList = append(drawPic.LineList, drawLine)
		}
	}

	messageItemInfo.DrawPresentPic = drawPic

	log.InfoWithCtx(ctx, "procPushEvent -- targetUids. uid:%v, targetUids:[%v] , extendInfo.userExtendJson:[%v]", sendUser.Uid, sendExtend.orderMap, extendInfo.userExtendJson)

	for _, order := range sendExtend.sucOrders {

		userExtendJson := ""

		user := sendExtend.targetUserMap[order.targetUid]

		out.MsgInfo.TargetUid = user.GetUid()
		out.MsgInfo.TargetAccount = user.GetUsername()
		out.MsgInfo.TargetNickname = user.GetNickname()

		if _, ok := sendExtend.ukwInfoMap[out.MsgInfo.GetTargetUid()]; ok && isPgc(sendExtend.channelSimpleInfo.GetChannelSimple().GetChannelType()) {
			out.MsgInfo.TargetAccount = sendExtend.ukwInfoMap[out.MsgInfo.GetTargetUid()].GetPrivilege().GetAccount()
			out.MsgInfo.TargetNickname = sendExtend.ukwInfoMap[out.MsgInfo.GetTargetUid()].GetPrivilege().GetNickname()
			out.MsgInfo.TargetUid = sendExtend.ukwInfoMap[out.MsgInfo.GetTargetUid()].GetUid()
		}

		if _, ok := sendExtend.ukwInfoMap[out.MsgInfo.GetSendUid()]; ok && isPgc(sendExtend.channelSimpleInfo.GetChannelSimple().GetChannelType()) {
			out.MsgInfo.TargetAccount = sendExtend.ukwInfoMap[out.MsgInfo.GetSendUid()].GetPrivilege().GetAccount()
			out.MsgInfo.TargetNickname = sendExtend.ukwInfoMap[out.MsgInfo.GetSendUid()].GetPrivilege().GetNickname()
			out.MsgInfo.TargetUid = sendExtend.ukwInfoMap[out.MsgInfo.GetSendUid()].GetUid()
		}

		out.MsgInfo.FromUserProfile = genUserProfile(sendExtend.ukwInfoMap[out.MsgInfo.GetSendUid()])
		out.MsgInfo.TargetUserProfile = genUserProfile(sendExtend.ukwInfoMap[out.MsgInfo.GetTargetUid()])

		extend, ok := extendInfo.targetInfoMap[user.GetUid()]
		if ok {
			userExtendJson = extend.userExtendJson
		}

		targetIndex++

		var richValue int32 = 0
		if s.isRecordSenderRich(ctx, sendUser.GetUid(), itemCfg.GetItemConfig().GetPriceType()) {
			richValue = int32(extendInfo.baseRichValue)
		}

		if !bIsNoPush {
			_ = s.pushNotificationToUser(ctx, sendUser, itemCfg, totalItemCount, timeVal, channelId, &messageItemInfo, user, userExtendJson, user.GetUid(), sendExtend)
			//这一条是新礼物中间服务才带的，用于向送礼者推送一条展示财富值的推送
			jsonSendUser := jsonSendUser{RichValue: richValue, RichValuePrefix: "", MemberContributionPrefix: "", BonusMsg: extendInfo.richValueBonusMsg}
			userExtendJson, _ := json.Marshal(jsonSendUser)
			_ = s.pushNotificationToUser(ctx, sendUser, itemCfg, totalItemCount, timeVal, channelId, &messageItemInfo, user, string(userExtendJson), sendUser.GetUid(), sendExtend)
		}
		log.InfoWithCtx(ctx, "pushNotificationToChannel %s", userExtendJson)
		if !bIsNoPush && channelId != 0 {
			_ = s.pushNotificationToChannel(ctx, sendUser, user, timeVal, channelId, &messageItemInfo, sendExtend)
		}

		//如果是特定礼物全服推送
		s.PushPresentBreakingNewsToAll(ctx, sendUser, itemCfg, totalItemCount, timeVal, channelId, user, sendExtend)

		// 单次1w全服
		if totalItemCount*itemCfg.GetItemConfig().GetPrice() > 1000000 {
			_ = s.PushHighSpendPresentBreakingNews(ctx, sendUser, itemCfg, totalItemCount, timeVal, channelInfo, user, sendExtend)
		}
	}
}
