package server

import (
	"context"
	"fmt"
	account "golang.52tt.com/clients/account-go"
	"golang.52tt.com/pkg/marketid_helper"
	"golang.52tt.com/protocol/services/presentextraconf"
	sendim "golang.52tt.com/protocol/services/sendimsvr"
	"golang.52tt.com/services/present-middleware/rpc/client"
	"time"

	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	pushclient "golang.52tt.com/clients/push-notification/v2"
	"golang.52tt.com/clients/seqgen/v2"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/app"
	channelPB_ "golang.52tt.com/protocol/app/channel"
	imPb "golang.52tt.com/protocol/app/im"
	magic_spirit_logic "golang.52tt.com/protocol/app/magic-spirit-logic"
	pushPb "golang.52tt.com/protocol/app/push"
	gaSyncPB "golang.52tt.com/protocol/app/sync"
	presentPB_ "golang.52tt.com/protocol/app/userpresent"
	accountPB "golang.52tt.com/protocol/services/account-go"
	channelPB "golang.52tt.com/protocol/services/channelsvr"
	pb "golang.52tt.com/protocol/services/present-middleware"
	publicNoticePb "golang.52tt.com/protocol/services/public-notice"
	pushPB "golang.52tt.com/protocol/services/push-notification/v2"
	pushv3PB "golang.52tt.com/protocol/services/push-notification/v3"
	timelinePB "golang.52tt.com/protocol/services/timelinesvr"
	presentPB "golang.52tt.com/protocol/services/userpresent"
	"golang.52tt.com/services/helper-from-cpp/immsghelper"
	"golang.52tt.com/services/notify"
)

const (
	ENUM_PRESENT_CASTLE       = 39  // 城堡礼物id
	ENUM_PRESENT_LUCKY_FEMALE = 190 // 幸运女神id
	ENUM_PRESENT_LUCKY_MALE   = 340 // 幸运男神id
	ENUM_PRESENT_DARK_GODDESS = 725 // 黑暗女神id
	ENUM_PRESENT_ONE_PIECE    = 798 // 航海礼物id
	ENUM_PRESENT_STAR_TREK    = 851 // 星际巡航礼物id

	NewsTypeGloryMedium = 100001 // 荣耀兑换中等奖励
)

func (s *PresentMiddlewareMgr) pushNotificationToChannel(ctx context.Context, sendUser *accountPB.UserResp, targetUser *accountPB.UserResp,
	timeVal time.Time, channelId uint32, sendItemInfo *presentPB_.PresentSendItemInfo, sendExtend *baseSendExtend) error {
	if 0 == channelId {
		return nil
	}
	sendTime := timeVal.UnixNano() / 1000000
	MsgContent, _ := proto.Marshal(sendItemInfo)
	channelMsg := &channelPB_.ChannelBroadcastMsg{
		FromUid:       sendUser.GetUid(),
		FromNick:      sendUser.GetNickname(),
		FromAccount:   sendUser.GetUsername(),
		ToChannelId:   channelId,
		Time:          uint64(sendTime),
		Type:          uint32(channelPB_.ChannelMsgType_CHANNEL_PRESENT_MSG),
		Content:       MsgContent,
		PbOptContent:  MsgContent,
		TargetUid:     targetUser.GetUid(),
		TargetNick:    targetUser.GetNickname(),
		TargetAccount: targetUser.GetUsername(),
	}

	fillUserProfile(channelMsg, sendExtend)

	err := s.expressCli.SendChannelBroadcastMsg(ctx, channelMsg)
	//err := s.expressCli.SendChannelBroadcastMsg(ctx, &channelMsg)
	log.InfoWithCtx(ctx, "pushNotificationToChannel -- PushMulticast. uid:%d, channelMsg :%v ,err :%v", sendUser.GetUid(),
		channelMsg, err)

	return err
}

func (s *PresentMiddlewareMgr) pushNotificationToUser(ctx context.Context, sendUser *accountPB.UserResp, itemCfg *presentPB.GetPresentConfigByIdResp,
	totalItemCount uint32, timeVal time.Time, channelId uint32, sendItemInfo *presentPB_.PresentSendItemInfo, targetUser *accountPB.UserResp,
	userExtendJson string, msgTarget uint32, sendExtend *baseSendExtend) error {
	if 0 == channelId {
		return nil
	}

	if nil == targetUser {
		return nil
	}

	sendTime := timeVal.UnixNano() / 1000000
	presentMsg := &presentPB_.PresentSendMsg{
		ChannelId:      channelId,
		SendTime:       uint64(sendTime),
		SendUid:        sendUser.GetUid(),
		SendAccount:    sendUser.GetUsername(),
		SendNickname:   sendUser.GetNickname(),
		TargetUid:      targetUser.GetUid(),
		TargetAccount:  targetUser.GetUsername(),
		TargetNickname: targetUser.GetNickname(),
		ExtendJson:     userExtendJson,
		ItemInfo:       sendItemInfo,
	}

	fillUserPushProfile(presentMsg, sendExtend)

	userMsgContent, _ := proto.Marshal(presentMsg)
	pushMessage := pushPb.PushMessage{Cmd: uint32(pushPb.PushMessage_PRESENT_MSG), Content: userMsgContent}

	pushMessageContent, _ := proto.Marshal(&pushMessage)
	notification := &pushPB.CompositiveNotification{
		Sequence: uint32(time.Now().Unix()),
		TerminalTypeList: []uint32{
			protocol.MobileAndroidTT,
			protocol.MobileIPhoneTT,
		},
		AppId:              uint32(protocol.TT),
		TerminalTypePolicy: pushclient.DefaultPolicy,
		ProxyNotification: &pushPB.ProxyNotification{
			Type:       uint32(pushPB.ProxyNotification_PUSH),
			Payload:    pushMessageContent,
			Policy:     pushPB.ProxyNotification_DEFAULT,
			ExpireTime: 86400,
		},
	}

	err := s.pushCli.PushToUsers(ctx, []uint32{msgTarget}, notification)
	log.InfoWithCtx(ctx, "procPushEvent -- targetUids. uid:%v ,rec:%v , sendItemInfo:[%v] , extendInfo.userExtendJson:[%v]", sendUser.GetUid(), targetUser.GetUid(), sendItemInfo, userExtendJson)
	log.InfoWithCtx(ctx, "presentMsg is %+v", presentMsg)
	return err
}

func (s *PresentMiddlewareMgr) broadcastUserRichOrCharmLevelUpdate(ctx context.Context, sendUser *accountPB.UserResp, channelId uint32,
	targetUserMap map[uint32]*accountPB.UserResp, extendInfo *ExtendInfo, sendExtend *baseSendExtend) (err error) {
	uidList := make([]uint32, 0)
	for _, info := range extendInfo.targetInfoMap {
		uidList = append(uidList, info.uid)
	}

	_, _, isRecordRich, isRecordCharm := s.isRecordRich(ctx, append(uidList, sendUser.GetUid()))

	//财富值
	for {
		if !isRecordRich[sendUser.GetUid()] {
			break
		}

		log.DebugWithCtx(ctx, "broadcastUserRichOrCharmLevelUpdate extendInfo %v", extendInfo)
		if extendInfo.bIsSendUserRichLevelChanged {
			serverLevel, mainLevel, subLevel1, subLevel2 := s.TransRichOrCharmUILevel(ctx, extendInfo.sendUserCurrRichValue)
			beforeUpgradeRichValue := extendInfo.sendUserCurrRichValue - uint64(extendInfo.realAddRichValue)
			beforeServerLevel, beforeMainLevel, beforeSubLevel1, beforeSubLevel2 := s.TransRichOrCharmUILevel(ctx, beforeUpgradeRichValue)
			s.ProcUserRichDecoration(ctx, sendUser.GetUid(), serverLevel, beforeServerLevel)
			beforeMinLevel := beforeUpgradeRichValue / ********
			afterMinLevel := extendInfo.sendUserCurrRichValue / ********

			// 这里额外调用一个限时礼物的权限升级检查

			tmpCtx := context.Background()
			go func() {
				_, _ = client.PresentExtraCli.NotifyPrivilegeLevelChange(tmpCtx, &presentextraconf.NotifyPrivilegeLevelChangeReq{
					Uid:         sendUser.GetUid(),
					ValueType:   uint32(presentextraconf.ValueType_ValueTypeRich),
					BeforeValue: beforeUpgradeRichValue,
					AfterValue:  extendInfo.sendUserCurrRichValue,
				})
			}()

			isNotify := false
			switch mainLevel {
			case 3:
				if mainLevel != beforeMainLevel {
					isNotify = true
					break
				}
			case 4:
				if mainLevel != beforeMainLevel {
					isNotify = true
					break
				}
			case 5:
				if mainLevel != beforeMainLevel || subLevel1 != beforeSubLevel1 {
					isNotify = true
					break
				}
			case 6:
				if mainLevel != beforeMainLevel || subLevel1 != beforeSubLevel1 || subLevel2 != beforeSubLevel2 {
					isNotify = true
					break
				}
			case 7, 8:
				if afterMinLevel != beforeMinLevel {
					isNotify = true
					break
				}
			default:
				break
			}

			log.DebugWithCtx(ctx, "broadcastUserRichOrCharmLevelUpdate %d %d %d %d", afterMinLevel, mainLevel, subLevel1, subLevel2)
			log.DebugWithCtx(ctx, "broadcastUserRichOrCharmLevelUpdate %d %d %d %d", beforeMinLevel, beforeMainLevel, beforeSubLevel1, beforeSubLevel2)
			log.DebugWithCtx(ctx, "broadcastUserRichOrCharmLevelUpdate isNotify %v", isNotify)

			if !isNotify {
				return
			}

			//获取升级礼物信息 比较麻烦
			decorationName := ""
			decorationConfig, err := s.personalizationCli.GetChannelEnterSpecialEffectConfig(ctx)
			if err != nil {
				log.ErrorWithCtx(ctx, "broadcastUserRichOrCharmLevelUpdate -- personalizationCli fail. uid:%v, err :%v", sendUser.GetUid(), err)
			}
			awardLevel := uint32(0)
			for _, decoration := range decorationConfig.RichLevelConfig {
				if decoration.MinLevel <= serverLevel && decoration.MinLevel >= awardLevel {
					decorationName = decoration.GetEffectInfo().GetDetail().GetChannelEnterSpecialEffect().GetName() + "座驾"
					awardLevel = decoration.MinLevel
				}
				log.DebugWithCtx(ctx, "broadcastUserRichOrCharmLevelUpdate awardLevel %d", awardLevel)
			}

			memberOpt := channelPB_.ChannelMemberOpt{
				NewRichLevel:         uint32(extendInfo.sendUserCurrRichValue / uint64(1000)),
				RichCharmLevelUpdate: uint32(channelPB_.RICH_CHARM_LEVEL_UPDATE_TYPE_RICH_LEVEL_UPDATE),
				RichUpgradeAwardList: []*channelPB_.RichUpgradeAward{},
			}
			if serverLevel >= awardLevel && beforeServerLevel < awardLevel && decorationName != "" {
				memberOpt.RichUpgradeAwardList = append(memberOpt.RichUpgradeAwardList, &channelPB_.RichUpgradeAward{AwardName: decorationName})
			}

			log.DebugWithCtx(ctx, "broadcastUserRichOrCharmLevelUpdate memberOpt %v", memberOpt)
			memberOptContent, _ := proto.Marshal(&memberOpt)
			broadcastMsg := &channelPB_.ChannelBroadcastMsg{
				Content:      []byte(""),
				FromAccount:  sendUser.GetUsername(),
				FromNick:     sendUser.GetNickname(),
				FromUid:      sendUser.GetUid(),
				Time:         uint64(time.Now().Unix()),
				Type:         uint32(channelPB_.ChannelMsgType_CHANNEL_MEMBER_OPT_INFO_CHANGED),
				ToChannelId:  channelId,
				PbOptContent: memberOptContent,
			}

			fillUserProfile(broadcastMsg, sendExtend)

			_ = s.expressCli.SendChannelBroadcastMsg(ctx, broadcastMsg)

			//检查是否是财富大事件
			isBreaking, richLevelName := s.checkIsBreakNews(ctx, beforeServerLevel, serverLevel)
			if isBreaking {
				log.InfoWithCtx(ctx, "broadcastUserRichOrCharmLevelUpdate c %d u %d notify RichLevelUpgrade News: %s",
					channelId, sendUser.GetUid(), richLevelName)
				s.ProcRichUpgradeBreakingNewsV3(ctx, sendUser, channelId, serverLevel, sendExtend)
			}
		}
		break //nolint:staticcheck
	}

	for _, info := range extendInfo.targetInfoMap {
		if !isRecordCharm[info.uid] {
			continue
		}

		userResp, ok := targetUserMap[info.uid]
		if !ok {
			return err
		}
		serverLevel, mainLevel, subLevel1, _ := s.TransRichOrCharmUILevel(ctx, uint64(info.recvUserCurrCharmValue))
		if serverLevel < 3 {
			continue
		}
		_, beforeMainLevel, beforeSubLevel1, _ := s.TransRichOrCharmUILevel(ctx, info.recvUserCurrCharmValue-uint64(info.realAddCharm))
		if mainLevel == beforeMainLevel && subLevel1 == beforeSubLevel1 {
			// 如果仅仅是subLevel_2 以下的等级有变化的话 不做升级提示的push
			return
		}
		memberOpt := channelPB_.ChannelMemberOpt{NewCharmLevel: uint32(info.recvUserCurrCharmValue / 1000), RichCharmLevelUpdate: uint32(channelPB_.RICH_CHARM_LEVEL_UPDATE_TYPE_CHARM_LEVEL_UPDATE)}
		memberOptContent, _ := proto.Marshal(&memberOpt)
		broadcastMsg := &channelPB_.ChannelBroadcastMsg{Content: []byte(""), FromAccount: userResp.GetUsername(), FromNick: userResp.GetNickname(), FromUid: userResp.GetUid(), Time: uint64(time.Now().Unix()), Type: uint32(channelPB_.ChannelMsgType_CHANNEL_MEMBER_OPT_INFO_CHANGED),
			ToChannelId: channelId, PbOptContent: memberOptContent}
		fillUserProfile(broadcastMsg, sendExtend)
		_ = s.expressCli.SendChannelBroadcastMsg(ctx, broadcastMsg)
	}

	return err
}

func (s *PresentMiddlewareMgr) TransRichOrCharmUILevel(ctx context.Context, value uint64) (serverLevel uint32, mainLevel uint32,
	subLevel1 uint32, subLevel2 uint32) {
	tmpServerLevel := value / 1000
	serverLevel = uint32(tmpServerLevel)
	mainLevel, subLevel1, subLevel2 = 0, 0, 0
	for {
		if tmpServerLevel <= 0 {
			break
		}
		mainLevel++
		subLevel2 = subLevel1
		subLevel1 = uint32(tmpServerLevel % 10)
		tmpServerLevel = tmpServerLevel / 10
	}

	return serverLevel, mainLevel, subLevel1, subLevel2
}

func (s *PresentMiddlewareMgr) checkIsBreakNews(ctx context.Context, beforeLevel uint32, newLevel uint32) (isBreaking bool, strRichLevelName string) {
	knightLevel := uint32(10000)
	kingLevel := uint32(100000)
	universityKingLevel := uint32(1000000)

	log.DebugWithCtx(ctx, "coding >> Breaking News check, beforeLevel=%d, newLevel=%d, knightLevel=%d, kingLevel=%d", beforeLevel,
		newLevel, knightLevel, kingLevel)

	if newLevel < knightLevel {
		return
	}

	if newLevel >= universityKingLevel && beforeLevel/knightLevel < newLevel/knightLevel {
		tmpLevel := newLevel / knightLevel

		strRichLevelName = fmt.Sprintf("永恒帝尊%d.%d", tmpLevel/10, tmpLevel%10)
		isBreaking = true
		return
	}

	if newLevel >= kingLevel && beforeLevel/knightLevel < newLevel/knightLevel {
		tmpLevel := newLevel / knightLevel

		strRichLevelName = fmt.Sprintf("至尊帝皇%v.%v", tmpLevel/10, tmpLevel%10)
		isBreaking = true
		return
	}

	if newLevel >= knightLevel && beforeLevel/knightLevel < newLevel/knightLevel {
		strRichLevelName = fmt.Sprintf("枭雄霸王%v", newLevel/knightLevel)
		isBreaking = true
		return
	}

	return
}

func (s *PresentMiddlewareMgr) ProcUserRichDecoration(ctx context.Context, uid uint32, richLv uint32, preRichLv uint32) {
	resp, err := s.personalizationCli.GetUserDecorations(ctx, uid, uint32(richLv), 1, false)
	if err != nil {
		log.ErrorWithCtx(ctx, "ProcUserRichDecoration -- GetUserDecorationByRichLv failed. err %v uid %d rich_level %d pre_rich_level %d",
			err, uid, richLv, preRichLv)
	}
	for _, decoration := range resp.GetUserDecorations() {
		//财富坐骑
		if decoration.GetDecoration().GetDetail().GetChannelEnterSpecialEffect().GetEffectType() == 1 {
			if decoration.GetDecoration().GetDetail().GetChannelEnterSpecialEffect().GetMinLevel() <= preRichLv {
				return
			}
			if decoration.GetDecoration().GetDetail().GetChannelEnterSpecialEffect().GetName() == "" {
				return
			}
			decorationDetail := decoration.GetDecoration().GetDetail().GetChannelEnterSpecialEffect()
			decorationBytes, _ := proto.Marshal(decorationDetail)

			decorationMsg := pushPb.MyDecorationMsg{
				Uid: uid,
				UserDecoration: &app.UserDecorationInfo{
					Uid: uid,
					DecorationCfg: &app.DecorationConfig{
						Id:        decoration.GetDecoration().GetId(),
						Type:      uint32(decoration.GetDecoration().GetType()),
						Ver:       decoration.GetDecoration().GetVer(),
						RichLevel: uint32(richLv),
						Detail:    decorationBytes,
					},
				},
				RichLevel: richLv,
			}
			content, _ := proto.Marshal(&decorationMsg)
			log.ErrorWithCtx(ctx, "ProcUserRichDecoration -- decorationDetail %+v .decorationMsg %+v", decorationDetail, decorationMsg)

			seq, err := s.seqgenCli.GenerateSequence(ctx, uid, "@reliable_push", "0", 1)
			if err != nil {
				log.ErrorWithCtx(ctx, "ProcUserRichDecoration -- GenerateSequence fail. uid:%v, err :%v", uid, err)
			}

			pushMessage := &pushPb.PushMessage{
				Cmd:     uint32(pushPb.PushMessage_MY_DECORATION_MSG),
				Content: content,
				SeqId:   uint32(seq),
			}
			pushMessageBytes, _ := pushMessage.Marshal()

			e := s.pushCli.PushToUsers(context.Background(), []uint32{uid}, &pushPB.CompositiveNotification{
				Sequence: uint32(seq),
				//TerminalTypeList: []uint32{protocol.MobileAndroidTT, protocol.MobileIPhoneTT},
				TerminalTypePolicy: pushclient.DefaultPolicy,
				AppId:              0,
				ProxyNotification: &pushPB.ProxyNotification{
					Type:    uint32(pushPB.ProxyNotification_PUSH),
					Payload: pushMessageBytes,
				},
			})

			if e != nil {
				log.ErrorWithCtx(ctx, "Failed to PushToUsers  %+v ", e)
			}
		}
	}
}

func (s *PresentMiddlewareMgr) ProcRichUpgradeBreakingNewsV3(ctx context.Context, sendUser *accountPB.UserResp, channelId uint32, serverLevel uint32,
	sendExtend *baseSendExtend) {

	channelInfo, err := s.channelCli.GetChannelSimpleInfo(ctx, sendUser.GetUid(), channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "ProcRichUpgradeBreakingNewsV3 -- GetChannelSimpleInfo failed. err %v uid %d channelId %d", err, sendUser.GetUid(), channelId)
	}
	BreakingNewsMessage := &publicNoticePb.CommonBreakingNewsV3{
		FromUid: sendUser.GetUid(),
		FromUserInfo: &publicNoticePb.UserInfo{
			Nick:    sendUser.GetNickname(),
			Account: sendUser.GetUsername(),
		},
		ChannelId: channelId,
		ChannelInfo: &publicNoticePb.ChannelInfo{
			ChannelType:      channelInfo.GetChannelType(),
			ChannelName:      channelInfo.GetName(),
			ChannelBindid:    channelInfo.GetBindId(),
			ChannelDisplayid: channelInfo.GetDisplayId(),
		},
		RichLevel: serverLevel,
		BreakingNewsBaseOpt: &publicNoticePb.CommBreakingNewsBaseOpt{
			TriggerType:   uint32(pushPb.CommBreakingNewsBaseOpt_HERO_UPGRADING),
			RollingCount:  2,
			RollingTime:   10,
			AnnounceScope: uint32(pushPb.CommBreakingNewsBaseOpt_INSIDE_CHANNEL + pushPb.CommBreakingNewsBaseOpt_OUTSIDE_CHANNEL),
			JumpType:      uint32(pushPb.CommBreakingNewsBaseOpt_JUMP_ClICK_OUTSIDE), JumpPosition: uint32(pushPb.CommBreakingNewsBaseOpt_JUMP_TO_CHANNEL_CLICK),
			AnnouncePosition: uint32(pushPb.CommBreakingNewsBaseOpt_UPPER),
		},
	}
	kingLevel := uint32(100000)
	universityKingLevel := uint32(1000000)
	if serverLevel >= universityKingLevel {
		BreakingNewsMessage.BreakingNewsBaseOpt.TriggerType = uint32(pushPb.CommBreakingNewsBaseOpt_UNIVERSITY_EMPEROR_UPGRADING)
		BreakingNewsMessage.NewsContent = "财富等级升级为【永恒帝尊】"
	} else if serverLevel >= kingLevel {
		BreakingNewsMessage.BreakingNewsBaseOpt.TriggerType = uint32(pushPb.CommBreakingNewsBaseOpt_EMPEROR_UPGRADING)
	}

	breakingReq := &publicNoticePb.PushBreakingNewsReq{
		CommonBreakingNews: BreakingNewsMessage,
	}

	_, perr := s.publicNoticeCli.PushBreakingNews(ctx, breakingReq)
	if perr != nil {
		log.ErrorWithCtx(ctx, "pushBatchInfoMsgToChannel -- PushMulticast fail. uid:%v, channel_id:%v, perr:%v", sendUser.GetUid(),
			channelId, perr)
	} else {
		log.InfoWithCtx(ctx, "pushBatchInfoMsgToChannel -- PushMulticast. uid:%v, channel_id:%v", sendUser.GetUid(),
			channelId)
	}
}

func (s *PresentMiddlewareMgr) PushPresentBreakingNewsToAll(ctx context.Context, sendUser *accountPB.UserResp, itemCfg *presentPB.GetPresentConfigByIdResp,
	count uint32, timeVal time.Time, channelId uint32, targetUser *account.User, sendExtend *baseSendExtend) {

	channelInfo := sendExtend.channelSimpleInfo.GetChannelSimple()

	if s.NeedAvoidMaskedPk(ctx, sendUser.GetUid(), channelInfo.GetChannelId(),
		channelInfo.GetChannelType()) {
		return
	}

	BreakingNewsMessage := &publicNoticePb.CommonBreakingNewsV3{
		FromUid: sendUser.GetUid(),
		FromUserInfo: &publicNoticePb.UserInfo{
			Nick:    sendUser.GetNickname(),
			Account: sendUser.GetUsername(),
		},
		TargetUid: targetUser.GetUid(),
		TargetUserInfo: &publicNoticePb.UserInfo{
			Account: targetUser.GetUsername(),
			Nick:    targetUser.GetNickname(),
		},
		ChannelId: channelInfo.GetChannelId(),
		ChannelInfo: &publicNoticePb.ChannelInfo{
			ChannelType:      channelInfo.GetChannelType(),
			ChannelName:      channelInfo.GetName(),
			ChannelBindid:    channelInfo.GetBindId(),
			ChannelDisplayid: channelInfo.GetDisplayId(),
		},

		PresentNewsBaseOpt: &publicNoticePb.PresentBreakingNewsBaseOpt{
			GiftId:      itemCfg.GetItemConfig().GetItemId(),
			GiftCount:   count,
			GiftIconUrl: itemCfg.GetItemConfig().GetIconUrl(),
			GiftName:    itemCfg.GetItemConfig().GetName(),
			GiftWorth:   itemCfg.GetItemConfig().GetPrice(),
		},
		BreakingNewsBaseOpt: &publicNoticePb.CommBreakingNewsBaseOpt{},
	}

	var richTextOpt *publicNoticePb.RichTextNews

	//打龙推送样式 , 需要是公会公开厅和语音直播房
	if (channelInfo.GetChannelType() == uint32(channelPB_.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE) ||
		channelInfo.GetChannelType() == uint32(channelPB_.ChannelType_RADIO_LIVE_CHANNEL_TYPE)) && s.GeneralConfig.NeedMonsterEntry(itemCfg.GetItemConfig().GetItemId()) {
		BreakingNewsMessage.NeedMonsterInfo = true
	}

	// 先考虑一个特殊情况，是不是1000元以上的、来自荣耀世界的非荣耀礼物
	// 26指荣耀兑换，在风控配置里有
	log.DebugWithCtx(ctx, "PushPresentBreakingNewsToAll bgPresentSource is %d, BreakingNewsMessage.NeedMonsterInfo:%v", sendExtend.bgPresentSource, BreakingNewsMessage.NeedMonsterInfo)
	isMedium := false
	if itemCfg.GetItemConfig().GetPriceType() == uint32(presentPB.PresentPriceType_PRESENT_PRICE_TBEAN) && itemCfg.GetItemConfig().GetPrice() >= 100000 &&
		itemCfg.GetItemConfig().GetPrice() < 999999 && sendExtend.bgPresentSource == 26 {
		BreakingNewsMessage.BreakingNewsBaseOpt = &publicNoticePb.CommBreakingNewsBaseOpt{TriggerType: uint32(pushPb.CommBreakingNewsBaseOpt_COMMON_RICH_TEXT_NEWS),
			RollingCount: 1, RollingTime: 10, AnnounceScope: uint32(pushPb.CommBreakingNewsBaseOpt_INSIDE_CHANNEL + pushPb.CommBreakingNewsBaseOpt_OUTSIDE_CHANNEL),
			JumpType: uint32(pushPb.CommBreakingNewsBaseOpt_JUMP_ClICK_OUTSIDE), AnnouncePosition: uint32(pushPb.CommBreakingNewsBaseOpt_UPPER),
		}
		BreakingNewsMessage.IsOldDeal = 1
		BreakingNewsMessage.OldNewsContent = fmt.Sprintf("在 %s【%d】送出价值 %d豆的 %s！", channelInfo.GetName(), channelInfo.GetDisplayId(), itemCfg.GetItemConfig().GetPrice(),
			itemCfg.GetItemConfig().GetName())

		richTextOpt = &publicNoticePb.RichTextNews{}
		richTextOpt.NewsId = s.GeneralConfig.GetPresentConfig().BreakingTypeMap[NewsTypeGloryMedium]
		if richTextOpt.NewsId != 0 {
			isMedium = true
		}
	}

	// 加个前置判断, 避免配错礼物的全服属性, 但是客户端没有指定全服样式
	if itemCfg.GetItemConfig().GetItemId() != ENUM_PRESENT_CASTLE && itemCfg.GetItemConfig().GetItemId() != ENUM_PRESENT_DARK_GODDESS && itemCfg.GetItemConfig().GetItemId() != ENUM_PRESENT_LUCKY_FEMALE &&
		itemCfg.GetItemConfig().GetItemId() != ENUM_PRESENT_LUCKY_MALE && itemCfg.GetItemConfig().GetItemId() != ENUM_PRESENT_ONE_PIECE && itemCfg.GetItemConfig().GetItemId() != ENUM_PRESENT_STAR_TREK &&
		s.GetBreakingPresentType(itemCfg.GetItemConfig().GetItemId()) == 0 && !isMedium {
		return
	}

	//如果是城堡
	if itemCfg.GetItemConfig().GetItemId() == ENUM_PRESENT_CASTLE || s.GetBreakingPresentType(itemCfg.GetItemConfig().GetItemId()) == uint32(pushPb.CommBreakingNewsBaseOpt_SEND_CASTLE) {
		BreakingNewsMessage.BreakingNewsBaseOpt = &publicNoticePb.CommBreakingNewsBaseOpt{TriggerType: uint32(pushPb.CommBreakingNewsBaseOpt_SEND_CASTLE),
			RollingCount: 2, RollingTime: 10, AnnounceScope: uint32(pushPb.CommBreakingNewsBaseOpt_INSIDE_CHANNEL + pushPb.CommBreakingNewsBaseOpt_OUTSIDE_CHANNEL),
			JumpType:     uint32(pushPb.CommBreakingNewsBaseOpt_JUMP_ClICK_OUTSIDE),
			JumpPosition: uint32(pushPb.CommBreakingNewsBaseOpt_JUMP_TO_CHANNEL_CLICK), AnnouncePosition: uint32(pushPb.CommBreakingNewsBaseOpt_UPPER),
		}
	}

	if itemCfg.GetItemConfig().GetItemId() == ENUM_PRESENT_LUCKY_MALE || itemCfg.GetItemConfig().GetItemId() == ENUM_PRESENT_LUCKY_FEMALE ||
		s.GetBreakingPresentType(itemCfg.GetItemConfig().GetItemId()) == uint32(pushPb.CommBreakingNewsBaseOpt_SEND_HONOUR_PRESENT) {
		BreakingNewsMessage.BreakingNewsBaseOpt = &publicNoticePb.CommBreakingNewsBaseOpt{TriggerType: uint32(pushPb.CommBreakingNewsBaseOpt_SEND_HONOUR_PRESENT),
			RollingCount: 1, RollingTime: 10, AnnounceScope: uint32(pushPb.CommBreakingNewsBaseOpt_INSIDE_CHANNEL),
			JumpType: uint32(pushPb.CommBreakingNewsBaseOpt_NO_JUMP_CLICK), AnnouncePosition: uint32(pushPb.CommBreakingNewsBaseOpt_UPPER),
		}
	}

	if itemCfg.GetItemConfig().GetItemId() == ENUM_PRESENT_DARK_GODDESS || s.GetBreakingPresentType(itemCfg.GetItemConfig().GetItemId()) == uint32(pushPb.CommBreakingNewsBaseOpt_SEND_DARK_GODDESS) {
		BreakingNewsMessage.BreakingNewsBaseOpt = &publicNoticePb.CommBreakingNewsBaseOpt{
			TriggerType:  uint32(pushPb.CommBreakingNewsBaseOpt_SEND_DARK_GODDESS),
			RollingCount: 1, RollingTime: 10, AnnounceScope: uint32(pushPb.CommBreakingNewsBaseOpt_INSIDE_CHANNEL),
			JumpType:         uint32(pushPb.CommBreakingNewsBaseOpt_NO_JUMP_CLICK),
			AnnouncePosition: uint32(pushPb.CommBreakingNewsBaseOpt_UPPER),
		}
		BreakingNewsMessage.NewsPrefix = "黑暗之神诞生!"
		BreakingNewsMessage.IsOldDeal = 1
		BreakingNewsMessage.OldNewsContent = fmt.Sprintf("在 %s【%d】送出价值 200000豆的 黑暗女神！", channelInfo.GetName(), channelInfo.GetDisplayId())
	}

	if itemCfg.GetItemConfig().GetItemId() == ENUM_PRESENT_ONE_PIECE || s.GetBreakingPresentType(itemCfg.GetItemConfig().GetItemId()) == uint32(pushPb.CommBreakingNewsBaseOpt_SEND_ONE_PIECE_AWARD) {
		BreakingNewsMessage.BreakingNewsBaseOpt = &publicNoticePb.CommBreakingNewsBaseOpt{
			TriggerType:  uint32(pushPb.CommBreakingNewsBaseOpt_SEND_ONE_PIECE_AWARD),
			RollingCount: 1, RollingTime: 10, AnnounceScope: uint32(pushPb.CommBreakingNewsBaseOpt_INSIDE_CHANNEL),
			JumpType: uint32(pushPb.CommBreakingNewsBaseOpt_NO_JUMP_CLICK), AnnouncePosition: uint32(pushPb.CommBreakingNewsBaseOpt_UPPER),
		}
		BreakingNewsMessage.NewsPrefix = ""
		BreakingNewsMessage.IsOldDeal = 1
		BreakingNewsMessage.OldNewsContent = fmt.Sprintf("在 %s【%d】送出价值 666600豆的 风暴女神！", channelInfo.GetName(), channelInfo.GetDisplayId())
	}

	if itemCfg.GetItemConfig().GetItemId() == ENUM_PRESENT_STAR_TREK || s.GetBreakingPresentType(itemCfg.GetItemConfig().GetItemId()) == uint32(pushPb.CommBreakingNewsBaseOpt_SEND_STAR_TREK_AWARD) {
		BreakingNewsMessage.BreakingNewsBaseOpt = &publicNoticePb.CommBreakingNewsBaseOpt{TriggerType: uint32(pushPb.CommBreakingNewsBaseOpt_SEND_STAR_TREK_AWARD),
			RollingCount: 1, RollingTime: 10, AnnounceScope: uint32(pushPb.CommBreakingNewsBaseOpt_INSIDE_CHANNEL),
			JumpType: uint32(pushPb.CommBreakingNewsBaseOpt_NO_JUMP_CLICK), AnnouncePosition: uint32(pushPb.CommBreakingNewsBaseOpt_UPPER),
		}
		BreakingNewsMessage.NewsPrefix = "宇宙瞩目！"
		BreakingNewsMessage.IsOldDeal = 1
		BreakingNewsMessage.OldNewsContent = fmt.Sprintf("在 %s【%d】送出价值 300000豆的 星尘洪荒！", channelInfo.GetName(), channelInfo.GetDisplayId())
	}

	// 如果类型是专属礼物全服推送
	if s.GetBreakingPresentType(itemCfg.GetItemConfig().GetItemId()) == uint32(pushPb.CommBreakingNewsBaseOpt_SEND_CUSTOM_PRESENT) {
		BreakingNewsMessage.BreakingNewsBaseOpt = &publicNoticePb.CommBreakingNewsBaseOpt{TriggerType: uint32(pushPb.CommBreakingNewsBaseOpt_SEND_CUSTOM_PRESENT),
			RollingCount: 2, RollingTime: 10, AnnounceScope: uint32(pushPb.CommBreakingNewsBaseOpt_INSIDE_CHANNEL + pushPb.CommBreakingNewsBaseOpt_OUTSIDE_CHANNEL),
			JumpType:     uint32(pushPb.CommBreakingNewsBaseOpt_JUMP_ClICK_OUTSIDE),
			JumpPosition: uint32(pushPb.CommBreakingNewsBaseOpt_JUMP_TO_CHANNEL_CLICK), AnnouncePosition: uint32(pushPb.CommBreakingNewsBaseOpt_UPPER),
		}
	}

	// 如果类型是猫猫餐厅大奖
	if s.GetBreakingPresentType(itemCfg.GetItemConfig().GetItemId()) == uint32(pushPb.CommBreakingNewsBaseOpt_SEND_CAT_CANTEEN_AWARD) {
		BreakingNewsMessage.BreakingNewsBaseOpt = &publicNoticePb.CommBreakingNewsBaseOpt{TriggerType: uint32(pushPb.CommBreakingNewsBaseOpt_SEND_CAT_CANTEEN_AWARD),
			RollingCount: 1, RollingTime: 10, AnnounceScope: uint32(pushPb.CommBreakingNewsBaseOpt_INSIDE_CHANNEL),
			JumpType: uint32(pushPb.CommBreakingNewsBaseOpt_NO_JUMP_CLICK), AnnouncePosition: uint32(pushPb.CommBreakingNewsBaseOpt_UPPER),
		}
		BreakingNewsMessage.IsOldDeal = 1
		BreakingNewsMessage.OldNewsContent = fmt.Sprintf("在 %s【%d】送出价值 %d豆的 %s！", channelInfo.GetName(), channelInfo.GetDisplayId(), itemCfg.GetItemConfig().GetPrice(),
			itemCfg.GetItemConfig().GetName())
	}

	// 如果类型是活动礼物
	if s.GetBreakingPresentType(itemCfg.GetItemConfig().GetItemId()) == uint32(pushPb.CommBreakingNewsBaseOpt_ACTIVITY_COMMON_USER) {
		BreakingNewsMessage.BreakingNewsBaseOpt = &publicNoticePb.CommBreakingNewsBaseOpt{TriggerType: uint32(pushPb.CommBreakingNewsBaseOpt_ACTIVITY_COMMON_USER),
			RollingCount: 2, RollingTime: 10, AnnounceScope: uint32(pushPb.CommBreakingNewsBaseOpt_INSIDE_CHANNEL + pushPb.CommBreakingNewsBaseOpt_OUTSIDE_CHANNEL),
			JumpType:     uint32(pushPb.CommBreakingNewsBaseOpt_JUMP_ClICK_OUTSIDE),
			JumpPosition: uint32(pushPb.CommBreakingNewsBaseOpt_JUMP_TO_CHANNEL_CLICK), AnnouncePosition: uint32(pushPb.CommBreakingNewsBaseOpt_UPPER),
		}
		BreakingNewsMessage.NewsPrefix = "情真意切！"
		BreakingNewsMessage.NewsContent = fmt.Sprintf("送出价值%d豆礼物%s！！", itemCfg.GetItemConfig().GetPrice(), itemCfg.GetItemConfig().GetName())
	}

	// 如果类型是自定义礼物

	if s.GetBreakingPresentType(itemCfg.GetItemConfig().GetItemId()) == uint32(pushPb.CommBreakingNewsBaseOpt_COMMON_RICH_TEXT_NEWS) {
		newsId := s.GeneralConfig.GetPresentConfig().BreakingTypeMap[itemCfg.GetItemConfig().GetItemId()]
		BreakingNewsMessage.BreakingNewsBaseOpt = &publicNoticePb.CommBreakingNewsBaseOpt{TriggerType: uint32(pushPb.CommBreakingNewsBaseOpt_COMMON_RICH_TEXT_NEWS),
			RollingCount: 1, RollingTime: 10, AnnounceScope: uint32(pushPb.CommBreakingNewsBaseOpt_INSIDE_CHANNEL + pushPb.CommBreakingNewsBaseOpt_OUTSIDE_CHANNEL),
			JumpType: uint32(pushPb.CommBreakingNewsBaseOpt_JUMP_ClICK_OUTSIDE), AnnouncePosition: uint32(pushPb.CommBreakingNewsBaseOpt_UPPER),
		}
		BreakingNewsMessage.IsOldDeal = 1
		BreakingNewsMessage.OldNewsContent = fmt.Sprintf("在 %s【%d】送出价值 %d豆的 %s！", channelInfo.GetName(), channelInfo.GetDisplayId(), itemCfg.GetItemConfig().GetPrice(),
			itemCfg.GetItemConfig().GetName())

		richTextOpt = &publicNoticePb.RichTextNews{}
		richTextOpt.NewsId = newsId
	}

	breakingReq := &publicNoticePb.PushBreakingNewsReq{
		CommonBreakingNews: BreakingNewsMessage,
		RichTextNews:       richTextOpt,
	}

	_, perr := s.publicNoticeCli.PushBreakingNews(ctx, breakingReq)
	if perr != nil {
		log.ErrorWithCtx(ctx, "PushPresentBreakingNewsToAll -- PushBreakingNews fail. uid:%v, channel_id:%v, perr:%v", sendUser.GetUid(),
			channelInfo.GetChannelId(), perr)
	} else {
		log.InfoWithCtx(ctx, "PushPresentBreakingNewsToAll -- PushBreakingNews. uid:%v, channel_id:%v", sendUser.GetUid(),
			channelInfo.GetChannelId())
	}
}

func (s *PresentMiddlewareMgr) GetBreakingPresentType(giftId uint32) uint32 {
	if s.GeneralConfig == nil {
		return 0
	}

	if s.GeneralConfig.PresentConfig == nil {
		return 0
	}

	if s.GeneralConfig.PresentConfig.BreakingNewMap == nil {
		return 0
	}

	return s.GeneralConfig.PresentConfig.BreakingNewMap[giftId]
}

func (s *PresentMiddlewareMgr) WritePresentImToUser(ctx context.Context, sendUser *accountPB.UserResp, itemCfg *presentPB.GetPresentConfigByIdResp,
	targetUser *account.User, imType uint32, sendItemInfo *presentPB_.PresentSendItemInfo, sendMethod uint32) {
	msg := s.genExt(ctx, sendUser, itemCfg.GetItemConfig(), targetUser, imType, sendItemInfo, sendMethod)

	typ := uint32(0)
	if imType == uint32(presentPB_.PresentTextType_E_TEXT_TYPE_INTIMACY) {
		typ = uint32(imPb.IM_MSG_TYPE_IM_PRESENT)
	} else if imType == uint32(presentPB_.PresentTextType_E_TEXT_TYPE_DEFAULT) {
		typ = uint32(imPb.IM_MSG_TYPE_IM_PRESENT_NEW)
	}
	imMsg := &timelinePB.ImMsg{
		FromId:   sendUser.GetUid(),
		ToId:     targetUser.GetUid(),
		FromName: sendUser.GetUsername(),
		ToName:   targetUser.GetUsername(),
		FromNick: sendUser.GetNickname(),
		ToNick:   targetUser.GetNickname(),
		//注意，这是一个特殊处理，这里content存的是礼物名，用来发离线消息的，在IM消息里没用到
		Content:          itemCfg.GetItemConfig().GetName(),
		Type:             typ,
		ClientMsgTime:    0,
		Status:           uint32(gaSyncPB.NewMessageSync_UN_READ),
		ServerMsgId:      0,
		ServerMsgTime:    uint32(time.Now().Unix()),
		HasAttachment:    false,
		Platform:         uint32(timelinePB.Platform_UNSPECIFIED), // 没有平台限制必须显式指定
		MsgSourceType:    uint32(imPb.MsgSourceType_MSG_SOURCE_FROM_IM_PRESENT),
		MsgSensitiveType: uint32(imPb.MsgSensitiveType_NORMAL_SENSTIVIVE),
		Origin:           0,
		Ext:              msg,
		//Label:         uint32(imPB.MsgLabel_NEW_FOLLOWER),      // 标注这是一条关注消息
	}

	// 写消息并通知
	if err := s.writeImMsg(ctx, sendUser.GetUid(), targetUser.GetUid(), imMsg); err != nil {
		return
	}

	err := notify.NotifySyncX(ctx, []uint32{sendUser.GetUid(), targetUser.GetUid()}, notify.ImMsg)
	if err != nil {
		log.ErrorWithCtx(ctx, "NotifySyncX err , err is: %v", err)
		return
	}

	//离线推送
	err = s.WriteOfflineMsg(ctx, sendUser.GetUid(), targetUser.GetUid(), imMsg)
	if err != nil {
		log.ErrorWithCtx(ctx, "WriteOfflineMsg err , err is: %v", err)
		return
	}

	log.DebugWithCtx(ctx, "write im success ,msg : %v", imMsg)
}

func (s *PresentMiddlewareMgr) writeImMsg(ctx context.Context, uid uint32, targetUid uint32, msg *timelinePB.ImMsg) (err error) {
	//svrMsgID, err := s.seqgenCli.GenerateSequence(ctx, uid, seqgen.NamespaceUser, seqgen.KeySvrMsgId, 1)
	//if err != nil {
	//	log.ErrorWithCtx(ctx,"writeImMsg: %d %v failed to generate svr msg id: %s", uid, msg, err.Error())
	//	return
	//}

	fromMsgID, err := s.seqgenCli.GenerateSequence(ctx, uid, seqgen.NamespaceUser, seqgen.KeySvrMsgId, 1)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendExtImMsg failed to generate svr msg id: %v", err)
		return err
	}

	toMsgID, err := s.seqgenCli.GenerateSequence(ctx, targetUid, seqgen.NamespaceUser, seqgen.KeySvrMsgId, 1)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendExtImMsg failed to generate svr msg id: %v", err)
		return err
	}

	msg.ServerMsgId = uint32(fromMsgID)
	msg.TargetMsgId = uint32(toMsgID)
	imErr := immsghelper.WriteMsgToUidWithId(ctx, uid, msg, s.seqgenCli, s.timelineCLi)
	if imErr != nil {
		log.ErrorWithCtx(ctx, "SendExtImMsg - WriteMsgToUidWithId fail , msg: %v , err: %v", msg, imErr)
		return imErr
	}

	msg.ServerMsgId = uint32(toMsgID)
	msg.TargetMsgId = uint32(fromMsgID)
	imErr = immsghelper.WriteMsgToUidWithId(ctx, targetUid, msg, s.seqgenCli, s.timelineCLi)
	if imErr != nil {
		log.ErrorWithCtx(ctx, "SendExtImMsg - WriteMsgToUidWithId fail , msg: %v , err: %v", msg, imErr)
		return imErr
	}

	imErr = notify.NotifySyncX(ctx, []uint32{uid, targetUid}, notify.ImMsg)
	if imErr != nil {
		log.ErrorWithCtx(ctx, "SendExtImMsg - NotifySyncX fail , msg: %v , err: %v", msg, imErr)
	}

	return nil
}

func (s *PresentMiddlewareMgr) genExt(ctx context.Context, sendUser *accountPB.UserResp, itemCfg *presentPB.StPresentItemConfig,
	targetUser *account.User, imType uint32, sendItemInfo *presentPB_.PresentSendItemInfo, sendMethod uint32) (ext []byte) {

	present := &imPb.RichTextPresent{Price: itemCfg.GetPrice(), PriceType: itemCfg.GetPriceType(), Name: itemCfg.GetName(), Url: itemCfg.GetIconUrl()}

	//送礼文本
	words := &imPb.RichTextWords{Text: fmt.Sprintf("我送给%s%s", targetUser.GetNickname(), itemCfg.GetName())}
	elementWords := &imPb.RichTextElement_Words{Words: words}
	textElement := &imPb.RichTextElement{Content: elementWords}

	//图像
	images := &imPb.RichTextImage{Url: itemCfg.GetIconUrl()}
	elementImage := &imPb.RichTextElement_Image{Image: images}
	imageElement := &imPb.RichTextElement{Content: elementImage}

	//财富文本
	richWords := &imPb.RichTextWords{Text: fmt.Sprintf("我的财富值+%d", itemCfg.GetRichValue())}
	richElementWords := &imPb.RichTextElement_Words{Words: richWords}
	richTextElement := &imPb.RichTextElement{Content: richElementWords}

	//外部文本
	preWords := &imPb.RichTextWords{Text: "送出一份礼物"}
	preElementWords := &imPb.RichTextElement_Words{Words: preWords}
	preTextElement := &imPb.RichTextElement{Content: preElementWords}

	//送礼消息
	rMsg := &imPb.RichTextMsg{InValue: []*imPb.RichTextElement{textElement, imageElement, richTextElement}, OutValue: []*imPb.RichTextElement{preTextElement}}
	perSend := &imPb.PersonalRichTextElement{Account: []string{sendUser.GetUsername()}, Msg: rMsg}

	//收礼文本
	recWords := &imPb.RichTextWords{Text: fmt.Sprintf("%s送给我%s", sendUser.GetNickname(), itemCfg.GetName())}
	recElementWords := &imPb.RichTextElement_Words{Words: recWords}
	recTextElement := &imPb.RichTextElement{Content: recElementWords}

	//图像
	recImages := &imPb.RichTextImage{Url: itemCfg.GetIconUrl()}
	recElementImage := &imPb.RichTextElement_Image{Image: recImages}
	recImageElement := &imPb.RichTextElement{Content: recElementImage}

	//魅力文本
	charmWords := &imPb.RichTextWords{Text: fmt.Sprintf("我的魅力值+%d", itemCfg.GetCharm())}
	charmElementWords := &imPb.RichTextElement_Words{Words: charmWords}
	charmTextElement := &imPb.RichTextElement{Content: charmElementWords}

	//外部文本
	charmPreWords := &imPb.RichTextWords{Text: "收到一份礼物"}
	charmPreElementWords := &imPb.RichTextElement_Words{Words: charmPreWords}
	charmPreTextElement := &imPb.RichTextElement{Content: charmPreElementWords}

	//送礼消息
	recMsg := &imPb.RichTextMsg{InValue: []*imPb.RichTextElement{recTextElement, recImageElement, charmTextElement}, OutValue: []*imPb.RichTextElement{charmPreTextElement}}
	recPerSend := &imPb.PersonalRichTextElement{Account: []string{targetUser.GetUsername()}, Msg: recMsg}

	perMsg := &imPb.PersonalRichTextMsg{Personal: []*imPb.PersonalRichTextElement{perSend, recPerSend}}
	pMsg := &imPb.RichTextWithPresentMsg{Msg: perMsg, Present: present, ItemInfo: sendItemInfo}

	if sendMethod == uint32(pb.PresentSendMethodType_PRESENT_TYPE_FELLOW) {
		pMsg.ImPresentType = uint32(imPb.RichTextWithPresentMsg_FELLOW)
	}

	bytes, _ := pMsg.Marshal()
	return bytes
}

func (s *PresentMiddlewareMgr) WriteOfflineMsg(ctx context.Context, uid uint32, targetUid uint32, msg *timelinePB.ImMsg) (err error) {
	jumpUrl := fmt.Sprintf(`tt://chat/%s/%s`, msg.FromName, msg.FromNick)
	title := msg.FromNick
	content := fmt.Sprintf("TA在聊天中送了你「%s」🎁，快来看看吧", msg.Content)
	notification := &pushv3PB.Notification{
		JumpUrl:   jumpUrl,
		Title:     title,
		Content:   content,
		VoiceType: "1",
		Extra:     map[string]string{"push_type": "5", "opt_user": "0"},
	}
	sendPushTaskReq := &pushv3PB.SendPushTaskReq{
		Notification: notification,
		UidList:      []uint32{targetUid},
	}
	_, err = s.pushv3Cli.SendPushTask(ctx, sendPushTaskReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "writeImMsg -- Failed to pushv3Cli.SendPushTask: %s", err.Error())
		return
	}

	return nil
}

func (s *PresentMiddlewareMgr) PushBatchInfoMsgToChannel(ctx context.Context, sendUser *accountPB.UserResp, itemCfg *presentPB.GetPresentConfigByIdResp,
	totalItemCount uint32, timeVal time.Time, channelId uint32, batchInfo *pb.PresentBatchInfoMsg, sendExtend *baseSendExtend) error {
	if 0 == channelId {
		return nil
	}
	sendTime := timeVal.UnixNano() / 1000000
	targetList := make([]*presentPB_.PresentBatchTargetInfo, 0)

	for _, target := range batchInfo.TargetList {
		newTarget := &presentPB_.PresentBatchTargetInfo{
			Uid:        target.Uid,
			Account:    target.Account,
			Nickname:   target.Nickname,
			ExtendJson: target.ExtendJson,
		}
		fillBatchTargetUserProfile(newTarget, sendExtend)

		targetList = append(targetList, newTarget)
	}
	drawLinelist := make([]*presentPB_.PresentLine, 0)
	if batchInfo.ItemInfo.GetDrawPresentPic() != nil {
		for _, line := range batchInfo.ItemInfo.DrawPresentPic.LineList {
			pointList := make([]*presentPB_.PresentPoint, 0)
			for _, point := range line.PointList {
				pointList = append(pointList, &presentPB_.PresentPoint{X: point.X, Y: point.Y})
			}
			presentLine := presentPB_.PresentLine{ItemId: line.ItemId, PointList: pointList}
			drawLinelist = append(drawLinelist, &presentLine)
		}
	}

	itemInfo := &presentPB_.PresentSendItemInfo{
		ItemId:            batchInfo.ItemInfo.ItemId,
		Count:             batchInfo.ItemInfo.Count,
		DynamicTemplateId: batchInfo.ItemInfo.DynamicTemplateId,
		ShowEffect:        batchInfo.ItemInfo.ShowEffect,
		ShowEffectV2:      batchInfo.ItemInfo.ShowEffectV2,
		FlowId:            batchInfo.ItemInfo.FlowId,
		IsBatch:           batchInfo.ItemInfo.IsBatch,
		ShowBatchEffect:   batchInfo.ItemInfo.ShowBatchEffect,
		SendType:          batchInfo.ItemInfo.SendType,
		DrawPresentPic:    &presentPB_.DrawPresentPicture{LineList: drawLinelist},
	}

	fansMsg := presentPB_.PresentBatchInfoMsg{
		SendUid:        sendUser.GetUid(),
		SendAccount:    sendUser.GetUsername(),
		SendNickname:   sendUser.GetNickname(),
		SendTime:       uint64(sendTime),
		ChannelId:      channelId,
		BatchType:      batchInfo.BatchType,
		ItemId:         batchInfo.ItemId,
		TotalItemCount: totalItemCount,
		ExtendJson:     batchInfo.ExtendJson,
		TargetList:     targetList,
		ItemInfo:       itemInfo,
	}

	fillBatchUserProfile(&fansMsg, sendExtend)

	batchMsgContent, _ := proto.Marshal(&fansMsg)
	channelMsg := &channelPB_.ChannelBroadcastMsg{
		FromUid:      sendUser.GetUid(),
		FromNick:     sendUser.GetNickname(),
		FromAccount:  sendUser.GetUsername(),
		ToChannelId:  channelId,
		Time:         uint64(sendTime),
		Type:         uint32(channelPB_.ChannelMsgType_CHANNEL_BATCH_SEND_PRESENT_NOTIFY),
		Content:      batchMsgContent,
		PbOptContent: batchMsgContent,
	}

	fillUserProfile(channelMsg, sendExtend)

	err := s.expressCli.SendChannelBroadcastMsg(ctx, channelMsg)

	log.InfoWithCtx(ctx, "PushBatchInfoMsgToChannel -- SendChannelBroadcastMsg. uid:%d, channel_id:%d ,  content:%v ,err :%v", sendUser.GetUid(),
		channelId, fansMsg, err)
	return err
}

func (s *PresentMiddlewareMgr) PushMagicInfoMsgToChannel(ctx context.Context, sendExtend *baseSendExtend, optPb *magic_spirit_logic.SendMagicSpiritOpt) error {

	sendUser := sendExtend.sendUser
	channelId := sendExtend.channelSimpleInfo.GetChannelSimple().GetChannelId()

	MsgContent, _ := proto.Marshal(optPb)
	channelMsg := &channelPB_.ChannelBroadcastMsg{
		FromUid:       sendUser.GetUid(),
		FromNick:      sendUser.GetNickname(),
		FromAccount:   sendUser.GetUsername(),
		ToChannelId:   channelId,
		ToChannelType: sendExtend.channelSimpleInfo.GetChannelSimple().GetChannelType(),
		Time:          uint64(sendExtend.nowTs.Unix()),
		Type:          uint32(channelPB_.ChannelMsgType_MAGIC_SPIRIT_PRESENT),
		Content:       MsgContent,
		PbOptContent:  MsgContent,
	}

	fillUserProfile(channelMsg, sendExtend)

	err := s.expressCli.SendChannelBroadcastMsg(ctx, channelMsg)

	log.InfoWithCtx(ctx, "PushMagicInfoMsgToChannel -- SendChannelBroadcastMsg. uid:%d, channel_id:%d ,  content:%v ,err :%v", sendUser.GetUid(),
		channelId, optPb, err)
	return err
}

func (s *PresentMiddlewareMgr) PushHighSpendPresentBreakingNews(ctx context.Context, sendUser *accountPB.UserResp, itemCfg *presentPB.GetPresentConfigByIdResp,
	itemCount uint32, timeVal time.Time, chSimpleInfo *channelPB.ChannelSimpleInfo, targetUser *accountPB.UserResp, sendExtend *baseSendExtend) error {
	if 0 == chSimpleInfo.GetChannelId() {
		return nil
	}

	if s.NeedAvoidMaskedPk(ctx, sendUser.GetUid(), chSimpleInfo.GetChannelId(),
		chSimpleInfo.GetChannelType()) {
		return nil
	}

	BreakingNewsMessage := &publicNoticePb.CommonBreakingNewsV3{
		FromUid: sendUser.GetUid(),
		FromUserInfo: &publicNoticePb.UserInfo{
			Nick:    sendUser.GetNickname(),
			Account: sendUser.GetUsername(),
		},
		TargetUid: targetUser.GetUid(),
		TargetUserInfo: &publicNoticePb.UserInfo{
			Nick:    targetUser.GetNickname(),
			Account: targetUser.GetUsername(),
		},
		ChannelId: chSimpleInfo.GetChannelId(),
		ChannelInfo: &publicNoticePb.ChannelInfo{
			ChannelType:      chSimpleInfo.GetChannelType(),
			ChannelName:      chSimpleInfo.GetName(),
			ChannelBindid:    chSimpleInfo.GetBindId(),
			ChannelDisplayid: chSimpleInfo.GetDisplayId(),
		},
		BreakingNewsBaseOpt: &publicNoticePb.CommBreakingNewsBaseOpt{
			TriggerType:      uint32(pushPb.CommBreakingNewsBaseOpt_SEND_TEN_THOUSAND_PRESENT),
			RollingCount:     2,
			RollingTime:      10,
			AnnounceScope:    uint32(pushPb.CommBreakingNewsBaseOpt_INSIDE_CHANNEL + pushPb.CommBreakingNewsBaseOpt_OUTSIDE_CHANNEL),
			JumpType:         uint32(pushPb.CommBreakingNewsBaseOpt_JUMP_ClICK_OUTSIDE),
			JumpPosition:     uint32(pushPb.CommBreakingNewsBaseOpt_JUMP_TO_CHANNEL_CLICK),
			AnnouncePosition: uint32(pushPb.CommBreakingNewsBaseOpt_UPPER)},
		PresentNewsBaseOpt: &publicNoticePb.PresentBreakingNewsBaseOpt{
			GiftWorth:   itemCfg.GetItemConfig().GetRichValue(),
			GiftName:    itemCfg.GetItemConfig().GetName(),
			GiftIconUrl: itemCfg.GetItemConfig().GetIconUrl(),
			GiftCount:   itemCount,
			GiftId:      itemCfg.GetItemConfig().GetItemId(),
		},
	}

	//打龙推送样式 , 需要是公会公开厅和语音直播房
	if chSimpleInfo.GetChannelType() == uint32(channelPB_.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE) ||
		chSimpleInfo.GetChannelType() == uint32(channelPB_.ChannelType_RADIO_LIVE_CHANNEL_TYPE) {
		BreakingNewsMessage.NeedMonsterInfo = true
	}

	breakingReq := &publicNoticePb.PushBreakingNewsReq{
		CommonBreakingNews: BreakingNewsMessage,
	}
	_, err := s.publicNoticeCli.PushBreakingNews(ctx, breakingReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "pushBatchInfoMsgToChannel -- PushBreakingNews fail. uid:%v, channel_id:%v, err:%v", sendUser.GetUid(),
			chSimpleInfo.GetChannelId(), err)
	} else {
		log.InfoWithCtx(ctx, "pushBatchInfoMsgToChannel -- PushBreakingNews. uid:%v, channel_id:%v", sendUser.GetUid(),
			chSimpleInfo.GetChannelId())
	}
	return err
}

func (s *PresentMiddlewareMgr) SendFirstScoreMsg(ctx context.Context, uid, marketId, clientType uint32) error {
	content := "积分提现功能仅支持通过达人认证的用户，立即进行达人认证"
	highLight := "立即进行达人认证"
	url := marketid_helper.Get("doyen_certification_url", marketId, clientType)

	if url == "" {
		log.ErrorWithCtx(ctx, "SendFirstScoreMsg url is empty , uid %d", uid)
		return nil
	}

	msg := &sendim.SendSyncReq{
		Sender: &sendim.Sender{
			Type: int32(sendim.Sender_User),
			Id:   10000,
		},
		Receiver: &sendim.Receiver{
			Type:   int32(sendim.Receiver_User),
			IdList: []uint32{uid},
		},
		Msg: &sendim.ImMsg{
			Content: &sendim.Content{
				Type: int32(sendim.Content_TextWithHighlightUrl),
				TextHlUrl: &sendim.ImTextWithHighlightUrl{
					Content:   content,
					Highlight: highLight,
					Url:       url,
				},
			},
			AppPlatform: "all",
			AppName:     "",
			ExpiredAt:   uint32(time.Now().Unix()) + 86400,
		},
		WithNotify: true,
	}
	log.InfoWithCtx(ctx, "sendIMMsg ctx: %+v ,msg: %s, uid:%v", ctx, content, uid)

	_, err := client.SendImCli.SendSync(ctx, msg)
	if err != nil {
		log.ErrorWithCtx(ctx, "sendIMMsg content %s , err: %s", content, err.Error())
	}

	return nil
}

func (s *PresentMiddlewareMgr) PushFirstScore(ctx context.Context, uid uint32) error {
	pushMsg := &pushPb.TbeanPresentDoyenVerifyPushMsg{Content: s.GeneralConfig.GetPresentConfig().FirstScoreMsg}

	userMsgContent, _ := proto.Marshal(pushMsg)
	pushMessage := pushPb.PushMessage{Cmd: uint32(pushPb.PushMessage_TBEAN_PRESENT_DOYEN_VERIFY_PUSH), Content: userMsgContent}

	pushMessageContent, _ := proto.Marshal(&pushMessage)
	notification := &pushPB.CompositiveNotification{
		Sequence: uint32(time.Now().Unix()),
		TerminalTypeList: []uint32{
			protocol.MobileAndroidTT,
			protocol.MobileIPhoneTT,
			protocol.WindowsTT,
		},
		AppId:              uint32(protocol.TT),
		TerminalTypePolicy: pushclient.DefaultPolicy,
		ProxyNotification: &pushPB.ProxyNotification{
			Type:       uint32(pushPB.ProxyNotification_PUSH),
			Payload:    pushMessageContent,
			Policy:     pushPB.ProxyNotification_DEFAULT,
			ExpireTime: 86400,
		},
	}

	err := s.pushCli.PushToUsers(ctx, []uint32{uid}, notification)
	log.InfoWithCtx(ctx, "PushFirstScore uid %d", uid)
	return err
}
