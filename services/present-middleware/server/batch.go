package server

import (
	"context"
	"crypto/rand"
	"encoding/json"
	"fmt"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	account "golang.52tt.com/clients/account-go"
	pushclient "golang.52tt.com/clients/push-notification/v2"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	channelPB_ "golang.52tt.com/protocol/app/channel"
	pushPb "golang.52tt.com/protocol/app/push"
	presentPB_ "golang.52tt.com/protocol/app/userpresent"
	"golang.52tt.com/protocol/common/status"
	pushPB "golang.52tt.com/protocol/services/push-notification/v2"
	"golang.52tt.com/services/present-middleware/conf"
	"math/big"

	accountPB "golang.52tt.com/protocol/services/account-go"
	pb "golang.52tt.com/protocol/services/present-middleware"
	presentPB "golang.52tt.com/protocol/services/userpresent"
	"time"
)

func (s *BatchPresentMiddlewareMgr) BeforeSendPresent(ctx context.Context, inter interface{}, outInter interface{}, sendExtend *baseSendExtend) (err error) {
	// 类型断言
	out, ok := outInter.(*pb.BatchSendPresentResp)
	if !ok {
		log.ErrorWithCtx(ctx, "BeforeSendPresent -- para inter need *SendPresentResp type. ")
		return nil
	}
	in, ok := inter.(*pb.BatchSendPresentReq)
	if !ok {
		log.ErrorWithCtx(ctx, "BeforeSendPresent -- para inter need *PresentSendBase type. ")
		return nil
	}
	if in.ServiceInfo == nil {
		in.ServiceInfo = &pb.ServiceCtrlInfo{}
	}
	sendExtend.nowTs = time.Now()
	out.ItemSource = in.ItemSource
	ctx = s.SetPresentGoCtx(ctx, in.GetSendUid())

	// 先检查是不是需要ban掉的礼物id
	if s.GeneralConfig.GetPresentConfig().BanItemMap != nil {
		if source, ok := s.GeneralConfig.GetPresentConfig().BanItemMap[in.GetItemId()]; ok {
			if source == conf.BanItemTypeLottie || source == conf.BanItemTypeAll {
				return protocol.NewExactServerError(nil, status.ErrUserPresentConfigNotExist)
			}
		}
	}

	// 预处理，获得送礼要用的相关信息
	err = s.preSendPresent(ctx, sendExtend.nowTs, in.GetSendUid(),
		in.GetTargetUidList(), in.GetItemId(), in.GetChannelId(), uint32(pb.PresentSendMethodType_PRESENT_TYPE_ROOM), sendExtend)
	if err != nil {
		log.ErrorWithCtx(ctx, "BeforeSendPresent -- preSendPresent fail. uid:%v c:%v err:%v",
			in.SendUid, in.ChannelId, err)
		return err
	}
	return err
}

func (s *BatchPresentMiddlewareMgr) SendingPresent(ctx context.Context, inter interface{}, outInter interface{}, sendExtend *baseSendExtend) (err error) {

	// 类型断言
	_, ok := outInter.(*pb.BatchSendPresentResp)
	if !ok {
		log.ErrorWithCtx(ctx, "SendingPresent -- para inter need *SendPresentResp type. ")
		return nil
	}
	in, ok := inter.(*pb.BatchSendPresentReq)
	if !ok {
		log.ErrorWithCtx(ctx, "SendingPresent -- para inter need *SendPresentReq type. ")
		return nil
	}

	//冻结红钻/t豆

	sendExtend.remainTbeans, sendExtend.remainSource, sendExtend.realItemSource, err =
		s.tryFreeze(ctx, sendExtend, in.Count, in.ItemSource, in.SourceId, uint32(pb.PresentSendMethodType_PRESENT_TYPE_ROOM))
	if err != nil {
		log.ErrorWithCtx(ctx, "SendingPresent -- tryFreeze fail. uid:%v c:%v err:%v",
			in.SendUid, in.ChannelId, err)
		return err
	}

	sendExtend.extendInfo = &ExtendInfo{targetInfoMap: map[uint32]*TargetExtendInfo{}}

	//赠送礼物
	sendExtend.sucOrders, err = s.sendPresent(ctx, sendExtend, in.ServiceInfo, in.Count, in.AppId, in.MarketId, in.ItemSource, in.SendSource, in.BatchType, 0, true)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendingPresent -- __SendPresent fail. uid:%v ret:%v req:%v",
			in.SendUid, err, in)
		return err
	}
	log.ErrorWithCtx(ctx, "after send present extend info : %v", sendExtend.extendInfo)

	//礼物周边信息处理
	err = s.ProcPresentWatch(ctx, sendExtend, in.Count, true)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendingPresent -- __ProcPresentWatch fail:%v. _req:%v", err, in)
	}

	//提交支付订单
	if uint32(pb.PresentSourceType_PRESENT_SOURCE_BUY) == in.ItemSource {
		err = s.commitPayOrder(ctx, sendExtend.sucOrders, uint32(pb.PresentSendMethodType_PRESENT_TYPE_ROOM), in.ItemSource, sendExtend.presentConfig.GetItemConfig().GetPriceType())
	}
	return err
}

func (s *BatchPresentMiddlewareMgr) AfterSendPresent(ctx context.Context, inter interface{}, outInter interface{}, sendExtend *baseSendExtend) (err error) {

	// 类型断言
	out, ok := outInter.(*pb.BatchSendPresentResp)
	if !ok {
		log.ErrorWithCtx(ctx, "AfterSendPresent -- para inter need *SendPresentResp type. ")
		return nil
	}
	in, ok := inter.(*pb.BatchSendPresentReq)
	if !ok {
		log.ErrorWithCtx(ctx, "AfterSendPresent -- para inter need *SendPresentReq type. ")
		return nil
	}

	//【防小白骚扰】24小时内单笔送出/收到1000元礼物的用户 存redis
	consumeMap := make(map[uint32]uint32, 0)

	for _, order := range sendExtend.sucOrders {
		consumeMap[order.targetUid] = sendExtend.presentConfig.GetItemConfig().GetPrice()
	}
	err = s.SetHighConsume(ctx, consumeMap, in.SendUid)
	if err != nil {
		log.ErrorWithCtx(ctx, "AfterSendPresent -- SetHighConsume err, uid %d ,  err : %v. ", in.SendUid, err)
		return nil
	}

	ctx = context.WithValue(ctx, IsRecordSenderRichKey, s.isRecordRichMap(ctx, sendExtend.sendUser.GetUid(), sendExtend.targetUserMap))

	//送礼后处理，填充动效、推送等
	s.FillRespAndPush(ctx, in, out, sendExtend)
	return err
}

//func (s *BatchPresentMiddlewareMgr) getStrMicroTime(time time.Time) string {
//	strMicroTime := fmt.Sprintf("batch_%s_%d", time.Format("**************"), (time.UnixNano()/100)%100000)
//	return strMicroTime
//}

func (s *BatchPresentMiddlewareMgr) procPushEvent(ctx context.Context, in *pb.BatchSendPresentReq, out *pb.BatchSendPresentResp, sendExtend *baseSendExtend) {

	//推送
	successTargetUserMap := make(map[uint32]*accountPB.UserResp)
	for _, item := range sendExtend.sucOrders {
		successTargetUserMap[item.targetUid] = sendExtend.targetUserMap[item.targetUid]
	}

	targetUids := []uint32{}
	for _, user := range successTargetUserMap {
		targetUids = append(targetUids, user.GetUid())
	}

	sendUser := sendExtend.sendUser
	itemCfg := sendExtend.presentConfig
	timeVal := sendExtend.nowTs
	extendInfo := sendExtend.extendInfo
	channelId := sendExtend.channelSimpleInfo.GetChannelSimple().GetChannelId()
	sendItemInfo := out.GetItemInfo()
	batchType := in.GetBatchType()
	bindChannel := in.GetAnotherChannelId()
	totalItemCount := in.Count * uint32(len(successTargetUserMap))

	bIsNoPush := false
	//房间人数较多，则丢一部分推送
	if uint32(presentPB.PresentPriceType_PRESENT_PRICE_RED_DIAMOND) == itemCfg.GetItemConfig().GetPriceType() && channelId != 0 {
		// 增加短超时
		tmpCtx, cancel := context.WithTimeout(ctx, time.Millisecond*100)
		defer cancel()
		size, err := s.channelolCli.GetChannelMemberSize(tmpCtx, sendUser.GetUid(), channelId)
		if err != nil {
			log.ErrorWithCtx(ctx, "PresentBatchSendService::procPushEvent GetChannelMemberSize failed. err:%v, uid:%v", err, sendUser.GetUid())
		}
		dropRadio := (size - 1000) / 30
		if dropRadio == 0 {
			dropRadio = 1
		}
		if dropRadio > 90 {
			dropRadio = 90
		}
		roll, _ := rand.Int(rand.Reader, big.NewInt(100))
		if size > 1000 && uint32(roll.Int64()) > dropRadio {
			bIsNoPush = true
		}
	}

	tmpGaPushBatchInfo := pb.PresentBatchInfoMsg{TargetList: []*pb.PresentBatchTargetInfo{}}
	tmpGaPushBatchInfo.ItemInfo = sendItemInfo
	tmpGaPushBatchInfo.ChannelId = channelId
	tmpGaPushBatchInfo.ItemId = itemCfg.GetItemConfig().ItemId
	tmpGaPushBatchInfo.BatchType = batchType
	tmpGaPushBatchInfo.SendTime = uint64(timeVal.UnixNano() / 1000000)
	tmpGaPushBatchInfo.SendUid = sendUser.GetUid()
	tmpGaPushBatchInfo.SendAccount = sendUser.GetUsername()
	tmpGaPushBatchInfo.SendNickname = sendUser.GetNickname()
	tmpGaPushBatchInfo.TotalItemCount = totalItemCount
	if itemCfg.GetItemConfig().GetExtend().GetFlowId() > 0 {
		tmpGaPushBatchInfo.ItemInfo.DynamicTemplateId = 0
	}

	for _, user := range successTargetUserMap {
		extend, ok := extendInfo.targetInfoMap[user.GetUid()]
		info := &pb.PresentBatchTargetInfo{
			Uid:      user.GetUid(),
			Account:  user.GetUsername(),
			Nickname: user.GetNickname(),
		}

		if ok {
			info.ExtendJson = extend.userExtendJson
		}

		if item, ok := sendExtend.ukwInfoMap[user.GetUid()]; ok {
			info.UserProfile = genUserProfile(item)

			if item.GetPrivilege() != nil {
				info.Account = item.GetPrivilege().GetAccount()
				info.Nickname = item.GetPrivilege().GetNickname()
			}
		}

		tmpGaPushBatchInfo.TargetList = append(tmpGaPushBatchInfo.TargetList, info)
	}

	err := s.pushFansPresentInfoMsgToChannel(ctx, sendUser, itemCfg, timeVal, channelId, successTargetUserMap, bindChannel, sendExtend)
	if err != nil {
		log.ErrorWithCtx(ctx, "pushBatchInfoMsgToChannel -- PushMulticast. uid:%v, channel_id:%v, item_id:%v", sendUser.GetUid(),
			channelId, itemCfg.GetItemConfig().GetItemId())
	}

	targetIndex := 0

	messageItemInfo := presentPB_.PresentSendItemInfo{
		ItemId:            sendItemInfo.ItemId,
		Count:             sendItemInfo.Count,
		ShowBatchEffect:   sendItemInfo.ShowBatchEffect,
		ShowEffect:        sendItemInfo.ShowEffect,
		ShowEffectV2:      sendItemInfo.ShowEffectV2,
		FlowId:            sendItemInfo.FlowId,
		SendType:          sendItemInfo.SendType,
		DynamicTemplateId: sendItemInfo.DynamicTemplateId,
		IsVisibleToSender: true}

	log.DebugWithCtx(ctx, "procPushEvent -- targetUids. uid:%v, targetUids:[%v] , extendInfo.userExtendJson:[%v]", sendUser.GetUid(), targetUids, extendInfo.userExtendJson)

	for _, uid := range targetUids {
		userExtendJson := ""
		user, ok := successTargetUserMap[uid]
		if !ok {
			continue
		}
		out.TargetList = append(out.TargetList, &pb.PresentTargetUserInfo{Uid: user.GetUid(), Account: user.GetUsername(), Name: user.GetNickname()})
		out.MsgInfo.TargetList = append(out.MsgInfo.TargetList, &pb.PresentBatchTargetInfo{Uid: user.GetUid(), Account: user.GetUsername(), Nickname: user.GetNickname()})
		extend, ok := extendInfo.targetInfoMap[uid]
		if ok {
			userExtendJson = extend.userExtendJson
		}
		targetIndex++

		var richValue int32 = 0
		if s.isRecordSenderRich(ctx, sendUser.GetUid(), itemCfg.GetItemConfig().GetPriceType()) {
			richValue = int32(extendInfo.baseRichValue) / int32(len(targetUids))
		}
		if !bIsNoPush {
			_ = s.pushNotificationToUser(ctx, sendUser, itemCfg, totalItemCount, timeVal, channelId, &messageItemInfo, user, userExtendJson, uid, sendExtend)
			//这一条是新礼物中间服务才带的，用于向送礼者推送一条展示财富值的推送
			jsonSendUser := jsonSendUser{RichValue: richValue, RichValuePrefix: "", MemberContributionPrefix: "", BonusMsg: extendInfo.richValueBonusMsg}
			userExtendJson, _ := json.Marshal(jsonSendUser)
			_ = s.pushNotificationToUser(ctx, sendUser, itemCfg, totalItemCount, timeVal, channelId, &messageItemInfo, user, string(userExtendJson), sendUser.GetUid(), sendExtend)
			// 尝试修复ios收礼人不显示特效的问题，再发一条给对应转播房间的
			if bindChannel != 0 {
				_ = s.pushNotificationToUser(ctx, sendUser, itemCfg, totalItemCount, timeVal, bindChannel, &messageItemInfo, user, string(userExtendJson), uid, sendExtend)
			}
		}

		if !bIsNoPush && channelId != 0 {
			_ = s.pushNotificationToChannel(ctx, sendUser, itemCfg, timeVal, channelId, &messageItemInfo, user, bindChannel, sendExtend)
		}

		//如果是特定礼物全服推送
		s.PushPresentBreakingNewsToAll(ctx, sendUser, itemCfg, totalItemCount, timeVal, channelId, successTargetUserMap[uid], sendExtend)
	}
}

func (s *BatchPresentMiddlewareMgr) FillRespAndPush(ctx context.Context, in *pb.BatchSendPresentReq, out *pb.BatchSendPresentResp, sendExtend *baseSendExtend) {
	nowTs := sendExtend.nowTs
	sendUser := sendExtend.sendUser
	presentConfig := sendExtend.presentConfig
	extendInfo := sendExtend.extendInfo

	sucUsers := make(map[uint32]*accountPB.UserResp)
	for _, item := range sendExtend.sucOrders {
		sucUsers[item.targetUid] = sendExtend.targetUserMap[item.targetUid]
	}

	out.CurTbeans = uint64(sendExtend.remainTbeans)
	out.SourceRemain = sendExtend.remainSource

	out.ItemInfo = &pb.PresentSendItemInfo{}
	out.MsgInfo = &pb.PresentBatchInfoMsg{}

	//送礼特效与动效
	showEffect, showEffectV2, FlowId := s.getPresentEffect(ctx, presentConfig.GetItemConfig(), in.Count)
	templateId, err := s.getPresentDynamicEffectTemplate(ctx, sendUser.GetUid(), presentConfig.GetItemConfig(), in.Count)
	if err != nil {
		return
	}

	out.ItemInfo.ItemId = presentConfig.GetItemConfig().GetItemId()
	out.ItemInfo.Count = in.Count
	out.ItemInfo.ShowEffect = showEffect
	out.ItemInfo.ShowEffectV2 = showEffectV2
	out.ItemInfo.FlowId = FlowId
	out.ItemInfo.IsBatch = false
	out.ItemInfo.SendType = in.SendType
	out.ItemInfo.DynamicTemplateId = templateId
	out.ItemInfo.ShowBatchEffect = false

	s.procPushEvent(ctx, in, out, sendExtend)

	log.DebugWithCtx(ctx, "extend info : %v", extendInfo)
	out.MsgInfo.ItemInfo = out.ItemInfo
	out.MsgInfo.ChannelId = in.ChannelId
	out.MsgInfo.SendTime = uint64(nowTs.UnixNano() / 1000000)
	out.MsgInfo.SendUid = sendUser.GetUid()
	out.MsgInfo.SendNickname = sendUser.GetNickname()
	out.MsgInfo.SendAccount = sendUser.GetUsername()
	out.MsgInfo.ItemId = presentConfig.GetItemConfig().GetItemId()
	out.MsgInfo.TotalItemCount = in.Count * uint32(len(sucUsers))
	out.MsgInfo.BatchType = in.BatchType
	out.MsgInfo.ExtendJson = extendInfo.userExtendJson
	if presentConfig.GetItemConfig().GetExtend().FlowId > 0 {
		out.MsgInfo.ItemInfo.DynamicTemplateId = 0
	}
}

func (s *BatchPresentMiddlewareMgr) pushNotificationToChannel(ctx context.Context, sendUser *accountPB.UserResp, itemCfg *presentPB.GetPresentConfigByIdResp,
	timeVal time.Time, channelId uint32, sendItemInfo *presentPB_.PresentSendItemInfo, targetUid *accountPB.UserResp,
	bindChannel uint32, sendExtend *baseSendExtend) error {
	if 0 == channelId {
		return nil
	}
	sendTime := timeVal.UnixNano() / 1000000
	MsgContent, _ := proto.Marshal(sendItemInfo)
	channelMsg := &channelPB_.ChannelBroadcastMsg{FromUid: sendUser.GetUid(), FromNick: sendUser.GetNickname(), FromAccount: sendUser.GetUsername(),
		ToChannelId: channelId, Time: uint64(sendTime), Type: uint32(channelPB_.ChannelMsgType_CHANNEL_PRESENT_MSG),
		Content: MsgContent, PbOptContent: MsgContent, TargetUid: targetUid.GetUid(), TargetNick: targetUid.GetNickname(), TargetAccount: targetUid.GetUsername(),
	}

	fillUserProfile(channelMsg, sendExtend)

	channelMsgContent, _ := proto.Marshal(channelMsg)
	pushMessage := pushPb.PushMessage{Cmd: uint32(pushPb.PushMessage_CHANNEL_MSG_BRO), Content: channelMsgContent}

	pushMessageContent, _ := proto.Marshal(&pushMessage)
	notification := &pushPB.CompositiveNotification{
		Sequence: uint32(time.Now().Unix()),
		TerminalTypeList: []uint32{
			protocol.MobileAndroidTT,
			protocol.MobileIPhoneTT,
		},
		AppId:              uint32(protocol.TT),
		TerminalTypePolicy: pushclient.DefaultPolicy,
		ProxyNotification: &pushPB.ProxyNotification{
			Type:       uint32(pushPB.ProxyNotification_PUSH),
			Payload:    pushMessageContent,
			Policy:     pushPB.ProxyNotification_DEFAULT,
			ExpireTime: 86400,
		},
	}

	err := s.pushCli.PushMulticast(ctx, uint64(channelId), fmt.Sprintf("%d@channel", channelId), []uint32{}, notification)

	// 考虑到官频，要做一个转发；由于要求中断转播后还要求能看到抽奖结果，所以只能手动发一条推送
	if bindChannel != 0 {
		bindChannelMsg := &channelPB_.ChannelBroadcastMsg{FromUid: sendUser.GetUid(), FromNick: sendUser.GetNickname(), FromAccount: sendUser.GetUsername(),
			ToChannelId: bindChannel, Time: uint64(sendTime), Type: uint32(channelPB_.ChannelMsgType_CHANNEL_PRESENT_MSG),
			Content: MsgContent, PbOptContent: MsgContent, TargetUid: targetUid.GetUid(), TargetNick: targetUid.GetNickname(), TargetAccount: targetUid.GetUsername(),
		}
		fillUserProfile(bindChannelMsg, sendExtend)

		bindChannelMsgContent, _ := proto.Marshal(bindChannelMsg)
		bindPushMessage := pushPb.PushMessage{Cmd: uint32(pushPb.PushMessage_CHANNEL_MSG_BRO), Content: bindChannelMsgContent}

		bindPushMessageContent, _ := proto.Marshal(&bindPushMessage)
		bindNotification := &pushPB.CompositiveNotification{
			Sequence: uint32(time.Now().Unix()),
			TerminalTypeList: []uint32{
				protocol.MobileAndroidTT,
				protocol.MobileIPhoneTT,
			},
			AppId:              uint32(protocol.TT),
			TerminalTypePolicy: pushclient.DefaultPolicy,
			ProxyNotification: &pushPB.ProxyNotification{
				Type:       uint32(pushPB.ProxyNotification_PUSH),
				Payload:    bindPushMessageContent,
				Policy:     pushPB.ProxyNotification_DEFAULT,
				ExpireTime: 86400,
			},
		}
		err = s.pushCli.PushMulticast(ctx, uint64(bindChannel), fmt.Sprintf("%d@channel", bindChannel), []uint32{}, bindNotification)
	}

	//err := s.expressCli.SendChannelBroadcastMsg(ctx, &channelMsg)
	log.InfoWithCtx(ctx, "pushNotificationToChannel -- PushMulticast. uid:%d, channel_id:%d , bind_channel:%d, item_id:%d ,err :%v", sendUser.GetUid(),
		channelId, bindChannel, itemCfg.GetItemConfig().GetItemId(), err)

	return err
}

func (s *BatchPresentMiddlewareMgr) pushFansPresentInfoMsgToChannel(ctx context.Context, sendUser *accountPB.UserResp, itemCfg *presentPB.GetPresentConfigByIdResp,
	timeVal time.Time, channelId uint32, targetUserMap map[uint32]*account.User, bindChannel uint32, sendExtend *baseSendExtend) error {
	if 0 == channelId {
		return nil
	}
	sendTime := timeVal.UnixNano() / 1000000
	accountList := []string{}
	for _, user := range targetUserMap {
		tmpAccount := user.GetUsername()
		if ukwInfo, ok := sendExtend.ukwInfoMap[user.GetUid()]; ok {
			if ukwInfo.GetPrivilege() != nil {
				tmpAccount = ukwInfo.GetPrivilege().GetAccount()
			}
		}
		accountList = append(accountList, tmpAccount)
	}
	fansMsg := presentPB_.FansPresentMessage{Account: accountList, PresentName: itemCfg.GetItemConfig().GetName(), PresentIcon: itemCfg.GetItemConfig().GetIconUrl()}
	batchMsgContent, _ := proto.Marshal(&fansMsg)
	channelMsg := &channelPB_.ChannelBroadcastMsg{FromUid: sendUser.GetUid(), FromNick: sendUser.GetNickname(), FromAccount: sendUser.GetUsername(),
		ToChannelId: channelId, Time: uint64(sendTime), Type: uint32(channelPB_.ChannelMsgType_OFFICIAL_FANS_BATCH_SEND_PRESENT),
		Content: batchMsgContent, PbOptContent: batchMsgContent,
	}

	fillUserProfile(channelMsg, sendExtend)

	//err := s.expressCli.SendChannelBroadcastMsg(ctx, &channelMsg)

	channelMsgContent, _ := proto.Marshal(channelMsg)
	pushMessage := pushPb.PushMessage{Cmd: uint32(pushPb.PushMessage_CHANNEL_MSG_BRO), Content: channelMsgContent}

	pushMessageContent, _ := proto.Marshal(&pushMessage)
	notification := &pushPB.CompositiveNotification{
		Sequence: uint32(time.Now().Unix()),
		TerminalTypeList: []uint32{
			protocol.MobileAndroidTT,
			protocol.MobileIPhoneTT,
		},
		AppId:              uint32(protocol.TT),
		TerminalTypePolicy: pushclient.DefaultPolicy,
		ProxyNotification: &pushPB.ProxyNotification{
			Type:       uint32(pushPB.ProxyNotification_PUSH),
			Payload:    pushMessageContent,
			Policy:     pushPB.ProxyNotification_DEFAULT,
			ExpireTime: 86400,
		},
	}

	err := s.pushCli.PushMulticast(ctx, uint64(channelId), fmt.Sprintf("%d@channel", channelId), []uint32{}, notification)

	// 考虑到官频，要做一个转发；由于中断转播后还要求能看到抽奖结果，所以只能手动发一条推送
	if bindChannel != 0 {
		tranFansMsg := presentPB_.FansPresentMessage{Account: accountList, PresentName: itemCfg.GetItemConfig().GetName(), PresentIcon: itemCfg.GetItemConfig().GetIconUrl()}
		tranBatchMsgContent, _ := proto.Marshal(&tranFansMsg)
		tranChannelMsg := &channelPB_.ChannelBroadcastMsg{FromUid: sendUser.GetUid(), FromNick: sendUser.GetNickname(), FromAccount: sendUser.GetUsername(),
			ToChannelId: bindChannel, Time: uint64(sendTime), Type: uint32(channelPB_.ChannelMsgType_OFFICIAL_FANS_BATCH_SEND_PRESENT),
			Content: tranBatchMsgContent, PbOptContent: tranBatchMsgContent,
		}

		fillUserProfile(tranChannelMsg, sendExtend)

		//err := s.expressCli.SendChannelBroadcastMsg(ctx, &channelMsg)

		tranChannelMsgContent, _ := proto.Marshal(tranChannelMsg)
		tranPushMessage := pushPb.PushMessage{Cmd: uint32(pushPb.PushMessage_CHANNEL_MSG_BRO), Content: tranChannelMsgContent}

		tranPushMessageContent, _ := proto.Marshal(&tranPushMessage)
		tranNotification := &pushPB.CompositiveNotification{
			Sequence: uint32(time.Now().Unix()),
			TerminalTypeList: []uint32{
				protocol.MobileAndroidTT,
				protocol.MobileIPhoneTT,
			},
			AppId:              uint32(protocol.TT),
			TerminalTypePolicy: pushclient.DefaultPolicy,
			ProxyNotification: &pushPB.ProxyNotification{
				Type:       uint32(pushPB.ProxyNotification_PUSH),
				Payload:    tranPushMessageContent,
				Policy:     pushPB.ProxyNotification_DEFAULT,
				ExpireTime: 86400,
			},
		}

		err = s.pushCli.PushMulticast(ctx, uint64(bindChannel), fmt.Sprintf("%d@channel", bindChannel), []uint32{}, tranNotification)
	}

	log.InfoWithCtx(ctx, "pushFansPresentInfoMsgToChannel -- PushMulticast. uid:%d, channel_id:%d , bind_channel:%d, item_id:%d ,err :%v", sendUser.GetUid(),
		channelId, bindChannel, itemCfg.GetItemConfig().GetItemId(), err)
	return err
}
