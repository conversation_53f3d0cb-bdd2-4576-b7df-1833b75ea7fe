package server

import (
	"context"
	account "golang.52tt.com/clients/account-go"
	"golang.52tt.com/pkg/decoration"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	ga "golang.52tt.com/protocol/app"
	presentPB_ "golang.52tt.com/protocol/app/userpresent"
	"golang.52tt.com/protocol/common/status"
	accountPB "golang.52tt.com/protocol/services/account-go"
	pb "golang.52tt.com/protocol/services/present-middleware"
	richer_birthday "golang.52tt.com/protocol/services/richer-birthday"
	presentPB "golang.52tt.com/protocol/services/userpresent"
	"golang.52tt.com/services/present-middleware/rpc/client"
	"time"
)

func (s *ImPresentMiddlewareMgr) BeforeSendPresent(ctx context.Context, inter interface{}, outInter interface{}, sendExtend *baseSendExtend) (err error) {
	// 类型断言
	out, ok := outInter.(*pb.ImSendPresentResp)
	if !ok {
		log.ErrorWithCtx(ctx, "BeforeSendPresent -- para inter need *ImSendPresentResp type. ")
		return nil
	}
	in, ok := inter.(*pb.ImSendPresentReq)
	if !ok {
		log.ErrorWithCtx(ctx, "BeforeSendPresent -- para inter need *ImSendPresentResp type. ")
		return nil
	}
	if in.ServiceInfo == nil {
		in.ServiceInfo = &pb.ServiceCtrlInfo{}
	}
	sendExtend.nowTs = time.Now()
	out.ItemSource = in.ItemSource

	ctx = s.SetPresentGoCtx(ctx, in.GetSendUid())

	// 预处理，获得送礼要用的相关信息
	TargetUidList := []uint32{in.TargetUid}
	err = s.preSendPresent(ctx, sendExtend.nowTs, in.SendUid,
		TargetUidList, in.ItemId, 0, in.GetSendMethod(), sendExtend)
	if err != nil {
		log.ErrorWithCtx(ctx, "PresentMiddlewareMgr -- preSendPresent fail. uid:%v c:%v err:%v",
			in.SendUid, 0, err)
		return err
	}
	return err
}

func (s *ImPresentMiddlewareMgr) SendingPresent(ctx context.Context, inter interface{}, outInter interface{}, sendExtend *baseSendExtend) (err error) {

	// 类型断言
	_, ok := outInter.(*pb.ImSendPresentResp)
	if !ok {
		log.ErrorWithCtx(ctx, "SendingPresent -- para inter need *ImSendPresentResp type. ")
		return protocol.NewExactServerError(nil, status.ErrUserPresentServiceInfoInvalid)
	}
	in, ok := inter.(*pb.ImSendPresentReq)
	if !ok {
		log.ErrorWithCtx(ctx, "SendingPresent -- para inter need *ImSendPresentReq type. ")
		return protocol.NewExactServerError(nil, status.ErrUserPresentServiceInfoInvalid)
	}

	//冻结红钻/t豆

	sendExtend.remainTbeans, sendExtend.remainSource, sendExtend.realItemSource, err = s.tryFreeze(ctx, sendExtend, in.Count, in.ItemSource, in.SourceId, in.SendMethod)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendingPresent -- tryFreeze fail. uid:%v c:%v err:%v",
			in.SendUid, 0, err)
		return err
	}

	sendExtend.extendInfo = &ExtendInfo{targetInfoMap: map[uint32]*TargetExtendInfo{}}

	// 方便对账，传入送礼的item_source在这里转换一下
	sendItemSource := sendExtend.realItemSource
	if in.SendMethod == uint32(pb.PresentSendMethodType_PRESENT_TYPE_FELLOW) {
		sendItemSource = uint32(pb.PresentSourceType_PRESENT_SOURCE_FELLOW)
	}

	// 送礼人是否记录财富值
	ctx = context.WithValue(ctx, IsRecordSenderRichKey, s.isRecordRichMap(ctx, sendExtend.sendUser.GetUid(), sendExtend.targetUserMap))
	
	//赠送礼物
	sendExtend.sucOrders, err = s.sendPresent(ctx, sendExtend, in.ServiceInfo, in.Count, in.AppId, in.MarketId, sendItemSource, in.SendSource, 0, in.SendMethod, in.IsOptValid)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendingPresent -- __SendPresent fail. uid:%v ret:%v req:%v",
			in.SendUid, err, in)
		return err
	}
	log.ErrorWithCtx(ctx, "after send present extend info : %v", sendExtend.extendInfo)

	//礼物周边信息处理
	err = s.ProcPresentWatch(ctx, sendExtend, in.Count, in.IsOptValid)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendingPresent -- __ProcPresentWatch fail:%v. _req:%v", err, in)
	}

	//提交支付订单
	if uint32(pb.PresentSourceType_PRESENT_SOURCE_BUY) == sendExtend.realItemSource {
		err = s.commitPayOrder(ctx, sendExtend.sucOrders, in.SendMethod, in.ItemSource, sendExtend.presentConfig.GetItemConfig().GetPriceType())
	}
	return err
}

func (s *ImPresentMiddlewareMgr) AfterSendPresent(ctx context.Context, inter interface{}, outInter interface{}, sendExtend *baseSendExtend) (err error) {

	// 类型断言
	out, ok := outInter.(*pb.ImSendPresentResp)
	if !ok {
		log.ErrorWithCtx(ctx, "AfterSendPresent -- para inter need *SendPresentResp type. ")
		return nil
	}
	in, ok := inter.(*pb.ImSendPresentReq)
	if !ok {
		log.ErrorWithCtx(ctx, "AfterSendPresent -- para inter need *SendPresentReq type. ")
		return nil
	}

	consumeMap := make(map[uint32]uint32, 0)
	for _, order := range sendExtend.orderMap {
		consumeMap[order.targetUid] = sendExtend.presentConfig.GetItemConfig().GetPrice()
	}
	err = s.SetHighConsume(ctx, consumeMap, in.SendUid)
	if err != nil {
		log.ErrorWithCtx(ctx, "AfterSendPresent -- SetHighConsume err, uid %d ,  err : %v. ", in.SendUid, err)
		return nil
	}

	//oss v2不用了 v3在 userpresent
	//s.ossReport(ctx, sendUser, targetUserMap, channelSimpleInfo, nowTs, presentConfig, in.Count, isVaild, in.ItemSource,
	//	in.BatchType, uniqOrderId, in.SendSource, in.ServiceInfo.ClientType)

	////ConsumeSdkEvent kafka
	//activateChannel, _, err := s.accountCli.GetUserActivateInfo(ctx, in.SendUid)
	//devId := make([]byte, 0)
	//hex.Encode(in.ServiceInfo.DeviceId, devId)
	//err = s.sdkKfkProduce.ProduceNewConsumeSdkEvent(sendExtend.sendUser.GetUid(), sendExtend.nowTs, in.Count*sendExtend.presentConfig.GetItemConfig().GetPrice(),
	//	activateChannel, sendExtend.sendUser.GetUsername(), sendExtend.uniqOrderId, in.ServiceInfo.ClientIp, in.ServiceInfo.ClientType, string(devId))
	//
	//if err != nil {
	//	log.ErrorWithCtx(ctx, "ProduceNewConsumeSdkEvent err %v info %d", err, in.SendUid)
	//}

	sucUsers := make(map[uint32]*accountPB.UserResp)
	for _, item := range sendExtend.sucOrders {
		sucUsers[item.targetUid] = sendExtend.targetUserMap[item.targetUid]
	}

	// 上报生日礼物
	if in.GetShowImPreEffect() {
		for _, item := range sendExtend.sucOrders {

			go func() {
				tmpCtx, cancel := NewContextWithInfoTimeout(ctx, time.Millisecond*200)
				defer cancel()
				_, err := client.RicherBirthdayCli.ReportRicherBirthdayBless(tmpCtx, &richer_birthday.ReportRicherBirthdayBlessRequest{
					FromUid:      in.GetSendUid(),
					ToUid:        item.targetUid,
					BlessContent: in.GetPreEffectText(),
					BlessTime:    sendExtend.nowTs.Unix(),
					OrderId:      item.orderId,
					GiftId:       in.GetItemId(),
					Num:          1,
				})

				if err != nil {
					log.ErrorWithCtx(ctx, "ReportRicherBirthdayBless err %v info %d", err, in.SendUid)
				}
			}()

		}
	}

	//送礼后处理，填充动效、推送等
	s.FillRespAndPush(ctx, sendExtend.nowTs, in, sendExtend.sendUser, sucUsers, sendExtend.presentConfig,
		sendExtend.extendInfo, uint64(sendExtend.remainTbeans), sendExtend.remainSource, out, in.PresentTextType)
	return err
}

func (s *ImPresentMiddlewareMgr) FillRespAndPush(ctx context.Context, nowTs time.Time, in *pb.ImSendPresentReq, sendUser *accountPB.UserResp, sucUsers map[uint32]*account.User,
	presentConfig *presentPB.GetPresentConfigByIdResp, extendInfo *ExtendInfo, remainTbeans uint64, remainSource uint32, out *pb.ImSendPresentResp, imType uint32) {
	out.CurTbeans = remainTbeans
	out.SourceRemain = remainSource

	//送礼特效与动效
	showEffect, showEffectV2, FlowId := s.getPresentEffect(ctx, presentConfig.GetItemConfig(), in.Count)
	templateId, _ := s.getPresentDynamicEffectTemplate(ctx, sendUser.GetUid(), presentConfig.GetItemConfig(), in.Count)
	out.MsgInfo = &pb.PresentSendMsg{ItemInfo: &pb.PresentSendItemInfo{}}

	out.MsgInfo.ItemInfo.ItemId = presentConfig.GetItemConfig().GetItemId()
	out.MsgInfo.ItemInfo.Count = in.Count
	out.MsgInfo.ItemInfo.ShowEffect = showEffect
	out.MsgInfo.ItemInfo.ShowEffectV2 = showEffectV2
	out.MsgInfo.ItemInfo.FlowId = FlowId
	out.MsgInfo.ItemInfo.IsBatch = true
	out.MsgInfo.ItemInfo.SendType = in.SendType
	out.MsgInfo.ItemInfo.ShowBatchEffect = presentConfig.GetItemConfig().GetPriceType() != uint32(presentPB.PresentPriceType_PRESENT_PRICE_TBEAN)
	out.MsgInfo.ItemInfo.DynamicTemplateId = templateId
	out.MsgInfo.ItemInfo.PreEffectText = in.PreEffectText
	out.MsgInfo.ItemInfo.ShowImPreEffect = in.ShowImPreEffect

	if presentConfig.GetItemConfig().GetPriceType() != uint32(presentPB.PresentPriceType_PRESENT_PRICE_TBEAN) &&
		presentConfig.GetItemConfig().GetExtend().GetShowEffect() == 1 {
		out.MsgInfo.ItemInfo.ShowBatchEffect = presentConfig.GetItemConfig().GetPrice() < 10000
	}

	// 涂鸦送礼，im用不着，先放着

	//推送
	targetUidList := []uint32{}
	for _, user := range sucUsers {
		targetUidList = append(targetUidList, user.GetUid())
	}
	s.procPushEvent(ctx, sendUser, targetUidList, presentConfig, in.Count*uint32(len(sucUsers)), nowTs, extendInfo, sucUsers,
		0, out.MsgInfo.ItemInfo, 0, 0, out, imType, in.GetSendMethod())

	log.DebugWithCtx(ctx, "extend info : %v", extendInfo)
	out.MsgInfo.ChannelId = 0
	out.MsgInfo.SendTime = uint64(nowTs.UnixNano() / 1000000)
	out.MsgInfo.SendUid = sendUser.GetUid()
	out.MsgInfo.SendNickname = sendUser.GetNickname()
	out.MsgInfo.SendAccount = sendUser.GetUsername()

	out.MsgInfo.ExtendJson = extendInfo.userExtendJson

	if presentConfig.GetItemConfig().GetExtend().FlowId > 0 {
		out.MsgInfo.ItemInfo.DynamicTemplateId = 0
	}
}

func (s *ImPresentMiddlewareMgr) procPushEvent(ctx context.Context, sendUser *accountPB.UserResp, targetUids []uint32,
	itemCfg *presentPB.GetPresentConfigByIdResp, totalItemCount uint32, timeVal time.Time, extendInfo *ExtendInfo,
	successTargetUserMap map[uint32]*account.User, channelId uint32, sendItemInfo *pb.PresentSendItemInfo,
	batchType uint32, bindChannel uint32, out *pb.ImSendPresentResp, imType uint32, sendMethod uint32) {

	tmpGaPushBatchInfo := pb.PresentBatchInfoMsg{TargetList: []*pb.PresentBatchTargetInfo{}}
	tmpGaPushBatchInfo.ItemInfo = sendItemInfo
	tmpGaPushBatchInfo.ChannelId = channelId
	tmpGaPushBatchInfo.ItemId = itemCfg.GetItemConfig().ItemId
	tmpGaPushBatchInfo.BatchType = batchType
	tmpGaPushBatchInfo.SendTime = uint64(timeVal.UnixNano() / 1000000)
	tmpGaPushBatchInfo.SendUid = sendUser.GetUid()
	tmpGaPushBatchInfo.SendAccount = sendUser.GetUsername()
	tmpGaPushBatchInfo.SendNickname = sendUser.GetNickname()
	tmpGaPushBatchInfo.TotalItemCount = totalItemCount
	if itemCfg.GetItemConfig().GetExtend().GetFlowId() > 0 {
		tmpGaPushBatchInfo.ItemInfo.DynamicTemplateId = 0
	}

	for _, user := range successTargetUserMap {
		extend, ok := extendInfo.targetInfoMap[user.GetUid()]
		if ok {
			tmpGaPushBatchInfo.TargetList = append(tmpGaPushBatchInfo.TargetList, &pb.PresentBatchTargetInfo{Uid: user.GetUid(),
				Account: user.GetUsername(), Nickname: user.GetNickname(), ExtendJson: extend.userExtendJson})
		} else {
			tmpGaPushBatchInfo.TargetList = append(tmpGaPushBatchInfo.TargetList, &pb.PresentBatchTargetInfo{Uid: user.GetUid(),
				Account: user.GetUsername(), Nickname: user.GetNickname()})
		}
	}

	messageItemInfo := &presentPB_.PresentSendItemInfo{ItemId: sendItemInfo.ItemId, Count: sendItemInfo.Count, ShowBatchEffect: sendItemInfo.ShowBatchEffect,
		ShowEffect: sendItemInfo.ShowEffect, ShowEffectV2: sendItemInfo.ShowEffectV2, FlowId: sendItemInfo.FlowId, SendType: sendItemInfo.SendType,
		DynamicTemplateId: sendItemInfo.DynamicTemplateId, IsVisibleToSender: true, ShowImPreEffect: sendItemInfo.ShowImPreEffect, PreEffectText: sendItemInfo.PreEffectText}

	tmpSendProfile := &ga.UserProfile{
		Uid:          sendUser.GetUid(),
		Account:      sendUser.GetUsername(),
		Nickname:     sendUser.GetNickname(),
		AccountAlias: sendUser.GetAlias(),
		Sex:          uint32(sendUser.GetSex()),
	}

	log.DebugWithCtx(ctx, "procPushEvent -- targetUids. uid:%v, targetUids:[%v] , extendInfo.userExtendJson:[%v]", sendUser.GetUid(), targetUids, extendInfo.userExtendJson)

	for _, uid := range targetUids {
		tmpTargetProfile := &ga.UserProfile{
			Uid:          successTargetUserMap[uid].GetUid(),
			Account:      successTargetUserMap[uid].GetUsername(),
			Nickname:     successTargetUserMap[uid].GetNickname(),
			AccountAlias: successTargetUserMap[uid].GetAlias(),
			Sex:          uint32(successTargetUserMap[uid].GetSex()),
		}

		messageItemInfo.CustomTextJson = decoration.GetCustomPresentText(itemCfg.GetItemConfig(), tmpSendProfile, tmpTargetProfile)

		//推im消息
		s.WritePresentImToUser(ctx, sendUser, itemCfg, successTargetUserMap[uid], imType, messageItemInfo, sendMethod)
		//如果是特定礼物全服推送，im没有房间号，就不推全服了
		//s.PushPresentBreakingNewsToAll(ctx, sendUser, itemCfg, totalItemCount, timeVal, channelId, successTargetUserMap[uid])
	}
}
