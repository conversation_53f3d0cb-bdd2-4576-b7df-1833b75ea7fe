package server

import (
	"context"
	"fmt"
	apicenter "golang.52tt.com/clients/apicenter/apiserver"
	backpackFunc "golang.52tt.com/clients/backpack-func-card"
	missionlogic "golang.52tt.com/clients/missiongo"
	userpresent_go "golang.52tt.com/clients/userpresent-go"
	"golang.52tt.com/pkg/present/score_type"
	"golang.52tt.com/protocol/common/status"
	backpack_func_card "golang.52tt.com/protocol/services/backpack-func-card"
	numeric_go2 "golang.52tt.com/protocol/services/numeric-go"
	present_set "golang.52tt.com/protocol/services/present-set"
	userpresent "golang.52tt.com/protocol/services/userpresent-go"
	"reflect"
	"time"

	"github.com/go-redis/redis/v8"
	account "golang.52tt.com/clients/account-go"
	"golang.52tt.com/pkg/files"
	ga "golang.52tt.com/protocol/app"
	"golang.52tt.com/services/present-middleware/cache"
	"golang.52tt.com/services/present-middleware/models/async_queue"
	"golang.52tt.com/services/present-middleware/models/feishu"

	"golang.52tt.com/clients/anti"
	"golang.52tt.com/clients/appconfig"
	backpack "golang.52tt.com/clients/backpack-base"
	"golang.52tt.com/clients/channel"
	channel_msg_express "golang.52tt.com/clients/channel-msg-express"
	personalization "golang.52tt.com/clients/channel-personalization"
	"golang.52tt.com/clients/channelmic"
	"golang.52tt.com/clients/channelol"
	"golang.52tt.com/clients/cooldown"
	"golang.52tt.com/clients/currency"
	"golang.52tt.com/clients/exp"
	"golang.52tt.com/clients/guild"
	"golang.52tt.com/clients/guildmemberlv"
	HeadImage "golang.52tt.com/clients/headimage"
	imstranger "golang.52tt.com/clients/imstranger-go"
	missionTL "golang.52tt.com/clients/missiontimeline"
	"golang.52tt.com/clients/numeric"
	numeric_go "golang.52tt.com/clients/numeric-go"
	offcialLive "golang.52tt.com/clients/official-live-channel"
	publicnotice "golang.52tt.com/clients/public-notice"
	pushclient "golang.52tt.com/clients/push-notification/v2"
	pushv3client "golang.52tt.com/clients/push-notification/v3/push"
	"golang.52tt.com/clients/seqgen/v2"
	Timeline "golang.52tt.com/clients/timeline"
	unifyPay "golang.52tt.com/clients/unified_pay"
	"golang.52tt.com/clients/userscore"
	vipprivilege "golang.52tt.com/clients/vipprivilegesvr-go"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	channelPB_ "golang.52tt.com/protocol/app/channel"
	accountPB "golang.52tt.com/protocol/services/account-go"
	channelPB "golang.52tt.com/protocol/services/channelsvr"
	pb "golang.52tt.com/protocol/services/present-middleware"
	presentPB "golang.52tt.com/protocol/services/userpresent"
	commonKFK "golang.52tt.com/services/common/kafka"
	"golang.52tt.com/services/present-middleware/conf"
	"golang.52tt.com/services/present-middleware/models/breaker"
	"golang.52tt.com/services/present-middleware/models/pay"
	"golang.52tt.com/services/present-middleware/models/presentPush"
	"golang.52tt.com/services/present-middleware/rpc/client"
	"golang.52tt.com/services/present-middleware/utils"
	"golang.52tt.com/services/ugc/common/kafka_produce"

	"sync"
)

type RecordRichKey string

const IsRecordSenderRichKey = RecordRichKey("isRecordSenderRichKey")
const PushDelayTime = uint32(22) * 1000 // 毫秒数

type PresentMiddleware struct {
	sc *conf.ServiceConfigT

	presentSendDelegate *SendPresentDelegate

	stop        chan interface{}
	PushFactory *presentPush.Factory
	//wg   sync.WaitGroup
}

type PresentMiddlewareMgr struct {
	accountCli         *account.Client
	unifyPayCli        *unifyPay.Client
	backpackCli        *backpack.Client
	backpackFuncCli    *backpackFunc.Client
	guildCli           *guild.Client
	antiCli            *anti.Client
	cooldownCli        *cooldown.Client
	channelCli         *channel.Client
	numericCli         *numeric.Client
	numericGoCli       *numeric_go.Client
	vipprivilegeCli    *vipprivilege.Client
	missionCli         *missionlogic.Client
	guildmemberlvCli   *guildmemberlv.Client
	MissionHelperCli   *missionHelper
	pushCli            *pushclient.Client
	imstrangerCli      *imstranger.Client
	channelolCli       *channelol.Client
	offcialLiveCli     *offcialLive.Client
	personalizationCli *personalization.Client
	seqgenCli          *seqgen.Client
	headImageCli       *HeadImage.Client
	expressCli         *channel_msg_express.Client
	timelineCLi        *Timeline.Client
	appconfigCli       *appconfig.Client
	pushv3Cli          *pushv3client.Client
	channelMicCli      *channelmic.Client
	publicNoticeCli    *publicnotice.Client

	//sdkKfkProduce      *event.KafkaProduce
	levelupKfkProducer *kafka_produce.KafkaProduce

	PayFactory    *pay.Factory
	PushFactory   *presentPush.Factory
	GeneralConfig *conf.GeneralConfig
	AsyncQueue    *async_queue.Queue
	ScoreTypeMgr  *score_type.ScoreTypeMgr
}

type BatchPresentMiddlewareMgr struct {
	PresentMiddlewareMgr
}

type AllMicPresentMiddlewareMgr struct {
	PresentMiddlewareMgr
}

type ImPresentMiddlewareMgr struct {
	PresentMiddlewareMgr
}

type ChannelPresentMiddlewareMgr struct {
	PresentMiddlewareMgr
}

type FellowPresentMiddlewareMgr struct {
	PresentMiddlewareMgr
}

type MagicPresentMiddlewareMgr struct {
	PresentMiddlewareMgr
}

type CommonPresentMiddlewareMgr struct {
	PresentMiddlewareMgr
}

type SetPresentMiddlewareMgr struct {
	PresentMiddlewareMgr
}

type baseSendExtend struct {
	sendUser          *accountPB.UserResp
	targetUserMap     map[uint32]*accountPB.UserResp
	presentConfig     *presentPB.GetPresentConfigByIdResp
	uniqOrderId       string
	orderMap          map[string]*PayOrderInfo
	channelSimpleInfo *channelPB.GetChannelSimpleInfoResp
	bgPresentSource   uint32
	remainTbeans      int64
	remainCurrency    int64
	remainSource      uint32
	realItemSource    uint32
	expireTime        uint32 // 背包物品的过期时间
	nowTs             time.Time
	sucOrders         map[string]*PayOrderInfo
	extendInfo        *ExtendInfo
	presentConfigMap  map[uint32]*presentPB.StPresentItemConfig
	totalPrice        uint32
	userBonusCardMap  map[uint32][]*backpack_func_card.FuncCardCfg
	ukwInfoMap        map[uint32]*ga.UserProfile
	fake2RealUidMap   map[uint32]uint32
	real2FakeUidMap   map[uint32]uint32
	primaryCustomGift *presentPB.StPresentItemConfig // 专属定制礼物的主礼物id
	ScoreTypeMap      map[uint32]uint32              // 用户获取的积分类型，uid - type
	sucUsers          map[uint32]*account.User

	// 帝王套才用这两个
	setId         uint32
	setTargetUser *account.User
	setConfig     *present_set.EmperorSetConfig

	timeCollect *utils.TimeCollect
}

func NewPresentMiddleware(ctx context.Context, config config.Configer) (*PresentMiddleware, error) {
	sc := &conf.ServiceConfigT{}
	err := sc.Parse(ctx, config)
	if err != nil {
		return nil, err
	}

	// 初始化redis
	redisClient := redis.NewClient(&redis.Options{
		Network:            sc.RedisConfig.Protocol,
		Addr:               sc.RedisConfig.Addr(),
		PoolSize:           sc.RedisConfig.PoolSize,
		IdleCheckFrequency: sc.RedisConfig.IdleCheckFrequency(),
		DB:                 sc.RedisConfig.DB,
		DialTimeout:        time.Second * 10,
		ReadTimeout:        time.Second * 10,
		WriteTimeout:       time.Second * 10,
	})
	cache.InitPresentMiddlewareCache(redisClient)
	log.DebugWithCtx(ctx, "NewClient %v", redisClient)

	PushFactory := presentPush.NewPushMgr()
	presentMiddlewareMgr, err := NewPresentMiddlewareMgr(ctx, sc, PushFactory)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewPresentMiddlewareMgr err %v", sc)
		return nil, err
	}

	presentMiddlewareMgr.ScoreTypeMgr, err = score_type.NewScoreTypeMgr()
	if err != nil {
		log.ErrorWithCtx(ctx, "NewScoreTypeMgr err %v", sc)
		return nil, err
	}

	batchPresentMiddlewareMgr := NewBatchPresentMiddlewareMgr(presentMiddlewareMgr)
	allMicPresentMiddlewareMgr := NewAllMicPresentMiddlewareMgr(presentMiddlewareMgr)
	imPresentMiddlewareMgr := NewImPresentMiddlewareMgr(presentMiddlewareMgr)
	FellowPresentMiddlewareMgr := NewFellowPresentMiddlewareMgr(presentMiddlewareMgr)
	MagicPresentMiddlewareMgr := NewMagicPresentMiddlewareMgr(presentMiddlewareMgr)
	CommonPresentMiddlewareMgr := NewCommonPresentMiddlewareMgr(presentMiddlewareMgr)
	SetPresentMiddlewareMgr := NewSetPresentMiddlewareMgr(presentMiddlewareMgr)

	delegate := newSendPresentDelegate()
	delegate.Register("batch", batchPresentMiddlewareMgr)
	delegate.Register("allMic", allMicPresentMiddlewareMgr)
	delegate.Register("im", imPresentMiddlewareMgr)
	delegate.Register("fellow", FellowPresentMiddlewareMgr)
	delegate.Register("magic", MagicPresentMiddlewareMgr)
	delegate.Register("common", CommonPresentMiddlewareMgr)
	delegate.Register("set", SetPresentMiddlewareMgr)
	s := &PresentMiddleware{
		sc:   sc,
		stop: make(chan interface{}),

		presentSendDelegate: delegate,
		PushFactory:         PushFactory,
	}
	return s, nil
}

func NewPresentMiddlewareMgr(ctx context.Context, sc *conf.ServiceConfigT, factory *presentPush.Factory) (*PresentMiddlewareMgr, error) {

	accountCli, err := account.NewClient()
	if err != nil {
		return nil, err
	}
	unifyPayCli, err := unifyPay.NewClient()
	if err != nil {
		return nil, err
	}
	backpackCli, _ := backpack.NewClient()
	backpackFuncCli, _ := backpackFunc.NewClient()
	guildCli := guild.NewClient()
	antiCli := anti.NewClient()
	cooldownCli := cooldown.NewClient()
	channelCli := channel.NewClient()
	numericCli := numeric.NewClient()
	numericGoCli, err := numeric_go.NewClient()
	if err != nil {
		return nil, err
	}
	vipprivilegeCli, _ := vipprivilege.NewClient()
	missionCli, _ := missionlogic.NewClient()
	guildmemberlvCli := guildmemberlv.NewClient()
	imstrangerCli, err := imstranger.NewClient()
	if err != nil {
		return nil, err
	}
	channelolCli := channelol.NewClient()
	offcialLiveCli, err := offcialLive.NewClient()
	if err != nil {
		return nil, err
	}
	personalizationCli, err := personalization.NewClient()
	if err != nil {
		return nil, err
	}
	headImageCli := HeadImage.NewClient()
	expressCli, err := channel_msg_express.NewClient()
	if err != nil {
		return nil, err
	}

	expCli := exp.NewClient()
	currCli := currency.NewClient()
	tlCli := missionTL.NewClient()
	seqgenCli, err := seqgen.NewClient()
	if err != nil {
		return nil, err
	}
	timelineCLi := Timeline.NewClient()

	scoreCLi := userscore.NewClient()
	apiCenterClient := apicenter.NewClient()

	MissionHelperCli := NewMissionHelperCli(expCli, currCli, tlCli, seqgenCli, timelineCLi, numericGoCli, scoreCLi, apiCenterClient)

	appconfigCli := appconfig.NewClient()

	pushCli, err := pushclient.NewClient()
	if err != nil {
		return nil, err
	}
	pushv3Cli, err := pushv3client.NewClient()
	if err != nil {
		return nil, err
	}

	channelMicClient := channelmic.NewClient()
	publicNoticeCli, err := publicnotice.NewClient()
	if err != nil {
		return nil, err
	}
	//sdkKfkProduce := event.NewKafkaProduce(ctx, strings.Split(sc.KafkaConfig.Brokers, ","), sc.KafkaConfig.ClientID, sc.KafkaConfig.Topics)
	fmt.Println(sc.LevelupKafkaConfig)

	levelupKfkProduce, _ := commonKFK.NewKFKProducer(sc.LevelupKafkaConfig)

	PayFactory := pay.NewFactory()

	// 初始化Client
	if err := client.Setup(); err != nil {
		log.ErrorWithCtx(ctx, "New client failed %v", err)
		return nil, err
	}

	// 初始化熔断器
	if err := breaker.Setup(ctx); err != nil {
		log.ErrorWithCtx(ctx, "New breaker failed %v", err)
		return nil, err
	}

	// 初始化通用配置
	gc := &conf.GeneralConfig{}
	gc.Load()

	reporter := feishu.NewFeiShuReporterV2(gc.GetPresentConfig().FeishuUrl, gc.GetPresentConfig().Env)

	s := &PresentMiddlewareMgr{
		accountCli:         accountCli,
		unifyPayCli:        unifyPayCli,
		backpackCli:        backpackCli,
		backpackFuncCli:    backpackFuncCli,
		guildCli:           guildCli,
		antiCli:            antiCli,
		cooldownCli:        cooldownCli,
		channelCli:         channelCli,
		numericCli:         numericCli,
		numericGoCli:       numericGoCli,
		vipprivilegeCli:    vipprivilegeCli,
		missionCli:         missionCli,
		guildmemberlvCli:   guildmemberlvCli,
		MissionHelperCli:   MissionHelperCli,
		pushCli:            pushCli,
		imstrangerCli:      imstrangerCli,
		channelolCli:       channelolCli,
		offcialLiveCli:     offcialLiveCli,
		personalizationCli: personalizationCli,
		seqgenCli:          seqgenCli,
		headImageCli:       headImageCli,
		expressCli:         expressCli,
		timelineCLi:        timelineCLi,
		pushv3Cli:          pushv3Cli,
		channelMicCli:      channelMicClient,
		publicNoticeCli:    publicNoticeCli,
		//sdkKfkProduce:      sdkKfkProduce,
		appconfigCli:       appconfigCli,
		PayFactory:         PayFactory,
		PushFactory:        factory,
		levelupKfkProducer: levelupKfkProduce,
		GeneralConfig:      gc,
		AsyncQueue: async_queue.NewQueue(int(gc.GetPresentConfig().QueueSize), int(gc.GetPresentConfig().Worker),
			reporter, gc.GetPresentConfig().ErrLimit),
	}

	go s.maintain()

	return s, nil
}

func (s *PresentMiddlewareMgr) maintain() {
	watch := files.NewFileModifyWatch(conf.GeneralCfgFile, time.Second)
	go watch.Start(func() {
		s.GeneralConfig.Load()
	})

	presentWatch := files.NewFileModifyWatch(conf.PresentMiddlewareCfgFile, time.Second)
	go presentWatch.Start(func() {
		s.GeneralConfig.Load()
	})

}

func NewBatchPresentMiddlewareMgr(mgr *PresentMiddlewareMgr) *BatchPresentMiddlewareMgr {

	return &BatchPresentMiddlewareMgr{*mgr}
}

func NewAllMicPresentMiddlewareMgr(mgr *PresentMiddlewareMgr) *AllMicPresentMiddlewareMgr {

	return &AllMicPresentMiddlewareMgr{*mgr}
}

func NewImPresentMiddlewareMgr(mgr *PresentMiddlewareMgr) *ImPresentMiddlewareMgr {

	return &ImPresentMiddlewareMgr{*mgr}
}

func NewChannelPresentMiddlewareMgr(mgr *PresentMiddlewareMgr) *ChannelPresentMiddlewareMgr {

	return &ChannelPresentMiddlewareMgr{*mgr}
}

func NewFellowPresentMiddlewareMgr(mgr *PresentMiddlewareMgr) *FellowPresentMiddlewareMgr {

	return &FellowPresentMiddlewareMgr{*mgr}
}

func NewMagicPresentMiddlewareMgr(mgr *PresentMiddlewareMgr) *MagicPresentMiddlewareMgr {

	return &MagicPresentMiddlewareMgr{*mgr}
}

func NewCommonPresentMiddlewareMgr(mgr *PresentMiddlewareMgr) *CommonPresentMiddlewareMgr {
	return &CommonPresentMiddlewareMgr{*mgr}
}

func NewSetPresentMiddlewareMgr(mgr *PresentMiddlewareMgr) *SetPresentMiddlewareMgr {
	return &SetPresentMiddlewareMgr{*mgr}
}

type PayOrderInfo struct {
	targetUid  uint32
	giftId     uint32
	count      uint32
	orderId    string
	payOrderId string
	tbTime     string
	dealToken  string
}

type TargetExtendInfo struct {
	uid                    uint32
	baseCharm              uint32 // 实际增加的魅力值的基础值(无加成)
	realAddCharm           uint32 // 实际增加的魅力值
	recvUserCurrCharmValue uint64 // 送礼后 操作者 当前魅力值

	bIsSendUserRichLevelChanged bool // 收礼后 操作者 的魅力等级有没有变化

	userExtendJson string // 收礼人的信息
	//charmBonusMsg  string // 魅力经验卡buff

	score uint32 // 收礼人的积分信息
}

type ExtendInfo struct {
	baseRichValue         uint32 // 实际增加的土豪值的基础值(无加成)
	realAddRichValue      uint32 // 实际增加的土豪值
	sendUserCurrRichValue uint64 // 送礼后 操作者 当前土豪值

	bIsSendUserRichLevelChanged bool // 送礼后 操作者 的土豪等级有没有变化

	userExtendJson    string // 送礼人的信息
	richValueBonusMsg string // 财富经验卡buff

	targetInfoMap   map[uint32]*TargetExtendInfo
	targetInfoMutex sync.Mutex

	sendInfoMap        map[uint32]*jsonSendUser                // 1对多，每种礼物的送礼财富值
	multiTargetInfoMap map[uint32]map[uint32]*TargetExtendInfo // 如果有多种礼物

	emperorSetSendUser   *jsonSendUser
	emperorSetTargetUser *jsonTargetUser
}

type UserBonusCardEffect struct {
	uid        uint32
	addValue   uint32
	finalValue uint32
	bonusMsg   string
}

//type sendPresentResp struct {
//	uid uint32
//	err error
//}
//
//type preSendResp struct {
//	name string
//	err  error
//}

type jsonSendUser struct {
	RichValue                int32  `json:"rich_value"`
	RichValuePrefix          string `json:"rich_value_prefix"`
	MemberContribution       int32  `json:"member_contribution"`
	MemberContributionPrefix string `json:"member_contribution_prefix"`
	BonusMsg                 string `json:"bonus_msg"`
}

type jsonTargetUser struct {
	Score       int32  `json:"score"`
	ScorePrefix string `json:"score_prefix"`
	Charm       int32  `json:"charm"`
	CharmPrefix string `json:"charm_prefix"`
}

// SendPresent 普通的房间送礼，最基本的送礼方法
func (s *PresentMiddleware) SendPresent(ctx context.Context, in *pb.SendPresentReq) (out *pb.SendPresentResp, err error) {
	log.DebugWithCtx(ctx, "SendPresent Begin ,req :%v", in)
	out = &pb.SendPresentResp{
		MsgInfo: []*pb.PresentSendMsg{},
	}

	err = s.presentSendDelegate.PresentSend(ctx, in, out, "common")
	if err != nil {
		log.ErrorWithCtx(ctx, "SendPresent --PresentSend err. req:%v , err: %v", in, err)
		return out, protocol.ToServerError(err)
	}
	return out, nil
}

// BatchSendPresent 官频抽奖的批量送礼
func (s *PresentMiddleware) BatchSendPresent(ctx context.Context, in *pb.BatchSendPresentReq) (out *pb.BatchSendPresentResp, err error) {
	log.DebugWithCtx(ctx, "BatchSendPresent Begin ,req :%v", in)
	out = &pb.BatchSendPresentResp{MsgInfo: &pb.PresentBatchInfoMsg{
		ItemInfo: &pb.PresentSendItemInfo{},
	},
		ItemInfo: &pb.PresentSendItemInfo{},
	}

	err = s.presentSendDelegate.PresentSend(ctx, in, out, "batch")
	if err != nil {
		log.ErrorWithCtx(ctx, "SendPresent --BatchSendPresent err. req:%v , err: %v", in, err)
		return out, protocol.ToServerError(err)
	}
	return out, nil
}

// ImSendPresent im送礼
func (s *PresentMiddleware) ImSendPresent(ctx context.Context, in *pb.ImSendPresentReq) (out *pb.ImSendPresentResp, err error) {
	log.DebugWithCtx(ctx, "ImSendPresent Begin ,req :%v", in)
	out = &pb.ImSendPresentResp{
		MsgInfo: &pb.PresentSendMsg{
			ItemInfo: &pb.PresentSendItemInfo{},
		},
	}

	err = s.presentSendDelegate.PresentSend(ctx, in, out, "im")
	if err != nil {
		log.ErrorWithCtx(ctx, "SendPresent --ImSendPresent err. req:%v , err: %v", in, err)
		return out, protocol.ToServerError(err)
	}
	return out, nil
}

// AllMicSendPresent 全麦送礼
func (s *PresentMiddleware) AllMicSendPresent(ctx context.Context, in *pb.AllMicSendPresentReq) (out *pb.AllMicSendPresentResp, err error) {
	log.DebugWithCtx(ctx, "AllMicSendPresent Begin ,req :%v", in)
	out = &pb.AllMicSendPresentResp{
		MsgInfo: &pb.PresentBatchInfoMsg{
			ItemInfo: &pb.PresentSendItemInfo{},
		},
		ItemInfo: &pb.PresentSendItemInfo{
			DrawPresentPic: &pb.DrawPresentPicture{},
		},
	}

	err = s.presentSendDelegate.PresentSend(ctx, in, out, "im")
	if err != nil {
		log.ErrorWithCtx(ctx, "SendPresent --AllMicSendPresent err. req:%v , err: %v", in, err)
		return out, protocol.ToServerError(err)
	}
	return out, nil
}

// FellowSendPresent 亲密关系的邀请信物
func (s *PresentMiddleware) FellowSendPresent(ctx context.Context, in *pb.FellowSendPresentReq) (out *pb.FellowSendPresentResp, err error) {
	log.DebugWithCtx(ctx, "FellowSendPresent Begin ,req :%v", in)
	out = &pb.FellowSendPresentResp{
		MsgInfo: &pb.PresentSendMsg{
			ItemInfo: &pb.PresentSendItemInfo{},
		},
	}

	err = s.presentSendDelegate.PresentSend(ctx, in, out, "fellow")
	if err != nil {
		log.ErrorWithCtx(ctx, "SendPresent --FellowSendPresent err. req:%v , err: %v", in, err)
		return out, protocol.ToServerError(err)
	}
	return out, nil
}

// MagicSendPresent 幸运礼物
func (s *PresentMiddleware) MagicSendPresent(ctx context.Context, in *pb.MagicSendPresentReq) (out *pb.MagicSendPresentResp, err error) {
	log.DebugWithCtx(ctx, "FellowSendPresent Begin ,req :%v", in)
	out = &pb.MagicSendPresentResp{
		MsgInfo: &pb.MagicPresentInfoMsg{},
	}

	err = s.presentSendDelegate.PresentSend(ctx, in, out, "magic")
	if err != nil {
		log.ErrorWithCtx(ctx, "SendPresent --MagicSendPresent err. req:%v , err: %v", in, err)
		return out, protocol.ToServerError(err)
	}
	return out, nil
}

// FixBackpackOrder 订单修复接口
func (s *PresentMiddleware) FixBackpackOrder(ctx context.Context, in *pb.FixBackpackOrderReq) (out *pb.FixBackpackOrderResp, err error) {
	log.DebugWithCtx(ctx, "FixPresentOrder Begin ,req :%v", in)
	out = &pb.FixBackpackOrderResp{}
	resp, err := client.BackpackCli.GetUseItemOrderInfo(ctx, in.GetOrderId())
	if err != nil {
		log.ErrorWithCtx(ctx, "FixPresentOrder GetUseItemOrderInfo err , in :%v ,err:%v", in, err)
		return out, err
	}

	channelInfo, err := client.ChannelCli.GetChannelSimpleInfo(ctx, 0, resp.GetUseOrderExtraInfo().GetChannelId())
	if err != nil {
		log.ErrorWithCtx(ctx, "FixPresentOrder GetChannelSimpleInfo err , in :%v ,err:%v", in, err)
		return out, err
	}

	//1 : 公会房  4：公会公开厅（娱乐房）
	channelGuildId := uint32(0)
	if channelInfo.GetChannelType() == uint32(channelPB_.ChannelType_GUILD_TYPE) || channelInfo.GetChannelType() == uint32(channelPB_.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE) {
		channelGuildId = channelInfo.GetBindId()
	}

	itemInfo, err := client.PresentCli.GetPresentConfigById(ctx, resp.GetUseOrderDetail().GetSourceId())
	if err != nil {
		log.ErrorWithCtx(ctx, "FixPresentOrder GetPresentConfigById err , in :%v ,err:%v", in, err)
		return out, err
	}

	numericLock, err := client.NumericGoCli.BatchGetUserNumericLock(ctx, &numeric_go2.BatchGetUserNumericLockReq{
		UidList: []uint32{resp.GetUseOrderDetail().GetUid()},
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "FixPresentOrder GetUserRichSwitch err , in :%v ,err:%v", in, err)
		return out, err
	}

	richValue := itemInfo.GetItemConfig().GetPrice() * resp.GetUseOrderDetail().GetUseCount()
	charmValue := itemInfo.GetItemConfig().GetPrice() * resp.GetUseOrderDetail().GetUseCount()

	if itemInfo.GetItemConfig().GetPriceType() == uint32(userpresent.PresentPriceType_PRESENT_PRICE_TBEAN) && numericLock.GetLockMap()[resp.GetUseOrderDetail().GetUid()].BeanCharmLock == numeric_go2.LockStatus_LOCK_STATUS_ENABLE {
		richValue = 0
	}

	if itemInfo.GetItemConfig().GetPriceType() == uint32(userpresent.PresentPriceType_PRESENT_PRICE_RED_DIAMOND) && numericLock.GetLockMap()[resp.GetUseOrderDetail().GetUid()].DiamondRichLock == numeric_go2.LockStatus_LOCK_STATUS_ENABLE {
		richValue = 0
	}

	if itemInfo.GetItemConfig().GetPriceType() == uint32(userpresent.PresentPriceType_PRESENT_PRICE_TBEAN) && numericLock.GetLockMap()[resp.GetUseOrderDetail().GetUid()].BeanCharmLock == numeric_go2.LockStatus_LOCK_STATUS_ENABLE {
		charmValue = 0
	}

	if itemInfo.GetItemConfig().GetPriceType() == uint32(userpresent.PresentPriceType_PRESENT_PRICE_RED_DIAMOND) && numericLock.GetLockMap()[resp.GetUseOrderDetail().GetUid()].DiamondCharmLock == numeric_go2.LockStatus_LOCK_STATUS_ENABLE {
		charmValue = 0
	}

	dealToken, err := UpdateDealTokenInfo(ctx, resp.GetUseOrderExtraInfo().GetDealToken(), resp.GetUseOrderDetail().GetOrderId(), false)
	if err != nil {
		log.ErrorWithCtx(ctx, "FixPresentOrder UpdateDealTokenInfo err , in :%v ,err:%v", in, err)
		return out, err
	}

	err = client.PresentCli.SendPresent(ctx, &presentPB.SendPresentReq{
		Uid:              resp.GetUseOrderDetail().GetUid(),
		TargetUid:        resp.GetUseOrderExtraInfo().GetTargetUid(),
		OrderId:          resp.GetUseOrderDetail().GetOrderId(),
		ItemId:           resp.GetUseOrderDetail().GetUserItemId(),
		ChannelId:        channelInfo.GetChannelId(),
		GuildId:          channelGuildId,
		ItemCount:        resp.GetUseOrderDetail().GetUseCount(),
		AddCharm:         charmValue,
		SendTime:         resp.GetUseOrderDetail().GetCreateTime(),
		ItemConfig:       itemInfo.GetItemConfig(),
		OptInvalid:       true,
		AsyncFlag:        false,
		UserFromIp:       "",
		ChannelType:      channelInfo.GetChannelType(),
		ItemSource:       uint32(pb.PresentSourceType_PRESENT_SOURCE_PACKAGE),
		ChannelName:      channelInfo.GetName(),
		ChannelDisplayId: channelInfo.GetDisplayId(),
		SendSource:       uint32(pb.PresentSendSourceType_E_SEND_SOURCE_GIFT_TURNTABLE),
		SendPlatform:     0,
		BatchType:        uint32(pb.PresentBatchSendType_PRESENT_SOURCE_NONE),
		AppId:            0,
		MarketId:         0,
		AddRich:          richValue,
		SendMethod:       uint32(pb.PresentSendMethodType_PRESENT_TYPE_ROOM),
		BindChannelId:    0,
		DealToken:        dealToken,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "FixPresentOrder SendPresent err , in :%v ,err:%v", in, err)
		return out, err
	}

	return out, nil
}

func (s *PresentMiddleware) ShutDown() {

}

// PresentMiddlewareSender 弄了个代理，用来区分调用哪一种送礼
// 要新增送礼方式的话，实现接口里的三种方法
// 用类型断言区分输入输出 ，然后在newserver地方注册一下就好了
type PresentMiddlewareSender interface {
	BeforeSendPresent(ctx context.Context, inter interface{}, outInter interface{}, sendExtend *baseSendExtend) (err error)
	SendingPresent(ctx context.Context, inter interface{}, outInter interface{}, sendExtend *baseSendExtend) (err error)
	AfterSendPresent(ctx context.Context, inter interface{}, outInter interface{}, sendExtend *baseSendExtend) (err error)
}

type SendPresentDelegate struct {
	SendPresentHandlers map[string]PresentMiddlewareSender
}

func (me *SendPresentDelegate) Register(cls string, sender PresentMiddlewareSender) {
	me.SendPresentHandlers[cls] = sender
}

func newSendPresentDelegate() *SendPresentDelegate {
	it := &SendPresentDelegate{
		SendPresentHandlers: make(map[string]PresentMiddlewareSender, 16),
	}

	return it
}

func (me *SendPresentDelegate) PresentSend(ctx context.Context, in interface{}, out interface{}, typeString string) (err error) {
	handler, ok := me.SendPresentHandlers[typeString]
	if !ok {
		log.DebugWithCtx(ctx, "PresentSend - no this send type")
		return err
	}
	log.DebugWithCtx(ctx, "%v", reflect.TypeOf(handler))

	sendExtend := &baseSendExtend{}

	sendExtend.timeCollect = utils.InitTimeCollect(true)

	defer func() {
		sendExtend.timeCollect.AddTimeEvent("PresentSend end")
		sendExtend.timeCollect.ShowTimeCost(1000)
	}()

	// 送礼前的一些操作，获取送礼相关信息
	err = handler.BeforeSendPresent(ctx, in, out, sendExtend)
	if err != nil {
		log.ErrorWithCtx(ctx, "presentSendBase -- BeforeSendPresent fail. err:%v", err)
		return err
	}
	sendExtend.timeCollect.AddTimeEvent("BeforeSendPresent")

	// 送礼过程，包括冻结 - 送礼 - 处理数值变化 - 支付订单
	err = handler.SendingPresent(ctx, in, out, sendExtend)
	if err != nil {
		log.ErrorWithCtx(ctx, "presentSendBase -- SendingPresent fail. err:%v", err)
		return err
	}
	sendExtend.timeCollect.AddTimeEvent("SendingPresent")

	// 送礼后的一些变动，包括陌生人、推送等
	err = handler.AfterSendPresent(ctx, in, out, sendExtend)
	if err != nil {
		log.ErrorWithCtx(ctx, "presentSendBase -- AfterSendPresent fail. err:%v", err)
		return err
	}
	sendExtend.timeCollect.AddTimeEvent("AfterSendPresent")

	log.ErrorWithCtx(ctx, "DEBUG: PresentMiddlewareMgr : resp : %v", out)
	return nil
}

type isRecordMap struct {
	isRecordRichTbeanMap       map[uint32]bool
	isRecordCharmTbeanMap      map[uint32]bool
	isRecordRichRedDiamondMap  map[uint32]bool
	isRecordCharmRedDiamondMap map[uint32]bool
}

func (s *PresentMiddlewareMgr) isRecordRichMap(ctx context.Context, uid uint32, targetUserMap map[uint32]*account.User) *isRecordMap {
	isRecordNumricMap := &isRecordMap{}
	uidList := make([]uint32, 0)
	for _, tmp := range targetUserMap {
		uidList = append(uidList, tmp.GetUid())
	}
	uidList = append(uidList, uid)

	isRecordRichTbeanMap, isRecordCharmTbeanMap, isRecordRichRedDiamondMap, isRecordCharmRedDiamondMap := s.isRecordRich(ctx, uidList)
	isRecordNumricMap.isRecordRichTbeanMap = isRecordRichTbeanMap
	isRecordNumricMap.isRecordCharmTbeanMap = isRecordCharmTbeanMap
	isRecordNumricMap.isRecordRichRedDiamondMap = isRecordRichRedDiamondMap
	isRecordNumricMap.isRecordCharmRedDiamondMap = isRecordCharmRedDiamondMap

	return isRecordNumricMap
}

// 是否记录财富值
func (s *PresentMiddlewareMgr) isRecordRich(ctx context.Context, uidList []uint32) (isRecordRichTbeanMap, isRecordCharmTbeanMap, isRecordRichRedDiamondMap, isRecordCharmRedDiamondMap map[uint32]bool) {
	tmpCtx, cancel := NewContextWithInfoTimeout(ctx, 200*time.Millisecond)
	defer cancel()

	// 先全部置为true
	isRecordRichTbeanMap = make(map[uint32]bool)
	for _, tmp := range uidList {
		isRecordRichTbeanMap[tmp] = true
	}
	isRecordCharmTbeanMap = make(map[uint32]bool)
	for _, tmp := range uidList {
		isRecordCharmTbeanMap[tmp] = true
	}
	isRecordRichRedDiamondMap = make(map[uint32]bool)
	for _, tmp := range uidList {
		isRecordRichRedDiamondMap[tmp] = true
	}
	isRecordCharmRedDiamondMap = make(map[uint32]bool)
	for _, tmp := range uidList {
		isRecordCharmRedDiamondMap[tmp] = true
	}

	resp, err := client.NumericGoCli.BatchGetUserNumericLock(tmpCtx, &numeric_go2.BatchGetUserNumericLockReq{
		UidList: uidList,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "isRecordRich GetUserRichSwitch err:%+v", err)
		return isRecordRichTbeanMap, isRecordCharmTbeanMap, isRecordRichRedDiamondMap, isRecordCharmRedDiamondMap
	}

	for _, tmp := range uidList {
		if !isRecord(resp.GetLockMap(), tmp, uint32(userpresent.PresentPriceType_PRESENT_PRICE_TBEAN), false) {
			isRecordRichTbeanMap[tmp] = false
		}
		if !isRecord(resp.GetLockMap(), tmp, uint32(userpresent.PresentPriceType_PRESENT_PRICE_RED_DIAMOND), false) {
			isRecordRichRedDiamondMap[tmp] = false
		}
		if !isRecord(resp.GetLockMap(), tmp, uint32(userpresent.PresentPriceType_PRESENT_PRICE_TBEAN), true) {
			isRecordCharmTbeanMap[tmp] = false
		}
		if !isRecord(resp.GetLockMap(), tmp, uint32(userpresent.PresentPriceType_PRESENT_PRICE_RED_DIAMOND), true) {
			isRecordCharmRedDiamondMap[tmp] = false
		}
	}

	return isRecordRichTbeanMap, isRecordCharmTbeanMap, isRecordRichRedDiamondMap, isRecordCharmRedDiamondMap
}

func isRecord(lockMap map[uint32]*numeric_go2.UserNumericLock, uid uint32, priceType uint32, isCharm bool) bool {
	if lockMap[uid].BeanRichLock == numeric_go2.LockStatus_LOCK_STATUS_ENABLE {
		if priceType == uint32(userpresent.PresentPriceType_PRESENT_PRICE_TBEAN) && !isCharm {
			return false
		}
	}

	if lockMap[uid].DiamondRichLock == numeric_go2.LockStatus_LOCK_STATUS_ENABLE {
		if priceType == uint32(userpresent.PresentPriceType_PRESENT_PRICE_RED_DIAMOND) && !isCharm {
			return false
		}
	}

	if lockMap[uid].BeanCharmLock == numeric_go2.LockStatus_LOCK_STATUS_ENABLE {
		if priceType == uint32(userpresent.PresentPriceType_PRESENT_PRICE_TBEAN) && isCharm {
			return false
		}
	}

	if lockMap[uid].DiamondCharmLock == numeric_go2.LockStatus_LOCK_STATUS_ENABLE {
		if priceType == uint32(userpresent.PresentPriceType_PRESENT_PRICE_RED_DIAMOND) && isCharm {
			return false
		}
	}

	return true
}

func (s *PresentMiddlewareMgr) isRecordSenderRich(ctx context.Context, uid, priceType uint32) bool {
	isRecord := &isRecordMap{
		isRecordRichTbeanMap:       make(map[uint32]bool),
		isRecordCharmTbeanMap:      make(map[uint32]bool),
		isRecordRichRedDiamondMap:  make(map[uint32]bool),
		isRecordCharmRedDiamondMap: make(map[uint32]bool),
	}
	if ctxV := ctx.Value(IsRecordSenderRichKey); ctxV != nil {
		if v, ok := ctxV.(*isRecordMap); ok {
			isRecord = v
		}
	}

	if priceType == uint32(userpresent.PresentPriceType_PRESENT_PRICE_TBEAN) {
		return isRecord.isRecordRichTbeanMap[uid]
	}

	if priceType == uint32(userpresent.PresentPriceType_PRESENT_PRICE_RED_DIAMOND) {
		return isRecord.isRecordRichRedDiamondMap[uid]
	}

	return true
}

func (s *PresentMiddlewareMgr) isRecordTargetCharm(ctx context.Context, uid, priceType uint32) bool {
	isRecord := &isRecordMap{
		isRecordRichTbeanMap:       make(map[uint32]bool),
		isRecordCharmTbeanMap:      make(map[uint32]bool),
		isRecordRichRedDiamondMap:  make(map[uint32]bool),
		isRecordCharmRedDiamondMap: make(map[uint32]bool),
	}
	if ctxV := ctx.Value(IsRecordSenderRichKey); ctxV != nil {
		if v, ok := ctxV.(*isRecordMap); ok {
			isRecord = v
		}
	}

	if priceType == uint32(userpresent.PresentPriceType_PRESENT_PRICE_TBEAN) {
		return isRecord.isRecordCharmTbeanMap[uid]
	}

	if priceType == uint32(userpresent.PresentPriceType_PRESENT_PRICE_RED_DIAMOND) {
		return isRecord.isRecordCharmRedDiamondMap[uid]
	}

	return true
}

func (s *PresentMiddlewareMgr) isRecordAllNumeric(ctx context.Context) *isRecordMap {
	isRecord := &isRecordMap{
		isRecordRichTbeanMap:       make(map[uint32]bool),
		isRecordCharmTbeanMap:      make(map[uint32]bool),
		isRecordRichRedDiamondMap:  make(map[uint32]bool),
		isRecordCharmRedDiamondMap: make(map[uint32]bool),
	}
	if ctxV := ctx.Value(IsRecordSenderRichKey); ctxV != nil {
		if v, ok := ctxV.(*isRecordMap); ok {
			isRecord = v
		}
	}
	return isRecord
}

func (s *PresentMiddlewareMgr) IsUsePresentGo(uid uint32) bool {
	if s.GeneralConfig.GetPresentConfig().PresentGoSwitch {
		return true
	} else {
		if uid%100 < s.GeneralConfig.GetPresentConfig().PresentGoRatio {
			return true
		}

		for _, item := range s.GeneralConfig.GetPresentConfig().PresentGoUidList {
			if item == uid {
				return true
			}
		}
	}

	return false
}

// SetPresentGoCtx 把是否使用present-go放进ctx
func (s *PresentMiddlewareMgr) SetPresentGoCtx(ctx context.Context, uid uint32) context.Context {
	return context.WithValue(ctx, userpresent_go.UsePresentGoKey, s.IsUsePresentGo(uid))
}

// DelPresentConfig 删除礼物配置
func (s *PresentMiddleware) DelPresentConfig(ctx context.Context, in *pb.DelPresentConfigReq) (out *pb.DelPresentConfigResp, err error) {
	log.DebugWithCtx(ctx, "DelPresentConfig Begin ,req :%v", in)
	out = &pb.DelPresentConfigResp{}

	// 如果是礼物套组礼物，检查一下是不是活跃的
	presentCfg, err := client.PresentCli.GetPresentConfigById(ctx, in.GetItemId())
	if err != nil {
		log.ErrorWithCtx(ctx, "DelPresentConfig --GetPresentConfigById err. req:%v , err: %v", in, err)
		return out, protocol.ToServerError(err)
	}

	if presentCfg.GetItemConfig().GetExtend().GetTag() == uint32(ga.PresentTagType_PRESENT_TAG_SET) {
		_, err := client.PresentSetCli.CheckPresentInActiveSet(ctx, &present_set.CheckPresentInActiveSetReq{PresentId: in.GetItemId()})
		if err != nil {
			log.ErrorWithCtx(ctx, "DelPresentConfig --CheckPresentInActiveSet err. req:%v , err: %v", in, err)
			return out, protocol.ToServerError(err)
		}
	}

	// 是否在活跃的帝王套内
	emperorList, err := client.PresentSetCli.GetEmperorSetList(ctx, &present_set.GetEmperorSetListReq{})
	if err != nil {
		log.ErrorWithCtx(ctx, "DelPresentConfig --GetEmperorSetList err. req:%v , err: %v", in, err)
		return out, protocol.ToServerError(err)
	}

	for _, emperorSet := range emperorList.GetEmperorSetList() {
		if emperorSet.GetStartTime() > uint32(time.Now().Unix()) || emperorSet.GetEndTime() < uint32(time.Now().Unix()) {
			continue
		}
		for _, item := range emperorSet.GetPresentList() {
			if item.GetPresentId() == in.GetItemId() {
				log.ErrorWithCtx(ctx, "DelPresentConfig --DelPresentConfig err. req:%v , err: %v", in, err)
				return out, protocol.NewExactServerError(nil, status.ErrRevenueSvrErr, "该礼物在活跃的帝王套内，不能删除")
			}
		}
	}

	err = client.PresentCli.DelPresentConfig(ctx, in.GetItemId())
	if err != nil {
		log.ErrorWithCtx(ctx, "DelPresentConfig --DelPresentConfig err. req:%v , err: %v", in, err)
		return out, protocol.ToServerError(err)
	}
	return out, nil
}

// SetSendPresent 成套送礼
func (s *PresentMiddleware) SetSendPresent(ctx context.Context, in *pb.SetSendPresentReq) (out *pb.SetSendPresentResp, err error) {
	log.DebugWithCtx(ctx, "SetSendPresent Begin ,req :%v", in)
	out = &pb.SetSendPresentResp{
		MsgInfo: &pb.SetPresentSendMsg{
			ItemInfo: make([]*pb.PresentSendItemInfo, 0),
		},
	}

	err = s.presentSendDelegate.PresentSend(ctx, in, out, "set")
	if err != nil {
		log.ErrorWithCtx(ctx, "SendPresent --SetSendPresent err. req:%v , err: %v", in, err)
		return out, protocol.ToServerError(err)
	}
	return out, nil
}
