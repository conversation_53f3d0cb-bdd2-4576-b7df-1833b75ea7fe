package store

import (
	"fmt"
	"github.com/jinzhu/gorm"
	"golang.52tt.com/services/pgc-channel/pgc-channel-ticket/model/data"
)

func (s *Store) SetTicketConf(conf *data.TicketConf) error {
	sql := fmt.Sprintf("INSERT INTO %s (ticket_id,pkg_top_msg,pop_up_msg,im_push_msg) VALUES(?,?,?,?) ON DUPLICATE KEY UPDATE"+
		" pkg_top_msg=?, pop_up_msg=?, im_push_msg=?", conf.TableName())

	err := s.mysqlDb.Exec(sql, conf.TicketId, conf.PkgTopMsg, conf.PopUpMsg, conf.ImPushMsg, conf.PkgTopMsg, conf.PopUpMsg, conf.ImPushMsg).Error

	return err
}

func (s *Store) DelConfByTicketId(ticketId uint32) error {
	sql := fmt.Sprintf("UPDATE %s SET is_delete = 1  WHERE ticket_id = ?", data.TicketConfTb)

	err := s.mysqlDb.Exec(sql, ticketId).Error

	return err
}

func (s *Store) GetTicketConfList() ([]*data.TicketConf, error) {
	confList := make([]*data.TicketConf, 0)

	err := s.mysqlDb.Model(&data.TicketConf{}).Where("is_delete != 1").Order("update_tm desc").Find(&confList).Error
	if err != nil {
		if gorm.IsRecordNotFoundError(err) {
			return confList, nil
		}
		return nil, err
	}
	return confList, nil
}

func (s *Store) GetAllTicketConf() ([]*data.TicketConf, error) {
	confList := make([]*data.TicketConf, 0)

	err := s.mysqlDb.Model(&data.TicketConf{}).Find(&confList).Error
	if err != nil {
		if gorm.IsRecordNotFoundError(err) {
			return confList, nil
		}
		return nil, err
	}
	return confList, nil
}

func (s *Store) DelWhiteChByTicketId(ticketId uint32) error {
	sql := fmt.Sprintf("DELETE FROM %s WHERE ticket_id = ?", data.TicketWhiteChTb)

	err := s.mysqlDb.Exec(sql, ticketId).Error

	return err
}

func (s *Store) AddWhiteCh(chInfo *data.TicketWhiteCh) error {
	sql := fmt.Sprintf("INSERT INTO %s (ticket_id,cid) VALUES(?,?)", chInfo.TableName())

	err := s.mysqlDb.Exec(sql, chInfo.TicketId, chInfo.Cid).Error

	return err
}

func (s *Store) GetWhiteChList(ticketId uint32) ([]*data.TicketWhiteCh, error) {
	confList := make([]*data.TicketWhiteCh, 0)

	err := s.mysqlDb.Model(&data.TicketWhiteCh{}).Where("ticket_id = ?", ticketId).Find(&confList).Error
	if err != nil {
		if gorm.IsRecordNotFoundError(err) {
			return confList, nil
		}
		return nil, err
	}
	return confList, nil
}
