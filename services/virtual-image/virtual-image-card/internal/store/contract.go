package store

import (
	"context"
	"encoding/json"
	"fmt"
	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
	"golang.52tt.com/pkg/log"
	"time"
)

func GenContractId(uid uint32, createTime time.Time) string {
	return fmt.Sprintf("card_%d_%d", uid, createTime.Unix())
}

type Contract struct {
	Id            uint32    `db:"id"`
	ContractId    string    `db:"contract_id"`
	Uid           uint32    `db:"uid"`
	MarketId      uint8     `db:"market_id"`
	ClientType    uint8     `db:"client_type"`
	ClientVersion uint32    `db:"client_version"`
	PayChannel    uint8     `db:"pay_channel"`
	PackageId     uint32    `db:"package_id"`
	NextPayTime   time.Time `db:"next_pay_time"`
	Status        uint8     `db:"status"`
	CreateTime    time.Time `db:"create_time"`
	UpdateTime    time.Time `db:"update_time"`
	PackageInfo   string    `db:"package_info"`
	Package       Package   `db:"-"`
}

func (o *Contract) EncodePackageInfo() {
	if o.Package.Id == 0 {
		return
	}
	packageInfo, _ := json.Marshal(o.Package)
	o.PackageInfo = string(packageInfo)
}

func (o *Contract) DecodePackageInfo() error {
	if o == nil || o.PackageInfo == "" {
		return nil
	}
	err := json.Unmarshal([]byte(o.PackageInfo), &o.Package)
	if err != nil {
		log.Errorf("Order DecodePackageInfo err: %v", err)
		return err
	}
	return nil
}

const createContractTblSQL = `CREATE TABLE IF NOT EXISTS %s (
    id int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增id',
    contract_id varchar(64) NOT NULL default '' COMMENT '签约id',
    uid int(10) unsigned NOT NULL DEFAULT 0 COMMENT 'uid',
    
    market_id tinyint(1) unsigned NOT NULL DEFAULT 0 COMMENT '马甲包id',
    client_type tinyint(1) unsigned NOT NULL DEFAULT 0 COMMENT '客户端类型',
    client_version int(10) unsigned NOT NULL DEFAULT 0 COMMENT '客户端版本',
    pay_channel tinyint(1) unsigned NOT NULL DEFAULT 0 COMMENT '支付渠道',
    package_id int(10) unsigned NOT NULL DEFAULT 0 COMMENT '套餐id',
    next_pay_time timestamp NOT NULL default 0 COMMENT '下次扣款时间',
    
    status tinyint(1) unsigned NOT NULL DEFAULT 0 COMMENT '签约状态 0-初始化 1-已签约 2-已解约',
    create_time timestamp NOT NULL default CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time timestamp NOT NULL default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    package_info varchar(1024) NOT NULL default '' COMMENT '套餐信息json',
    
    primary key (id),
    unique key uniq_contract_id (contract_id),
    key idx_pay_channel (pay_channel),
    key idx_uid (uid)
) engine=InnoDB default charset=utf8 COMMENT "无限换装卡签约表";`

const (
	queryContractFields    = "id, contract_id, uid, market_id, client_type, client_version, pay_channel, package_id, next_pay_time, status, create_time, update_time, package_info"
	contractTblName        = "tbl_virtual_image_card_contract"
	contractArchiveTblName = "tbl_virtual_image_card_contract_archive"
	ContractStatusInit     = 0 // 初始化
	ContractStatusSigned   = 1 // 已签约
	ContractStatusCanceled = 2 // 已解约
)

func (s *Store) AddContract(ctx context.Context, contract *Contract, tx mysql.Txx) (bool, error) {
	if tx == nil {
		return false, nil
	}

	contract.EncodePackageInfo()
	query := fmt.Sprintf("INSERT INTO %s (contract_id, uid, market_id, client_type, client_version, pay_channel, package_id, next_pay_time, package_info) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)", contractTblName)
	params := []interface{}{contract.ContractId, contract.Uid, contract.MarketId, contract.ClientType, contract.ClientVersion, contract.PayChannel, contract.PackageId, contract.NextPayTime, contract.PackageInfo}

	_, err := tx.ExecContext(ctx, query, params...)
	if err != nil {
		return false, err
	}
	return true, nil
}

func (s *Store) UpdateContract(ctx context.Context, contract *Contract, oldStatus uint8, tx mysql.Txx) (bool, error) {
	if tx == nil {
		return false, nil
	}

	contract.EncodePackageInfo()
	query := fmt.Sprintf("UPDATE %s SET uid = ?, package_id = ?, next_pay_time = ?, status = ?, package_info = ? WHERE contract_id = ? AND status = ?", contractTblName)
	params := []interface{}{contract.Uid, contract.PackageId, contract.NextPayTime, contract.Status, contract.PackageInfo, contract.ContractId, oldStatus}

	result, err := tx.ExecContext(ctx, query, params...)
	if err != nil {
		return false, err
	}
	rowsAffected, _ := result.RowsAffected()
	return rowsAffected > 0, nil
}

func (s *Store) UpdateContractNextPayTime(ctx context.Context, contractId string, nextPayTime time.Time, tx mysql.Txx) (bool, error) {
	var err error
	query := fmt.Sprintf("UPDATE %s SET next_pay_time = ? WHERE contract_id = ?", contractTblName)
	params := []interface{}{nextPayTime, contractId}

	if tx != nil {
		_, err = tx.ExecContext(ctx, query, params...)
	} else {
		_, err = s.db.ExecContext(ctx, query, params...)
	}
	if err != nil {
		return false, err
	}
	return true, nil
}

func (s *Store) UpdateContractStatus(ctx context.Context, contractId string, status, oldStatus uint8, tx mysql.Txx) (bool, error) {
	var err error
	query := fmt.Sprintf("UPDATE %s SET status = ? WHERE contract_id = ? AND status = ?", contractTblName)
	params := []interface{}{status, contractId, oldStatus}

	if tx != nil {
		_, err = tx.ExecContext(ctx, query, params...)
	} else {
		_, err = s.db.ExecContext(ctx, query, params...)
	}
	if err != nil {
		return false, err
	}
	return true, nil
}

func (s *Store) UpdateContractPackageInfo(ctx context.Context, contract *Contract, tx mysql.Txx) (bool, error) {
	if tx == nil {
		return false, nil
	}

	contract.EncodePackageInfo()
	query := fmt.Sprintf("UPDATE %s SET package_id = ?, package_info = ? WHERE contract_id = ?", contractTblName)
	params := []interface{}{contract.PackageId, contract.PackageInfo, contract.ContractId}

	_, err := tx.ExecContext(ctx, query, params...)
	if err != nil {
		return false, err
	}
	return true, nil
}

func (s *Store) GetContract(ctx context.Context, contractId string, tx mysql.Txx) (*Contract, error) {
	query := fmt.Sprintf("SELECT %s FROM %s WHERE contract_id = ?", queryContractFields, contractTblName)

	contract := &Contract{}
	var err error
	if tx != nil {
		err = tx.GetContext(ctx, contract, query, contractId)
	} else {
		err = s.db.GetContext(ctx, contract, query, contractId)
	}

	if err != nil && mysql.IsNoRowsError(err) {
		return nil, nil
	}
	_ = contract.DecodePackageInfo()
	return contract, err
}

func (s *Store) GetUserContractList(ctx context.Context, uid uint32, tx mysql.Txx) ([]*Contract, error) {
	contracts := make([]*Contract, 0)

	query := fmt.Sprintf("SELECT %s FROM %s WHERE uid = ? AND status = ?", queryContractFields, contractTblName)
	params := []interface{}{uid, ContractStatusSigned}

	var err error
	if tx != nil {
		err = tx.SelectContext(ctx, &contracts, query, params...)
	} else {
		err = s.db.SelectContext(ctx, &contracts, query, params...)
	}

	if err != nil {
		return contracts, nil
	}
	for _, contract := range contracts {
		_ = contract.DecodePackageInfo()
	}
	return contracts, nil
}

// GetNextPayContractList 获取即将扣款的签约列表
func (s *Store) GetNextPayContractList(ctx context.Context, minId, limit uint32, shiftTime time.Time) (contracts []*Contract, nextId uint32, hasMore bool, err error) {
	contracts = make([]*Contract, 0)

	query := fmt.Sprintf("SELECT %s FROM %s WHERE status = ? AND next_pay_time <= ? AND id > ? ORDER BY id ASC LIMIT ?", queryContractFields, contractTblName)
	params := []interface{}{ContractStatusSigned, shiftTime, minId, limit}
	err = s.readonlyDb.SelectContext(ctx, &contracts, query, params...)
	if err != nil {
		return
	}

	if len(contracts) >= int(limit) {
		hasMore = true
		nextId = contracts[len(contracts)-1].Id
	}
	for _, contract := range contracts {
		_ = contract.DecodePackageInfo()
	}
	return
}

// GetContractsByPayChannel 根据支付类型获取签约信息
func (s *Store) GetContractsByPayChannel(ctx context.Context, payChannel, limit, offset uint32) ([]*Contract, error) {
	log.DebugWithCtx(ctx, "GetContractsWithIOS limit:%d, offset:%d", limit, offset)

	shiftTime := time.Now().Add(-time.Minute * 5) // 刚创建的不要立刻检查，避免和回调竞争
	sql := fmt.Sprintf("select %s from %s where pay_channel = ? and create_time < ? order by `contract_id` limit ? offset ?", queryContractFields, contractTblName)
	contracts := make([]*Contract, 0)
	err := s.readonlyDb.SelectContext(ctx, &contracts, sql, payChannel, shiftTime, limit, offset)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetContractsByPayChannel err:%v, payChannel:%d, limit:%d, offset:%d", err, payChannel, limit, offset)
		return nil, err
	}

	log.DebugWithCtx(ctx, "GetContractsByPayChannel payChannel:%d, limit:%d, offset:%d, contracts:%+v", payChannel, limit, offset, contracts)
	return contracts, nil
}

func (s *Store) CleanInitContract(ctx context.Context, duration time.Duration) {
	query := fmt.Sprintf("DELETE FROM %s WHERE status = %d AND create_time < ?", contractTblName, ContractStatusInit)
	result, err := s.db.ExecContext(ctx, query, time.Now().Add(-duration))
	if err != nil {
		log.ErrorWithCtx(ctx, "Clean CleanInitContract err: %v", err)
		return
	}
	rowsAffected, _ := result.RowsAffected()
	log.InfoWithCtx(ctx, "Clean CleanInitContract count: %d", rowsAffected)
}

func (s *Store) CleanCanceledContract(ctx context.Context, duration time.Duration) {
	// 只处理支付宝的，苹果的解约后还能恢复签约
	query := fmt.Sprintf("select %s from %s where pay_channel = 1 and status = %d and update_time < ?", queryContractFields, contractTblName, ContractStatusCanceled)
	contracts := make([]*Contract, 0)
	err := s.readonlyDb.SelectContext(ctx, &contracts, query, time.Now().Add(-duration))
	if err != nil {
		log.ErrorWithCtx(ctx, "Clean CleanCanceledContract err: %v", err)
		return
	}

	for _, contract := range contracts {
		s.cleanCanceledContract(ctx, contract)
	}
	log.InfoWithCtx(ctx, "Clean CleanCanceledContract count: %d", len(contracts))
}

func (s *Store) cleanCanceledContract(ctx context.Context, contract *Contract) {
	// 先添加到归档表中
	query := fmt.Sprintf("insert into %s (contract_id, uid, market_id, client_type, client_version, pay_channel, package_id, next_pay_time, status, create_time, update_time, package_info) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", contractArchiveTblName)
	params := []interface{}{contract.ContractId, contract.Uid, contract.MarketId, contract.ClientType, contract.ClientVersion, contract.PayChannel, contract.PackageId, contract.NextPayTime, contract.Status, contract.CreateTime, contract.UpdateTime, contract.PackageInfo}
	_, err := s.db.ExecContext(ctx, query, params...)
	if err != nil && !mysql.IsDupEntryError(err) {
		log.ErrorWithCtx(ctx, "Clean CleanCanceledContract add %s err: %v", contract.ContractId, err)
		return
	}

	// 删除原表中的数据
	query = fmt.Sprintf("delete from %s where contract_id = ?", contractTblName)
	_, err = s.db.ExecContext(ctx, query, contract.ContractId)
	if err != nil {
		log.ErrorWithCtx(ctx, "Clean CleanCanceledContract delete %s err: %v", contract.ContractId, err)
		return
	}
}
