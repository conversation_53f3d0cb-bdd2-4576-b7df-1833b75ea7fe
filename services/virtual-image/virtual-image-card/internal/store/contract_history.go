package store

import (
	"context"
	"fmt"
	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
	"time"
)

type ContractHistory struct {
	ContractId    string    `db:"contract_id"`
	Uid           uint32    `db:"uid"`
	PayChannel    uint8     `db:"pay_channel"`
	PackageId     uint32    `db:"package_id"`
	PackageInfo   string    `db:"package_info"`
	Status        uint8     `db:"status"`
	OperationRole uint8     `db:"operation_role"`
	Reason        string    `db:"reason"`
	CreateTime    time.Time `db:"create_time"`
	UpdateTime    time.Time `db:"update_time"`
}

const createContractHistoryTblSQL = `CREATE TABLE IF NOT EXISTS %s (
    id int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增id',
    contract_id varchar(64) NOT NULL default '' COMMENT '签约id',
    uid int(10) unsigned NOT NULL DEFAULT 0 COMMENT 'uid',
    pay_channel tinyint(1) unsigned NOT NULL DEFAULT 0 COMMENT '支付渠道',
    package_id int(10) unsigned NOT NULL DEFAULT 0 COMMENT '套餐id',
    package_info varchar(1024) NOT NULL default '' COMMENT '套餐信息json',
    status tinyint(1) unsigned NOT NULL DEFAULT 0 COMMENT '签约状态 0-初始化 1-已签约 2-已解约',
    operation_role tinyint(1) unsigned NOT NULL DEFAULT 0 COMMENT '操作角色 0-用户操作 1-系统触发',
    reason varchar(64) NOT NULL default '' COMMENT '系统触发时，进一步记录原因',
    create_time timestamp NOT NULL default CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time timestamp NOT NULL default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    primary key (id),
    key idx_contract_id (contract_id),
    key idx_uid (uid)
) engine=InnoDB default charset=utf8 COMMENT "无限换装卡签约变更历史表";`

const (
	ContractHistoryOperationRoleUser   = 0 // 用户操作
	ContractHistoryOperationRoleSystem = 1 // 系统触发
)

func getContractHistoryTblName(createTime time.Time) string {
	return fmt.Sprintf("tbl_virtual_image_card_contract_history_%s", createTime.Format("200601"))
}

func (s *Store) AddContractHistory(ctx context.Context, history *ContractHistory, tx mysql.Txx) (bool, error) {
	var err error
	query := fmt.Sprintf("INSERT INTO %s (contract_id, uid, pay_channel, package_id, package_info, status, operation_role, reason, create_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)", getContractHistoryTblName(history.CreateTime))
	params := []interface{}{history.ContractId, history.Uid, history.PayChannel, history.PackageId, history.PackageInfo, history.Status, history.OperationRole, history.Reason, history.CreateTime}

	if tx != nil {
		_, err = tx.ExecContext(ctx, query, params...)
	} else {
		_, err = s.db.ExecContext(ctx, query, params...)
	}
	if err != nil {
		return false, err
	}
	return true, nil
}
