package store

import (
	"context"
	"fmt"
	"golang.52tt.com/pkg/log"
	"strings"
	"time"
)

type StatInfo struct {
	PeriodStr           string    `db:"period_str" json:"period_str"`                       // 账期
	PackageId           uint32    `db:"package_id" json:"package_id"`                       // 套餐ID
	PackageName         string    `db:"package_name" json:"package_name"`                   // 套餐名称
	PayChannel          string    `db:"pay_channel" json:"pay_channel"`                     // 支付渠道
	Price               float64   `db:"price" json:"price"`                                 // 价格
	Days                uint32    `db:"days" json:"days"`                                   // 天数
	DailyPrice          float64   `db:"daily_price" json:"daily_price"`                     // 日均价格
	BeforeLeftDays      uint32    `db:"before_left_days" json:"before_left_days"`           // 上月剩余天数
	BeforeLeftPrice     float64   `db:"before_left_price" json:"before_left_price"`         // 上月剩余金额
	LeftDays            uint32    `db:"left_days" json:"left_days"`                         // 本月剩余天数
	LeftPrice           float64   `db:"left_price" json:"left_price"`                       // 本月剩余金额
	BuyDays             uint32    `db:"buy_days" json:"buy_days"`                           // 本月购买天数
	BuyPrice            float64   `db:"buy_price" json:"buy_price"`                         // 本月购买金额
	RedemptionDays      uint32    `db:"redemption_days" json:"redemption_days"`             // 本月核销天数
	RedemptionPrice     float64   `db:"redemption_price" json:"redemption_price"`           // 本月核销金额
	RevokePrice         float64   `db:"revoke_price" json:"revoke_price"`                   // 本月撤销金额
	RevokeRollbackDays  uint32    `db:"revoke_rollback_days" json:"revoke_rollback_days"`   // 本月撤销回滚天数
	RevokeRollbackPrice float64   `db:"revoke_rollback_price" json:"revoke_rollback_price"` // 本月撤销回滚金额
	DiffCheck           float64   `db:"diff_check" json:"diff_check"`                       // 误差结果
	CreateTime          time.Time `db:"create_time" json:"create_time"`                     // 创建时间
	UpdateTime          time.Time `db:"update_time" json:"update_time"`                     // 更新时间
}

const createStatTblSQL = `CREATE TABLE IF NOT EXISTS %s (
  id bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  period_str varchar(64) NOT NULL DEFAULT '' COMMENT '账期',
  package_id int(10) unsigned NOT NULL DEFAULT '0' COMMENT '套餐ID',
  package_name varchar(128) NOT NULL DEFAULT '' COMMENT '套餐名称',
  pay_channel varchar(128) NOT NULL DEFAULT '' COMMENT '支付渠道',
  price double NOT NULL DEFAULT '0' COMMENT '支付价格',
  days int(10) unsigned NOT NULL DEFAULT '0' COMMENT '订单天数',
  daily_price double NOT NULL DEFAULT '0' COMMENT '日均价格',
  before_left_days int(10) unsigned NOT NULL DEFAULT '0' COMMENT '上月剩余天数',
  before_left_price double NOT NULL DEFAULT '0' COMMENT '上月剩余金额',
  left_days int(10) unsigned NOT NULL DEFAULT '0' COMMENT '本月剩余天数',
  left_price double NOT NULL DEFAULT '0' COMMENT '本月剩余金额',
  buy_days int(10) unsigned NOT NULL DEFAULT '0' COMMENT '本月购买天数',
  buy_price double NOT NULL DEFAULT '0' COMMENT '本月购买金额',
  redemption_days int(10) unsigned NOT NULL DEFAULT '0' COMMENT '本月核销天数',
  redemption_price double NOT NULL DEFAULT '0' COMMENT '本月核销金额',
  revoke_price double NOT NULL DEFAULT '0' COMMENT '本月撤销金额',
  revoke_rollback_days int(10) unsigned NOT NULL DEFAULT '0' COMMENT '本月撤销回滚天数',
  revoke_rollback_price double NOT NULL DEFAULT '0' COMMENT '本月撤销回滚金额',
  diff_check double NOT NULL DEFAULT '0' COMMENT '误差结果',
  create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  update_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (id),
  UNIQUE KEY uniq_period_package (period_str, package_id, pay_channel, price)
) engine=InnoDB default charset=utf8 COMMENT "无限换装卡统计表";`

const (
	statTblName = "tbl_virtual_image_card_stat"
)

func (s *Store) SetPeriodStatInfos(ctx context.Context, periodTime time.Time, statList []*StatInfo) error {
	// 先清空对应周期的数据
	query := fmt.Sprintf("DELETE FROM %s WHERE period_str = ?", statTblName)
	_, err := s.db.ExecContext(ctx, query, periodTime.Format("200601"))
	if err != nil {
		log.ErrorWithCtx(ctx, "SetPeriodStatInfos DELETE err: %v", err)
		return err
	}

	// 分批添加数据
	batchSize := 100
	for start := 0; start < len(statList); start += batchSize {
		end := start + batchSize
		if end > len(statList) {
			end = len(statList)
		}
		batch := statList[start:end]

		sqlStr := fmt.Sprintf("INSERT INTO %s (period_str, package_id, package_name, pay_channel, price, days, daily_price, before_left_days, before_left_price, left_days, left_price, buy_days, buy_price, redemption_days, redemption_price, revoke_price, revoke_rollback_days, revoke_rollback_price, diff_check) VALUES ", statTblName)
		var values []interface{}
		var placeholders []string
		for _, stat := range batch {
			values = append(values, stat.PeriodStr, stat.PackageId, stat.PackageName, stat.PayChannel, stat.Price, stat.Days, stat.DailyPrice, stat.BeforeLeftDays, stat.BeforeLeftPrice, stat.LeftDays, stat.LeftPrice, stat.BuyDays, stat.BuyPrice, stat.RedemptionDays, stat.RedemptionPrice, stat.RevokePrice, stat.RevokeRollbackDays, stat.RevokeRollbackPrice, stat.DiffCheck)
			placeholders = append(placeholders, "(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)")
		}
		sqlStr += strings.Join(placeholders, ", ")

		_, err = s.db.ExecContext(ctx, sqlStr, values...)
		if err != nil {
			log.ErrorWithCtx(ctx, "SetPeriodStatInfos INSERT err: %v", err)
			return err
		}
	}

	return nil
}