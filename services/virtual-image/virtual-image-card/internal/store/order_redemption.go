package store

import (
	"context"
	"errors"
	"fmt"
	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"time"
)

const createOrderRedemptionTblSQL = `CREATE TABLE IF NOT EXISTS %s (
    id int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增id',
    uid int(10) unsigned NOT NULL DEFAULT 0 COMMENT 'uid',
    order_id varchar(64) NOT NULL default '' COMMENT '订单id',
    days int(10) unsigned NOT NULL DEFAULT 0 COMMENT '订单时长/天数',
    package_type tinyint(4) NOT NULL DEFAULT 0 COMMENT '套餐类型 1-单次购买 2-自动续费',
    begin_time timestamp NOT NULL default CURRENT_TIMESTAMP COMMENT '开始核销时间',
    end_time timestamp NOT NULL default CURRENT_TIMESTAMP COMMENT '结束核销时间',
    status tinyint(1) unsigned NOT NULL DEFAULT 0 COMMENT '核销状态 0-正常核销 1-已退单',
    create_time timestamp NOT NULL default CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time timestamp NOT NULL default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    
    primary key (id),
    unique key uniq_order_id (order_id),
    key idx_uid (uid)
) engine=InnoDB default charset=utf8 COMMENT "订单核销表";`

const (
	orderRedemptionTblName        = "tbl_virtual_image_card_order_redemption"
	orderRedemptionArchiveTblName = "tbl_virtual_image_card_order_redemption_archive"
	queryOrderRedemptionFields    = "id, uid, order_id, days, package_type, begin_time, end_time, status, create_time, update_time"
	OrderRedemptionStatusNormal   = 0 // 正常核销
	OrderRedemptionStatusRefund   = 1 // 已退单
)

type OrderRedemption struct {
	Id          uint32    `db:"id"`
	Uid         uint32    `db:"uid"`
	OrderId     string    `db:"order_id"`
	Days        uint32    `db:"days"`
	PackageType uint32    `db:"package_type"`
	BeginTime   time.Time `db:"begin_time"`
	EndTime     time.Time `db:"end_time"`
	Status      uint8     `db:"status"`
	CreateTime  time.Time `db:"create_time"`
	UpdateTime  time.Time `db:"update_time"`
}

// AddOrderRedemption 新增订单核销记录
func (s *Store) AddOrderRedemption(ctx context.Context, info *OrderRedemption, tx mysql.Txx) error {
	if info == nil || tx == nil {
		return errors.New("info or tx is nil")
	}

	sql := fmt.Sprintf("INSERT INTO %s(uid, order_id, days, package_type, begin_time, end_time, status) VALUES(?,?,?,?,?,?,?)", orderRedemptionTblName)
	_, err := tx.ExecContext(ctx, sql, info.Uid, info.OrderId, info.Days, info.PackageType, info.BeginTime, info.EndTime, info.Status)
	if err != nil {
		log.ErrorWithCtx(ctx, "AddOrderRedemption fail. info:%+v, err:%v ", info, err)
		return err
	}

	return nil
}

// GetOrderRedemption 根据订单ID获取订单核销记录
func (s *Store) GetOrderRedemption(ctx context.Context, orderId string) (*OrderRedemption, error) {
	sql := fmt.Sprintf("SELECT %s FROM %s WHERE order_id = ?", queryOrderRedemptionFields, orderRedemptionTblName)
	var info OrderRedemption
	err := s.db.GetContext(ctx, &info, sql, orderId)
	if err != nil {
		if mysql.IsNoRowsError(err) {
			return nil, nil
		}
		log.ErrorWithCtx(ctx, "GetOrderRedemption fail. orderId:%s, err:%v ", orderId, err)
		return nil, err
	}

	return &info, nil
}

// GetUserOrderRedemptionList 获取用户订单核销记录列表
func (s *Store) GetUserOrderRedemptionList(ctx context.Context, uid uint32, endTime time.Time, tx mysql.Txx) ([]*OrderRedemption, error) {
	sql := fmt.Sprintf("SELECT %s FROM %s WHERE uid = ? AND end_time > ? AND status = 0 ORDER BY id", queryOrderRedemptionFields, orderRedemptionTblName)
	var list []*OrderRedemption
	var err error

	if tx != nil {
		sql += " FOR UPDATE" // 加锁
		err = tx.SelectContext(ctx, &list, sql, uid, endTime)
	} else {
		err = s.db.SelectContext(ctx, &list, sql, uid, endTime)
	}
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserOrderRedemptionList fail. uid:%d, err:%v", uid, err)
		return nil, err
	}

	return list, nil
}

// BatchUpdateUserOrderRedemptionList 批量更新
func (s *Store) BatchUpdateUserOrderRedemptionList(ctx context.Context, list []*OrderRedemption, tx mysql.Txx) error {
	if len(list) == 0 || tx == nil {
		return errors.New("list is empty or tx is nil")
	}

	sql := fmt.Sprintf("REPLACE INTO %s (id, uid, order_id, days, begin_time, end_time, status, create_time) VALUES", orderRedemptionTblName)
	var args []interface{}
	for i, info := range list {
		args = append(args, info.Id, info.Uid, info.OrderId, info.Days, info.BeginTime, info.EndTime, info.Status, info.CreateTime)
		sql += "(?,?,?,?,?,?,?,?)"
		if i < len(list)-1 {
			sql += ","
		}
	}

	_, err := tx.ExecContext(ctx, sql, args...)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchUpdateUserOrderRedemptionList fail. list:%+v, err:%v ", list, err)
		return err
	}

	return nil
}

func (s *Store) GetUndoneRedemptionOrders(ctx context.Context, startTime, endTime time.Time) ([]*OrderRedemption, error) {
	query := fmt.Sprintf("SELECT %s FROM %s WHERE end_time > ?", queryOrderRedemptionFields, orderRedemptionTblName)

	orderList := make([]*OrderRedemption, 0)
	err := s.readonlyDb.SelectContext(ctx, &orderList, query, startTime)
	if err != nil {
		return nil, err
	}
	return orderList, nil
}

func (s *Store) CleanExpiredOrderRedemption(ctx context.Context, duration time.Duration) {
	query := fmt.Sprintf("select %s from %s WHERE end_time < ?", queryOrderRedemptionFields, orderRedemptionTblName)
	orders := make([]*OrderRedemption, 0)
	err := s.readonlyDb.SelectContext(ctx, &orders, query, time.Now().Add(-duration))
	if err != nil {
		log.ErrorWithCtx(ctx, "Clean CleanExpiredOrderRedemption err: %v", err)
		return
	}

	for _, order := range orders {
		s.cleanExpiredOrderRedemption(ctx, order)
	}
	log.InfoWithCtx(ctx, "Clean CleanExpiredOrderRedemption count: %d", len(orders))
}

func (s *Store) cleanExpiredOrderRedemption(ctx context.Context, order *OrderRedemption) {
	// 先添加到归档表中
	query := fmt.Sprintf("INSERT INTO %s (uid, order_id, days, package_type, begin_time, end_time, status, create_time, update_time) VALUES(?,?,?,?,?,?,?,?,?)", orderRedemptionArchiveTblName)
	params := []interface{}{order.Uid, order.OrderId, order.Days, order.PackageType, order.BeginTime, order.EndTime, order.Status, order.CreateTime, order.UpdateTime}
	_, err := s.db.ExecContext(ctx, query, params...)
	if err != nil && !mysql.IsDupEntryError(err) {
		log.ErrorWithCtx(ctx, "Clean CleanExpiredOrderRedemption add %s err: %v", order.OrderId, err)
		return
	}

	// 删除原表中的数据
	query = fmt.Sprintf("delete from %s where order_id = ?", orderRedemptionTblName)
	_, err = s.db.ExecContext(ctx, query, order.OrderId)
	if err != nil {
		log.ErrorWithCtx(ctx, "Clean CleanExpiredOrderRedemption delete %s err: %v", order.OrderId, err)
		return
	}
}
