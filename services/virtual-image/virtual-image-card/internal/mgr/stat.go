package mgr

import (
	"context"
	"fmt"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/services/virtual-image/virtual-image-card/internal/config"
	"golang.52tt.com/services/virtual-image/virtual-image-card/internal/store"
	"math"
	"strings"
	"time"
)

func (m *Mgr) GenerateStat(ctx context.Context, startTime, endTime time.Time) (err error) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Hour)
	defer cancel()
	now := time.Now()
	log.InfoWithCtx(ctx, "GenerateStat start, startTime: %v, endTime: %v", startTime, endTime)
	defer func() {
		log.InfoWithCtx(ctx, "GenerateStat cost: %v", time.Since(now))
		if err != nil {
			m.SendFeiShuMsg("", []string{"生成财务报表失败"}, "")
		}
	}()

	// 捞取上周期核销有剩余的订单
	preOrders, err := m.store.GetPrePeriodLeftOrders(ctx, startTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "GenerateStat GetPrePeriodLeftOrders err: %v", err)
		return
	}
	log.InfoWithCtx(ctx, "GenerateStat GetPrePeriodLeftOrders count: %d", len(preOrders))

	// 捞取本周期购买的订单
	buyOrders, err := m.store.GetCurrentPeriodBuyOrders(ctx, startTime, endTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "GenerateStat GetCurrentPeriodBuyOrders err: %v", err)
		return
	}
	log.InfoWithCtx(ctx, "GenerateStat GetCurrentPeriodBuyOrders count: %d", len(buyOrders))

	// 捞取未完全核销的订单
	redemptionOrders, err := m.store.GetUndoneRedemptionOrders(ctx, startTime, endTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "GenerateStat GetCurrentPeriodRedemptionOrders err: %v", err)
		return
	}
	log.InfoWithCtx(ctx, "GenerateStat GetCurrentPeriodRedemptionOrders count: %d", len(redemptionOrders))

	// 捞取本周期退款的订单
	revokeOrders, err := m.store.GetCurrentPeriodRevokeOrders(ctx, startTime, endTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "GenerateStat GetCurrentPeriodRevokeOrders err: %v", err)
		return
	}
	log.InfoWithCtx(ctx, "GenerateStat GetCurrentPeriodRevokeOrders count: %d", len(revokeOrders))

	// 合并，生成本周期关联的订单
	currentPeriodOrders := m.calcCurrentPeriodOrders(ctx, startTime, endTime, preOrders, buyOrders, redemptionOrders, revokeOrders)
	log.InfoWithCtx(ctx, "GenerateStat currentPeriodOrders count: %d", len(currentPeriodOrders))

	// 聚合本周期关联的订单
	statList := m.aggregateCurrentPeriodOrders(ctx, startTime, endTime, currentPeriodOrders)
	log.InfoWithCtx(ctx, "GenerateStat statList count: %d", len(statList))

	// 写入订单统计表
	err = m.store.SetPeriodStatOrders(ctx, startTime, currentPeriodOrders)
	if err != nil {
		log.ErrorWithCtx(ctx, "GenerateStat SetPeriodStatOrders err: %v", err)
		return
	}

	// 写入统计表
	err = m.store.SetPeriodStatInfos(ctx, startTime, statList)
	if err != nil {
		log.ErrorWithCtx(ctx, "GenerateStat SetPeriodStatInfos err: %v", err)
		return
	}

	log.InfoWithCtx(ctx, "GenerateStat success, startTime: %v, endTime: %v", startTime, endTime)
	return
}

func (m *Mgr) calcCurrentPeriodOrders(ctx context.Context, startTime, endTime time.Time, preOrders []*store.OrderStat,
	buyOrders []*store.Order, redemptionOrders []*store.OrderRedemption, revokeOrders []*store.OrderRevokeFlow) []*store.OrderStat {
	orderMap := make(map[string]*store.OrderStat)

	// 遗留和购买，用来初始化
	for _, order := range preOrders {
		orderMap[order.OrderId] = &store.OrderStat{
			OrderId:             order.OrderId,
			Uid:                 order.Uid,
			PackageId:           order.PackageId,
			PackageName:         order.PackageName,
			PayChannel:          order.PayChannel,
			Price:               order.Price,
			Days:                order.Days,
			DailyPrice:          order.DailyPrice,
			PayTime:             order.PayTime,
			SettleStartTime:     order.SettleStartTime,
			SettleEndTime:       order.SettleEndTime,
			BeforeLeftDays:      order.LeftDays,
			BeforeLeftPrice:     order.LeftPrice,
			BuyDays:             0,
			BuyPrice:            0,
			RedemptionDays:      0,
			RedemptionPrice:     0,
			RevokePrice:         0,
			RevokeRollbackDays:  0,
			RevokeRollbackPrice: 0,
			LeftDays:            order.LeftDays,
			LeftPrice:           order.LeftPrice,
		}
	}
	for _, order := range buyOrders {
		orderMap[order.OrderId] = &store.OrderStat{
			OrderId:             order.OrderId,
			Uid:                 order.ActiveUid,
			PackageId:           order.PackageId,
			PackageName:         order.Package.Name,
			PayChannel:          order.CoinPayChannel,
			Price:               float64(order.PayPriceCent) / 100,
			Days:                order.Package.Days,
			DailyPrice:          float64(order.PayPriceCent) / 100 / float64(order.Package.Days),
			PayTime:             order.PayTime,
			SettleStartTime:     time.Time{},
			SettleEndTime:       time.Time{},
			BeforeLeftDays:      0,
			BeforeLeftPrice:     0,
			BuyDays:             order.Package.Days,
			BuyPrice:            float64(order.PayPriceCent) / 100,
			RedemptionDays:      0,
			RedemptionPrice:     0,
			RevokePrice:         0,
			RevokeRollbackDays:  0,
			RevokeRollbackPrice: 0,
			LeftDays:            order.Package.Days,
			LeftPrice:           float64(order.PayPriceCent) / 100,
		}
	}

	// 计算核销带来的变化
	for _, redemption := range redemptionOrders {
		order, ok := orderMap[redemption.OrderId]
		if !ok {
			continue
		}

		// 更新核销起止时间
		order.SettleStartTime = redemption.BeginTime
		order.SettleEndTime = redemption.EndTime

		// 更新 核销天数/剩余天数
		if redemption.BeginTime.After(endTime) || redemption.BeginTime.Equal(endTime) { // 还没开始核销
			order.RedemptionDays = 0
			order.LeftDays = order.Days
		} else { // 已经开始核销
			left, right := startTime, endTime
			if redemption.BeginTime.After(startTime) {
				left = redemption.BeginTime
			}
			if redemption.EndTime.Before(endTime) {
				right = redemption.EndTime
			}
			order.RedemptionDays = uint32(math.Ceil(right.Sub(left).Hours() / 24))

			order.LeftDays = 0
			if redemption.EndTime.After(endTime) { // 还有剩
				order.LeftDays = uint32(math.Ceil(redemption.EndTime.Sub(endTime).Hours() / 24))
			}
		}

		order.RedemptionPrice = order.DailyPrice * float64(order.RedemptionDays)
		order.LeftPrice = order.DailyPrice * float64(order.LeftDays)
	}

	// 计算退款带来的变化
	for _, revoke := range revokeOrders {
		order, ok := orderMap[revoke.OrderId]
		if !ok {
			continue
		}

		order.RevokePrice = order.Price
		order.RevokeRollbackDays = revoke.RevokeDays
		order.RevokeRollbackPrice = float64(revoke.RevokeDays) * order.DailyPrice
		order.LeftDays = 0
		order.LeftPrice = 0
	}

	// 判断是否要过滤沙盒，并计算误差
	ignoreSandboxInStat := config.GetDynamicConfig().IgnoreSandboxInStat
	orderList := make([]*store.OrderStat, 0, len(orderMap))
	for _, order := range orderMap {
		if ignoreSandboxInStat && strings.Contains(order.PayChannel, "SANDBOX") {
			continue
		}
		order.DiffCheck = math.Abs(order.BeforeLeftPrice + order.BuyPrice - order.RedemptionPrice - order.RevokeRollbackPrice - order.LeftPrice)
		orderList = append(orderList, order)
	}
	return orderList
}

func (m *Mgr) aggregateCurrentPeriodOrders(ctx context.Context, startTime, endTime time.Time, orderList []*store.OrderStat) []*store.StatInfo {
	statMap := make(map[string]*store.StatInfo)
	periodStr := startTime.Format("200601")

	for _, order := range orderList {
		key := fmt.Sprintf("%d:%s:%.2f", order.PackageId, order.PayChannel, order.Price)
		stat, ok := statMap[key]
		if !ok {
			stat = &store.StatInfo{
				PeriodStr:           periodStr,
				PackageId:           order.PackageId,
				PackageName:         order.PackageName,
				PayChannel:          order.PayChannel,
				Price:               order.Price,
				Days:                order.Days,
				DailyPrice:          order.DailyPrice,
				BeforeLeftDays:      order.BeforeLeftDays,
				BeforeLeftPrice:     order.BeforeLeftPrice,
				LeftDays:            order.LeftDays,
				LeftPrice:           order.LeftPrice,
				BuyDays:             order.BuyDays,
				BuyPrice:            order.BuyPrice,
				RedemptionDays:      order.RedemptionDays,
				RedemptionPrice:     order.RedemptionPrice,
				RevokePrice:         order.RevokePrice,
				RevokeRollbackDays:  order.RevokeRollbackDays,
				RevokeRollbackPrice: order.RevokeRollbackPrice,
			}
		} else {
			stat.BeforeLeftDays += order.BeforeLeftDays
			stat.BeforeLeftPrice += order.BeforeLeftPrice
			stat.LeftDays += order.LeftDays
			stat.LeftPrice += order.LeftPrice
			stat.BuyDays += order.BuyDays
			stat.BuyPrice += order.BuyPrice
			stat.RedemptionDays += order.RedemptionDays
			stat.RedemptionPrice += order.RedemptionPrice
			stat.RevokePrice += order.RevokePrice
			stat.RevokeRollbackDays += order.RevokeRollbackDays
			stat.RevokeRollbackPrice += order.RevokeRollbackPrice
		}
		statMap[key] = stat
	}

	statList := make([]*store.StatInfo, 0, len(statMap))
	for _, stat := range statMap {
		// 计算误差
		stat.DiffCheck = math.Abs(stat.BeforeLeftPrice + stat.BuyPrice - stat.RedemptionPrice - stat.RevokeRollbackPrice - stat.LeftPrice)
		statList = append(statList, stat)
	}
	return statList
}
