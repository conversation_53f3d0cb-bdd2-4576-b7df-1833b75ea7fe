package mgr

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/golang/protobuf/proto"
	"golang.52tt.com/clients/account"
	push "golang.52tt.com/clients/push-notification/v2"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	pushPb "golang.52tt.com/protocol/app/push"
	"golang.52tt.com/protocol/app/virtual_image_logic"
	"golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafka_virtual_image_card"
	push_notification "golang.52tt.com/protocol/services/push-notification/v2"
	pb "golang.52tt.com/protocol/services/virtual-image-card"
	"golang.52tt.com/services/tt-rev/common/feishu"
	"golang.52tt.com/services/virtual-image/virtual-image-card/internal/config"
	"golang.52tt.com/services/virtual-image/virtual-image-card/internal/store"
	"math"
	"time"
)

func (m *Mgr) SendFeiShuMsg(title string, textLines []string, atUser string) {
	lineList := make([][]*feishu.LineMem, 0, len(textLines))
	for _, text := range textLines {
		line := []*feishu.LineMem{
			{Tag: "text", Text: text},
		}
		lineList = append(lineList, line)
	}

	if atUser != "" {
		line := []*feishu.LineMem{
			{Tag: "at", UserId: atUser},
		}
		lineList = append(lineList, line)
	}

	_ = feishu.SendFeiShuRichMsg(config.GetDynamicConfig().FeiShuUrl, title, lineList)
}

func (m *Mgr) sendTTAssistantText(ctx context.Context, uid uint32, content, highlight, url string) {
	_, err := m.rpcCli.ImApiCli.SimpleSendTTAssistantText(ctx, uid, content, highlight, url)
	if err != nil {
		log.ErrorWithCtx(ctx, "%d sendTTAssistantText err: %v", uid, err)
	} else {
		log.InfoWithCtx(ctx, "%d sendTTAssistantText success, content: %s", uid, content)
	}
}

func buildNotification(inputMsg proto.Message, cmd uint32, labelString string) *push_notification.CompositiveNotification {
	msg, _ := proto.Marshal(inputMsg)
	pushMessage := &pushPb.PushMessage{
		Cmd:     cmd,
		Content: msg,
	}
	pushMessageBytes, _ := proto.Marshal(pushMessage)
	return &push_notification.CompositiveNotification{
		Sequence:           uint32(time.Now().Unix()),
		TerminalTypeList:   []uint32{protocol.MobileAndroidTT, protocol.MobileIPhoneTT},
		TerminalTypePolicy: push.DefaultPolicy,
		AppId:              0,
		ProxyNotification: &push_notification.ProxyNotification{
			Type:      uint32(push_notification.ProxyNotification_PUSH),
			Payload:   pushMessageBytes,
			PushLabel: labelString,
		},
	}
}

func (m *Mgr) pushCardStatusChangeNotify(ctx context.Context, uid uint32, expireTime time.Time) {
	opt := &virtual_image_logic.VirtualImageCardStatusChangeNotify{
		ExpireTime: uint32(expireTime.Unix()),
	}
	notification := buildNotification(opt, uint32(pushPb.PushMessage_VIRTUAL_IMAGE_CARD_STATUS_CHANGE_NOTIFY), "无限换装卡变更推送")
	err := m.rpcCli.PushCli.PushToUsers(ctx, []uint32{uid}, notification)
	if err != nil {
		log.ErrorWithCtx(ctx, "%d pushCardStatusChangeNotify err: %v", uid, err)
	} else {
		log.InfoWithCtx(ctx, "%d pushCardStatusChangeNotify success, opt: %+v", uid, opt)
	}
}

func (m *Mgr) pushPayResultNotify(ctx context.Context, uid uint32, resultType virtual_image_logic.VirtualImageCardPayResultExt_ResultType, popupInfo string) {
	ext := &virtual_image_logic.VirtualImageCardPayResultExt{
		ResultType: resultType,
		PopupInfo:  popupInfo,
	}
	extStr, _ := json.Marshal(ext)
	opt := &pushPb.CommonRmbPayResultNotify{
		BusinessType: pushPb.CommonRmbPayResultNotify_BUSINESS_TYPE_VIRTUAL_IMAGE_CARD,
		Result:       true,
		Reason:       "SUCCESS",
		ExtMsg:       string(extStr),
	}
	notification := buildNotification(opt, uint32(pushPb.PushMessage_COMMON_RMB_PAY_RESULT_NOTIFY), "无限换装卡支付结果推送")
	err := m.rpcCli.PushCli.PushToUsers(ctx, []uint32{uid}, notification)
	if err != nil {
		log.ErrorWithCtx(ctx, "%d pushPayResultNotify err: %v", uid, err)
	} else {
		log.InfoWithCtx(ctx, "%d pushPayResultNotify success, opt: %+v", uid, opt)
	}
}

func (m *Mgr) processPush4PayCallback(ctx context.Context, orderId string) {
	// 获取最新的订单详情
	order, err := m.store.GetOrder(ctx, orderId, nil)
	if err != nil {
		log.ErrorWithCtx(ctx, "%s processPush4PayCallback GetOrder err: %v", orderId, err)
		return
	}
	if order == nil {
		return
	}
	// 查询用户最新的背包
	remain, err := m.store.GetRemain(ctx, order.ActiveUid, nil)
	if err != nil {
		log.ErrorWithCtx(ctx, "%s processPush4PayCallback GetRemain err: %v", orderId, err)
		return
	}
	if remain == nil {
		return
	}
	// 查询用户已有的签约列表
	contractList, err := m.store.GetUserContractList(ctx, order.ActiveUid, nil)
	if err != nil {
		log.ErrorWithCtx(ctx, "%s processPush4PayCallback GetUserContractList err: %v", orderId, err)
		return
	}
	// 查询帐号信息
	userMap, err := m.rpcCli.AccountCli.GetUsersMap(ctx, []uint32{order.Uid, order.ActiveUid})
	if err != nil {
		log.ErrorWithCtx(ctx, "%s processPush4PayCallback GetUsersMap err: %v", orderId, err)
		return
	}

	// 发送TT助手消息：购买成功
	content := fmt.Sprintf("开通虚拟形象无限换装卡成功！\n开通时长：%d天\n开通套餐：%s\n剩余有效期至：%s\n有效期内可免费使用虚拟形象商城海量商品，快去试试吧。 去试试＞",
		order.Package.Days, order.Package.Name, remain.ExpireTime.Format("2006-01-02 15:04"))
	if order.OrderRole == store.OrderRoleSystem {
		content = fmt.Sprintf("自动续费虚拟形象无限换装卡成功！\n续费时长：%d天\n剩余有效期至：%s\n有效期内可免费使用虚拟形象商城海量商品，快去试试吧。 去试试＞",
			order.Package.Days, remain.ExpireTime.Format("2006-01-02 15:04"))
	}
	m.sendTTAssistantText(ctx, order.ActiveUid, content, "去试试＞", "tt://m.52tt.com/show_virtual_image_shop")

	// 发送不同的支付成功推送
	if order.Uid != order.ActiveUid { // 给别人开通的异常情况
		userInfo, ok := userMap[order.ActiveUid]
		if !ok {
			userInfo = &account.User{}
		}
		// 弹窗
		popupInfo := fmt.Sprintf("同个苹果账号只能为一个ID开通连续订阅服务，您当前的苹果账号已为ID：%s开通连续订阅服务,请登录ID：%s查看", userInfo.Alias, userInfo.Alias)
		m.pushPayResultNotify(ctx, order.Uid, virtual_image_logic.VirtualImageCardPayResultExt_RESULT_TYPE_PAY_FOR_OTHER, popupInfo)
		// 助手推送
		content = fmt.Sprintf("您在%s购买的无限换装卡自动续费套餐实际到账账号为%s（ID：%s）。温馨提示：一个苹果账号只能为一个app账号开通无限换装卡连续订阅服务，您可选择为当前账号开通非自动续费类型的无限换装卡套餐。",
			order.PayTime.Format("1月2日15:04:05"), userInfo.Nickname, userInfo.Alias)
		m.sendTTAssistantText(ctx, order.Uid, content, "", "")

	} else if order.Package.PackageType == uint32(pb.PackageType_PACKAGE_TYPE_NORMAL) && len(contractList) > 0 { // 单买，但已有签约
		// 弹窗
		popupInfo := "您的账户同时存在苹果App Store和支付宝签约的连续订阅套餐，如需取消连续订阅，请在苹果手机【设置】-【iTunes Store与App Store】【Apple ID】-【订阅项目】中取消订阅；并在支付宝设置中关闭无限换装卡连续订阅服务"
		if len(contractList) == 1 && contractList[0].PayChannel == uint8(pb.PayChannel_PAY_CHANNEL_ALIPAY) {
			popupInfo = "当前账户已签约连续订阅套餐，如需取消，请在手机打开支付宝，选择【我的】-【设置】-【支付设置】-【免密支付/自动扣款】-选中无限换装卡并关闭服务"
		}
		if len(contractList) == 1 && contractList[0].PayChannel == uint8(pb.PayChannel_PAY_CHANNEL_APPSTORE) {
			popupInfo = "当前账户已签约连续订阅套餐，如需取消，请在手机【设置】-【iTunes Store与App Store】-【Apple ID】-【订阅项目】中取消订阅"
		}
		m.pushPayResultNotify(ctx, order.ActiveUid, virtual_image_logic.VirtualImageCardPayResultExt_RESULT_TYPE_ALREADY_CONTRACT, popupInfo)
		// 助手推送
		for _, contract := range contractList {
			if contract.PayChannel == uint8(pb.PayChannel_PAY_CHANNEL_ALIPAY) {
				m.sendTTAssistantText(ctx, order.ActiveUid, "当前账户已签约连续订阅套餐，如需取消，请在手机打开支付宝，选择【我的】-【设置】-【支付设置】-【免密支付/自动扣款】-选中无限换装卡并关闭服务", "", "")
			}
			if contract.PayChannel == uint8(pb.PayChannel_PAY_CHANNEL_APPSTORE) {
				m.sendTTAssistantText(ctx, order.ActiveUid, "当前账户已签约连续订阅套餐，如需取消，请在手机【设置】-【iTunes Store与App Store】-【Apple ID】-【订阅项目】中取消订阅", "", "")
			}
		}

	} else { // 普通情况
		m.pushPayResultNotify(ctx, order.ActiveUid, virtual_image_logic.VirtualImageCardPayResultExt_RESULT_TYPE_UNSPECIFIED, "")
	}
}

func (m *Mgr) processPush4NotifyContract(ctx context.Context, contractId string, isNewSign bool) {
	// 查询最新的签约详情
	contract, err := m.store.GetContract(ctx, contractId, nil)
	if err != nil {
		log.ErrorWithCtx(ctx, "%s processPush4NotifyContract GetContract err: %v", contractId, err)
		return
	}
	if contract == nil {
		return
	}

	if isNewSign {
		content := fmt.Sprintf("亲爱的用户，您已开通虚拟形象无限换装卡自动续费服务，系统将于%s前自动扣款，请确保您的签约账号有足够的余额以避免扣款失败导致无限换装卡权益失效。若想取消自动续费，可前往无限换装卡常见问题页查看指引。点击前往＞", contract.NextPayTime.Format("2006-01-02"))
		link := "https://activity.52tt.com/yy-act/rules/xunixxykfaq/"
		if contract.PayChannel == uint8(pb.PayChannel_PAY_CHANNEL_APPSTORE) {
			link = "https://activity.52tt.com/yy-act/rules/xunixxykfaqpg/"
		}
		m.sendTTAssistantText(ctx, contract.Uid, content, "点击前往＞", link)
	}
}

func (m *Mgr) processPush4FailAlipayAutoPay(ctx context.Context, orderId string, remain *store.Remain) {
	// 检查是否已经推送过
	err := m.cache.TryLockPushFailAutoPay(ctx, orderId)
	if err != nil {
		return
	}

	content := fmt.Sprintf("【温馨提示】您的虚拟形象无限换装卡自动续费扣款失败，请在%s前确保账户余额充足，否则无限换装卡权益将到期失效。", remain.ExpireTime.Format("2006-01-02"))
	m.sendTTAssistantText(ctx, remain.Uid, content, "", "")
}

func (m *Mgr) processPush4FailAppstoreAutoPay(ctx context.Context, contract *store.Contract) {
	ctx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	// 检查是否已经过了很久还没扣费成功
	now := time.Now()
	if now.Sub(contract.NextPayTime) < time.Second*time.Duration(config.GetDynamicConfig().PayApiConf.IosAutoPayFailPushWaitSecond) || now.Sub(contract.NextPayTime) > time.Hour*24*7 {
		return
	}

	// 检查是否已经推送过
	orderId := store.GenOrderId(contract.Uid, contract.NextPayTime)
	err := m.cache.TryLockPushFailAutoPay(ctx, orderId)
	if err != nil {
		return
	}

	remain, err := m.store.GetRemain(ctx, contract.Uid, nil)
	if err != nil || remain == nil {
		return
	}
	content := fmt.Sprintf("【温馨提示】您的虚拟形象无限换装卡自动续费扣款失败，请在%s前确保账户余额充足，否则无限换装卡权益将到期失效。", remain.ExpireTime.Format("2006-01-02"))
	m.sendTTAssistantText(ctx, remain.Uid, content, "", "")
}

func (m *Mgr) processPush4NextPayPreNotify(ctx context.Context, contract *store.Contract) {
	ctx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	// 已经过了当前时间，那就没处理的必要了
	now := time.Now()
	if contract.NextPayTime.Before(now) {
		return
	}

	// 检查是否已经推送过
	err := m.cache.TryLockNextPayPreNotify(ctx, contract.Uid, contract.NextPayTime)
	if err != nil {
		return
	}

	content := fmt.Sprintf("【温馨提示】您的虚拟形象无限换装卡将于%s前自动续费，请确保账户余额充足。若想取消自动续费服务，可前往无限换装卡常见问题页查看指引。点击前往＞", contract.NextPayTime.Format("2006/01/02"))
	link := "https://activity.52tt.com/yy-act/rules/xunixxykfaq/"
	if contract.PayChannel == uint8(pb.PayChannel_PAY_CHANNEL_APPSTORE) {
		link = "https://activity.52tt.com/yy-act/rules/xunixxykfaqpg/"
	}
	m.sendTTAssistantText(ctx, contract.Uid, content, "点击前往＞", link)
}

func (m *Mgr) processPush4ExpiringAlert(ctx context.Context, remain *store.Remain) {
	ctx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	// 已经过了当前时间，那就没处理的必要了
	now := time.Now()
	if remain.ExpireTime.Before(now) {
		return
	}

	// 有签约信息的话不用推。从缓存拿避免db压力大
	cardInfo, err := m.GetUserCardInfo(ctx, remain.Uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "%d processPush4ExpiringAlert GetUserCardInfo err: %v", remain.Uid, err)
		return
	}
	if len(cardInfo.GetContracts()) > 0 {
		return
	}

	// 检查当天是否已推送过
	err = m.cache.TryLockPushExpiringAlert(ctx, remain.Uid, now.Format("20060102"))
	if err != nil {
		return
	}

	leftDays := uint32(math.Ceil(remain.ExpireTime.Sub(now).Hours() / 24))
	content := fmt.Sprintf("您的虚拟形象无限换装卡即将过期，剩余天数：不足%d天。过期后无法享用免费无限换装的权益，记得提前续费哦。立即续费＞", leftDays)
	m.sendTTAssistantText(ctx, remain.Uid, content, "立即续费＞", "tt://m.52tt.com/show_virtual_image_shop?show_buy_infinite_dialog=true")
}

func (m *Mgr) processPush4ExpiredNotify(ctx context.Context, uid uint32) {
	ctx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()
	now := time.Now()

	// 获取最新的用户信息
	cardInfo, err := m.GetUserCardInfo(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "%d processPush4ExpiredNotify GetUserCardInfo err: %v", uid, err)
		return
	}
	log.InfoWithCtx(ctx, "%d processPush4ExpiredNotify GetUserCardInfo: %+v", uid, cardInfo)

	// 已经可以删缓存了，避免后面重复推送
	err = m.cache.DelExpiredUser(ctx, uid)
	if err != nil {
		log.WarnWithCtx(ctx, "%d processPush4ExpiredNotify DelExpiredUser err: %v", uid, err)
	}

	// 队列说过期了，但实际存储还没过期的异常情况
	if cardInfo.ExpireTs > now.Unix() {
		log.WarnWithCtx(ctx, "%d processPush4ExpiredNotify zset != store", uid)
		return
	}

	// 发送过期kafka消息
	m.producer.SendCardChangeEvent(ctx, &kfk_virtual_image_card.UserVirtualImageCardChangeEvent{
		Uid:       uid,
		EventTs:   now.Unix(),
		EventType: kfk_virtual_image_card.EventType_EVENT_TYPE_EXPIRED,
		EffectTs:  cardInfo.EffectTs,
		ExpireTs:  cardInfo.ExpireTs,
	})

	if len(cardInfo.Contracts) > 0 {
		content := "【温馨提示】您的虚拟形象无限换装卡自动续费扣款失败，无限换装权益已过期，若想继续使用权益，请保持签约账号余额充足。"
		m.sendTTAssistantText(ctx, uid, content, "", "")
	} else {
		content := "您的虚拟形象无限换装卡已过期，若想继续享用免费无限换装的权益，请前往再次开通。立即开通＞"
		m.sendTTAssistantText(ctx, uid, content, "立即开通＞", "tt://m.52tt.com/show_virtual_image_shop?show_buy_infinite_dialog=true")
	}
}
