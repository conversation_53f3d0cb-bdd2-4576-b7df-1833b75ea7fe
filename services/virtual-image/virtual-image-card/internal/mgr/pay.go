package mgr

import (
	"context"
	"fmt"
	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	protoGrpc "golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/virtual-image-card"
	"golang.52tt.com/services/tt-rev/common/goroutineex"
	"golang.52tt.com/services/virtual-image/virtual-image-card/internal/config"
	"golang.52tt.com/services/virtual-image/virtual-image-card/internal/pay_api"
	"golang.52tt.com/services/virtual-image/virtual-image-card/internal/store"
	"math"
	"time"
)

func (m *Mgr) PlaceOrder(ctx context.Context, in *pb.PlaceOrderReq) (out *pb.PlaceOrderResp, err error) {
	log.InfoWithCtx(ctx, "PlaceOrder in: %+v", in)
	out = &pb.PlaceOrderResp{}

	// 获得对应的套餐配置
	packageConf := m.localCache.GetPackageConf(in.PackageId)
	if packageConf == nil {
		err = protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "套餐不存在")
		return
	}
	isAuto := false // 是否自动续费套餐
	if packageConf.PackageType == uint32(pb.PackageType_PACKAGE_TYPE_AUTO_RENEW) {
		isAuto = true
	}

	// 共用参数
	svrInfo, _ := protoGrpc.ServiceInfoFromContext(ctx)
	now := time.Now()
	orderId := store.GenOrderId(in.Uid, now)
	var contractId string
	if isAuto {
		contractId = store.GenContractId(in.Uid, now)
	}

	// 如果订单享受优惠，要做频控。避免下了优惠单后，还没来得及处理回调，又下了另一个优惠单
	if isAuto && in.PayPriceCent == packageConf.DiscountPrice {
		err = m.cache.TryLockPlaceDiscountOrder(ctx, in.Uid, time.Second*time.Duration(config.GetDynamicConfig().PlaceDiscountOrderSecond))
		if err != nil {
			log.ErrorWithCtx(ctx, "PlaceOrder TryLockPlaceDiscountOrder err: %v", err)
			err = protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "发起订单太频繁，请稍后再试")
			return
		}
	}

	// 获取用户当前背包
	remain, err := m.store.GetRemain(ctx, in.Uid, nil)
	if err != nil {
		log.ErrorWithCtx(ctx, "PlaceOrder GetRemain err: %v", err)
		return
	}

	// 叠加开通时长上限检查，开通后有效期不能超过2年
	oldLeftDays := uint32(0)
	if remain != nil && remain.ExpireTime.After(now) {
		oldLeftDays = uint32(math.Ceil(remain.ExpireTime.Sub(now).Hours() / 24))
	}
	if oldLeftDays+packageConf.Days > config.GetDynamicConfig().MaxOpenDays {
		msg := fmt.Sprintf(config.GetDynamicConfig().MaxOpenDaysLimitHint, oldLeftDays, packageConf.Days)
		err = protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, msg)
		log.ErrorWithCtx(ctx, "PlaceOrder MaxOpenDaysLimit err: %v", err)
		return
	}

	// 计算下次扣款时间，主要是支付宝用，选在新的到期日前几天
	oldEffectTime, oldExpireTime, newEffectTime, newExpireTime := calcCardPeriodChange(remain, now, packageConf.Days)
	log.DebugWithCtx(ctx, "PlaceOrder oldEffectTime: %v, oldExpireTime: %v, newEffectTime: %v, newExpireTime: %v", oldEffectTime, oldExpireTime, newEffectTime, newExpireTime)
	var nextPayTime time.Time
	if isAuto {
		nextPayTime = config.GetAlipayNextPayTime(newExpireTime)
	}

	// 订单db参数
	order := &store.Order{
		Uid:        in.Uid,
		OrderId:    orderId,
		OrderRole:  store.OrderRoleUser,
		ContractId: contractId,
		PayChannel: uint8(in.PayChannel),
		PriceCent:  in.PayPriceCent,
		PackageId:  in.PackageId,
		CreateTime: now,
		Package:    *packageConf,
	}
	log.DebugWithCtx(ctx, "PlaceOrder order: %+v", *order)

	// 签约db参数
	var contract *store.Contract
	if isAuto {
		contract = &store.Contract{
			Uid:           in.Uid,
			ContractId:    contractId,
			MarketId:      uint8(svrInfo.MarketID),
			ClientType:    uint8(svrInfo.ClientType),
			ClientVersion: svrInfo.ClientVersion,
			PayChannel:    uint8(in.PayChannel),
			PackageId:     in.PackageId,
			NextPayTime:   nextPayTime,
			Package:       *packageConf,
		}
		log.DebugWithCtx(ctx, "PlaceOrder contract: %+v", *contract)
	}

	// 发起订单请求参数
	placeOrderReq := &pb.ApiPlaceOrderReq{
		OrderType:              pay_api.FormatOrderType(pb.PackageType(packageConf.PackageType)),
		OsType:                 pay_api.FormatOsType(svrInfo.ClientType),
		PayChannel:             pay_api.FormatPayChannel(in.PayChannel),
		BusinessId:             config.GetMarketBusinessId(svrInfo.MarketID, isAuto),
		Fm:                     config.GetMarketFm(svrInfo.MarketID),
		Version:                fmt.Sprintf("%d", svrInfo.ClientVersion),
		CliOrderNo:             orderId,
		CliBuyerId:             fmt.Sprintf("%d", in.Uid),
		CliPrice:               pay_api.ConvertCent2Yuan(order.PriceCent),
		CliOrderTitle:          fmt.Sprintf("购买无限换装卡%s", packageConf.Name),
		CliOrderDesc:           fmt.Sprintf("购买无限换装卡%s", packageConf.Name),
		CliNotifyUrl:           config.GetDynamicConfig().PayApiConf.PayNotifyUrl,
		CreateTime:             now.Format("2006-01-02 15:04:05"),
		BundleId:               packageConf.ProductId,
		ProductId:              packageConf.ProductId,
		TimeOut:                fmt.Sprintf("%d", config.GetDynamicConfig().PayApiConf.PlaceOrderTimeoutMinute),
		OriginalTransactionIds: in.OriginalTransactionIds,
		BusinessScenceCode:     pay_api.BusinessScenceCode,
	}
	if isAuto {
		placeOrderReq.PeriodParam = &pb.ApiPeriodParam{
			ContractId:        contractId,
			PlanId:            pay_api.FormatPlanId(in.PayChannel),
			PeriodType:        "DAY",
			Period:            int64(packageConf.Days),
			ContractNotifyUrl: config.GetDynamicConfig().PayApiConf.ContractNotifyUrl,
			ExecuteTime:       nextPayTime.Format("2006-01-02 15:04:05"),
			ProductCode:       pay_api.FormatProductCode(in.PayChannel),
			SingleAmount:      pay_api.ConvertCent2Yuan(packageConf.Price),
		}
	}
	log.DebugWithCtx(ctx, "PlaceOrder placeOrderReq: %+v", placeOrderReq)

	// 事务做各种更新
	var outRsp pb.ApiPlaceOrderResp
	err = m.store.Transaction(ctx, func(ctx context.Context, tx mysql.Txx) error {
		var terr error

		// db添加订单
		_, terr = m.store.AddOrder(ctx, order, tx)
		if terr != nil {
			log.ErrorWithCtx(ctx, "PlaceOrder AddOrder err: %v", terr)
			return terr
		}

		// db添加签约
		if contract != nil {
			_, terr = m.store.AddContract(ctx, contract, tx)
			if terr != nil {
				log.ErrorWithCtx(ctx, "PlaceOrder AddContract err: %v", terr)
				return terr
			}
		}

		// 发起支付请求
		subCtx := pay_api.GenPayCtx(ctx, in.Uid, svrInfo.MarketID)
		payRsp, terr := m.rpcCli.PayCli.PlaceOrder(subCtx, placeOrderReq)
		if terr != nil {
			log.ErrorWithCtx(ctx, "PlaceOrder PlaceOrder err: %v", terr)
			if svrErr, transOk := terr.(protocol.ServerError); transOk {
				if svrErr.Code() == status.ErrSuperPlayerInvalidAppstoreUser { // 苹果AB账号重复订阅错误
					return protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidAppstoreUser, svrErr.Message())
				}
			}
			return terr
		}
		outRsp = payRsp.(pb.ApiPlaceOrderResp)

		return nil
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "PlaceOrder Transaction err: %v", err)
		return
	}
	log.DebugWithCtx(ctx, "PlaceOrder placeOrderRsp: %+v", outRsp)

	// 返回结果
	out.OrderNo = outRsp.GetOrderNo()
	out.Token = outRsp.GetToken()
	out.CliOrderNo = outRsp.GetCliOrderNo()
	out.CliOrderTitle = outRsp.GetCliOrderTitle()
	out.OrderPrice = outRsp.GetOrderPrice()
	out.Tsk = outRsp.GetTsk()
	out.ChannelMap = outRsp.GetChannelMap()
	log.InfoWithCtx(ctx, "PlaceOrder success, in: %+v, out: %+v, packageConf: %+v", in, out, packageConf)

	return
}

func (m *Mgr) PlaceAlipayAutoOrder(ctx context.Context, contract *store.Contract) {
	ctx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	// 公共参数
	now := time.Now()
	orderId := store.GenOrderId(contract.Uid, contract.NextPayTime)
	packageConf := contract.Package

	// 避免重试太频繁，需要间隔一段时间
	err := m.cache.TryLockPlaceAutoOrder(ctx, orderId, time.Hour)
	if err != nil {
		return
	}

	// 获取当前背包
	remain, err := m.store.GetRemain(ctx, contract.Uid, nil)
	if err != nil {
		log.ErrorWithCtx(ctx, "PlaceAlipayAutoOrder GetRemain err: %v", err)
		return
	}
	if remain == nil { // 不应该出现
		log.ErrorWithCtx(ctx, "PlaceAlipayAutoOrder remain not found, uid: %d", contract.Uid)
		return
	}
	// 已经过期好久就不继续往下走了
	if contract.NextPayTime.Sub(remain.ExpireTime) > time.Second*time.Duration(config.GetDynamicConfig().PayApiConf.StopAutoOrderWaitSecond) {
		return
	}

	log.DebugWithCtx(ctx, "PlaceAlipayAutoOrder enter, contract: %+v", *contract)

	// 一直扣款不成功，发起主动解约。免得后面突然有钱扣上了，用户忘记这事，然后来投诉
	if config.CanSystemCancelContract(contract.NextPayTime) {
		m.systemCancelContract(ctx, contract, "自动下单一直失败")
		return
	}

	// 检查对应订单是否ok了，有的话直接返回成功
	preOrder, err := m.store.GetOrder(ctx, orderId, nil)
	if err != nil {
		log.ErrorWithCtx(ctx, "PlaceAlipayAutoOrder GetOrder err: %v", err)
		return
	}
	if preOrder != nil && preOrder.Status == store.OrderStatusPaid {
		log.WarnWithCtx(ctx, "PlaceAlipayAutoOrder order already handle, order: %+v", *preOrder)
		return
	}

	// 订单db参数
	order := &store.Order{
		Uid:        contract.Uid,
		OrderId:    orderId,
		OrderRole:  store.OrderRoleSystem,
		ContractId: contract.ContractId,
		PayChannel: contract.PayChannel,
		PriceCent:  packageConf.Price,
		PackageId:  contract.PackageId,
		CreateTime: contract.NextPayTime,
		Package:    packageConf,
	}
	log.DebugWithCtx(ctx, "PlaceAlipayAutoOrder order: %+v", *order)

	// 发起订单请求参数
	autoPayReq := &pb.ApiAutoPayReq{
		OrderType:     pay_api.FormatOrderType(pb.PackageType(packageConf.PackageType)),
		OsType:        pay_api.FormatOsType(uint16(contract.ClientType)),
		PayChannel:    pay_api.FormatPayChannel(pb.PayChannel(contract.PayChannel)),
		BusinessId:    config.GetMarketBusinessId(uint32(contract.MarketId), true),
		Fm:            config.GetMarketFm(uint32(contract.MarketId)),
		Version:       fmt.Sprintf("%d", contract.ClientVersion),
		CliOrderNo:    orderId,
		CliBuyerId:    fmt.Sprintf("%d", contract.Uid),
		CliPrice:      pay_api.ConvertCent2Yuan(packageConf.Price),
		CliOrderTitle: fmt.Sprintf("购买无限换装卡%s", packageConf.Name),
		CliOrderDesc:  fmt.Sprintf("购买无限换装卡%s", packageConf.Name),
		CliNotifyUrl:  config.GetDynamicConfig().PayApiConf.PayNotifyUrl,
		CreateTime:    now.Format("2006-01-02 15:04:05"),
		BundleId:      packageConf.ProductId,
		ProductId:     packageConf.ProductId,
		DeductParam: &pb.DeductParam{
			ContractId:   contract.ContractId,
			PlanId:       pay_api.FormatPlanId(pb.PayChannel(contract.PayChannel)),
			ProductCode:  pay_api.FormatProductCode(pb.PayChannel(contract.PayChannel)),
			SingleAmount: pay_api.ConvertCent2Yuan(packageConf.Price),
		},
		BusinessScenceCode: pay_api.BusinessScenceCode,
	}
	log.DebugWithCtx(ctx, "PlaceAlipayAutoOrder autoPayReq: %+v", autoPayReq)

	// 事务做各种更新
	err = m.store.Transaction(ctx, func(ctx context.Context, tx mysql.Txx) error {
		var terr error

		// db添加订单
		_, terr = m.store.AddOrder(ctx, order, tx)
		if terr != nil && !mysql.IsDupEntryError(terr) { // 忽略重复错误
			log.ErrorWithCtx(ctx, "PlaceAlipayAutoOrder AddOrder err: %v", terr)
			return terr
		}

		// 发起支付请求
		subCtx := pay_api.GenPayCtx(ctx, contract.Uid, uint32(contract.MarketId))
		_, terr = m.rpcCli.PayCli.AutoPay(subCtx, autoPayReq)
		if terr != nil {
			log.ErrorWithCtx(ctx, "PlaceAlipayAutoOrder AutoPay err: %v", terr)
			return terr
		}

		return nil
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "PlaceAlipayAutoOrder Transaction err: %v", err)
		// 进一步处理扣款错误

		if pay_api.IsBalanceNotEnoughErr(err) { // 余额不足
			// 要发送一次余额不足通知
			m.processPush4FailAlipayAutoPay(ctx, orderId, remain)

		} else if pay_api.IsDelayPayErr(err) { // 扣款时间不对
			subCtx := pay_api.GenPayCtx(ctx, contract.Uid, uint32(contract.MarketId))
			delayPayReq := &pb.ApiDelayPayReq{
				ContractId:         contract.ContractId,
				BusinessId:         config.GetMarketBusinessId(uint32(contract.MarketId), true),
				BuyerId:            fmt.Sprintf("%d", contract.Uid),
				DeductTime:         now.Format("2006-01-02 15:04:05"),
				Memo:               "发起延期扣款",
				PayChannel:         pay_api.FormatPayChannel(pb.PayChannel(contract.PayChannel)),
				BusinessScenceCode: pay_api.BusinessScenceCode,
			}
			_, terr := m.rpcCli.PayCli.DelayPay(subCtx, delayPayReq)
			if err != nil {
				log.ErrorWithCtx(ctx, "PlaceAlipayAutoOrder DelayPay err: %v, delayPayReq: %+v", terr, delayPayReq)
			} else {
				log.InfoWithCtx(ctx, "PlaceAlipayAutoOrder DelayPay success, delayPayReq: %+v", delayPayReq)
			}

		} else if pay_api.IsCloseOrderErr(err) {
			reason := fmt.Sprintf("自动下单err：%s", err.Error())
			m.systemCancelContract(ctx, contract, reason)

		} else {
			lines := []string{
				fmt.Sprintf("contract_id: %s", contract.ContractId),
				fmt.Sprintf("uid: %d", contract.Uid),
				fmt.Sprintf("err: %v", err),
			}
			m.SendFeiShuMsg("支付宝自动扣费未知错误", lines, "")
		}
	} else {
		log.InfoWithCtx(ctx, "PlaceAlipayAutoOrder success, contract: %+v", *contract)
	}
}

// systemCancelContract 系统取消签约
func (m *Mgr) systemCancelContract(ctx context.Context, contract *store.Contract, reason string) {
	log.InfoWithCtx(ctx, "systemCancelContract: %+v", *contract)
	contractId, uid := contract.ContractId, contract.Uid

	// 发起解约请求参数
	cancelContractReq := &pb.ApiCancelContractReq{
		ContractId:         contract.ContractId,
		BusinessId:         config.GetMarketBusinessId(uint32(contract.MarketId), true),
		BuyerId:            fmt.Sprintf("%v", contract.Uid),
		ContractNotifyUrl:  config.GetDynamicConfig().PayApiConf.ContractNotifyUrl,
		ProductCode:        pay_api.FormatProductCode(pb.PayChannel(contract.PayChannel)),
		BusinessScenceCode: pay_api.BusinessScenceCode,
	}
	log.DebugWithCtx(ctx, "systemCancelContract cancelContractReq: %+v", cancelContractReq)

	// 事务做各种更新
	err := m.store.Transaction(ctx, func(ctx context.Context, tx mysql.Txx) error {
		// 更新db状态
		_, terr := m.store.UpdateContractStatus(ctx, contract.ContractId, store.ContractStatusCanceled, store.ContractStatusSigned, tx)
		if terr != nil {
			log.ErrorWithCtx(ctx, "%s systemCancelContract UpdateContractStatus err: %v", contractId, terr)
			return terr
		}

		// 把变更写个db记录
		_, terr = m.store.AddContractHistory(ctx, &store.ContractHistory{
			ContractId:    contract.ContractId,
			Uid:           contract.Uid,
			PayChannel:    contract.PayChannel,
			PackageId:     contract.PackageId,
			PackageInfo:   contract.PackageInfo,
			Status:        store.ContractStatusCanceled,
			OperationRole: store.ContractHistoryOperationRoleSystem,
			Reason:        reason,
			CreateTime:    time.Now(),
		}, tx)
		if terr != nil {
			log.ErrorWithCtx(ctx, "%s systemCancelContract AddContractHistory err: %v", contractId, terr)
			return terr
		}

		// 发起解约
		subCtx := pay_api.GenPayCtx(ctx, contract.Uid, uint32(contract.MarketId))
		resp, terr := m.rpcCli.PayCli.CancelContract(subCtx, cancelContractReq)
		if terr != nil {
			log.ErrorWithCtx(ctx, "%s systemCancelContract CancelContract err: %v", contractId, terr)
			return terr
		}
		cancelContractResp := resp.(pb.ApiCancelContractResp)
		log.DebugWithCtx(ctx, "%s systemCancelContract cancelContractResp: %+v", contractId, cancelContractResp)
		if !pay_api.IsCancelContractCodeOk(cancelContractResp.Code) {
			log.ErrorWithCtx(ctx, "%s systemCancelContract CancelContract fail: %+v", contractId, cancelContractResp)
			return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "发起解约失败")
		}

		return nil
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "%s systemCancelContract Transaction err: %v", contractId, err)
		return
	}

	// 各种变更通知
	goroutineex.GoroutineWithTimeoutCtx(ctx, time.Second*10, func(ctx context.Context) {
		_ = m.cache.DelUserCard(ctx, uid) // 删除用户缓存
	})
}

func (m *Mgr) PlaceAutoPayOrder(ctx context.Context, in *pb.PlaceAutoPayOrderReq) error {
	log.InfoWithCtx(ctx, "PlaceAutoPayOrder in: %+v", in)

	contract, err := m.store.GetContract(ctx, in.ContractId, nil)
	if err != nil {
		log.ErrorWithCtx(ctx, "PlaceAutoPayOrder GetContract err: %v", err)
		return err
	}
	if contract == nil {
		log.ErrorWithCtx(ctx, "PlaceAutoPayOrder contract not found, contractId: %s", in.ContractId)
		return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "签约信息不存在")
	}
	log.DebugWithCtx(ctx, "PlaceAutoPayOrder contract: %+v", *contract)

	// 公共参数
	now := time.Now()
	orderId := store.GenOrderId(contract.Uid, contract.NextPayTime)
	packageConf := contract.Package

	// 避免重试太频繁，需要间隔一段时间
	err = m.cache.TryLockPlaceAutoOrder(ctx, orderId, time.Minute)
	if err != nil {
		return err
	}

	// 苹果的话，套餐可能变了，要更新一下
	if len(in.ProductId) > 0 && in.ProductId != contract.Package.ProductId {
		log.WarnWithCtx(ctx, "PlaceAutoPayOrder productId changed, contractId: %s, old: %s, new: %s", contract.ContractId, contract.Package.ProductId, in.ProductId)
		newConf := m.localCache.GetProductConf(in.ProductId)
		if newConf == nil {
			log.ErrorWithCtx(ctx, "PlaceAutoPayOrder productId not found, productId: %s", in.ProductId)
			return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "套餐不存在")
		}
		contract.PackageId = newConf.Id
		contract.Package = *newConf
		packageConf = *newConf
	}

	// 检查对应订单是否ok了，有的话直接返回成功
	preOrder, err := m.store.GetOrder(ctx, orderId, nil)
	if err != nil {
		log.ErrorWithCtx(ctx, "PlaceAutoPayOrder GetOrder err: %v", err)
		return err
	}
	if preOrder != nil && preOrder.Status == store.OrderStatusPaid {
		log.WarnWithCtx(ctx, "PlaceAutoPayOrder order already handle, order: %+v", *preOrder)
		return nil
	}

	// 订单db参数
	order := &store.Order{
		Uid:        contract.Uid,
		OrderId:    orderId,
		OrderRole:  store.OrderRoleSystem,
		ContractId: contract.ContractId,
		PayChannel: contract.PayChannel,
		PriceCent:  packageConf.Price,
		PackageId:  contract.PackageId,
		CreateTime: contract.NextPayTime,
		Package:    packageConf,
	}
	log.DebugWithCtx(ctx, "PlaceAutoPayOrder order: %+v", *order)

	// 发起订单请求参数
	autoPayReq := &pb.ApiAutoPayReq{
		OrderType:     pay_api.FormatOrderType(pb.PackageType(packageConf.PackageType)),
		OsType:        pay_api.FormatOsType(uint16(contract.ClientType)),
		PayChannel:    pay_api.FormatPayChannel(pb.PayChannel(contract.PayChannel)),
		BusinessId:    config.GetMarketBusinessId(uint32(contract.MarketId), true),
		Fm:            config.GetMarketFm(uint32(contract.MarketId)),
		Version:       fmt.Sprintf("%d", contract.ClientVersion),
		CliOrderNo:    orderId,
		CliBuyerId:    fmt.Sprintf("%d", contract.Uid),
		CliPrice:      pay_api.ConvertCent2Yuan(packageConf.Price),
		CliOrderTitle: fmt.Sprintf("购买无限换装卡%s", packageConf.Name),
		CliOrderDesc:  fmt.Sprintf("购买无限换装卡%s", packageConf.Name),
		CliNotifyUrl:  config.GetDynamicConfig().PayApiConf.PayNotifyUrl,
		CreateTime:    now.Format("2006-01-02 15:04:05"),
		BundleId:      packageConf.ProductId,
		ProductId:     packageConf.ProductId,
		DeductParam: &pb.DeductParam{
			ContractId:   contract.ContractId,
			PlanId:       pay_api.FormatPlanId(pb.PayChannel(contract.PayChannel)),
			ProductCode:  pay_api.FormatProductCode(pb.PayChannel(contract.PayChannel)),
			SingleAmount: pay_api.ConvertCent2Yuan(packageConf.Price),
		},
		BusinessScenceCode: pay_api.BusinessScenceCode,
	}
	log.DebugWithCtx(ctx, "PlaceAutoPayOrder autoPayReq: %+v", autoPayReq)

	// 事务做各种更新
	err = m.store.Transaction(ctx, func(ctx context.Context, tx mysql.Txx) error {
		var terr error

		// db添加订单
		_, terr = m.store.AddOrder(ctx, order, tx)
		if terr != nil && !mysql.IsDupEntryError(terr) { // 忽略重复错误
			log.ErrorWithCtx(ctx, "PlaceAutoPayOrder AddOrder err: %v", terr)
			return terr
		}

		// 更新签约信息
		_, terr = m.store.UpdateContractPackageInfo(ctx, contract, tx)
		if terr != nil {
			log.ErrorWithCtx(ctx, "PlaceAutoPayOrder UpdateContractPackageInfo err: %v", terr)
			return terr
		}

		// 发起支付请求
		subCtx := pay_api.GenPayCtx(ctx, contract.Uid, uint32(contract.MarketId))
		_, terr = m.rpcCli.PayCli.AutoPay(subCtx, autoPayReq)
		if terr != nil {
			log.ErrorWithCtx(ctx, "PlaceAutoPayOrder AutoPay err: %v", terr)
			return terr
		}

		return nil
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "PlaceAutoPayOrder Transaction err: %v", err)
		return err
	}

	return nil
}

func (m *Mgr) PayCallback(ctx context.Context, in *pb.PayCallbackReq) error {
	log.InfoWithCtx(ctx, "PayCallback in: %+v", in)
	orderId := in.GetCliOrderNo()
	payTime := time.Unix(in.GetPayTs(), 0)

	// 查询订单状态
	order, err := m.store.GetOrder(ctx, orderId, nil)
	if err != nil {
		log.ErrorWithCtx(ctx, "PayCallback GetOrder err: %v", err)
		return err
	}
	if order == nil {
		log.ErrorWithCtx(ctx, "PayCallback order not found, orderId: %s", orderId)
		return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "订单不存在")
	}
	if order.Status == store.OrderStatusPaid {
		log.InfoWithCtx(ctx, "PayCallback order already handle, order: %+v", *order)
		return nil
	}

	// 查询用户背包
	remain, err := m.store.GetRemain(ctx, in.GetActiveUid(), nil)
	if err != nil {
		log.ErrorWithCtx(ctx, "PayCallback GetRemain err: %v", err)
		return err
	}

	// 计算变更前后的有效期
	oldEffectTime, oldExpireTime, newEffectTime, newExpireTime := calcCardPeriodChange(remain, payTime, order.Package.Days)
	// 计算本单的核销起止时间
	redemptionEndTime := newExpireTime
	redemptionBeginTime := redemptionEndTime.Add(-time.Hour * 24 * time.Duration(order.Package.Days))

	// 如果是支付宝关联系统订单，更新下次扣款时间
	var nextPayTime time.Time
	if order.OrderRole == store.OrderRoleSystem && order.PayChannel == uint8(pb.PayChannel_PAY_CHANNEL_ALIPAY) && len(order.ContractId) > 0 {
		nextPayTime = config.GetAlipayNextPayTime(newExpireTime)
	}

	// 事务做各种更新
	err = m.store.Transaction(ctx, func(ctx context.Context, tx mysql.Txx) error {
		var ok bool
		var terr error

		// 更新订单信息
		ok, terr = m.store.UpdateOrder(ctx, &store.Order{
			OrderId:        orderId,
			CoinOrderNo:    in.GetOrderNo(),
			ChannelOrderNo: in.GetOtherOrderNo(),
			PayTime:        payTime,
			PayPriceCent:   in.GetPayPriceCent(),
			CoinPayChannel: in.GetCoinPayChannel(),
			ActiveUid:      in.GetActiveUid(),
			Status:         store.OrderStatusPaid,
		}, order.Status, tx)
		if terr != nil {
			log.ErrorWithCtx(ctx, "PayCallback UpdatePaidOrder err: %v", terr)
			return terr
		}
		if !ok {
			return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "订单信息已更新")
		}

		// 添加or更新背包
		newRemain := &store.Remain{
			Uid:        in.GetActiveUid(),
			EffectTime: newEffectTime,
			ExpireTime: newExpireTime,
		}
		if remain == nil {
			_, terr = m.store.AddRemain(ctx, newRemain, tx)
			if terr != nil {
				log.ErrorWithCtx(ctx, "PayCallback AddRemain err: %v", terr)
				return terr
			}
		} else {
			ok, terr = m.store.UpdateRemain(ctx, remain, newRemain, tx)
			if terr != nil {
				log.ErrorWithCtx(ctx, "PayCallback UpdateRemain err: %v", terr)
				return terr
			}
			if !ok {
				return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "背包信息已更新")
			}
		}

		// 设置首购优惠标记
		if order.Package.DiscountPrice != 0 && in.PayPriceCent == order.Package.DiscountPrice {
			ok, terr = m.store.UpdateRemainDiscountOrderId(ctx, in.GetActiveUid(), orderId, tx)
			if terr != nil {
				log.ErrorWithCtx(ctx, "PayCallback UpdateRemainDiscountOrderId err: %v", terr)
				return terr
			}
			if !ok {
				return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "设置首购优惠标记失败")
			}
		}

		// 添加背包变更记录
		_, terr = m.store.AddRemainHistory(ctx, &store.RemainHistory{
			Uid:             in.GetActiveUid(),
			OldEffectTime:   oldEffectTime,
			OldExpireTime:   oldExpireTime,
			NewEffectTime:   newEffectTime,
			NewExpireTime:   newExpireTime,
			OrderId:         orderId,
			OrderInvokeTime: payTime,
			IsRevoke:        false,
			Reason:          "下单",
		}, tx)
		if terr != nil {
			log.ErrorWithCtx(ctx, "PayCallback AddRemainHistory err: %v", terr)
			return terr
		}

		// 相应地更新下次扣款时间
		if !nextPayTime.IsZero() {
			_, terr = m.store.UpdateContractNextPayTime(ctx, order.ContractId, nextPayTime, tx)
			if terr != nil {
				log.ErrorWithCtx(ctx, "PayCallback UpdateContractNextPayTime err: %v", terr)
				return terr
			}
		}

		// 添加订单核销记录，这里只是简单append，后续可能需要优化为调整已有记录
		terr = m.store.AddOrderRedemption(ctx, &store.OrderRedemption{
			Uid:         in.GetActiveUid(),
			OrderId:     orderId,
			Days:        order.Package.Days,
			PackageType: order.Package.PackageType,
			BeginTime:   redemptionBeginTime,
			EndTime:     redemptionEndTime,
			Status:      store.OrderRedemptionStatusNormal,
		}, tx)
		if terr != nil {
			log.ErrorWithCtx(ctx, "PayCallback AddOrderRedemption err: %v", terr)
			return terr
		}

		return nil
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "PayCallback Transaction err: %v", err)
		return err
	}

	// 各种变更通知
	goroutineex.GoroutineWithTimeoutCtx(ctx, time.Second*10, func(ctx context.Context) {
		_ = m.cache.DelUserCard(ctx, in.GetActiveUid())                      // 删除用户缓存
		_ = m.cache.DelUserRedemption(ctx, in.GetActiveUid())                // 删除用户核销缓存
		m.pushCardStatusChangeNotify(ctx, in.GetActiveUid(), newExpireTime)  // 给客户端推送有效期变更
		m.processPush4PayCallback(ctx, orderId)                              // 大部分推送逻辑
		_ = m.cache.SetUserExpireTime(ctx, in.GetActiveUid(), newExpireTime) // 设置用户到期队列
	})

	return nil
}

func (m *Mgr) NotifyContract(ctx context.Context, in *pb.NotifyContractReq) error {
	log.InfoWithCtx(ctx, "NotifyContract in: %+v", in)

	// 查询签约信息
	oldContract, err := m.store.GetContract(ctx, in.ContractId, nil)
	if err != nil {
		log.ErrorWithCtx(ctx, "NotifyContract GetContract err: %v", err)
		return err
	}
	if oldContract == nil {
		log.ErrorWithCtx(ctx, "NotifyContract contract not found, contractId: %s", in.ContractId)
		return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "签约信息不存在")
	}
	log.DebugWithCtx(ctx, "NotifyContract old contract: %+v", *oldContract)

	// 构造新的签约信息
	newContract := &store.Contract{
		ContractId:  oldContract.ContractId,
		Uid:         oldContract.Uid,
		NextPayTime: oldContract.NextPayTime,
		Status:      oldContract.Status,
		PackageId:   oldContract.PackageId,
		Package:     oldContract.Package,
	}
	if in.IsSign {
		newContract.Status = store.ContractStatusSigned
	} else {
		newContract.Status = store.ContractStatusCanceled
	}

	// 苹果的话，套餐可能变了，要更新一下
	if len(in.ProductId) > 0 && in.ProductId != oldContract.Package.ProductId {
		log.WarnWithCtx(ctx, "NotifyContract productId changed, contractId: %s, old: %s, new: %s", oldContract.ContractId, oldContract.Package.ProductId, in.ProductId)
		newConf := m.localCache.GetProductConf(in.ProductId)
		if newConf == nil {
			log.ErrorWithCtx(ctx, "NotifyContract productId not found, productId: %s", in.ProductId)
			return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "套餐不存在")
		}
		newContract.PackageId = newConf.Id
		newContract.Package = *newConf
	}

	// 苹果的扣费时间由苹果决定
	if in.NextPayTs > 0 && oldContract.PayChannel == uint8(pb.PayChannel_PAY_CHANNEL_APPSTORE) {
		newContract.NextPayTime = time.Unix(in.NextPayTs, 0)
	}

	// 还是苹果，签约uid可能和请求的不一样，坑爹
	if in.ActiveUid > 0 && in.ActiveUid != oldContract.Uid {
		log.WarnWithCtx(ctx, "NotifyContract uid changed, contractId: %s, old: %d, new: %d", oldContract.ContractId, oldContract.Uid, in.ActiveUid)
		newContract.Uid = in.ActiveUid
	}

	// 事务做各种更新
	err = m.store.Transaction(ctx, func(ctx context.Context, tx mysql.Txx) error {
		var ok bool
		var terr error

		// 更新签约信息
		newContract.EncodePackageInfo()
		ok, terr = m.store.UpdateContract(ctx, newContract, oldContract.Status, tx)
		if terr != nil {
			log.ErrorWithCtx(ctx, "NotifyContract UpdateContract err: %v", terr)
			return terr
		}
		if !ok {
			return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "签约信息已更新")
		}

		// 添加签约变更历史
		if newContract.Status != oldContract.Status {
			_, terr = m.store.AddContractHistory(ctx, &store.ContractHistory{
				ContractId:    newContract.ContractId,
				Uid:           newContract.Uid,
				PayChannel:    oldContract.PayChannel,
				PackageId:     newContract.PackageId,
				PackageInfo:   newContract.PackageInfo,
				Status:        newContract.Status,
				OperationRole: store.ContractHistoryOperationRoleUser,
				Reason:        "",
				CreateTime:    time.Now(),
			}, tx)
			if terr != nil {
				log.ErrorWithCtx(ctx, "NotifyContract AddContractHistory err: %v", terr)
				return terr
			}
		}

		return nil
	})

	// 各种变更通知
	goroutineex.GoroutineWithTimeoutCtx(ctx, time.Second*10, func(ctx context.Context) {
		_ = m.cache.DelUserCard(ctx, in.GetActiveUid()) // 删除用户缓存
		isNewSign := oldContract.Status == store.ContractStatusInit && newContract.Status == store.ContractStatusSigned
		m.processPush4NotifyContract(ctx, in.ContractId, isNewSign) // 大部分推送逻辑
	})

	return nil
}

// calcCardPeriodChange 计算背包变更前后的有效期
func calcCardPeriodChange(remain *store.Remain, payTime time.Time, days uint32) (time.Time, time.Time, time.Time, time.Time) {
	oldEffectTime, oldExpireTime := time.Unix(0, 0), time.Unix(0, 0)
	if remain != nil {
		oldEffectTime = remain.EffectTime
		oldExpireTime = remain.ExpireTime
	}
	newEffectTime := time.Date(payTime.Year(), payTime.Month(), payTime.Day(), 0, 0, 0, 0, time.Local) // 规整从0点开始，后续计算可简化
	newExpireTime := newEffectTime.Add(time.Duration(days) * time.Hour * 24)
	// 说明当前还没过期
	if oldExpireTime.After(payTime) {
		// 这个0点规整，正常情况下是没啥作用的。主要是规整云测有人xjb改db
		newEffectTime = time.Date(oldEffectTime.Year(), oldEffectTime.Month(), oldEffectTime.Day(), 0, 0, 0, 0, time.Local)
		newExpireTime = time.Date(oldExpireTime.Year(), oldExpireTime.Month(), oldExpireTime.Day(), 0, 0, 0, 0, time.Local).Add(time.Duration(days) * time.Hour * 24)
	}
	return oldEffectTime, oldExpireTime, newEffectTime, newExpireTime
}

// RevokeOrder 订单退款
func (m *Mgr) RevokeOrder(ctx context.Context, in *pb.RevokeOrderReq) error {
	orderId := in.GetOrderId()
	if orderId == "" || in.GetUid() == 0 {
		return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	revokeTime, err := time.ParseInLocation("2006-01-02 15:04:05", in.GetNotifyTime(), time.Local)
	if err != nil {
		log.ErrorWithCtx(ctx, "RevokeOrder fail to ParseInLocation. in:%+v, err:%v", in, err)
		return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "时间格式错误")
	}

	// 查询订单状态
	order, err := m.store.GetOrder(ctx, orderId, nil)
	if err != nil {
		log.ErrorWithCtx(ctx, "RevokeOrder fail to GetOrder. in:%+v, err:%v", in, err)
		return err
	}
	if order == nil {
		log.ErrorWithCtx(ctx, "RevokeOrder order not found. in:%+v", in)
		return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "订单不存在")
	}
	if order.Status != store.OrderStatusPaid {
		log.ErrorWithCtx(ctx, "RevokeOrder order status err. in:%+v", in)
		return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "该订单未完成支付，无法退款")
	}

	// 查询用户背包
    uid := order.ActiveUid
	remain, err := m.store.GetRemain(ctx, uid, nil)
	if err != nil {
		log.ErrorWithCtx(ctx, "RevokeOrder fail to GetRemain. in:%+v, err:%v", in, err)
		return err
	}

	now := time.Now()
    var newExpireTime time.Time
    revokeOrderId := fmt.Sprintf("%s_revoke", orderId)

	// 事务操作
	err = m.store.Transaction(ctx, func(ctx context.Context, tx mysql.Txx) error {
		// 更新订单状态
		order.Status = store.OrderStatusRevoke
		ok, err := m.store.UpdateOrder(ctx, order, store.OrderStatusPaid, tx)
		if err != nil {
			log.ErrorWithCtx(ctx, "RevokeOrder fail to UpdateOrder. in:%+v, err:%v", in, err)
			return err
		}
		if !ok {
			return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "订单信息已更新")
		}

		// 获取核销订单列表
		list, err := m.store.GetUserOrderRedemptionList(ctx, uid, now, tx)
		if err != nil {
			log.ErrorWithCtx(ctx, "RevokeOrder fail to GetUserOrderRedemptionList. in:%+v, err:%v", in, err)
			return err
		}

		// 生成待更新核销订单列表
		revokeRedemption, redemptionUpdateList, err := genRevokeRedemptionUpdateList(orderId, list, now)
		if err != nil {
			log.ErrorWithCtx(ctx, "RevokeOrder fail to genRevokeRedemptionUpdateList. in:%+v, err:%v", in, err)
			return err
		}

		// 更新核销订单列表
		err = m.store.BatchUpdateUserOrderRedemptionList(ctx, redemptionUpdateList, tx)
		if err != nil {
			log.ErrorWithCtx(ctx, "RevokeOrder fail to BatchUpdateUserOrderRedemptionList. in:%+v, err:%v", in, err)
			return err
		}

		// 需回收用户的权益剩余时长
        var reclaimDays uint32
		if remain != nil {
			useDays := uint32(revokeRedemption.EndTime.Sub(revokeRedemption.BeginTime).Hours() / 24)
			if revokeRedemption.Days < useDays {
				return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "订单已用天数大于剩余天数，无法回收权益")
			}

			reclaimDays = revokeRedemption.Days - useDays
			newExpireTime = remain.ExpireTime.Add(-time.Duration(reclaimDays) * 24 * time.Hour)
			newRemain := &store.Remain{
				Uid:        uid,
				EffectTime: remain.EffectTime,
				ExpireTime: newExpireTime,
			}

			// 更新背包
			ok, err = m.store.UpdateRemain(ctx, remain, newRemain, tx)
			if err != nil {
				log.ErrorWithCtx(ctx, "RevokeOrder fail to UpdateRemain. in:%+v, err:%v", in, err)
				return err
			}
			if !ok {
				return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "背包信息更新")
			}

            _, err = m.store.AddRemainHistory(ctx, &store.RemainHistory{
                Uid:             uid,
                OldEffectTime:   remain.EffectTime,
                OldExpireTime:   remain.ExpireTime,
                NewEffectTime:   remain.EffectTime,
                NewExpireTime:   newExpireTime,
                OrderId:         orderId,
                OrderInvokeTime: order.PayTime,
                IsRevoke:        true,
                Reason:          "退款",
            }, tx)
            if err != nil {
                log.ErrorWithCtx(ctx, "RevokeOrder fail to AddRemainHistory. in:%+v, err:%v", in, err)
                return err
            }
		}

        revokeFlow := &store.OrderRevokeFlow{
            OrderId:       orderId,
            Uid:           order.Uid,
            PackageId:     order.PackageId,
            PayChannel:    order.PayChannel,
            PriceCent:     order.PriceCent,
            RevokeDays:    reclaimDays,
            RevokeOrderId: revokeOrderId,
            ServerTime:    order.PayTime,
            CreateTime:    now,
            UpdateTime:    now,
        }

        // 插入撤销订单
        if err = m.store.AddRevokeFlow(ctx, revokeFlow, tx); err != nil {
            log.ErrorWithCtx(ctx, "RevokeOrder AddRevokeFlow in:%+v, err: %v", in, err)
            return err
        }

		return nil
	})
    if err != nil {
        log.ErrorWithCtx(ctx, "RevokeOrder Transaction in:%+v, err: %v", in, err)
        return err
    }

    // 各种变更通知
    goroutineex.GoroutineWithTimeoutCtx(ctx, time.Second*10, func(ctx context.Context) {
        _ = m.cache.DelUserCard(ctx, uid)                      // 删除用户缓存
		_ = m.cache.DelUserRedemption(ctx, uid)                // 删除用户核销缓存
        m.pushCardStatusChangeNotify(ctx, uid, newExpireTime)  // 给客户端推送有效期变更
        _ = m.cache.SetUserExpireTime(ctx, uid, newExpireTime) // 设置用户到期队列

        // 发送TT助手消息通知
        content := fmt.Sprintf("您在%s购买的无限换装卡【%s】已退款成功，已扣除相应有效期。",
            order.PayTime.Format("01月02日 15:04:05"), order.Package.Name)
        m.sendTTAssistantText(ctx, uid, content, "", "")
    })

	log.InfoWithCtx(ctx, "RevokeOrder done. in:%+v, %v", in, revokeTime)
	return nil
}

func genRevokeRedemptionUpdateList(orderId string, list []*store.OrderRedemption, now time.Time) (*store.OrderRedemption, []*store.OrderRedemption, error) {
	today := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local)
	updateList := make([]*store.OrderRedemption, 0)
	var revokeRedemption *store.OrderRedemption
	for _, v := range list {
		if v.OrderId == orderId {
			revokeRedemption = v
			break
		}
	}

	if revokeRedemption == nil || revokeRedemption.EndTime.Before(today) {
		// 该笔订单已核销完，无法处理退款
		return revokeRedemption, updateList, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "该笔订单已核销完，无法处理退款")
	}

	if revokeRedemption.BeginTime.Before(today) {
		revokeRedemption.EndTime = today
	} else {
		revokeRedemption.EndTime = revokeRedemption.BeginTime
	}
	revokeRedemption.Status = store.OrderRedemptionStatusRefund
	updateList = append(updateList, revokeRedemption)

	// 重新计算后续订单的核销起始时间
	lastOrderEndTime := revokeRedemption.EndTime
	for _, v := range list {
		if v.OrderId == orderId || v.EndTime.Before(lastOrderEndTime) {
			// 之前的订单不处理
			continue
		}

		v.BeginTime = lastOrderEndTime
		v.EndTime = v.BeginTime.Add(time.Duration(v.Days) * 24 * time.Hour)
		lastOrderEndTime = v.EndTime

		updateList = append(updateList, v)
	}

	return revokeRedemption, updateList, nil
}
