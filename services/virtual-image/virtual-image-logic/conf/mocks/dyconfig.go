// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/virtual-image/virtual-image-logic/conf (interfaces: ISDyConfigHandler)

// Package mocks is a generated GoMock package.
package mocks

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	conf "golang.52tt.com/services/virtual-image/virtual-image-logic/conf"
)

// MockISDyConfigHandler is a mock of ISDyConfigHandler interface.
type MockISDyConfigHandler struct {
	ctrl     *gomock.Controller
	recorder *MockISDyConfigHandlerMockRecorder
}

// MockISDyConfigHandlerMockRecorder is the mock recorder for MockISDyConfigHandler.
type MockISDyConfigHandlerMockRecorder struct {
	mock *MockISDyConfigHandler
}

// NewMockISDyConfigHandler creates a new mock instance.
func NewMockISDyConfigHandler(ctrl *gomock.Controller) *MockISDyConfigHandler {
	mock := &MockISDyConfigHandler{ctrl: ctrl}
	mock.recorder = &MockISDyConfigHandlerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockISDyConfigHandler) EXPECT() *MockISDyConfigHandlerMockRecorder {
	return m.recorder
}

// GetAllTabCfg mocks base method.
func (m *MockISDyConfigHandler) GetAllTabCfg() *conf.TabCfg {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllTabCfg")
	ret0, _ := ret[0].(*conf.TabCfg)
	return ret0
}

// GetAllTabCfg indicates an expected call of GetAllTabCfg.
func (mr *MockISDyConfigHandlerMockRecorder) GetAllTabCfg() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllTabCfg", reflect.TypeOf((*MockISDyConfigHandler)(nil).GetAllTabCfg))
}

// GetAutoPlayDurationSec mocks base method.
func (m *MockISDyConfigHandler) GetAutoPlayDurationSec() uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAutoPlayDurationSec")
	ret0, _ := ret[0].(uint32)
	return ret0
}

// GetAutoPlayDurationSec indicates an expected call of GetAutoPlayDurationSec.
func (mr *MockISDyConfigHandlerMockRecorder) GetAutoPlayDurationSec() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAutoPlayDurationSec", reflect.TypeOf((*MockISDyConfigHandler)(nil).GetAutoPlayDurationSec))
}

// GetBeginnerGuidanceSourceUrl mocks base method.
func (m *MockISDyConfigHandler) GetBeginnerGuidanceSourceUrl() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBeginnerGuidanceSourceUrl")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetBeginnerGuidanceSourceUrl indicates an expected call of GetBeginnerGuidanceSourceUrl.
func (mr *MockISDyConfigHandlerMockRecorder) GetBeginnerGuidanceSourceUrl() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBeginnerGuidanceSourceUrl", reflect.TypeOf((*MockISDyConfigHandler)(nil).GetBeginnerGuidanceSourceUrl))
}

// GetBeginnerGuidanceSourceUrlMd5 mocks base method.
func (m *MockISDyConfigHandler) GetBeginnerGuidanceSourceUrlMd5() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBeginnerGuidanceSourceUrlMd5")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetBeginnerGuidanceSourceUrlMd5 indicates an expected call of GetBeginnerGuidanceSourceUrlMd5.
func (mr *MockISDyConfigHandlerMockRecorder) GetBeginnerGuidanceSourceUrlMd5() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBeginnerGuidanceSourceUrlMd5", reflect.TypeOf((*MockISDyConfigHandler)(nil).GetBeginnerGuidanceSourceUrlMd5))
}

// GetBeginnerGuideSourceWaitTime mocks base method.
func (m *MockISDyConfigHandler) GetBeginnerGuideSourceWaitTime() uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBeginnerGuideSourceWaitTime")
	ret0, _ := ret[0].(uint32)
	return ret0
}

// GetBeginnerGuideSourceWaitTime indicates an expected call of GetBeginnerGuideSourceWaitTime.
func (mr *MockISDyConfigHandlerMockRecorder) GetBeginnerGuideSourceWaitTime() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBeginnerGuideSourceWaitTime", reflect.TypeOf((*MockISDyConfigHandler)(nil).GetBeginnerGuideSourceWaitTime))
}

// GetCheckNewUserUrl mocks base method.
func (m *MockISDyConfigHandler) GetCheckNewUserUrl() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCheckNewUserUrl")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetCheckNewUserUrl indicates an expected call of GetCheckNewUserUrl.
func (mr *MockISDyConfigHandlerMockRecorder) GetCheckNewUserUrl() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCheckNewUserUrl", reflect.TypeOf((*MockISDyConfigHandler)(nil).GetCheckNewUserUrl))
}

// GetDatingHatMap mocks base method.
func (m *MockISDyConfigHandler) GetDatingHatMap() map[uint32]uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDatingHatMap")
	ret0, _ := ret[0].(map[uint32]uint32)
	return ret0
}

// GetDatingHatMap indicates an expected call of GetDatingHatMap.
func (mr *MockISDyConfigHandlerMockRecorder) GetDatingHatMap() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDatingHatMap", reflect.TypeOf((*MockISDyConfigHandler)(nil).GetDatingHatMap))
}

// GetDefaultOutFit mocks base method.
func (m *MockISDyConfigHandler) GetDefaultOutFit() []uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDefaultOutFit")
	ret0, _ := ret[0].([]uint32)
	return ret0
}

// GetDefaultOutFit indicates an expected call of GetDefaultOutFit.
func (mr *MockISDyConfigHandlerMockRecorder) GetDefaultOutFit() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDefaultOutFit", reflect.TypeOf((*MockISDyConfigHandler)(nil).GetDefaultOutFit))
}

// GetFeishuNotice mocks base method.
func (m *MockISDyConfigHandler) GetFeishuNotice() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFeishuNotice")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetFeishuNotice indicates an expected call of GetFeishuNotice.
func (mr *MockISDyConfigHandlerMockRecorder) GetFeishuNotice() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFeishuNotice", reflect.TypeOf((*MockISDyConfigHandler)(nil).GetFeishuNotice))
}

// GetFemaleResourceList mocks base method.
func (m *MockISDyConfigHandler) GetFemaleResourceList() []uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFemaleResourceList")
	ret0, _ := ret[0].([]uint32)
	return ret0
}

// GetFemaleResourceList indicates an expected call of GetFemaleResourceList.
func (mr *MockISDyConfigHandlerMockRecorder) GetFemaleResourceList() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFemaleResourceList", reflect.TypeOf((*MockISDyConfigHandler)(nil).GetFemaleResourceList))
}

// GetGameEnable mocks base method.
func (m *MockISDyConfigHandler) GetGameEnable(arg0 uint32) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGameEnable", arg0)
	ret0, _ := ret[0].(bool)
	return ret0
}

// GetGameEnable indicates an expected call of GetGameEnable.
func (mr *MockISDyConfigHandlerMockRecorder) GetGameEnable(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGameEnable", reflect.TypeOf((*MockISDyConfigHandler)(nil).GetGameEnable), arg0)
}

// GetHideRecommendTab mocks base method.
func (m *MockISDyConfigHandler) GetHideRecommendTab() bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHideRecommendTab")
	ret0, _ := ret[0].(bool)
	return ret0
}

// GetHideRecommendTab indicates an expected call of GetHideRecommendTab.
func (mr *MockISDyConfigHandlerMockRecorder) GetHideRecommendTab() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHideRecommendTab", reflect.TypeOf((*MockISDyConfigHandler)(nil).GetHideRecommendTab))
}

// GetMaleAnimationMap mocks base method.
func (m *MockISDyConfigHandler) GetMaleAnimationMap() map[uint32]uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMaleAnimationMap")
	ret0, _ := ret[0].(map[uint32]uint32)
	return ret0
}

// GetMaleAnimationMap indicates an expected call of GetMaleAnimationMap.
func (mr *MockISDyConfigHandlerMockRecorder) GetMaleAnimationMap() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMaleAnimationMap", reflect.TypeOf((*MockISDyConfigHandler)(nil).GetMaleAnimationMap))
}

// GetMaleResourceList mocks base method.
func (m *MockISDyConfigHandler) GetMaleResourceList() []uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMaleResourceList")
	ret0, _ := ret[0].([]uint32)
	return ret0
}

// GetMaleResourceList indicates an expected call of GetMaleResourceList.
func (mr *MockISDyConfigHandlerMockRecorder) GetMaleResourceList() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMaleResourceList", reflect.TypeOf((*MockISDyConfigHandler)(nil).GetMaleResourceList))
}

// GetMapMutualExclusion mocks base method.
func (m *MockISDyConfigHandler) GetMapMutualExclusion() map[uint32][]uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMapMutualExclusion")
	ret0, _ := ret[0].(map[uint32][]uint32)
	return ret0
}

// GetMapMutualExclusion indicates an expected call of GetMapMutualExclusion.
func (mr *MockISDyConfigHandlerMockRecorder) GetMapMutualExclusion() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMapMutualExclusion", reflect.TypeOf((*MockISDyConfigHandler)(nil).GetMapMutualExclusion))
}

// GetMyDisplayExpireRemind mocks base method.
func (m *MockISDyConfigHandler) GetMyDisplayExpireRemind() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMyDisplayExpireRemind")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetMyDisplayExpireRemind indicates an expected call of GetMyDisplayExpireRemind.
func (mr *MockISDyConfigHandlerMockRecorder) GetMyDisplayExpireRemind() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMyDisplayExpireRemind", reflect.TypeOf((*MockISDyConfigHandler)(nil).GetMyDisplayExpireRemind))
}

// GetResourceCategoryReloadCron mocks base method.
func (m *MockISDyConfigHandler) GetResourceCategoryReloadCron() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetResourceCategoryReloadCron")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetResourceCategoryReloadCron indicates an expected call of GetResourceCategoryReloadCron.
func (mr *MockISDyConfigHandlerMockRecorder) GetResourceCategoryReloadCron() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetResourceCategoryReloadCron", reflect.TypeOf((*MockISDyConfigHandler)(nil).GetResourceCategoryReloadCron))
}

// GetSitJumpUrl mocks base method.
func (m *MockISDyConfigHandler) GetSitJumpUrl() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSitJumpUrl")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetSitJumpUrl indicates an expected call of GetSitJumpUrl.
func (mr *MockISDyConfigHandlerMockRecorder) GetSitJumpUrl() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSitJumpUrl", reflect.TypeOf((*MockISDyConfigHandler)(nil).GetSitJumpUrl))
}

// GetSitSubCategory mocks base method.
func (m *MockISDyConfigHandler) GetSitSubCategory() uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSitSubCategory")
	ret0, _ := ret[0].(uint32)
	return ret0
}

// GetSitSubCategory indicates an expected call of GetSitSubCategory.
func (mr *MockISDyConfigHandlerMockRecorder) GetSitSubCategory() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSitSubCategory", reflect.TypeOf((*MockISDyConfigHandler)(nil).GetSitSubCategory))
}

// GetSuitTabCfg mocks base method.
func (m *MockISDyConfigHandler) GetSuitTabCfg() *conf.TabCfg {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSuitTabCfg")
	ret0, _ := ret[0].(*conf.TabCfg)
	return ret0
}

// GetSuitTabCfg indicates an expected call of GetSuitTabCfg.
func (mr *MockISDyConfigHandlerMockRecorder) GetSuitTabCfg() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSuitTabCfg", reflect.TypeOf((*MockISDyConfigHandler)(nil).GetSuitTabCfg))
}

// GetTargetDisplayExpireRemind mocks base method.
func (m *MockISDyConfigHandler) GetTargetDisplayExpireRemind() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTargetDisplayExpireRemind")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetTargetDisplayExpireRemind indicates an expected call of GetTargetDisplayExpireRemind.
func (mr *MockISDyConfigHandlerMockRecorder) GetTargetDisplayExpireRemind() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTargetDisplayExpireRemind", reflect.TypeOf((*MockISDyConfigHandler)(nil).GetTargetDisplayExpireRemind))
}

// GetWeddingAllowChangeSubCategoryList mocks base method.
func (m *MockISDyConfigHandler) GetWeddingAllowChangeSubCategoryList() []uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWeddingAllowChangeSubCategoryList")
	ret0, _ := ret[0].([]uint32)
	return ret0
}

// GetWeddingAllowChangeSubCategoryList indicates an expected call of GetWeddingAllowChangeSubCategoryList.
func (mr *MockISDyConfigHandlerMockRecorder) GetWeddingAllowChangeSubCategoryList() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWeddingAllowChangeSubCategoryList", reflect.TypeOf((*MockISDyConfigHandler)(nil).GetWeddingAllowChangeSubCategoryList))
}

// Start mocks base method.
func (m *MockISDyConfigHandler) Start() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Start")
	ret0, _ := ret[0].(error)
	return ret0
}

// Start indicates an expected call of Start.
func (mr *MockISDyConfigHandlerMockRecorder) Start() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Start", reflect.TypeOf((*MockISDyConfigHandler)(nil).Start))
}
