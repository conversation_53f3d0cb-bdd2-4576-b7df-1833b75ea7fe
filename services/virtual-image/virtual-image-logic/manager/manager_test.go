package manager

import (
    "context"
    "fmt"
    "github.com/golang/mock/gomock"
    pb "golang.52tt.com/protocol/app/virtual_image_logic"
    virtual_image_mall "golang.52tt.com/protocol/services/virtual-image-mall"
    virtual_image_resource "golang.52tt.com/protocol/services/virtual-image-resource"
    "testing"
)

func TestVirtualImageLogicMgr_GetResourceList(t *testing.T) {
    initTestMgr(t)
    type args struct {
        ctx context.Context
    }
    tests := []struct {
        name     string
        initFunc func()
        args     args
        wantErr  bool
    }{
        {
            name: "success",
            args: args{
                ctx: ctx,
            },
            initFunc: func() {
                resourceCli.EXPECT().GetClientListByPage(gomock.Any(), gomock.Any()).Return(&virtual_image_resource.GetClientListByPageResponse{
                    Resources: []*virtual_image_resource.VirtualImageResourceInfo{
                        {Id: 1, SkinMap: map[string]*virtual_image_resource.SkinInfo{"test": { Url: "test skin"}}},
                    },
                }, nil)
            },
            wantErr: false,
        },
        {
            name: "error - GetClientListByPage fails",
            args: args{
                ctx: ctx,
            },
            initFunc: func() {
                resourceCli.EXPECT().GetClientListByPage(gomock.Any(), gomock.Any()).Return(nil, fmt.Errorf("internal error"))
            },
            wantErr: true,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := testMgr
            if tt.initFunc != nil {
                tt.initFunc()
            }
            _, err := m.GetResourceList(tt.args.ctx, &pb.GetResourceListRequest{Limit: 10})
            if (err != nil) != tt.wantErr {
                t.Errorf("GetClientListByPage() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
        })
    }
}

func TestVirtualImageLogicMgr_RefreshVirtualImageRedDotAlert(t *testing.T) {
    initTestMgr(t)
    type args struct {
        ctx context.Context
    }
    tests := []struct {
        name     string
        initFunc func()
        args     args
        wantErr  bool
    }{
        {
            name: "success",
            args: args{
                ctx: ctx,
            },
            initFunc: func() {
                mallCli.EXPECT().GetUnRefreshedRedDotPackageData(gomock.Any(), gomock.Any()).Return(&virtual_image_mall.GetUnRefreshedRedDotPackageDataResp{
                    PackageList: []*virtual_image_mall.CommodityDataPackage{
                        {PackageId: 1, CommodityId: 1},{PackageId: 2, CommodityId: 2},
                    },
                }, nil)
                mallCli.EXPECT().GetCommodityDataList(gomock.Any(), gomock.Any()).Return(&virtual_image_mall.GetCommodityDataListResponse{
                    CommodityDataList: []*virtual_image_mall.CommodityData{
                        {CommodityId: 1, PricePackageList: []*virtual_image_mall.CommodityDataPackage{
                            {PackageId: 1, Price: 100},
                        }},
                    },

                }, nil)
                mallCli.EXPECT().UpdateCommodityPackageRedDot(gomock.Any(), gomock.Any()).Return(nil, nil)
            },
            wantErr: false,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := testMgr
            if tt.initFunc != nil {
                tt.initFunc()
            }
            m.RefreshVirtualImageRedDotAlert(tt.args.ctx)
        })
    }
}

func TestVirtualImageLogicMgr_GetVirtualImageCommodityRedDot(t *testing.T) {
    initTestMgr(t)
    type args struct {
        ctx context.Context
        uid uint32
    }
    tests := []struct {
        name     string
        initFunc func()
        args     args
        wantErr  bool
    }{
        {
            name: "success",
            args: args{
                ctx: ctx,
                uid: 1,
            },
            initFunc: func() {
                mallCli.EXPECT().BatchGetCommodityRedDot(gomock.Any(), gomock.Any()).Return(nil, nil)
            },
            wantErr: false,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := testMgr
            if tt.initFunc != nil {
                tt.initFunc()
            }
            _, err := m.GetVirtualImageCommodityRedDot(tt.args.ctx, &pb.GetVirtualImageCommodityRedDotRequest{})
            if (err != nil) != tt.wantErr {
                t.Errorf("GetVirtualImageCommodityRedDot() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
        })
    }
}

func TestVirtualImageLogicMgr_GetVirtualImageResourceCategory(t *testing.T) {
    initTestMgr(t)
    type args struct {
        ctx context.Context
    }
    tests := []struct {
        name     string
        initFunc func()
        args     args
        wantErr  bool
    }{
        {
            name: "success",
            args: args{
                ctx: ctx,
            },
            initFunc: func() {
                resourceCli.EXPECT().GetVirtualImageResourceCategory(gomock.Any(), gomock.Any()).Return(&virtual_image_resource.GetVirtualImageResourceCategoryResponse{
                    ResourceCategoryInfoList: []*virtual_image_resource.VirtualImageResourceCategoryInfo{
                        {ParentCategoryInfo: &virtual_image_resource.VirtualImageParentCategoryInfo{Category:1}},
                        {
                            ParentCategoryInfo: &virtual_image_resource.VirtualImageParentCategoryInfo{Category:2},
                            SubCategoryInfoList: make([]*virtual_image_resource.VirtualImageSubCategoryInfo, 2),
                        },
                    },
                }, nil)
            },
            wantErr: false,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := testMgr
            if tt.initFunc != nil {
                tt.initFunc()
            }
            _, err := m.GetVirtualImageResourceCategory(tt.args.ctx, &pb.GetVirtualImageResourceCategoryRequest{})
            if (err != nil) != tt.wantErr {
                t.Errorf("GetVirtualImageResourceCategory() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
        })
    }
}

func TestVirtualImageLogicMgr_GetDefaultResourceList(t *testing.T) {
    initTestMgr(t)
    type args struct {
        ctx context.Context
    }
    tests := []struct {
        name     string
        initFunc func()
        args     args
        wantErr  bool
    }{
        {
            name: "success",
            args: args{
                ctx: ctx,
            },
            initFunc: func() {
                resourceCli.EXPECT().GetDefaultResourceList(gomock.Any(), gomock.Any()).Return(&virtual_image_resource.GetDefaultResourceListResponse{}, nil)
            },
            wantErr: false,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := testMgr
            if tt.initFunc != nil {
                tt.initFunc()
            }
            _, err := m.GetDefaultResourceList(tt.args.ctx)
            if (err != nil) != tt.wantErr {
                t.Errorf("GetDefaultResourceList() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
        })
    }
}

func TestVirtualImageLogicMgr_GetRedDotAlertStatus(t *testing.T) {
    initTestMgr(t)
    type args struct {
        ctx context.Context
    }
    tests := []struct {
        name     string
        initFunc func()
        args     args
        wantErr  bool
    }{
        {
            name: "success",
            args: args{
                ctx: ctx,
            },
            initFunc: func() {
                mallCli.EXPECT().GetUserUnReadCommodityRedDot(gomock.Any(), gomock.Any()).Return(&virtual_image_mall.GetUserUnReadCommodityRedDotResp{
                    CategoryList: []*virtual_image_resource.VirtualImageResourceCategoryInfo{
                        {SubCategoryInfoList: []*virtual_image_resource.VirtualImageSubCategoryInfo{
                            {SubCategory: 1},
                        }},
                    },
                }, nil)
            },
            wantErr: false,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := testMgr
            if tt.initFunc != nil {
                tt.initFunc()
            }
            _, err := m.GetRedDotAlertStatus(tt.args.ctx, 6)
            if (err != nil) != tt.wantErr {
                t.Errorf("GetRedDotAlertStatus() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
        })
    }
}

func TestVirtualImageLogicMgr_GetLevelIconConfig(t *testing.T) {
    initTestMgr(t)
    type args struct {
        ctx context.Context
    }
    tests := []struct {
        name     string
        initFunc func()
        args     args
        wantErr  bool
    }{
        {
            name: "success",
            args: args{
                ctx: ctx,
            },
            initFunc: func() {
                resourceCli.EXPECT().GetLevelConfig(gomock.Any(), gomock.Any()).Return(&virtual_image_resource.GetLevelConfigResponse{
                    List: []*virtual_image_resource.LevelConfig{{Level: 1}},
                }, nil)
            },
            wantErr: false,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := testMgr
            if tt.initFunc != nil {
                tt.initFunc()
            }
            _ = m.GetLevelIconConfig(tt.args.ctx)
        })
    }
}