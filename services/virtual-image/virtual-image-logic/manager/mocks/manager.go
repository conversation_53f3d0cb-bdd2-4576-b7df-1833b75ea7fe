// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/virtual-image/virtual-image-logic/manager (interfaces: IVirtualImageLogicMgr)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	app "golang.52tt.com/protocol/app"
	virtual_image_logic "golang.52tt.com/protocol/app/virtual_image_logic"
	risk_mng_api "golang.52tt.com/protocol/services/risk-mng-api"
	virtual_image_mall "golang.52tt.com/protocol/services/virtual-image-mall"
	virtual_image_resource "golang.52tt.com/protocol/services/virtual-image-resource"
	virtual_image_user "golang.52tt.com/protocol/services/virtual-image-user"
)

// MockIVirtualImageLogicMgr is a mock of IVirtualImageLogicMgr interface.
type MockIVirtualImageLogicMgr struct {
	ctrl     *gomock.Controller
	recorder *MockIVirtualImageLogicMgrMockRecorder
}

// MockIVirtualImageLogicMgrMockRecorder is the mock recorder for MockIVirtualImageLogicMgr.
type MockIVirtualImageLogicMgrMockRecorder struct {
	mock *MockIVirtualImageLogicMgr
}

// NewMockIVirtualImageLogicMgr creates a new mock instance.
func NewMockIVirtualImageLogicMgr(ctrl *gomock.Controller) *MockIVirtualImageLogicMgr {
	mock := &MockIVirtualImageLogicMgr{ctrl: ctrl}
	mock.recorder = &MockIVirtualImageLogicMgrMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIVirtualImageLogicMgr) EXPECT() *MockIVirtualImageLogicMgrMockRecorder {
	return m.recorder
}

// AcceptBindInvite mocks base method.
func (m *MockIVirtualImageLogicMgr) AcceptBindInvite(arg0 context.Context, arg1 uint32, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AcceptBindInvite", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// AcceptBindInvite indicates an expected call of AcceptBindInvite.
func (mr *MockIVirtualImageLogicMgrMockRecorder) AcceptBindInvite(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AcceptBindInvite", reflect.TypeOf((*MockIVirtualImageLogicMgr)(nil).AcceptBindInvite), arg0, arg1, arg2)
}

// BatchGetUserVirtualImageInuse mocks base method.
func (m *MockIVirtualImageLogicMgr) BatchGetUserVirtualImageInuse(arg0 context.Context, arg1 uint32, arg2 *virtual_image_logic.BatchGetUserVirtualImageInuseRequest) ([]*virtual_image_logic.UserVirtualImageInuse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUserVirtualImageInuse", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*virtual_image_logic.UserVirtualImageInuse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetUserVirtualImageInuse indicates an expected call of BatchGetUserVirtualImageInuse.
func (mr *MockIVirtualImageLogicMgrMockRecorder) BatchGetUserVirtualImageInuse(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserVirtualImageInuse", reflect.TypeOf((*MockIVirtualImageLogicMgr)(nil).BatchGetUserVirtualImageInuse), arg0, arg1, arg2)
}

// BuyCommodityData mocks base method.
func (m *MockIVirtualImageLogicMgr) BuyCommodityData(arg0 context.Context, arg1 *virtual_image_logic.BuyCommodityDataRequest) (*virtual_image_logic.BuyCommodityDataResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BuyCommodityData", arg0, arg1)
	ret0, _ := ret[0].(*virtual_image_logic.BuyCommodityDataResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BuyCommodityData indicates an expected call of BuyCommodityData.
func (mr *MockIVirtualImageLogicMgrMockRecorder) BuyCommodityData(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BuyCommodityData", reflect.TypeOf((*MockIVirtualImageLogicMgr)(nil).BuyCommodityData), arg0, arg1)
}

// CancelBindInvite mocks base method.
func (m *MockIVirtualImageLogicMgr) CancelBindInvite(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CancelBindInvite", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// CancelBindInvite indicates an expected call of CancelBindInvite.
func (mr *MockIVirtualImageLogicMgrMockRecorder) CancelBindInvite(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CancelBindInvite", reflect.TypeOf((*MockIVirtualImageLogicMgr)(nil).CancelBindInvite), arg0, arg1, arg2)
}

// CheckChannelEntrance mocks base method.
func (m *MockIVirtualImageLogicMgr) CheckChannelEntrance(arg0 context.Context, arg1, arg2 uint32) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckChannelEntrance", arg0, arg1, arg2)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckChannelEntrance indicates an expected call of CheckChannelEntrance.
func (mr *MockIVirtualImageLogicMgrMockRecorder) CheckChannelEntrance(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckChannelEntrance", reflect.TypeOf((*MockIVirtualImageLogicMgr)(nil).CheckChannelEntrance), arg0, arg1, arg2)
}

// CheckFaceAuthInfo mocks base method.
func (m *MockIVirtualImageLogicMgr) CheckFaceAuthInfo(arg0 context.Context, arg1, arg2 uint32, arg3 *app.BaseReq) (*risk_mng_api.CheckResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckFaceAuthInfo", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*risk_mng_api.CheckResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckFaceAuthInfo indicates an expected call of CheckFaceAuthInfo.
func (mr *MockIVirtualImageLogicMgrMockRecorder) CheckFaceAuthInfo(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckFaceAuthInfo", reflect.TypeOf((*MockIVirtualImageLogicMgr)(nil).CheckFaceAuthInfo), arg0, arg1, arg2, arg3)
}

// CheckFrozenOrderList mocks base method.
func (m *MockIVirtualImageLogicMgr) CheckFrozenOrderList(arg0 context.Context) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "CheckFrozenOrderList", arg0)
}

// CheckFrozenOrderList indicates an expected call of CheckFrozenOrderList.
func (mr *MockIVirtualImageLogicMgrMockRecorder) CheckFrozenOrderList(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckFrozenOrderList", reflect.TypeOf((*MockIVirtualImageLogicMgr)(nil).CheckFrozenOrderList), arg0)
}

// CheckGameEnable mocks base method.
func (m *MockIVirtualImageLogicMgr) CheckGameEnable(arg0 context.Context, arg1 uint32) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckGameEnable", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckGameEnable indicates an expected call of CheckGameEnable.
func (mr *MockIVirtualImageLogicMgrMockRecorder) CheckGameEnable(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckGameEnable", reflect.TypeOf((*MockIVirtualImageLogicMgr)(nil).CheckGameEnable), arg0, arg1)
}

// ComputeCommodityPrice mocks base method.
func (m *MockIVirtualImageLogicMgr) ComputeCommodityPrice(arg0 context.Context, arg1 *virtual_image_logic.ComputeCommodityPriceRequest) (*virtual_image_logic.ComputeCommodityPriceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ComputeCommodityPrice", arg0, arg1)
	ret0, _ := ret[0].(*virtual_image_logic.ComputeCommodityPriceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ComputeCommodityPrice indicates an expected call of ComputeCommodityPrice.
func (mr *MockIVirtualImageLogicMgrMockRecorder) ComputeCommodityPrice(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ComputeCommodityPrice", reflect.TypeOf((*MockIVirtualImageLogicMgr)(nil).ComputeCommodityPrice), arg0, arg1)
}

// GetAutoDisplayDurationSec mocks base method.
func (m *MockIVirtualImageLogicMgr) GetAutoDisplayDurationSec() uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAutoDisplayDurationSec")
	ret0, _ := ret[0].(uint32)
	return ret0
}

// GetAutoDisplayDurationSec indicates an expected call of GetAutoDisplayDurationSec.
func (mr *MockIVirtualImageLogicMgrMockRecorder) GetAutoDisplayDurationSec() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAutoDisplayDurationSec", reflect.TypeOf((*MockIVirtualImageLogicMgr)(nil).GetAutoDisplayDurationSec))
}

// GetBindBeInvitedList mocks base method.
func (m *MockIVirtualImageLogicMgr) GetBindBeInvitedList(arg0 context.Context, arg1 uint32) (*virtual_image_logic.GetBindBeInvitedListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBindBeInvitedList", arg0, arg1)
	ret0, _ := ret[0].(*virtual_image_logic.GetBindBeInvitedListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBindBeInvitedList indicates an expected call of GetBindBeInvitedList.
func (mr *MockIVirtualImageLogicMgrMockRecorder) GetBindBeInvitedList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBindBeInvitedList", reflect.TypeOf((*MockIVirtualImageLogicMgr)(nil).GetBindBeInvitedList), arg0, arg1)
}

// GetBindInvitableList mocks base method.
func (m *MockIVirtualImageLogicMgr) GetBindInvitableList(arg0 context.Context, arg1, arg2 uint32, arg3 string) (*virtual_image_logic.GetBindInvitableListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBindInvitableList", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*virtual_image_logic.GetBindInvitableListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBindInvitableList indicates an expected call of GetBindInvitableList.
func (mr *MockIVirtualImageLogicMgrMockRecorder) GetBindInvitableList(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBindInvitableList", reflect.TypeOf((*MockIVirtualImageLogicMgr)(nil).GetBindInvitableList), arg0, arg1, arg2, arg3)
}

// GetBindInviteStatus mocks base method.
func (m *MockIVirtualImageLogicMgr) GetBindInviteStatus(arg0 context.Context, arg1 uint32, arg2 string) (*virtual_image_logic.GetBindInviteStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBindInviteStatus", arg0, arg1, arg2)
	ret0, _ := ret[0].(*virtual_image_logic.GetBindInviteStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBindInviteStatus indicates an expected call of GetBindInviteStatus.
func (mr *MockIVirtualImageLogicMgrMockRecorder) GetBindInviteStatus(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBindInviteStatus", reflect.TypeOf((*MockIVirtualImageLogicMgr)(nil).GetBindInviteStatus), arg0, arg1, arg2)
}

// GetCommodityDataList mocks base method.
func (m *MockIVirtualImageLogicMgr) GetCommodityDataList(arg0 context.Context, arg1 *virtual_image_logic.GetCommodityDataListRequest) (*virtual_image_logic.GetCommodityDataListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCommodityDataList", arg0, arg1)
	ret0, _ := ret[0].(*virtual_image_logic.GetCommodityDataListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCommodityDataList indicates an expected call of GetCommodityDataList.
func (mr *MockIVirtualImageLogicMgrMockRecorder) GetCommodityDataList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCommodityDataList", reflect.TypeOf((*MockIVirtualImageLogicMgr)(nil).GetCommodityDataList), arg0, arg1)
}

// GetCommodityDataListById mocks base method.
func (m *MockIVirtualImageLogicMgr) GetCommodityDataListById(arg0 context.Context, arg1 *virtual_image_logic.GetCommodityDataListByIdRequest) (*virtual_image_logic.GetCommodityDataListByIdResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCommodityDataListById", arg0, arg1)
	ret0, _ := ret[0].(*virtual_image_logic.GetCommodityDataListByIdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCommodityDataListById indicates an expected call of GetCommodityDataListById.
func (mr *MockIVirtualImageLogicMgrMockRecorder) GetCommodityDataListById(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCommodityDataListById", reflect.TypeOf((*MockIVirtualImageLogicMgr)(nil).GetCommodityDataListById), arg0, arg1)
}

// GetDefaultResourceList mocks base method.
func (m *MockIVirtualImageLogicMgr) GetDefaultResourceList(arg0 context.Context) (*virtual_image_logic.GetDefaultResourceListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDefaultResourceList", arg0)
	ret0, _ := ret[0].(*virtual_image_logic.GetDefaultResourceListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDefaultResourceList indicates an expected call of GetDefaultResourceList.
func (mr *MockIVirtualImageLogicMgrMockRecorder) GetDefaultResourceList(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDefaultResourceList", reflect.TypeOf((*MockIVirtualImageLogicMgr)(nil).GetDefaultResourceList), arg0)
}

// GetLevelIconConfig mocks base method.
func (m *MockIVirtualImageLogicMgr) GetLevelIconConfig(arg0 context.Context) map[uint32]*virtual_image_resource.LevelConfig {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLevelIconConfig", arg0)
	ret0, _ := ret[0].(map[uint32]*virtual_image_resource.LevelConfig)
	return ret0
}

// GetLevelIconConfig indicates an expected call of GetLevelIconConfig.
func (mr *MockIVirtualImageLogicMgrMockRecorder) GetLevelIconConfig(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLevelIconConfig", reflect.TypeOf((*MockIVirtualImageLogicMgr)(nil).GetLevelIconConfig), arg0)
}

// GetMyExpireRemindText mocks base method.
func (m *MockIVirtualImageLogicMgr) GetMyExpireRemindText() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMyExpireRemindText")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetMyExpireRemindText indicates an expected call of GetMyExpireRemindText.
func (mr *MockIVirtualImageLogicMgrMockRecorder) GetMyExpireRemindText() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMyExpireRemindText", reflect.TypeOf((*MockIVirtualImageLogicMgr)(nil).GetMyExpireRemindText))
}

// GetNoticeCfg mocks base method.
func (m *MockIVirtualImageLogicMgr) GetNoticeCfg(arg0 context.Context) (*virtual_image_logic.ChannelNoticeCfg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNoticeCfg", arg0)
	ret0, _ := ret[0].(*virtual_image_logic.ChannelNoticeCfg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNoticeCfg indicates an expected call of GetNoticeCfg.
func (mr *MockIVirtualImageLogicMgrMockRecorder) GetNoticeCfg(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNoticeCfg", reflect.TypeOf((*MockIVirtualImageLogicMgr)(nil).GetNoticeCfg), arg0)
}

// GetRecommendCommodityDataList mocks base method.
func (m *MockIVirtualImageLogicMgr) GetRecommendCommodityDataList(arg0 context.Context, arg1 *virtual_image_logic.GetRecommendCommodityDataListRequest) (*virtual_image_logic.GetRecommendCommodityDataListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRecommendCommodityDataList", arg0, arg1)
	ret0, _ := ret[0].(*virtual_image_logic.GetRecommendCommodityDataListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRecommendCommodityDataList indicates an expected call of GetRecommendCommodityDataList.
func (mr *MockIVirtualImageLogicMgrMockRecorder) GetRecommendCommodityDataList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRecommendCommodityDataList", reflect.TypeOf((*MockIVirtualImageLogicMgr)(nil).GetRecommendCommodityDataList), arg0, arg1)
}

// GetRedDotAlertStatus mocks base method.
func (m *MockIVirtualImageLogicMgr) GetRedDotAlertStatus(arg0 context.Context, arg1 uint32) (*virtual_image_logic.GetRedDotAlertStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRedDotAlertStatus", arg0, arg1)
	ret0, _ := ret[0].(*virtual_image_logic.GetRedDotAlertStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRedDotAlertStatus indicates an expected call of GetRedDotAlertStatus.
func (mr *MockIVirtualImageLogicMgrMockRecorder) GetRedDotAlertStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRedDotAlertStatus", reflect.TypeOf((*MockIVirtualImageLogicMgr)(nil).GetRedDotAlertStatus), arg0, arg1)
}

// GetResourceList mocks base method.
func (m *MockIVirtualImageLogicMgr) GetResourceList(arg0 context.Context, arg1 *virtual_image_logic.GetResourceListRequest) (*virtual_image_logic.GetResourceListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetResourceList", arg0, arg1)
	ret0, _ := ret[0].(*virtual_image_logic.GetResourceListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetResourceList indicates an expected call of GetResourceList.
func (mr *MockIVirtualImageLogicMgrMockRecorder) GetResourceList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetResourceList", reflect.TypeOf((*MockIVirtualImageLogicMgr)(nil).GetResourceList), arg0, arg1)
}

// GetTargetExpireRemindText mocks base method.
func (m *MockIVirtualImageLogicMgr) GetTargetExpireRemindText() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTargetExpireRemindText")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetTargetExpireRemindText indicates an expected call of GetTargetExpireRemindText.
func (mr *MockIVirtualImageLogicMgrMockRecorder) GetTargetExpireRemindText() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTargetExpireRemindText", reflect.TypeOf((*MockIVirtualImageLogicMgr)(nil).GetTargetExpireRemindText))
}

// GetUserDisplaySwitchList mocks base method.
func (m *MockIVirtualImageLogicMgr) GetUserDisplaySwitchList(arg0 context.Context, arg1 uint32) ([]*virtual_image_logic.VirtualImageDisplaySwitchInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserDisplaySwitchList", arg0, arg1)
	ret0, _ := ret[0].([]*virtual_image_logic.VirtualImageDisplaySwitchInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserDisplaySwitchList indicates an expected call of GetUserDisplaySwitchList.
func (mr *MockIVirtualImageLogicMgrMockRecorder) GetUserDisplaySwitchList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserDisplaySwitchList", reflect.TypeOf((*MockIVirtualImageLogicMgr)(nil).GetUserDisplaySwitchList), arg0, arg1)
}

// GetUserRelationInfo mocks base method.
func (m *MockIVirtualImageLogicMgr) GetUserRelationInfo(arg0 context.Context, arg1 uint32) (*virtual_image_logic.UserRelationInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserRelationInfo", arg0, arg1)
	ret0, _ := ret[0].(*virtual_image_logic.UserRelationInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserRelationInfo indicates an expected call of GetUserRelationInfo.
func (mr *MockIVirtualImageLogicMgrMockRecorder) GetUserRelationInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserRelationInfo", reflect.TypeOf((*MockIVirtualImageLogicMgr)(nil).GetUserRelationInfo), arg0, arg1)
}

// GetUserVirtualImage mocks base method.
func (m *MockIVirtualImageLogicMgr) GetUserVirtualImage(arg0 context.Context, arg1 uint32, arg2 uint16) (*virtual_image_logic.GetUserVirtualImageResponse, uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserVirtualImage", arg0, arg1, arg2)
	ret0, _ := ret[0].(*virtual_image_logic.GetUserVirtualImageResponse)
	ret1, _ := ret[1].(uint32)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetUserVirtualImage indicates an expected call of GetUserVirtualImage.
func (mr *MockIVirtualImageLogicMgrMockRecorder) GetUserVirtualImage(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserVirtualImage", reflect.TypeOf((*MockIVirtualImageLogicMgr)(nil).GetUserVirtualImage), arg0, arg1, arg2)
}

// GetUserVirtualImageDisplay mocks base method.
func (m *MockIVirtualImageLogicMgr) GetUserVirtualImageDisplay(arg0 context.Context, arg1 uint32, arg2 *virtual_image_logic.GetUserVirtualImageDisplayRequest) (*virtual_image_logic.GetUserVirtualImageDisplayResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserVirtualImageDisplay", arg0, arg1, arg2)
	ret0, _ := ret[0].(*virtual_image_logic.GetUserVirtualImageDisplayResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserVirtualImageDisplay indicates an expected call of GetUserVirtualImageDisplay.
func (mr *MockIVirtualImageLogicMgrMockRecorder) GetUserVirtualImageDisplay(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserVirtualImageDisplay", reflect.TypeOf((*MockIVirtualImageLogicMgr)(nil).GetUserVirtualImageDisplay), arg0, arg1, arg2)
}

// GetUserVirtualImagePose mocks base method.
func (m *MockIVirtualImageLogicMgr) GetUserVirtualImagePose(arg0 context.Context, arg1 uint32, arg2 uint16) (*virtual_image_logic.GetUserVirtualImagePoseResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserVirtualImagePose", arg0, arg1, arg2)
	ret0, _ := ret[0].(*virtual_image_logic.GetUserVirtualImagePoseResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserVirtualImagePose indicates an expected call of GetUserVirtualImagePose.
func (mr *MockIVirtualImageLogicMgrMockRecorder) GetUserVirtualImagePose(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserVirtualImagePose", reflect.TypeOf((*MockIVirtualImageLogicMgr)(nil).GetUserVirtualImagePose), arg0, arg1, arg2)
}

// GetVirtualImageBeginnerGuide mocks base method.
func (m *MockIVirtualImageLogicMgr) GetVirtualImageBeginnerGuide(arg0 context.Context, arg1 uint32) (bool, uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetVirtualImageBeginnerGuide", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(uint32)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetVirtualImageBeginnerGuide indicates an expected call of GetVirtualImageBeginnerGuide.
func (mr *MockIVirtualImageLogicMgrMockRecorder) GetVirtualImageBeginnerGuide(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVirtualImageBeginnerGuide", reflect.TypeOf((*MockIVirtualImageLogicMgr)(nil).GetVirtualImageBeginnerGuide), arg0, arg1)
}

// GetVirtualImageCardCommonCfg mocks base method.
func (m *MockIVirtualImageLogicMgr) GetVirtualImageCardCommonCfg(arg0 context.Context) (*virtual_image_logic.GetVirtualImageCardCommonCfgResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetVirtualImageCardCommonCfg", arg0)
	ret0, _ := ret[0].(*virtual_image_logic.GetVirtualImageCardCommonCfgResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetVirtualImageCardCommonCfg indicates an expected call of GetVirtualImageCardCommonCfg.
func (mr *MockIVirtualImageLogicMgrMockRecorder) GetVirtualImageCardCommonCfg(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVirtualImageCardCommonCfg", reflect.TypeOf((*MockIVirtualImageLogicMgr)(nil).GetVirtualImageCardCommonCfg), arg0)
}

// GetVirtualImageCardEntry mocks base method.
func (m *MockIVirtualImageLogicMgr) GetVirtualImageCardEntry(arg0 context.Context, arg1 uint32) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetVirtualImageCardEntry", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetVirtualImageCardEntry indicates an expected call of GetVirtualImageCardEntry.
func (mr *MockIVirtualImageLogicMgrMockRecorder) GetVirtualImageCardEntry(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVirtualImageCardEntry", reflect.TypeOf((*MockIVirtualImageLogicMgr)(nil).GetVirtualImageCardEntry), arg0, arg1)
}

// GetVirtualImageCardEntryStatus mocks base method.
func (m *MockIVirtualImageLogicMgr) GetVirtualImageCardEntryStatus(arg0 context.Context, arg1 uint32) (*virtual_image_logic.GetVirtualImageCardEntryStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetVirtualImageCardEntryStatus", arg0, arg1)
	ret0, _ := ret[0].(*virtual_image_logic.GetVirtualImageCardEntryStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetVirtualImageCardEntryStatus indicates an expected call of GetVirtualImageCardEntryStatus.
func (mr *MockIVirtualImageLogicMgrMockRecorder) GetVirtualImageCardEntryStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVirtualImageCardEntryStatus", reflect.TypeOf((*MockIVirtualImageLogicMgr)(nil).GetVirtualImageCardEntryStatus), arg0, arg1)
}

// GetVirtualImageCommodityRedDot mocks base method.
func (m *MockIVirtualImageLogicMgr) GetVirtualImageCommodityRedDot(arg0 context.Context, arg1 *virtual_image_logic.GetVirtualImageCommodityRedDotRequest) (*virtual_image_logic.GetVirtualImageCommodityRedDotResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetVirtualImageCommodityRedDot", arg0, arg1)
	ret0, _ := ret[0].(*virtual_image_logic.GetVirtualImageCommodityRedDotResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetVirtualImageCommodityRedDot indicates an expected call of GetVirtualImageCommodityRedDot.
func (mr *MockIVirtualImageLogicMgrMockRecorder) GetVirtualImageCommodityRedDot(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVirtualImageCommodityRedDot", reflect.TypeOf((*MockIVirtualImageLogicMgr)(nil).GetVirtualImageCommodityRedDot), arg0, arg1)
}

// GetVirtualImageCommodityRedDotDetail mocks base method.
func (m *MockIVirtualImageLogicMgr) GetVirtualImageCommodityRedDotDetail(arg0 context.Context, arg1 *virtual_image_logic.GetVirtualImageCommodityRedDotDetailRequest) (*virtual_image_logic.GetVirtualImageCommodityRedDotDetailResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetVirtualImageCommodityRedDotDetail", arg0, arg1)
	ret0, _ := ret[0].(*virtual_image_logic.GetVirtualImageCommodityRedDotDetailResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetVirtualImageCommodityRedDotDetail indicates an expected call of GetVirtualImageCommodityRedDotDetail.
func (mr *MockIVirtualImageLogicMgrMockRecorder) GetVirtualImageCommodityRedDotDetail(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVirtualImageCommodityRedDotDetail", reflect.TypeOf((*MockIVirtualImageLogicMgr)(nil).GetVirtualImageCommodityRedDotDetail), arg0, arg1)
}

// GetVirtualImagePoseType mocks base method.
func (m *MockIVirtualImageLogicMgr) GetVirtualImagePoseType(arg0 context.Context, arg1 uint32) ([]*virtual_image_logic.VirtualImagePoseTypeInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetVirtualImagePoseType", arg0, arg1)
	ret0, _ := ret[0].([]*virtual_image_logic.VirtualImagePoseTypeInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetVirtualImagePoseType indicates an expected call of GetVirtualImagePoseType.
func (mr *MockIVirtualImageLogicMgrMockRecorder) GetVirtualImagePoseType(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVirtualImagePoseType", reflect.TypeOf((*MockIVirtualImageLogicMgr)(nil).GetVirtualImagePoseType), arg0, arg1)
}

// GetVirtualImageResourceCategory mocks base method.
func (m *MockIVirtualImageLogicMgr) GetVirtualImageResourceCategory(arg0 context.Context, arg1 *virtual_image_logic.GetVirtualImageResourceCategoryRequest) (*virtual_image_logic.GetVirtualImageResourceCategoryResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetVirtualImageResourceCategory", arg0, arg1)
	ret0, _ := ret[0].(*virtual_image_logic.GetVirtualImageResourceCategoryResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetVirtualImageResourceCategory indicates an expected call of GetVirtualImageResourceCategory.
func (mr *MockIVirtualImageLogicMgrMockRecorder) GetVirtualImageResourceCategory(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVirtualImageResourceCategory", reflect.TypeOf((*MockIVirtualImageLogicMgr)(nil).GetVirtualImageResourceCategory), arg0, arg1)
}

// MarkVirtualImageBeginnerGuideDone mocks base method.
func (m *MockIVirtualImageLogicMgr) MarkVirtualImageBeginnerGuideDone(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MarkVirtualImageBeginnerGuideDone", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// MarkVirtualImageBeginnerGuideDone indicates an expected call of MarkVirtualImageBeginnerGuideDone.
func (mr *MockIVirtualImageLogicMgrMockRecorder) MarkVirtualImageBeginnerGuideDone(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MarkVirtualImageBeginnerGuideDone", reflect.TypeOf((*MockIVirtualImageLogicMgr)(nil).MarkVirtualImageBeginnerGuideDone), arg0, arg1)
}

// PaddingCommodityData mocks base method.
func (m *MockIVirtualImageLogicMgr) PaddingCommodityData(arg0 context.Context, arg1 uint32, arg2 []*virtual_image_mall.CommodityData, arg3 []*virtual_image_resource.VirtualImageResourceInfo, arg4 map[string]*virtual_image_user.UserSuitInfo, arg5 map[string]*virtual_image_user.UserItemInfo, arg6 bool) ([]*virtual_image_logic.CommodityData, []*virtual_image_logic.CommodityData) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PaddingCommodityData", arg0, arg1, arg2, arg3, arg4, arg5, arg6)
	ret0, _ := ret[0].([]*virtual_image_logic.CommodityData)
	ret1, _ := ret[1].([]*virtual_image_logic.CommodityData)
	return ret0, ret1
}

// PaddingCommodityData indicates an expected call of PaddingCommodityData.
func (mr *MockIVirtualImageLogicMgrMockRecorder) PaddingCommodityData(arg0, arg1, arg2, arg3, arg4, arg5, arg6 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PaddingCommodityData", reflect.TypeOf((*MockIVirtualImageLogicMgr)(nil).PaddingCommodityData), arg0, arg1, arg2, arg3, arg4, arg5, arg6)
}

// RedDotAlertReaded mocks base method.
func (m *MockIVirtualImageLogicMgr) RedDotAlertReaded(arg0 context.Context, arg1, arg2, arg3 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RedDotAlertReaded", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// RedDotAlertReaded indicates an expected call of RedDotAlertReaded.
func (mr *MockIVirtualImageLogicMgrMockRecorder) RedDotAlertReaded(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RedDotAlertReaded", reflect.TypeOf((*MockIVirtualImageLogicMgr)(nil).RedDotAlertReaded), arg0, arg1, arg2, arg3)
}

// RefreshVirtualImageRedDotAlert mocks base method.
func (m *MockIVirtualImageLogicMgr) RefreshVirtualImageRedDotAlert(arg0 context.Context) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "RefreshVirtualImageRedDotAlert", arg0)
}

// RefreshVirtualImageRedDotAlert indicates an expected call of RefreshVirtualImageRedDotAlert.
func (mr *MockIVirtualImageLogicMgrMockRecorder) RefreshVirtualImageRedDotAlert(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RefreshVirtualImageRedDotAlert", reflect.TypeOf((*MockIVirtualImageLogicMgr)(nil).RefreshVirtualImageRedDotAlert), arg0)
}

// RejectBindInvite mocks base method.
func (m *MockIVirtualImageLogicMgr) RejectBindInvite(arg0 context.Context, arg1 uint32, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RejectBindInvite", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// RejectBindInvite indicates an expected call of RejectBindInvite.
func (mr *MockIVirtualImageLogicMgrMockRecorder) RejectBindInvite(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RejectBindInvite", reflect.TypeOf((*MockIVirtualImageLogicMgr)(nil).RejectBindInvite), arg0, arg1, arg2)
}

// ResourceCategoryLoad mocks base method.
func (m *MockIVirtualImageLogicMgr) ResourceCategoryLoad() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "ResourceCategoryLoad")
}

// ResourceCategoryLoad indicates an expected call of ResourceCategoryLoad.
func (mr *MockIVirtualImageLogicMgrMockRecorder) ResourceCategoryLoad() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResourceCategoryLoad", reflect.TypeOf((*MockIVirtualImageLogicMgr)(nil).ResourceCategoryLoad))
}

// SendBindInvite mocks base method.
func (m *MockIVirtualImageLogicMgr) SendBindInvite(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendBindInvite", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendBindInvite indicates an expected call of SendBindInvite.
func (mr *MockIVirtualImageLogicMgrMockRecorder) SendBindInvite(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendBindInvite", reflect.TypeOf((*MockIVirtualImageLogicMgr)(nil).SendBindInvite), arg0, arg1, arg2)
}

// SendChannelMsg mocks base method.
func (m *MockIVirtualImageLogicMgr) SendChannelMsg(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendChannelMsg", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendChannelMsg indicates an expected call of SendChannelMsg.
func (mr *MockIVirtualImageLogicMgrMockRecorder) SendChannelMsg(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendChannelMsg", reflect.TypeOf((*MockIVirtualImageLogicMgr)(nil).SendChannelMsg), arg0, arg1, arg2)
}

// SetUserDisplaySwitch mocks base method.
func (m *MockIVirtualImageLogicMgr) SetUserDisplaySwitch(arg0 context.Context, arg1, arg2 uint32, arg3 bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserDisplaySwitch", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetUserDisplaySwitch indicates an expected call of SetUserDisplaySwitch.
func (mr *MockIVirtualImageLogicMgrMockRecorder) SetUserDisplaySwitch(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserDisplaySwitch", reflect.TypeOf((*MockIVirtualImageLogicMgr)(nil).SetUserDisplaySwitch), arg0, arg1, arg2, arg3)
}

// SetUserVirtualImageInuse mocks base method.
func (m *MockIVirtualImageLogicMgr) SetUserVirtualImageInuse(arg0 context.Context, arg1 uint32, arg2 []*virtual_image_logic.SetUseItem, arg3 bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserVirtualImageInuse", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetUserVirtualImageInuse indicates an expected call of SetUserVirtualImageInuse.
func (mr *MockIVirtualImageLogicMgrMockRecorder) SetUserVirtualImageInuse(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserVirtualImageInuse", reflect.TypeOf((*MockIVirtualImageLogicMgr)(nil).SetUserVirtualImageInuse), arg0, arg1, arg2, arg3)
}

// SetUserVirtualImageOrientation mocks base method.
func (m *MockIVirtualImageLogicMgr) SetUserVirtualImageOrientation(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserVirtualImageOrientation", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetUserVirtualImageOrientation indicates an expected call of SetUserVirtualImageOrientation.
func (mr *MockIVirtualImageLogicMgrMockRecorder) SetUserVirtualImageOrientation(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserVirtualImageOrientation", reflect.TypeOf((*MockIVirtualImageLogicMgr)(nil).SetUserVirtualImageOrientation), arg0, arg1, arg2)
}

// SetVirtualBindInUse mocks base method.
func (m *MockIVirtualImageLogicMgr) SetVirtualBindInUse(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetVirtualBindInUse", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetVirtualBindInUse indicates an expected call of SetVirtualBindInUse.
func (mr *MockIVirtualImageLogicMgrMockRecorder) SetVirtualBindInUse(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetVirtualBindInUse", reflect.TypeOf((*MockIVirtualImageLogicMgr)(nil).SetVirtualBindInUse), arg0, arg1, arg2)
}

// SetVirtualImagePoseType mocks base method.
func (m *MockIVirtualImageLogicMgr) SetVirtualImagePoseType(arg0 context.Context, arg1, arg2, arg3 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetVirtualImagePoseType", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetVirtualImagePoseType indicates an expected call of SetVirtualImagePoseType.
func (mr *MockIVirtualImageLogicMgrMockRecorder) SetVirtualImagePoseType(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetVirtualImagePoseType", reflect.TypeOf((*MockIVirtualImageLogicMgr)(nil).SetVirtualImagePoseType), arg0, arg1, arg2, arg3)
}

// ShippedCommodityData mocks base method.
func (m *MockIVirtualImageLogicMgr) ShippedCommodityData(arg0 context.Context, arg1 uint32, arg2 []*virtual_image_mall.CommodityDataOrders) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "ShippedCommodityData", arg0, arg1, arg2)
}

// ShippedCommodityData indicates an expected call of ShippedCommodityData.
func (mr *MockIVirtualImageLogicMgrMockRecorder) ShippedCommodityData(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ShippedCommodityData", reflect.TypeOf((*MockIVirtualImageLogicMgr)(nil).ShippedCommodityData), arg0, arg1, arg2)
}

// ShutDown mocks base method.
func (m *MockIVirtualImageLogicMgr) ShutDown() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "ShutDown")
}

// ShutDown indicates an expected call of ShutDown.
func (mr *MockIVirtualImageLogicMgrMockRecorder) ShutDown() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ShutDown", reflect.TypeOf((*MockIVirtualImageLogicMgr)(nil).ShutDown))
}

// UnbindVirtualImage mocks base method.
func (m *MockIVirtualImageLogicMgr) UnbindVirtualImage(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnbindVirtualImage", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UnbindVirtualImage indicates an expected call of UnbindVirtualImage.
func (mr *MockIVirtualImageLogicMgrMockRecorder) UnbindVirtualImage(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnbindVirtualImage", reflect.TypeOf((*MockIVirtualImageLogicMgr)(nil).UnbindVirtualImage), arg0, arg1, arg2)
}
