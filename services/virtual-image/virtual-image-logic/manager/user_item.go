package manager

import (
    "context"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    "golang.52tt.com/clients/account"
    "golang.52tt.com/pkg/protocol"
    "golang.52tt.com/protocol/app"
    pb "golang.52tt.com/protocol/app/virtual_image_logic"
    "golang.52tt.com/protocol/common/status"
    channel_wedding "golang.52tt.com/protocol/services/channel-wedding"
    virtual_image_card "golang.52tt.com/protocol/services/virtual-image-card"
    mallPb "golang.52tt.com/protocol/services/virtual-image-mall"
    virtual_image_resource "golang.52tt.com/protocol/services/virtual-image-resource"
    virtual_image_user "golang.52tt.com/protocol/services/virtual-image-user"
    "golang.52tt.com/services/virtual-image/virtual-image-logic/util"
    "google.golang.org/grpc/codes"
    "sort"
    "time"
)

func (m *VirtualImageLogicMgr) GetUserVirtualImage(ctx context.Context, uid uint32, clientType uint16) (*pb.GetUserVirtualImageResponse, uint32, error) {
    out := &pb.GetUserVirtualImageResponse{
        ItemTabs:         make([]*pb.VirtualImageItemTab, 0),
        InuseRightsItems: make([]*pb.UserVirtualImageItem, 0),
        InuseResourceIds: make([]uint32, 0),
    }

    userItemResp, err := m.vUserClient.GetUserVirtualImageList(ctx, &virtual_image_user.GetUserVirtualImageListReq{Uid: uid})
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUserVirtualImageList uid:%d, err:%v", uid, err)
        return out, 0, err
    }

    maxDisplayTime := time.Now().AddDate(2, 0, 0).Unix() // 超过2年的物品不展示过期时间
    for _, item := range userItemResp.GetInuseItems() {
        // 所有在使用的物品
        out.InuseResourceIds = append(out.InuseResourceIds, item.GetCfgId())

        if item.GetUseRightsType() == 0 {
            continue
        }

        // 在使用的权益类物品
        out.InuseRightsItems = append(out.InuseRightsItems, &pb.UserVirtualImageItem{
            ResourceId:    item.GetCfgId(),
            ExpireTs:      getItemDisplayTime(item.GetExpireTime(), maxDisplayTime),
            Inuse:         true,
            UseRightsType: item.GetUseRightsType(),
        })
    }

    if len(userItemResp.GetItems()) == 0 {
        return out, userItemResp.GetOrientation(), nil
    }

    category2Items := make(map[uint32][]*pb.UserVirtualImageItem)
    for _, item := range userItemResp.GetItems() {
        if out.GetLastUpdateTs() < item.GetUpdateTime() {
            out.LastUpdateTs = item.GetUpdateTime()
        }

        if _, ok := category2Items[item.GetSubCategory()]; !ok {
            category2Items[item.GetSubCategory()] = make([]*pb.UserVirtualImageItem, 0)
        }
        category2Items[item.GetSubCategory()] = append(category2Items[item.GetSubCategory()], fillUserVirtualImageItem(item, maxDisplayTime))
    }

    // 获取所有资源分类信息
    categoryInfoList := m.localCache.GetAllResourceCategoryInfos()

    sub2CategoryInfo := make(map[uint32]*virtual_image_resource.VirtualImageSubCategoryInfo)
    for _, category := range categoryInfoList {
        for _, subCategory := range category.GetSubCategoryInfoList() {
            sub2CategoryInfo[subCategory.GetSubCategory()] = subCategory
        }
    }

    suitList := make([]*pb.UserVirtualImageSuit, 0)
    var inuseSuitLen int
    var inuseSuitId uint32
    for _, suit := range userItemResp.GetSuits() {
        suitPb, inuse := fillUserVirtualImageSuit(suit, sub2CategoryInfo, maxDisplayTime)
        suitList = append(suitList, suitPb)

        if inuse && inuseSuitLen < len(suit.GetItems()) {
            inuseSuitLen = len(suit.GetItems())
            inuseSuitId = suit.GetSuitId()
        }
    }
    // 处理套装的使用状态
    for _, suit := range suitList {
        if suit.GetSuitId() == inuseSuitId {
            suit.Inuse = true
            break
        }
    }

    for _, category := range categoryInfoList {
        for _, subCategory := range category.GetSubCategoryInfoList() {
            items, ok := category2Items[subCategory.GetSubCategory()]
            if !ok {
                continue
            }
            tab := &pb.VirtualImageItemTab{
                TabName:         subCategory.GetSubCategoryName(),
                TabIcon:         subCategory.GetSubCategoryImgUrl(),
                TabSelectIcon:   subCategory.GetSubCategoryImgUrlSelected(),
                Items:           items,
                CategoryType:    category.GetParentCategoryInfo().GetCategoryType(),
                Category:        category.GetParentCategoryInfo().GetCategory(),
                SubCategoryType: subCategory.GetSubCategoryType(),
                SubCategory:     subCategory.GetSubCategory(),
            }

            if clientType == protocol.ClientTypePcTT {
                tab.TabIcon = subCategory.GetWebSubCategoryImgUrl()
                tab.TabSelectIcon = subCategory.GetWebSubCategoryImgUrlSelected()
            }
            out.ItemTabs = append(out.ItemTabs, tab)
        }
    }

    suitTab := m.dyconfig.GetSuitTabCfg()
    if suitTab != nil && len(suitList) > 0 {
        tab := &pb.VirtualImageSuitTab{
            TabName:       suitTab.Name,
            TabIcon:       suitTab.Icon,
            TabSelectIcon: suitTab.SelectIcon,
            Suits:         suitList,
        }
        if clientType == protocol.ClientTypePcTT {
            tab.TabIcon = suitTab.PCIcon
            tab.TabSelectIcon = suitTab.PCSelectIcon
        }

        out.SuitTab = tab
    }

    allTab := m.dyconfig.GetAllTabCfg()
    if allTab != nil {
        out.AllTabCfg = &pb.VirtualImageAllTabCfg{
            TabName:       allTab.Name,
            TabIcon:       allTab.Icon,
            TabSelectIcon: allTab.SelectIcon,
        }
        if clientType == protocol.ClientTypePcTT {
            out.AllTabCfg.TabIcon = allTab.PCIcon
            out.AllTabCfg.TabSelectIcon = allTab.PCSelectIcon
        }
    }

    return out, userItemResp.GetOrientation(), nil
}

func (m *VirtualImageLogicMgr) checkUseRightsItem(ctx context.Context, uid uint32, rightsCfgIdList []uint32) (canUseList []uint32, err error) {
    if len(rightsCfgIdList) == 0 {
        return
    }

    // 检查用户权益
    cardResp, err := m.cardCli.GetUserCardInfo(ctx, &virtual_image_card.GetUserCardInfoReq{
        Uid: uid,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "checkUseRightsItem fail to GetUserCardInfo. uid:%d, err:%v", uid, err)
        return nil, err
    }

    cardInfo := cardResp.GetCard()
    now := time.Now()
    if cardInfo.GetEffectTs() > now.Unix() || now.Unix() >= cardInfo.GetExpireTs() {
        // 该用户无权益
        return nil, nil
    }

    // 检查是否是权益商品
    commodityResp, err := m.mallClient.GetCommodityDataList(ctx, &mallPb.GetCommodityDataListRequest{
        ResourceIdList: rightsCfgIdList,
        ShelfStatus:    uint32(mallPb.ShelfStatus_SHELF_STATUS_NOW),
        GainPath:       uint32(mallPb.CommodityGainPath_COMMODITY_GAIN_PATH_INFINITE_CHANGE_CARD),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "checkUseRightsItem fail to GetCommodityDataList. uid:%d, err:%v", uid, err)
        return nil, err
    }

    commodityCfgIdMap := make(map[uint32]bool)
    for _, commodity := range commodityResp.GetCommodityDataList() {
        for _, cfgId := range commodity.GetResourceIdList() {
            commodityCfgIdMap[cfgId] = true
        }
    }

    canUseList = make([]uint32, 0)
    for _, cfgId := range rightsCfgIdList {
        if _, ok := commodityCfgIdMap[cfgId]; !ok {
            // 该物品不是权益商品， 不能使用
            continue
        }
        canUseList = append(canUseList, cfgId)
    }

    return canUseList, nil
}

func (m *VirtualImageLogicMgr) SetUserVirtualImageInuse(ctx context.Context, uid uint32, items []*pb.SetUseItem, isFullUpdate bool) error {
    list := make([]*virtual_image_user.ItemInfo, 0)
    var err error

    weddingSubCateList := m.dyconfig.GetWeddingAllowChangeSubCategoryList()
    weddingSubCateMap := make(map[uint32]bool)
    for _, subCate := range weddingSubCateList {
        weddingSubCateMap[subCate] = true
    }

    commCfgIdList := make([]uint32, 0)   // 普通物品id列表
    rightsCfgIdList := make([]uint32, 0) // 权益类物品id列表

    for _, item := range items {
        if item.GetResourceId() == 0 {
            continue
        }

        if item.GetRightsType() > 0 {
            rightsCfgIdList = append(rightsCfgIdList, item.GetResourceId())
        } else {
            commCfgIdList = append(commCfgIdList, item.GetResourceId())
        }
    }

    // 检查是否可以使用权益类物品
    canUseRightsIdList, err := m.checkUseRightsItem(ctx, uid, rightsCfgIdList)
    if err != nil {
        log.ErrorWithCtx(ctx, "SetUserVirtualImageInuse fail to checkUseRightsItem. uid:%d, items:%v, err:%v",
            uid, items, err)
        return err
    }

    useCfgIdList := append(commCfgIdList, canUseRightsIdList...) // 可使用的物品列表
    cfgMap := make(map[uint32]*virtual_image_resource.VirtualImageResourceInfo)
    if len(useCfgIdList) >= 0 {
        cfgResp, err := m.resourceCli.GetVirtualImageResourcesByIds(ctx, &virtual_image_resource.GetVirtualImageResourcesByIdsRequest{
            Ids: useCfgIdList,
        })
        if err != nil {
            log.ErrorWithCtx(ctx, "SetUserVirtualImageInuse fail to GetVirtualImageResourcesByIds. uid:%d, items:%v, err:%v",
                uid, items, err)
            return err
        }

        for _, cfg := range cfgResp.GetResources() {
            cfgMap[cfg.GetId()] = cfg
        }
    }

    var needCheckWedding bool
    for _, item := range items {
        subCategory := item.GetSubCategory()
        if item.GetResourceId() > 0 {
            cfg, ok := cfgMap[item.GetResourceId()]
            if !ok || cfg.GetSubCategory() == 0 {
                log.ErrorWithCtx(ctx, "SetUserVirtualImageInuse fail to GetVirtualImageResourcesByIds. uid:%d, item:%v",
                    uid, item)
                //return protocol.NewExactServerError(codes.OK, status.ErrVirtualAvatarNotFound, "物品资源不存在")
                continue
            }
            subCategory = cfg.GetSubCategory()
        }

        if !isFullUpdate && !weddingSubCateMap[subCategory] {
            needCheckWedding = true
        }

        list = append(list, &virtual_image_user.ItemInfo{
            CfgId:         item.GetResourceId(),
            SubCategory:   subCategory,
            UseRightsType: item.GetRightsType(),
        })
    }

    if isFullUpdate && len(weddingSubCateMap) > 0 {
        // 找出使用物品中与已佩戴物品不同的子分类
        diffSubCateMap, err := m.getDiffItemSubCategory(ctx, uid, list)
        if err != nil {
            log.ErrorWithCtx(ctx, "SetUserVirtualImageInuse fail to getDiffItemSubCategory. uid:%d, items:%v, err:%v", uid, items, err)
            return err
        }

        for subCate := range diffSubCateMap {
            // 有不同的子分类，需要检查是否在婚礼中
            if !weddingSubCateMap[subCate] {
                needCheckWedding = true
                break
            }
        }
    }

    if needCheckWedding {
        inWedding, err := m.checkIfInWedding(ctx, uid)
        if err != nil {
            log.ErrorWithCtx(ctx, "SetUserVirtualImageInuse fail to checkIfInWedding. uid:%d, err:%v", uid, err)
            return err
        }

        if inWedding {
            log.ErrorWithCtx(ctx, "SetUserVirtualImageInuse fail to checkIfInWedding. uid:%d, weddingSubCateMap:%+v, err:%v",
                uid, weddingSubCateMap, err)
            return protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "婚礼过程中暂不支持更换该装扮哦~")
        }
    }

    _, err = m.vUserClient.SetUserVirtualImageInUse(ctx, &virtual_image_user.SetUserVirtualImageInUseReq{
        Uid:          uid,
        Items:        list,
        IsFullUpdate: isFullUpdate, // 全量覆盖已使用的物品，即会先取消佩戴用户已佩戴的所有物品
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "SetUserVirtualImageInUse uid:%d, items:%v, err:%v", uid, items, err)
        return err
    }

    return nil
}

func (m *VirtualImageLogicMgr) getDiffItemSubCategory(ctx context.Context, uid uint32, newItems []*virtual_image_user.ItemInfo) (map[uint32]bool, error) {
    resp, err := m.vUserClient.BatchGetUserInuseItemInfo(ctx, &virtual_image_user.BatchGetUserInuseItemInfoReq{
        UidList: []uint32{uid},
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "getDiffItemSubCategory fail to BatchGetUserInuseItemInfo. uid:%d, err:%v", uid, err)
        return nil, err
    }

    oldSubCategoryMap := make(map[uint32]uint32)
    for _, item := range resp.GetUserInuseItemInfo() {
        for _, oldItem := range item.GetItems() {
            oldSubCategoryMap[oldItem.GetSubCategory()] = oldItem.GetCfgId()
        }
    }

    newSubCategoryMap := make(map[uint32]uint32)
    for _, item := range newItems {
        newSubCategoryMap[item.GetSubCategory()] = item.GetCfgId()
    }

    // get different sub category
    diffSubCategoryMap := make(map[uint32]bool)
    for oldSubCategory, oldCfgId := range oldSubCategoryMap {
        if newCfgId, ok := newSubCategoryMap[oldSubCategory]; !ok || oldCfgId != newCfgId {
            diffSubCategoryMap[oldSubCategory] = true
        }
    }
    for newSubCategory, newCfgId := range newSubCategoryMap {
        if _, ok := diffSubCategoryMap[newSubCategory]; ok {
            continue
        }
        if oldCfgId, ok := oldSubCategoryMap[newSubCategory]; !ok || oldCfgId != newCfgId {
            diffSubCategoryMap[newSubCategory] = true
        }
    }

    log.DebugWithCtx(ctx, "getDiffItemSubCategory uid:%d, oldSubCategoryMap:%+v, newSubCategoryMap:%+v, diffSubCategoryMap:%+v")
    return diffSubCategoryMap, nil
}

// BatchGetUserVirtualImageInuse 批量获取用户已佩戴的物品
func (m *VirtualImageLogicMgr) BatchGetUserVirtualImageInuse(ctx context.Context, opUid uint32, in *pb.BatchGetUserVirtualImageInuseRequest) ([]*pb.UserVirtualImageInuse, error) {
    list := make([]*pb.UserVirtualImageInuse, 0)
    if len(in.GetUidList()) == 0 {
        return list, nil
    }
    if len(in.GetUidList()) > 100 {
        log.WarnWithCtx(ctx, "BatchGetUserVirtualImageInuse uidList too long. len:%v", len(in.GetUidList()))
        return list, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "uidList too long")
    }

    var err error
    userMap, err := m.userProfileCli.BatchGetUserProfileV2(ctx, in.GetUidList(), true)
    if err != nil {
        log.ErrorWithCtx(ctx, "BatchGetUserVirtualImageInuse fail to BatchGetUserProfileV2 opUid:%d, uidList:%v, err:%v",
            opUid, in.GetUidList(), err)
        return list, err
    }

    uidList := make([]uint32, 0)
    if in.GetScope() > 0 {
        switchResp, err := m.vUserClient.BatchGetUserDisplaySwitch(ctx, &virtual_image_user.BatchGetUserDisplaySwitchRequest{
            UidList: in.GetUidList(),
        })
        if err != nil {
            log.ErrorWithCtx(ctx, "BatchGetUserVirtualImageInuse fail to BatchGetUserDisplaySwitch opUid:%d, uidList:%v, err:%v", opUid, in.GetUidList(), err)
            return list, err
        }

        for _, switchInfo := range switchResp.GetUserDisplaySwitchList() {
            if needUKWCheck(in.GetScope()) && isUKWUser(userMap[switchInfo.GetUid()]) {
                // 神秘人不返回虚拟形象信息
                continue
            }
            for _, switchItem := range switchInfo.GetDisplaySwitch() {
                if switchItem.GetType() == in.GetScope() && switchItem.GetSwitchOn() {
                    // 该场景开关开启，获取用户已佩戴物品
                    uidList = append(uidList, switchInfo.GetUid())
                    break
                }
            }
        }
    } else {
        uidList = in.GetUidList()
    }

    if len(uidList) == 0 {
        return list, nil
    }

    resp, err := m.vUserClient.BatchGetUserInuseItemInfo(ctx, &virtual_image_user.BatchGetUserInuseItemInfoReq{
        UidList: uidList,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "BatchGetUserVirtualImageInuse fail to BatchGetUserInuseItemInfo opUid:%d, uidList:%v, err:%v", opUid, uidList, err)
        return list, err
    }

    sitSubCateType := uint32(virtual_image_resource.VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_SITTING_POSE)
    micPoseCfgMap := make(map[uint32]uint32)
    var maleDefaultSitCfgId, femaleDefaultSitCfgId uint32
    isMicScene := in.GetScope() == uint32(pb.VirtualImageDisplaySwitch_VIRTUAL_IMAGE_DISPLAY_SWITCH_MIC)

    if isMicScene {
        actCfgMapResp, err := m.resourceCli.GetActionResourceMap(ctx, &virtual_image_resource.GetActionResourceMapRequest{})
        if err != nil {
            log.WarnWithCtx(ctx, "BatchGetUserVirtualImageInuse fail to GetActivityCfgIdByType. err:%v", err)
        }
        micPoseCfgMap = actCfgMapResp.GetActionResourceMap()

        defaultResourceResp, err := m.resourceCli.GetDefaultResourceList(ctx, &virtual_image_resource.GetDefaultResourceListRequest{})
        if err != nil {
            log.WarnWithCtx(ctx, "BatchGetUserVirtualImageInuse fail to GetDefaultResourceList. err:%v", err)
        }

        maleDefaultSitCfgId = defaultResourceResp.GetMaleAnimationMap()[sitSubCateType]
        femaleDefaultSitCfgId = defaultResourceResp.GetFemaleAnimationMap()[sitSubCateType]
    }

    for _, userInuseInfo := range resp.GetUserInuseItemInfo() {
        if isMicScene && len(userInuseInfo.GetItems()) > 0 {
            hasSitItem := false
            for _, item := range userInuseInfo.GetItems() {
                if item.GetSubCategory() == sitSubCateType && item.GetCfgId() > 0 {
                    hasSitItem = true // 已佩戴坐姿动作
                }
            }

            if !hasSitItem {
                var defaultSitCfgId uint32
                userInfo := userMap[userInuseInfo.GetUid()]
                if userInfo.GetSex() == uint32(account.Male) {
                    defaultSitCfgId = maleDefaultSitCfgId
                } else {
                    defaultSitCfgId = femaleDefaultSitCfgId
                }

                // 未佩戴坐姿动作，添加默认坐姿动作
                userInuseInfo.Items = append(userInuseInfo.Items, &virtual_image_user.InuseItemInfo{
                    CfgId: defaultSitCfgId, SubCategory: sitSubCateType,
                })
            }
        }

        list = append(list, fillUserVirtualImageInuseInfo(userInuseInfo, micPoseCfgMap))
    }

    return list, nil
}

func fillUserVirtualImageInuseInfo(userInuseInfo *virtual_image_user.UserInuseItemInfo, micPoseCfgMap map[uint32]uint32) *pb.UserVirtualImageInuse {
    items := make([]*pb.UserVirtualImageItem, 0)
    for _, item := range userInuseInfo.GetItems() {
        cfgId := item.GetCfgId()
        if actCfgId, ok := micPoseCfgMap[item.GetCfgId()]; ok {
            cfgId = actCfgId
        }
        items = append(items, &pb.UserVirtualImageItem{
            ResourceId: cfgId,
            ExpireTs:   item.GetExpireTime(),
            Inuse:      true,
        })
    }

    return &pb.UserVirtualImageInuse{
        Uid:         userInuseInfo.GetUid(),
        Items:       items,
        Orientation: userInuseInfo.GetOrientation(),
    }

}

func getItemDisplayTime(ts, maxDisplayTime int64) int64 {
    if ts > maxDisplayTime {
        return 0
    }
    return ts
}

func fillUserVirtualImageItem(item *virtual_image_user.UserItemInfo, maxDisplayTime int64) *pb.UserVirtualImageItem {
    return &pb.UserVirtualImageItem{
        ResourceId: item.GetCfgId(),
        ExpireTs:   getItemDisplayTime(item.GetExpireTime(), maxDisplayTime),
        Inuse:      item.GetInUse(),
        UpdateTs:   item.GetUpdateTime(),
    }
}

func fillUserVirtualImageSuit(suit *virtual_image_user.UserSuitInfo,
    sub2CategoryInfo map[uint32]*virtual_image_resource.VirtualImageSubCategoryInfo, maxDisplayTime int64) (*pb.UserVirtualImageSuit, bool) {
    info := &pb.UserVirtualImageSuit{
        SuitId:              suit.GetSuitId(),
        Name:                suit.GetName(),
        Icon:                suit.GetSuitIcon(),
        ExpireTs:            getItemDisplayTime(suit.GetExpireTime(), maxDisplayTime),
        UpdateTs:            suit.GetUpdateTime(),
        LevelIcon:           suit.GetLevelIcon(),
        PromotionResourceId: suit.GetPromotionResourceId(),
    }

    // 排序, 椅子＞进房特效＞其他
    items := suit.GetItems()
    charSubCategory := uint32(virtual_image_resource.VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_CHAIR)
    roomSubCategory := uint32(virtual_image_resource.VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_ROOM_ENTRANCE_EFFECTS)
    sort.SliceStable(items, func(r, l int) bool {
        rSubCategory := items[r].GetSubCategory()
        lSubCategory := items[l].GetSubCategory()
        rCategory := sub2CategoryInfo[rSubCategory].GetParentCategory()
        lCategory := sub2CategoryInfo[lSubCategory].GetParentCategory()

        if rSubCategory == charSubCategory && lSubCategory != charSubCategory {
            return true
        }
        if rSubCategory != charSubCategory && lSubCategory == charSubCategory {
            return false
        }

        if rSubCategory == roomSubCategory && lSubCategory != roomSubCategory {
            return true
        }
        if rSubCategory != roomSubCategory && lSubCategory == roomSubCategory {
            return false
        }

        if rCategory < lCategory {
            return true
        }
        if rCategory > lCategory {
            return false
        }
        return rSubCategory < lSubCategory
    })

    inuse := true
    resourceIdList := make([]uint32, 0)
    for _, item := range items {
        resourceIdList = append(resourceIdList, item.GetCfgId())
        info.Items = append(info.Items, &pb.UserVirtualImageItem{
            ResourceId: item.GetCfgId(),
            ExpireTs:   getItemDisplayTime(item.GetExpireTime(), maxDisplayTime),
            Inuse:      item.GetInUse(),
            UpdateTs:   item.GetUpdateTime(),
        })
        if !item.GetInUse() {
            inuse = false
        }
    }

    idStr := util.GetResourceIdStr(resourceIdList)
    info.SuitUniqueId = util.GetMd5Str(idStr)

    return info, inuse
}

func needUKWCheck(scope uint32) bool {
    switch scope {
    case uint32(pb.VirtualImageDisplaySwitch_VIRTUAL_IMAGE_DISPLAY_SWITCH_PROFILE_CARD):
        return true
    case uint32(pb.VirtualImageDisplaySwitch_VIRTUAL_IMAGE_DISPLAY_SWITCH_MIC):
        return true
    case uint32(pb.VirtualImageDisplaySwitch_VIRTUAL_IMAGE_DISPLAY_SWITCH_ROOM_ENTER_EFFECT):
        return true
    default:
        return false
    }
}

func isUKWUser(userProfile *app.UserProfile) bool {
    return userProfile.GetPrivilege().GetType() == uint32(app.EUserPrivilegeType_ENUM_USER_PRIVILEGE_UKW)
}

func (m *VirtualImageLogicMgr) GetUserVirtualImageDisplay(ctx context.Context, opUid uint32, in *pb.GetUserVirtualImageDisplayRequest) (*pb.GetUserVirtualImageDisplayResponse, error) {
    out := &pb.GetUserVirtualImageDisplayResponse{}

    displayData, err := m.vUserClient.GetUserDisplayData(ctx, &virtual_image_user.GetUserDisplayDataReq{
        Uid: in.GetTargetUid(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUserVirtualImageDisplay fail to GetUserDisplayData. req:%+v, err:%v", in, err)
        return out, err
    }

    if opUid != in.GetTargetUid() {
        // 非本人查看时，需根据场景开关屏蔽数据
        switchMap := make(map[uint32]bool)
        for _, switchInfo := range displayData.GetUserDisplaySwitch() {
            switchMap[switchInfo.GetType()] = switchInfo.GetSwitchOn()
        }

        if switchOn, ok := switchMap[in.GetScope()]; !ok || !switchOn {
            // 该场景开关未开启，不返回外显数据
            return out, nil
        }
    }

    userInuse := displayData.GetUserInuseItemInfo()
    if userInuse.GetUid() == 0 || len(userInuse.GetItems()) == 0 {
        // 用户未佩戴物品
        return out, nil
    }

    userInfo, err := m.userProfileCli.GetUserProfileV2(ctx, in.GetTargetUid(), needUKWCheck(in.GetScope()))
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUserVirtualImageDisplay fail to GetUserProfileV2. req:%+v, err:%v", in, err)
        return out, err
    }

    if isUKWUser(userInfo) {
        // 神秘人不返回虚拟形象信息
        return out, nil
    }

    out.UserProfile = userInfo
    out.InuseInfo = fillUserVirtualImageInuseInfo(userInuse, nil)

    // 填充姿势信息
    for _, item := range displayData.GetPoseInfo() {
        if item.GetScene() == in.GetScope() {
            out.InuseInfo.PoseType = item.GetPoseType()
            break
        }
    }

    if len(displayData.GetUserDisplaySwitch()) > 0 {
        out.SwitchInfo = make([]*pb.VirtualImageDisplaySwitchInfo, 0, len(displayData.GetUserDisplaySwitch()))
        for _, switchInfo := range displayData.GetUserDisplaySwitch() {
            out.SwitchInfo = append(out.SwitchInfo, &pb.VirtualImageDisplaySwitchInfo{
                SwitchType:   switchInfo.GetType(),
                SwitchStatus: switchInfo.GetSwitchOn(),
            })
        }
    }

    relationInuse := displayData.GetRelationUserInuseItemInfo()
    if relationInuse.GetUid() != 0 {
        out.RelationInuseInfo = fillUserVirtualImageInuseInfo(relationInuse, nil)

        out.RelationUserProfile, err = m.userProfileCli.GetUserProfileV2(ctx, relationInuse.GetUid(), false)
        if err != nil {
            log.WarnWithCtx(ctx, "GetUserVirtualImageDisplay fail to GetUserProfileV2. req:%+v, err:%v", in, err)
        }
    }

    return out, nil
}

func (m *VirtualImageLogicMgr) SetUserVirtualImageOrientation(ctx context.Context, opUid, orientation uint32) error {
    _, err := m.vUserClient.SetUserVirtualImageOrientation(ctx, &virtual_image_user.SetUserVirtualImageOrientationReq{
        Uid:         opUid,
        Orientation: orientation,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "SetUserVirtualImageOrientation fail to SetUserVirtualImageOrientation. opUid:%d, orientation:%d, err:%v",
            opUid, orientation, err)
        return err
    }

    return nil
}

func (m *VirtualImageLogicMgr) GetUserVirtualImagePose(ctx context.Context, uid uint32, clientType uint16) (*pb.GetUserVirtualImagePoseResponse, error) {
    sitPoseSubCategory := m.dyconfig.GetSitSubCategory()

    resp := &pb.GetUserVirtualImagePoseResponse{
        Items:       make([]*pb.UserVirtualImageItem, 0),
        JumpUrl:     m.dyconfig.GetSitJumpUrl(),
        SubCategory: sitPoseSubCategory,
    }

    userItemResp, err := m.vUserClient.GetUserVirtualImageList(ctx, &virtual_image_user.GetUserVirtualImageListReq{Uid: uid})
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUserVirtualImagePose fail to GetUserVirtualImageList. uid:%d, err:%v", uid, err)
        return resp, err
    }

    existPoseMap := make(map[uint32]bool)
    for _, item := range userItemResp.GetItems() {
        if item.GetSubCategory() != sitPoseSubCategory {
            continue
        }

        resp.Items = append(resp.Items, fillUserVirtualImageItem(item, 0))
        existPoseMap[item.GetCfgId()] = true
    }

    resp.Orientation = userItemResp.GetOrientation()

    // 如果用户没有无限换装卡权益，直接返回

    userInfo, serr := m.accountCli.GetUserByUid(ctx, uid)
    if serr != nil {
        log.ErrorWithCtx(ctx, "GetUserVirtualImagePose uid:%v GetUserByUid err:%v", uid, serr)
    }

    resourceSexList := make([]uint32, 0)
    // 1: 男 2: 女 3: 通用
    resourceSexList = append(resourceSexList, uint32(virtual_image_resource.VirtualImageResourceSex_VIRTUAL_IMAGE_RESOURCE_SEX_COMMON))
    if userInfo.GetSex() == account.Male {
        resourceSexList = append(resourceSexList, uint32(virtual_image_resource.VirtualImageResourceSex_VIRTUAL_IMAGE_RESOURCE_SEX_MALE))
    }
    if userInfo.GetSex() == account.Female {
        resourceSexList = append(resourceSexList, uint32(virtual_image_resource.VirtualImageResourceSex_VIRTUAL_IMAGE_RESOURCE_SEX_FEMALE))
    }

    // 拉取权益类的坐姿动作商品
    commodityResp, err := m.mallClient.GetCommodityDataList(ctx, &mallPb.GetCommodityDataListRequest{
        SubCategory:     sitPoseSubCategory,
        ShelfStatus:     uint32(mallPb.ShelfStatus_SHELF_STATUS_NOW),
        ResourceSexList: resourceSexList,
        GainPath:        uint32(mallPb.CommodityGainPath_COMMODITY_GAIN_PATH_INFINITE_CHANGE_CARD),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUserVirtualImagePose fail to GetCommodityDataList. uid:%d, err:%v", uid, err)
        return resp, err
    }

    var inusePoseId uint32
    for _, info := range userItemResp.GetInuseItems() {
        if info.GetSubCategory() == sitPoseSubCategory {
            inusePoseId = info.GetCfgId()
        }
    }

    for _, commodity := range commodityResp.GetCommodityDataList() {
        if len(commodity.GetResourceIdList()) != 1 {
            continue
        }

        poseId := commodity.GetResourceIdList()[0]
        if _, ok := existPoseMap[poseId]; ok {
            continue
        }
        existPoseMap[poseId] = true

        item := &pb.UserVirtualImageItem{
            ResourceId:    poseId,
            UseRightsType: uint32(pb.VirtualImageRightsType_VIRTUAL_IMAGE_RIGHTS_TYPE_INFINITE_CHANGE_CARD),
        }
        if poseId == inusePoseId {
            item.Inuse = true
        }

        resp.Items = append(resp.Items, item)
    }

    return resp, nil
}

func (m *VirtualImageLogicMgr) checkIfInWedding(ctx context.Context, uid uint32) (bool, error) {
    resp, err := m.weddingCli.GetUserChannelWeddingInfo(ctx, &channel_wedding.GetUserChannelWeddingInfoReq{Uid: uid})
    if err != nil {
        log.ErrorWithCtx(ctx, "checkIfInWedding fail to GetUserChannelWeddingInfo. uid:%d, err:%v", uid, err)
        return false, err
    }

    weddingInfo := resp.GetWeddingInfo()
    if weddingInfo.GetWeddingId() > 0 && weddingInfo.GetStageInfo().GetCurrStage() > 0 {
        return true, nil
    }

    return false, nil
}
