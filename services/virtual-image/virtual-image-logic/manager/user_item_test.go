package manager

import (
    "context"
    "fmt"
    channel_wedding "golang.52tt.com/protocol/services/channel-wedding"
    virtual_image_card "golang.52tt.com/protocol/services/virtual-image-card"
    virtual_image_mall "golang.52tt.com/protocol/services/virtual-image-mall"
    virtual_image_resource "golang.52tt.com/protocol/services/virtual-image-resource"
    "reflect"
    "testing"
    "time"

    "github.com/golang/mock/gomock"
    pb "golang.52tt.com/protocol/app/virtual_image_logic"
    virtual_image_user "golang.52tt.com/protocol/services/virtual-image-user"
)

func TestVirtualImageLogicMgr_GetUserVirtualImage(t *testing.T) {
    initTestMgr(t)
    ctx := context.Background()
    uid := uint32(12345)
    clientType := uint16(1)

    tests := []struct {
        name           string
        mockSetup      func()
        expectedResult *pb.GetUserVirtualImageResponse
        expectedErr    error
    }{
        {
            name: "success - user has virtual image data",
            mockSetup: func() {
                vUserClient.EXPECT().
                    GetUserVirtualImageList(ctx, gomock.Any()).
                    Return(&virtual_image_user.GetUserVirtualImageListResp{
                        InuseItems: []*virtual_image_user.InuseItemInfo{
                            {CfgId: 1, UseRightsType: 1, ExpireTime: time.Now().Add(24 * time.Hour).Unix()},
                        },
                        Items: []*virtual_image_user.UserItemInfo{
                            {CfgId: 2, SubCategory: 10, UpdateTime: time.Now().Unix()},
                        },
                        Suits: []*virtual_image_user.UserSuitInfo{
                            {SuitId: 3, Items: make([]*virtual_image_user.UserItemInfo, 1)},
                        },
                        Orientation: 1,
                    }, nil)
            },
            expectedResult: &pb.GetUserVirtualImageResponse{
                ItemTabs:         []*pb.VirtualImageItemTab{},
                InuseRightsItems: []*pb.UserVirtualImageItem{{ResourceId: 1, Inuse: true, UseRightsType: 1}},
                InuseResourceIds: []uint32{1},
                LastUpdateTs:     0,
            },
            expectedErr: nil,
        },
        {
            name: "success - user has no virtual image data",
            mockSetup: func() {
                vUserClient.EXPECT().
                    GetUserVirtualImageList(ctx, gomock.Any()).
                    Return(&virtual_image_user.GetUserVirtualImageListResp{
                        InuseItems:  []*virtual_image_user.InuseItemInfo{},
                        Items:       []*virtual_image_user.UserItemInfo{},
                        Orientation: 0,
                    }, nil)
            },
            expectedResult: &pb.GetUserVirtualImageResponse{
                ItemTabs:         []*pb.VirtualImageItemTab{},
                InuseRightsItems: []*pb.UserVirtualImageItem{},
                InuseResourceIds: []uint32{},
            },
            expectedErr: nil,
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            tt.mockSetup()

            _, _, err := testMgr.GetUserVirtualImage(ctx, uid, clientType)
            if err != nil {
                t.Errorf("expected error: %v, got: %v", tt.expectedErr, err)
            }
        })
    }
}

func TestVirtualImageLogicMgr_SetUserVirtualImageInuse(t *testing.T) {
    initTestMgr(t)
    ctx := context.Background()
    uid := uint32(12345)
    items := []*pb.SetUseItem{
        {ResourceId: 1, RightsType: 1},
    }

    tests := []struct {
        name        string
        mockSetup   func()
        expectedErr error
    }{
        {
            name: "success",
            mockSetup: func() {
                vUserClient.EXPECT().
                    SetUserVirtualImageInUse(ctx, gomock.Any()).
                    Return(nil, nil)
                cardCli.EXPECT().
                    GetUserCardInfo(ctx, gomock.Any()).Return(&virtual_image_card.GetUserCardInfoResp{
                    Card: &virtual_image_card.UserCardInfo{
                        ExpireTs: time.Now().Unix() + 60,
                    },
                }, nil)
                mallCli.EXPECT().
                    GetCommodityDataList(ctx, gomock.Any()).Return(&virtual_image_mall.GetCommodityDataListResponse{
                    CommodityDataList: []*virtual_image_mall.CommodityData{
                        {ResourceIdList: []uint32{1}},
                    },
                }, nil)
                resourceCli.EXPECT().
                    GetVirtualImageResourcesByIds(ctx, gomock.Any()).Return(&virtual_image_resource.GetVirtualImageResourcesByIdsResponse{
                    Resources: []*virtual_image_resource.VirtualImageResourceInfo{
                        {Id: 1},
                    }}, nil)
            },
            expectedErr: nil,
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            tt.mockSetup()

            err := testMgr.SetUserVirtualImageInuse(ctx, uid, items, true)
            if !reflect.DeepEqual(err, tt.expectedErr) {
                t.Errorf("expected error: %v, got: %v", tt.expectedErr, err)
            }
        })
    }
}

func TestVirtualImageLogicMgr_BatchGetUserVirtualImageInuse(t *testing.T) {
    initTestMgr(t)
    ctx := context.Background()
    type args struct {
        ctx context.Context
        in  *pb.BatchGetUserVirtualImageInuseRequest
    }
    tests := []struct {
        name     string
        initFunc func()
        args     args
        wantErr  bool
    }{
        {
            name: "success",
            args: args{
                ctx: ctx,
                in: &pb.BatchGetUserVirtualImageInuseRequest{
                    UidList: []uint32{testUid},
                    Scope:   0,
                },
            },
            initFunc: func() {
                userProfileCli.EXPECT().BatchGetUserProfileV2(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
                vUserClient.EXPECT().BatchGetUserInuseItemInfo(gomock.Any(), gomock.Any()).Return(&virtual_image_user.BatchGetUserInuseItemInfoResp{
                    UserInuseItemInfo: []*virtual_image_user.UserInuseItemInfo{
                        {
                            Uid: testUid,
                            Items: []*virtual_image_user.InuseItemInfo{
                                {CfgId: 1, SubCategory: 10},
                            },
                        },
                    },
                }, nil)
            },
            wantErr: false,
        },
        {
            name: "error - BatchGetUserInuseItemInfo fails",
            args: args{
                ctx: ctx,
                in: &pb.BatchGetUserVirtualImageInuseRequest{
                    UidList: []uint32{testUid},
                    Scope:   0,
                },
            },
            initFunc: func() {
                userProfileCli.EXPECT().BatchGetUserProfileV2(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
                vUserClient.EXPECT().BatchGetUserInuseItemInfo(gomock.Any(), gomock.Any()).Return(nil, fmt.Errorf("internal error"))
            },
            wantErr: true,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            if tt.initFunc != nil {
                tt.initFunc()
            }
            _, err := testMgr.BatchGetUserVirtualImageInuse(tt.args.ctx, testUid, tt.args.in)
            if (err != nil) != tt.wantErr {
                t.Errorf("BatchGetUserVirtualImageInuse() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
        })
    }
}

func TestVirtualImageLogicMgr_GetUserVirtualImageDisplay(t *testing.T) {
    initTestMgr(t)
    type args struct {
        ctx context.Context
        in  *pb.GetUserVirtualImageDisplayRequest
    }
    tests := []struct {
        name     string
        initFunc func()
        args     args
        wantErr  bool
    }{
        {
            name: "success",
            args: args{
                ctx: ctx,
                in: &pb.GetUserVirtualImageDisplayRequest{
                    TargetUid: testUid,
                },
            },
            initFunc: func() {
                vUserClient.EXPECT().GetUserDisplayData(gomock.Any(), gomock.Any(), gomock.Any()).Return(&virtual_image_user.GetUserDisplayDataResp{}, nil)
                userProfileCli.EXPECT().GetUserProfileV2(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
            },
            wantErr: false,
        },
        {
            name: "error - GetUserRelationInUse fails",
            args: args{
                ctx: ctx,
                in: &pb.GetUserVirtualImageDisplayRequest{
                    TargetUid: testUid,
                },
            },
            initFunc: func() {
                vUserClient.EXPECT().GetUserDisplayData(gomock.Any(), gomock.Any(), gomock.Any()).Return(&virtual_image_user.GetUserDisplayDataResp{}, fmt.Errorf("internal error"))
            },
            wantErr: true,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := testMgr
            if tt.initFunc != nil {
                tt.initFunc()
            }
            _, err := m.GetUserVirtualImageDisplay(tt.args.ctx, testUid, tt.args.in)
            if (err != nil) != tt.wantErr {
                t.Errorf("GetUserVirtualImageDisplay() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
        })
    }
}

func TestVirtualImageLogicMgr_SetUserVirtualImageOrientation(t *testing.T) {
    initTestMgr(t)
    type args struct {
        ctx context.Context
    }
    tests := []struct {
        name     string
        initFunc func()
        args     args
        wantErr  bool
    }{
        {
            name: "success",
            args: args{
                ctx: ctx,
            },
            initFunc: func() {
                vUserClient.EXPECT().SetUserVirtualImageOrientation(gomock.Any(), gomock.Any()).Return(nil, nil)
            },
            wantErr: false,
        },
        {
            name: "error - SetUserVirtualImageOrientation fails",
            args: args{
                ctx: ctx,
            },
            initFunc: func() {
                vUserClient.EXPECT().SetUserVirtualImageOrientation(gomock.Any(), gomock.Any()).Return(nil, fmt.Errorf("internal error"))
            },
            wantErr: true,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := testMgr
            if tt.initFunc != nil {
                tt.initFunc()
            }
            err := m.SetUserVirtualImageOrientation(tt.args.ctx, testUid, 1)
            if (err != nil) != tt.wantErr {
                t.Errorf("SetUserVirtualImageOrientation() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
        })
    }
}

func TestVirtualImageLogicMgr_GetUserVirtualImagePose(t *testing.T) {
    initTestMgr(t)
    type args struct {
        ctx context.Context
    }
    tests := []struct {
        name     string
        initFunc func()
        args     args
        wantErr  bool
    }{
        {
            name: "success",
            args: args{
                ctx: ctx,
            },
            initFunc: func() {
                vUserClient.EXPECT().GetUserVirtualImageList(gomock.Any(), gomock.Any()).Return(&virtual_image_user.GetUserVirtualImageListResp{
                    Items: make([]*virtual_image_user.UserItemInfo, 1),
                }, nil)
                accountCli.EXPECT().GetUserByUid(gomock.Any(), gomock.Any()).Return(nil, nil)
                mallCli.EXPECT().GetCommodityDataList(gomock.Any(), gomock.Any()).Return(&virtual_image_mall.GetCommodityDataListResponse{
                    CommodityDataList: []*virtual_image_mall.CommodityData{
                        {ResourceIdList: []uint32{1}},
                    },
                }, nil)
            },
            wantErr: false,
        },
        {
            name: "error - GetUserVirtualImagePose fails",
            args: args{
                ctx: ctx,
            },
            initFunc: func() {
                vUserClient.EXPECT().GetUserVirtualImageList(gomock.Any(), gomock.Any()).Return(nil, fmt.Errorf("internal error"))
            },
            wantErr: true,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := testMgr
            if tt.initFunc != nil {
                tt.initFunc()
            }
            _, err := m.GetUserVirtualImagePose(tt.args.ctx, testUid, 0)
            if (err != nil) != tt.wantErr {
                t.Errorf("GetUserVirtualImagePose() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
        })
    }
}

func TestVirtualImageLogicMgr_checkIfInWedding(t *testing.T) {
    initTestMgr(t)
    type args struct {
        ctx context.Context
    }
    tests := []struct {
        name     string
        initFunc func()
        args     args
        wantErr  bool
    }{
        {
            name: "success",
            args: args{
                ctx: ctx,
            },
            initFunc: func() {
                channelWeddingCli.EXPECT().GetUserChannelWeddingInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(&channel_wedding.GetUserChannelWeddingInfoResp{}, nil)
            },
            wantErr: false,
        },
        {
            name: "error - GetUserDisplayData fails",
            args: args{
                ctx: ctx,
            },
            initFunc: func() {
                channelWeddingCli.EXPECT().GetUserChannelWeddingInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(&channel_wedding.GetUserChannelWeddingInfoResp{}, fmt.Errorf("internal error"))
            },
            wantErr: true,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := testMgr
            if tt.initFunc != nil {
                tt.initFunc()
            }
            _, err := m.checkIfInWedding(tt.args.ctx, 1)
            if (err != nil) != tt.wantErr {
                t.Errorf("checkIfInWedding() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
        })
    }
}

func TestVirtualImageLogicMgr_getDiffItemSubCategory(t *testing.T) {
    initTestMgr(t)
    type args struct {
        ctx context.Context
    }
    tests := []struct {
        name     string
        initFunc func()
        args     args
        wantErr  bool
    }{
        {
            name: "success",
            args: args{
                ctx: ctx,
            },
            initFunc: func() {
                vUserClient.EXPECT().BatchGetUserInuseItemInfo(gomock.Any(), gomock.Any()).Return(&virtual_image_user.BatchGetUserInuseItemInfoResp{
                    UserInuseItemInfo:  []*virtual_image_user.UserInuseItemInfo{
                        {Uid: testUid, Items: []*virtual_image_user.InuseItemInfo{{CfgId: 1, SubCategory: 10},{CfgId: 2, SubCategory: 20}}},
                    },
                }, nil)
            },
            wantErr: false,
        },
        {
            name: "error - getDiffItemSubCategory fails",
            args: args{
                ctx: ctx,
            },
            initFunc: func() {
                vUserClient.EXPECT().BatchGetUserInuseItemInfo(gomock.Any(), gomock.Any()).Return(nil, fmt.Errorf("internal error"))
            },
            wantErr: true,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := testMgr
            if tt.initFunc != nil {
                tt.initFunc()
            }
            _, err := m.getDiffItemSubCategory(tt.args.ctx, testUid, []*virtual_image_user.ItemInfo{{CfgId: 2, SubCategory: 20}})
            if (err != nil) != tt.wantErr {
                t.Errorf("getDiffItemSubCategory() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
        })
    }
}

func TestVirtualImageLogicMgr_GetUserDisplaySwitchList(t *testing.T) {
    initTestMgr(t)
    type args struct {
        ctx context.Context
    }
    tests := []struct {
        name     string
        initFunc func()
        args     args
        wantErr  bool
    }{
        {
            name: "success",
            args: args{
                ctx: ctx,
            },
            initFunc: func() {
                vUserClient.EXPECT().GetUserDisplaySwitch(gomock.Any(), gomock.Any()).Return(&virtual_image_user.GetUserDisplaySwitchResponse{
                    DisplaySwitch: []*virtual_image_user.DisplaySwitch{
                        {Type: uint32(pb.VirtualImageDisplaySwitch_VIRTUAL_IMAGE_DISPLAY_SWITCH_MAIN), SwitchOn: true},
                    },
                }, nil)
            },
            wantErr: false,
        },
        {
            name: "error - GetUserDisplaySwitch fails",
            args: args{
                ctx: ctx,
            },
            initFunc: func() {
                vUserClient.EXPECT().GetUserDisplaySwitch(gomock.Any(), gomock.Any()).Return(nil, fmt.Errorf("internal error"))
            },
            wantErr: true,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := testMgr
            if tt.initFunc != nil {
                tt.initFunc()
            }
            _, err := m.GetUserDisplaySwitchList(tt.args.ctx, testUid)
            if (err != nil) != tt.wantErr {
                t.Errorf("GetUserDisplaySwitchList() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
        })
    }
}

func TestVirtualImageLogicMgr_SetUserDisplaySwitch(t *testing.T) {
    initTestMgr(t)
    type args struct {
        ctx context.Context
    }
    tests := []struct {
        name     string
        initFunc func()
        args     args
        wantErr  bool
    }{
        {
            name: "success",
            args: args{
                ctx: ctx,
            },
            initFunc: func() {
                channelWeddingCli.EXPECT().GetUserChannelWeddingInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(&channel_wedding.GetUserChannelWeddingInfoResp{}, nil)
                vUserClient.EXPECT().SetUserDisplaySwitch(gomock.Any(), gomock.Any()).Return(nil, nil)
            },
            wantErr: false,
        },
        {
            name: "error - in wedding",
            args: args{
                ctx: ctx,
            },
            initFunc: func() {
                channelWeddingCli.EXPECT().GetUserChannelWeddingInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(&channel_wedding.GetUserChannelWeddingInfoResp{
                    WeddingInfo: &channel_wedding.WeddingInfo{
                        Groom: &channel_wedding.WeddingCpMemInfo{Uid: testUid},
                    },
                }, nil)
            },
            wantErr: true,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := testMgr
            if tt.initFunc != nil {
                tt.initFunc()
            }
            err := m.SetUserDisplaySwitch(tt.args.ctx, testUid, 1, false)
            if (err != nil) != tt.wantErr {
                t.Errorf("SetUserDisplaySwitch() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
        })
    }
}

func TestVirtualImageLogicMgr_SetVirtualImagePoseType(t *testing.T) {
    initTestMgr(t)
    type args struct {
        ctx context.Context
    }
    tests := []struct {
        name     string
        initFunc func()
        args     args
        wantErr  bool
    }{
        {
            name: "success",
            args: args{
                ctx: ctx,
            },
            initFunc: func() {
                vUserClient.EXPECT().SetVirtualImagePoseType(gomock.Any(), gomock.Any()).Return(nil, nil)
            },
            wantErr: false,
        },
        {
            name: "error - SetVirtualImagePoseType fails",
            args: args{
                ctx: ctx,
            },
            initFunc: func() {
                vUserClient.EXPECT().SetVirtualImagePoseType(gomock.Any(), gomock.Any()).Return(nil, fmt.Errorf("internal error"))
            },
            wantErr: true,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := testMgr
            if tt.initFunc != nil {
                tt.initFunc()
            }
            err := m.SetVirtualImagePoseType(tt.args.ctx, testUid, 1, 1)
            if (err != nil) != tt.wantErr {
                t.Errorf("SetVirtualImagePoseType() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
        })
    }
}