package manager

import (
    "context"
    "fmt"
    "github.com/golang/mock/gomock"
    virtual_image_card "golang.52tt.com/protocol/services/virtual-image-card"
    "testing"
)

func TestVirtualImageLogicMgr_GetVirtualImageCardEntryStatus(t *testing.T) {
    initTestMgr(t)
    type args struct {
        ctx context.Context
        uid uint32
        cid uint32
    }
    tests := []struct {
        name     string
        initFunc func()
        args     args
        wantErr  bool
    }{
        {
            name: "success",
            args: args{
                ctx: ctx,
                uid: 1,
                cid: 2,
            },
            initFunc: func() {
                cardCli.EXPECT().GetVirtualImageCardEntryStatus(gomock.Any(), gomock.Any()).Return(nil, nil)
            },
            wantErr: false,
        },
        {
            name: "error - GetVirtualImageCardEntryStatus fails",
            args: args{
                ctx: ctx,
                uid: 1,
                cid: 2,
            },
            initFunc: func() {
                cardCli.EXPECT().GetVirtualImageCardEntryStatus(gomock.Any(), gomock.Any()).Return(nil, fmt.Errorf("internal error"))
            },
            wantErr: true,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := testMgr
            if tt.initFunc != nil {
                tt.initFunc()
            }
            _, err := m.GetVirtualImageCardEntryStatus(tt.args.ctx, tt.args.uid)
            if (err != nil) != tt.wantErr {
                t.Errorf("CheckChannelEntrance() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
        })
    }
}

func TestVirtualImageLogicMgr_GetVirtualImageCardCommonCfg(t *testing.T) {
    initTestMgr(t)
    type args struct {
        ctx context.Context
    }
    tests := []struct {
        name     string
        initFunc func()
        args     args
        wantErr  bool
    }{
        {
            name: "success",
            args: args{
                ctx: ctx,
            },
            initFunc: func() {
                cardCli.EXPECT().GetVirtualImageCardCommonCfg(gomock.Any(), gomock.Any()).Return(&virtual_image_card.GetVirtualImageCardCommonCfgResponse{
                    AboutToExpireCfgList: []*virtual_image_card.AboutToExpireCfg{
                        {Icon: ""},
                    },
                }, nil)
            },
            wantErr: false,
        },
        {
            name: "error - GetVirtualImageCardCommonCfg fails",
            args: args{
                ctx: ctx,
            },
            initFunc: func() {
                cardCli.EXPECT().GetVirtualImageCardCommonCfg(gomock.Any(), gomock.Any()).Return(nil, fmt.Errorf("internal error"))
            },
            wantErr: true,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := testMgr
            if tt.initFunc != nil {
                tt.initFunc()
            }
            _, err := m.GetVirtualImageCardCommonCfg(tt.args.ctx)
            if (err != nil) != tt.wantErr {
                t.Errorf("GetVirtualImageCardCommonCfg() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
        })
    }
}

func TestVirtualImageLogicMgr_GetVirtualImageCardEntry(t *testing.T) {
    initTestMgr(t)
    type args struct {
        ctx context.Context
        uid uint32
        cid uint32
    }
    tests := []struct {
        name     string
        initFunc func()
        args     args
        wantErr  bool
    }{
        {
            name: "success",
            args: args{
                ctx: ctx,
                uid: 1,
                cid: 2,
            },
            initFunc: func() {
                cardCli.EXPECT().GetVirtualImageCardEntryStatus(gomock.Any(), gomock.Any()).Return(nil, nil)
            },
            wantErr: false,
        },
        {
            name: "error - GetVirtualImageCardEntry fails",
            args: args{
                ctx: ctx,
                uid: 1,
                cid: 2,
            },
            initFunc: func() {
                cardCli.EXPECT().GetVirtualImageCardEntryStatus(gomock.Any(), gomock.Any()).Return(nil, fmt.Errorf("internal error"))
            },
            wantErr: true,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := testMgr
            if tt.initFunc != nil {
                tt.initFunc()
            }
            _, err := m.GetVirtualImageCardEntry(tt.args.ctx, tt.args.uid)
            if (err != nil) != tt.wantErr {
                t.Errorf("GetVirtualImageCardEntry() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
        })
    }
}