package manager

import (
    "context"
    "fmt"
    "github.com/golang/mock/gomock"
    mockaccount "golang.52tt.com/clients/mocks/account"
    mocks_presence_v2 "golang.52tt.com/clients/mocks/presence/v2"
    mocks_superPlayer "golang.52tt.com/clients/mocks/super-player-privilege"
    mocks_friendship "golang.52tt.com/clients/mocks/ugc/friendship"
    mocks_userBlackListService "golang.52tt.com/clients/mocks/user-black-list"
    mocks_profile "golang.52tt.com/clients/mocks/user-profile-api"
    "golang.52tt.com/protocol/app"
    pb "golang.52tt.com/protocol/app/virtual_image_logic"
    channel_go "golang.52tt.com/protocol/services/channel-go"
    channel_wedding "golang.52tt.com/protocol/services/channel-wedding"
    Presence "golang.52tt.com/protocol/services/presencesvr"
    super_player_privilege "golang.52tt.com/protocol/services/super-player-privilege"
    friendshipPb "golang.52tt.com/protocol/services/ugc/friendship"
    virtual_image_card "golang.52tt.com/protocol/services/virtual-image-card"
    virtual_image_mall "golang.52tt.com/protocol/services/virtual-image-mall"
    virtual_image_resource "golang.52tt.com/protocol/services/virtual-image-resource"
    virtual_image_user "golang.52tt.com/protocol/services/virtual-image-user"
    "golang.52tt.com/services/virtual-image/virtual-image-logic/cache"
    "golang.52tt.com/services/virtual-image/virtual-image-logic/conf"
    "reflect"
    "testing"
    "time"
)

var (
	testMgr *VirtualImageLogicMgr

	ctx           = context.Background()
	testUid       = uint32(1)
	testTargetUid = uint32(2)

	vUserClient    *virtual_image_user.MockVirtualImageUserClient
	userProfileCli *mocks_profile.MockIClient
	friendCli      *mocks_friendship.MockIClient
	presenceV2Cli  *mocks_presence_v2.MockIClient
	userBlackCli   *mocks_userBlackListService.MockIClient
	superPlayerCli *mocks_superPlayer.MockIClient
    accountCli     *mockaccount.MockIClient
    resourceCli *virtual_image_resource.MockVirtualImageResourceClient
    cardCli *virtual_image_card.MockVirtualImageCardClient
    channelWeddingCli *channel_wedding.MockChannelWeddingClient
    mallCli *virtual_image_mall.MockVirtualImageMallClient
    channelCli *channel_go.MockChannelGoClient
)

func initTestMgr(t *testing.T) {
	ctrl := gomock.NewController(t)

	vUserClient = virtual_image_user.NewMockVirtualImageUserClient(ctrl)
	userProfileCli = mocks_profile.NewMockIClient(ctrl)
	friendCli = mocks_friendship.NewMockIClient(ctrl)
	presenceV2Cli = mocks_presence_v2.NewMockIClient(ctrl)
	userBlackCli = mocks_userBlackListService.NewMockIClient(ctrl)
	superPlayerCli = mocks_superPlayer.NewMockIClient(ctrl)
    accountCli = mockaccount.NewMockIClient(ctrl)

    localCache := cache.NewLocalCache()
    resourceCli = virtual_image_resource.NewMockVirtualImageResourceClient(ctrl)
    cardCli = virtual_image_card.NewMockVirtualImageCardClient(ctrl)
    channelWeddingCli = channel_wedding.NewMockChannelWeddingClient(ctrl)
    mallCli = virtual_image_mall.NewMockVirtualImageMallClient(ctrl)
    channelCli = channel_go.NewMockChannelGoClient(ctrl)

	testMgr = &VirtualImageLogicMgr{
        dyconfig:       conf.NewConfigHandler(""),
		vUserClient:    vUserClient,
		userProfileCli: userProfileCli,
		friendCli:      friendCli,
		presenceV2Cli:  presenceV2Cli,
		userBlackCli:   userBlackCli,
		superPlayerCli: superPlayerCli,
        accountCli:     accountCli,
        localCache:     localCache,
        resourceCli:    resourceCli,
        cardCli:        cardCli,
        weddingCli: channelWeddingCli,
        mallClient:        mallCli,
        channelCli:     channelCli,
	}
}

func TestVirtualImageLogicMgr_AcceptBindInvite(t *testing.T) {
	initTestMgr(t)
	now := time.Now()
	type args struct {
		ctx      context.Context
		uid      uint32
		inviteId string
	}
	tests := []struct {
		name     string
		initFunc func()
		args     args
		wantErr  bool
	}{
		{
			name: "success",
			args: args{
				ctx:      ctx,
				uid:      testTargetUid,
				inviteId: fmt.Sprintf("%d_%d_%d", testUid, testTargetUid, now.Unix()),
			},
			initFunc: func() {
				friendCli.EXPECT().GetOneFriendInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(&friendshipPb.FriendInfo{
					FriendUid: testTargetUid,
					IsDelete:  false,
				}, nil)
				vUserClient.EXPECT().AcceptBindInvite(gomock.Any(), gomock.Any()).Return(&virtual_image_user.AcceptBindInviteResponse{}, nil)
			},
			wantErr: false,
		},
		{
			name: "invalid inviteId",
			args: args{
				ctx:      ctx,
				uid:      testTargetUid,
				inviteId: "invalid_invite_id",
			},
			wantErr: true,
		},
		{
			name: "not friend",
			args: args{
				ctx:      ctx,
				uid:      testTargetUid,
				inviteId: fmt.Sprintf("%d_%d_%d", testUid, testTargetUid, now.Unix()),
			},
			initFunc: func() {
				friendCli.EXPECT().GetOneFriendInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(&friendshipPb.FriendInfo{
					FriendUid: testTargetUid,
					IsDelete:  true,
				}, nil)
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := testMgr
			if tt.initFunc != nil {
				tt.initFunc()
			}
			if err := m.AcceptBindInvite(tt.args.ctx, tt.args.uid, tt.args.inviteId); (err != nil) != tt.wantErr {
				t.Errorf("AcceptBindInvite() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestVirtualImageLogicMgr_CancelBindInvite(t *testing.T) {
	initTestMgr(t)
	type args struct {
		ctx       context.Context
		uid       uint32
		targetUid uint32
	}
	tests := []struct {
		name     string
		initFunc func()
		args     args
		wantErr  bool
	}{
		{
			name: "success",
			args: args{
				ctx:       ctx,
				uid:       testUid,
				targetUid: testTargetUid,
			},
			initFunc: func() {
				vUserClient.EXPECT().CancelBindInvite(gomock.Any(), gomock.Any()).Return(&virtual_image_user.CancelBindInviteResponse{}, nil)
			},
			wantErr: false,
		},
		{
			name: "invalid targetUid",
			args: args{
				ctx:       ctx,
				uid:       testUid,
				targetUid: 0,
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := testMgr
			if tt.initFunc != nil {
				tt.initFunc()
			}
			if err := m.CancelBindInvite(tt.args.ctx, tt.args.uid, tt.args.targetUid); (err != nil) != tt.wantErr {
				t.Errorf("CancelBindInvite() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestVirtualImageLogicMgr_GetBindBeInvitedList(t *testing.T) {
	initTestMgr(t)
	now := time.Now()
	type args struct {
		ctx context.Context
		uid uint32
	}
	tests := []struct {
		name     string
		initFunc func()
		args     args
		want     *pb.GetBindBeInvitedListResponse
		wantErr  bool
	}{
		{
			name: "success",
			args: args{
				ctx: ctx,
				uid: testTargetUid,
			},
			initFunc: func() {
				vUserClient.EXPECT().GetBindBeInvitedList(gomock.Any(), gomock.Any()).Return(&virtual_image_user.GetBindBeInvitedListResponse{
					InfoList: []*virtual_image_user.BindInviteInfo{
						{
							Uid:       testUid,
							TargetUid: testTargetUid,
							InviteId:  fmt.Sprintf("%d_%d_%d", testUid, testTargetUid, now.Unix()),
							Status:    1,
						},
					},
				}, nil)

				userProfileCli.EXPECT().BatchGetUserProfileV2(gomock.Any(), gomock.Any(), gomock.Any()).Return(map[uint32]*app.UserProfile{
					testUid: {
						Uid:      testUid,
						Account:  "111",
						Nickname: "111",
					},
				}, nil)
			},
			want: &pb.GetBindBeInvitedListResponse{
				InviteList: []*pb.InviteInfo{
					{
						InviteId: fmt.Sprintf("%d_%d_%d", testUid, testTargetUid, now.Unix()),
						Inviter: &app.UserProfile{
							Uid:      testUid,
							Account:  "111",
							Nickname: "111",
						},
						Status: 1,
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := testMgr
			if tt.initFunc != nil {
				tt.initFunc()
			}
			got, err := m.GetBindBeInvitedList(tt.args.ctx, tt.args.uid)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetBindBeInvitedList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetBindBeInvitedList() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestVirtualImageLogicMgr_GetBindInvitableList(t *testing.T) {
	initTestMgr(t)
	type args struct {
		ctx    context.Context
		uid    uint32
		limit  uint32
		offset string
	}
	tests := []struct {
		name     string
		initFunc func()
		args     args
		want     *pb.GetBindInvitableListResponse
		wantErr  bool
	}{
		{
			name: "success",
			args: args{
				ctx:    ctx,
				uid:    testUid,
				limit:  10,
				offset: "",
			},
			initFunc: func() {
				vUserClient.EXPECT().GetUserRelationList(gomock.Any(), gomock.Any()).Return(&virtual_image_user.GetUserRelationListResponse{
					MaxBindCount: 10,
				}, nil)
				vUserClient.EXPECT().GetBindBeInvitedList(gomock.Any(), gomock.Any()).Return(&virtual_image_user.GetBindBeInvitedListResponse{}, nil)

				friendCli.EXPECT().GetAllFriendInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*friendshipPb.FriendInfo{
					{
						FriendUid: testTargetUid,
						IsDelete:  false,
					},
				}, nil)

				userProfileCli.EXPECT().BatchGetUserProfileV2(gomock.Any(), gomock.Any(), gomock.Any()).Return(map[uint32]*app.UserProfile{
					testTargetUid: {
						Uid:      testTargetUid,
						Account:  "111",
						Nickname: "111",
					},
				}, nil)

				presenceV2Cli.EXPECT().BatchGetUserPres(gomock.Any(), gomock.Any()).Return(map[uint32]*Presence.PresInfoList{
					testTargetUid: {
						InfoList: []*Presence.PresInfo{
							{
								Offline: false,
							},
						},
					},
				}, nil)

				superPlayerCli.EXPECT().BatchGetUserOnlineSwitch(ctx, gomock.Any()).Return(&super_player_privilege.BatchGetUserOnlineSwitchResp{
					UidOnlineSwitchMap: map[uint32]super_player_privilege.OnlineSwitch{
						testTargetUid: super_player_privilege.OnlineSwitch_ENUM_ONLINE_SWITCH_ONLINE,
					},
				}, nil)
			},

			want: &pb.GetBindInvitableListResponse{
				NextOffset: "",
				UserList: []*pb.InvitableUser{
					{
						Info: &app.UserProfile{
							Uid:      testTargetUid,
							Account:  "111",
							Nickname: "111",
						},
						IsOnline: true,
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := testMgr
			if tt.initFunc != nil {
				tt.initFunc()
			}
			got, err := m.GetBindInvitableList(tt.args.ctx, tt.args.uid, tt.args.limit, tt.args.offset)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetBindInvitableList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetBindInvitableList() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestVirtualImageLogicMgr_GetBindInviteStatus(t *testing.T) {
	initTestMgr(t)
	now := time.Now()
	type args struct {
		ctx      context.Context
		uid      uint32
		inviteId string
	}
	tests := []struct {
		name     string
		initFunc func()
		args     args
		want     *pb.GetBindInviteStatusResponse
		wantErr  bool
	}{
		{
			name: "success",
			args: args{
				ctx:      ctx,
				uid:      testTargetUid,
				inviteId: fmt.Sprintf("%d_%d_%d", testUid, testTargetUid, now.Unix()),
			},
			initFunc: func() {
				vUserClient.EXPECT().GetBindInviteStatus(gomock.Any(), gomock.Any()).Return(&virtual_image_user.GetBindInviteStatusResponse{
					Info: &virtual_image_user.BindInviteInfo{
						Uid:       testUid,
						TargetUid: testTargetUid,
						InviteId:  fmt.Sprintf("%d_%d_%d", testUid, testTargetUid, now.Unix()),
						Status:    4,
					},
				}, nil)

				userProfileCli.EXPECT().GetUserProfileV2(gomock.Any(), gomock.Any(), gomock.Any()).Return(&app.UserProfile{
					Uid: testUid,
				}, nil)
			},
			want: &pb.GetBindInviteStatusResponse{
				InviteInfo: &pb.InviteInfo{
					InviteId: fmt.Sprintf("%d_%d_%d", testUid, testTargetUid, now.Unix()),
					Inviter: &app.UserProfile{
						Uid: testUid,
					},
					Status: 4,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := testMgr
			if tt.initFunc != nil {
				tt.initFunc()
			}
			got, err := m.GetBindInviteStatus(tt.args.ctx, tt.args.uid, tt.args.inviteId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetBindInviteStatus() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetBindInviteStatus() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestVirtualImageLogicMgr_GetUserRelationInfo(t *testing.T) {
	initTestMgr(t)
	type args struct {
		ctx context.Context
		uid uint32
	}
	tests := []struct {
		name     string
		initFunc func()
		args     args
		want     *pb.UserRelationInfo
		wantErr  bool
	}{
		{
			name: "success",
			args: args{
				ctx: ctx,
				uid: testUid,
			},
			initFunc: func() {
				vUserClient.EXPECT().GetUserRelationList(gomock.Any(), gomock.Any()).Return(&virtual_image_user.GetUserRelationListResponse{
					BindList:   []uint32{testTargetUid},
					InviteList: []uint32{testTargetUid + 1},
				}, nil)

				vUserClient.EXPECT().GetUserRelationInUse(gomock.Any(), gomock.Any(), gomock.Any()).Return(&virtual_image_user.GetUserRelationInUseResponse{
					TargetUid: testTargetUid,
				}, nil)

				userProfileCli.EXPECT().BatchGetUserProfileV2(gomock.Any(), gomock.Any(), gomock.Any()).Return(map[uint32]*app.UserProfile{
					testTargetUid: {
						Uid: testTargetUid,
					},
					testTargetUid + 1: {
						Uid: testTargetUid + 1,
					},
				}, nil)
			},
			want: &pb.UserRelationInfo{
				BindList: []*app.UserProfile{
					{
						Uid: testTargetUid,
					},
				},
				InvitingList: []*app.UserProfile{
					{
						Uid: testTargetUid + 1,
					},
				},
				RelationUidInUse: testTargetUid,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := testMgr
			if tt.initFunc != nil {
				tt.initFunc()
			}
			got, err := m.GetUserRelationInfo(tt.args.ctx, tt.args.uid)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetUserRelationInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetUserRelationInfo() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestVirtualImageLogicMgr_RejectBindInvite(t *testing.T) {
	initTestMgr(t)
	type args struct {
		ctx      context.Context
		uid      uint32
		inviteId string
	}
	tests := []struct {
		name     string
		initFunc func()
		args     args
		wantErr  bool
	}{
		{
			name: "success",
			args: args{
				ctx:      ctx,
				uid:      testTargetUid,
				inviteId: fmt.Sprintf("%d_%d_%d", testUid, testTargetUid, time.Now().Unix()),
			},
			initFunc: func() {
				vUserClient.EXPECT().RejectBindInvite(gomock.Any(), gomock.Any()).Return(&virtual_image_user.RejectBindInviteResponse{}, nil)
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := testMgr
			if tt.initFunc != nil {
				tt.initFunc()
			}
			if err := m.RejectBindInvite(tt.args.ctx, tt.args.uid, tt.args.inviteId); (err != nil) != tt.wantErr {
				t.Errorf("RejectBindInvite() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestVirtualImageLogicMgr_SendBindInvite(t *testing.T) {
	initTestMgr(t)
	type args struct {
		ctx       context.Context
		uid       uint32
		targetUid uint32
	}
	tests := []struct {
		name     string
		initFunc func()
		args     args
		wantErr  bool
	}{
		{
			name: "success",
			args: args{
				ctx:       ctx,
				uid:       testUid,
				targetUid: testTargetUid,
			},
			initFunc: func() {
				friendCli.EXPECT().GetOneFriendInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(&friendshipPb.FriendInfo{
					FriendUid: testTargetUid,
					IsDelete:  false,
				}, nil)
				vUserClient.EXPECT().SetBindInvite(gomock.Any(), gomock.Any()).Return(&virtual_image_user.SetBindInviteResponse{}, nil)
			},
		},
		{
			name: "target is not friend",
			args: args{
				ctx:       ctx,
				uid:       testUid,
				targetUid: testTargetUid,
			},
			initFunc: func() {
				friendCli.EXPECT().GetOneFriendInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(&friendshipPb.FriendInfo{
					FriendUid: testTargetUid,
					IsDelete:  true,
				}, nil)
			},
			wantErr: true,
		},

		{
			name: "invalid in-param",
			args: args{
				ctx:       ctx,
				uid:       testUid,
				targetUid: 0,
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := testMgr
			if tt.initFunc != nil {
				tt.initFunc()
			}
			if err := m.SendBindInvite(tt.args.ctx, tt.args.uid, tt.args.targetUid); (err != nil) != tt.wantErr {
				t.Errorf("SendBindInvite() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestVirtualImageLogicMgr_SetVirtualBindInUse(t *testing.T) {
	initTestMgr(t)
	type args struct {
		ctx       context.Context
		uid       uint32
		targetUid uint32
	}
	tests := []struct {
		name     string
		initFunc func()
		args     args
		wantErr  bool
	}{
		{
			name: "success",
			args: args{
				ctx:       ctx,
				uid:       testUid,
				targetUid: testTargetUid,
			},
			initFunc: func() {
				vUserClient.EXPECT().SetVirtualBindInUse(gomock.Any(), gomock.Any()).Return(&virtual_image_user.SetVirtualBindInUseResponse{}, nil)
			},
		},
		{
			name: "invalid param",
			args: args{
				ctx:       ctx,
				uid:       testUid,
				targetUid: testUid,
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := testMgr
			if tt.initFunc != nil {
				tt.initFunc()
			}
			if err := m.SetVirtualBindInUse(tt.args.ctx, tt.args.uid, tt.args.targetUid); (err != nil) != tt.wantErr {
				t.Errorf("SetVirtualBindInUse() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestVirtualImageLogicMgr_UnbindVirtualImage(t *testing.T) {
	initTestMgr(t)
	type args struct {
		ctx       context.Context
		uid       uint32
		targetUid uint32
	}
	tests := []struct {
		name     string
		initFunc func()
		args     args
		wantErr  bool
	}{
		{
			name: "success",
			args: args{
				ctx:       ctx,
				uid:       testUid,
				targetUid: testTargetUid,
			},
			initFunc: func() {
				vUserClient.EXPECT().UnbindVirtualImage(gomock.Any(), gomock.Any()).Return(&virtual_image_user.UnbindVirtualImageResponse{}, nil)
			},
			wantErr: false,
		},
		{
			name: "invalid param",
			args: args{
				ctx:       ctx,
				uid:       testUid,
				targetUid: 0,
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := testMgr
			if tt.initFunc != nil {
				tt.initFunc()
			}
			if err := m.UnbindVirtualImage(tt.args.ctx, tt.args.uid, tt.args.targetUid); (err != nil) != tt.wantErr {
				t.Errorf("UnbindVirtualImage() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
