package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
)

const (
	apiURL = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal"
)

type TokenRequest struct {
	AppID     string `json:"app_id"`
	AppSecret string `json:"app_secret"`
}
type TokenResponse struct {
	Code              int    `json:"code"`
	Msg               string `json:"msg"`
	TenantAccessToken string `json:"tenant_access_token"`
	Expire            int    `json:"expire"`
}

func genAccessToken() (string, error) {
	cacheToken, err := localCache.Get([]byte("tenant_access_token"))
	if err == nil {
		fmt.Printf("get cacheToken: %s\n", cacheToken)
		return string(cacheToken), nil
	}

	requestBody := TokenRequest{
		AppID:     AppID,
		AppSecret: AppSecret,
	}
	jsonBody, err := json.Marshal(requestBody)
	if err != nil {
		fmt.Printf("Error marshalling request body: %v\n", err)
		return "", err
	}
	// 发送 HTTP POST 请求
	resp, err := http.Post(apiURL, "application/json; charset=utf-8", bytes.NewBuffer(jsonBody))
	if err != nil {
		fmt.Printf("Error making HTTP request: %v\n", err)
		return "", err
	}
	defer resp.Body.Close()

	// 读取响应体
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("Error reading response body: %v\n", err)
		return "", err
	}
	// 解析响应
	var tokenResponse TokenResponse
	err = json.Unmarshal(body, &tokenResponse)
	if err != nil {
		fmt.Printf("Error unmarshalling response: %v\n", err)
		return "", err
	}
	// 检查响应状态
	if tokenResponse.Code != 0 {
		fmt.Printf("Failed to get tenant_access_token: %s\n", tokenResponse.Msg)
		return "", err
	}

	localCache.Set([]byte("tenant_access_token"), []byte(tokenResponse.TenantAccessToken), tokenResponse.Expire)
	return tokenResponse.TenantAccessToken, nil
}
