package main

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/coocood/freecache"
	_ "github.com/joho/godotenv/autoload"
	"github.com/larksuite/oapi-sdk-go/v3"
	"github.com/larksuite/oapi-sdk-go/v3/core"
	"github.com/larksuite/oapi-sdk-go/v3/service/bitable/v1"
	mysqlConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/mysql/connect"
	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/virtual-image-resource"
	"golang.52tt.com/services/virtual-image/virtual-image-resource/internal/model/resource-cfg/store"
	"strings"
	"time"
)

var (
	AppID      = "cli_a4fefd83137ad00e"
	AppSecret  = "4Bm9YuHeqQAAb9z1lPbDVcLnQKQd1Jgt"
	localCache freecache.Cache
	larkClient *lark.Client
)

type SuitValue struct {
	Text string `json:"text"`
	Type string `json:"type"`
}

type Suit struct {
	Type  int         `json:"type"`
	Value []SuitValue `json:"value"`
}

var testStore *store.Store

func init() {
	mysqlConfig := &mysqlConnect.MysqlConfig{
		Host:     "**********",
		Port:     3306,
		Database: "appsvr",
		Charset:  "utf8",
		UserName: "godman",
		Password: "thegodofman",
	}

	dbCli, err := mysqlConnect.NewClient(context.Background(), mysqlConfig)
	if err != nil {
		return
	}
	testStore = store.NewStore(dbCli)
}

func main() {
	// 创建 Client
	/*larkClient = lark.NewClient(AppID, AppSecret)
	  var nextToken string
	  for {
	  	nextToken = refreshID(nextToken)
	  	if len(nextToken) > 0 {
	  		nextToken = refreshID(nextToken)
	  		if len(nextToken) > 0 {
	  			continue
	  		} else {
	  			break
	  		}
	  	} else {
	  		break
	  	}
	  }
	  fmt.Println("refreshID Done")

	*/
	larkMgr := NewLarkMgr()
	filedValue := larkMgr.SearchRecordByFieldNameAndValue(context.Background(), "套装ID/物品码", "suit01_2025011065")
	fmt.Println("SearchRecordByFieldNameAndValue", filedValue)

	var pageToken string
	var sheetDataList []Response
	for {
		dataList, nextPage, err := larkMgr.GetSheetDataByPageToken(pageToken)
		if err != nil {
			fmt.Println(err)
			return
		}

		sheetDataList = append(sheetDataList, dataList...)
		fmt.Println("GetSheetDataByPageToken", len(dataList), nextPage)
		if nextPage == "" {
			break
		}
		pageToken = nextPage
	}

	for _, item := range sheetDataList {
		fmt.Println("sheetDataList", item.RecordId, item.Fields.SuiteIDItemCode.Value[0].Text)

		var hasPrefix bool //是否侧身资源
		var isSuit bool    //是否套装
		var itemId string
		suitName := item.Fields.SuiteIDItemCode.Value[0].Text
		progress := item.Fields.Progress
		if len(item.Fields.ItemIdList) > 0 {
			itemId = item.Fields.ItemIdList[0].Text
		}

		fmt.Println("套装ID/物品码", suitName, progress, itemId)

		if strings.HasPrefix(suitName, "c") {
			suitName = strings.Replace(suitName, "c", "", 1)
			hasPrefix = true
		}

		if strings.Contains(suitName, "suit") {
			isSuit = true
		}

		var resourceList []store.VirtualImageResourceInfo
		var resourceIDList []string
		var idList []uint32
		var err error

		if isSuit {
			resourceList, err = testStore.SearchVirtualImageResource(context.Background(), &pb.SearchVirtualImageResourceRequest{
				Suit:   suitName,
				Offset: 0,
				Limit:  100,
			})
			if err != nil {
				log.ErrorWithCtx(context.Background(), "Error searching virtual image resource: %v", err)
				continue
			}
		} else {
			resourceList, err = testStore.SearchVirtualImageResource(context.Background(), &pb.SearchVirtualImageResourceRequest{
				SkinName: suitName,
				Offset:   0,
				Limit:    100,
			})
			if err != nil {
				log.ErrorWithCtx(context.Background(), "Error searching virtual image resource: %v", err)
				continue
			}
		}

		for _, r := range resourceList {
			if hasPrefix {
				if r.ResourcePrefix != "c" {
					continue
				}
			} else {
				if r.ResourcePrefix != "" {
					continue
				}
			}

			if r.DefaultSuit == suitName || r.SkinName == suitName || r.ResourceName == suitName {
				resourceIDList = append(resourceIDList, fmt.Sprintf("%d", r.ID))
				idList = append(idList, r.ID)
			}

			if !isSuit && r.Status != getStatusIntByString(progress) {
				fmt.Println("RecordId not same status:", item.RecordId, "status:", progress)
				testStore.UpdateVirtualImageResourceStatus(context.Background(), idList, getStatusIntByString(progress))
			}
		}

		if len(resourceIDList) > 0 {
			if itemId != strings.Join(resourceIDList, ",") {
				fmt.Println("RecordId not same id list:", suitName, item.RecordId, "itemId:", itemId, "resourceIDList:", resourceIDList)
				writeResourceIDList(item.RecordId, strings.Join(resourceIDList, ","), item.RecordId, larkClient)
				//larkMgr.UpdateSheetByRecordID(context.Background(), item.RecordId, "物品ID", strings.Join(resourceIDList, ","))
			}

			fmt.Println("RecordId not same status:", item.RecordId, "status:", progress)
			//testStore.UpdateVirtualImageResourceStatus(context.Background(), idList, getStatusIntByString(progress))

		}
	}

}

// Response 是最外层的响应结构体
type Response struct {
	Fields   Fields `json:"Fields"`
	RecordId string `json:"_"`
}

// Fields 包含记录的字段
type Fields struct {
	SuiteIDItemCode SuiteIDItemCode `json:"套装ID/物品码"`
	Progress        string          `json:"进度"`
	ItemIdList      []SuiteIDItem   `json:"物品ID"`
}

// SuiteIDItemCode 是套装ID/物品码的具体内容
type SuiteIDItemCode struct {
	Type  int           `json:"type"`
	Value []SuiteIDItem `json:"value"`
}

// SuiteIDItem 是套装ID/物品码的具体项
type SuiteIDItem struct {
	Text string `json:"text"`
	Type string `json:"type"`
}

func refreshID(pageToken string) string {
	userToken, err := genAccessToken()
	if err != nil {
		fmt.Println(err)
		return ""
	}

	suitFieldName := "套装ID/物品码"
	start := time.Now()

	// 创建请求对象
	req := larkbitable.NewSearchAppTableRecordReqBuilder().
		AppToken(`K3PObLjETapgK4stQrLcgsoqnCb`).
		TableId(`tbll8k9HVzOt8LTl`).
		UserIdType(`open_id`).
		PageSize(500).
		PageToken(pageToken).
		Body(larkbitable.NewSearchAppTableRecordReqBodyBuilder().
			ViewId(`vewgtDnaZ4`).
			FieldNames([]string{suitFieldName, `进度`}).
			AutomaticFields(false).
			Sort([]*larkbitable.Sort{
				larkbitable.NewSortBuilder().
					FieldName(suitFieldName).
					Desc(true).
					Build(),
			}).
			Build()).
		Build()

	// 发起请求
	resp, err := larkClient.Bitable.V1.AppTableRecord.Search(context.Background(), req, larkcore.WithTenantAccessToken(userToken))

	// 处理错误
	if err != nil {
		fmt.Println(err)
		return ""
	}

	// 服务端错误处理
	if !resp.Success() {
		fmt.Printf("logId: %s, error response: \n%s", resp.RequestId(), larkcore.Prettify(resp.CodeError))
		return ""
	}

	for _, item := range resp.Data.Items {
		jsonData, err := json.Marshal(item)
		var itemField Response
		err = json.Unmarshal(jsonData, &itemField)
		if err != nil {
			fmt.Printf("Error Unmarshal processing item:%s - err:%v\n", larkcore.Prettify(item), err)
			return ""
		}

		if len(itemField.Fields.SuiteIDItemCode.Value) == 0 {
			fmt.Println("No suiteIDItemCode", item)
			continue
		}

		suitName := itemField.Fields.SuiteIDItemCode.Value[0].Text
		itemField.RecordId = *item.RecordId

		var resourceIDList []string
		var idList []uint32

		var hasPrefix bool
		if strings.HasPrefix(suitName, "c") {
			suitName = strings.Replace(suitName, "c", "", 1)
			hasPrefix = true
		}

		//先搜索套装
		resource, err := testStore.SearchVirtualImageResource(context.Background(), &pb.SearchVirtualImageResourceRequest{
			Suit:   suitName,
			Offset: 0,
			Limit:  100,
		})
		if err != nil {
			fmt.Printf("Error searching virtual image resource: %v\n", err)
			continue
		}

		if len(resource) > 0 {
			for _, r := range resource {
				if hasPrefix {
					if r.ResourcePrefix != "c" {
						continue
					}
				} else {
					if r.ResourcePrefix != "" {
						continue
					}
				}

				if r.DefaultSuit == suitName {
					resourceIDList = append(resourceIDList, fmt.Sprintf("%d", r.ID))
					idList = append(idList, r.ID)
				}
			}
		} else {
			//找不到套装  根据skin_name搜索单品
			if strings.Contains(suitName, "csingle") {
				suitName = strings.Replace(suitName, "csingle", "single", 1)
				hasPrefix = true
			}
			resource, err = testStore.SearchVirtualImageResource(context.Background(), &pb.SearchVirtualImageResourceRequest{
				SkinName: suitName,
				Offset:   0,
				Limit:    10,
			})

			for _, r := range resource {
				if hasPrefix {
					if r.ResourcePrefix != "c" {
						continue
					}
				} else {
					if r.ResourcePrefix != "" {
						continue
					}
				}

				if r.SkinName == suitName || r.ResourceName == suitName {
					resourceIDList = append(resourceIDList, fmt.Sprintf("%d", r.ID))
					idList = append(idList, r.ID)
				}
			}
		}

		if len(resourceIDList) > 0 {
			fmt.Printf("RecordId:%s  %s == %v\n", *item.RecordId, suitName, resourceIDList)
			writeResourceIDList(*item.RecordId, strings.Join(resourceIDList, ","), userToken, larkClient)
			testStore.UpdateVirtualImageResourceStatus(context.Background(), idList, getStatusIntByString(itemField.Fields.Progress))
		} else {
			fmt.Printf("Record Not found:%s  %s\n", *item.RecordId, suitName)
		}
	}

	if !*resp.Data.HasMore {
		return ""
	}

	fmt.Println("cost:", time.Since(start))
	return *resp.Data.PageToken
}

func writeResourceIDList(recordId string, list, userToken string, client *lark.Client) {
	req := larkbitable.NewUpdateAppTableRecordReqBuilder().
		AppToken(`K3PObLjETapgK4stQrLcgsoqnCb`).
		TableId(`tbll8k9HVzOt8LTl`).
		RecordId(recordId).
		AppTableRecord(larkbitable.NewAppTableRecordBuilder().
			Fields(map[string]interface{}{`物品ID`: list}).
			Build()).
		Build()

	// 发起请求
	resp, err := client.Bitable.V1.AppTableRecord.Update(context.Background(), req, larkcore.WithTenantAccessToken(userToken))

	// 处理错误
	if err != nil {
		fmt.Println("writeResourceIDList failed", err)
		return
	}

	// 服务端错误处理
	if !resp.Success() {
		fmt.Printf("logId: %s, error response: \n%s", resp.RequestId(), larkcore.Prettify(resp.CodeError))
		return
	}
}

func getStatusIntByString(name string) uint32 {
	switch name {
	case "已上传,待测试":
		return 1
	case "测试中":
		return 2
	case "已重新上传待测试":
		return 3
	case "有问题待修复":
		return 4
	case "原画已输出":
		return 5
	case "暂停测试":
		return 6
	case "动作制作中":
		return 7
	case "原画制作中":
		return 8
	case "待定制作":
		return 9
	case "测试完成":
		return 10
	default:
		return 0
	}
}
