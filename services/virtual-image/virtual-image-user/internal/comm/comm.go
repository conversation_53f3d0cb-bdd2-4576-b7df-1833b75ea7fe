package comm

import (
    virtual_image_user "golang.52tt.com/protocol/services/virtual-image-user"
    "golang.52tt.com/services/virtual-image/virtual-image-user/internal/model/user-virtual-avatar/cache"
    "sort"
)

func FillInuseItemInfoList(list []*cache.UserVAInUse) []*virtual_image_user.InuseItemInfo {
    items := make([]*virtual_image_user.InuseItemInfo, 0)
    for _, info := range list {
        items = append(items, &virtual_image_user.InuseItemInfo{
            CfgId:         info.VAId,
            SubCategory:   info.CategoryID,
            ExpireTime:    info.ExpireTs,
            UseRightsType: info.Rights,
        })
    }

    // 按照SubCategory降序排序
    sort.SliceStable(items, func(i, j int) bool {
        return items[i].SubCategory > items[j].SubCategory
    })
    return items
}
