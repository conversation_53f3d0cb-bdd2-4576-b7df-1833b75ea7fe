package event

import (
    "context"
    "gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
    "gitlab.ttyuyin.com/tt-infra/middleware/kafka/subscriber"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    "golang.52tt.com/protocol/app/virtual_image_logic"
    "golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafkasimplemic"
    virtual_image_user "golang.52tt.com/protocol/services/virtual-image-user"
    "golang.52tt.com/services/virtual-image/virtual-image-user/internal/comm"
)

// HandleChannelMicEvent return error,（true-retry, false-no retry）
func (k *KafkaEvent) HandleChannelMicEvent(ctx context.Context, msg *subscriber.ConsumerMessage) (error, bool) {
    event := &kafkasimplemic.SimpleMicEvent{}
    err := proto.Unmarshal(msg.Value, event)
    if err != nil {
        log.ErrorWithCtx(ctx, "HandleChannelMicEvent Failed to proto.Unmarshal %+v", err)
        return err, false
    }

    if event.GetEventType() != uint32(kafkasimplemic.ESIMPLE_MIC_EVENT_TYPE_ENUM_SIMPLE_MIC_EV_HOLD) {
        // 只关注上麦事件
        return nil, false
    }

    log.DebugWithCtx(ctx, "HandleChannelMicEvent %+v", event)

    uid := event.GetMicUserId()
    err = k.pushChannelUserVIChange(ctx, event.GetChId(), uid, true)
    if err != nil {
        log.ErrorWithCtx(ctx, "HandleChannelMicEvent pushChannelUserVIChange failed,  %+v, err:%v", event, err)
        return err, true
    }

    log.InfoWithCtx(ctx, "HandleChannelMicEvent success. %+v", event)
    return nil, false
}

func (k *KafkaEvent) pushChannelUserVIChange(ctx context.Context, cid, uid uint32, checkUkw bool) error {
    switchMap, err := k.displaySwitch.GetUserDisplaySwitchMap(ctx, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "pushChannelUserVIChange GetUserDisplaySwitchMap failed, %+v, err:%v", uid, err)
        return err
    }

    if info, ok := switchMap[uint32(virtual_image_logic.VirtualImageDisplaySwitch_VIRTUAL_IMAGE_DISPLAY_SWITCH_MIC)]; !ok || !info.Switch {
        // 未开启虚拟形象麦位外显
        return nil
    }

    inuseInfoMap, err := k.userVa.BatchGetUserInUseList(ctx, []uint32{uid})
    if err != nil {
        log.DebugWithCtx(ctx, "pushChannelUserVIChange BatchGetUserInUseList failed, %+v, err:%v", uid, err)
        return err
    }

    inuseInfo, ok := inuseInfoMap[uid]
    if !ok {
        log.DebugWithCtx(ctx, "pushChannelUserVIChange inuseInfoMap not found, uid:%v", uid)
        return nil
    }

    orientationMap, err := k.userVa.BatchGetUserOrientation(ctx, []uint32{uid})
    if err != nil {
        log.ErrorWithCtx(ctx, "pushChannelUserVIChange BatchGetUserOrientation failed, %+v, err:%v", uid, err)
        return err
    }

    //now := time.Now()
    items := comm.FillInuseItemInfoList(inuseInfo)

    err = k.acLayer.PushChannelUserVIChange(ctx, cid, &virtual_image_user.UserInuseItemInfo{
        Uid:         uid,
        Items:       items,
        Orientation: orientationMap[uid],
    }, checkUkw)
    if err != nil {
        log.ErrorWithCtx(ctx, "pushChannelUserVIChange PushChannelUserVIChange failed,  %+v, err:%v", uid, err)
        return err
    }

    return nil
}

//func fillInuseItemInfoList(list []*cache.UserVAInUse, now time.Time) []*virtual_image_user.InuseItemInfo {
//    items := make([]*virtual_image_user.InuseItemInfo, 0)
//    for _, info := range list {
//        items = append(items, &virtual_image_user.InuseItemInfo{
//            CfgId:         info.VAId,
//            SubCategory:   info.CategoryID,
//            ExpireTime:    info.ExpireTs,
//            UseRightsType: info.Rights,
//        })
//    }
//    return items
//}
