package reminder

import (
    "context"
    "github.com/golang/mock/gomock"
    virtual_image_resource "golang.52tt.com/protocol/services/virtual-image-resource"
    acLaymocks "golang.52tt.com/services/virtual-image/virtual-image-user/internal/model/anti-corruption-layer/mocks"
    "golang.52tt.com/services/virtual-image/virtual-image-user/internal/model/reminder/cache"
    "sync"
    "testing"
    "time"
)

var (
    testMgr *Mgr

    ctx       = context.Background()
    ayLayMock *acLaymocks.MockIMgr
)

func initTestMgr(t *testing.T) *gomock.Controller {
    ctrl := gomock.NewController(t)
    ayLayMock = acLaymocks.NewMockIMgr(ctrl)

    testMgr = &Mgr{
        wg:       sync.WaitGroup{},
        shutDown: make(chan struct{}),
        acLayer:  ayLayMock,
    }

    return ctrl
}

func TestMgr_genExpireIMMsgContent(t *testing.T) {
    initTestMgr(t)

    type args struct {
        ctx  context.Context
        info *cache.ExpireInfo
    }
    tests := []struct {
        name     string
        initFunc func()
        args     args
        wantErr  bool
    }{
        {
            name: "expired 1",
            initFunc: func() {
                ayLayMock.EXPECT().BatGetVirtualImageCfg(gomock.Any(), gomock.Any()).Return([]*virtual_image_resource.VirtualImageResourceInfo{
                    {Id: 1, DisplayName: "1"},
                }, nil)
            },
            args: args{
                ctx: ctx,
                info: &cache.ExpireInfo{
                    Uid:      1001,
                    ItemIds:  []uint32{1},
                    ExpireTs: time.Now().Unix() - 10,
                },
            },
            wantErr: false,
        },
        {
            name: "expired 2",
            initFunc: func() {
                ayLayMock.EXPECT().BatGetVirtualImageCfg(gomock.Any(), gomock.Any()).Return([]*virtual_image_resource.VirtualImageResourceInfo{
                    {Id: 1, DisplayName: "1"},{Id: 2, DisplayName: "2"},
                }, nil)
            },
            args: args{
                ctx: ctx,
                info: &cache.ExpireInfo{
                    Uid:      1001,
                    ItemIds:  []uint32{1, 2},
                    ExpireTs: time.Now().Unix() - 10,
                },
            },
            wantErr: false,
        },
        {
            name: "expired 3",
            initFunc: func() {
                ayLayMock.EXPECT().BatGetVirtualImageCfg(gomock.Any(), gomock.Any()).Return([]*virtual_image_resource.VirtualImageResourceInfo{
                    {Id: 1, DisplayName: "1"},{Id: 2, DisplayName: "2"},{Id: 3, DisplayName: "3"},
                }, nil)
            },
            args: args{
                ctx: ctx,
                info: &cache.ExpireInfo{
                    Uid:      1001,
                    ItemIds:  []uint32{1,2,3},
                    ExpireTs: time.Now().Unix() - 10,
                },
            },
            wantErr: false,
        },
        {
            name: "expire soon 1",
            initFunc: func() {
                ayLayMock.EXPECT().BatGetVirtualImageCfg(gomock.Any(), gomock.Any()).Return([]*virtual_image_resource.VirtualImageResourceInfo{
                    {Id: 1, DisplayName: "1"},
                }, nil)
            },
            args: args{
                ctx: ctx,
                info: &cache.ExpireInfo{
                    Uid:      1001,
                    ItemIds:  []uint32{1},
                    ExpireTs: time.Now().Unix() + 60,
                },
            },
            wantErr: false,
        },
        {
            name: "expire soon 2",
            initFunc: func() {
                ayLayMock.EXPECT().BatGetVirtualImageCfg(gomock.Any(), gomock.Any()).Return([]*virtual_image_resource.VirtualImageResourceInfo{
                    {Id: 1, DisplayName: "1"},{Id: 2, DisplayName: "2"},
                }, nil)
            },
            args: args{
                ctx: ctx,
                info: &cache.ExpireInfo{
                    Uid:      1001,
                    ItemIds:  []uint32{1, 2},
                    ExpireTs: time.Now().Unix() + 60,
                },
            },
            wantErr: false,
        },
        {
            name: "expire soon 3",
            initFunc: func() {
                ayLayMock.EXPECT().BatGetVirtualImageCfg(gomock.Any(), gomock.Any()).Return([]*virtual_image_resource.VirtualImageResourceInfo{
                    {Id: 1, DisplayName: "1"},{Id: 2, DisplayName: "2"},{Id: 3, DisplayName: "3"},
                }, nil)
            },
            args: args{
                ctx: ctx,
                info: &cache.ExpireInfo{
                    Uid:      1001,
                    ItemIds:  []uint32{1,2,3},
                    ExpireTs: time.Now().Unix() + 60,
                },
            },
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := testMgr
            if tt.initFunc != nil {
                tt.initFunc()
            }
            content, err := m.genExpireIMMsgContent(tt.args.ctx, tt.args.info)
            if (err != nil) != tt.wantErr {
                t.Errorf("AcceptBindInvite() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
            t.Log("genExpireIMMsgContent content: ", content)
        })
    }
}
