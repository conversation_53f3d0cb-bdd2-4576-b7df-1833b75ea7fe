package store

import (
    "time"
    "context"
    "fmt"
    "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
    "golang.52tt.com/pkg/log"
)

const (
    SingleModeCpUid        = 0
    RelationInUseTableName = "virtual_image_relation_in_use"
)

// 记录用户使用中的关系
var createRelationInUseTblSql = `CREATE Table IF NOT EXISTS %s(
    id int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id 自增',
    uid int(10) unsigned NOT NULL COMMENT '用户uid',
    cp_uid int(10) unsigned NOT NULL COMMENT 'cp用户uid',
    ctime DATETIME NOT NULL default CURRENT_TIMESTAMP COMMENT '创建时间',
    mtime DATETIME NOT NULL default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    
    primary key (id),
    unique key uniq_idx_uid(uid)
)engine=InnoDB default charset=utf8 COMMENT "关系使用表";`

type RelationInUse struct {
    ID    uint32    `db:"id"`
    Uid   uint32    `db:"uid"`
    CpUid uint32    `db:"cp_uid"`
    Mtime time.Time `db:"mtime"`
}

// genTableName 生成表名
func genRelationInUseTableName() string {
    return RelationInUseTableName
}

func (s *Store) CreateRelationInUseTable(ctx context.Context, t time.Time) error {
    query := fmt.Sprintf(createRelationInUseTblSql, genRelationInUseTableName())
    _, err := s.db.ExecContext(ctx, query)
    return err
}

func (s *Store) UpdateRelationInUse(ctx context.Context, uid, cpUid uint32) error {

    query := fmt.Sprintf("INSERT INTO %s (uid, cp_uid) VALUES (?, ?) ON DUPLICATE KEY UPDATE cp_uid=?", genRelationInUseTableName())
    _, err := s.db.ExecContext(ctx, query, uid, cpUid, cpUid)
    return err
}

func (s *Store) UpdateRelationInUseWithCheck(ctx context.Context, uid, cpUid, oldCpUid uint32) error {
    query := fmt.Sprintf("UPDATE %s SET cp_uid=? WHERE uid=? AND cp_uid=?", genRelationInUseTableName())
    _, err := s.db.ExecContext(ctx, query, cpUid, uid, oldCpUid)
    if err != nil {
        log.ErrorWithCtx(ctx, "UpdateRelationInUseWithCheck failed uid:%d newCp:%d oldCheck:%d", uid, cpUid, oldCpUid)
        return err
    }
    return err
}

// GetRelationInUse 获取用户使用中的关系
func (s *Store) GetRelationInUse(ctx context.Context, uid uint32) (*RelationInUse, error) {

    query := fmt.Sprintf("SELECT id, uid, cp_uid, mtime FROM %s WHERE uid=?", genRelationInUseTableName())
    result := &RelationInUse{}
    err := s.db.GetContext(ctx, result, query, uid)
    if err != nil {
        // 记录不存在
        if mysql.IsNoRowsError(err) {
            return result, nil
        }
    }

    return result, err
}
