package main

import (
	"context"
	"fmt"
	"github.com/jmoiron/sqlx"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
	"golang.52tt.com/pkg/foundation/utils"
	mallStore "golang.52tt.com/services/virtual-image/virtual-image-mall/internal/store/mysql"
	"strings"

	"time"
)

type FinancialStatements struct {
	Id           uint32    `db:"id" json:"id"`                       // id
	Uid          uint32    `db:"uid" json:"uid"`                     //用户id
	OrderNo      string    `db:"order_no" json:"order_no"`           //订单号
	TotalPrice   uint32    `db:"total_price" json:"total_price"`     //总价
	AvgPrice     uint32    `db:"avg_price" json:"avg_price"`         //平均价
	CommodityId  uint32    `db:"commodity_id" json:"commodity_id"`   //商品id
	EffectiveDay uint32    `db:"effective_day" json:"effective_day"` //有效天数
	Count        uint32    `db:"c_count" json:"c_count"`             //数量
	CreateTime   time.Time `db:"create_time" json:"create_time"`     //获取时间
	StartTime    time.Time `db:"start_time" json:"start_time"`       //开始时间
	EndTime      time.Time `db:"end_time" json:"end_time"`           //结束时间
}

func (t *FinancialStatements) SqlString() string {
	return utils.ToSqlStr(t)
}

var st *Store

func (s *Store) BatchGetCommodityDataOrdersByTime(ctx context.Context, queryTime time.Time, offset, limit uint32) ([]*mallStore.CommodityDataOrders, error) {
	info := mallStore.CommodityDataOrders{CreateTime: queryTime}
	tableName := info.TableName()
	query := fmt.Sprintf("select %s from %+v where pay_status = 4 order by id limit %d offset %d", info.SqlString(), tableName, limit, offset)
	var err error

	orderList := make([]*mallStore.CommodityDataOrders, 0)
	inQuery, args, err := sqlx.In(query)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetCommodityDataOrdersPaying In fail, err :%v", err)
		return orderList, nil
	}

	err = s.db.SelectContext(ctx, &orderList, inQuery, args...)
	if err != nil {
		if mysql.IsNoRowsError(err) {
			return orderList, nil
		}
		log.ErrorWithCtx(ctx, "GetCommodityDataOrdersPaying sql:%v error: %v", query, err)
		return nil, err
	}
	log.DebugWithCtx(ctx, "GetCommodityDataOrdersPaying orderList size:%d", len(orderList))
	return orderList, nil
}

func (s *Store) createFinancialStatementsTable() {
	createTableSQL := `
	CREATE TABLE IF NOT EXISTS financial_statements (
		id INT AUTO_INCREMENT PRIMARY KEY COMMENT "自增id",
		uid INT NOT NULL DEFAULT 0 COMMENT "用户uid",
		order_no VARCHAR(255) NOT NULL DEFAULT "" COMMENT "订单号",
		total_price INT NOT NULL DEFAULT 0 COMMENT "总价",
		avg_price INT NOT NULL DEFAULT 0 COMMENT "平均价", 
		commodity_id INT NOT NULL DEFAULT 0 COMMENT "商品id", 
 	    effective_day INT NOT NULL DEFAULT 0 COMMENT "有效天数", 
		c_count INT NOT NULL DEFAULT 0 COMMENT "数量",
 		create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT "获取时间", 
 		start_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT "开始时间", 
	    end_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT "结束时间",
		UNIQUE KEY uniq_order_no (order_no),
	    INDEX idx_uid (uid),
	    INDEX idx_commodity_id (commodity_id),
	    INDEX idx_start_time (start_time),
		INDEX idx_end_time (end_time)
	) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT '商城财务报表';`
	_, err := s.db.Exec(createTableSQL)
	if err != nil {
		log.Errorf("createFinancialStatementsTable creating table err:", err)
		return
	}
}

func (s *Store) InsertFinancialStatements(ctx context.Context, in *FinancialStatements) error {
	insertSQL := `INSERT INTO  financial_statements ` + ` (uid, order_no, total_price, avg_price, commodity_id, effective_day, c_count,create_time, start_time, end_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`
	_, err := s.db.ExecContext(ctx, insertSQL, in.Uid, in.OrderNo, in.TotalPrice, in.AvgPrice, in.CommodityId, in.EffectiveDay, in.Count, in.CreateTime, in.StartTime, in.EndTime)
	if err != nil {
		if strings.Contains(err.Error(), "1062") {
			return nil
		}
		log.ErrorWithCtx(ctx, "InsertFinancialStatements insert err:", err)
	}
	return err
}

// 删除报表数据
func (s *Store) DeleteFinancialStatements(ctx context.Context) error {
	deleteSQL := `DELETE FROM financial_statements`
	_, err := s.db.ExecContext(ctx, deleteSQL)
	if err != nil {
		log.ErrorWithCtx(ctx, "DeleteFinancialStatements delete err:", err)
	}
	return err
}

func (s *Store) GetLastValidFinancialStatementsRecord(ctx context.Context, uid, commodityId uint32, startTime time.Time) (out *FinancialStatements, err error) {
	out = &FinancialStatements{}
	querySQL := fmt.Sprintf(`SELECT %s FROM financial_statements  WHERE uid = ? AND commodity_id = ? AND end_time > ? ORDER BY create_time DESC LIMIT 1`, out.SqlString())
	err = s.db.GetContext(ctx, out, querySQL, uid, commodityId, startTime)
	if err != nil {
		if err.Error() != "sql: no rows in result set" {
			log.ErrorWithCtx(ctx, "GetLastFinancialStatementsRecord query err:", err)
		}
	}
	return
}

func (s *Store) GetFinancialStatementsRecordMonthly(ctx context.Context, startTime, endTime time.Time, offset, limit uint32) ([]*FinancialStatements, error) {
	info := &FinancialStatements{}
	query := fmt.Sprintf("SELECT %s FROM financial_statements  where (create_time >= ? and create_time < ?) or (start_time >= ? and end_time < ?) or (end_time > ?)order by id limit ? offset ?", info.SqlString())
	list := make([]*FinancialStatements, 0)
	err := s.db.SelectContext(ctx, &list, query, startTime, endTime, startTime, endTime, startTime, limit, offset)
	if err != nil {
		// 表不存在
		if mysql.IsMySQLError(err, 1146) {
			return list, nil
		}
	}
	return list, err
}
