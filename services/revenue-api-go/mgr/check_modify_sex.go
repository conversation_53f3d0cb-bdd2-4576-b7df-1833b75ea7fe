package mgr

import (
    "context"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    "golang.52tt.com/protocol/app/channel_wedding_logic"
    "golang.52tt.com/protocol/common/status"
    channel_wedding "golang.52tt.com/protocol/services/channel-wedding"
    channel_wedding_plan "golang.52tt.com/protocol/services/channel-wedding-plan"
    revenue_api_go "golang.52tt.com/protocol/services/revenue-api-go"
    "golang.52tt.com/services/revenue-api-go/rpc"
)

func (m *RevenueApiMgr) CheckCanModifySex(ctx context.Context, uid uint32) (*revenue_api_go.CheckCanModifySexResp, error) {
    out := &revenue_api_go.CheckCanModifySexResp{}

    weddingInfo, err := rpc.Cli.ChannelWeddingCli.GetUserChannelWeddingInfo(ctx, &channel_wedding.GetUserChannelWeddingInfoReq{
        Uid: uid,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "CheckCanModifySex GetUserChannelWeddingInfo failed uid:%v err:%v", uid, err)
        return out, err
    }

    defer func() {
        log.DebugWithCtx(ctx, "CheckCanModifySex end uid:%v out:%+v", uid, out)
    }()

    if weddingInfo.GetWeddingInfo().GetWeddingId() != 0 {
        // 在婚礼中
        out.ErrCode = status.ErrChannelWeddingPlanCommonError
        out.ErrMsg = "婚礼过程中暂不支持切换性别哦~"
        log.DebugWithCtx(ctx, "CheckCanModifySex in wedding uid:%v", uid)
        return out, nil
    }

    // 检测有将开始的婚礼(不能取消)
    weddingRoleResp, err := rpc.Cli.ChannelWeddingPlanCli.GetMyWeddingRole(ctx, &channel_wedding_plan.GetMyWeddingRoleRequest{
        Uid: uid,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "CheckCanModifySex GetMyWeddingPlanInfo failed uid:%v err:%v", uid, err)
        return out, err
    }

    if weddingRoleResp.GetWeddingRole() > 0 && !weddingRoleResp.GetInChangeTime() {
        // 在婚礼计划中
        out.ErrCode = status.ErrChannelWeddingPlanCommonError
        out.ErrMsg = "婚礼过程中暂不支持切换性别哦~"
        if weddingRoleResp.GetWeddingRole() == uint32(channel_wedding_logic.WeddingGuestType_WEDDING_GUEST_TYPE_BRIDES) {
            out.ErrMsg = "当前有伴郎身份," + out.ErrMsg
        } else if weddingRoleResp.GetWeddingRole() == uint32(channel_wedding_logic.WeddingGuestType_WEDDING_GUEST_TYPE_BRIDESMAID) {
            out.ErrMsg = "当前有伴娘身份," + out.ErrMsg
        }
        log.DebugWithCtx(ctx, "CheckCanModifySex in wedding plan uid:%v, roleResp:%+v", uid, weddingRoleResp)
        return out, nil
    }

    return out, nil
}
