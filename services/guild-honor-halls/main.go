package main

import (
	"context"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	"gitlab.ttyuyin.com/avengers/tyr/core/service/startup/suit/grpc/server"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka"
	pb "golang.52tt.com/protocol/services/guildhonorhalls"
	"golang.52tt.com/services/guild-honor-halls/internal/conf"
	ser "golang.52tt.com/services/guild-honor-halls/internal/server"
	"google.golang.org/grpc"
)

func main() {
	var (
		svr *ser.GuildHonorHallsServer
		cfg = &conf.ServiceConfigT{}
		err error
	)

	// config file support yaml & json, default guild-honor-halls.json/yaml
	if err := server.NewServer("guild-honor-halls", cfg).
		AddGrpcServer(server.NewBuildOption().
			WithInitializeFunc(func(ctx context.Context, s *grpc.Server) error {
				kafka.InitEventLinkSubWithGrpcSvr(s)

				if svr, err = ser.NewGuildHonorHallsServer(ctx, cfg); err != nil {
					return err
				}

				// register custom grpc server
				pb.RegisterGuildHonorHallsServer(s, svr)
				return nil
			}),
		).
		WithCloseFunc(func(ctx context.Context) {
			// do something when server terminating
			svr.ShutDown()
		}).
		Start(); err != nil {
		log.Errorf("server start fail, err: %v", err)
	}
}
