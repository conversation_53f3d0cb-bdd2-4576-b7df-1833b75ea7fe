// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/levelup-present/cache (interfaces: IRedisCache)

// Package mocks is a generated GoMock package.
package mocks

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockIRedisCache is a mock of IRedisCache interface.
type MockIRedisCache struct {
	ctrl     *gomock.Controller
	recorder *MockIRedisCacheMockRecorder
}

// MockIRedisCacheMockRecorder is the mock recorder for MockIRedisCache.
type MockIRedisCacheMockRecorder struct {
	mock *MockIRedisCache
}

// NewMockIRedisCache creates a new mock instance.
func NewMockIRedisCache(ctrl *gomock.Controller) *MockIRedisCache {
	mock := &MockIRedisCache{ctrl: ctrl}
	mock.recorder = &MockIRedisCacheMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIRedisCache) EXPECT() *MockIRedisCacheMockRecorder {
	return m.recorder
}

// DelLevelupChildPresent mocks base method.
func (m *MockIRedisCache) DelLevelupChildPresent(arg0 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelLevelupChildPresent", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelLevelupChildPresent indicates an expected call of DelLevelupChildPresent.
func (mr *MockIRedisCacheMockRecorder) DelLevelupChildPresent(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelLevelupChildPresent", reflect.TypeOf((*MockIRedisCache)(nil).DelLevelupChildPresent), arg0)
}

// DelLevelupParentPresent mocks base method.
func (m *MockIRedisCache) DelLevelupParentPresent(arg0 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelLevelupParentPresent", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelLevelupParentPresent indicates an expected call of DelLevelupParentPresent.
func (mr *MockIRedisCacheMockRecorder) DelLevelupParentPresent(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelLevelupParentPresent", reflect.TypeOf((*MockIRedisCache)(nil).DelLevelupParentPresent), arg0)
}

// DelLevelupPresentBatch mocks base method.
func (m *MockIRedisCache) DelLevelupPresentBatch(arg0 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelLevelupPresentBatch", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelLevelupPresentBatch indicates an expected call of DelLevelupPresentBatch.
func (mr *MockIRedisCacheMockRecorder) DelLevelupPresentBatch(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelLevelupPresentBatch", reflect.TypeOf((*MockIRedisCache)(nil).DelLevelupPresentBatch), arg0)
}

// DelUserItemVersionData mocks base method.
func (m *MockIRedisCache) DelUserItemVersionData(arg0, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelUserItemVersionData", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelUserItemVersionData indicates an expected call of DelUserItemVersionData.
func (mr *MockIRedisCacheMockRecorder) DelUserItemVersionData(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelUserItemVersionData", reflect.TypeOf((*MockIRedisCache)(nil).DelUserItemVersionData), arg0, arg1, arg2)
}

// ExistParentId mocks base method.
func (m *MockIRedisCache) ExistParentId(arg0 string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExistParentId", arg0)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ExistParentId indicates an expected call of ExistParentId.
func (mr *MockIRedisCacheMockRecorder) ExistParentId(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExistParentId", reflect.TypeOf((*MockIRedisCache)(nil).ExistParentId), arg0)
}

// GetAllLevelupChildPresent mocks base method.
func (m *MockIRedisCache) GetAllLevelupChildPresent() (map[string]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllLevelupChildPresent")
	ret0, _ := ret[0].(map[string]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllLevelupChildPresent indicates an expected call of GetAllLevelupChildPresent.
func (mr *MockIRedisCacheMockRecorder) GetAllLevelupChildPresent() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllLevelupChildPresent", reflect.TypeOf((*MockIRedisCache)(nil).GetAllLevelupChildPresent))
}

// GetAllLevelupParentPresent mocks base method.
func (m *MockIRedisCache) GetAllLevelupParentPresent() (map[string]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllLevelupParentPresent")
	ret0, _ := ret[0].(map[string]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllLevelupParentPresent indicates an expected call of GetAllLevelupParentPresent.
func (mr *MockIRedisCacheMockRecorder) GetAllLevelupParentPresent() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllLevelupParentPresent", reflect.TypeOf((*MockIRedisCache)(nil).GetAllLevelupParentPresent))
}

// GetAllLevelupPresentBatch mocks base method.
func (m *MockIRedisCache) GetAllLevelupPresentBatch() (map[string]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllLevelupPresentBatch")
	ret0, _ := ret[0].(map[string]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllLevelupPresentBatch indicates an expected call of GetAllLevelupPresentBatch.
func (mr *MockIRedisCacheMockRecorder) GetAllLevelupPresentBatch() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllLevelupPresentBatch", reflect.TypeOf((*MockIRedisCache)(nil).GetAllLevelupPresentBatch))
}

// GetLevelupChildPresentById mocks base method.
func (m *MockIRedisCache) GetLevelupChildPresentById(arg0 string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLevelupChildPresentById", arg0)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLevelupChildPresentById indicates an expected call of GetLevelupChildPresentById.
func (mr *MockIRedisCacheMockRecorder) GetLevelupChildPresentById(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLevelupChildPresentById", reflect.TypeOf((*MockIRedisCache)(nil).GetLevelupChildPresentById), arg0)
}

// GetLevelupParentPresent mocks base method.
func (m *MockIRedisCache) GetLevelupParentPresent(arg0 string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLevelupParentPresent", arg0)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLevelupParentPresent indicates an expected call of GetLevelupParentPresent.
func (mr *MockIRedisCacheMockRecorder) GetLevelupParentPresent(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLevelupParentPresent", reflect.TypeOf((*MockIRedisCache)(nil).GetLevelupParentPresent), arg0)
}

// GetLevelupPresentBatchById mocks base method.
func (m *MockIRedisCache) GetLevelupPresentBatchById(arg0 string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLevelupPresentBatchById", arg0)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLevelupPresentBatchById indicates an expected call of GetLevelupPresentBatchById.
func (mr *MockIRedisCacheMockRecorder) GetLevelupPresentBatchById(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLevelupPresentBatchById", reflect.TypeOf((*MockIRedisCache)(nil).GetLevelupPresentBatchById), arg0)
}

// GetUserItemVersionData mocks base method.
func (m *MockIRedisCache) GetUserItemVersionData(arg0, arg1, arg2 uint32) string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserItemVersionData", arg0, arg1, arg2)
	ret0, _ := ret[0].(string)
	return ret0
}

// GetUserItemVersionData indicates an expected call of GetUserItemVersionData.
func (mr *MockIRedisCacheMockRecorder) GetUserItemVersionData(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserItemVersionData", reflect.TypeOf((*MockIRedisCache)(nil).GetUserItemVersionData), arg0, arg1, arg2)
}

// IncrSend mocks base method.
func (m *MockIRedisCache) IncrSend(arg0, arg1, arg2 uint32) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncrSend", arg0, arg1, arg2)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IncrSend indicates an expected call of IncrSend.
func (mr *MockIRedisCacheMockRecorder) IncrSend(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrSend", reflect.TypeOf((*MockIRedisCache)(nil).IncrSend), arg0, arg1, arg2)
}

// SetMultiLevelupChildPresent mocks base method.
func (m *MockIRedisCache) SetMultiLevelupChildPresent(arg0 map[string]interface{}) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetMultiLevelupChildPresent", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetMultiLevelupChildPresent indicates an expected call of SetMultiLevelupChildPresent.
func (mr *MockIRedisCacheMockRecorder) SetMultiLevelupChildPresent(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetMultiLevelupChildPresent", reflect.TypeOf((*MockIRedisCache)(nil).SetMultiLevelupChildPresent), arg0)
}

// SetMultiLevelupParentPresent mocks base method.
func (m *MockIRedisCache) SetMultiLevelupParentPresent(arg0 map[string]interface{}) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetMultiLevelupParentPresent", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetMultiLevelupParentPresent indicates an expected call of SetMultiLevelupParentPresent.
func (mr *MockIRedisCacheMockRecorder) SetMultiLevelupParentPresent(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetMultiLevelupParentPresent", reflect.TypeOf((*MockIRedisCache)(nil).SetMultiLevelupParentPresent), arg0)
}

// SetMultiLevelupPresentBatch mocks base method.
func (m *MockIRedisCache) SetMultiLevelupPresentBatch(arg0 map[string]interface{}) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetMultiLevelupPresentBatch", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetMultiLevelupPresentBatch indicates an expected call of SetMultiLevelupPresentBatch.
func (mr *MockIRedisCacheMockRecorder) SetMultiLevelupPresentBatch(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetMultiLevelupPresentBatch", reflect.TypeOf((*MockIRedisCache)(nil).SetMultiLevelupPresentBatch), arg0)
}

// SetRank mocks base method.
func (m *MockIRedisCache) SetRank(arg0, arg1, arg2, arg3 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetRank", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetRank indicates an expected call of SetRank.
func (mr *MockIRedisCacheMockRecorder) SetRank(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetRank", reflect.TypeOf((*MockIRedisCache)(nil).SetRank), arg0, arg1, arg2, arg3)
}

// SetUserItemVersionData mocks base method.
func (m *MockIRedisCache) SetUserItemVersionData(arg0, arg1, arg2 uint32, arg3 interface{}) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserItemVersionData", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetUserItemVersionData indicates an expected call of SetUserItemVersionData.
func (mr *MockIRedisCacheMockRecorder) SetUserItemVersionData(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserItemVersionData", reflect.TypeOf((*MockIRedisCache)(nil).SetUserItemVersionData), arg0, arg1, arg2, arg3)
}
