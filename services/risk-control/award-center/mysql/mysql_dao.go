package mysql

import (
	"context"
	"database/sql"
	"github.com/go-redis/redis"
	"github.com/jinzhu/gorm"
	"gitlab.ttyuyin.com/avengers/tyr/pkg/cluster/timer"
	"gitlab.ttyuyin.com/avengers/tyr/pkg/cluster/timer/task/tasks"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	"strings"
	"time"

	_ "github.com/go-sql-driver/mysql" // MySQL驱动
	pb "golang.52tt.com/protocol/services/risk-control/award-center"

	"fmt"
)

const DefaultAwardLimitType = uint32(pb.EGiftType_UnknownGiftType)

type Dao struct {
	db *gorm.DB
}

func NewMysql(db *gorm.DB, cacheCli *redis.Client) *Dao {
	dao := &Dao{
		db: db,
	}

	dao.CreateTable()

	createTableTimer, err := timer.NewTimerD(context.Background(), "award-center", timer.WithV6RedisCmdable(cacheCli))
	if err != nil {
		log.Errorf("create table timer fail err %v", err)
		return nil
	}

	createTableTimer.AddIntervalTask("awardCenterCreateOrderTable", time.Hour, tasks.FuncTask(dao.CreateOrderTable))
	createTableTimer.Start()
	return dao
}

func (d *Dao) CreateOrderTable(ctx context.Context) {
	nextMonth := time.Now().AddDate(0, 1, 0)
	awardOrder := &AwardOrder{OutsideTime: nextMonth}
	err := d.db.Set("gorm:table_options", "ENGINE=InnoDB").CreateTable(awardOrder).Error
	if err != nil {
		log.ErrorWithCtx(ctx, "create table award order fail err %v", err)
		return
	}
}

func InitMysqlDao(confPath string) (*Dao, error) {
	cfg, err := config.NewConfig("json", confPath)
	if err != nil {
		panic(err)
	}

	mysqlConfig := new(config.MysqlConfig)
	mysqlConfig.Read(cfg, "mysql")

	mysqlAddr := mysqlConfig.Description()
	log.Debugf("Initializing mysql connection pool to %s", mysqlAddr)

	var db *gorm.DB
	db, err = gorm.Open("mysql", mysqlAddr)
	if err != nil {
		log.Errorf("Initializing mysql connection pool to %s fail err %v", mysqlAddr, err)
		return nil, err
	}

	db.DB().SetMaxIdleConns(mysqlConfig.MaxIdleConns)
	db.DB().SetMaxOpenConns(mysqlConfig.MaxOpenConns)

	dao := &Dao{db: db}
	dao.CreateTable()

	return dao, nil
}

func (d *Dao) CreateTable() {
	var err error

	if !d.db.HasTable(&BusinessConf{}) {
		err = d.db.Set("gorm:table_options", "ENGINE=InnoDB").CreateTable(&BusinessConf{}).Error
		if err != nil {
			log.Errorf("CreateAwardConfTable fail err %v", err)
		}
	}

	if !d.db.HasTable(&AwardConf{}) {
		err = d.db.Set("gorm:table_options", "ENGINE=InnoDB").CreateTable(&AwardConf{}).Error
		if err != nil {
			log.Errorf("CreateAwardConfTable fail err %v", err)
		}
	}

	if !d.db.HasTable(&AwardTotalStats{}) {
		err = d.db.Set("gorm:table_options", "ENGINE=InnoDB").CreateTable(&AwardTotalStats{}).Error
		if err != nil {
			log.Errorf("CreateAwardConfTable fail err %v", err)
		}
	}

	log.Debugf("CreateTable done")
}

func (d *Dao) getDB(tx *gorm.DB) *gorm.DB {
	if tx != nil {
		return tx
	} else {
		return d.db
	}
}

func (d *Dao) Transaction(ctx context.Context, f func(tx *gorm.DB) error) error {
	tx := d.db.BeginTx(ctx, &sql.TxOptions{})

	err := f(tx)
	if err != nil {
		log.Errorf("Transaction fail err %v", err)
		tx.Rollback()
		return err
	}

	tx.Commit()
	return nil
}

// 业务配置
type BusinessConf struct {
	BusinessId   uint32    `gorm:"primary_key"`
	Name         string    `gorm:"unique_index:name_idx;not null;default:''"`
	BusinessDesc string    `gorm:"not null;default:''"`
	Invalid      bool      `gorm:"not null;default:0"`
	IsDel        bool      `gorm:"not null;default:0"`
	UpdateTime   time.Time `gorm:"not null;default:CURRENT_TIMESTAMP"`
	CreateTime   time.Time `gorm:"index:create_time_idx;not null;default:CURRENT_TIMESTAMP"`
	SourceType   uint32    `gorm:"not null;default:0"`
	BeginTime    time.Time `gorm:"not null;default:CURRENT_TIMESTAMP"`
	EndTime      time.Time `gorm:"not null;default:CURRENT_TIMESTAMP"`
}

func (c *BusinessConf) TableName() string {
	return "tbl_business_conf"
}

func (d *Dao) CreateBusinessConfTable() error {
	err := d.db.Set("gorm:table_options", "ENGINE=InnoDB").CreateTable(&BusinessConf{}).Error

	if err != nil {
		log.Errorf("CreateBusinessConfTable fail err %v", err)
		return err
	}

	return nil
}

func (d *Dao) AddBusinessConf(tx *gorm.DB, businessConf *BusinessConf) error {
	return d.getDB(tx).Create(businessConf).Error
}

func (d *Dao) DelBusinessConf(businessId uint32) error {
	c := &BusinessConf{BusinessId: businessId, IsDel: true, UpdateTime: time.Now()}
	return d.db.Model(c).Where("business_id=?", businessId).Update(c).Error
}

func (d *Dao) UpdateBusinessConf(businessConf *BusinessConf) error {
	return d.db.Model(businessConf).Where("business_id=? and is_del=?", businessConf.BusinessId, false).Update(businessConf).Error
}

func (d *Dao) GetBusinessConfById(businessId uint32) (businessConf *BusinessConf, err error) {
	businessConf = &BusinessConf{}
	err = d.db.Model(&BusinessConf{}).Where("business_id=? and is_del=?", businessId, false).First(businessConf).Error
	if err != nil && !gorm.IsRecordNotFoundError(err) {
		log.Errorf("GetBusinessConfById fail businessId %d err %v", businessId, err)
		return
	}

	return businessConf, nil
}

func (d *Dao) GetBusinessConfByName(tx *gorm.DB, name string) (businessConf *BusinessConf, err error) {
	businessConf = &BusinessConf{}
	err = d.getDB(tx).Model(&BusinessConf{}).Where("name=? and is_del=?", name, false).First(businessConf).Error
	if err != nil && !gorm.IsRecordNotFoundError(err) {
		log.Errorf("GetBusinessConfByName fail name %s err %v", name, err)
		return
	}

	return businessConf, nil
}

func (d *Dao) GetBusinessConfList(begin, limit uint32) (businessConfList []*BusinessConf, err error) {
	businessConfList = make([]*BusinessConf, 0)
	err = d.db.Find(&businessConfList, "is_del=?", false).Offset(begin).Limit(limit).Error
	if err != nil {
		log.Errorf("GetBusinessConfList fail err %v", err)
		return
	}

	return businessConfList, nil
}

func (d *Dao) GetBusinessConfListTotal() (uint32, error) {
	rows, err := d.db.Model(&BusinessConf{}).Select("count(1)").Where("is_del=?", false).Rows()
	if rows.Err() != nil {
		log.Errorf("GetBusinessConfListTotal fail err %v", err)
		return 0, rows.Err()
	}
	if err != nil {
		log.Errorf("GetBusinessConfListTotal fail err %v", err)
		return 0, err
	}

	defer rows.Close()
	total := uint32(0)
	if rows.Next() {
		_ = rows.Scan(&total)
	}

	return total, nil
}

// 业务奖励配置
type AwardConf struct {
	BusinessId uint32 `gorm:"primary_key" sql:"type:INT(10) UNSIGNED NOT NULL"`
	GiftId     string `gorm:"primary_key" sql:"type:varchar(255) NOT NULL DEFAULT '0'"`
	GiftType   uint32 `gorm:"primary_key" sql:"type:INT(10) UNSIGNED NOT NULL"`
	AwardDesc  string `gorm:"not null;default:''"`
	//DailyNumLimit  uint32 `gorm:"not null;default:0"`
	//HourNumLimit   uint32 `gorm:"not null;default:0"`
	SingleNumLimit     uint32 `gorm:"not null;default:0"`
	DailyAwardCntLimit uint32 `gorm:"not null;default:0"`
}

func (c *AwardConf) TableName() string {
	return "tbl_award_conf"
}

func (d *Dao) CreateAwardConfTable() error {
	err := d.db.Set("gorm:table_options", "ENGINE=InnoDB").CreateTable(&AwardConf{}).Error

	if err != nil {
		log.Errorf("CreateAwardConfTable fail err %v", err)
		return err
	}

	return nil
}

func (d *Dao) AddAwardConf(tx *gorm.DB, awardConf *AwardConf) error {
	return d.getDB(tx).Create(awardConf).Error
}

func (d *Dao) DelAwardConf(businessId uint32, giftId string, giftType uint32) error {
	return d.db.Delete(&AwardConf{}, "business_id=? AND gift_id=? AND gift_type=?", businessId, giftId, giftType).Error
}

func (d *Dao) UpdateAwardConf(awardConf *AwardConf) error {
	return d.db.Model(awardConf).Where("business_id=? AND gift_id=? AND gift_type=?", awardConf.BusinessId, awardConf.GiftId, awardConf.GiftType).
		UpdateColumns(map[string]interface{}{
			"award_desc":            awardConf.AwardDesc,
			"single_num_limit":      awardConf.SingleNumLimit,
			"daily_award_cnt_limit": awardConf.DailyAwardCntLimit,
		}).Error
}

func (d *Dao) GetAwardConf(businessId uint32, giftId string, giftType uint32) (awardConf *AwardConf, exist bool, err error) {
	awardConf = &AwardConf{}
	err = d.db.Model(&AwardConf{}).Where("business_id=? AND gift_id=? AND gift_type=?", businessId, giftId, giftType).First(awardConf).Error
	if err != nil {
		log.Errorf("GetAwardConf fail businessId %d giftId %v giftType %v err %v", businessId, giftId, giftType, err)

		if gorm.IsRecordNotFoundError(err) {
			return awardConf, false, nil
		}
		return awardConf, false, err
	}

	return awardConf, true, nil
}

func (d *Dao) GetAwardConfLimitList(businessId uint32, giftId string, giftType uint32) (awardConfList []*AwardConf, err error) {
	awardConfList = make([]*AwardConf, 0)
	err = d.db.Find(&awardConfList, "business_id=? AND ((gift_id=? AND gift_type=?) OR gift_type=?)",
		businessId, giftId, giftType, DefaultAwardLimitType).Error
	if err != nil {
		log.Errorf("GetAwardConfLimitList fail err %v", err)
		return
	}

	return awardConfList, nil
}

func (d *Dao) GetAllAwardConf() (awardConfList []*AwardConf, err error) {
	awardConfList = make([]*AwardConf, 0)
	err = d.db.Find(&awardConfList).Error
	if err != nil {
		log.Errorf("GetAwardConf fail err %v", err)
		return
	}

	return awardConfList, nil
}

func (d *Dao) GetAwardConfList(businessId, giftType, begin, limit uint32) (awardConfList []*AwardConf, err error) {
	awardConfList = make([]*AwardConf, 0)
	if limit == 0 {
		return
	}

	whereSql := fmt.Sprintf("business_id=%d", businessId)
	if giftType != 0 {
		whereSql = fmt.Sprintf("%s AND gift_type=%d", whereSql, giftType)
	}

	err = d.db.Find(&awardConfList, whereSql).Offset(begin).Limit(limit).Error
	if err != nil {
		log.Errorf("GetAwardConfList fail err %v", err)
		return
	}

	return awardConfList, nil
}

func (d *Dao) GetAwardConfListTotal(businessId, giftType uint32) (uint32, error) {
	whereSql := fmt.Sprintf("business_id=%d", businessId)
	if giftType != 0 {
		whereSql = fmt.Sprintf("%s AND gift_type=%d", whereSql, giftType)
	}

	rows, err := d.db.Model(&AwardConf{}).Select("count(1)").Where(whereSql).Rows()
	if rows.Err() != nil {
		log.Errorf("GetBusinessConfListTotal fail err %v", err)
		return 0, rows.Err()
	}
	if err != nil {
		log.Errorf("GetAwardConfListTotal fail err %v", err)
		return 0, err
	}

	defer rows.Close()
	total := uint32(0)
	if rows.Next() {
		_ = rows.Scan(&total)
	}

	return total, nil
}

type AwardOrder struct {
	Id          uint32    `gorm:"primary_key" json:"id,omitempty"`
	OrderId     string    `gorm:"unique_index:order_idx" json:"order_id,omitempty"`
	TargetUid   uint32    `gorm:"index:uid_idx;not null" json:"target_uid,omitempty"`
	BusinessId  uint32    `gorm:"index:business_gift_idx;not null" json:"business_id,omitempty"`
	GiftId      string    `gorm:"index:business_gift_idx;not null" json:"gift_id,omitempty"`
	GiftType    uint32    `gorm:"index:business_gift_idx;not null" json:"gift_type,omitempty"`
	Status      uint32    `gorm:"index:status_idx;not null;default:0" json:"status,omitempty"`
	HoldingDay  uint32    `gorm:"not null;default:0" json:"holding_day,omitempty"`
	UpdateTime  time.Time `gorm:"not null" json:"update_time,omitempty"`
	OutsideTime time.Time `gorm:"index:outside_time_idx;not null;default:CURRENT_TIMESTAMP" json:"outside_time,omitempty"`
	CreateTime  time.Time `gorm:"index:create_time_idx;not null;default:CURRENT_TIMESTAMP" json:"create_time,omitempty"`
	Extra       string    `json:"extra,omitempty"`
	SourceType  uint32    `gorm:"index:source_type_idx;not null;default:0" json:"source_type,omitempty"`
	ExpireType  uint32    `gorm:"not null;default:0" json:"expire_type,omitempty"`
	AutoWear    bool      `gorm:"not null;default:0" json:"auto_wear,omitempty"`
}

func (a *AwardOrder) TableName() string {
	return fmt.Sprintf("tbl_award_monthly_order_%s", a.OutsideTime.Format("200601"))
}

func (d *Dao) AddAwardOrder(tx *gorm.DB, awardOrder *AwardOrder) error {
	db := d.getDB(tx)

	/*awardOrder := &AwardOrder{
		OrderId:     order.GetOrderId(),
		TargetUid:   order.GetTargetUid(),
		BusinessId:  order.GetBusinessId(),
		GiftId:      order.GetGiftId(),
		GiftType:    order.GetGiftType(),
		HoldingDay:  order.GetHoldingDay(),
		Status:      order.GetAwardStatus(),
		OutsideTime: time.Unix(int64(order.GetOutsideTs()), 0),
	}*/

	// 插入数据
	err := db.Create(awardOrder).Error
	if err != nil {
		if strings.Contains(err.Error(), "Error 1146") {
			// 建表
			err := db.Set("gorm:table_options", "ENGINE=InnoDB").CreateTable(awardOrder).Error
			if err != nil {
				log.Errorf("AddAwardOrder CreateTable fail err %v", err)
				return err
			}

			log.Debugf("AddAwardOrder CreateTable table name %s", awardOrder.TableName())
			return db.Create(awardOrder).Error
		}

		log.Errorf("AddAwardOrder fail err %v", err)
		return err
	}

	log.Debugf("AddAwardOrder awardOrder %+v", awardOrder)
	return nil
}

// 更新订单状态
func (d *Dao) UpdateAwardOrderStatus(orderId string, preStatus, status uint32, outsideTime time.Time) error {
	now := time.Now()

	// 本月
	r := d.db.Model(&AwardOrder{OutsideTime: outsideTime}).Where("order_id=? and status=?", orderId, preStatus).
		UpdateColumns(map[string]interface{}{"status": status, "update_time": now})
	if r.Error != nil {
		log.Errorf("UpdateAwardOrderStatus fail orderId %+v err:%v", orderId, r.Error)
		return r.Error

	} else if r.RowsAffected > 0 {
		log.Debugf("UpdateAwardOrderStatus orderId %+v  status %v", orderId, status)
		return nil
	}

	log.Errorf("UpdateAwardOrderStatus orderId %+v status %v order not found", orderId, pb.EAwardStatus_Handling)
	return nil
}

// 获取还在处理中的任务（最多查两个月）
func (d *Dao) GetHandlingAwardOrderList(begin time.Time) ([]*AwardOrder, error) {
	awardOrderList := make([]*AwardOrder, 0, 1000)
	now := time.Now()

	// 查这个月
	err := d.db.Model(&AwardOrder{OutsideTime: now}).Where("status=? and outside_time>=?", uint32(pb.EAwardStatus_Handling), begin).
		Select("*").Scan(&awardOrderList).Error
	if err != nil {
		log.Errorf("GetHandlingAwardOrder Scan fail err %v", err)
		return awardOrderList, err
	}

	if begin.Month() != now.Month() {
		// 查上个月
		lastMonth := time.Date(now.Year(), time.Month(int(now.Month())-1), 1, 0, 0, 0, 0, time.Local)

		err = d.db.Model(&AwardOrder{OutsideTime: lastMonth}).Where("status=? and outside_time>=?", uint32(pb.EAwardStatus_Handling), begin).
			Select("*").Scan(&awardOrderList).Error
		if err != nil {
			log.Errorf("GetHandlingAwardOrder Scan fail err %v", err)
			return awardOrderList, err
		}
	}

	/*orderList = make([]*pb.AwardOrder, 0, len(AwardOrderList))
	for _, order := range AwardOrderList {
		orderList = append(orderList, &pb.AwardOrder{
			OrderId:     order.OrderId,
			TargetUid:   order.TargetUid,
			BusinessId:  order.BusinessId,
			GiftType:    order.GiftType,
			GiftId:      order.GiftId,
			HoldingDay:  order.HoldingDay,
			AwardStatus: order.Status,
			OutsideTs:   uint32(order.OutsideTime.Unix()),
			CreateTs:    uint32(order.CreateTime.Unix()),
			UpdateTs:    uint32(order.UpdateTime.Unix()),
		})
	}*/

	log.Debugf("GetHandlingAwardOrder %+v begin %v len %d", awardOrderList, begin, len(awardOrderList))
	return awardOrderList, nil
}

// 获取近两个月还在处理中的orderId
func (d *Dao) GetHandlingOrderId() (list []string, err error) {
	list = make([]string, 0, 1000)
	now := time.Now()

	// 查这个月
	rows, _ := d.db.Model(&AwardOrder{OutsideTime: now}).Select("order_id").
		Where("status=? ", uint32(pb.EAwardStatus_Handling)).Rows()
	if rows.Err() != nil {
		log.Errorf("GetBusinessConfListTotal fail err %v", err)
		return list, rows.Err()
	}
	if rows != nil {
		defer rows.Close()

		for rows.Next() {
			orderId := ""
			err := rows.Scan(&orderId)
			if err != nil {
				log.Errorf("GetHandlingOrderId Scan fail err %v", err)
			}

			list = append(list, orderId)
		}
	}

	// 查上个月
	lastMonth := time.Date(now.Year(), time.Month(int(now.Month())-1), 1, 0, 0, 0, 0, time.Local)
	lrows, _ := d.db.Model(&AwardOrder{OutsideTime: lastMonth}).Select("order_id").
		Where("status=?", uint32(pb.EAwardStatus_Handling)).Rows()
	if lrows.Err() != nil {
		log.Errorf("GetBusinessConfListTotal fail err %v", lrows.Err())
		return list, lrows.Err()
	}
	if lrows != nil {
		defer lrows.Close()

		for lrows.Next() {
			orderId := ""
			err := lrows.Scan(&orderId)
			if err != nil {
				log.Errorf("GetHandlingOrderId Scan fail err %v", err)
			}

			list = append(list, orderId)
		}
	}

	log.Debugf("GetHandlingOrderId %+v len %d", list, len(list))
	return list, nil
}

// 查一个月的某条记录
func (d *Dao) GetAwardOrderByOrderId(orderId string, time time.Time) (*AwardOrder, error) {
	awardLog := &AwardOrder{OutsideTime: time}

	tx := d.db.Where("order_id=?", orderId).First(awardLog)
	if tx.Error != nil &&
		!gorm.IsRecordNotFoundError(tx.Error) &&
		!strings.Contains(tx.Error.Error(), "Error 1146") {

		log.Errorf("GetAwardOrderByOrderId fail err %v", tx.Error)
		return nil, tx.Error
	}

	var cnt uint32 = 0
	tx.Count(&cnt)
	if cnt == 0 {
		return nil, nil
	}

	log.Debugf("GetAwardOrderByOrderId awardLog %+v", awardLog)
	return awardLog, nil
}

// GetAwardOrderBySource 对账用
func (d *Dao) GetAwardOrderBySource(sourceType, giftType uint32, begin, end time.Time) ([]*AwardOrder, error) {
	awardLog := make([]*AwardOrder, 0)

	tx := d.db.Table(fmt.Sprintf("tbl_award_monthly_order_%s", begin.Format("200601"))).
		Where("source_type=? and gift_type = ? and outside_time>= ? and outside_time < ?", sourceType,
			giftType, begin, end).Find(&awardLog)
	if tx.Error != nil &&
		!gorm.IsRecordNotFoundError(tx.Error) &&
		!strings.Contains(tx.Error.Error(), "Error 1146") {

		log.Errorf("GetAwardOrderByOrderId fail err %v", tx.Error)
		return nil, tx.Error
	}

	log.Debugf("GetAwardOrderByOrderId awardLog %+v", awardLog)
	return awardLog, nil
}

// 查近n个月的某条记录
// 数据库只保留了近半年的数据，故 n 不能大于6
func (d *Dao) GetAwardLogInNMonths(orderId string, n int) (*AwardOrder, error) {
	if n <= 0 {
		n = 1
	} else if n > 6 {
		n = 6
	} else {
		log.Infof("GetAwardLogInNMonths n should be in [1, 6]")
	}

	now := time.Now()
	monthTime := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.Local)

	// 查这n个月的表
	for i := 0; i < n; i++ {
		// 上一个月
		t := monthTime.AddDate(0, -i, 0)

		awardLog, err := d.GetAwardOrderByOrderId(orderId, t)
		if err != nil {
			log.Errorf("GetAwardLogInNMonths fail orderId:%s err:%v", orderId, err)
			return nil, err
		}

		if awardLog != nil {
			log.Debugf("GetAwardLogInNMonths orderId %s N：%d awardLog %+v", orderId, n, awardLog)
			return awardLog, nil
		}
	}

	log.Debugf("GetAwardLogInNMonths N：%d orderId %s is not found log", n, orderId)
	return nil, nil
}

type AwardTotalStats struct {
	Id         uint32    `gorm:"primary_key"`
	BusinessId uint32    `gorm:"unique_index:business_gift_idx"`
	GiftId     string    `gorm:"unique_index:business_gift_idx"`
	GiftType   uint32    `gorm:"unique_index:business_gift_idx"`
	TimeKey    time.Time `gorm:"unique_index:business_gift_idx"`
	Cnt        uint64    `gorm:"not null;default:0"`
}

func (c *AwardTotalStats) TableName() string {
	return "tbl_award_cnt_record"
}

func (d *Dao) IncrAwardStats(tx *gorm.DB, businessId uint32, giftId string, giftType uint32, cnt uint32, t time.Time) error {
	db := d.getDB(tx)

	query := fmt.Sprintf("insert into %s (business_id,gift_id,gift_type,cnt,time_key) values(?,?,?,?,?) on duplicate key update cnt=cnt+?",
		(&AwardTotalStats{}).TableName())

	err := db.Exec(query, businessId, giftId, giftType, cnt, t, cnt).Error
	if err != nil {
		log.Errorf("IncrAwardStats fail. businessId:%v, GiftId:%v, giftType:%v, cnt:%v, timeKey:%v err:%v", businessId, giftId, giftType, cnt, t, err)
		return err
	}

	log.Debugf("IncrAwardStats businessId:%v, GiftId:%v, giftType:%v, cnt:%v, timeKey:%v", businessId, giftId, giftType, cnt, t)
	return nil
}

func (d *Dao) GetAwardStats(tx *gorm.DB, businessId uint32, giftId string, giftType uint32, t time.Time) (cnt uint32, err error) {
	db := d.getDB(tx)

	err = db.Model(&AwardTotalStats{}).Select("cnt").
		Where("business_id=? and gift_id=? and gift_type=? and time_key = ?", businessId, giftId, giftType, t).Row().Scan(&cnt)
	if err != nil {
		log.Errorf("GetAwardStats fail. businessId:%v, GiftId:%v, giftType:%v, t:%v err:%v", businessId, giftId, giftType, t, err)
		return
	}

	log.Debugf("GetAwardStats businessId:%v, GiftId:%v, giftType:%v, t:%v  cnt:%v", businessId, giftId, giftType, t, cnt)
	return cnt, nil
}

func (d *Dao) GetAwardSumBetweenTime(tx *gorm.DB, businessId uint32, giftId string, giftType uint32, begin, end time.Time) (cnt uint64, err error) {
	db := d.getDB(tx)

	err = db.Model(&AwardTotalStats{}).Select("sum(cnt) as cnt").
		Where("business_id=? and gift_id=? and gift_type=? and time_key >= ? and time_key < ?", businessId, giftId, giftType, begin, end).Row().Scan(&cnt)
	if err != nil {
		log.Errorf("GetAwardSumBetweenTime fail. businessId:%v, GiftId:%v, giftType:%v, begin:%v, end:%v err:%v", businessId, giftId, giftType, begin, end, err)
		return
	}

	log.Debugf("GetAwardSumBetweenTime businessId:%v, GiftId:%v, giftType:%v, begin:%v, end:%v  cnt:%v", businessId, giftId, giftType, begin, end, cnt)
	return cnt, nil
}

func (d *Dao) ClearAwardCntRecordBeforeTime(before time.Time) error {
	return d.db.Delete(&AwardTotalStats{}, "time_key < ?", before).Error
}

func (d *Dao) Close() {
	_ = d.db.Close()
}
