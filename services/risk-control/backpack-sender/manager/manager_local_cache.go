package manager

import (
	"context"
	"fmt"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	bpb "golang.52tt.com/protocol/services/backpack-base"
	upb "golang.52tt.com/protocol/services/userpresent"
	"golang.52tt.com/services/risk-control/backpack-sender/mysql"
	"sync"
	"time"
)

var localCacheFragmentCfg = sync.Map{}
var localCacheBackpackVal = sync.Map{}
var localCacheBackpackCfg = sync.Map{}
var localCachePresentCfg = sync.Map{}
var localCachePackItemList = sync.Map{}
var localCacheBusinessConf = sync.Map{}
var localCacheRiskControlConf = sync.Map{}

func getBackpackCfg(ctx context.Context, bgId uint32) (*bpb.PackageCfg, protocol.ServerError) {
	if bgId == 0 {
		return nil, protocol.NewServerError(status.ErrRequestParamInvalid, "invalid bgId")
	}
	v, ok := localCacheBackpackCfg.Load(bgId)
	if ok {
		cfg, ok := v.(*bpb.PackageCfg)
		if ok {
			return cfg, nil
		}
	}

	updatePackCfg(ctx)

	v, ok = localCacheBackpackCfg.Load(bgId)
	if ok {
		cfg, ok := v.(*bpb.PackageCfg)
		if ok {
			return cfg, nil
		}
	}

	return nil, protocol.NewServerError(status.ErrRequestParamInvalid, "packCfg not found ")
}

func updatePackCfg(ctx context.Context) ([]*bpb.PackageCfg, protocol.ServerError) {
	packRsp, err := backpackBaseCli.GetPackageCfg(ctx, &bpb.GetPackageCfgReq{})
	if nil != err {
		log.ErrorWithCtx(ctx, "updatePackCfg GetPackageCfg err:%v", err)
		return nil, err
	}

	for _, packCfg := range packRsp.GetCfgList() {
		localCacheBackpackCfg.Store(packCfg.GetBgId(), packCfg)
	}
	return packRsp.GetCfgList(), nil
}

func (m *Manager) UpdateLocalCache() {
	ctx, cancel := context.WithTimeout(context.Background(), time.Minute*5)
	defer cancel()
	now := time.Now()
	log.InfoWithCtx(ctx, "UpdateLocalCache start :%v", now)

	//更新礼物配置
	updatePresentCfg(ctx)
	//更新碎片配置
	updateFramentCfg(ctx)

	//更新业务配置
	businessList, err := m.updateBusinessCfg(ctx)
	if nil != err {
		log.ErrorWithCtx(ctx, "UpdateLocalCache updateBusinessCfg err:%v", err)
		return
	}
	for _, businessCfg := range businessList {
		_, err := m.updateControlConfList(ctx, businessCfg.BusinessID)
		if nil != err {
			log.ErrorWithCtx(ctx, "UpdateLocalCache updateControlConfList bid:%v err:%v", businessCfg.BusinessID, err)
			continue
		}
		//log.ErrorWithCtx(ctx, "UpdateLocalCache updateControlConfList bid:%v controlCfgList:%+v", businessCfg.BusinessID, controlCfgList)
	}

	//刷新包裹配置缓存
	packCfgList, err := updatePackCfg(ctx)
	if nil != err {
		log.ErrorWithCtx(ctx, "UpdateLocalCache updatePackCfg err:%v", err)
		return
	}

	//刷新所有包裹物品项的配置
	bgIds := make([]uint32, 0, 200)
	for _, packCfg := range packCfgList {
		bgIds = append(bgIds, packCfg.GetBgId())
		if len(bgIds) == 200 {
			_ = batchUpdatePackItemList(ctx, bgIds)
			bgIds = make([]uint32, 0, 200)
		}
	}
	if len(bgIds) > 0 {
		_ = batchUpdatePackItemList(ctx, bgIds)
	}

	//更新包裹价值
	for _, packCfg := range packCfgList {
		packVal, err := updatePackValue(ctx, packCfg.GetBgId())
		if nil != err {
			log.ErrorWithCtx(ctx, "UpdateLocalCache updatePackValue err:%v", err)
			continue
		}
		log.DebugWithCtx(ctx, "UpdateLocalCache updatePackItemList bgId:%v packVal:%v ", packCfg.GetBgId(), packVal)
	}
	log.InfoWithCtx(ctx, "UpdateLocalCache end :%v, businessSize:%d, packSize:%d", now, len(businessList), len(packCfgList))
}

func updatePackItemList(ctx context.Context, bgId uint32) ([]*bpb.PackageItemCfg, protocol.ServerError) {
	resp, err := backpackBaseCli.GetPackageItemCfg(ctx, bgId)
	if nil != err {
		log.ErrorWithCtx(ctx, "GetBackPackItemList GetPackageItemCfg backpackId:%v err:%v", bgId, err)
		return nil, err
	}
	localCachePackItemList.Store(bgId, resp.GetItemCfgList())
	return resp.GetItemCfgList(), nil
}

// 更新所有包裹物品项
func batchUpdatePackItemList(ctx context.Context, bgIds []uint32) protocol.ServerError {
	resp, err := backpackBaseCli.GetPackageItemCfgV2(ctx, &bpb.GetPackageItemCfgReq{
		BgIdList: bgIds,
	})
	if nil != err {
		log.ErrorWithCtx(ctx, "batchUpdatePackItemList GetPackageItemCfgV2 err:%v", err)
		return err
	}
	for i, cfgList := range resp.PackageItemCfgList {
		bgId := bgIds[i]
		localCachePackItemList.Store(bgId, cfgList.GetItemCfgList())
	}
	return nil
}

func getBackPackItemList(ctx context.Context, bgId uint32) ([]*bpb.PackageItemCfg, protocol.ServerError) {
	if v, ok := localCachePackItemList.Load(bgId); ok {
		if itemList, ok := v.([]*bpb.PackageItemCfg); ok {
			return itemList, nil
		}
	}
	updatePackItemList(ctx, bgId)
	if v, ok := localCachePackItemList.Load(bgId); ok {
		if itemList, ok := v.([]*bpb.PackageItemCfg); ok {
			return itemList, nil
		}
	}
	return nil, protocol.NewServerError(status.ErrRequestParamInvalid, "empty backpack")
}

func getPresentCfg(ctx context.Context, presentId uint32) (*upb.StPresentItemConfig, protocol.ServerError) {
	if v, ok := localCachePresentCfg.Load(presentId); ok {
		if presentCfg, ok := v.(*upb.StPresentItemConfig); ok {
			return presentCfg, nil
		}
	}
	updatePresentCfg(ctx)
	if v, ok := localCachePresentCfg.Load(presentId); ok {
		if presentCfg, ok := v.(*upb.StPresentItemConfig); ok {
			return presentCfg, nil
		}
	}
	return nil, protocol.NewServerError(status.ErrRequestParamInvalid, "present cfg not found")
}

func updatePresentCfg(ctx context.Context) protocol.ServerError {
	rsp, err := userpresentCli.GetPresentConfigList(ctx)
	if nil != err {
		log.ErrorWithCtx(ctx, "updatePresentCfg GetPresentConfigList err:%v", err)
		return protocol.NewServerError(status.ErrRequestParamInvalid, err.Error())
	}
	for _, presentCfg := range rsp.GetItemList() {
		localCachePresentCfg.Store(presentCfg.GetItemId(), presentCfg)
	}
	return nil
}

func getItemVal(ctx context.Context, item *bpb.PackageItemCfg) (uint32, protocol.ServerError) {
	if item.GetItemType() == uint32(bpb.PackageItemType_BACKPACK_PRESENT) {
		if item.GetIsDel() {
			return 0, nil
		}
		presentCfg, err := getPresentCfg(ctx, item.GetSourceId())
		if nil != err {
			log.ErrorWithCtx(ctx, "itemValFunc GetPresentConfigById sourceId:%v err:%v", item.SourceId, err)
			return 0, err
		}
		if upb.PresentPriceType(presentCfg.GetPriceType()) == upb.PresentPriceType_PRESENT_PRICE_TBEAN {
			return presentCfg.GetPrice() * item.ItemCount, nil
		}
	} else if item.GetItemType() == uint32(bpb.PackageItemType_BACKPACK_LOTTERY_FRAGMENT) {
		fragmentCfg, err := getFragmentCfg(ctx, item.GetSourceId())
		if nil != err {
			return 0, err
		}
		return fragmentCfg.GetFragmentPrice() * item.ItemCount, nil
	} else {
		log.ErrorWithCtx(ctx, "getItemVal type sourceId:%v type:%v", item.SourceId, item.GetItemType())
		return 0, nil
	}
	return 0, nil
}

func getPackValue(ctx context.Context, bgId uint32) (uint32, protocol.ServerError) {
	//短缓存里面有直接返回
	val, ok := localCacheBackpackVal.Load(bgId)
	if ok {
		return val.(uint32), nil
	}
	return updatePackValue(ctx, bgId)
}

func updatePackValue(ctx context.Context, bgId uint32) (uint32, protocol.ServerError) {
	itemList, err := getBackPackItemList(ctx, bgId)
	if nil != err {
		return 0, err
	}
	var packVal uint32 = 0
	for _, item := range itemList {
		itemVal, err := getItemVal(ctx, item)
		if nil != err {
			return 0, err
		}
		packVal = packVal + itemVal
	}

	localCacheBackpackVal.Store(bgId, packVal)
	return packVal, nil
}

func updateFramentCfg(ctx context.Context) {
	resp, err := backpackBaseCli.GetItemCfg(context.Background(), &bpb.GetItemCfgReq{
		ItemType: uint32(bpb.PackageItemType_BACKPACK_LOTTERY_FRAGMENT),
		GetAll:   true,
	})

	if nil != err {
		return
	}

	for _, str := range resp.GetItemCfgList() {
		item := &bpb.LotteryFragmentCfg{}
		err := proto.Unmarshal(str, item)
		if nil != err {
			log.ErrorWithCtx(ctx, "GetFragmentTbeamVal Unmarshal err:%v", err)
			continue
		}
		localCacheFragmentCfg.Store(item.FragmentId, item)

		log.InfoWithCtx(ctx, "GetFragmentTbeamVal %+v\n", item)
	}
}

func getFragmentCfg(ctx context.Context, fid uint32) (*bpb.LotteryFragmentCfg, protocol.ServerError) {
	v, ok := localCacheFragmentCfg.Load(fid)
	if ok {
		fragment, ok := v.(*bpb.LotteryFragmentCfg)
		if ok {
			return fragment, nil
		}
	}

	updateFramentCfg(ctx)

	v, ok = localCacheFragmentCfg.Load(fid)
	if ok {
		fragment, ok := v.(*bpb.LotteryFragmentCfg)
		if ok {
			return fragment, nil
		}
	}

	return nil, protocol.NewServerError(status.ErrRequestParamInvalid, fmt.Sprintf("fragment %v not found", fid))
}

func (m *Manager) getBusinessCfg(ctx context.Context, bid uint32) (*mysql.BusinessConf, protocol.ServerError) {
	if v, ok := localCacheBusinessConf.Load(bid); ok {
		if businessCfg, ok := v.(*mysql.BusinessConf); ok {
			return businessCfg, nil
		}
	}

	m.updateBusinessCfg(ctx)

	if v, ok := localCacheBusinessConf.Load(bid); ok {
		if businessCfg, ok := v.(*mysql.BusinessConf); ok {
			return businessCfg, nil
		}
	}

	return nil, protocol.NewServerError(status.ErrRequestParamInvalid, fmt.Sprintf("business %v not found", bid))

}

func (m *Manager) updateBusinessCfg(ctx context.Context) ([]*mysql.BusinessConf, protocol.ServerError) {
	businessList, err := m.mysqlStore.GetBusiness(ctx, 0)
	if nil != err {
		return nil, protocol.NewServerError(status.ErrRequestParamInvalid, err.Error())
	}

	for _, businessCfg := range businessList {
		localCacheBusinessConf.Store(businessCfg.BusinessID, businessCfg)
	}
	return businessList, nil
}

func (m *Manager) GetControlConfList(ctx context.Context, bid uint32) ([]*mysql.RiskControlConf, protocol.ServerError) {
	if v, ok := localCacheRiskControlConf.Load(bid); ok {
		if res, ok := v.([]*mysql.RiskControlConf); ok {
			return res, nil
		}
	}
	return m.updateControlConfList(ctx, bid)
}

func (m *Manager) GetControlConfMap(ctx context.Context, bid uint32) (map[uint32]*mysql.RiskControlConf, error) {
	rc, err := m.GetControlConfList(ctx, bid)
	if nil != err {
		log.ErrorWithCtx(ctx, "GetControlConfMap GetControlConfList bid:%d err:%v", bid, err)
		return nil, err
	}
	controlConfMap := make(map[uint32]*mysql.RiskControlConf, len(rc))
	for _, conf := range rc {
		controlConfMap[conf.Id] = conf
	}
	return controlConfMap, nil
}

func (m *Manager) updateControlConfList(ctx context.Context, bid uint32) ([]*mysql.RiskControlConf, protocol.ServerError) {
	controlConfList, err := m.mysqlStore.GetBusinessRiskControlConf(ctx, bid)
	if nil != err {
		log.ErrorWithCtx(ctx, "CheckRiskControl GetBusinessRiskControlConf not found orderInfo:%v err:%v", err, bid)
		return nil, protocol.NewServerError(status.ErrRiskControlBackpackConfigNotFound, "找不到对应业务风控配置")
	}
	localCacheRiskControlConf.Store(bid, controlConfList)

	return controlConfList, nil
}
