package server

// TODO：线上 mysql 存中文直接返回会出问题，先这样转换一下
func transDecName(decName string) string {
	m := map[string]string{
		"fanxingqiji":       "繁星奇迹",
		"qicaimaoxian":      "七彩冒险",
		"yingxueliange":     "樱雪恋歌",
		"youyuantonghua":    "游园童话",
		"luoyingbinfen":     "落樱缤纷",
		"tianshizhiyi":      "天使之翼",
		"tongqumeimeng":     "童趣美梦",
		"aoyoutaikong":      "遨游太空",
		"caomeixiong":       "草莓熊",
		"tongxinbugai":      "童心不改",
		"tongxinweimin":     "童心未泯",
		"yunitongxing":      "与你同行",
		"huangyeluandou":    "荒野乱斗",
		"anyingemo":         "暗影恶魔",
		"zongyouduanwu":     "粽游端午",
		"qiangqidazhan":     "抢期大战",
		"zhouniankuanghuan": "周年狂欢",
		"shengrikuaile":     "生日快乐",
		"shengripaidui":     "生日派对",
		"fentuanjiadao":     "粉团驾到",
		"xiari":             "夏日",
		"fangjiale":         "放假了",
		"yingyuanhui":       "应援会",
		"xingfushuwu":       "幸福书屋",
		"shenghuojilujia":   "生活记录家",
		"aixinqiji":         "爱心奇迹",
		"chaorenqiwutai":    "超人气舞台",
		"PKsai":             "PK赛",
	}
	var r string
	var ok bool
	if r, ok = m[decName]; !ok {
		return decName
	}
	return r
}
