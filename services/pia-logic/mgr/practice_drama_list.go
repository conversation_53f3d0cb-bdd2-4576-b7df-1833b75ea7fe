package mgr

import (
	"context"
	"errors"
	"golang.52tt.com/clients/account"
	"golang.52tt.com/clients/channel"
	channelol_stat_go "golang.52tt.com/clients/channelol-stat-go"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/protocol/app/pia"
	pia_pb "golang.52tt.com/protocol/services/pia"
	"golang.52tt.com/services/pia-logic/conf"
	"golang.org/x/sync/errgroup"
	"golang.org/x/sync/singleflight"
)

var (
	ErrPracticeFailToFill   = errors.New("填充排练室出错误")
	ErrPracticeFailToFilter = errors.New("过滤排练室出错误")
)

type PracticeChannelFilter func(ctx context.Context, target *pia.GetPracticeDramaListResp) error

type PracticeChannelFiller func(ctx context.Context, target *pia.GetPracticeDramaListResp) error

type PracticeDramaList struct {
	channelTopConfigMgr *ChannelTopConfig
	recommandChannel    *RecommandChannel
	piaCli              pia_pb.PiaClient
	accountCli          account.IClient
	channelCli          channel.IClient
	channelOlCli        channelol_stat_go.IClient
	aggConf             *conf.AggregatePage
	sg                  *singleflight.Group
}

func NewPracticeDramaList(
	ChannelTopConfigMgr *ChannelTopConfig,
	recommandChannel *RecommandChannel,
	piaCli pia_pb.PiaClient,
	accountCli account.IClient,
	channelCli channel.IClient,
	channelOlCli channelol_stat_go.IClient,
	aggConf *conf.AggregatePage,
) IPracticeDramaList {
	return &PracticeDramaList{
		channelTopConfigMgr: ChannelTopConfigMgr,
		recommandChannel:    recommandChannel,
		piaCli:              piaCli,
		accountCli:          accountCli,
		channelCli:          channelCli,
		channelOlCli:        channelOlCli,
		aggConf:             aggConf,
		sg:                  &singleflight.Group{},
	}
}

// GetPracticeDramaList 获取排练房间列表
// 从推荐那边拿数据，拿不到就本地走兜底逻辑
// 1、获取全部运营后台配置的UGC置顶房间
// 2、调用推荐服务获取房间
// 3、过滤排练房间
// 4、调用pia戏服务获取房间的pia系剧本信息
// 5、调用房间服务获取房间信息，调用账号服务获取用户的信息 *
// 6、调用房间在线服务获取房间在线人数 *（3、4、5并发执行）
// 7、组装数据返回
func (p *PracticeDramaList) GetPracticeDramaList(c context.Context, req *pia.GetPracticeDramaListReq) (*pia.GetPracticeDramaListResp, error) {
	log.Debugf("GetPracticeDramaList req:%+v", req)
	// 1、解析page token
	pageToken := NewPageToken(req.PageToken)
	log.DebugWithCtx(c, "1、解析page token，pageToken:%+v", pageToken)
	do, err, shared := p.sg.Do(pageToken.String(), func() (interface{}, error) {
		return p.getPracticeDramaList(c, req, pageToken)
	})
	if err != nil {
		return nil, err
	}
	if shared {
		log.DebugWithCtx(c, "请求使用共享数据，进入了singlefight.Do，req: %+v", req)
	}
	return do.(*pia.GetPracticeDramaListResp), nil
}

func (p *PracticeDramaList) getPracticeDramaList(c context.Context, req *pia.GetPracticeDramaListReq, pageToken *PageToken) (*pia.GetPracticeDramaListResp, error) {
	// 2、获取UGC房间列表
	channels, isEnd, err := p.getUgcChannels(c, pageToken)
	if err != nil {
		log.Errorf("getUgcChannels error(%v),req: %+v", err, req)
		return nil, err
	}
	log.DebugWithCtx(c, "2、获取UGC房间列表，参数：%+v，返回值: channels: %+v,isEnd: %s,", pageToken, channels, isEnd)
	// 生成page token
	token := ""
	if !isEnd {
		pageToken.PageNum++
		token = pageToken.String()
	}
	// 生成返回数据，建立骨架
	result := &pia.GetPracticeDramaListResp{
		List:          make([]*pia.PracticeDrama, len(channels)),
		NextPageToken: token,
		TabId:         p.aggConf.GetUgcPiaTabId(),
	}
	for i := 0; i < len(channels); i++ {
		result.List[i] = &pia.PracticeDrama{
			BaseInfo: &pia.DramaRoomBaseInfo{
				RoomInfo: &pia.DramaRoom{
					Id:   channels[i].Id,
					Tags: channels[i].Tag,
				},
				TabId: result.TabId,
			},
		}
	}
	// 填充返回数据，填充骨架
	err = p.fillUgcChannels(c, result)
	if err != nil {
		log.Errorf("fillUgcChannels error(%v),req: %+v", err, req)
		return nil, err
	}
	// 过滤返回数据
	err = p.filterUgcChannels(c, pageToken, result)
	if err != nil {
		log.Errorf("filterUgcChannels error(%v),req: %+v", err, req)
		return nil, err
	}

	return result, nil
}

//getUgcChannels 获取UGC的房间
func (p *PracticeDramaList) getUgcChannels(ctx context.Context, token *PageToken) (_list ChannelInfoList, _isEnd bool, err error) {
	result := make(ChannelInfoList, 0, 20)
	// 调用推荐服务获取房间
	ugcRecommandChannelResult, err := p.recommandChannel.GetUgcRecommandChannel(ctx, token)
	if err != nil {
		log.Errorf("调用推荐服务获取房间失败，err:%+v", err)
		return nil, true, err
	}
	// 去重
	indexMap := make(map[uint32]bool)
	for _, channelId := range ugcRecommandChannelResult.ChannelIds {
		if _, ok := indexMap[channelId]; !ok {
			indexMap[channelId] = true
			result = append(result, &ChannelInfo{
				Id: channelId,
			})
		}
	}
	return result, ugcRecommandChannelResult.IsEnd, nil
}

//fillUgcChannels 填充返回数据
func (p *PracticeDramaList) fillUgcChannels(ctx context.Context, result *pia.GetPracticeDramaListResp) error {
	// 填充返回数据
	fillers := []PracticeChannelFiller{
		WithDramaFiller(p.piaCli),
		WithOnlineNumFiller(p.channelOlCli),
		WithChannelInfoFiller(p.accountCli, p.channelCli),
	}
	eg, cancelCtx := errgroup.WithContext(ctx)
	for i := range fillers {
		filler := fillers[i]
		eg.Go(func() error {
			err := filler(cancelCtx, result)
			if err != nil {
				return ErrPracticeFailToFill
			}
			return nil
		})
	}
	return eg.Wait()
}

//filterUgcChannels 过滤返回数据
func (p *PracticeDramaList) filterUgcChannels(ctx context.Context, pageToken *PageToken, result *pia.GetPracticeDramaListResp) error {
	// 去掉人数为0的数据
	for i := 0; i < len(result.List); i++ {
		if result.List[i].BaseInfo.RoomInfo.GetUserNum() == 0 {
			result.List = append(result.List[:i], result.List[i+1:]...)
			i--
		}
	}
	return nil
}

//WithDramaFiller 房间剧本信息填充器
func WithDramaFiller(piaCli pia_pb.PiaClient) PracticeChannelFiller {
	return func(ctx context.Context, result *pia.GetPracticeDramaListResp) error {
		log.DebugWithCtx(ctx, "填充房间剧本信息")
		if len(result.List) == 0 {
			return nil
		}
		channelIds := make([]uint32, len(result.List))
		for i := 0; i < len(result.List); i++ {
			channelIds[i] = result.List[i].BaseInfo.RoomInfo.Id
		}
		resp, err := piaCli.GetChannelDramaInfos(ctx, &pia_pb.GetChannelDramaInfosReq{
			ChannelIds: channelIds,
		})
		if err != nil {
			log.Errorf("调用pia戏服务获取房间的pia系剧本信息失败，err:%+v", err)
			return err
		}
		infos := resp.GetDramaInfos()
		for i := 0; i < len(result.List); i++ {
			result.List[i].BaseInfo.DramaPhase = pia.PiaPhaseType(infos[i].GetDramaPhase())
			result.List[i].BaseInfo.DramaInfo = &pia.DramaInfoForAggPage{
				Id:      infos[i].GetId(),
				Name:    infos[i].GetName(),
				Type:    infos[i].GetType(),
				Tags:    infos[i].GetTags(),
				Summary: infos[i].GetSummary(),
			}
			switch infos[i].GetDramaPhase() {
			case pia_pb.DramaPhase_DRAMA_PHASE_UNSPECIFIED:
				result.List[i].BaseInfo.PiaStage = pia.DramaRoomBaseInfo_PIA_STAGE_NONE
			case pia_pb.DramaPhase_DRAMA_PHASE_SELECT_ROLE:
				result.List[i].BaseInfo.PiaStage = pia.DramaRoomBaseInfo_PIA_STAGE_SELECT_ROLE
			case pia_pb.DramaPhase_DRAMA_PHASE_PLAY:
				result.List[i].BaseInfo.PiaStage = pia.DramaRoomBaseInfo_PIA_STAGE_PLAYING
			case pia_pb.DramaPhase_DRAMA_PHASE_PAUSE:
				result.List[i].BaseInfo.PiaStage = pia.DramaRoomBaseInfo_PIA_STAGE_PLAYING
			case pia_pb.DramaPhase_DRAMA_PHASE_END:
				result.List[i].BaseInfo.PiaStage = pia.DramaRoomBaseInfo_PIA_STAGE_NONE
			default:
				result.List[i].BaseInfo.PiaStage = pia.DramaRoomBaseInfo_PIA_STAGE_NONE
			}
		}
		return nil
	}
}

//WithOnlineNumFiller 房间在线人数填充器
func WithOnlineNumFiller(channelOlCli channelol_stat_go.IClient) PracticeChannelFiller {
	return func(ctx context.Context, target *pia.GetPracticeDramaListResp) error {
		log.DebugWithCtx(ctx, "填充房间在线人数")
		if len(target.List) == 0 {
			return nil
		}
		channelIds := make([]uint32, len(target.List))
		for i := 0; i < len(target.List); i++ {
			channelIds[i] = target.List[i].BaseInfo.RoomInfo.Id
		}
		channelOnlineMemberMap, serverError := channelOlCli.BatchGetChannelMemberSize(ctx, 0, channelIds)
		if serverError != nil {
			log.ErrorWithCtx(ctx, "BatchGetChannelMemberSize error(%v),req: %+v", serverError, channelIds)
			return nil
		}
		log.DebugWithCtx(ctx, "填充房间在线人数，channelOnlineMemberMap:%+v", channelOnlineMemberMap)
		for i := 0; i < len(target.List); i++ {
			target.List[i].BaseInfo.RoomInfo.UserNum = int32(channelOnlineMemberMap[target.List[i].BaseInfo.RoomInfo.Id])
		}
		return nil
	}
}

//WithChannelInfoFiller 房间信息填充器
func WithChannelInfoFiller(accountCli account.IClient, channelCli channel.IClient) PracticeChannelFiller {
	return func(ctx context.Context, target *pia.GetPracticeDramaListResp) error {
		log.DebugWithCtx(ctx, "填充房间信息")
		if len(target.List) == 0 {
			return nil
		}
		channelIds := make([]uint32, len(target.List))
		for i := 0; i < len(target.List); i++ {
			channelIds[i] = target.List[i].BaseInfo.RoomInfo.Id
		}
		channelInfoMap, serverError := channelCli.BatchGetChannelSimpleInfo(ctx, 0, channelIds)
		if serverError != nil {
			log.Errorf("BatchGetChannelSimpleInfo error(%v),req: %+v", serverError, channelIds)
			return nil
		}
		targetUids := make([]uint32, 0, len(channelInfoMap))
		for _, item := range channelInfoMap {
			targetUids = append(targetUids, item.GetCreaterUid())
		}
		userInfoMap, serverError := accountCli.BatGetUserByUid(ctx, targetUids...)
		if serverError != nil {
			log.Errorf("BatGetUserByUid error(%v),req: %+v", serverError, targetUids)
			return nil
		}
		for i := 0; i < len(target.List); i++ {
			channelInfo, ok := channelInfoMap[target.List[i].BaseInfo.RoomInfo.Id]
			if !ok {
				continue
			}
			target.List[i].BaseInfo.RoomInfo.Name = channelInfo.GetName()
			if userInfo, ok := userInfoMap[channelInfo.GetCreaterUid()]; ok {
				target.List[i].BaseInfo.RoomInfo.OwnerName = userInfo.GetNickname()
				target.List[i].BaseInfo.RoomInfo.OwnerAvatar = userInfo.GetUsername()
				target.List[i].BaseInfo.RoomInfo.Sex = userInfo.GetSex()
			}
		}
		return nil
	}
}
