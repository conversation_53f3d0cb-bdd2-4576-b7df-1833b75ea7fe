package internal

import (
    "context"
    channelMic "gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channel_mic"
    micMiddle "gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channel_mic_middle"
    micScheme "gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channel_scheme_middle"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    "golang.52tt.com/pkg/protocol"
    protogrpc "golang.52tt.com/pkg/protocol/grpc"
    "golang.52tt.com/protocol/app"
    "golang.52tt.com/protocol/app/channel_wedding_logic"
    "golang.52tt.com/protocol/common/status"
    channel_wedding "golang.52tt.com/protocol/services/channel-wedding"
    channel_wedding_minigame "golang.52tt.com/protocol/services/channel-wedding-minigame"
    channel_wedding_plan "golang.52tt.com/protocol/services/channel-wedding-plan"
    magic_spirit "golang.52tt.com/protocol/services/magic-spirit"
    "golang.52tt.com/protocol/services/presentextraconf"
    presentPb "golang.52tt.com/protocol/services/userpresent-go"
    "google.golang.org/grpc/codes"
    "sort"
    "time"
)

const (
    MicStateUnLock          = uint32(1)
    ChairGamePlayerMicBegin = uint32(12) //游戏麦位固定为[12-16]  服务器用的麦位从1开始

    ChairGameRewardGiftEffectLimitSec = 4080 // 1小时+8分钟（一局奖励最长冻结时间+一局游戏时间）
    ChairGameMicHandleSource          = "grab-chair-game"

    GroomMicId = uint32(2) // 新郎麦
    BrideMicId = uint32(3) // 新娘麦
)

var (
    ChairGameMicList    = []uint32{12, 13, 14, 15, 16, 17, 18, 19, 20}
    ErrParamInValid     = protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
    ErrPlayersNotEnough = protocol.NewExactServerError(codes.OK, status.ErrChannelWeddingChairGamePlayerNotEnough, "玩家人数不足3人不支持开启游戏")
)

// SetChairGameReward 新人设置抢椅子游戏奖励
func (s *Server) SetChairGameReward(ctx context.Context, req *channel_wedding_logic.SetChairGameRewardRequest) (*channel_wedding_logic.SetChairGameRewardResponse, error) {
    out := &channel_wedding_logic.SetChairGameRewardResponse{}
    svrInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
    if !ok || req.GetChannelId() == 0 {
        log.ErrorWithCtx(ctx, "ApplyToJoinChairGame ServiceInfoFromContext or channelId:%d invalid", req.GetChannelId())
        return out, ErrParamInValid
    }
    opUid := svrInfo.UserID

    // 获取当前房间婚礼房信息
    weddingResp, err := s.weddingCli.GetChannelWeddingInfo(ctx, &channel_wedding.GetChannelWeddingInfoReq{
        Uid: opUid,
        Cid: req.GetChannelId(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "SetChairGameReward fail to GetChannelWeddingInfo. in:%+v, err:%v", req, err)
        return out, err
    }

    // 仅新人可设置游戏奖励
    if opUid != weddingResp.GetWeddingInfo().GetBride().GetUid() && opUid != weddingResp.GetWeddingInfo().GetGroom().GetUid() {
        log.ErrorWithCtx(ctx, "SetChairGameReward opUid:%d not bride or groom", opUid)
        return out, protocol.NewExactServerError(codes.OK, status.ErrChannelWeddingMinigameCommonErr, "仅新人可设置游戏奖励")
    }

    awardConfResp, err := s.miniGameCli.GetChairGameReward(ctx, &channel_wedding_minigame.GetChairGameRewardRequest{
        ChannelId: req.GetChannelId(),
        WeddingId: uint32(weddingResp.GetWeddingInfo().GetWeddingId()),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetChairGameReward fail to GetChairGameReward. in:%+v, err:%v", req, err)
        return out, err
    }

    if req.GetIsMagicGift() && !awardConfResp.GetRewardSetting().GetSupportMagicGift() {
        log.ErrorWithCtx(ctx, "SetChairGameReward fail to GetChairGameReward. in:%+v, err:%v", req, err)
        return out, protocol.NewExactServerError(codes.OK, status.ErrChannelWeddingMinigameCommonErr, "当前不支持设置该礼物哦，选择其它礼物试试吧~")
    }

    rewardInfo, err := s.GetPresentInfo(ctx, opUid, req.GetGiftType(), req.GetAwardGiftId(), req.GetAwardGiftNum(), req.GetIsMagicGift())
    if err != nil {
        log.ErrorWithCtx(ctx, "SetChairGameReward fail to GetPresentInfo. in:%+v, err:%v", req, err)
        return out, err
    }
    priceTBean := uint32(0)
    if rewardInfo.GetPriceType() == uint32(presentPb.PresentPriceType_PRESENT_PRICE_TBEAN) {
        priceTBean = rewardInfo.GetValue() * rewardInfo.GetAmount()
    }

    // 仅T豆礼物需要风控检查
    if priceTBean > 0 {
        // 风控检查
        baseResp, err := s.checkUserSetGamePrizeRisk(ctx, req.GetBaseReq(), priceTBean)
        if err != nil {
            log.ErrorWithCtx(ctx, "SetChairGameReward fail to checkUserSetGamePrizeRisk. in:%+v, err:%v", req, err)
            out.BaseResp = baseResp
            return out, err
        }
    }

    _, err = s.miniGameCli.SetChairGameReward(ctx, &channel_wedding_minigame.SetChairGameRewardRequest{
        Uid:       opUid,
        ChannelId: req.GetChannelId(),
        WeddingId: uint32(weddingResp.GetWeddingInfo().GetWeddingId()),
        Reward:    rewardInfo,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "SetChairGameReward fail to SetChairGameReward. in:%+v, err:%v", req, err)
        return out, err
    }

    log.InfoWithCtx(ctx, "SetChairGameReward success. opUid:%d req:%+v", opUid, req)
    return out, nil
}

func (s *Server) GetPresentInfo(ctx context.Context, uid, giftType, giftId, amount uint32, isMagicGift bool) (*channel_wedding_minigame.ChairGameRewardInfo, error) {
    // 幸运礼物
    if isMagicGift {
        magicResp, err := s.magicSpiritCli.GetMagicSpirit(ctx, &magic_spirit.GetMagicSpiritReq{})
        if err != nil {
            log.ErrorWithCtx(ctx, "GetPresentPrice fail to GetMagicSpirit. giftType:%v, giftId:%d, err:%v", giftType, giftId, err)
            return nil, err
        }

        for _, info := range magicResp.GetMagicSpirit() {
            if giftId == info.GetMagicSpiritId() {
                return magicGIft2RewardInfo(info, amount), err
            }
        }

        return nil, protocol.NewExactServerError(nil, status.ErrChannelLotteryInfoErr, "该幸运礼物配置不存在")
    }

    presentConfig, err := s.userPresentCli.GetPresentConfigById(ctx, giftId)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetPresentPrice fail to GetPresentConfigById. giftType:%v, giftId:%d, err:%v", giftType, giftId, err)
        return nil, err
    }

    if presentConfig.GetItemConfig() == nil {
        return nil, protocol.NewExactServerError(nil, status.ErrChannelLotteryInfoErr, "该礼物配置不存在")
    }

    itemCfg := presentConfig.GetItemConfig()

    if itemCfg.GetEffectEnd() == 0 {
        return nil, protocol.NewExactServerError(nil, status.ErrChannelWeddingMinigameCommonErr, "该礼物已下架，请选择其他礼物")
    }

    // 已下架礼物
    if itemCfg.GetEffectEnd() > 0 &&
        itemCfg.GetEffectEnd() <= uint32(time.Now().Unix())+ChairGameRewardGiftEffectLimitSec {
        // 未配置可强制送出
        if !itemCfg.GetExtend().GetForceSendable() {
            // 再检查是不是延长期限的显示礼物
            if itemCfg.GetExtend().GetEffectEndDelay() {
                resp, sErr := s.presentExtraConfClient.GetPresentEffectTime(ctx, &presentextraconf.GetPresentEffectTimeReq{
                    Uid: uid,
                })
                if sErr != nil {
                    return nil, sErr
                }
                for _, info := range resp.GetPresentEffectTimeInfos() {
                    if info.GetGiftId() == giftId && (info.GetEffectEnd() >= uint32(time.Now().Unix()) ||
                        info.GetEffectEnd() == 0) {
                        return present2RewardInfo(presentConfig.GetItemConfig(), giftType, amount), nil
                    }
                }
            }
            return nil, protocol.NewExactServerError(nil, status.ErrChannelWeddingMinigameCommonErr, "该礼物即将下架，请选择其他礼物")
        }
    }

    return present2RewardInfo(presentConfig.GetItemConfig(), giftType, amount), nil
}

func magicGIft2RewardInfo(info *magic_spirit.MagicSpirit, amount uint32) *channel_wedding_minigame.ChairGameRewardInfo {
    return &channel_wedding_minigame.ChairGameRewardInfo{
        Icon:   info.GetIconUrl(),
        Name:   info.GetName(),
        Value:  info.GetPrice(),
        Amount: amount,
        GiftId: info.GetMagicSpiritId(),
        //GiftType:    0,
        IsMagicGift: true,
        PriceType:   uint32(presentPb.PresentPriceType_PRESENT_PRICE_TBEAN),
    }
}

func present2RewardInfo(info *presentPb.StPresentItemConfig, giftType uint32, amount uint32) *channel_wedding_minigame.ChairGameRewardInfo {
    return &channel_wedding_minigame.ChairGameRewardInfo{
        Icon:        info.GetIconUrl(),
        Name:        info.GetName(),
        Value:       info.GetPrice(),
        Amount:      amount,
        GiftId:      info.GetItemId(),
        GiftType:    info.GetExtend().GetTag(), // 对应 ga_base.proto PresentTagType
        IsMagicGift: false,
        PriceType:   info.GetPriceType(),
    }
}

func (s *Server) GetChairGameRewardSetting(ctx context.Context, req *channel_wedding_logic.GetChairGameRewardSettingRequest) (*channel_wedding_logic.GetChairGameRewardSettingResponse, error) {
    out := &channel_wedding_logic.GetChairGameRewardSettingResponse{}
    svrInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
    if !ok || req.GetChannelId() == 0 {
        log.ErrorWithCtx(ctx, "GetChairGameRewardSetting ServiceInfoFromContext or channelId:%d invalid", req.GetChannelId())
        return out, ErrParamInValid
    }
    opUid := svrInfo.UserID

    // 获取当前房间婚礼房信息
    weddingResp, err := s.weddingCli.GetChannelWeddingInfo(ctx, &channel_wedding.GetChannelWeddingInfoReq{
        Uid: opUid,
        Cid: req.GetChannelId(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "SetChairGameReward fail to GetChannelWeddingInfo. in:%+v, err:%v", req, err)
        return out, err
    }

    if weddingResp.GetWeddingInfo().GetWeddingId() == 0 {
        log.WarnWithCtx(ctx, "GetChairGameRewardSetting weddingId=0 in:%+v, err:%v", req, err)
        return out, nil
    }

    resp, err := s.miniGameCli.GetChairGameReward(ctx, &channel_wedding_minigame.GetChairGameRewardRequest{
        ChannelId: req.GetChannelId(),
        WeddingId: uint32(weddingResp.GetWeddingInfo().GetWeddingId()),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetChairGameReward fail to GetChairGameReward. in:%+v, err:%v", req, err)
        return out, err
    }

    out.RewardConf = &channel_wedding_logic.ChairGameRewardSetting{
        GiftType:         resp.GetRewardSetting().GetGiftType(),
        SupportMagicGift: resp.GetRewardSetting().GetSupportMagicGift(),
        PriceLimit:       resp.GetRewardSetting().GetPriceLimit(),
    }

    if resp.GetRewardList() == nil {
        return out, nil
    }
    awardList := make([]*channel_wedding_logic.ChairGameRewardInfo, 0, len(resp.GetRewardList()))
    for _, reward := range resp.GetRewardList() {
        awardList = append(awardList, &channel_wedding_logic.ChairGameRewardInfo{
            Icon:       reward.GetIcon(),
            Name:       reward.GetName(),
            Value:      reward.GetValue(),
            RewardUnit: reward.GetRewardUnit(),
            Amount:     reward.GetAmount(),
        })
    }

    out.RewardList = awardList
    out.SponsorUid = resp.GetSponsorUid()
    return out, nil
}

// ApplyToJoinChairGame 申请参加/取消申请
func (s *Server) ApplyToJoinChairGame(ctx context.Context, req *channel_wedding_logic.ApplyToJoinChairGameRequest) (*channel_wedding_logic.ApplyToJoinChairGameResponse, error) {
    out := &channel_wedding_logic.ApplyToJoinChairGameResponse{}
    svrInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
    if !ok || req.GetChannelId() == 0 {
        log.ErrorWithCtx(ctx, "ApplyToJoinChairGame ServiceInfoFromContext or req:%+v invalid", req)
        return out, ErrParamInValid
    }
    opUid := svrInfo.UserID
    hostUid := uint32(0)

    if !req.GetIsCancel() {
        // 仅在房的用户可申请
        if !s.isInRoom(ctx, opUid, req.GetChannelId()) {
            log.ErrorWithCtx(ctx, "ApplyToJoinChairGame opUid:%d not in room", opUid)
            return out, protocol.NewExactServerError(codes.OK, status.ErrChannelWeddingMinigameCommonErr, "仅在房的用户可申请")
        }

        // 仅佩戴了虚拟形象的用户可报名游戏
        isVirtualImageOn, err := s.userApplyChairGameVirtualImageCheck(ctx, opUid)
        if err != nil {
            log.ErrorWithCtx(ctx, "ApplyToJoinChairGame fail to userApplyChairGameVirtualImageCheck. opUid:%d", opUid)
            return out, err
        }
        if !isVirtualImageOn {
            log.ErrorWithCtx(ctx, "ApplyToJoinChairGame opUid:%d not wearing virtual image", opUid)
            return out, protocol.NewExactServerError(codes.OK, status.ErrChannelWeddingMinigameCommonErr, "为虚拟形象穿戴服装才能报名~")
        }

        // 主持人不能申请参加游戏
        hostUid, err = s.getChannelHost(ctx, req.GetChannelId())
        if err != nil {
            log.ErrorWithCtx(ctx, "ApplyToJoinChairGame fail to getChannelHost. opUid:%d", opUid)
            return out, err
        }

        if hostUid == opUid {
            log.ErrorWithCtx(ctx, "ApplyToJoinChairGame opUid:%d is host", opUid)
            return out, protocol.NewExactServerError(codes.OK, status.ErrChannelWeddingMinigameCommonErr, "主持人不能申请参与游戏哦")
        }
    }

    // 获取当前房间婚礼房信息
    weddingResp, err := s.weddingCli.GetChannelWeddingInfo(ctx, &channel_wedding.GetChannelWeddingInfoReq{
        Uid: opUid,
        Cid: req.GetChannelId(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "ApplyToJoinChairGame fail to GetChannelWeddingInfo. in:%+v, err:%v", req, err)
        return out, err
    }

    if weddingResp.GetWeddingInfo().GetWeddingId() == 0 {
        log.ErrorWithCtx(ctx, "ApplyToJoinChairGame weddingId=0 in:%+v, err:%v", req, err)
        return out, ErrParamInValid
    }

    if !req.GetIsCancel() { // 仅报名需要风控检查
        applyRiskInfo := &ApplyRiskInfo{
            uid:      opUid,
            cid:      req.GetChannelId(),
            hostUid:  hostUid,
            groomUid: weddingResp.GetWeddingInfo().GetGroom().GetUid(),
            brideUid: weddingResp.GetWeddingInfo().GetBride().GetUid(),
        }
        // 风控检查
        baseResp, err := s.checkUserApplyChairGameRisk(ctx, req.GetBaseReq(), applyRiskInfo)
        if err != nil {
            log.ErrorWithCtx(ctx, "ApplyToJoinChairGame fail to checkUserApplyChairGameRisk. opUid:%d channelId:%+v", opUid, req.GetChannelId())
            out.BaseResp = baseResp
            return out, err
        }
    }

    _, err = s.miniGameCli.ApplyToJoinChairGame(ctx, &channel_wedding_minigame.ApplyToJoinChairGameRequest{
        Uid:       opUid,
        ChannelId: req.GetChannelId(),
        WeddingId: uint32(weddingResp.GetWeddingInfo().GetWeddingId()),
        IsCancel:  req.GetIsCancel(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "ApplyToJoinChairGame fail to ApplyToJoinChairGame. opUid:%d channelId:%+v", opUid, req.GetChannelId())
        return out, err
    }

    log.InfoWithCtx(ctx, "ApplyToJoinChairGame success. opUid:%d req:%+v", opUid, req)
    return out, nil
}

// 判断用户是否有佩戴中的虚拟形象
func (s *Server) userApplyChairGameVirtualImageCheck(ctx context.Context, uid uint32) (bool, error) {
    var isUse bool
    imageMap, err := s.batGetUserVirtualImage(ctx, []uint32{uid})
    if err != nil {
        return false, err
    }

    if image, ok := imageMap[uid]; ok {
        if len(image.GetItems()) > 0 {
            isUse = true
        }
    }

    //micSwitch, err := s.checkUserVirtualImageMicDisplay(ctx, uid)
    //if err != nil {
    //    return false, err
    //}

    return isUse, nil
}

// GetChairGameApplyList 获取报名列表
func (s *Server) GetChairGameApplyList(ctx context.Context, req *channel_wedding_logic.GetChairGameApplyListRequest) (*channel_wedding_logic.GetChairGameApplyListResponse, error) {
    out := &channel_wedding_logic.GetChairGameApplyListResponse{}
    svrInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
    if !ok || req.GetChannelId() == 0 {
        log.ErrorWithCtx(ctx, "GetChairGameApplyList ServiceInfoFromContext or channelId:%d invalid", req.GetChannelId())
        return out, ErrParamInValid
    }
    opUid := svrInfo.UserID
    // 获取当前房间婚礼房信息
    weddingResp, err := s.weddingCli.GetChannelWeddingInfo(ctx, &channel_wedding.GetChannelWeddingInfoReq{
        Uid: opUid,
        Cid: req.GetChannelId(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "SetChairGameReward fail to GetChannelWeddingInfo. in:%+v, err:%v", req, err)
        return out, err
    }

    if weddingResp.GetWeddingInfo().GetWeddingId() == 0 {
        log.WarnWithCtx(ctx, "GetChairGameApplyList weddingId=0 in:%+v, err:%v", req, err)
        return out, nil
    }

    resp, err := s.miniGameCli.GetChairGameApplyList(ctx, &channel_wedding_minigame.GetChairGameApplyListRequest{
        ChannelId: req.GetChannelId(),
        WeddingId: uint32(weddingResp.GetWeddingInfo().GetWeddingId()),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetChairGameApplyList GetChairGameApplyList fail,opUid:%d cid:%d err:%v", opUid, req.GetChannelId(), err)
        return out, err
    }

    if len(resp.GetApplyUserList()) == 0 {
        return out, nil
    }

    // 已报名列表
    userMap, err := s.userProfile.BatchGetUserProfileV2(ctx, resp.GetApplyUserList(), false)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetChairGameApplyList ServiceInfoFromContext fail.uid:%d,cid:%d err:%v", opUid, req.GetChannelId(), err)
        return out, err
    }

    roleMapResp, err := s.weddingPlanCli.BatchGetWeddingRole(ctx, &channel_wedding_plan.BatchGetWeddingRoleRequest{
        PlanId:  weddingResp.GetWeddingInfo().GetPlanId(),
        UidList: resp.GetApplyUserList(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetChairGameApplyList BatchGetWeddingRole fail.uid:%d,cid:%d err:%v", opUid, req.GetChannelId(), err)
        return out, err
    }

    applyList := make([]*channel_wedding_logic.ChairGameUserInfo, 0, len(resp.GetApplyUserList()))

    guestRoleMap := roleMapResp.GetWeddingRoleMap()
    for _, uid := range resp.GetApplyUserList() {
        if user, ok := userMap[uid]; ok {
            var guestType uint32
            if role, ok := guestRoleMap[uid]; ok {
                // 亲友团无需特殊标识
                if role != uint32(channel_wedding_logic.WeddingGuestType_WEDDING_GUEST_TYPE_FRIENDS) {
                    guestType = role
                }
            }
            log.DebugWithCtx(ctx, "GetChairGameApplyList uid:%d user:%+v guestType:%d", uid, user, guestType)
            applyList = append(applyList, &channel_wedding_logic.ChairGameUserInfo{
                UserInfo:         user,
                WeddingGuestType: guestType,
                //IsInChannel: onlineMap[uid], // 用例评审之后觉得用户退房直接从已申请列表移除，不需要再展示用户在房状态
            })
        }
    }

    sort.Slice(applyList, func(i, j int) bool {
        // 获取i和j的类型
        typeI := applyList[i].WeddingGuestType
        typeJ := applyList[j].WeddingGuestType

        // 定义优先级：2 > 1 > 3 > 4 > 5 = 0
        priority := map[uint32]int{
            uint32(channel_wedding_logic.WeddingGuestType_WEDDING_GUEST_TYPE_GROOM):       5,
            uint32(channel_wedding_logic.WeddingGuestType_WEDDING_GUEST_TYPE_BRIDE):       4,
            uint32(channel_wedding_logic.WeddingGuestType_WEDDING_GUEST_TYPE_BRIDES):      3,
            uint32(channel_wedding_logic.WeddingGuestType_WEDDING_GUEST_TYPE_BRIDESMAID):  2,
            uint32(channel_wedding_logic.WeddingGuestType_WEDDING_GUEST_TYPE_FRIENDS):     1,
            uint32(channel_wedding_logic.WeddingGuestType_WEDDING_GUEST_TYPE_UNSPECIFIED): 1,
        }

        // 比较优先级
        return priority[typeI] > priority[typeJ]
    })

    out.ApplyUserList = applyList
    out.TotalApplyNum = uint32(len(resp.GetApplyUserList()))

    log.DebugWithCtx(ctx, "GetChairGameApplyList success. opUid:%d applyList:%+v", opUid, resp.GetApplyUserList())
    return out, nil
}

// GetChairGameInfo 获取游戏信息（进程、奖励、参与人员）
func (s *Server) GetChairGameInfo(ctx context.Context, req *channel_wedding_logic.GetChairGameInfoRequest) (*channel_wedding_logic.GetChairGameInfoResponse, error) {
    out := &channel_wedding_logic.GetChairGameInfoResponse{}
    svrInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
    if !ok || req.GetChannelId() == 0 {
        log.ErrorWithCtx(ctx, "GetChairGameInfo ServiceInfoFromContext or channelId:%d invalid", req.GetChannelId())
        return out, ErrParamInValid
    }
    opUid := svrInfo.UserID

    resp, err := s.miniGameCli.GetChairGameInfo(ctx, &channel_wedding_minigame.GetChairGameInfoRequest{
        ChannelId: req.GetChannelId(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetChairGameInfo GetChairGameInfo fail,opUid:%d cid:%d err:%v", opUid, req.GetChannelId(), err)
        return out, err
    }

    if resp.GetGameInfo().GetGameId() == 0 {
        return out, nil
    }
    uidList := make([]uint32, 0, len(resp.GetGameInfo().GetPlayers()))
    for _, player := range resp.GetGameInfo().GetPlayers() {
        uidList = append(uidList, player.GetUid())
    }
    userMap, err := s.userProfile.BatchGetUserProfileV2(ctx, uidList, false)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetChairGameInfo ServiceInfoFromContext fail.uid:%d,cid:%d err:%v", opUid, req.GetChannelId(), err)
        return out, err
    }

    out.GameInfo = transformGameInfo2LogicPb(userMap, resp.GetGameInfo())
    return out, nil
}

func transformGameInfo2LogicPb(userMap map[uint32]*app.UserProfile, info *channel_wedding_minigame.ChairGameInfo) *channel_wedding_logic.ChairGameInfo {
    if info == nil {
        return nil
    }

    gameProcess := &channel_wedding_logic.ChairGameProgress{
        GameId:               info.GetGameProgress().GetGameId(),
        CurRound:             info.GetGameProgress().GetCurRound(),
        ChairNum:             info.GetGameProgress().GetChairNum(),
        RoundStatus:          info.GetGameProgress().GetRoundStatus(),
        ShowRoundTip:         info.GetGameProgress().GetShowRoundTip(),
        RoundPalyerUids:      info.GetGameProgress().GetRoundPalyerUids(),
        RoundWinnerUids:      info.GetGameProgress().GetRoundWinnerUids(),
        NextRoundChairNum:    info.GetGameProgress().GetNextRoundChairNum(),
        ServerTimeMs:         info.GetGameProgress().GetServerTimeMs(),
        HostStartButDuration: info.GetGameProgress().GetHostStartButDuration(),
        HostButtonEndTs:      info.GetGameProgress().GetHostButtonEndTs(),
    }

    rewardList := make([]*channel_wedding_logic.ChairGameRewardInfo, 0, len(info.GetRewardList()))
    for _, v := range info.GetRewardList() {
        rewardList = append(rewardList, &channel_wedding_logic.ChairGameRewardInfo{
            Icon:       v.GetIcon(),
            Name:       v.GetName(),
            Value:      v.GetValue(),
            RewardUnit: v.GetRewardUnit(),
            Amount:     v.GetAmount(),
        })
    }

    var PlayerMicList []*channel_wedding_logic.ChairGamePlayerInfo
    for _, v := range info.GetPlayers() {
        if user, ok := userMap[v.GetUid()]; ok {
            PlayerMicList = append(PlayerMicList, &channel_wedding_logic.ChairGamePlayerInfo{
                UserInfo: user,
                MicId:    v.GetMicId(),
            })
        }
    }

    logicInfo := &channel_wedding_logic.ChairGameInfo{
        GameId:            info.GetGameId(),
        ShowGameBeginAnim: info.GetShowGameBeginAnim(),
        GameProgress:      gameProcess,
        RewardList:        rewardList,
        Players:           PlayerMicList,
    }

    return logicInfo
}

// GrabChair 玩家抢椅子
func (s *Server) GrabChair(ctx context.Context, req *channel_wedding_logic.GrabChairRequest) (*channel_wedding_logic.GrabChairResponse, error) {
    out := &channel_wedding_logic.GrabChairResponse{}

    svrInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
    if !ok || req.GetChannelId() == 0 {
        log.ErrorWithCtx(ctx, "GrabChair ServiceInfoFromContext or channelId:%d invalid", req.GetChannelId())
        return out, ErrParamInValid
    }
    opUid := svrInfo.UserID

    _, err := s.miniGameCli.GrabChair(ctx, &channel_wedding_minigame.GrabChairRequest{
        ChannelId: req.GetChannelId(),
        Uid:       opUid,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GrabChair GrabChair fail,opUid:%d cid:%d err:%v", opUid, req.GetChannelId(), err)
        return out, err
    }

    log.InfoWithCtx(ctx, "GrabChair GrabChair success,opUid:%d cid:%d", opUid, req.GetChannelId())
    return out, nil
}

// StartChairGame 开启新一局抢椅子游戏
/*
1.获取当前房间内婚礼房id
2.preCheck
3.玩家自动上麦
4.开启一局游戏
    如果开启失败-踢玩家下麦
*/
func (s *Server) StartChairGame(ctx context.Context, req *channel_wedding_logic.StartChairGameRequest) (*channel_wedding_logic.StartChairGameResponse, error) {
    out := &channel_wedding_logic.StartChairGameResponse{}

    svrInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
    if !ok || req.GetChannelId() == 0 {
        log.ErrorWithCtx(ctx, "GrabChair ServiceInfoFromContext or channelId:%d invalid", req.GetChannelId())
        return out, ErrParamInValid
    }
    opUid := svrInfo.UserID

    // 验证玩家数量是否足够
    if len(req.GetPlayerUids()) < 3 {
        log.ErrorWithCtx(ctx, "StartChairGame player count less than 3, opUid:%d cid:%d", opUid, req.GetChannelId())
        return out, ErrPlayersNotEnough
    }

    // 获取当前房间婚礼房信息
    weddingResp, err := s.weddingCli.GetChannelWeddingInfo(ctx, &channel_wedding.GetChannelWeddingInfoReq{
        Uid: opUid,
        Cid: req.GetChannelId(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "SetChairGameReward fail to GetChannelWeddingInfo. in:%+v, err:%v", req, err)
        return out, err
    }

    // 仅可见入口时支持开启抢椅子游戏
    if !weddingResp.GetWeddingInfo().GetChairGameEntry() {
        log.ErrorWithCtx(ctx, "StartChairGame fail. no chair game entry. in:%+v", req)
        return out, protocol.NewExactServerError(codes.OK, status.ErrChannelWeddingMinigameCommonErr, "游戏开启失败")
    }

    weddingId := uint32(weddingResp.GetWeddingInfo().GetWeddingId())
    brideUid := weddingResp.GetWeddingInfo().GetBride().GetUid()
    groomUid := weddingResp.GetWeddingInfo().GetGroom().GetUid()

    starNewGameReq := &channel_wedding_minigame.StartChairGameRequest{
        Uid:       opUid,
        ChannelId: req.GetChannelId(),
        Uids:      req.GetPlayerUids(),
        WeddingId: weddingId,
    }

    // preCheck
    preCheckResp, err := s.miniGameCli.StartNewGamePreCheck(ctx, starNewGameReq)
    if err != nil {
        log.ErrorWithCtx(ctx, "StartChairGame StartNewGamePreCheck fail,opUid:%d cid:%d err:%v", opUid, req.GetChannelId(), err)
        return out, err
    }
    if len(preCheckResp.GetPlayers()) < 3 {
        log.ErrorWithCtx(ctx, "StartChairGame StartNewGamePreCheck fail,opUid:%d req:%d err:%v", opUid, req, err)
        return out, ErrPlayersNotEnough
    }

    log.InfoWithCtx(ctx, "StartChairGame StartNewGamePreCheck success,opUid:%d cid:%d GetPlayers():%v", opUid, req.GetChannelId(), preCheckResp.GetPlayers())

    // 玩家自动上麦
    fail2KickList, finalPlayerList, err := s.takePlayersToMicAndCheck(ctx, opUid, req.GetChannelId(), weddingId, preCheckResp.GetPlayers(), brideUid, groomUid)
    if err != nil {
        log.ErrorWithCtx(ctx, "StartChairGame takePlayersToMicAndCheck fail,opUid:%d cid:%d err:%v", opUid, req.GetChannelId(), err)
        _ = s.kickOutMicSpace(ctx, req.GetChannelId(), fail2KickList)
        return out, err
    }

    if len(finalPlayerList) < 3 {
        log.ErrorWithCtx(ctx, "StartChairGame takePlayersToMicAndCheck fail,opUid:%d cid:%d err:%v", opUid, req.GetChannelId(), err)
        _ = s.kickOutMicSpace(ctx, req.GetChannelId(), fail2KickList)
        return out, ErrPlayersNotEnough
    }

    starNewGameReq.Players = finalPlayerList
    // 开启一局游戏
    _, err = s.miniGameCli.StartChairGame(ctx, starNewGameReq)
    if err != nil {
        log.ErrorWithCtx(ctx, "StartChairGame fail,opUid:%d cid:%d err:%v", opUid, req.GetChannelId(), err)
        // 踢玩家下麦
        _ = s.kickOutMicSpace(ctx, req.GetChannelId(), fail2KickList)
        return out, err
    }

    log.InfoWithCtx(ctx, "StartChairGame StartChairGame success,opUid:%d cid:%d, finalPlayers:%+v", opUid, req.GetChannelId(), finalPlayerList)
    return out, nil
}

// SetChairGameToNextRound 进入下一轮
func (s *Server) SetChairGameToNextRound(ctx context.Context, req *channel_wedding_logic.SetChairGameToNextRoundRequest) (*channel_wedding_logic.SetChairGameToNextRoundResponse, error) {
    out := &channel_wedding_logic.SetChairGameToNextRoundResponse{}

    svrInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
    if !ok || req.GetChannelId() == 0 {
        log.ErrorWithCtx(ctx, "GrabChair ServiceInfoFromContext or channelId:%d invalid", req.GetChannelId())
        return out, ErrParamInValid
    }
    opUid := svrInfo.UserID

    // 判断主持人身份
    isHost, err := s.isHost(ctx, opUid, req.GetChannelId())
    if err != nil {
        log.ErrorWithCtx(ctx, "SetChairGameToNextRound isHost fail,opUid:%d cid:%d err:%v", opUid, req.GetChannelId(), err)
        return out, err
    }
    if !isHost {
        log.ErrorWithCtx(ctx, "SetChairGameToNextRound isHost fail,opUid:%d cid:%d err:%v", opUid, req.GetChannelId(), err)
        return out, protocol.NewExactServerError(codes.OK, status.ErrChannelWeddingMinigameCommonErr, "仅主持人可操作")
    }

    _, err = s.miniGameCli.SetChairGameToNextRound(ctx, &channel_wedding_minigame.SetChairGameToNextRoundRequest{
        ChannelId: req.GetChannelId(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "SetChairGameToNextRound SetChairGameToNextRound fail,opUid:%d cid:%d err:%v", opUid, req.GetChannelId(), err)
        return out, err
    }

    log.InfoWithCtx(ctx, "SetChairGameToNextRound SetChairGameToNextRound success,opUid:%d cid:%d", opUid, req.GetChannelId())
    return out, nil
}

// StartGrabChair 本轮开抢
func (s *Server) StartGrabChair(ctx context.Context, req *channel_wedding_logic.StartGrabChairRequest) (*channel_wedding_logic.StartGrabChairResponse, error) {
    out := &channel_wedding_logic.StartGrabChairResponse{}

    svrInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
    if !ok || req.GetChannelId() == 0 {
        log.ErrorWithCtx(ctx, "GrabChair ServiceInfoFromContext or channelId:%d invalid", req.GetChannelId())
        return out, ErrParamInValid
    }
    opUid := svrInfo.UserID

    // 判断主持人身份
    isHost, err := s.isHost(ctx, opUid, req.GetChannelId())
    if err != nil {
        log.ErrorWithCtx(ctx, "SetChairGameToNextRound isHost fail,opUid:%d cid:%d err:%v", opUid, req.GetChannelId(), err)
        return out, err
    }
    if !isHost {
        log.ErrorWithCtx(ctx, "SetChairGameToNextRound isHost fail,opUid:%d cid:%d err:%v", opUid, req.GetChannelId(), err)
        return out, protocol.NewExactServerError(codes.OK, status.ErrChannelWeddingMinigameCommonErr, "仅主持人可操作")
    }

    _, err = s.miniGameCli.StartGrabChair(ctx, &channel_wedding_minigame.StartGrabChairRequest{
        ChannelId: req.GetChannelId(),
        Uid:       opUid,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "StartGrabChair StartGrabChair fail,opUid:%d cid:%d err:%v", opUid, req.GetChannelId(), err)
        return out, err
    }

    log.InfoWithCtx(ctx, "StartGrabChair StartGrabChair success,opUid:%d cid:%d", opUid, req.GetChannelId())
    return out, nil
}

type PlayerMic struct {
    Uid   uint32
    MicId uint32
}

// takePlayersToMicAndCheck 抢椅子游戏抱玩家上麦
/*
1. 检查房间布局是否满足20麦位条件
2. 获取房间当前麦位列表
3. 帮用户上麦
*/
func (s *Server) takePlayersToMicAndCheck(ctx context.Context, opUid, channelId, weddingId uint32, uidList []uint32, brideUid, groomUid uint32) (fail2KickList []uint32, finalPlayers []*channel_wedding_minigame.PlayerInfo, err error) {

    // 1. 检查房间布局是否满足20麦位条件
    micSizeResp, err := s.micSchemeCli.GetCurMicSize(ctx, &micScheme.GetCurMicSizeReq{
        Cid: channelId,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "TakePlayersToMicAndCheck GetCurMicSize. channelId:%d err:%v ", channelId, err)
        return nil, nil, err
    }

    if micSizeResp == nil {
        log.ErrorWithCtx(ctx, "TakePlayersToMicAndCheck GetCurMicSize. channelId:%d err:%v ", channelId, err)
        return nil, nil, ToChairGameCommonErr("系统错误")
    }

    if micSizeResp.GetMicSize() < 20 {
        log.ErrorWithCtx(ctx, "TakePlayersToMicAndCheck. 房间布局不满足20麦位条件. channelId:%d,micSize:%d", channelId, micSizeResp.GetMicSize())
        return nil, nil, ToChairGameCommonErr("当前房间布局支持抢椅子游戏")
    }

    // 2. 获取房间当前麦位列表
    micResp, err := s.channelMicCli.GetMicrList(ctx, &channelMic.GetMicrListReq{
        ChannelId: channelId,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "TakePlayersToMicAndCheck fail to GetMicrList. channelId:%d, err:%v", channelId, err)
        return nil, nil, err
    }

    // 首先将游戏麦位都解锁 [12-20]
    _, err = s.micMiddleCli.SetMicStatus(ctx, &micMiddle.SetMicStatusReq{
        Source:    ChairGameMicHandleSource,
        OpUid:     opUid,
        Cid:       channelId,
        MicIdList: ChairGameMicList,
        MicState:  MicStateUnLock,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "TakePlayersToMicAndCheck fail to SetMicStatus. channelId:%d, err:%v", channelId, err)
        return nil, nil, err
    }

    originPlayerMap := make(map[uint32]struct{})
    kickMicList := make([]uint32, 0)               // 如果12-16号麦位有玩家，需要踢下麦
    userAlreadyOnMicMap := make(map[uint32]uint32) // uid -> micId

    // （1）检查uidList中的用户是否已在麦上；（2）判断12-16号麦是否有人
    for _, mic := range micResp.GetAllMicList() {
        // 主持人身份检查
        if mic.GetMicId() == HostMicId && mic.GetMicUid() != opUid {
            log.ErrorWithCtx(ctx, "takePlayersToMicAndCheck. not host. opUid:%d channelId:%d, micUid:%d", opUid, channelId, mic.GetMicUid())
            return nil, nil, ToChairGameCommonErr("只有主持人能开启游戏")
        }

        if mic.GetMicId() >= ChairGamePlayerMicBegin {
            // 12-16号麦位有玩家,需要先踢下麦
            if mic.GetMicUid() != 0 {
                kickMicList = append(kickMicList, mic.GetMicUid())
            }
        } else {
            // 已经在麦上的用户
            userAlreadyOnMicMap[mic.GetMicUid()] = mic.GetMicId()
        }
    }

    if len(kickMicList) > 0 {
        // 踢人
        _, err = s.micMiddleCli.KickOutMic(ctx, &micMiddle.KickOutMicReq{
            Source:        ChairGameMicHandleSource,
            OpUid:         0,
            Cid:           channelId,
            TargetUidList: kickMicList,
            //BanSecond:     0,
            //Toasts:        "",
        })
        if err != nil {
            log.ErrorWithCtx(ctx, "takePlayersToMicAndCheck fail to KickOutMicSpace.kickOutList:%v channelId:%d, err:%v", kickMicList, channelId, err)
            //return nil, nil, err  // 忽略错误
        }
    }

    needToHoldMicList := make([]*PlayerMic, 0) // 需要自动上麦的用户
    playerMicIdx := ChairGamePlayerMicBegin
    for _, uid := range uidList {
        if uid == opUid {
            log.ErrorWithCtx(ctx, "takePlayersToMicAndCheck. 司仪不能选择自己参与游戏 opUid:%d uidList:%+v", opUid, uidList)
            return nil, nil, ToChairGameCommonErr("司仪不能选择自己参与游戏")
        }
        originPlayerMap[uid] = struct{}{}
        if _, ok := userAlreadyOnMicMap[uid]; !ok {
            idxIncr := false
            targetMicId := playerMicIdx
            if uid == brideUid {
                // 上新娘麦
                targetMicId = BrideMicId
            } else if uid == groomUid {
                // 上新郎麦
                targetMicId = GroomMicId
            } else {
                idxIncr = true
            }

            // 需要帮用户上麦
            needToHoldMicList = append(needToHoldMicList, &PlayerMic{
                Uid:   uid,
                MicId: targetMicId,
            })
            if idxIncr {
                playerMicIdx++
            }
            fail2KickList = append(fail2KickList, uid)
        } else {
            finalPlayers = append(finalPlayers, &channel_wedding_minigame.PlayerInfo{
                Uid:   uid,
                MicId: userAlreadyOnMicMap[uid],
            })
        }
    }

    if len(needToHoldMicList) == 0 {
        return nil, finalPlayers, nil
    }

    finalPlayers = nil
    allMicList := make([]*micMiddle.MicSpaceInfo, 0)

    // 3. 抱用户上麦
    for _, holdMic := range needToHoldMicList {
        resp, err := s.micMiddleCli.HoldMic(ctx, &micMiddle.HoldMicReq{
            Source: ChairGameMicHandleSource,
            Uid:    holdMic.Uid,
            Cid:    channelId,
            MicId:  holdMic.MicId,
            //AppId:    0,
            //MarketId: 0,
        })
        if err != nil {
            log.ErrorWithCtx(ctx, "TakePlayersToMicAndCheck fail to HoldMic. holdMic:%+v channelId:%d, err:%v", holdMic, channelId, err)
            // -2119特殊处理
            if e := protocol.ToServerError(err); e.Code() == status.ErrChannelUserNotJoinThisChannel {
                GoroutineWithTimeoutCtx(ctx, 3*time.Second, func(ctx context.Context) {
                    s.PlayerFailToHoldMicHandle(ctx, holdMic.Uid, channelId, weddingId)
                })
            }
            // 忽略其他错误
        } else {
            allMicList = resp.GetAllMicList()
        }
    }

    for _, mic := range allMicList {
        if _, ok := originPlayerMap[mic.GetMicUid()]; ok {
            finalPlayers = append(finalPlayers, &channel_wedding_minigame.PlayerInfo{
                Uid:   mic.GetMicUid(),
                MicId: mic.GetMicId(),
            })
        }
    }

    return
}

// PlayerFailToHoldMicHandle 玩家上麦失败handle
func (s *Server) PlayerFailToHoldMicHandle(ctx context.Context, uid, cid, weddingId uint32) {
    // 判断用户是否不在房间
    if s.isInRoom(ctx, uid, cid) {
        return
    }

    // 取消用户报名
    _, err := s.miniGameCli.ApplyToJoinChairGame(ctx, &channel_wedding_minigame.ApplyToJoinChairGameRequest{
        Uid:       uid,
        ChannelId: cid,
        WeddingId: weddingId,
        IsCancel:  true,
    })
    log.ErrorWithCtx(ctx, "PlayerFailToHoldMicHandle ApplyToJoinChairGame fail, uid:%d, cid:%d,weddingId:%d, err%v", uid, cid, weddingId, err)
}

// kickOutMicSpace 游戏结束，踢用户下麦
func (s *Server) kickOutMicSpace(ctx context.Context, channelId uint32, uidList []uint32) error {
    // 1.获取房间内当前麦位列表
    micResp, err := s.channelMicCli.GetMicrList(ctx, &channelMic.GetMicrListReq{
        ChannelId: channelId,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "KickOutMicSpace fail to GetMicrList. channelId:%d, err:%v", channelId, err)
        return err
    }

    uidMap := make(map[uint32]struct{})
    for _, uid := range uidList {
        uidMap[uid] = struct{}{}
    }

    kickOutList := make([]uint32, 0)
    for _, mic := range micResp.GetAllMicList() {
        if _, ok := uidMap[mic.GetMicUid()]; ok {
            if mic.GetMicId() < 12 {
                continue
            }
            kickOutList = append(kickOutList, mic.GetMicUid())
        }
    }

    if len(kickOutList) == 0 {
        return nil
    }

    _, err = s.micMiddleCli.KickOutMic(ctx, &micMiddle.KickOutMicReq{
        Source:        ChairGameMicHandleSource,
        OpUid:         0,
        Cid:           channelId,
        TargetUidList: kickOutList,
        //BanSecond:     0,
        //Toasts:        "",
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "KickOutMicSpace fail to KickOutMicSpace.kickOutList:%v channelId:%d, err:%v", kickOutList, channelId, err)
        return err
    }

    log.InfoWithCtx(ctx, "KickOutMicSpace success. kickOutList:%v channelId:%d", kickOutList, channelId)
    return nil
}
