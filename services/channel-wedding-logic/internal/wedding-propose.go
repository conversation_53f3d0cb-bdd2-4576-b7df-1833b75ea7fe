package internal

import (
    "context"
    "gitlab.ttyuyin.com/avengers/tyr/core/log"
    "golang.52tt.com/pkg/protocol"
    protocolgrpc "golang.52tt.com/pkg/protocol/grpc"
    "golang.52tt.com/protocol/app/channel_wedding_logic"
    "golang.52tt.com/protocol/common/status"
    planPB "golang.52tt.com/protocol/services/channel-wedding-plan"
    fellow "golang.52tt.com/protocol/services/fellow-svr"
    context0 "golang.org/x/net/context"
    "google.golang.org/grpc/codes"
    "sort"
)

func (s *Server) GetProposeList(ctx context.Context, request *channel_wedding_logic.GetProposeListRequest) (*channel_wedding_logic.GetProposeListResponse, error) {
    out := &channel_wedding_logic.GetProposeListResponse{}
    out.Tips = s.dyConfig.GetProposeTips()
    log.DebugWithCtx(ctx, "GetProposeList request:%v", request)
    svrInfo, ok := protocolgrpc.ServiceInfoFromContext(ctx)
    if !ok {
        log.ErrorWithCtx(ctx, "GetUserVirtualImage failed to get service info")
        return out, protocol.NewExactServerError(codes.OK, status.ErrBadRequest)
    }

    if len(request.GetAccount()) > 0 {
        searchUserId, _, err := s.accountCli.GetUidByName(ctx, request.GetAccount())
        if err != nil {
            log.ErrorWithCtx(ctx, "GetProposeList failed to GetUidByName err:%v", err)
            return out, nil
        }

        if searchUserId == 0 {
            return out, nil
        }

        targetUser, err := s.userProfile.GetUserProfile(ctx, searchUserId)
        if err != nil {
            log.ErrorWithCtx(ctx, "GetProposeList failed to GetUserProfile err:%v", err)
            return out, err
        }

        fellowPointResp, serr := s.fellowCli.GetFellowPoint(ctx, &fellow.GetFellowPointReq{
            Uid:       svrInfo.UserID,
            FellowUid: searchUserId,
        })
        if serr != nil {
            log.ErrorWithCtx(ctx, "GetProposeList failed to GetFellowPoint err:%v", serr)
            return out, serr
        }

        out.ProposeList = append(out.ProposeList, &channel_wedding_logic.ProposeUser{
            User:        targetUser,
            FellowPoint: fellowPointResp.GetFellowPoint(),
        })

        return out, nil
    }

    fellowList, err := s.fellowCli.GetTopNFellow(ctx, &fellow.GetTopNFellowReq{
        Uid: svrInfo.UserID,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetProposeList failed to GetTopNFellow err:%v", err)
        return out, err
    }

    if len(fellowList.GetTopNFellowList()) == 0 {
        return out, nil
    }

    uidList := make([]uint32, 0)
    uidList = append(uidList, svrInfo.UserID)
    fellowPointMap := make(map[uint32]uint32)
    for _, fellowInfo := range fellowList.GetTopNFellowList() {
        if fellowInfo.GetFellowVal() == 0 {
            continue
        }
        uidList = append(uidList, fellowInfo.GetFellowUid())
        fellowPointMap[fellowInfo.GetFellowUid()] = fellowInfo.GetFellowVal()
    }

    userMap, err := s.userProfile.BatchGetUserProfileV2(ctx, uidList, false)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetProposeList failed to BatchGetUserProfile %v, err:%v", uidList, err)
        return out, err
    }

    sort.SliceStable(fellowList.GetTopNFellowList(), func(i, j int) bool {
        return fellowList.GetTopNFellowList()[i].GetFellowVal() > fellowList.GetTopNFellowList()[j].GetFellowVal()
    })

    // 过滤同性
    diffSexUidList := make([]uint32, 0)
    selfSex := userMap[svrInfo.UserID].GetSex()
    for _, fellowInfo := range fellowList.GetTopNFellowList() {
        targetUser := userMap[fellowInfo.GetFellowUid()]
        if targetUser.GetSex() == selfSex {
            continue
        }
        // 过滤神秘人
        if targetUser.GetPrivilege().GetType() != 0 {
            continue
        }
        diffSexUidList = append(diffSexUidList, fellowInfo.GetFellowUid())
    }

    if len(diffSexUidList) == 0 {
        return out, nil
    }

    marriageInfo, err := s.weddingPlanCli.BatchGetMarriageInfo(ctx, &planPB.BatchGetMarriageInfoRequest{
        UidList: diffSexUidList,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetProposeList failed to BatchGetMarriageInfo")
    }

    for _, uid := range diffSexUidList {
        if svrInfo.UserID == uid {
            continue
        }

        if _, ok := marriageInfo.GetMarriageInfoMap()[uid]; ok {
            continue
        }

        targetUser, found := userMap[uid]
        if !found {
            continue
        }

        out.ProposeList = append(out.ProposeList, &channel_wedding_logic.ProposeUser{
            User:        targetUser,
            FellowPoint: fellowPointMap[uid],
        })
    }

    return out, nil
}

func (s *Server) SendPropose(ctx context.Context, request *channel_wedding_logic.SendProposeRequest) (*channel_wedding_logic.SendProposeResponse, error) {
    out := &channel_wedding_logic.SendProposeResponse{}
    defer func() {
        log.InfoWithCtx(ctx, "SendPropose out:%v", out)
    }()
    serviceInfo, ok := protocolgrpc.ServiceInfoFromContext(ctx)
    if !ok {
        log.ErrorWithCtx(ctx, "SendPropose failed to get service info")
        return out, protocol.NewExactServerError(codes.OK, status.ErrBadRequest)
    }

    _, err := s.weddingPlanCli.SendPropose(ctx, &planPB.SendProposeRequest{
        TargetUid: request.GetTargetUid(),
        Tips:      request.GetTips(),
        FromUid:   serviceInfo.UserID,
    })

    if err != nil {
        log.ErrorWithCtx(ctx, "SendPropose failed to SendPropose")
        return out, err
    }
    return out, nil
}

func (s *Server) HandlePropose(ctx context.Context, request *channel_wedding_logic.HandleProposeRequest) (*channel_wedding_logic.HandleProposeResponse, error) {
    serviceInfo, ok := protocolgrpc.ServiceInfoFromContext(ctx)
    if !ok {
        log.ErrorWithCtx(ctx, "SendPropose failed to get service info")
        return nil, protocol.NewExactServerError(codes.OK, status.ErrBadRequest)
    }

    _, err := s.weddingPlanCli.HandlePropose(ctx, &planPB.HandleProposeRequest{
        ProposeId: request.GetProposeId(),
        IsAccept:  request.GetIsAccept(),
        HandleUid: serviceInfo.UserID,
    })
    return &channel_wedding_logic.HandleProposeResponse{}, err
}

func (s *Server) GetProposeById(ctx context.Context, request *channel_wedding_logic.GetProposeByIdRequest) (*channel_wedding_logic.GetProposeByIdResponse, error) {
    out := &channel_wedding_logic.GetProposeByIdResponse{}
    resp, err := s.weddingPlanCli.GetProposeById(ctx, &planPB.GetProposeByIdRequest{ProposeId: request.GetProposeId()})
    if err != nil {
        log.ErrorWithCtx(ctx, "GetProposeById failed to GetProposeById")
        return out, err
    }

    proposeInfo := resp.GetPropose()
    userMap, err := s.userProfile.BatchGetUserProfile(ctx, []uint32{proposeInfo.GetFromUser(), proposeInfo.GetTargetUser()})
    if err != nil {
        log.ErrorWithCtx(ctx, "GetProposeById failed to BatchGetUserProfile")
        return out, err
    }

    out.Propose = &channel_wedding_logic.WeddingProposeInfo{
        FromUser:   userMap[proposeInfo.GetFromUser()],
        TargetUser: userMap[proposeInfo.GetTargetUser()],
        ProposeId:  proposeInfo.GetProposeId(),
        Tips:       proposeInfo.GetTips(),
        Status:     proposeInfo.GetStatus(),
        CreateTime: proposeInfo.GetCreateTime(),
        EndTime:    proposeInfo.GetEndTime(),
        ExpireDay:  proposeInfo.GetExpireDay(),
    }

    return out, nil
}

func (s *Server) GetSendPropose(ctx context.Context, request *channel_wedding_logic.GetSendProposeRequest) (*channel_wedding_logic.GetSendProposeResponse, error) {
    out := &channel_wedding_logic.GetSendProposeResponse{}
    serviceInfo, ok := protocolgrpc.ServiceInfoFromContext(ctx)
    if !ok {
        log.ErrorWithCtx(ctx, "GetSendPropose failed to get service info")
        return out, protocol.NewExactServerError(codes.OK, status.ErrBadRequest)
    }

    resp, err := s.weddingPlanCli.GetSendPropose(ctx, &planPB.GetSendProposeRequest{FromUid: serviceInfo.UserID})
    if err != nil {
        log.ErrorWithCtx(ctx, "GetSendPropose failed uid:%d, err:%v", serviceInfo.UserID, err)
        return out, err
    }

    proposeInfo := resp.GetPropose()
    userMap, err := s.userProfile.BatchGetUserProfile(ctx, []uint32{proposeInfo.GetFromUser(), proposeInfo.GetTargetUser()})
    if err != nil {
        log.ErrorWithCtx(ctx, "GetProposeById failed to BatchGetUserProfile")
        return out, err
    }

    out.Propose = &channel_wedding_logic.WeddingProposeInfo{
        FromUser:   userMap[proposeInfo.GetFromUser()],
        TargetUser: userMap[proposeInfo.GetTargetUser()],
        ProposeId:  proposeInfo.GetProposeId(),
        Tips:       proposeInfo.GetTips(),
        Status:     proposeInfo.GetStatus(),
        CreateTime: proposeInfo.GetCreateTime(),
        EndTime:    proposeInfo.GetEndTime(),
        ExpireDay:  proposeInfo.GetExpireDay(),
    }
    return out, nil
}

func (s *Server) RevokePropose(c context0.Context, request *channel_wedding_logic.RevokeProposeRequest) (*channel_wedding_logic.RevokeProposeResponse, error) {
    out := &channel_wedding_logic.RevokeProposeResponse{}
    defer func() {
        log.InfoWithCtx(c, "RevokePropose req:%+v, out:%+v", request, out)
    }()
    serviceInfo, ok := protocolgrpc.ServiceInfoFromContext(c)
    if !ok {
        log.ErrorWithCtx(c, "RevokePropose failed to get service info")
        return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
    }
    _, err := s.weddingPlanCli.RevokePropose(c, &planPB.RevokeProposeRequest{
        Uid: serviceInfo.UserID,
    })
    if err != nil {
        log.ErrorWithCtx(c, "RevokePropose failed to RevokePropose err:%v", err)
        return out, err
    }
    return out, nil
}
