package internal

import (
    context "context"
    "fmt"
    "github.com/golang/protobuf/proto"
    push "golang.52tt.com/clients/push-notification/v2"
    "golang.52tt.com/pkg/log"
    "golang.52tt.com/pkg/protocol"
    protogrpc "golang.52tt.com/pkg/protocol/grpc"
    "golang.52tt.com/protocol/app"
    "golang.52tt.com/protocol/app/channel_wedding_logic"
    pushPb "golang.52tt.com/protocol/app/push"
    "golang.52tt.com/protocol/common/status"
    channel_wedding_conf "golang.52tt.com/protocol/services/channel-wedding-conf"
    channel_wedding_plan "golang.52tt.com/protocol/services/channel-wedding-plan"
    push_notification "golang.52tt.com/protocol/services/push-notification/v2"
    context0 "golang.org/x/net/context"
    "google.golang.org/grpc/codes"
    "math"
    "sort"
    "time"
)

func (s *Server) GetWeddingSchedulePageInfo(c context.Context, request *channel_wedding_logic.GetWeddingSchedulePageInfoRequest) (*channel_wedding_logic.GetWeddingSchedulePageInfoResponse, error) {
    out := &channel_wedding_logic.GetWeddingSchedulePageInfoResponse{}
    defer func() {
        log.DebugWithCtx(c, "GetWeddingSchedulePageInfo req:%+v, resp:%+v", request, out)
    }()

    serviceInfo, ok := protogrpc.ServiceInfoFromContext(c)
    if !ok {
        log.ErrorWithCtx(c, "GetWeddingSchedulePageInfo req:%+v", c)
        return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
    }
    proposeResp, err := s.weddingPlanCli.GetSendPropose(c, &channel_wedding_plan.GetSendProposeRequest{FromUid: serviceInfo.UserID})
    if err != nil {
        log.ErrorWithCtx(c, "GetWeddingSchedulePageInfo get send propose failed, err: %v, req:%+v", err, request)
        return out, err
    }

    var partnerUid uint32
    if proposeResp.GetPropose().GetFromUser() != serviceInfo.UserID {
        partnerUid = proposeResp.GetPropose().GetFromUser()
    } else {
        partnerUid = proposeResp.GetPropose().GetTargetUser()
    }
    switch proposeResp.GetPropose().GetStatus() {
    case uint32(channel_wedding_logic.ProposeStatus_PROPOSE_STATUS_SUCCESS):
        statusResp, err := s.weddingPlanCli.GetUserDivorceStatus(c, &channel_wedding_plan.GetUserDivorceStatusRequest{Uid: serviceInfo.UserID})
        if err != nil {
            log.ErrorWithCtx(c, "GetWeddingSchedulePageInfo failed to GetUserDivorceStatus, err: %v, req:%+v", err, request)
            return out, err
        }
        out.Partner = &channel_wedding_logic.WeddingPartner{
            IsAccepted:              true,
            EndRelationshipDeadline: statusResp.GetDivorceDeadline(),
            AutoEndRelationshipDay:  uint32(statusResp.GetAutoDivorceDay()),
        }
    case uint32(channel_wedding_logic.ProposeStatus_PROPOSE_STATUS_INVITED):
        out.Partner = &channel_wedding_logic.WeddingPartner{
            IsAccepted:          false,
            InviteAutoRejectDay: proposeResp.GetPropose().GetExpireDay(),
        }
    default:
        log.DebugWithCtx(c, "GetWeddingSchedulePageInfo no propose yet, req:%+v", request)
    }

    needProfileList := []uint32{serviceInfo.UserID}
    if partnerUid != 0 {
        needProfileList = append(needProfileList, partnerUid)
    }
    userResp, err := s.userProfile.BatchGetUserProfileV2(c, needProfileList, false)
    if err != nil {
        log.ErrorWithCtx(c, "GetWeddingSchedulePageInfo failed to BatchGetUserProfileV2, err: %v, req:%+v", err, request)
        return out, err
    }

    dyHeadMap, tErr := s.headDynamicImageCli.GetHeadDynamicImageMd5(c, needProfileList)
    if tErr != nil {
        log.ErrorWithCtx(c, "GetHeadDynamicImageMd5 uidList: %+v, err: %v", needProfileList, tErr)
        return out, tErr
    }

    usernames := make([]string, 0, len(userResp))
    invokeUid := uint32(0)
    for _, item := range userResp {
        if invokeUid == 0 {
            invokeUid = item.GetUid()
        }
        usernames = append(usernames, item.GetAccount())
    }

    headImgMap, err := s.headImageCli.BatchGetHeadImageMd5(c, invokeUid, usernames)
    if err != nil {
        log.ErrorWithCtx(c, "getUserProfileWithDyHead.BatchGetHeadImageMd5, uidList: %+v, err: %v", needProfileList, err)
    }

    for _, item := range userResp {
        item.HeadImgMd5 = headImgMap[item.GetAccount()]
        item.HeadDyImgMd5 = dyHeadMap[item.Uid]
    }

    out.MyInfo = userResp[serviceInfo.UserID]
    if partnerUid != 0 {
        out.Partner.UserProfile = userResp[partnerUid]
    }

    confResp, err := s.channelWeddingConfCli.GetThemeCfgList(c, &channel_wedding_conf.GetThemeCfgListReq{})
    if err != nil {
        log.ErrorWithCtx(c, "GetWeddingSchedulePageInfo failed to GetThemeCfgList, err: %v, req:%+v", err, request)
        return out, err
    }
    out.ThemeTitleSelectedIcon = confResp.GetThemeTitleSelectedIcon()
    out.ThemeTitleBgIcon = confResp.GetThemeTitleBgIcon()

    weddingInfoResp, err := s.weddingPlanCli.GetMyWeddingInfo(c, &channel_wedding_plan.GetMyWeddingInfoRequest{Uid: serviceInfo.UserID})
    if err != nil {
        log.ErrorWithCtx(c, "GetWeddingSchedulePageInfo failed to GetMyWeddingInfo, err: %v, req:%+v", err, request)
        return out, err
    }

    if weddingInfoResp.GetWeddingPlanId() != 0 {
        out.IsBuyer = serviceInfo.UserID == weddingInfoResp.GetBuyerUid()
        out.WeddingPlanInfo = &channel_wedding_logic.SimpleWeddingPlanInfo{
            ThemeId:       weddingInfoResp.GetThemeId(),
            WeddingPlanId: weddingInfoResp.GetWeddingPlanId(),
        }
        // fill reserveInfo
        reserveInfoResp, err := s.weddingPlanCli.GetMyWeddingReserveInfo(c, &channel_wedding_plan.GetMyWeddingReserveInfoRequest{WeddingPlanId: weddingInfoResp.GetWeddingPlanId()})
        if err != nil {
            log.ErrorWithCtx(c, "GetWeddingSchedulePageInfo failed to GetMyWeddingReserveInfo, err: %v, req:%+v", err, request)
            return out, err
        }
        if reserveInfoResp.GetReserveStartTime() != 0 && reserveInfoResp.GetReserveEndTime() != 0 {
            out.WeddingPlanInfo.ScheduleWeddingDurationStr = genDurationStr(int64(reserveInfoResp.GetReserveStartTime()), int64(reserveInfoResp.GetReserveEndTime()))
        }
        // 按天计算
        out.LimitCancelWeddingDay = uint32(math.Ceil(float64(reserveInfoResp.GetChangeLimitTime()) / 24))

        groomsmanAndBridesmaidResp, err := s.weddingPlanCli.GetGroomsmanAndBridesmaidInfo(c,
            &channel_wedding_plan.GetGroomsmanAndBridesmaidInfoRequest{WeddingPlanId: weddingInfoResp.GetWeddingPlanId()})
        if err != nil {
            log.ErrorWithCtx(c, "GetWeddingSchedulePageInfo failed to GetGroomsmanAndBridesmaidInfo, err: %v, req:%+v", err, request)
            return out, err
        }
        guestInfoList := make([]*channel_wedding_plan.WeddingGuestInfo, 0, len(groomsmanAndBridesmaidResp.GetBridesmaidList())+len(groomsmanAndBridesmaidResp.GetGroomsmanList()))
        guestInfoList = append(guestInfoList, groomsmanAndBridesmaidResp.GetGroomsmanList()...)
        guestInfoList = append(guestInfoList, groomsmanAndBridesmaidResp.GetBridesmaidList()...)
        sort.Slice(guestInfoList, func(i, j int) bool {
            return guestInfoList[i].GetCreateTime() < guestInfoList[j].GetCreateTime()
        })
        uidList := make([]uint32, 0, len(guestInfoList))
        maxCnt := 3
        for i, v := range guestInfoList {
            uidList = append(uidList, v.GetUid())
            if i == maxCnt {
                break
            }
        }

        friendResp, err := s.weddingPlanCli.GetWeddingFriendInfo(c, &channel_wedding_plan.GetWeddingFriendInfoRequest{WeddingPlanId: weddingInfoResp.GetWeddingPlanId()})
        if err != nil {
            log.ErrorWithCtx(c, "GetWeddingSchedulePageInfo failed to GetWeddingFriendInfo, err: %v, req:%+v", err, request)
            return out, err
        }
        sort.Slice(friendResp.GetFriendList(), func(i, j int) bool {
            return friendResp.GetFriendList()[i].GetCreateTime() < friendResp.GetFriendList()[j].GetCreateTime()
        })
        for i, v := range friendResp.GetFriendList() {
            uidList = append(uidList, v.GetUid())
            if i == maxCnt {
                break
            }
        }

        if len(uidList) > 0 {
            var err error
            userResp, err := s.userProfile.BatchGetUserProfileV2(c, uidList, false)
            if err != nil {
                log.ErrorWithCtx(c, "GetWeddingSchedulePageInfo failed to BatchGetUserProfileV2, err: %v, req:%+v", err, request)
                return out, err
            }

            for i, v := range guestInfoList {
                profile, ok := userResp[v.GetUid()]
                if !ok {
                    log.ErrorWithCtx(c, "GetWeddingSchedulePageInfo failed to BatchGetUserProfileV2, err: %v, req:%+v", err, request)
                    continue
                }
                out.WeddingPlanInfo.GroomsmanList = append(out.WeddingPlanInfo.GroomsmanList, profile)
                if i == maxCnt {
                    break
                }
            }
            for i, v := range friendResp.GetFriendList() {
                profile, ok := userResp[v.GetUid()]
                if !ok {
                    log.ErrorWithCtx(c, "GetWeddingSchedulePageInfo failed to BatchGetUserProfileV2, err: %v, req:%+v", err, request)
                    continue
                }
                out.WeddingPlanInfo.FamilyList = append(out.WeddingPlanInfo.FamilyList, profile)
                if i == maxCnt {
                    break
                }
            }

        }
        screenResp, err := s.weddingPlanCli.GetWeddingBigScreen(c, &channel_wedding_plan.GetWeddingBigScreenRequest{WeddingPlanId: weddingInfoResp.GetWeddingPlanId()})
        if err != nil {
            log.ErrorWithCtx(c, "GetWeddingSchedulePageInfo failed to GetWeddingBigScreen, err: %v, req:%+v", err, request)
            return out, err
        }
        for _, v := range screenResp.GetBigScreenList() {
            if v.GetReviewStatus() == channel_wedding_plan.ReviewStatus_REVIEW_STATUS_PASS {
                out.WeddingPlanInfo.AlreadyUploadBigScreenNum++
            }
        }

    }
    out.BlessText = s.dyConfig.GetConfig().BlessText
    return out, nil
}

func genDurationStr(startTime, endTime int64) string {
    // out format: 2025-01-01 15:00-15:30
    return fmt.Sprintf("%s-%s", time.Unix(startTime, 0).Format("01.02 15:04"), time.Unix(endTime, 0).Format("15:04"))
}

func (s *Server) BuyWedding(c context0.Context, request *channel_wedding_logic.BuyWeddingRequest) (*channel_wedding_logic.BuyWeddingResponse, error) {
    out := &channel_wedding_logic.BuyWeddingResponse{}
    defer func() {
        log.DebugWithCtx(c, "BuyWedding req:%+v, resp:%+v", request, out)
    }()

    themeResp, err := s.channelWeddingConfCli.GetThemeCfg(c, &channel_wedding_conf.GetThemeCfgReq{
        ThemeId: request.ThemeId,
    })
    if err != nil {
        log.ErrorWithCtx(c, "BuyWedding get theme cfg by id failed, err: %v, req:%+v", err, request)
        return out, err
    }

    if themeResp.GetThemeCfg().GetThemeId() == 0 {
        log.ErrorWithCtx(c, "BuyWedding theme cfg is nil, req:%+v", request)
        return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "主题不存在")
    }

    if themeResp.GetThemeCfg().GetIsDeleted() {
        log.ErrorWithCtx(c, "BuyWedding theme cfg is deleted, req:%+v", request)
        return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "主题已下架")
    }

    serviceInfo, ok := protogrpc.ServiceInfoFromContext(c)
    if !ok {
        log.ErrorWithCtx(c, "BuyWedding ctx:%+v", c)
        return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
    }

    proposeResp, err := s.weddingPlanCli.GetSendPropose(c, &channel_wedding_plan.GetSendProposeRequest{
        FromUid: serviceInfo.UserID,
    })
    if err != nil {
        log.ErrorWithCtx(c, "BuyWedding get send propose failed, err: %v, req:%+v", err, request)
        return out, err
    }

    if proposeResp.GetPropose() == nil || proposeResp.GetPropose().GetStatus() != uint32(channel_wedding_logic.ProposeStatus_PROPOSE_STATUS_SUCCESS) {
        log.ErrorWithCtx(c, "BuyWedding propose is nil, req:%+v", request)
        return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "要有结婚对象才能购买婚礼哦~")
    }

    var partnerUid uint32
    if proposeResp.GetPropose().GetFromUser() == serviceInfo.UserID {
        partnerUid = proposeResp.GetPropose().GetTargetUser()
    } else {
        partnerUid = proposeResp.GetPropose().GetFromUser()
    }

    price := themeResp.GetThemeCfg().GetPriceInfo().GetPrice() // 普通时段价格
    if request.GetReserveInfo().GetIsHot() {
        for _, item := range themeResp.GetThemeCfg().GetPriceInfo().GetHotTimePrice() {
            if item.GetGiftId() == request.GetReserveInfo().GetGiftId() {
                price = item.GetPrice()
                break
            }
        }
    }

    if themeResp.GetThemeCfg().GetPriceInfo().GetPriceType() == uint32(channel_wedding_logic.WeddingPriceType_WEDDING_PRICE_TYPE_T_BEAN) {
        baseResp, err := s.riskCheckBeforeBuyWedding(c, price, partnerUid, request.BaseReq)
        if err != nil {
            out.BaseResp = baseResp
            return out, err
        }
    }
    svrReq := &channel_wedding_plan.BuyWeddingRequest{
        ThemeId:  request.ThemeId,
        BuyerUid: serviceInfo.UserID,
        SourceMsgId: request.GetSourceMsgId(),
    }
    if request.GetReserveInfo() != nil {
        svrReq.BuyReserveInfo = &channel_wedding_plan.BuyReserveInfo{
            ReserveDate: request.GetReserveInfo().GetReserveDate(),
            ChannelId:   request.GetReserveInfo().GetChannelId(),
            ReserveTime: request.GetReserveInfo().GetReserveTime(),
            IsHot:       request.GetReserveInfo().GetIsHot(),
            GiftId:      request.GetReserveInfo().GetGiftId(),
        }
    }
    buyResp, err := s.weddingPlanCli.BuyWedding(c, svrReq)
    if err != nil {
        log.ErrorWithCtx(c, "buy wedding failed, err: %v, req:%+v", err, request)
        return out, err
    }

    GoroutineWithTimeoutCtx(c, time.Second*10, func(ctx context.Context) {
        var err error
        // get user info
        userInfoMap, err := s.userProfile.BatchGetUserProfileV2(ctx, []uint32{serviceInfo.UserID, partnerUid}, false)
        if err != nil {
            log.ErrorWithCtx(c, "BuyWedding failed to BatchGetUserProfileV2, err: %v, req:%+v", err, request)
            return
        }

        var myUserProfile, partnerUserProfile *app.UserProfile
        if profile, ok := userInfoMap[serviceInfo.UserID]; ok {
            myUserProfile = profile
        }
        if profile, ok := userInfoMap[partnerUid]; ok {
            partnerUserProfile = profile
        }
        err = s.pushCli.PushToUsers(ctx, []uint32{serviceInfo.UserID}, buildNotification(
            &channel_wedding_logic.WeddingPaidNotify{
                MyInfo:        myUserProfile,
                PartnerInfo:   partnerUserProfile,
                BlessText:     s.dyConfig.GetConfig().BlessText,
                WeddingPlanId: buyResp.GetWeddingPlanId(),
            }, uint32(pushPb.PushMessage_WEDDING_PAID_NOTIFY), "wedding_paid_notify"))
        if err != nil {
            log.ErrorWithCtx(ctx, "pushWeddingPaidNotify failed, err: %v, req:%+v", err, request)
            return
        }

        err = s.pushCli.PushToUsers(ctx, []uint32{partnerUid}, buildNotification(
            &channel_wedding_logic.WeddingPaidNotify{
                MyInfo:        partnerUserProfile,
                PartnerInfo:   myUserProfile,
                BlessText:     s.dyConfig.GetConfig().BlessText,
                WeddingPlanId: buyResp.GetWeddingPlanId(),
            }, uint32(pushPb.PushMessage_WEDDING_PAID_NOTIFY), "wedding_paid_notify"))
        if err != nil {
            log.ErrorWithCtx(ctx, "pushWeddingPaidNotify failed, err: %v, req:%+v", err, request)
            return
        }
    })

    return out, nil
}

func buildNotification(inputMsg proto.Message, cmd uint32, labelString string) *push_notification.CompositiveNotification {
    msg, _ := proto.Marshal(inputMsg)

    pushMessage := &pushPb.PushMessage{
        Cmd:     cmd,
        Content: msg,
    }
    pushMessageBytes, _ := proto.Marshal(pushMessage)

    return &push_notification.CompositiveNotification{
        Sequence:           uint32(time.Now().Unix()),
        TerminalTypeList:   []uint32{protocol.MobileAndroidTT, protocol.MobileIPhoneTT},
        TerminalTypePolicy: push.DefaultPolicy,
        AppId:              0,
        ProxyNotification: &push_notification.ProxyNotification{
            Type:      uint32(push_notification.ProxyNotification_PUSH),
            Payload:   pushMessageBytes,
            PushLabel: labelString,
        },
    }
}

func (s *Server) GetWeddingPreviewResource(c context0.Context, request *channel_wedding_logic.GetWeddingPreviewResourceRequest) (*channel_wedding_logic.GetWeddingPreviewResourceResponse, error) {
    out := &channel_wedding_logic.GetWeddingPreviewResourceResponse{}
    defer func() {
        log.DebugWithCtx(c, "GetWeddingPreviewResource req:%+v, resp:%+v", request, out)
    }()
    cfgResp, err := s.channelWeddingConfCli.GetThemeCfgList(c, &channel_wedding_conf.GetThemeCfgListReq{})
    if err != nil {
        log.ErrorWithCtx(c, "GetWeddingPreviewResource get all theme cfg failed, err: %v, req:%+v", err, request)
        return out, err
    }

    out.ThemeList = make([]*channel_wedding_logic.WeddingTheme, 0, len(cfgResp.GetThemeCfgList()))
    for _, cfg := range cfgResp.GetThemeCfgList() {
        var rewardInfoList []*channel_wedding_logic.FinishWeddingAward
        for _, reward := range cfg.GetRewardInfoList() {
            rewardInfoList = append(rewardInfoList, &channel_wedding_logic.FinishWeddingAward{
                AwardAnimation: &channel_wedding_logic.WeddingAnimationInfo{
                    AnimationType:        reward.GetAwardAnimation().GetResourceType(),
                    AnimationResource:    reward.GetAwardAnimation().GetResourceUrl(),
                    AnimationResourceMd5: reward.GetAwardAnimation().GetResourceMd5(),
                    AnimationPng:         reward.GetAwardAnimation().GetResourcePng(),
                },
                TopText:    reward.TopText,
                BottomText: reward.BottomText,
            })
        }

        var dressPreviewMap = make(map[uint32]*channel_wedding_logic.WeddingDressPreviewList)
        var dressListMap = make(map[uint32][]*channel_wedding_logic.WeddingDressPreview)
        for _, dressPreview := range cfg.GetThemeLevelCfgList() {
            for dressType, dress := range dressPreview.GetGuestDressCfgMap() {
                if _, ok := dressListMap[dressType]; !ok {
                    dressListMap[dressType] = make([]*channel_wedding_logic.WeddingDressPreview, 0)
                }
                dressListMap[dressType] = append(dressListMap[dressType], &channel_wedding_logic.WeddingDressPreview{
                    GuestType: dressType,
                    Level:     dressPreview.GetLevel(),
                    WeddingDress: &channel_wedding_logic.WeddingAnimationInfo{
                        AnimationType:        dress.GetWeddingDress().GetResourceType(),
                        AnimationResource:    dress.GetWeddingDress().GetResourceUrl(),
                        AnimationResourceMd5: dress.GetWeddingDress().GetResourceMd5(),
                        AnimationPng:         dress.GetWeddingDress().GetResourcePng(),
                    },
                    DressText:             dress.GetDressText(),
                    WeddingDressSmallIcon: dress.GetSmallIcon(),
                })
            }
        }
        for dressType, dressList := range dressListMap {
            dressPreviewMap[dressType] = &channel_wedding_logic.WeddingDressPreviewList{
                DressPreviewList: dressList,
            }
        }

        var previewResourceList []*channel_wedding_logic.WeddingScenePreview
        for _, preview := range cfg.GetThemeLevelCfgList() {
            sceneAnimation := preview.GetWeddingPreview().GetSceneAnimation()
            zoomAnimation := preview.GetWeddingPreview().GetZoomAnimation()
            previewResourceList = append(previewResourceList, &channel_wedding_logic.WeddingScenePreview{
                Level: preview.GetLevel(),
                SceneAnimation: &channel_wedding_logic.WeddingAnimationInfo{
                    AnimationType:        sceneAnimation.GetResourceType(),
                    AnimationResource:    sceneAnimation.GetResourceUrl(),
                    AnimationResourceMd5: sceneAnimation.GetResourceMd5(),
                    AnimationPng:         sceneAnimation.GetResourcePng(),
                },
                SmallIcon: preview.GetWeddingPreview().GetSmallIcon(),
                ZoomAnimation: &channel_wedding_logic.WeddingAnimationInfo{
                    AnimationType:        zoomAnimation.GetResourceType(),
                    AnimationResource:    zoomAnimation.GetResourceUrl(),
                    AnimationResourceMd5: zoomAnimation.GetResourceMd5(),
                    AnimationPng:         zoomAnimation.GetResourcePng(),
                },
            })
        }

        var bigScreenResource *channel_wedding_logic.WeddingAnimationInfo
        if cfg.MemorialVideoResource != nil {
            bigScreenResource = &channel_wedding_logic.WeddingAnimationInfo{
                AnimationType:        cfg.MemorialVideoResource.GetResourceType(),
                AnimationResource:    cfg.MemorialVideoResource.GetResourceUrl(),
                AnimationResourceMd5: cfg.MemorialVideoResource.GetResourceMd5(),
                AnimationPng:         cfg.MemorialVideoResource.GetResourcePng(),
            }
        }

        out.ThemeList = append(out.ThemeList, &channel_wedding_logic.WeddingTheme{
            Name:                   cfg.GetThemeName(),
            PreviewResourceList:    previewResourceList,
            ThemeId:                cfg.GetThemeId(),
            DressPreviewMap:        dressPreviewMap,
            SelectedThemeTitleIcon: cfg.GetSelectedThemeTitleIcon(),
            ExamplePhoto:           cfg.GetExamplePhoto(),
            RewardInfoDesc:         cfg.GetRewardInfoDesc(),
            RewardInfoList:         rewardInfoList,
            MaxBigScreenNum:        10, // 固定10个
            PriceInfo: &channel_wedding_logic.WeddingPriceInfo{
                Price:     cfg.GetPriceInfo().GetPrice(),
                PriceType: cfg.GetPriceInfo().GetPriceType(),
            },
            ThemeBackground:          cfg.ThemeBackground,
            ThemePreviewText:         cfg.ThemePreviewText,
            UnselectedThemeTitleIcon: cfg.GetUnselectedThemeTitleIcon(),
            MailLadyRightBgIcon:      cfg.GetMailLadyRightBgIcon(),
            MailLadyLeftBgIcon:       cfg.GetMailLadyLeftBgIcon(),
            BigScreenResource:        bigScreenResource,
        })
    }
    return out, nil
}
