// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/tt-rev/esport/esport-role/internal/cache (interfaces: ICache)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"
	time "time"

	redis "github.com/go-redis/redis/v8"
	gomock "github.com/golang/mock/gomock"
	esport_role "golang.52tt.com/protocol/services/esport_role"
	cache "golang.52tt.com/services/tt-rev/esport/esport-role/internal/cache"
)

// MockICache is a mock of ICache interface.
type MockICache struct {
	ctrl     *gomock.Controller
	recorder *MockICacheMockRecorder
}

// MockICacheMockRecorder is the mock recorder for MockICache.
type MockICacheMockRecorder struct {
	mock *MockICache
}

// NewMockICache creates a new mock instance.
func NewMockICache(ctrl *gomock.Controller) *MockICache {
	mock := &MockICache{ctrl: ctrl}
	mock.recorder = &MockICacheMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockICache) EXPECT() *MockICacheMockRecorder {
	return m.recorder
}

// BatGetUserRoleInfo mocks base method.
func (m *MockICache) BatGetUserRoleInfo(arg0 context.Context, arg1 []uint32) (map[uint32]*cache.RoleInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatGetUserRoleInfo", arg0, arg1)
	ret0, _ := ret[0].(map[uint32]*cache.RoleInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatGetUserRoleInfo indicates an expected call of BatGetUserRoleInfo.
func (mr *MockICacheMockRecorder) BatGetUserRoleInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatGetUserRoleInfo", reflect.TypeOf((*MockICache)(nil).BatGetUserRoleInfo), arg0, arg1)
}

// BatchDelCoachLabelCache mocks base method.
func (m *MockICache) BatchDelCoachLabelCache(arg0 context.Context, arg1 []uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchDelCoachLabelCache", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchDelCoachLabelCache indicates an expected call of BatchDelCoachLabelCache.
func (mr *MockICacheMockRecorder) BatchDelCoachLabelCache(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchDelCoachLabelCache", reflect.TypeOf((*MockICache)(nil).BatchDelCoachLabelCache), arg0, arg1)
}

// BatchGetCoachLabel mocks base method.
func (m *MockICache) BatchGetCoachLabel(arg0 context.Context, arg1 []uint32) (map[uint32]*esport_role.CoachLabel, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetCoachLabel", arg0, arg1)
	ret0, _ := ret[0].(map[uint32]*esport_role.CoachLabel)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetCoachLabel indicates an expected call of BatchGetCoachLabel.
func (mr *MockICacheMockRecorder) BatchGetCoachLabel(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetCoachLabel", reflect.TypeOf((*MockICache)(nil).BatchGetCoachLabel), arg0, arg1)
}

// BatchInsertCoachLabel mocks base method.
func (m *MockICache) BatchInsertCoachLabel(arg0 context.Context, arg1 map[uint32]*esport_role.CoachLabel) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchInsertCoachLabel", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchInsertCoachLabel indicates an expected call of BatchInsertCoachLabel.
func (mr *MockICacheMockRecorder) BatchInsertCoachLabel(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchInsertCoachLabel", reflect.TypeOf((*MockICache)(nil).BatchInsertCoachLabel), arg0, arg1)
}

// BatchSetUserRoleInfo mocks base method.
func (m *MockICache) BatchSetUserRoleInfo(arg0 context.Context, arg1 map[uint32]*cache.RoleInfo, arg2 time.Duration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchSetUserRoleInfo", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchSetUserRoleInfo indicates an expected call of BatchSetUserRoleInfo.
func (mr *MockICacheMockRecorder) BatchSetUserRoleInfo(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchSetUserRoleInfo", reflect.TypeOf((*MockICache)(nil).BatchSetUserRoleInfo), arg0, arg1, arg2)
}

// CheckInLarkPool mocks base method.
func (m *MockICache) CheckInLarkPool(arg0 context.Context, arg1 string, arg2 []uint32) (map[uint32]bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckInLarkPool", arg0, arg1, arg2)
	ret0, _ := ret[0].(map[uint32]bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckInLarkPool indicates an expected call of CheckInLarkPool.
func (mr *MockICacheMockRecorder) CheckInLarkPool(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckInLarkPool", reflect.TypeOf((*MockICache)(nil).CheckInLarkPool), arg0, arg1, arg2)
}

// Close mocks base method.
func (m *MockICache) Close() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Close")
	ret0, _ := ret[0].(error)
	return ret0
}

// Close indicates an expected call of Close.
func (mr *MockICacheMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockICache)(nil).Close))
}

// DelUserRoleInfo mocks base method.
func (m *MockICache) DelUserRoleInfo(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelUserRoleInfo", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelUserRoleInfo indicates an expected call of DelUserRoleInfo.
func (mr *MockICacheMockRecorder) DelUserRoleInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelUserRoleInfo", reflect.TypeOf((*MockICache)(nil).DelUserRoleInfo), arg0, arg1)
}

// GetLarkPool mocks base method.
func (m *MockICache) GetLarkPool(arg0 context.Context, arg1 string) ([]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLarkPool", arg0, arg1)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLarkPool indicates an expected call of GetLarkPool.
func (mr *MockICacheMockRecorder) GetLarkPool(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLarkPool", reflect.TypeOf((*MockICache)(nil).GetLarkPool), arg0, arg1)
}

// GetRedisClient mocks base method.
func (m *MockICache) GetRedisClient() redis.Cmdable {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRedisClient")
	ret0, _ := ret[0].(redis.Cmdable)
	return ret0
}

// GetRedisClient indicates an expected call of GetRedisClient.
func (mr *MockICacheMockRecorder) GetRedisClient() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRedisClient", reflect.TypeOf((*MockICache)(nil).GetRedisClient))
}

// GetUserRoleInfo mocks base method.
func (m *MockICache) GetUserRoleInfo(arg0 context.Context, arg1 uint32) (*cache.RoleInfo, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserRoleInfo", arg0, arg1)
	ret0, _ := ret[0].(*cache.RoleInfo)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetUserRoleInfo indicates an expected call of GetUserRoleInfo.
func (mr *MockICacheMockRecorder) GetUserRoleInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserRoleInfo", reflect.TypeOf((*MockICache)(nil).GetUserRoleInfo), arg0, arg1)
}

// SetLarkPool mocks base method.
func (m *MockICache) SetLarkPool(arg0 context.Context, arg1 string, arg2 []uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetLarkPool", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetLarkPool indicates an expected call of SetLarkPool.
func (mr *MockICacheMockRecorder) SetLarkPool(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetLarkPool", reflect.TypeOf((*MockICache)(nil).SetLarkPool), arg0, arg1, arg2)
}

// SetUserRoleInfo mocks base method.
func (m *MockICache) SetUserRoleInfo(arg0 context.Context, arg1 uint32, arg2 *cache.RoleInfo, arg3 time.Duration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserRoleInfo", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetUserRoleInfo indicates an expected call of SetUserRoleInfo.
func (mr *MockICacheMockRecorder) SetUserRoleInfo(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserRoleInfo", reflect.TypeOf((*MockICache)(nil).SetUserRoleInfo), arg0, arg1, arg2, arg3)
}
