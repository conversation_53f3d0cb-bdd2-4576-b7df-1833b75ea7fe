package store

import (
	context "context"
	"time"
)

type IStore interface {
	BatGetPublishRecord(ctx context.Context, idList []string) (map[string]*PublishRecord, error)
	BatInsertGrabCenterRecord(ctx context.Context, records []*GrabCenterRecord) error
	Close(ctx context.Context) error
	GetPublishRecord(ctx context.Context, id string) (*PublishRecord, error)
	InsertPublishRecord(ctx context.Context, record *PublishRecord) (*PublishRecord, error)
	UpdatePublishRecordStatus(ctx context.Context, publishId string, publishStatus uint32) error
	GetTimeoutPublishRecords(ctx context.Context, timeout time.Duration) ([]*PublishRecord, error)
}
