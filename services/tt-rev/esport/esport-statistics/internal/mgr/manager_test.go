package mgr

import (
	"context"
	"errors"
	"github.com/golang/mock/gomock"
	pb "golang.52tt.com/protocol/services/esport-statistics"
	esport_trade_appeal "golang.52tt.com/protocol/services/esport-trade-appeal"
	"golang.52tt.com/services/tt-rev/esport/esport-statistics/internal/mocks"
	"golang.52tt.com/services/tt-rev/esport/esport-statistics/internal/store"
	"reflect"
	"testing"
)

type mangerForTest struct {
	*Manager
}

func NewMangerForTest(t *testing.T) *mangerForTest {
	controller := gomock.NewController(t)
	defer controller.Finish()
	return &mangerForTest{
		Manager: &Manager{
			cache:         mocks.NewMockCache(controller),
			store:         mocks.NewMockStore(controller),
			bc:            mocks.NewMockIBusinessConfManager(controller),
			refundService: mocks.NewMockRefundServiceClient(controller),
		},
	}
}

func (receiver *mangerForTest) getStore() *mocks.MockStore {
	return receiver.store.(*mocks.MockStore)
}

func (receiver *mangerForTest) getCache() *mocks.MockCache {
	return receiver.cache.(*mocks.MockCache)
}

func (receiver *mangerForTest) getRefundService() *mocks.MockRefundServiceClient {
	return receiver.refundService.(*mocks.MockRefundServiceClient)
}

func (receiver *mangerForTest) getBusinessConf() *mocks.MockIBusinessConfManager {
	return receiver.bc.(*mocks.MockIBusinessConfManager)
}

func TestManager_BatchGetOrderCntByGameId(t *testing.T) {
	tests := []struct {
		name     string
		uidList  []uint32
		gameId   uint32
		want     *pb.BatchGetOrderCntByGameIdResponse
		wantErr  bool
		initFunc func(m *mangerForTest)
	}{
		{
			name:     "uidList is empty",
			uidList:  []uint32{},
			gameId:   1,
			want:     &pb.BatchGetOrderCntByGameIdResponse{},
			wantErr:  true,
			initFunc: nil,
		},
		{
			name:     "gameId is zero",
			uidList:  []uint32{1, 2},
			gameId:   0,
			want:     &pb.BatchGetOrderCntByGameIdResponse{},
			wantErr:  true,
			initFunc: nil,
		},
		{
			name:    "get all data from cache ",
			uidList: []uint32{1, 2},
			gameId:  1,
			want: &pb.BatchGetOrderCntByGameIdResponse{
				OrderNumMap: map[uint32]uint32{1: 10, 2: 20},
			},
			wantErr: false,
			initFunc: func(m *mangerForTest) {
				m.getCache().EXPECT().BatchGetOrderCntByGameId(gomock.Any(), []uint32{1, 2}, uint32(1)).Return(map[uint32]uint32{
					1: 10,
					2: 20,
				}, nil).AnyTimes()
			},
		},
		{
			name:    "get data from cache failed",
			uidList: []uint32{1, 2},
			gameId:  1,
			want:    &pb.BatchGetOrderCntByGameIdResponse{},
			wantErr: true,
			initFunc: func(m *mangerForTest) {
				m.getCache().EXPECT().BatchGetOrderCntByGameId(gomock.Any(), []uint32{1, 2}, uint32(1)).Return(nil, errors.New("error")).AnyTimes()
			},
		},
		{
			name:    "get data from store failed",
			uidList: []uint32{1, 2},
			gameId:  1,
			want:    &pb.BatchGetOrderCntByGameIdResponse{},
			wantErr: true,
			initFunc: func(m *mangerForTest) {
				m.getCache().EXPECT().BatchGetOrderCntByGameId(gomock.Any(), []uint32{1, 2}, uint32(1)).Return(map[uint32]uint32{
					1: 10,
				}, nil).AnyTimes()
				m.getStore().EXPECT().BatchGetOrderCntByGameId(gomock.Any(), []uint32{2}, uint32(1)).Return(nil, errors.New("error")).AnyTimes()
			},
		},
		{
			name:    "set data to cache failed",
			uidList: []uint32{1, 2},
			gameId:  1,
			want: &pb.BatchGetOrderCntByGameIdResponse{
				OrderNumMap: map[uint32]uint32{1: 10, 2: 20},
			},
			wantErr: false,
			initFunc: func(m *mangerForTest) {
				m.getCache().EXPECT().BatchGetOrderCntByGameId(gomock.Any(), []uint32{1, 2}, uint32(1)).Return(nil, nil).AnyTimes()
				m.getStore().EXPECT().BatchGetOrderCntByGameId(gomock.Any(), []uint32{1, 2}, uint32(1)).Return(map[uint32]uint32{1: 10, 2: 20}, nil).AnyTimes()
				m.getCache().EXPECT().BatchSetOrderCntByGameId(gomock.Any(), gomock.Any(), uint32(1)).Return(errors.New("error")).AnyTimes()
			},
		},
		// 添加更多的测试用例...
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := NewMangerForTest(t)
			if tt.initFunc != nil {
				tt.initFunc(m)
			}
			got, err := m.BatchGetOrderCntByGameId(context.Background(), tt.uidList, tt.gameId)
			if (err != nil) != tt.wantErr {
				t.Errorf("BatchGetOrderCntByGameId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("BatchGetOrderCntByGameId() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestManager_IsPartialRefund(t *testing.T) {
	tests := []struct {
		name     string
		orderId  string
		want     bool
		wantErr  bool
		wantCnt  uint32
		initFunc func(m *mangerForTest)
	}{
		{
			name:     "orderId is empty",
			orderId:  "",
			want:     false,
			wantErr:  true,
			initFunc: nil,
		},
		{
			name:    "get data from refundService failed",
			orderId: "123",
			want:    false,
			wantErr: true,
			initFunc: func(m *mangerForTest) {
				m.getRefundService().EXPECT().GetOrderRefundView(gomock.Any(), gomock.Any()).Return(nil, errors.New("失败")).AnyTimes()
			},
		},
		{
			name:    "refund type is partial",
			orderId: "123",
			want:    true,
			wantErr: false,
			wantCnt: 100,
			initFunc: func(m *mangerForTest) {
				m.getRefundService().EXPECT().GetOrderRefundView(gomock.Any(), gomock.Any()).Return(&esport_trade_appeal.GetOrderRefundViewResponse{
					OrderRefundView: &esport_trade_appeal.OrderRefundView{
						RefundPrice: 100,
						RefundType:  esport_trade_appeal.RefundType_REFUND_TYPE_PARTIAL,
					},
				}, nil).AnyTimes()
			},
		},
		{
			name:    "refund type is not partial",
			orderId: "123",
			want:    false,
			wantErr: false,
			wantCnt: 100,
			initFunc: func(m *mangerForTest) {
				m.getRefundService().EXPECT().GetOrderRefundView(gomock.Any(), gomock.Any()).Return(&esport_trade_appeal.GetOrderRefundViewResponse{
					OrderRefundView: &esport_trade_appeal.OrderRefundView{
						RefundPrice: 100,
						RefundType:  esport_trade_appeal.RefundType_REFUND_TYPE_FULL,
					},
				}, nil).AnyTimes()
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := NewMangerForTest(t)
			if tt.initFunc != nil {
				tt.initFunc(m)
			}
			got, cnt, err := m.IsPartialRefund(context.Background(), tt.orderId)
			if (err != nil) != tt.wantErr {
				t.Errorf("IsPartialRefund() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("IsPartialRefund() got = %v, want %v", got, tt.want)
			}
			if cnt != tt.wantCnt {
				t.Errorf("IsPartialRefund() got = %v, want %v", cnt, tt.wantCnt)
			}
		})
	}
}

func TestManager_GetAllCoachTradeData(t *testing.T) {
	tests := []struct {
		name     string
		uid      uint32
		want     *pb.GetCoachAllStatisticsResponse
		wantErr  bool
		initFunc func(m *mangerForTest)
	}{
		{
			name:     "uid is zero",
			uid:      0,
			want:     &pb.GetCoachAllStatisticsResponse{},
			wantErr:  true,
			initFunc: nil,
		},
		{
			name:    "get data from store failed",
			uid:     123,
			want:    &pb.GetCoachAllStatisticsResponse{},
			wantErr: true,
			initFunc: func(m *mangerForTest) {
				m.getStore().EXPECT().GetCoachAllTradeData(gomock.Any(), uint32(123)).Return(nil, errors.New("error")).AnyTimes()
			},
		},
		{
			name: "get empty data from store",
			uid:  123,
			want: &pb.GetCoachAllStatisticsResponse{
				GameDataMap: map[uint32]*pb.StatisticsData{},
			},
			wantErr: false,
			initFunc: func(m *mangerForTest) {
				m.getStore().EXPECT().GetCoachAllTradeData(gomock.Any(), uint32(123)).Return(make(map[uint32]*store.TradeData), nil).AnyTimes()
			},
		},
		{
			name: "get data from store success",
			uid:  123,
			want: &pb.GetCoachAllStatisticsResponse{
				GameDataMap: map[uint32]*pb.StatisticsData{
					1: {
						OrderNum:    10,
						CustomerNum: 20,
					},
				},
			},
			wantErr: false,
			initFunc: func(m *mangerForTest) {
				m.getStore().EXPECT().GetCoachAllTradeData(gomock.Any(), uint32(123)).Return(map[uint32]*store.TradeData{
					1: {
						OrderNum: 10,
						UserCnt:  20,
					},
				}, nil).AnyTimes()
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := NewMangerForTest(t)
			if tt.initFunc != nil {
				tt.initFunc(m)
			}
			got, err := m.GetAllCoachTradeData(context.Background(), tt.uid)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAllCoachTradeData() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetAllCoachTradeData() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestManager_BatchGetUsersFirstTradeTime(t *testing.T) {
	tests := []struct {
		name     string
		uidList  []uint32
		want     *pb.BatchGetUserFirstTakeOrdersTimeResponse
		wantErr  bool
		initFunc func(m *mangerForTest)
	}{
		{
			name:     "uidList is empty",
			uidList:  []uint32{},
			want:     &pb.BatchGetUserFirstTakeOrdersTimeResponse{},
			wantErr:  true,
			initFunc: nil,
		},
		{
			name:    "get data from cache failed",
			uidList: []uint32{1, 2},
			want:    &pb.BatchGetUserFirstTakeOrdersTimeResponse{},
			wantErr: true,
			initFunc: func(m *mangerForTest) {
				m.getCache().EXPECT().BatchGetUsersFirstTradeTime(gomock.Any(), []uint32{1, 2}).Return(nil, errors.New("error")).AnyTimes()
			},
		},
		{
			name:    "get empty data from cache",
			uidList: []uint32{1, 2},
			want: &pb.BatchGetUserFirstTakeOrdersTimeResponse{
				TimeMap: map[uint32]int64{},
			},
			wantErr: false,
			initFunc: func(m *mangerForTest) {
				m.getCache().EXPECT().BatchGetUsersFirstTradeTime(gomock.Any(), []uint32{1, 2}).Return(make(map[uint32]int64), nil).AnyTimes()
			},
		},
		{
			name:    "get data from cache success",
			uidList: []uint32{1, 2},
			want: &pb.BatchGetUserFirstTakeOrdersTimeResponse{
				TimeMap: map[uint32]int64{1: 1000, 2: 2000},
			},
			wantErr: false,
			initFunc: func(m *mangerForTest) {
				m.getCache().EXPECT().BatchGetUsersFirstTradeTime(gomock.Any(), []uint32{1, 2}).Return(map[uint32]int64{1: 1000, 2: 2000}, nil).AnyTimes()
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := NewMangerForTest(t)
			if tt.initFunc != nil {
				tt.initFunc(m)
			}
			got, err := m.BatchGetUsersFirstTradeTime(context.Background(), tt.uidList)
			if (err != nil) != tt.wantErr {
				t.Errorf("BatchGetUsersFirstTradeTime() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("BatchGetUsersFirstTradeTime() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestManager_GetCoachTradeData(t *testing.T) {
	tests := []struct {
		name     string
		uid      uint32
		gameId   uint32
		want     *pb.GetCoachStatisticsResponse
		wantErr  bool
		initFunc func(m *mangerForTest)
	}{
		{
			name:     "uid is zero",
			uid:      0,
			gameId:   1,
			want:     &pb.GetCoachStatisticsResponse{},
			wantErr:  true,
			initFunc: nil,
		},
		{
			name:     "gameId is zero",
			uid:      1,
			gameId:   0,
			want:     &pb.GetCoachStatisticsResponse{},
			wantErr:  true,
			initFunc: nil,
		},
		{
			name:    "get data from store failed",
			uid:     1,
			gameId:  1,
			want:    &pb.GetCoachStatisticsResponse{},
			wantErr: true,
			initFunc: func(m *mangerForTest) {
				m.getStore().EXPECT().GetCoachTradeData(gomock.Any(), uint32(1), uint32(1)).Return(nil, errors.New("error")).AnyTimes()
			},
		},
		{
			name:   "get data from store success",
			uid:    1,
			gameId: 1,
			want: &pb.GetCoachStatisticsResponse{
				Data: &pb.StatisticsData{
					OrderNum:    10,
					CustomerNum: 5,
				},
			},
			wantErr: false,
			initFunc: func(m *mangerForTest) {
				m.getStore().EXPECT().GetCoachTradeData(gomock.Any(), uint32(1), uint32(1)).Return(&store.TradeData{
					OrderNum: 10,
					UserCnt:  5,
				}, nil).AnyTimes()
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := NewMangerForTest(t)
			if tt.initFunc != nil {
				tt.initFunc(m)
			}
			got, err := m.GetCoachTradeData(context.Background(), tt.uid, tt.gameId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetCoachTradeData() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetCoachTradeData() got = %v, want %v", got, tt.want)
			}
		})
	}
}
