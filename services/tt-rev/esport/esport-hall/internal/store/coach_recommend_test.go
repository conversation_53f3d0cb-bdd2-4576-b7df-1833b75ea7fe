package store

import (
	"testing"
	"time"
)

func TestStore_AddCoachRecommend(t *testing.T) {
	testStore.AddCoachRecommend(ctx, &CoachRecommend{
		Uid:        2465835,
		Ttid:       "111",
		GameId:     1,
		NickName:   "刘华",
		CoachType:  1,
		Sort:       1.1,
		StartTime:  1705305600,
		EndTime:    1705316400,
		Deleted:    false,
		CreateTime: time.Now(),
	})
}

func TestStore_DelCoachRecommend(t *testing.T) {
	testStore.DelCoachRecommend(ctx, "65a4f36634d335358b72b80f")
}

func TestStore_UpdateCoachRecommend(t *testing.T) {
	testStore.UpdateCoachRecommend(ctx, "65b9f89a0774999892b6398f", &CoachRecommend{
		Uid:        2465835,
		Ttid:       "111",
		GameId:     1,
		CoachType:  1,
		Sort:       1.339,
		StartTime:  2,
		EndTime:    3,
	})
}

func TestStore_GetCoachRecommend(t *testing.T) {
    //testStore.GetCoachRecommend(ctx, 1, 0, 0, 0, 0)
    //testStore.GetCoachRecommend(ctx, 0, 111, 0, 0, 0)
    //testStore.GetCoachRecommend(ctx, 0, 0, 2, 0, 0)
    testStore.GetCoachRecommend(ctx, "0", 0, 0, 1, 5)
    //testStore.GetCoachRecommend(ctx, 1, 111, 2, 1705305600, 1705316400)
}

