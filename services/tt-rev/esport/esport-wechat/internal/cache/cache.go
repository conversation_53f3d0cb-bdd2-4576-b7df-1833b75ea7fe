package cache

import (
	"context"
	"fmt"
	redisConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/redis/connect"
	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"
	"golang.52tt.com/pkg/log"
	"time"
)

type Cache struct {
	cmder redis.Cmdable
}

func NewCache(ctx context.Context, cfg *redisConnect.RedisConfig) (ICache, error) {
	client, err := redisConnect.NewClient(ctx, cfg)
	if err != nil {
		return nil, err
	}

	c := &Cache{
		cmder: client,
	}
	return c, nil
}

func (c *Cache) Close() error {
	return c.cmder.(redis.Client).Close()
}

func (c *Cache) GetRedisClient() redis.Cmdable {
	return c.cmder
}

func (c *Cache) Get(key string) interface{} {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()
	return c.cmder.Get(ctx, key).Val()
}

func (c *Cache) Set(key string, val interface{}, timeout time.Duration) error {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()
	log.InfoWithCtx(ctx, "cache set key:%s, val:%v, timeout:%v", key, val, timeout)
	return c.cmder.Set(ctx, key, val, timeout).Err()
}

func (c *Cache) IsExist(key string) bool {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()
	return c.cmder.Exists(ctx, key).Val() > 0
}

func (c *Cache) Delete(key string) error {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()
	return c.cmder.Del(ctx, key).Err()
}

func (c *Cache) SetSessionId(ctx context.Context, uid uint32, sessionId string, ttl time.Duration) error {
	c.cmder.Set(ctx, fmt.Sprintf("uid:to:session:%d", uid), sessionId, ttl)
	return c.cmder.Set(ctx, sessionId, uid, ttl).Err()
}

func (c *Cache) GetUidBySessionId(ctx context.Context, sessionId string) (bool, uint32, error) {
	uid, err := c.cmder.Get(ctx, sessionId).Int64()
	if err != nil {
		if redis.IsNil(err) {
			return false, 0, nil
		}

		return false, 0, err
	}
	return true, uint32(uid), nil
}

func (c *Cache) GetSessionIdByUid(ctx context.Context, uid uint32) (string, error) {
	session, err := c.cmder.Get(ctx, fmt.Sprintf("uid:to:session:%d", uid)).Result()
	if err != nil {
		if redis.IsNil(err) {
			return "", nil
		}

		return session, err
	}
	return session, nil
}

func (c *Cache) GetSingleMsgCount(ctx context.Context, uid, toUid uint32) (uint32, error) {
	key := fmt.Sprintf("single_msg_interval:%d:%d", uid, toUid)
	val, err := c.cmder.Get(ctx, key).Int64()
	if err != nil {
		if redis.IsNil(err) {
			return 0, nil
		}
		return 0, err
	}

	return uint32(val), nil
}

func (c *Cache) IncrSingleMsgCount(ctx context.Context, uid, toUid, expiredSec uint32) (uint32, error) {
	key := fmt.Sprintf("single_msg_interval:%d:%d", uid, toUid)
	var incr *redis.IntCmd
	_, err := c.cmder.Pipelined(ctx, func(pipe redis.Pipeliner) error {
		incr = pipe.Incr(ctx, key)
		return pipe.Expire(ctx, key, time.Duration(expiredSec)*time.Second).Err()
	})
	if err != nil || incr.Err() != nil {
		log.ErrorWithCtx(ctx, "IncrSingleMsgCount err uid:%d, err:%v", uid, err)
		return 0, err
	}

	return uint32(incr.Val()), err
}

func (c *Cache) GetMultiMasterCount(ctx context.Context, uid uint32) uint32 {
	masterKey := fmt.Sprintf("multi_master_interval:%d", uid)
	return uint32(c.cmder.HLen(ctx, masterKey).Val())
}

func (c *Cache) GetIsMultiMaster(ctx context.Context, uid, toUid uint32) (bool, error) {
	masterKey := fmt.Sprintf("multi_master_interval:%d", uid)
	val, err := c.cmder.HGet(ctx, masterKey, fmt.Sprintf("%d", toUid)).Int()
	if redis.IsNil(err) {
		return false, nil
	}

	return uint32(val) == toUid, err
}

func (c *Cache) IncrMultiMasterCount(ctx context.Context, uid, toUid, expiredSec uint32) (uint32, error) {
	var masterCmd *redis.IntCmd
	_, err := c.cmder.Pipelined(ctx, func(pipe redis.Pipeliner) error {
		masterKey := fmt.Sprintf("multi_master_interval:%d", uid)
		pipe.HSet(ctx, masterKey, toUid, toUid)
		pipe.Expire(ctx, masterKey, time.Duration(expiredSec)*time.Second).Err()
		masterCmd = pipe.HLen(ctx, masterKey)
		return nil
	})
	if err != nil || masterCmd.Err() != nil {
		log.ErrorWithCtx(ctx, "IncrMultiMsgCount err uid:%d, err:%v", uid, err)
		return 0, err
	}

	return uint32(masterCmd.Val()), nil
}

func (c *Cache) SetAccessToken(ctx context.Context, accessToken string, ttl time.Duration) error {
	key := "wechat_assess_token"
	return c.cmder.Set(ctx, key, accessToken, ttl).Err()
}

func (c *Cache) GetAccessToken(ctx context.Context) (string, uint32, error) {
	key := "wechat_assess_token"
	value, err := c.cmder.Get(ctx, key).Result()
	if err != nil {
		if redis.IsNil(err) {
			return "", 0, nil
		}

		return "", 0, err
	}

	expired := c.cmder.TTL(ctx, key).Val().Seconds()
	return value, uint32(expired), nil
}
