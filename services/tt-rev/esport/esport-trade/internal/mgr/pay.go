package mgr

import (
	"context"
	"encoding/json"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"golang.52tt.com/pkg/deal_token"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/esport-trade"
	kfk_esport_trade "golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafka-esport-trade"
	reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"
	unifiedPayPB "golang.52tt.com/protocol/services/unified_pay"
	"golang.52tt.com/services/tt-rev/esport/esport-trade/internal/store"
	"time"
)

// payFreeze 冻结T豆
func (m *Mgr) payFreeze(ctx context.Context, orderId string, uid, totalPrice uint32, outsideTime time.Time) (uint32, error) {
	// unified-pay 冻结T豆
	timeStr := outsideTime.Format("2006-01-02 15:04:05")
	reason := "电竞下单"

	restBalance, sErr := m.rpcCli.UnifyPayCli.PresetFreeze(ctx, uid, totalPrice, m.bc.GetPayAppId(), orderId, timeStr, reason)
	if sErr != nil {
		log.ErrorWithCtx(ctx, "payFreeze fail to PresetFreeze. uid:%v, orderId:%v, outsideTime:%v, err:%v", uid, orderId, outsideTime, sErr)
		return restBalance, sErr
	}

	log.Infof("payFreeze uid:%d, orderId:%s, totalPrice:%d, outsideTime:%v, restBalance:%d", uid, orderId, totalPrice, outsideTime, restBalance)
	return restBalance, nil
}

// payCommit 确认订单
func (m *Mgr) payCommit(ctx context.Context, orderId string, uid uint32, outsideTime time.Time) error {
	order, exist, err := m.mysqlStore.GetConsumeRecordByOrderId(ctx, uid, getOrderTimeFromOrderId(orderId), orderId)
	if err != nil {
		log.ErrorWithCtx(ctx, "payCommit fail to GetConsumeRecordByOrderId. uid:%v, orderId:%v, outsideTime:%v, err:%v", uid, orderId, outsideTime, err)
		return err
	}

	if !exist {
		return protocol.NewExactServerError(nil, status.ErrEsportsOrderNotExist)
	}

	var tBeanTime time.Time
	var dealToken string
	if order.CommitFee > 0 {
		var err error
		// 确认消费
		tBeanTime, dealToken, err = m.unfreezeAndConsume(ctx, order, uid, outsideTime)
		if err != nil {
			log.ErrorWithCtx(ctx, "payCommit fail to unfreezeAndConsume. uid:%v, orderId:%v, outsideTime:%v, err:%v", uid, orderId, outsideTime, err)
			return err
		}

	} else if order.Status == store.ConsumeStatusInit ||
		order.Status == store.ConsumeStatusCommit ||
		order.Status == store.ConsumeStatusDone {

		var err error
		// 全部回滚
		tBeanTime, err = m.payRollback(ctx, order, uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "payCommit fail to payRollback. uid:%v, orderId:%v, outsideTime:%v, err:%v", uid, orderId, outsideTime, err)
			return err
		}

	} else {
		return protocol.NewExactServerError(nil, status.ErrEsportsOrderCommonError)
	}

	// 更新T豆支付信息
	_, err2 := m.mysqlStore.UpdateConsumeTBeanInfo(ctx, orderId, dealToken, order.CreateTime, tBeanTime)
	if err2 != nil {
		log.ErrorWithCtx(ctx, "payCommit fail to UpdateConsumeTBeanInfo. uid:%v, orderId:%v, outsideTime:%v, err:%v", uid, orderId, outsideTime, err2)
		return err2
	}

	log.Infof("payCommit uid:%d, orderId:%s, outsideTime:%v, tBeanTime:%v", uid, orderId, outsideTime, tBeanTime)
	return nil
}

func (m *Mgr) unfreezeAndConsume(ctx context.Context, order *store.ConsumeRecord, uid uint32, outsideTime time.Time) (time.Time, string, error) {
	orderId := order.OrderId
	user, err := m.rpcCli.AccountCli.GetUser(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "unfreezeAndConsume fail to GetUser. uid:%v, orderId:%v, outsideTime:%v, err:%v", uid, orderId, outsideTime, err)
		return time.Now(), "", err
	}

	req := &unifiedPayPB.UnfreezeAndConsumeReq{
		AppId:        m.bc.GetPayAppId(),
		Uid:          uid,
		UserName:     user.GetUsername(),
		ItemId:       1,
		ItemName:     "电竞下单",
		ItemNum:      1,
		ItemPrice:    order.CommitFee, // 货币组要求num*price=total，但这里可能有优惠价对不上，所以这里直接传总价
		TotalPrice:   order.CommitFee,
		Platform:     "0",
		OutTradeNo:   orderId,
		Notes:        "esport-trade",
		OutOrderTime: outsideTime.Format("2006-01-02 15:04:05"),
		RefundPrice:  order.FreezeFee - order.CommitFee,
	}

	// unified-pay 确认扣除T豆
	timeStr, dealToken, err := m.rpcCli.UnifyPayCli.UnfreezeAndConsume(ctx, req)
	if err != nil {
		// 货币组commit接口在并发时会因锁报错，在这里重试一次
		retryCtx, cancel := context.WithTimeout(context.Background(), 1*time.Second)
		defer cancel()

		timeStr, dealToken, err = m.rpcCli.UnifyPayCli.UnfreezeAndConsume(retryCtx, req)
		if err != nil {
			log.ErrorWithCtx(ctx, "unfreezeAndConsume fail to UnfreezeAndConsume. uid:%v, orderId:%v, outsideTime:%v, err:%v", uid, orderId, outsideTime, err)
			return time.Now(), "", err
		}
	}

	tBeanTime, _ := time.ParseInLocation("2006-01-02 15:04:05", timeStr, time.Local)

	newDt := deal_token.NewDealTokenData(orderId, orderId, "esport-trade", int64(req.GetUid()), int64(order.CommitFee))
	newToken, err2 := deal_token.AddDealToken(dealToken, newDt)
	if err2 != nil {
		log.ErrorWithCtx(ctx, "unfreezeAndConsume fail to AddDealToken. uid:%v, orderId:%v, outsideTime:%v, err:%v", uid, orderId, outsideTime, err2)
	} else {
		// 结算kafka
		m.orderKfk.SendOrderSettleMsg(ctx, &kfk_esport_trade.EsportTradeSettleEvent{
			OrderId:         orderId,
			PlayerUid:       order.Uid,
			CoachUid:        order.CoachUid,
			TotalPrice:      order.CommitFee,
			PayTime:         order.FreezeTime.Unix(),
			SettleTime:      tBeanTime.Unix(),
			DealToken:       newToken,
			EventTime:       time.Now().Unix(),
			SignGuildId:     order.GuildId,
			CoachTotalPrice: order.CommitFee + order.PlatBonusFee,
		})
	}

	log.InfoWithCtx(ctx, "unfreezeAndConsume uid:%d, orderId:%s, outsideTime:%v, tBeanTime:%v", uid, orderId, outsideTime, tBeanTime)
	return tBeanTime, dealToken, nil
}

func (m *Mgr) payRollback(ctx context.Context, order *store.ConsumeRecord, uid uint32) (time.Time, error) {
	orderId := order.OrderId

	// unified-pay 回滚T豆
	err := m.rpcCli.UnifyPayCli.UnFreezeAndRefund(ctx, uid, m.bc.GetPayAppId(), orderId)
	if err != nil {
		// 货币组commit接口在并发时会因锁报错，在这里重试一次
		retryCtx, cancel := context.WithTimeout(context.Background(), 1*time.Second)
		defer cancel()

		err = m.rpcCli.UnifyPayCli.UnFreezeAndRefund(retryCtx, uid, m.bc.GetPayAppId(), orderId)
		if err != nil {
			log.ErrorWithCtx(ctx, "payRollback fail to UnFreezeAndRefund. uid:%v, orderId:%v, err:%v", uid, orderId, err)
			return time.Now(), err
		}
	}

	log.InfoWithCtx(ctx, "payRollback uid:%d, orderId:%s", uid, orderId)
	return time.Now(), nil
}

// PayCallback 支付回调
func (m *Mgr) PayCallback(ctx context.Context, uid uint32, orderId string) error {
	orderTime := getOrderTimeFromOrderId(orderId)
	order, exist, err := m.mysqlStore.GetOrderRecord(ctx, nil, orderTime, orderId)
	if err != nil {
		log.ErrorWithCtx(ctx, "PayCallback fail to GetConsumeRecordByOrderId. orderId:%v, err:%v", orderId, err)
		return err
	}

	if !exist {
		return protocol.NewExactServerError(nil, status.ErrEsportsOrderCommonError, "订单不存在")
	}

	if order.Status == uint32(pb.OrderStatus_ORDER_STATUS_PAYED) ||
		order.Status == uint32(pb.OrderStatus_ORDER_STATUS_RECEIVED) ||
		order.Status == uint32(pb.OrderStatus_ORDER_STATUS_IN_REFUNDING) {
		return protocol.NewExactServerError(nil, status.ErrEsportsOrderCommonError, "订单还在进行中")
	}

	// 去确认订单
	err = m.payCommit(ctx, orderId, uid, time.Now())
	if err != nil {
		log.ErrorWithCtx(ctx, "PayCallback fail to payCommit. uid:%v, orderId:%v, err:%v", uid, orderId, err)
		return err
	}

	log.Infof("PayCallback uid:%d, order:%v, orderId:%v", uid, order, orderId)
	return nil
}

type TimeRangeReqParam struct {
	UseCoachTotalPrice bool `json:"use_coach_total_price"`
}

func NewTimeRangeReqParam(str string) *TimeRangeReqParam {
	param := &TimeRangeReqParam{}
	if len(str) > 0 {
		_ = json.Unmarshal([]byte(str), param)
	}
	return param
}

func (m *Mgr) GetPayTotalCount(ctx context.Context, req *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error) {
	out := &reconcile_v2.CountResp{}
	begin := time.Unix(req.GetBeginTime(), 0)
	end := time.Unix(req.GetEndTime(), 0)
	param := NewTimeRangeReqParam(req.GetParams())

	if begin.AddDate(0, 0, 7).Before(end) {
		return out, protocol.NewExactServerError(nil, status.ErrEsportsOrderCommonError, "时间范围不能超过7天")
	}

	info, err := m.mysqlStore.GetConsumeTotalCountInfo(ctx, begin, end, param.UseCoachTotalPrice)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPayTotalCount fail to GetConsumeTotalCountInfo. begin:%v, end:%v, err:%v", begin, end, err)
		return out, err
	}

	out.Count = uint32(info.Count)
	out.Value = uint32(info.Worth)

	return out, nil
}

func (m *Mgr) GetPayOrderIds(ctx context.Context, req *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error) {
	out := &reconcile_v2.OrderIdsResp{}
	begin := time.Unix(req.GetBeginTime(), 0)
	end := time.Unix(req.GetEndTime(), 0)

	if begin.AddDate(0, 0, 7).Before(end) {
		return out, protocol.NewExactServerError(nil, status.ErrEsportsOrderCommonError, "时间范围不能超过7天")
	}

	list, err := m.mysqlStore.GetConsumeOrderIds(ctx, begin, end)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPayOrderIds fail to GetConsumeOrderIds. begin:%v, end:%v, err:%v", begin, end, err)
		return out, err
	}

	out.OrderIds = list
	return out, nil
}

func (m *Mgr) GetOrderConsume(ctx context.Context, req *pb.GetOrderConsumeRequest) (*pb.GetOrderConsumeResponse, error) {
	out := &pb.GetOrderConsumeResponse{}
	orderTime := getOrderTimeFromOrderId(req.GetOrderId())

	info, exist, err := m.mysqlStore.GetConsumeRecordByOrderId(ctx, 0, orderTime, req.GetOrderId())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetOrderConsume fail to GetConsumeRecordByOrderId. orderId:%v, err:%v", req.GetOrderId(), err)
		return out, err
	}

	if !exist {
		return out, protocol.NewExactServerError(nil, status.ErrEsportsOrderNotExist)
	}

	if info.Status != store.ConsumeStatusDone {
		return out, protocol.NewExactServerError(nil, status.ErrEsportsOrderCommonError, "订单还未完成")
	}

	orderId := req.GetOrderId()
	newDt := deal_token.NewDealTokenData(orderId, orderId, "esport-trade", int64(info.Uid), int64(info.CommitFee))
	newToken, err := deal_token.AddDealToken(info.DealToken, newDt)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetOrderConsume fail to AddDealToken. orderId:%v, err:%v", req.GetOrderId(), err)
		return out, err
	}

	out = &pb.GetOrderConsumeResponse{
		OrderId:          orderId,
		CoachUid:         info.CoachUid,
		PlayerUid:        info.Uid,
		CommitCount:      info.CommitAmount,
		CommitTotalPrice: info.CommitFee,
		CommitTime:       info.CommitTime.Unix(),
		SignGuildId:      info.GuildId,
		DealToken:        newToken,
		CoachTotalPrice:  info.CommitFee + info.PlatBonusFee,
	}

	return out, nil
}
