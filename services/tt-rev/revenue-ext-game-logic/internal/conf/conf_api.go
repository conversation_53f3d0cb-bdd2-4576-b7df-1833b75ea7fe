package conf

import(
)

type IBusinessConfManager interface {
	CheckChannelTypeLimit(channelType uint32) bool
	Close() 
	GetExtGameAccessInfoList() []*ExtGameAccessInfo
	GetExtGameVersionSwitch(marketId, clientVersion uint32) (bool,error)
	GetGameAppConfByAppid(appid string) (*GameAppConf,bool)
	Reload(file string) error
	Watch(file string) 
}


type IBusinessConf interface {
	CheckConf() error
	Parse(configFile string) (isChange bool,err error)
}

