package conf

//go:generate quicksilver-cli test interface ../conf
//go:generate mockgen -destination=./mocks/business_config.go -package=mocks golang.52tt.com/services/tt-rev/revenue-ext-game-logic/internal/conf IBusinessConfManager

import (
    "crypto/md5"
    "encoding/json"
    "fmt"
    "io/ioutil"
    "os"
    "time"

    "gitlab.ttyuyin.com/bizFund/bizFund/pkg/marketid_helper"
    "gitlab.ttyuyin.com/bizFund/bizFund/pkg/protocol"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    "strconv"
    "strings"
)

const (
    BusinessConfPath = "/data/oss/conf-center/tt/"
    BusinessConfFile = "channel-ext-game.json"
)

var LastConfMd5Sum [md5.Size]byte

type ExtGameAccessInfo struct {
    Appid               string `json:"app_id"`
    PopupUrl            string `json:"popup_url"`
    DaysShowAfterPlay   uint32 `json:"days_show_after_play"`
    DaysShowAfterIgnore uint32 `json:"days_show_after_ignore"`
    HasRed              bool   `json:"has_red"`
    FloatingUrl         string `json:"floating_url"`
    PgcFloatingUrl      string `json:"Pgc_floating_url"`
    ShowInMore          bool   `json:"show_in_more"`
    Icon                string `json:"icon"`
    Name                string `json:"name"`
}

type GameAppConf struct {
    Switch     bool               `json:"switch"` // 游戏开关
    AccessInfo *ExtGameAccessInfo `json:"access_info"`
}

type BusinessConf struct {
    GameAppConfMap             map[string]*GameAppConf `json:"game_app_conf_map"`
    GameSupportChannelTypeList []uint32                `json:"game_support_channel_type_list"`
}

func (c *BusinessConf) Parse(configFile string) (isChange bool, err error) {
    defer func() {
        if e := recover(); e != nil {
            err = fmt.Errorf("Failed to parse config: %v \n", e)
        }
    }()

    data, err := ioutil.ReadFile(configFile)
    if err != nil {
        return false, err
    }

    md5Sum := md5.Sum(data)
    if md5Sum == LastConfMd5Sum {
        isChange = false
        return
    }

    err = json.Unmarshal(data, &c)
    if err != nil {
        return false, err
    }

    err = c.CheckConf()
    if err != nil {
        return false, err
    }

    LastConfMd5Sum = md5Sum

    log.Infof("BusinessConf : %+v", c)
    return true, nil
}

type BusinessConfManager struct {
    Done chan interface{}
    //mutex sync.RWMutex
    conf *BusinessConf
}

func NewBusinessConfManager() (*BusinessConfManager, error) {
    businessConf := &BusinessConf{}

    businessConfFilePath := BusinessConfPath + BusinessConfFile
    if devBusinessConfPath := os.Getenv("DEV_BUSINESS_CONF_PATH"); devBusinessConfPath != "" {
        businessConfFilePath = devBusinessConfPath + BusinessConfFile
    }
    _, err := businessConf.Parse(businessConfFilePath)
    if err != nil {
        log.Errorf("NewBusinessConfManager fail to Parse BusinessConf err:%v", err)
        return nil, err
    }

    confMgr := &BusinessConfManager{
        conf: businessConf,
        Done: make(chan interface{}),
    }

    go confMgr.Watch(businessConfFilePath)

    return confMgr, nil
}

func (bm *BusinessConfManager) Reload(file string) error {
    businessConf := &BusinessConf{}

    isChange, err := businessConf.Parse(file)
    if err != nil {
        log.Errorf("NewBusinessConfManager fail to Parse BusinessConf err:%v", err)
        return err
    }

    if isChange {
        //bm.mutex.Lock()
        bm.conf = businessConf
        //bm.mutex.Unlock()

        log.Infof("Reload %+v", businessConf)
    }

    return nil
}

func (bm *BusinessConfManager) Watch(file string) {
    log.Infof("Watch start. file:%s", file)

    for {
        select {
        case _, ok := <-bm.Done:
            if !ok {
                log.Infof("Watch done")
                return
            }

        case <-time.After(30 * time.Second):
            log.Debugf("Watch check change")

            err := bm.Reload(file)
            if err != nil {
                log.Errorf("Watch Reload fail. file:%s, err:%v", file, err)
            }
        }
    }
}

func (bm *BusinessConfManager) Close() {
    close(bm.Done)
}

func (c *BusinessConf) CheckConf() error {
    return nil
}

// GetGameAppConfByAppid return conf, exist
func (bm *BusinessConfManager) GetGameAppConfByAppid(appid string) (*GameAppConf, bool) {
    if bm.conf == nil || bm.conf.GameAppConfMap == nil {
        return nil, false
    }
    if appConf, ok := bm.conf.GameAppConfMap[appid]; ok {
        return appConf, true
    }
    return nil, false
}

func (bm *BusinessConfManager) GetExtGameAccessInfoList() []*ExtGameAccessInfo {
    returnList := make([]*ExtGameAccessInfo, 0)
    for _, v := range bm.conf.GameAppConfMap {
        if v.Switch && v.AccessInfo != nil {
            returnList = append(returnList, v.AccessInfo)
        }
    }

    return returnList
}

// GetExtGameVersionSwitch 查询外部游戏版本控制开关状态
func (bm *BusinessConfManager) GetExtGameVersionSwitch(marketId, clientVersion uint32) (bool, error) {
    // 通过马甲包获取最低可见入口的版本号
    minVersion := marketid_helper.Get("channel_ext_game_min_version", marketId, 0)
    if version, ok := parseCliVersion(minVersion); !ok ||
        protocol.ClientVersion(clientVersion) < version {

        // 未达到指定客户端版本，不可见入口
        return false, nil
    }

    return true, nil
}

func parseCliVersion(version string) (protocol.ClientVersion, bool) {
    subStrings := strings.Split(version, ".")
    subVers := make([]uint16, 0)
    for _, subString := range subStrings {
        t, err := strconv.ParseUint(subString, 10, 32)
        if err != nil {
            log.Debugf("parse version fail, version: %s, err: %v", version, err)
            continue
        }
        subVers = append(subVers, uint16(t))
    }
    if 3 != len(subVers) {
        return protocol.ClientVersion(0), false
    }

    iVersion := protocol.ClientVersion(protocol.FormatClientVersion(uint8(subVers[0]), uint8(subVers[1]), subVers[2]))
    return iVersion, true
}

// CheckChannelTypeLimit GameSupportChannelType
func (bm *BusinessConfManager) CheckChannelTypeLimit(channelType uint32) bool {
    if len(bm.conf.GameSupportChannelTypeList) == 0 {
        return true
    }

    for _, v := range bm.conf.GameSupportChannelTypeList {
        if v == channelType {
            return true
        }
    }

    return false
}
