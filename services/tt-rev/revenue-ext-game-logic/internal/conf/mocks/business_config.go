// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/tt-rev/revenue-ext-game-logic/internal/conf (interfaces: IBusinessConfManager)

// Package mocks is a generated GoMock package.
package mocks

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	conf "golang.52tt.com/services/tt-rev/revenue-ext-game-logic/internal/conf"
)

// MockIBusinessConfManager is a mock of IBusinessConfManager interface.
type MockIBusinessConfManager struct {
	ctrl     *gomock.Controller
	recorder *MockIBusinessConfManagerMockRecorder
}

// MockIBusinessConfManagerMockRecorder is the mock recorder for MockIBusinessConfManager.
type MockIBusinessConfManagerMockRecorder struct {
	mock *MockIBusinessConfManager
}

// NewMockIBusinessConfManager creates a new mock instance.
func NewMockIBusinessConfManager(ctrl *gomock.Controller) *MockIBusinessConfManager {
	mock := &MockIBusinessConfManager{ctrl: ctrl}
	mock.recorder = &MockIBusinessConfManagerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIBusinessConfManager) EXPECT() *MockIBusinessConfManagerMockRecorder {
	return m.recorder
}

// CheckChannelTypeLimit mocks base method.
func (m *MockIBusinessConfManager) CheckChannelTypeLimit(arg0 uint32) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckChannelTypeLimit", arg0)
	ret0, _ := ret[0].(bool)
	return ret0
}

// CheckChannelTypeLimit indicates an expected call of CheckChannelTypeLimit.
func (mr *MockIBusinessConfManagerMockRecorder) CheckChannelTypeLimit(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckChannelTypeLimit", reflect.TypeOf((*MockIBusinessConfManager)(nil).CheckChannelTypeLimit), arg0)
}

// Close mocks base method.
func (m *MockIBusinessConfManager) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIBusinessConfManagerMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIBusinessConfManager)(nil).Close))
}

// GetExtGameAccessInfoList mocks base method.
func (m *MockIBusinessConfManager) GetExtGameAccessInfoList() []*conf.ExtGameAccessInfo {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetExtGameAccessInfoList")
	ret0, _ := ret[0].([]*conf.ExtGameAccessInfo)
	return ret0
}

// GetExtGameAccessInfoList indicates an expected call of GetExtGameAccessInfoList.
func (mr *MockIBusinessConfManagerMockRecorder) GetExtGameAccessInfoList() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExtGameAccessInfoList", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetExtGameAccessInfoList))
}

// GetExtGameVersionSwitch mocks base method.
func (m *MockIBusinessConfManager) GetExtGameVersionSwitch(arg0, arg1 uint32) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetExtGameVersionSwitch", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExtGameVersionSwitch indicates an expected call of GetExtGameVersionSwitch.
func (mr *MockIBusinessConfManagerMockRecorder) GetExtGameVersionSwitch(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExtGameVersionSwitch", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetExtGameVersionSwitch), arg0, arg1)
}

// GetGameAppConfByAppid mocks base method.
func (m *MockIBusinessConfManager) GetGameAppConfByAppid(arg0 string) (*conf.GameAppConf, bool) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGameAppConfByAppid", arg0)
	ret0, _ := ret[0].(*conf.GameAppConf)
	ret1, _ := ret[1].(bool)
	return ret0, ret1
}

// GetGameAppConfByAppid indicates an expected call of GetGameAppConfByAppid.
func (mr *MockIBusinessConfManagerMockRecorder) GetGameAppConfByAppid(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGameAppConfByAppid", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetGameAppConfByAppid), arg0)
}

// Reload mocks base method.
func (m *MockIBusinessConfManager) Reload(arg0 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Reload", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// Reload indicates an expected call of Reload.
func (mr *MockIBusinessConfManagerMockRecorder) Reload(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Reload", reflect.TypeOf((*MockIBusinessConfManager)(nil).Reload), arg0)
}

// Watch mocks base method.
func (m *MockIBusinessConfManager) Watch(arg0 string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Watch", arg0)
}

// Watch indicates an expected call of Watch.
func (mr *MockIBusinessConfManagerMockRecorder) Watch(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Watch", reflect.TypeOf((*MockIBusinessConfManager)(nil).Watch), arg0)
}
