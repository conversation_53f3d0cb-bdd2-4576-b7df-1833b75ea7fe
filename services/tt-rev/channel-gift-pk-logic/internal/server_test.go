package internal

import (
    "context"
    "github.com/golang/mock/gomock"
    mockChannel "golang.52tt.com/clients/mocks/channel"
    mockRealName "golang.52tt.com/clients/mocks/realnameauth"
    "golang.52tt.com/clients/mocks/risk-mng-api"
    mockUserOnline "golang.52tt.com/clients/mocks/user-online"
    mockProfile "golang.52tt.com/clients/mocks/user-profile-api"
    mockPresent "golang.52tt.com/clients/mocks/userpresent"
    mockDevice "golang.52tt.com/clients/mocks/usual-device"
    protogrpc "golang.52tt.com/pkg/protocol/grpc"
    channelPB "golang.52tt.com/protocol/app/channel"
    pb "golang.52tt.com/protocol/app/channel_gift_pk"
    "golang.52tt.com/protocol/services/channel_gift_pk"
    channelPb "golang.52tt.com/protocol/services/channelsvr"
    "golang.52tt.com/protocol/services/mocks"
    "testing"
)

var (
    channelGiftPkCli  *mocks.MockChannelGiftPkClient
    riskMngApiCli     *risk_mng_api.MockIClient
    usualDeviceClient *mockDevice.MockIClient
    realNameClient    *mockRealName.MockIClient
    presentClient     *mockPresent.MockIClient
    userProfileApiCli *mockProfile.MockIClient
    channelCli        *mockChannel.MockIClient
    userOnlineCli     *mockUserOnline.MockIClient
    uid               uint32 = 1000
    cid               uint32 = 2000
    ctx                      = protogrpc.WithServiceInfo(context.Background(), &protogrpc.ServiceInfo{UserID: uid})
)

func initSvr(ctl *gomock.Controller) *Server {
    channelGiftPkCli = mocks.NewMockChannelGiftPkClient(ctl)
    riskMngApiCli = risk_mng_api.NewMockIClient(ctl)
    usualDeviceClient = mockDevice.NewMockIClient(ctl)
    realNameClient = mockRealName.NewMockIClient(ctl)
    presentClient = mockPresent.NewMockIClient(ctl)
    userProfileApiCli = mockProfile.NewMockIClient(ctl)
    channelCli = mockChannel.NewMockIClient(ctl)
    userOnlineCli = mockUserOnline.NewMockIClient(ctl)

    return &Server{
        channelGiftPkCli:  channelGiftPkCli,
        riskMngApiCli:     riskMngApiCli,
        usualDeviceClient: usualDeviceClient,
        realNameClient:    realNameClient,
        userPresentCli:    presentClient,
        UserProfileCli:    userProfileApiCli,
        channelCli:        channelCli,
        userOnlineCli:     userOnlineCli,
    }
}

func TestServer_CheckChannelGiftPkEntry(t *testing.T) {
    ctl := gomock.NewController(t)
    defer ctl.Finish()
    svr := initSvr(ctl)

    type args struct {
        ctx context.Context
        in  *pb.CheckChannelGiftPkEntryRequest
    }
    tests := []struct {
        name    string
        args    args
        wantErr bool
        f       func()
    }{
        {
            name: "common",
            args: args{
                ctx: ctx,
                in: &pb.CheckChannelGiftPkEntryRequest{
                    ChannelId: cid,
                },
            },
            wantErr: false,
            f: func() {
                channelGiftPkCli.EXPECT().CheckChannelGiftPkEntry(gomock.Any(), gomock.Any()).Return(&channel_gift_pk.CheckChannelGiftPkEntryResp{
                    HasEntry: true,
                    GiftPkCfg: &channel_gift_pk.GiftPkCfg{
                        PkLvGifts: []*channel_gift_pk.PkLvGiftCfg{
                            {GiftId: 1},
                        },
                    },
                }, nil)
                presentClient.EXPECT().GetPresentConfigByIdList(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
            },
        },
    }
    for _, tt := range tests {
        tt.f()
        t.Run(tt.name, func(t *testing.T) {
            _, err := svr.CheckChannelGiftPkEntry(tt.args.ctx, tt.args.in)
            if (err != nil) != tt.wantErr {
                t.Errorf("CheckChannelGiftPkEntry() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
        })
    }
}

func TestServer_GetChannelGiftPkInfo(t *testing.T) {
    ctl := gomock.NewController(t)
    defer ctl.Finish()
    svr := initSvr(ctl)

    gomock.InOrder(
        channelGiftPkCli.EXPECT().GetChannelGiftPkInfo(gomock.Any(), gomock.Any()).Return(&channel_gift_pk.GetChannelGiftPkInfoResp{
            GiftPkInfo: &channel_gift_pk.GiftPkInfo{
                MyPkInfos: &channel_gift_pk.GiftPkChannelInfo{
                    AnchorUid: 1,
                },
                PkGift: &channel_gift_pk.PkLvGiftCfg{
                    GiftId: 1,
                },
            },
        }, nil),
        userProfileApiCli.EXPECT().BatchGetUserProfileV2(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil),
        presentClient.EXPECT().GetPresentConfigById(gomock.Any(), gomock.Any()).Return(nil, nil),
    )

    type args struct {
        ctx context.Context
        in  *pb.GetChannelGiftPkInfoRequest
    }
    tests := []struct {
        name    string
        args    args
        wantErr bool
    }{
        {
            name: "common",
            args: args{
                ctx: ctx,
                in: &pb.GetChannelGiftPkInfoRequest{
                    ChannelId: cid,
                },
            },
            wantErr: false,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            _, err := svr.GetChannelGiftPkInfo(tt.args.ctx, tt.args.in)
            if (err != nil) != tt.wantErr {
                t.Errorf("GetChannelGiftPkInfo() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
        })
    }
}

func TestServer_SponsorChannelGiftPk(t *testing.T) {
    ctl := gomock.NewController(t)
    defer ctl.Finish()
    svr := initSvr(ctl)

    type args struct {
        ctx context.Context
        in  *pb.SponsorChannelGiftPkRequest
    }
    tests := []struct {
        name    string
        args    args
        wantErr bool
        f       func()
    }{
        {
            name: "common",
            args: args{
                ctx: ctx,
                in: &pb.SponsorChannelGiftPkRequest{
                    ChannelId: cid,
                    GiftId:    1,
                    Price:     10,
                },
            },
            wantErr: false,
            f: func() {
                channelType := uint32(channelPB.ChannelType_RADIO_LIVE_CHANNEL_TYPE)
                channelCli.EXPECT().GetChannelSimpleInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(&channelPb.ChannelSimpleInfo{
                    ChannelType: &channelType,
                }, nil)
                userProfileApiCli.EXPECT().GetUserProfileV2(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
                userOnlineCli.EXPECT().GetLatestOnlineInfo(gomock.Any(), gomock.Any()).Return(nil, nil)
                riskMngApiCli.EXPECT().CheckHelper(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
                usualDeviceClient.EXPECT().CheckUsualDevice(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
                usualDeviceClient.EXPECT().GetDeviceAuthError(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
                channelGiftPkCli.EXPECT().SponsorChannelGiftPk(gomock.Any(), gomock.Any()).Return(&channel_gift_pk.SponsorChannelGiftPkResp{Balance: 10}, nil)
            },
        },
        {
            name: "channel type error",
            args: args{
                ctx: ctx,
                in: &pb.SponsorChannelGiftPkRequest{
                    ChannelId: cid,
                    GiftId:    1,
                    Price:     10,
                },
            },
            wantErr: true,
            f: func() {
                channelCli.EXPECT().GetChannelSimpleInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
            },
        },
    }
    for _, tt := range tests {
        tt.f()
        t.Run(tt.name, func(t *testing.T) {
            _, err := svr.SponsorChannelGiftPk(tt.args.ctx, tt.args.in)
            if (err != nil) != tt.wantErr {
                t.Errorf("SponsorChannelGiftPk() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
        })
    }
}

func TestServer_ChooseChannelGiftPkSprite(t *testing.T) {
    ctl := gomock.NewController(t)
    defer ctl.Finish()
    svr := initSvr(ctl)

    type args struct {
        ctx context.Context
        in  *pb.ChooseChannelGiftPkSpriteRequest
    }
    tests := []struct {
        name    string
        args    args
        wantErr bool
        f       func()
    }{
        {
            name: "common",
            args: args{
                ctx: ctx,
                in: &pb.ChooseChannelGiftPkSpriteRequest{
                    ChannelId: cid,
                    SpriteId:  1,
                },
            },
            wantErr: false,
            f: func() {
                channelGiftPkCli.EXPECT().ChooseChannelGiftPkSprite(gomock.Any(), gomock.Any()).Return(nil, nil)
            },
        },
    }
    for _, tt := range tests {
        tt.f()
        t.Run(tt.name, func(t *testing.T) {
            _, err := svr.ChooseChannelGiftPkSprite(tt.args.ctx, tt.args.in)
            if (err != nil) != tt.wantErr {
                t.Errorf("ChooseChannelGiftPkSprite() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
        })
    }
}

func TestServer_CancelChannelGiftPkMatch(t *testing.T) {
    ctl := gomock.NewController(t)
    defer ctl.Finish()
    svr := initSvr(ctl)

    type args struct {
        ctx context.Context
        in  *pb.CancelChannelGiftPkMatchRequest
    }
    tests := []struct {
        name    string
        args    args
        wantErr bool
        f       func()
    }{
        {
            name: "common",
            args: args{
                ctx: ctx,
                in: &pb.CancelChannelGiftPkMatchRequest{
                    ChannelId: cid,
                },
            },
            wantErr: false,
            f: func() {
                channelGiftPkCli.EXPECT().CancelChannelGiftPkMatch(gomock.Any(), gomock.Any()).Return(nil, nil)
            },
        },
    }
    for _, tt := range tests {
        tt.f()
        t.Run(tt.name, func(t *testing.T) {
            _, err := svr.CancelChannelGiftPkMatch(tt.args.ctx, tt.args.in)
            if (err != nil) != tt.wantErr {
                t.Errorf("CancelChannelGiftPkMatch() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
        })
    }
}

func TestServer_GetChannelGiftPkRecord(t *testing.T) {
    ctl := gomock.NewController(t)
    defer ctl.Finish()
    svr := initSvr(ctl)

    type args struct {
        ctx context.Context
        in  *pb.GetChannelGiftPkRecordRequest
    }
    tests := []struct {
        name    string
        args    args
        wantErr bool
        f       func()
    }{
        {
            name: "common",
            args: args{
                ctx: ctx,
                in: &pb.GetChannelGiftPkRecordRequest{
                    ChannelId: cid,
                    Limit:     10,
                },
            },
            wantErr: false,
            f: func() {
                channelGiftPkCli.EXPECT().GetChannelGiftPkRecord(gomock.Any(), gomock.Any()).Return(&channel_gift_pk.GetChannelGiftPkRecordResp{
                    PkRecord: []*channel_gift_pk.ChannelGiftPkRecord{
                        {RecordId: "1"},
                    },
                }, nil)
                presentClient.EXPECT().GetPresentConfigByIdList(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
                userProfileApiCli.EXPECT().BatchGetUserProfileV2(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
            },
        },
    }
    for _, tt := range tests {
        tt.f()
        t.Run(tt.name, func(t *testing.T) {
            _, err := svr.GetChannelGiftPkRecord(tt.args.ctx, tt.args.in)
            if (err != nil) != tt.wantErr {
                t.Errorf("GetChannelGiftPkRecord() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
        })
    }
}

func TestServer_GetRecentlyChannelGiftPkLog(t *testing.T) {
    ctl := gomock.NewController(t)
    defer ctl.Finish()
    svr := initSvr(ctl)

    type args struct {
        ctx context.Context
        in  *pb.GetRecentlyChannelGiftPkLogRequest
    }
    tests := []struct {
        name    string
        args    args
        wantErr bool
        f       func()
    }{
        {
            name: "common",
            args: args{
                ctx: ctx,
                in: &pb.GetRecentlyChannelGiftPkLogRequest{
                    ChannelId: cid,
                },
            },
            wantErr: false,
            f: func() {
                channelGiftPkCli.EXPECT().GetChannelGiftPkWinRecord(gomock.Any(), gomock.Any()).Return(&channel_gift_pk.GetChannelGiftPkWinRecordResp{
                    WinRecord: []*channel_gift_pk.GiftPkSimpleWinRecord{
                        {PkId: "1"},
                    },
                }, nil)
                presentClient.EXPECT().GetPresentConfigByIdList(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
                userProfileApiCli.EXPECT().BatchGetUserProfileV2(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
            },
        },
    }
    for _, tt := range tests {
        tt.f()
        t.Run(tt.name, func(t *testing.T) {
            _, err := svr.GetRecentlyChannelGiftPkLog(tt.args.ctx, tt.args.in)
            if (err != nil) != tt.wantErr {
                t.Errorf("GetRecentlyChannelGiftPkLog() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
        })
    }
}
