package cache

import (
	"context"
	"fmt"
	"time"

	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"
)

/********** 客户端音频流缓存 **********/

// 房间主持麦音频流key
func genChannelHostRadioClientKey(channelId uint32) string {
	return fmt.Sprintf("hset_pgc_pk_radio_client_%d", channelId)
}

// 房间禁言对手key
func genChannelOpponentMicBanKey(pkId, channelId, micId uint32) string {
	return fmt.Sprintf("pgc_pk_opponent_mic_ban_%d_%d_%d", pkId, channelId, micId)
}

// SetChannelHostRadioClientId 记录房间音频流id
func (c *Cache) SetChannelHostRadioClientId(ctx context.Context, channelId, micId uint32, radioClientId string) error {
	key := genChannelHostRadioClientKey(channelId)
	c.cmder.HSet(ctx, key, fmt.Sprintf("%d", micId), radioClientId).Err()
	return c.cmder.Expire(ctx, key, time.Hour).Err()
}

// GetChannelHostRadioClientId 获取房间音频流id
func (c *Cache) GetChannelHostRadioClientId(ctx context.Context, channelId uint32) (map[string]string, error) {
	key := genChannelHostRadioClientKey(channelId)
	str, err := c.cmder.HGetAll(ctx, key).Result()
	if err == redis.Nil {
		return str, nil
	}
	return str, err
}

func (c *Cache) GetChannelHostRadioClientIdByMicId(ctx context.Context, channelId, micId uint32) (string, error) {
	key := genChannelHostRadioClientKey(channelId)
	str, err := c.cmder.HGet(ctx, key, fmt.Sprintf("%d", micId)).Result()
	if err == redis.Nil {
		return str, nil
	}
	return str, err
}

func (c *Cache) DelChannelHostRadioClientId(ctx context.Context, channelId, micId uint32) error {
	key := genChannelHostRadioClientKey(channelId)
	return c.cmder.HDel(ctx, key, fmt.Sprintf("%d", micId)).Err()
}

const BanFlag = "1"

// SetOpponentMicBan 设置禁言状态
func (c *Cache) SetOpponentMicBan(ctx context.Context, pkId, channelId, micId uint32, flag string) error {
	key := genChannelOpponentMicBanKey(pkId, channelId, micId)
	// 不设过期时间
	return c.cmder.Set(ctx, key, flag, 15*time.Minute).Err()
}

// CheckOpponentMicBan 检查是否禁言
func (c *Cache) CheckOpponentMicBan(ctx context.Context, pkId, channelId, micId uint32) (uint32, error) {
	var flag uint32
	key := genChannelOpponentMicBanKey(pkId, channelId, micId)
	str, err := c.cmder.Get(ctx, key).Result()
	if err == redis.Nil {
		return flag, nil
	}

	if str == BanFlag {
		flag = 1
	}
	return flag, err
}
