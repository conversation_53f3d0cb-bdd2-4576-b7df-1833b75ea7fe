package pia

import (
	"bytes"
	"encoding/json"
	"errors"
	"io/ioutil"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/golang/mock/gomock"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/web"
	accountPB "golang.52tt.com/protocol/services/accountsvr"
	piaSvrPb "golang.52tt.com/protocol/services/pia"
)

func Test_piaMgr_GetAuthorGenInfo(t *testing.T) {

	tests := []struct {
		name        string
		init        func(m *piaHelperForTest)
		getBody     func() []byte
		getAuthInfo func(body []byte) *web.AuthInfo
	}{
		{
			name: "正常",
			init: func(m *piaHelperForTest) {
				m.GetPiaCli().EXPECT().GetAuthorGenInfo(gomock.Any(), &piaSvrPb.PiaAuthorGenInfoReq{
					AuthorId: 123,
				}).Return(&piaSvrPb.PiaAuthorGenInfoResp{
					DramaCount:     100,
					CollectedCount: 1000,
					AuthorId:       123,
					Nickname:       "test",
				}, nil)
			},
			getBody: func() []byte {
				baseReq := &GetAuthorGenInfoReq{
					AuthorId: 123,
				}
				body, _ := json.Marshal(baseReq)
				return body
			},
			getAuthInfo: func(body []byte) *web.AuthInfo {
				return &web.AuthInfo{
					Body: body,
				}
			},
		},
		{
			name: "decode失败",
			getBody: func() []byte {
				return []byte("invalid value")
			},
			getAuthInfo: func(body []byte) *web.AuthInfo {
				return &web.AuthInfo{Body: body}
			},
		},
		{
			name: "请求pia戏服务失败",
			init: func(m *piaHelperForTest) {
				m.GetPiaCli().EXPECT().GetAuthorGenInfo(gomock.Any(), &piaSvrPb.PiaAuthorGenInfoReq{
					AuthorId: 123,
				}).Return(nil, errors.New("请求pia戏服务失败"))
			},
			getBody: func() []byte {
				baseReq := &GetAuthorGenInfoReq{
					AuthorId: 123,
				}
				body, _ := json.Marshal(baseReq)
				return body
			},
			getAuthInfo: func(body []byte) *web.AuthInfo {
				return &web.AuthInfo{
					Body: body,
				}
			},
		},
		{
			name: "正常 绑定了端内账号",
			init: func(m *piaHelperForTest) {
				m.GetPiaCli().EXPECT().GetAuthorGenInfo(gomock.Any(), &piaSvrPb.PiaAuthorGenInfoReq{
					AuthorId: 123,
				}).Return(&piaSvrPb.PiaAuthorGenInfoResp{
					DramaCount:     100,
					CollectedCount: 1000,
					AuthorId:       123,
					Nickname:       "test",
					Uid:            123456,
				}, nil)
				m.GetAccountCli().EXPECT().GetUserByUid(gomock.Any(), uint32(123456)).Return(
					&accountPB.UserResp{Uid: 123456, Nickname: "端内用户"}, nil,
				)
			},
			getBody: func() []byte {
				baseReq := &GetAuthorGenInfoReq{
					AuthorId: 123,
				}
				body, _ := json.Marshal(baseReq)
				return body
			},
			getAuthInfo: func(body []byte) *web.AuthInfo {
				return &web.AuthInfo{
					Body: body,
				}
			},
		},
		{
			name: "正常 绑定了端内账号， 请求account服务出错",
			init: func(m *piaHelperForTest) {
				m.GetPiaCli().EXPECT().GetAuthorGenInfo(gomock.Any(), &piaSvrPb.PiaAuthorGenInfoReq{
					AuthorId: 123,
				}).Return(&piaSvrPb.PiaAuthorGenInfoResp{
					DramaCount:     100,
					CollectedCount: 1000,
					AuthorId:       123,
					Nickname:       "test",
					Uid:            123456,
				}, nil)
				m.GetAccountCli().EXPECT().GetUserByUid(gomock.Any(), uint32(123456)).Return(
					nil, protocol.NewExactServerError(nil, -2, "请求account服务出错"),
				)
			},
			getBody: func() []byte {
				baseReq := &GetAuthorGenInfoReq{
					AuthorId: 123,
				}
				body, _ := json.Marshal(baseReq)
				return body
			},
			getAuthInfo: func(body []byte) *web.AuthInfo {
				return &web.AuthInfo{
					Body: body,
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := newPiaHelperForTest(t)
			if tt.init != nil {
				tt.init(m)
			}

			handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				r.Body = ioutil.NopCloser(bytes.NewBuffer(tt.getBody()))
				m.GetAuthorGenInfo(tt.getAuthInfo(tt.getBody()), w, r)
			})
			rr := httptest.NewRecorder()
			req, err := http.NewRequest("POST", "/tt-revenue-http-logic/pia/getDramaDetail", nil)
			if err != nil {
				t.Fatal(err)
			}
			handler.ServeHTTP(rr, req)
		})
	}
}
