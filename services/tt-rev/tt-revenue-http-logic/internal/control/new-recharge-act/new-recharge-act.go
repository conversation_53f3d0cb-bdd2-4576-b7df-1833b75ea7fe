package new_recharge_act

import (
	"golang.52tt.com/clients/darkserver"
	present_week_card "golang.52tt.com/clients/present-week-card"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/web"
	darkPB "golang.52tt.com/protocol/services/darkserver"
	pb "golang.52tt.com/protocol/services/present-week-card"
	"strings"
	"strconv"
	"time"
	"net/http"
	"context"
	"encoding/json"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"golang.52tt.com/pkg/marketid_helper"
	new_recharge_act "golang.52tt.com/protocol/services/new-recharge-act"
	"golang.52tt.com/services/tt-rev/tt-revenue-http-logic/internal/common"
	tyr_http "gitlab.ttyuyin.com/avengers/tyr/core/service/http"
	"golang.52tt.com/services/tt-rev/tt-revenue-http-logic/internal/conf"
)

/*
enum BannerType {
        BANNER_TYPE_UNSPECIFIED = 0;
        BANNER_TYPE_WEEK_CARD = 1;      // 周卡banner
        BANNER_TYPE_FIRST_RECHARGE = 2; // 首充活动
    }
*/

const (
	BANNER_TYPE_WEEK_CARD      = 1
	BANNER_TYPE_FIRST_RECHARGE = 2
)

type GetRechargePageBannerReq struct {
	OSType        uint32 `json:"os_type "`
	CliVersion    uint32 `json:"cli_version"`
	MarketId      uint32 `json:"market_id"`
}

type RechargeBannerInfo struct {
	BannerType uint32 `json:"banner_type"` //see BannerType
	BannerPic  string `json:"banner_pic"`
	JumpUrl    string `json:"jump_url"`
}

type GetRechargePageBannerReqResp struct {
	BannerList []*RechargeBannerInfo `json:"banner_list"`
}

type NewRechargeActMgr struct {
	presentWeekCardCli present_week_card.IClient
	DarkCli            darkserver.IClient
	newRechargeActCli  new_recharge_act.NewRechargeActClient
}

func NewRechargeActMgrMgr(presentWeekCardCli present_week_card.IClient, darkCli darkserver.IClient) *NewRechargeActMgr {
	newRechargeActCli, err := new_recharge_act.NewClient(context.Background())
	if err != nil {
		log.Errorf("NewRechargeActMgrMgr NewClient err:%v", err)
		return nil
	}
	m := &NewRechargeActMgr{
		presentWeekCardCli: presentWeekCardCli,
		DarkCli:            darkCli,
		newRechargeActCli:  newRechargeActCli,
	}
	return m
}

func (mgr *NewRechargeActMgr) Register(cfg *conf.ServiceConfig, router *tyr_http.Router) {
	chile := router.Child("/recharge-act")
	chile.POST("/get_banner_list", common.AuthHandleInterceptor(cfg, mgr.GetRechargePageBannerList))
}

// GetRechargePageBannerList uri: /get_banner_list
func (mgr *NewRechargeActMgr) GetRechargePageBannerList(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	uid := authInfo.UserID
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*3)
	defer cancel()

	var in GetRechargePageBannerReq
	err := json.Unmarshal(authInfo.Body, &in)
	if err != nil {
		e := protocol.ToServerError(err)
		log.Errorf("GetRechargePageBannerList Failed to parse request uid:%d,  body(%s)  err(%v)", uid, string(authInfo.Body), err)
		_ = web.ServeAPICodeJson(w, int32(e.Code()), e.Message(), nil)
		return
	}

	log.Debugf("GetRechargePageBannerList uid:%d, req:%+v", uid, in)
	out := &GetRechargePageBannerReqResp{}

	bannerList := make([]*RechargeBannerInfo, 0)

	// 获取首充活动banner信息
	svrResp, err := mgr.newRechargeActCli.GetUserFirstRechargeActStatusInfo(ctx, &new_recharge_act.GetUserFirstRechargeActStatusReq{
		Uid:           uid,
		WithActBanner: true,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetRechargePageBannerList Failed to get user first recharge act status info uid:%d, err(%v)", uid, err)
	}

	if svrResp.GetIsActOpen() && svrResp.GetBannerInfo().GetBannerUrl() != "" {
		bannerList = append(bannerList, &RechargeBannerInfo{
			BannerType: BANNER_TYPE_FIRST_RECHARGE,
			BannerPic:  svrResp.GetBannerInfo().GetBannerUrl(),
			JumpUrl:    svrResp.GetBannerInfo().GetJumpUrl(),
		})
	}

	// 获取周卡入口
	weekCardAccess, _ := mgr.getPresentWeekCardEntry(ctx, uid, in.MarketId, in.CliVersion)
	if weekCardAccess {
		bannerList = append(bannerList, &RechargeBannerInfo{
			BannerType: BANNER_TYPE_WEEK_CARD,
			BannerPic:  "https://obs-cdn.52tt.com/tt/fe-moss/web/20240903174637_84886170.png",
			JumpUrl:    "", // 客户端内部跳转链接，
		})
	}
	
	out.BannerList = bannerList
	log.DebugWithCtx(ctx, "GetRechargePageBannerList:uid:%d %+v", uid, in)
	_ = web.ServeAPIJsonV2(w, out)
}

// getPresentWeekCardEntry 查询礼物周卡入口
func (mgr *NewRechargeActMgr) getPresentWeekCardEntry(ctx context.Context, uid, marketId, clientVersion uint32) (bool, error) {
	// 通过马甲包获取最低可见入口的版本号
	minVersion := marketid_helper.Get("present_week_card_entry_min_version", marketId, 0)
	if version, ok := parseCliVersion(minVersion); !ok ||
		protocol.ClientVersion(clientVersion) < version {

		// 未达到指定客户端版本，不可见入口
		return false, nil
	}

	resp, err := mgr.DarkCli.BlackUserCheck(ctx, darkPB.GetBlackUserReq{
		Uid: uid,
	})
	if err != nil {
		return false, err
	}

	// 黑产
	if resp.GetBlackFlag() {
		return false, nil
	}

	svrResp, e := mgr.presentWeekCardCli.GetPresentWeekCardAccess(ctx, &pb.GetPresentWeekCardEntryReq{
		Uid: uid,
	})
	if e != nil {
		return false, e
	}

	return svrResp.GetHaveAccess(), nil
}

func parseCliVersion(version string) (protocol.ClientVersion, bool) {
	subStrings := strings.Split(version, ".")
	subVers := make([]uint16, 0)
	for _, subString := range subStrings {
		t, err := strconv.ParseUint(subString, 10, 32)
		if err != nil {
			log.Debugf("parse version fail, version: %s, err: %v", version, err)
			continue
		}
		subVers = append(subVers, uint16(t))
	}
	if 3 != len(subVers) {
		return protocol.ClientVersion(0), false
	}

	iVersion := protocol.ClientVersion(protocol.FormatClientVersion(uint8(subVers[0]), uint8(subVers[1]), subVers[2]))
	return iVersion, true
}
