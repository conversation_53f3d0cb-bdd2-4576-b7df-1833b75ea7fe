// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/clients/fellow-svr (interfaces: IClient)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	channel_cp_game "golang.52tt.com/protocol/services/channel-cp-game"
	fellow_svr "golang.52tt.com/protocol/services/fellow-svr"
)

// MockFellowIClient is a mock of IClient interface.
type MockFellowIClient struct {
	ctrl     *gomock.Controller
	recorder *MockFellowIClientMockRecorder
}

// MockFellowIClientMockRecorder is the mock recorder for MockFellowIClient.
type MockFellowIClientMockRecorder struct {
	mock *MockFellowIClient
}

// NewMockFellowIClient creates a new mock instance.
func NewMockFellowIClient(ctrl *gomock.Controller) *MockFellowIClient {
	mock := &MockFellowIClient{ctrl: ctrl}
	mock.recorder = &MockFellowIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockFellowIClient) EXPECT() *MockFellowIClientMockRecorder {
	return m.recorder
}

// AddChannelRelationshipBinding mocks base method.
func (m *MockFellowIClient) AddChannelRelationshipBinding(arg0 context.Context, arg1 *fellow_svr.ChannelRelationshipBindingAddReq) (*fellow_svr.ChannelRelationshipBindingAddResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddChannelRelationshipBinding", arg0, arg1)
	ret0, _ := ret[0].(*fellow_svr.ChannelRelationshipBindingAddResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddChannelRelationshipBinding indicates an expected call of AddChannelRelationshipBinding.
func (mr *MockFellowIClientMockRecorder) AddChannelRelationshipBinding(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddChannelRelationshipBinding", reflect.TypeOf((*MockFellowIClient)(nil).AddChannelRelationshipBinding), arg0, arg1)
}

// AddFellowPointDelay mocks base method.
func (m *MockFellowIClient) AddFellowPointDelay(arg0 context.Context, arg1 *fellow_svr.AddFellowPointDelayReq) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddFellowPointDelay", arg0, arg1)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// AddFellowPointDelay indicates an expected call of AddFellowPointDelay.
func (mr *MockFellowIClientMockRecorder) AddFellowPointDelay(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddFellowPointDelay", reflect.TypeOf((*MockFellowIClient)(nil).AddFellowPointDelay), arg0, arg1)
}

// AddFellowPresentConfig mocks base method.
func (m *MockFellowIClient) AddFellowPresentConfig(arg0 context.Context, arg1 *fellow_svr.AddFellowPresentConfigReq) (*fellow_svr.AddFellowPresentConfigResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddFellowPresentConfig", arg0, arg1)
	ret0, _ := ret[0].(*fellow_svr.AddFellowPresentConfigResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// AddFellowPresentConfig indicates an expected call of AddFellowPresentConfig.
func (mr *MockFellowIClientMockRecorder) AddFellowPresentConfig(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddFellowPresentConfig", reflect.TypeOf((*MockFellowIClient)(nil).AddFellowPresentConfig), arg0, arg1)
}

// AddRare mocks base method.
func (m *MockFellowIClient) AddRare(arg0 context.Context, arg1, arg2, arg3, arg4, arg5, arg6 uint32, arg7 []*channel_cp_game.ChoseRare) (*fellow_svr.AddRareResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddRare", arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7)
	ret0, _ := ret[0].(*fellow_svr.AddRareResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddRare indicates an expected call of AddRare.
func (mr *MockFellowIClientMockRecorder) AddRare(arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddRare", reflect.TypeOf((*MockFellowIClient)(nil).AddRare), arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7)
}

// AddRare2 mocks base method.
func (m *MockFellowIClient) AddRare2(arg0 context.Context, arg1 *fellow_svr.AddRareReq) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddRare2", arg0, arg1)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// AddRare2 indicates an expected call of AddRare2.
func (mr *MockFellowIClientMockRecorder) AddRare2(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddRare2", reflect.TypeOf((*MockFellowIClient)(nil).AddRare2), arg0, arg1)
}

// AddRelationship mocks base method.
func (m *MockFellowIClient) AddRelationship(arg0 context.Context, arg1 *fellow_svr.RelationshipAddReq) (*fellow_svr.RelationshipAddResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddRelationship", arg0, arg1)
	ret0, _ := ret[0].(*fellow_svr.RelationshipAddResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddRelationship indicates an expected call of AddRelationship.
func (mr *MockFellowIClientMockRecorder) AddRelationship(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddRelationship", reflect.TypeOf((*MockFellowIClient)(nil).AddRelationship), arg0, arg1)
}

// CC mocks base method.
func (m *MockFellowIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockFellowIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockFellowIClient)(nil).CC))
}

// CancelFellowInvite mocks base method.
func (m *MockFellowIClient) CancelFellowInvite(arg0 context.Context, arg1 *fellow_svr.CancelFellowInviteReq) (*fellow_svr.CancelFellowInviteResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CancelFellowInvite", arg0, arg1)
	ret0, _ := ret[0].(*fellow_svr.CancelFellowInviteResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// CancelFellowInvite indicates an expected call of CancelFellowInvite.
func (mr *MockFellowIClientMockRecorder) CancelFellowInvite(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CancelFellowInvite", reflect.TypeOf((*MockFellowIClient)(nil).CancelFellowInvite), arg0, arg1)
}

// CancelUnboundFellow mocks base method.
func (m *MockFellowIClient) CancelUnboundFellow(arg0 context.Context, arg1 *fellow_svr.CancelUnboundFellowReq) (*fellow_svr.CancelUnboundFellowResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CancelUnboundFellow", arg0, arg1)
	ret0, _ := ret[0].(*fellow_svr.CancelUnboundFellowResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// CancelUnboundFellow indicates an expected call of CancelUnboundFellow.
func (mr *MockFellowIClientMockRecorder) CancelUnboundFellow(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CancelUnboundFellow", reflect.TypeOf((*MockFellowIClient)(nil).CancelUnboundFellow), arg0, arg1)
}

// ChangeFellowBindType mocks base method.
func (m *MockFellowIClient) ChangeFellowBindType(arg0 context.Context, arg1 *fellow_svr.ChangeFellowBindTypeReq) (*fellow_svr.ChangeFellowBindTypeResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ChangeFellowBindType", arg0, arg1)
	ret0, _ := ret[0].(*fellow_svr.ChangeFellowBindTypeResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// ChangeFellowBindType indicates an expected call of ChangeFellowBindType.
func (mr *MockFellowIClientMockRecorder) ChangeFellowBindType(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChangeFellowBindType", reflect.TypeOf((*MockFellowIClient)(nil).ChangeFellowBindType), arg0, arg1)
}

// ChannelSendFellowPresent mocks base method.
func (m *MockFellowIClient) ChannelSendFellowPresent(arg0 context.Context, arg1 *fellow_svr.ChannelSendFellowPresentReq) (*fellow_svr.ChannelSendFellowPresentResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ChannelSendFellowPresent", arg0, arg1)
	ret0, _ := ret[0].(*fellow_svr.ChannelSendFellowPresentResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// ChannelSendFellowPresent indicates an expected call of ChannelSendFellowPresent.
func (mr *MockFellowIClientMockRecorder) ChannelSendFellowPresent(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChannelSendFellowPresent", reflect.TypeOf((*MockFellowIClient)(nil).ChannelSendFellowPresent), arg0, arg1)
}

// CheckFellowInvite mocks base method.
func (m *MockFellowIClient) CheckFellowInvite(arg0 context.Context, arg1 *fellow_svr.CheckFellowInviteReq) (*fellow_svr.CheckFellowInviteResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckFellowInvite", arg0, arg1)
	ret0, _ := ret[0].(*fellow_svr.CheckFellowInviteResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// CheckFellowInvite indicates an expected call of CheckFellowInvite.
func (mr *MockFellowIClientMockRecorder) CheckFellowInvite(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckFellowInvite", reflect.TypeOf((*MockFellowIClient)(nil).CheckFellowInvite), arg0, arg1)
}

// Close mocks base method.
func (m *MockFellowIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockFellowIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockFellowIClient)(nil).Close))
}

// DelChannelRelationshipBinding mocks base method.
func (m *MockFellowIClient) DelChannelRelationshipBinding(arg0 context.Context, arg1 *fellow_svr.ChannelRelationshipBindingDeleteReq) (*fellow_svr.ChannelRelationshipBindingDeleteResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelChannelRelationshipBinding", arg0, arg1)
	ret0, _ := ret[0].(*fellow_svr.ChannelRelationshipBindingDeleteResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelChannelRelationshipBinding indicates an expected call of DelChannelRelationshipBinding.
func (mr *MockFellowIClientMockRecorder) DelChannelRelationshipBinding(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelChannelRelationshipBinding", reflect.TypeOf((*MockFellowIClient)(nil).DelChannelRelationshipBinding), arg0, arg1)
}

// DelFellowPresentConfig mocks base method.
func (m *MockFellowIClient) DelFellowPresentConfig(arg0 context.Context, arg1 *fellow_svr.DelFellowPresentConfigReq) (*fellow_svr.DelFellowPresentConfigResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelFellowPresentConfig", arg0, arg1)
	ret0, _ := ret[0].(*fellow_svr.DelFellowPresentConfigResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// DelFellowPresentConfig indicates an expected call of DelFellowPresentConfig.
func (mr *MockFellowIClientMockRecorder) DelFellowPresentConfig(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelFellowPresentConfig", reflect.TypeOf((*MockFellowIClient)(nil).DelFellowPresentConfig), arg0, arg1)
}

// DelRare mocks base method.
func (m *MockFellowIClient) DelRare(arg0 context.Context, arg1, arg2, arg3, arg4 uint32) (*fellow_svr.DelRareResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelRare", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*fellow_svr.DelRareResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// DelRare indicates an expected call of DelRare.
func (mr *MockFellowIClientMockRecorder) DelRare(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelRare", reflect.TypeOf((*MockFellowIClient)(nil).DelRare), arg0, arg1, arg2, arg3, arg4)
}

// DelRelationship mocks base method.
func (m *MockFellowIClient) DelRelationship(arg0 context.Context, arg1 *fellow_svr.RelationshipDeleteReq) (*fellow_svr.RelationshipDeleteResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelRelationship", arg0, arg1)
	ret0, _ := ret[0].(*fellow_svr.RelationshipDeleteResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelRelationship indicates an expected call of DelRelationship.
func (mr *MockFellowIClientMockRecorder) DelRelationship(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelRelationship", reflect.TypeOf((*MockFellowIClient)(nil).DelRelationship), arg0, arg1)
}

// DirectUnboundFellow mocks base method.
func (m *MockFellowIClient) DirectUnboundFellow(arg0 context.Context, arg1, arg2 uint32) (*fellow_svr.DirectUnboundFellowResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DirectUnboundFellow", arg0, arg1, arg2)
	ret0, _ := ret[0].(*fellow_svr.DirectUnboundFellowResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// DirectUnboundFellow indicates an expected call of DirectUnboundFellow.
func (mr *MockFellowIClientMockRecorder) DirectUnboundFellow(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DirectUnboundFellow", reflect.TypeOf((*MockFellowIClient)(nil).DirectUnboundFellow), arg0, arg1, arg2)
}

// GetAllChannelFellowInvite mocks base method.
func (m *MockFellowIClient) GetAllChannelFellowInvite(arg0 context.Context, arg1 *fellow_svr.GetAllChannelFellowInviteReq) (*fellow_svr.GetAllChannelFellowInviteResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllChannelFellowInvite", arg0, arg1)
	ret0, _ := ret[0].(*fellow_svr.GetAllChannelFellowInviteResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetAllChannelFellowInvite indicates an expected call of GetAllChannelFellowInvite.
func (mr *MockFellowIClientMockRecorder) GetAllChannelFellowInvite(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllChannelFellowInvite", reflect.TypeOf((*MockFellowIClient)(nil).GetAllChannelFellowInvite), arg0, arg1)
}

// GetAllFellowPresentConfig mocks base method.
func (m *MockFellowIClient) GetAllFellowPresentConfig(arg0 context.Context, arg1 *fellow_svr.GetAllFellowPresentConfigReq) (*fellow_svr.GetAllFellowPresentConfigResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllFellowPresentConfig", arg0, arg1)
	ret0, _ := ret[0].(*fellow_svr.GetAllFellowPresentConfigResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetAllFellowPresentConfig indicates an expected call of GetAllFellowPresentConfig.
func (mr *MockFellowIClientMockRecorder) GetAllFellowPresentConfig(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllFellowPresentConfig", reflect.TypeOf((*MockFellowIClient)(nil).GetAllFellowPresentConfig), arg0, arg1)
}

// GetChannelFellowCandidateInfo mocks base method.
func (m *MockFellowIClient) GetChannelFellowCandidateInfo(arg0 context.Context, arg1 *fellow_svr.GetChannelFellowCandidateInfoReq) (*fellow_svr.GetChannelFellowCandidateInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelFellowCandidateInfo", arg0, arg1)
	ret0, _ := ret[0].(*fellow_svr.GetChannelFellowCandidateInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetChannelFellowCandidateInfo indicates an expected call of GetChannelFellowCandidateInfo.
func (mr *MockFellowIClientMockRecorder) GetChannelFellowCandidateInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelFellowCandidateInfo", reflect.TypeOf((*MockFellowIClient)(nil).GetChannelFellowCandidateInfo), arg0, arg1)
}

// GetChannelRareConfig mocks base method.
func (m *MockFellowIClient) GetChannelRareConfig(arg0 context.Context, arg1 uint32) (*fellow_svr.GetChannelRareConfigResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelRareConfig", arg0, arg1)
	ret0, _ := ret[0].(*fellow_svr.GetChannelRareConfigResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetChannelRareConfig indicates an expected call of GetChannelRareConfig.
func (mr *MockFellowIClientMockRecorder) GetChannelRareConfig(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelRareConfig", reflect.TypeOf((*MockFellowIClient)(nil).GetChannelRareConfig), arg0, arg1)
}

// GetChannelRelationshipBindingList mocks base method.
func (m *MockFellowIClient) GetChannelRelationshipBindingList(arg0 context.Context, arg1 *fellow_svr.ChannelRelationshipBindingListReq) (*fellow_svr.ChannelRelationshipBindingListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelRelationshipBindingList", arg0, arg1)
	ret0, _ := ret[0].(*fellow_svr.ChannelRelationshipBindingListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelRelationshipBindingList indicates an expected call of GetChannelRelationshipBindingList.
func (mr *MockFellowIClientMockRecorder) GetChannelRelationshipBindingList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelRelationshipBindingList", reflect.TypeOf((*MockFellowIClient)(nil).GetChannelRelationshipBindingList), arg0, arg1)
}

// GetFellowCandidateInfo mocks base method.
func (m *MockFellowIClient) GetFellowCandidateInfo(arg0 context.Context, arg1 *fellow_svr.GetFellowCandidateInfoReq) (*fellow_svr.GetFellowCandidateInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFellowCandidateInfo", arg0, arg1)
	ret0, _ := ret[0].(*fellow_svr.GetFellowCandidateInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetFellowCandidateInfo indicates an expected call of GetFellowCandidateInfo.
func (mr *MockFellowIClientMockRecorder) GetFellowCandidateInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFellowCandidateInfo", reflect.TypeOf((*MockFellowIClient)(nil).GetFellowCandidateInfo), arg0, arg1)
}

// GetFellowCandidateList mocks base method.
func (m *MockFellowIClient) GetFellowCandidateList(arg0 context.Context, arg1 *fellow_svr.GetFellowCandidateListReq) (*fellow_svr.GetFellowCandidateListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFellowCandidateList", arg0, arg1)
	ret0, _ := ret[0].(*fellow_svr.GetFellowCandidateListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetFellowCandidateList indicates an expected call of GetFellowCandidateList.
func (mr *MockFellowIClientMockRecorder) GetFellowCandidateList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFellowCandidateList", reflect.TypeOf((*MockFellowIClient)(nil).GetFellowCandidateList), arg0, arg1)
}

// GetFellowInfoByUid mocks base method.
func (m *MockFellowIClient) GetFellowInfoByUid(arg0 context.Context, arg1 *fellow_svr.GetFellowInfoByUidReq) (*fellow_svr.GetFellowInfoByUidResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFellowInfoByUid", arg0, arg1)
	ret0, _ := ret[0].(*fellow_svr.GetFellowInfoByUidResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetFellowInfoByUid indicates an expected call of GetFellowInfoByUid.
func (mr *MockFellowIClientMockRecorder) GetFellowInfoByUid(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFellowInfoByUid", reflect.TypeOf((*MockFellowIClient)(nil).GetFellowInfoByUid), arg0, arg1)
}

// GetFellowInviteInfoById mocks base method.
func (m *MockFellowIClient) GetFellowInviteInfoById(arg0 context.Context, arg1 *fellow_svr.GetFellowInviteInfoByIdReq) (*fellow_svr.GetFellowInviteInfoByIdResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFellowInviteInfoById", arg0, arg1)
	ret0, _ := ret[0].(*fellow_svr.GetFellowInviteInfoByIdResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetFellowInviteInfoById indicates an expected call of GetFellowInviteInfoById.
func (mr *MockFellowIClientMockRecorder) GetFellowInviteInfoById(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFellowInviteInfoById", reflect.TypeOf((*MockFellowIClient)(nil).GetFellowInviteInfoById), arg0, arg1)
}

// GetFellowInviteList mocks base method.
func (m *MockFellowIClient) GetFellowInviteList(arg0 context.Context, arg1 *fellow_svr.GetFellowInviteListReq) (*fellow_svr.GetFellowInviteListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFellowInviteList", arg0, arg1)
	ret0, _ := ret[0].(*fellow_svr.GetFellowInviteListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetFellowInviteList indicates an expected call of GetFellowInviteList.
func (mr *MockFellowIClientMockRecorder) GetFellowInviteList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFellowInviteList", reflect.TypeOf((*MockFellowIClient)(nil).GetFellowInviteList), arg0, arg1)
}

// GetFellowList mocks base method.
func (m *MockFellowIClient) GetFellowList(arg0 context.Context, arg1 int64) (*fellow_svr.GetFellowListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFellowList", arg0, arg1)
	ret0, _ := ret[0].(*fellow_svr.GetFellowListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetFellowList indicates an expected call of GetFellowList.
func (mr *MockFellowIClientMockRecorder) GetFellowList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFellowList", reflect.TypeOf((*MockFellowIClient)(nil).GetFellowList), arg0, arg1)
}

// GetFellowPoint mocks base method.
func (m *MockFellowIClient) GetFellowPoint(arg0 context.Context, arg1 *fellow_svr.GetFellowPointReq) (*fellow_svr.GetFellowPointResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFellowPoint", arg0, arg1)
	ret0, _ := ret[0].(*fellow_svr.GetFellowPointResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetFellowPoint indicates an expected call of GetFellowPoint.
func (mr *MockFellowIClientMockRecorder) GetFellowPoint(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFellowPoint", reflect.TypeOf((*MockFellowIClient)(nil).GetFellowPoint), arg0, arg1)
}

// GetFellowPresentConfigById mocks base method.
func (m *MockFellowIClient) GetFellowPresentConfigById(arg0 context.Context, arg1 *fellow_svr.GetFellowPresentConfigByIdReq) (*fellow_svr.GetFellowPresentConfigByIdResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFellowPresentConfigById", arg0, arg1)
	ret0, _ := ret[0].(*fellow_svr.GetFellowPresentConfigByIdResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetFellowPresentConfigById indicates an expected call of GetFellowPresentConfigById.
func (mr *MockFellowIClientMockRecorder) GetFellowPresentConfigById(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFellowPresentConfigById", reflect.TypeOf((*MockFellowIClient)(nil).GetFellowPresentConfigById), arg0, arg1)
}

// GetFellowPresentDetail mocks base method.
func (m *MockFellowIClient) GetFellowPresentDetail(arg0 context.Context, arg1 *fellow_svr.GetFellowPresentDetailReq) (*fellow_svr.GetFellowPresentDetailResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFellowPresentDetail", arg0, arg1)
	ret0, _ := ret[0].(*fellow_svr.GetFellowPresentDetailResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetFellowPresentDetail indicates an expected call of GetFellowPresentDetail.
func (mr *MockFellowIClientMockRecorder) GetFellowPresentDetail(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFellowPresentDetail", reflect.TypeOf((*MockFellowIClient)(nil).GetFellowPresentDetail), arg0, arg1)
}

// GetFellowSimpleInfo mocks base method.
func (m *MockFellowIClient) GetFellowSimpleInfo(arg0 context.Context, arg1 *fellow_svr.GetFellowSimpleInfoReq) (*fellow_svr.GetFellowSimpleInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFellowSimpleInfo", arg0, arg1)
	ret0, _ := ret[0].(*fellow_svr.GetFellowSimpleInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetFellowSimpleInfo indicates an expected call of GetFellowSimpleInfo.
func (mr *MockFellowIClientMockRecorder) GetFellowSimpleInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFellowSimpleInfo", reflect.TypeOf((*MockFellowIClient)(nil).GetFellowSimpleInfo), arg0, arg1)
}

// GetFellowType mocks base method.
func (m *MockFellowIClient) GetFellowType(arg0 context.Context, arg1, arg2 uint32) (*fellow_svr.GetFellowTypeResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFellowType", arg0, arg1, arg2)
	ret0, _ := ret[0].(*fellow_svr.GetFellowTypeResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetFellowType indicates an expected call of GetFellowType.
func (mr *MockFellowIClientMockRecorder) GetFellowType(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFellowType", reflect.TypeOf((*MockFellowIClient)(nil).GetFellowType), arg0, arg1, arg2)
}

// GetFromAllRelationshipByIds mocks base method.
func (m *MockFellowIClient) GetFromAllRelationshipByIds(arg0 context.Context, arg1 []uint32) (*fellow_svr.GetFromAllRelationshipByIdsResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFromAllRelationshipByIds", arg0, arg1)
	ret0, _ := ret[0].(*fellow_svr.GetFromAllRelationshipByIdsResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetFromAllRelationshipByIds indicates an expected call of GetFromAllRelationshipByIds.
func (mr *MockFellowIClientMockRecorder) GetFromAllRelationshipByIds(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFromAllRelationshipByIds", reflect.TypeOf((*MockFellowIClient)(nil).GetFromAllRelationshipByIds), arg0, arg1)
}

// GetHistoryFellowType mocks base method.
func (m *MockFellowIClient) GetHistoryFellowType(arg0 context.Context, arg1 *fellow_svr.GetHistoryFellowTypeReq) (*fellow_svr.GetHistoryFellowTypeResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHistoryFellowType", arg0, arg1)
	ret0, _ := ret[0].(*fellow_svr.GetHistoryFellowTypeResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetHistoryFellowType indicates an expected call of GetHistoryFellowType.
func (mr *MockFellowIClientMockRecorder) GetHistoryFellowType(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHistoryFellowType", reflect.TypeOf((*MockFellowIClient)(nil).GetHistoryFellowType), arg0, arg1)
}

// GetOnMicFellowList mocks base method.
func (m *MockFellowIClient) GetOnMicFellowList(arg0 context.Context, arg1 *fellow_svr.GetOnMicFellowListReq) (*fellow_svr.GetOnMicFellowListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOnMicFellowList", arg0, arg1)
	ret0, _ := ret[0].(*fellow_svr.GetOnMicFellowListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetOnMicFellowList indicates an expected call of GetOnMicFellowList.
func (mr *MockFellowIClientMockRecorder) GetOnMicFellowList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOnMicFellowList", reflect.TypeOf((*MockFellowIClient)(nil).GetOnMicFellowList), arg0, arg1)
}

// GetRareConfig mocks base method.
func (m *MockFellowIClient) GetRareConfig(arg0 context.Context) (*fellow_svr.GetRareConfigResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRareConfig", arg0)
	ret0, _ := ret[0].(*fellow_svr.GetRareConfigResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetRareConfig indicates an expected call of GetRareConfig.
func (mr *MockFellowIClientMockRecorder) GetRareConfig(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRareConfig", reflect.TypeOf((*MockFellowIClient)(nil).GetRareConfig), arg0)
}

// GetRareList mocks base method.
func (m *MockFellowIClient) GetRareList(arg0 context.Context, arg1, arg2 uint32) (*fellow_svr.GetRareListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRareList", arg0, arg1, arg2)
	ret0, _ := ret[0].(*fellow_svr.GetRareListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetRareList indicates an expected call of GetRareList.
func (mr *MockFellowIClientMockRecorder) GetRareList(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRareList", reflect.TypeOf((*MockFellowIClient)(nil).GetRareList), arg0, arg1, arg2)
}

// GetRareTags mocks base method.
func (m *MockFellowIClient) GetRareTags(arg0 context.Context, arg1, arg2 uint32) (*fellow_svr.GetRareTagsResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRareTags", arg0, arg1, arg2)
	ret0, _ := ret[0].(*fellow_svr.GetRareTagsResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetRareTags indicates an expected call of GetRareTags.
func (mr *MockFellowIClientMockRecorder) GetRareTags(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRareTags", reflect.TypeOf((*MockFellowIClient)(nil).GetRareTags), arg0, arg1, arg2)
}

// GetRelationship mocks base method.
func (m *MockFellowIClient) GetRelationship(arg0 context.Context, arg1 *fellow_svr.RelationshipGetReq) (*fellow_svr.RelationshipGetResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRelationship", arg0, arg1)
	ret0, _ := ret[0].(*fellow_svr.RelationshipGetResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRelationship indicates an expected call of GetRelationship.
func (mr *MockFellowIClientMockRecorder) GetRelationship(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRelationship", reflect.TypeOf((*MockFellowIClient)(nil).GetRelationship), arg0, arg1)
}

// GetRelationshipList mocks base method.
func (m *MockFellowIClient) GetRelationshipList(arg0 context.Context, arg1 *fellow_svr.RelationshipListReq) (*fellow_svr.RelationshipListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRelationshipList", arg0, arg1)
	ret0, _ := ret[0].(*fellow_svr.RelationshipListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRelationshipList indicates an expected call of GetRelationshipList.
func (mr *MockFellowIClientMockRecorder) GetRelationshipList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRelationshipList", reflect.TypeOf((*MockFellowIClient)(nil).GetRelationshipList), arg0, arg1)
}

// GetRoomFellowList mocks base method.
func (m *MockFellowIClient) GetRoomFellowList(arg0 context.Context, arg1 *fellow_svr.GetRoomFellowListReq) (*fellow_svr.GetRoomFellowListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRoomFellowList", arg0, arg1)
	ret0, _ := ret[0].(*fellow_svr.GetRoomFellowListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetRoomFellowList indicates an expected call of GetRoomFellowList.
func (mr *MockFellowIClientMockRecorder) GetRoomFellowList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRoomFellowList", reflect.TypeOf((*MockFellowIClient)(nil).GetRoomFellowList), arg0, arg1)
}

// GetSendInviteList mocks base method.
func (m *MockFellowIClient) GetSendInviteList(arg0 context.Context, arg1 *fellow_svr.GetSendInviteListReq) (*fellow_svr.GetSendInviteListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSendInviteList", arg0, arg1)
	ret0, _ := ret[0].(*fellow_svr.GetSendInviteListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetSendInviteList indicates an expected call of GetSendInviteList.
func (mr *MockFellowIClientMockRecorder) GetSendInviteList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSendInviteList", reflect.TypeOf((*MockFellowIClient)(nil).GetSendInviteList), arg0, arg1)
}

// GetWebFellowInfo mocks base method.
func (m *MockFellowIClient) GetWebFellowInfo(arg0 context.Context, arg1, arg2 uint32) (*fellow_svr.GetWebFellowListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWebFellowInfo", arg0, arg1, arg2)
	ret0, _ := ret[0].(*fellow_svr.GetWebFellowListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetWebFellowInfo indicates an expected call of GetWebFellowInfo.
func (mr *MockFellowIClientMockRecorder) GetWebFellowInfo(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWebFellowInfo", reflect.TypeOf((*MockFellowIClient)(nil).GetWebFellowInfo), arg0, arg1, arg2)
}

// HandleChannelFellowInvite mocks base method.
func (m *MockFellowIClient) HandleChannelFellowInvite(arg0 context.Context, arg1 *fellow_svr.HandleChannelFellowInviteReq) (*fellow_svr.HandleChannelFellowInviteResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HandleChannelFellowInvite", arg0, arg1)
	ret0, _ := ret[0].(*fellow_svr.HandleChannelFellowInviteResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// HandleChannelFellowInvite indicates an expected call of HandleChannelFellowInvite.
func (mr *MockFellowIClientMockRecorder) HandleChannelFellowInvite(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandleChannelFellowInvite", reflect.TypeOf((*MockFellowIClient)(nil).HandleChannelFellowInvite), arg0, arg1)
}

// HandleFellowInvite mocks base method.
func (m *MockFellowIClient) HandleFellowInvite(arg0 context.Context, arg1 *fellow_svr.HandleFellowInviteReq) (*fellow_svr.HandleFellowInviteResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HandleFellowInvite", arg0, arg1)
	ret0, _ := ret[0].(*fellow_svr.HandleFellowInviteResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// HandleFellowInvite indicates an expected call of HandleFellowInvite.
func (mr *MockFellowIClientMockRecorder) HandleFellowInvite(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandleFellowInvite", reflect.TypeOf((*MockFellowIClient)(nil).HandleFellowInvite), arg0, arg1)
}

// SendChannelFellowInvite mocks base method.
func (m *MockFellowIClient) SendChannelFellowInvite(arg0 context.Context, arg1 *fellow_svr.SendChannelFellowInviteReq) (*fellow_svr.SendChannelFellowInviteResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendChannelFellowInvite", arg0, arg1)
	ret0, _ := ret[0].(*fellow_svr.SendChannelFellowInviteResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SendChannelFellowInvite indicates an expected call of SendChannelFellowInvite.
func (mr *MockFellowIClientMockRecorder) SendChannelFellowInvite(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendChannelFellowInvite", reflect.TypeOf((*MockFellowIClient)(nil).SendChannelFellowInvite), arg0, arg1)
}

// SendFellowInvite mocks base method.
func (m *MockFellowIClient) SendFellowInvite(arg0 context.Context, arg1 *fellow_svr.SendFellowInviteReq) (*fellow_svr.SendFellowInviteResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendFellowInvite", arg0, arg1)
	ret0, _ := ret[0].(*fellow_svr.SendFellowInviteResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SendFellowInvite indicates an expected call of SendFellowInvite.
func (mr *MockFellowIClientMockRecorder) SendFellowInvite(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendFellowInvite", reflect.TypeOf((*MockFellowIClient)(nil).SendFellowInvite), arg0, arg1)
}

// SendFellowPresent mocks base method.
func (m *MockFellowIClient) SendFellowPresent(arg0 context.Context, arg1 *fellow_svr.SendFellowPresentReq) (*fellow_svr.SendFellowPresentResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendFellowPresent", arg0, arg1)
	ret0, _ := ret[0].(*fellow_svr.SendFellowPresentResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SendFellowPresent indicates an expected call of SendFellowPresent.
func (mr *MockFellowIClientMockRecorder) SendFellowPresent(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendFellowPresent", reflect.TypeOf((*MockFellowIClient)(nil).SendFellowPresent), arg0, arg1)
}

// SetBindRelation mocks base method.
func (m *MockFellowIClient) SetBindRelation(arg0 context.Context, arg1, arg2, arg3, arg4 uint32, arg5 bool) (*fellow_svr.SetBindRelationResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetBindRelation", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].(*fellow_svr.SetBindRelationResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SetBindRelation indicates an expected call of SetBindRelation.
func (mr *MockFellowIClientMockRecorder) SetBindRelation(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetBindRelation", reflect.TypeOf((*MockFellowIClient)(nil).SetBindRelation), arg0, arg1, arg2, arg3, arg4, arg5)
}

// Stub mocks base method.
func (m *MockFellowIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockFellowIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockFellowIClient)(nil).Stub))
}

// UnboundFellow mocks base method.
func (m *MockFellowIClient) UnboundFellow(arg0 context.Context, arg1 *fellow_svr.UnboundFellowReq) (*fellow_svr.UnboundFellowResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnboundFellow", arg0, arg1)
	ret0, _ := ret[0].(*fellow_svr.UnboundFellowResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// UnboundFellow indicates an expected call of UnboundFellow.
func (mr *MockFellowIClientMockRecorder) UnboundFellow(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnboundFellow", reflect.TypeOf((*MockFellowIClient)(nil).UnboundFellow), arg0, arg1)
}

// UnlockFellowSite mocks base method.
func (m *MockFellowIClient) UnlockFellowSite(arg0 context.Context, arg1 *fellow_svr.UnlockFellowSiteReq) (*fellow_svr.UnlockFellowSiteResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnlockFellowSite", arg0, arg1)
	ret0, _ := ret[0].(*fellow_svr.UnlockFellowSiteResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// UnlockFellowSite indicates an expected call of UnlockFellowSite.
func (mr *MockFellowIClientMockRecorder) UnlockFellowSite(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnlockFellowSite", reflect.TypeOf((*MockFellowIClient)(nil).UnlockFellowSite), arg0, arg1)
}

// UpdateChannelRelationshipBinding mocks base method.
func (m *MockFellowIClient) UpdateChannelRelationshipBinding(arg0 context.Context, arg1 *fellow_svr.ChannelRelationshipBindingUpdateReq) (*fellow_svr.ChannelRelationshipBindingUpdateResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateChannelRelationshipBinding", arg0, arg1)
	ret0, _ := ret[0].(*fellow_svr.ChannelRelationshipBindingUpdateResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateChannelRelationshipBinding indicates an expected call of UpdateChannelRelationshipBinding.
func (mr *MockFellowIClientMockRecorder) UpdateChannelRelationshipBinding(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateChannelRelationshipBinding", reflect.TypeOf((*MockFellowIClient)(nil).UpdateChannelRelationshipBinding), arg0, arg1)
}

// UpdateFellowPresentConfig mocks base method.
func (m *MockFellowIClient) UpdateFellowPresentConfig(arg0 context.Context, arg1 *fellow_svr.UpdateFellowPresentConfigReq) (*fellow_svr.UpdateFellowPresentConfigResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateFellowPresentConfig", arg0, arg1)
	ret0, _ := ret[0].(*fellow_svr.UpdateFellowPresentConfigResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// UpdateFellowPresentConfig indicates an expected call of UpdateFellowPresentConfig.
func (mr *MockFellowIClientMockRecorder) UpdateFellowPresentConfig(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateFellowPresentConfig", reflect.TypeOf((*MockFellowIClient)(nil).UpdateFellowPresentConfig), arg0, arg1)
}

// UpdateRelationship mocks base method.
func (m *MockFellowIClient) UpdateRelationship(arg0 context.Context, arg1 *fellow_svr.RelationshipUpdateReq) (*fellow_svr.RelationshipUpdateResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateRelationship", arg0, arg1)
	ret0, _ := ret[0].(*fellow_svr.RelationshipUpdateResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateRelationship indicates an expected call of UpdateRelationship.
func (mr *MockFellowIClientMockRecorder) UpdateRelationship(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateRelationship", reflect.TypeOf((*MockFellowIClient)(nil).UpdateRelationship), arg0, arg1)
}
