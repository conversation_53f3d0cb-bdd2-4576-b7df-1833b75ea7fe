// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/tt-rev/user-recall-award/internal/cache (interfaces: ICache)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"
	time "time"

	gomock "github.com/golang/mock/gomock"
	store "golang.52tt.com/services/tt-rev/user-recall-award/internal/store"
)

// MockICache is a mock of ICache interface.
type MockICache struct {
	ctrl     *gomock.Controller
	recorder *MockICacheMockRecorder
}

// MockICacheMockRecorder is the mock recorder for MockICache.
type MockICacheMockRecorder struct {
	mock *MockICache
}

// NewMockICache creates a new mock instance.
func NewMockICache(ctrl *gomock.Controller) *MockICache {
	mock := &MockICache{ctrl: ctrl}
	mock.recorder = &MockICacheMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockICache) EXPECT() *MockICacheMockRecorder {
	return m.recorder
}

// BatchSetRecallRecord mocks base method.
func (m *MockICache) BatchSetRecallRecord(arg0 context.Context, arg1 []*store.RecallRecord) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchSetRecallRecord", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchSetRecallRecord indicates an expected call of BatchSetRecallRecord.
func (mr *MockICacheMockRecorder) BatchSetRecallRecord(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchSetRecallRecord", reflect.TypeOf((*MockICache)(nil).BatchSetRecallRecord), arg0, arg1)
}

// ClearCache mocks base method.
func (m *MockICache) ClearCache(arg0 context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClearCache", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// ClearCache indicates an expected call of ClearCache.
func (mr *MockICacheMockRecorder) ClearCache(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClearCache", reflect.TypeOf((*MockICache)(nil).ClearCache), arg0)
}

// ClearCacheByUid mocks base method.
func (m *MockICache) ClearCacheByUid(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClearCacheByUid", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// ClearCacheByUid indicates an expected call of ClearCacheByUid.
func (mr *MockICacheMockRecorder) ClearCacheByUid(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClearCacheByUid", reflect.TypeOf((*MockICache)(nil).ClearCacheByUid), arg0, arg1)
}

// Close mocks base method.
func (m *MockICache) Close() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Close")
	ret0, _ := ret[0].(error)
	return ret0
}

// Close indicates an expected call of Close.
func (mr *MockICacheMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockICache)(nil).Close))
}

// DelRecallRecord mocks base method.
func (m *MockICache) DelRecallRecord(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelRecallRecord", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelRecallRecord indicates an expected call of DelRecallRecord.
func (mr *MockICacheMockRecorder) DelRecallRecord(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelRecallRecord", reflect.TypeOf((*MockICache)(nil).DelRecallRecord), arg0, arg1)
}

// FixConsecutiveLoginDay mocks base method.
func (m *MockICache) FixConsecutiveLoginDay(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FixConsecutiveLoginDay", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// FixConsecutiveLoginDay indicates an expected call of FixConsecutiveLoginDay.
func (mr *MockICacheMockRecorder) FixConsecutiveLoginDay(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FixConsecutiveLoginDay", reflect.TypeOf((*MockICache)(nil).FixConsecutiveLoginDay), arg0, arg1)
}

// GetConsecutiveLoginDays mocks base method.
func (m *MockICache) GetConsecutiveLoginDays(arg0 context.Context, arg1 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetConsecutiveLoginDays", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetConsecutiveLoginDays indicates an expected call of GetConsecutiveLoginDays.
func (mr *MockICacheMockRecorder) GetConsecutiveLoginDays(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetConsecutiveLoginDays", reflect.TypeOf((*MockICache)(nil).GetConsecutiveLoginDays), arg0, arg1)
}

// GetRecallRecord mocks base method.
func (m *MockICache) GetRecallRecord(arg0 context.Context, arg1 uint32) (*store.RecallRecord, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRecallRecord", arg0, arg1)
	ret0, _ := ret[0].(*store.RecallRecord)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRecallRecord indicates an expected call of GetRecallRecord.
func (mr *MockICacheMockRecorder) GetRecallRecord(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRecallRecord", reflect.TypeOf((*MockICache)(nil).GetRecallRecord), arg0, arg1)
}

// GetRecallRecordByUids mocks base method.
func (m *MockICache) GetRecallRecordByUids(arg0 context.Context, arg1 []uint32) ([]*store.RecallRecord, []uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRecallRecordByUids", arg0, arg1)
	ret0, _ := ret[0].([]*store.RecallRecord)
	ret1, _ := ret[1].([]uint32)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetRecallRecordByUids indicates an expected call of GetRecallRecordByUids.
func (mr *MockICacheMockRecorder) GetRecallRecordByUids(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRecallRecordByUids", reflect.TypeOf((*MockICache)(nil).GetRecallRecordByUids), arg0, arg1)
}

// IsUserRecall mocks base method.
func (m *MockICache) IsUserRecall(arg0 context.Context, arg1 uint32) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsUserRecall", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsUserRecall indicates an expected call of IsUserRecall.
func (mr *MockICacheMockRecorder) IsUserRecall(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsUserRecall", reflect.TypeOf((*MockICache)(nil).IsUserRecall), arg0, arg1)
}

// Lock mocks base method.
func (m *MockICache) Lock(arg0 context.Context, arg1 string, arg2 time.Duration) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Lock", arg0, arg1, arg2)
	ret0, _ := ret[0].(bool)
	return ret0
}

// Lock indicates an expected call of Lock.
func (mr *MockICacheMockRecorder) Lock(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Lock", reflect.TypeOf((*MockICache)(nil).Lock), arg0, arg1, arg2)
}

// LogConsecutiveLogin mocks base method.
func (m *MockICache) LogConsecutiveLogin(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LogConsecutiveLogin", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// LogConsecutiveLogin indicates an expected call of LogConsecutiveLogin.
func (mr *MockICacheMockRecorder) LogConsecutiveLogin(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LogConsecutiveLogin", reflect.TypeOf((*MockICache)(nil).LogConsecutiveLogin), arg0, arg1)
}

// MarkOldVersionIMFlag mocks base method.
func (m *MockICache) MarkOldVersionIMFlag(arg0 context.Context, arg1 uint32) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MarkOldVersionIMFlag", arg0, arg1)
	ret0, _ := ret[0].(bool)
	return ret0
}

// MarkOldVersionIMFlag indicates an expected call of MarkOldVersionIMFlag.
func (mr *MockICacheMockRecorder) MarkOldVersionIMFlag(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MarkOldVersionIMFlag", reflect.TypeOf((*MockICache)(nil).MarkOldVersionIMFlag), arg0, arg1)
}

// MarkUserRecall mocks base method.
func (m *MockICache) MarkUserRecall(arg0 context.Context, arg1 uint32) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MarkUserRecall", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MarkUserRecall indicates an expected call of MarkUserRecall.
func (mr *MockICacheMockRecorder) MarkUserRecall(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MarkUserRecall", reflect.TypeOf((*MockICache)(nil).MarkUserRecall), arg0, arg1)
}

// UnMarkUserRecall mocks base method.
func (m *MockICache) UnMarkUserRecall(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnMarkUserRecall", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UnMarkUserRecall indicates an expected call of UnMarkUserRecall.
func (mr *MockICacheMockRecorder) UnMarkUserRecall(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnMarkUserRecall", reflect.TypeOf((*MockICache)(nil).UnMarkUserRecall), arg0, arg1)
}

// Unlock mocks base method.
func (m *MockICache) Unlock(arg0 context.Context, arg1 string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Unlock", arg0, arg1)
}

// Unlock indicates an expected call of Unlock.
func (mr *MockICacheMockRecorder) Unlock(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Unlock", reflect.TypeOf((*MockICache)(nil).Unlock), arg0, arg1)
}
