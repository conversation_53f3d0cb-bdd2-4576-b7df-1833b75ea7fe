package main

import (
	"context"
	"fmt"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"golang.52tt.com/pkg/config"
	"os"
	"strconv"
	"time"
)

func main() {
	if len(os.Args) < 7 {
		panic("usage: ./update_fellow_info [uid_a] [uid_b] [bind_type] [fellow_type] [fellow_point] [lv]")
	}

	uidA, _ := strconv.Atoi(os.Args[1])
	uidB, _ := strconv.Atoi(os.Args[2])
	bindType, _ := strconv.Atoi(os.Args[3])
	fellowType, _ := strconv.Atoi(os.Args[4])
	fellowPoint, _ := strconv.ParseFloat(os.Args[5], 64)
	lv, _ := strconv.Atoi(os.Args[6])

	cfg := &config.MongoConfig{
		Addrs:       "10.208.3.44:27017,10.208.6.105:27017,10.208.4.26:27017",
		Database:    "fellow_db",
		MaxPoolSize: 50,
		UserName:    "godman",
		Password:    "thegodofman",
	}
	client := &mongo.Client{}
	client, err := mongo.NewClient(cfg.OptionsForReplicaSet())
	if err != nil {
		panic(err)
	}
	fmt.Println("new client success")

	ctx, _ := context.WithTimeout(context.Background(), time.Minute)
	if err = client.Connect(ctx); err != nil {
		fmt.Println("newServer Connect err: ", err)
		return
	}

	fmt.Println("connect success")

	collAName := fmt.Sprintf("fellow_info_%02d", uidA%100)
	collBName := fmt.Sprintf("fellow_info_%02d", uidB%100)

	collA := client.Database(cfg.Database).Collection(collAName)
	collB := client.Database(cfg.Database).Collection(collBName)

	if err = collA.FindOneAndUpdate(context.Background(),
		bson.M{"uid": uidA, "to_uid": uidB},
		bson.M{"$set": bson.M{"bind_type": bindType, "fellow_type": fellowType, "point": fellowPoint, "level": lv}}).Err(); err != nil {
		fmt.Println("FindOneAndUpdate err: ", err)
	}

	if err = collB.FindOneAndUpdate(context.Background(),
		bson.M{"uid": uidB, "to_uid": uidA},
		bson.M{"$set": bson.M{"bind_type": bindType, "fellow_type": fellowType, "point": fellowPoint, "level": lv}}).Err(); err != nil {
		fmt.Println("FindOneAndUpdate err: ", err)
	}

	fmt.Println("update success")
}
