package mysql

import (
	"context"
	"golang.52tt.com/pkg/log"
	"time"
)

var createMagicSpiritTmpTbl = `CREATE TABLE IF NOT EXISTS magic_spirit_temporary (
  id int unsigned NOT NULL AUTO_INCREMENT,
  magic_spirit_id int(10) unsigned NOT NULL DEFAULT 0 COMMENT "幸运礼物id",
  name varchar(255) NOT NULL DEFAULT '' COMMENT "礼物名称",
  icon_url varchar(255) NOT NULL DEFAULT '' COMMENT "礼物图标",
  price int(10) unsigned NOT NULL DEFAULT '0' COMMENT "礼物价格",
  ranking int(10) unsigned NOT NULL DEFAULT '0' COMMENT "礼物排名",
  effect_begin int(10) unsigned NOT NULL DEFAULT '0' COMMENT "上架时间",
  effect_end int(10) unsigned NOT NULL DEFAULT '0' COMMENT "下架时间",
  describe_image_url varchar(255) NOT NULL DEFAULT '' COMMENT "礼物介绍浮层",
  gift_describe varchar(255) NOT NULL DEFAULT '' COMMENT "礼物介绍",
  activity_jump_url varchar(255) NOT NULL DEFAULT '' COMMENT "礼物活动链接跳转url",
  junior_lighting int(10) unsigned NOT NULL DEFAULT '0' COMMENT "初级光效个数",
  middle_lighting int(10) unsigned NOT NULL DEFAULT '0' COMMENT "高级光效个数",
  vfx_resource varchar(255) NOT NULL DEFAULT '' COMMENT "特效资源地址",
  vfx_resource_md5 varchar(32) NOT NULL DEFAULT '' COMMENT "特效资源md5",
  is_del tinyint(3) unsigned NOT NULL DEFAULT 0,
  update_flag int(10) unsigned NOT NULL DEFAULT 0 COMMENT "已生效标记",
  begin_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT "新配置生效时间",
  create_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  update_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  rank_float float NOT NULL DEFAULT 0,
  channel_type_list varchar(255) NOT NULL DEFAULT '' COMMENT "房间类型列表",
  show_effect_end tinyint(3) unsigned NOT NULL DEFAULT 0 COMMENT "是否展示下架时间",
  act_name varchar(255) NOT NULL DEFAULT '' COMMENT "活动名称",
  act_begin_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT "活动开始时间",
  act_end_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT "活动结束时间",
  act_image_url varchar(255) NOT NULL DEFAULT '' COMMENT "活动图片",
  act_jump_url_hc_android varchar(255) NOT NULL DEFAULT '' COMMENT "活动跳转链接-欢游安卓",
  act_jump_url_hc_ios varchar(255) NOT NULL DEFAULT '' COMMENT "活动跳转链接-欢游ios",
  act_jump_url_mike_android varchar(255) NOT NULL DEFAULT '' COMMENT "活动跳转链接-麦可安卓",
  act_jump_url_mike_ios varchar(255) NOT NULL DEFAULT '' COMMENT "活动跳转链接-麦可ios",

  PRIMARY KEY (id),
  UNIQUE KEY idx_magic_unique(magic_spirit_id, update_flag),
  INDEX idx_begin_time(begin_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
`

// alter table magic_spirit_temporary add column show_effect_end tinyint(3) unsigned NOT NULL DEFAULT 0 COMMENT '是否展示下架时间', add column act_name varchar(255) NOT NULL DEFAULT '' COMMENT '活动名称', add column act_begin_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '活动开始时间', add column act_end_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '活动结束时间', add column act_image_url varchar(255) NOT NULL DEFAULT '' COMMENT '活动图片', add column act_jump_url_hc_android varchar(255) NOT NULL DEFAULT '' COMMENT '活动跳转链接-欢游安卓', add column act_jump_url_hc_ios varchar(255) NOT NULL DEFAULT '' COMMENT '活动跳转链接-欢游ios', add column act_jump_url_mike_android varchar(255) NOT NULL DEFAULT '' COMMENT '活动跳转链接-麦可安卓', add column act_jump_url_mike_ios varchar(255) NOT NULL DEFAULT '' COMMENT '活动跳转链接-麦可ios';

type MagicSpiritTemporary struct {
	Id               uint32    `json:"id"`
	MagicSpiritId    uint32    `json:"magic_spirit_id"`
	Name             string    `json:"name"`
	IconUrl          string    `json:"icon_url"`
	Price            uint32    `json:"price"`
	Ranking          uint32    `json:"ranking"`
	EffectBegin      uint32    `json:"effect_begin"`
	EffectEnd        uint32    `json:"effect_end"`
	DescribeImageUrl string    `json:"describe_image_url"`
	GiftDescribe     string    `json:"gift_describe"`
	ActivityJumpUrl  string    `json:"activity_jump_url"`
	JuniorLighting   uint32    `json:"junior_lighting"`
	MiddleLighting   uint32    `json:"middle_lighting"`
	VfxResource      string    `json:"vfx_resource"`
	VfxResourceMd5   string    `json:"vfx_resource_md5"`
	IsDel            uint32    `json:"is_del"`
	BeginTime        time.Time `json:"begin_time"`
	UpdateFlag       uint32    `json:"update_flag"`
	CreateTime       time.Time `json:"create_time"`
	UpdateTime       time.Time `json:"update_time"`
	RankFloat        float32   `json:"rank_float"`
	ChannelTypeList  string    `json:"channel_type_list"`

    ShowEffectEnd         bool      `json:"show_effect_end"`
    ActName               string    `json:"act_name"`
    ActBeginTime          time.Time `json:"act_begin_time"`
    ActEndTime            time.Time `json:"act_end_time"`
    ActImageUrl           string    `json:"act_image_url"`
    ActJumpUrlHcAndroid   string    `json:"act_jump_url_hc_android"`
    ActJumpUrlHcIos       string    `json:"act_jump_url_hc_ios"`
    ActJumpUrlMikeAndroid string    `json:"act_jump_url_mike_android"`
    ActJumpUrlMikeIos     string    `json:"act_jump_url_mike_ios"`
}

func (s *Store) AddMagicSpiritTmp(ctx context.Context, spirit *MagicSpiritTemporary) (uint32, error) {
	if spirit == nil {
		return uint32(0), nil
	}

	sql := "REPLACE INTO magic_spirit_temporary (magic_spirit_id,name,icon_url,price,ranking,effect_begin,effect_end," +
		"describe_image_url,gift_describe,activity_jump_url,junior_lighting,middle_lighting,vfx_resource,vfx_resource_md5,begin_time,rank_float,channel_type_list," +
        "show_effect_end,act_name,act_begin_time,act_end_time,act_image_url,act_jump_url_hc_android,act_jump_url_hc_ios,act_jump_url_mike_android,act_jump_url_mike_ios) " +
		"values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)"

	err := s.db.Exec(sql, spirit.MagicSpiritId, spirit.Name, spirit.IconUrl, spirit.Price, spirit.Ranking, spirit.EffectBegin, spirit.EffectEnd,
		spirit.DescribeImageUrl, spirit.GiftDescribe, spirit.ActivityJumpUrl, spirit.JuniorLighting, spirit.MiddleLighting, spirit.VfxResource, spirit.VfxResourceMd5, spirit.BeginTime,
		spirit.RankFloat, spirit.ChannelTypeList,
        spirit.ShowEffectEnd, spirit.ActName, spirit.ActBeginTime, spirit.ActEndTime, spirit.ActImageUrl,
        spirit.ActJumpUrlHcAndroid, spirit.ActJumpUrlHcIos, spirit.ActJumpUrlMikeAndroid, spirit.ActJumpUrlMikeIos).Error
	if err != nil {
		log.ErrorWithCtx(ctx, "AddMagicSpiritTmp fail, spirit:%+v, err:%v", spirit, err)
		return uint32(0), err
	}

	log.InfoWithCtx(ctx, "AddMagicSpiritTmp success, spirit:%+v", spirit)
	return spirit.MagicSpiritId, nil
}

func (s *Store) GetMagicSpiritTmp(ctx context.Context, beginTime time.Time) ([]*MagicSpiritTemporary, error) {
	rs := make([]*MagicSpiritTemporary, 0)

	sql := "select id,magic_spirit_id,name,icon_url,price,ranking,begin_time,effect_begin, effect_end,describe_image_url,gift_describe,activity_jump_url," +
		"junior_lighting,middle_lighting,vfx_resource,vfx_resource_md5,update_time,rank_float,channel_type_list," +
        "show_effect_end,act_name,act_begin_time,act_end_time,act_image_url,act_jump_url_hc_android,act_jump_url_hc_ios,act_jump_url_mike_android,act_jump_url_mike_ios " +
        " from magic_spirit_temporary where begin_time>? and update_flag =0 and is_del=0"

	err := s.db.Raw(sql, beginTime).Scan(&rs).Error
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMagicSpiritTmp lastETime:%d error: %v", beginTime, err)
		return rs, err
	}

	return rs, nil
}

func (s *Store) GetMagicSpiritTmpEffective(ctx context.Context, cur time.Time) ([]*MagicSpiritTemporary, error) {
	rs := make([]*MagicSpiritTemporary, 0)

	sql := "select id,magic_spirit_id,name,icon_url,price,ranking,begin_time,effect_begin, effect_end,describe_image_url,gift_describe,activity_jump_url," +
		"junior_lighting,middle_lighting,vfx_resource,vfx_resource_md5,rank_float,channel_type_list," +
        "show_effect_end,act_name,act_begin_time,act_end_time,act_image_url,act_jump_url_hc_android,act_jump_url_hc_ios,act_jump_url_mike_android,act_jump_url_mike_ios " +
        " from magic_spirit_temporary where begin_time<=? and update_flag =0 and is_del=0"

	err := s.db.Raw(sql, cur).Scan(&rs).Error
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMagicSpiritTmp cur:%d error: %v", cur, err)
		return rs, err
	}

	return rs, nil
}

func (s *Store) SetMagicSpiritUpdateFlag(ctx context.Context, magicId uint32, beginTime time.Time) error {
	query := "update magic_spirit_temporary set update_flag=id where magic_spirit_id=? and begin_time=?"

	err := s.db.Exec(query, magicId, beginTime).Error
	if err != nil {
		log.ErrorWithCtx(ctx, "SetMagicSpiritUpdateFlag magicId:%d,beginTime:%v, error: %v", magicId, beginTime, err)
		return err
	}

	log.InfoWithCtx(ctx, "SetMagicSpiritUpdateFlag magicId:%d,beginTime:%v success", magicId, beginTime)
	return nil
}

func (s *Store) DelMagicSpiritTmp(ctx context.Context, ids []uint32) error {
	sql := "update magic_spirit_temporary set is_del=1 where magic_spirit_id in (?)"

	err := s.db.Exec(sql, genParamJoinStr(ids)).Error
	if err != nil {
		log.ErrorWithCtx(ctx, "DelMagicSpiritTmp id:%d error: %v", ids, err)
		return err
	}
	log.InfoWithCtx(ctx, "DelMagicSpiritTmp id:%d success", ids)
	return nil
}
