syntax = "proto3";

package ga.game_ugc;

import "ga_base.proto";
import "ugc/ugc_.proto";
import "online/friendol_.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/game-ugc";

//开黑拉流接口
message GetGameNewsFeedsReq {
  ga.BaseReq base_req = 1;
  uint32 get_mode = 2; //1代表下一页，2代表刷新
  uint32 content_type = 3[deprecated = true]; //废弃，见ugc_.proto ContentType
  repeated string browse_list = 4;  // 未浏览帖子id
  message GameRecommendationStreamReq {
    uint32 tab_id = 1;
    string topic_set_id = 2[deprecated = true]; //废弃字段
    string config_tab_set_id = 3; //动态tabId
    repeated string topic_ids = 4; // 选中的话题id列表
  }
  message GetPersonalSourcePostReq {
    int64 last_post_created_time = 1; // 上次拉流的最后一个帖子的创建时间，用于分页，第一页传0
    uint32 filter_type = 2; // 筛选类型，见FilterType
  }
  oneof request_type {
    GameRecommendationStreamReq game_recommendation_stream_req = 5; // 开黑游戏专区帖子推荐流
    GetPersonalSourcePostReq get_personal_source_post_req = 7; // 个人流开黑帖子
  }
  uint32 rec_rule_type = 6; // 1-默认热门推荐流规则，2-最新流推荐规则
}

enum PersonalPageFilterType {
  PERSONAL_PAGE_FILTER_TYPE_UNSPECIFIED = 0;
  PERSONAL_PAGE_FILTER_TYPE_GAME_ZONE = 1; // 仅专区可见
}

message GetGameNewsFeedsResp {
  ga.BaseResp base_resp = 1;
  repeated GameFeed feeds = 2; // 流列表
  bool load_finish = 3; //返回true表示没有下一页了
}

message GameUgcTopicInfo {
  string topic_id = 1;
  string name = 2;
}

message GameFeed {
  string feed_id = 1; //流id
  // 帖子
  message GamePostInfo {
    BasePostInfo post_info = 1; //帖子内容及属性
    PostOwnerInfo post_owner = 2; //用户展示信息
    ga.ugc.VoteInfo vote = 12;    // 帖子关联的投票信息
    RelationWithPost relation_with_post = 4; //拉流用户与该帖子的关系（是否点赞，收藏）
    repeated GameUgcTopicInfo topic_infos = 5; //关联的话题信息
    ga.online.FriendsDetail follow_info = 6; // 跟随信息
    uint32 button_type = 7; //按钮类型，见GamePostButtonType
  }
  oneof feed_data {
    GamePostInfo post = 2; //开黑专区帖子推荐流
    ga.ugc.PostInfo ugc_post_info = 3; //个人流开黑帖子信息
  }

}

enum GamePostButtonType {
  GAME_POST_BUTTON_TYPE_UNSPECIFIED = 0;
  GAME_POST_BUTTON_TYPE_ENTER_ROOM = 1; //进房按钮
  GAME_POST_BUTTON_TYPE_FOLLOW = 2; //关注按钮
  GAME_POST_BUTTON_TYPE_ALREADY_FOLLOW = 3; //已关注按钮
}

//帖子信息
message BasePostInfo{
  string post_id = 1; //帖子id
  uint32 post_type = 2; //帖子类型 见ugc_.proto PostType
  string content = 3; //帖子文本内容
  repeated ga.ugc.Attachment attachments = 4; //帖子附件信息
  uint32 comment_count = 5; //评论数量
  uint32 attitude_count = 6; //点赞数量
  uint32 content_type = 7; //内容类型，见ugc_.proto ContentType
  enum PostSource {
    POST_SOURCE_UNSPECIFIED = 0;
    POST_SOURCE_GAME_RECOMMENDATION = 1;      // 游戏专区推荐流
  }
  uint32 post_source = 8[deprecated = true]; // 废弃
  uint32 privacy = 9;  //帖子附件下载策略，见ugc_.proto AttachmentDownloadPrivacy
  int64 post_time = 10; //发帖时间
  string post_title = 11; //发布帖子标题
  uint32 share_count = 12; // 分享数
}

// 拉流用户与帖子的关系
message RelationWithPost{
  bool had_followed_poster = 1; //拉流用户是否关注帖子发布者
  // 调整代码结构时，忘记修改位序！！！导致后续新增字段位序错误，空白位序可以被后续新增字段占用
  bool had_attitude = 7; //拉流用户是否点赞
  bool had_favoured = 16; //拉流用户是否收藏
}

//用户状态
enum UserStatus {
  USER_STATUS_UNSPECIFIED = 0;//无效
  USER_STATUS_PUBLISHING = 1;//发布中
  USER_STATUS_ONLINE = 2;//在线
}

//帖子用户信息
message PostOwnerInfo {
  uint32 uid = 1; // id
  string account = 2; // 账号
  string nickname = 3; // 昵称
  uint32 gender = 4; // 性别, 0：女，1：男
  message GameCardInfo {
    string game_card_text = 1; //游戏卡信息文案
    uint32 game_card_id = 2; //游戏卡id
  }
  GameCardInfo game_card_info = 5;
  repeated uint32 user_status = 6[deprecated = true]; //废弃，用户状态,见UserStatus
  uint32 ugc_channel_id = 7[deprecated = true]; //废弃 个人房房间id
  uint32 user_online_status = 8; //用户在线状态,1在线,2不在线
}

// 发帖
message GamePostPostReq {
  ga.BaseReq base_req = 1;
  string title = 2;
  string content = 3; // 帖子内容
  uint32 post_type = 4; //帖子类型 见ugc_.proto PostType
  // ga.ugc.PostOrigin origin = 5[deprecated = true]; //废弃，// 发帖来源
  uint32 origin = 5;  // 见ugc_.proto PostOrigin 发帖来源
  uint32 game_origin = 6; // 发帖来源  GamePostOrigin
  uint32 attachment_image_count = 7; // 图片附件数量
  uint32 attachment_video_count = 8; // 视频附件数量
  uint32 attachment_audio_count = 9; // 音频附件数量
  repeated ga.ugc.Attachment predefined_attachments = 10;
  // ga.ugc.AttachmentDownloadPrivacy privacy = 11[deprecated = true];
  uint32 privacy = 11; // 见ugc_.proto AttachmentDownloadPrivacy
  //extra 额外信息
  ga.ugc.Extra extra = 12;
  // 投票信息
  ga.ugc.VoteInfo vote = 13;
  uint32 config_tab_id = 14[deprecated = true]; // 开黑专区里面的动态流tab
  uint32 tab_id = 15; // 主题玩法id
  uint32 origin_new = 16[deprecated = true];
  uint32 privacy_new = 17[deprecated = true];
  string config_tab_id_new = 18; // 开黑专区里面的动态流tab
  uint32 visible_scope = 19; // 可见范围，见VisibleScope
  string topic_id = 20; // 发帖话题id
}

enum VisibleScope {
  VISIBLE_SCOPE_UNSPECIFIED = 0;
  VISIBLE_SCOPE_GAME_ZONE = 1; // 仅开黑专区可见
}

enum GamePostOrigin {
  GAME_POST_ORIGIN_UNSPECIFIED = 0;
  GAME_POST_ORIGIN_GAME_TAB = 1;
}

message GamePostPostResp {
  ga.BaseResp base_resp = 1;
  string post_id = 2; // 帖子id
  string image_token = 3; // upload image token
  string video_token = 4; // upload video token
  repeated string image_keys = 5; // 上传用的key
  repeated string video_keys = 6; // 上传用的key
  string audio_token = 7; // upload audio token
  repeated string audio_keys = 8; // 上传用的key
}
// 废弃结构
message SubTabConfig {
  uint32 sub_tab_id = 1;  // 子tab_id
  string sub_tab_name = 2; // 子tab名称
  uint32 tab_type = 3;   // 0-房间类型, 1-动态类型
}
// 废弃结构
message GetSubTabConfigByTabIdReq {
  ga.BaseReq base_req = 1;
  uint32 tab_id = 2;
}
// 废弃结构
message GetSubTabConfigByTabIdResp {
  ga.BaseResp base_resp = 1;
  repeated SubTabConfig configs = 2;
}

message ConfigTab {
  string config_tab_id = 1;  // 子tab_id
  string config_tab_name = 2; // 子tab名称
  uint32 tab_type = 3;   // 1-房间类型, 2-动态类型, 3-活动海报类型, 4-活动中心类型
  repeated RecRule rec_rules = 4; // 推荐配置
  uint32 force_rec_rule = 5; // 强制定位推荐规则， 0为空代表没有开启
  uint32 display_strategy = 6; // tab展示策略：0-AB实验组可见，1-所有用户可见
  oneof ext_info {
      UgcTabExtInfo ugc_ext_info = 7; // 动态tab额外信息
  }
}

message UgcTabExtInfo {
  repeated GameUgcTopicInfo display_topics = 1; // 动态tab展示话题信息
}

message RecRule {
  uint32 rec_rules = 1; // 1-默认热门推荐流规则，2-最新流推荐规则
  string name = 2;  // 展示流的文案
}

enum ConfigTabType {
  CONFIG_TAB_TYPE_UNSPECIFIED = 0;
  CONFIG_TAB_TYPE_CHANNEL_LIST = 1; // 房间流tab
  CONFIG_TAB_TYPE_POST_TAB = 2;     // 动态tab
  CONFIG_TAB_TYPE_ACTIVITY_POST = 3;// 活动海报tab
  CONFIG_TAB_TYPE_ACTIVITY_SET = 4; // 活动集合tab
  CONFIG_TAB_TYPE_GAME_PAL_CARD = 5;// 搭子卡tab
  CONFIG_TAB_TYPE_GROUP_CHAT = 6;   // 群聊tab
  CONFIG_TAB_TYPE_GAME_HALL = 7;    // 组队大厅tab
}

message GetConfigTabByTabIdReq {
  ga.BaseReq base_req = 1;
  uint32 tab_id = 2;
  repeated uint32 visible_config_tab_type = 3; // 游戏专区默认返回房间流和动态tab，后续新增的其他tab由客户端传参控制，详见ConfigTabType
}

message GetConfigTabByTabIdResp {
  ga.BaseResp base_resp = 1;
  repeated ConfigTab configs = 2;
}

message GetConfigTabTitleReq {
  ga.BaseReq base_req = 1;
  string config_tab_id = 2;  // 动态配置tab_id
}

message GetConfigTabTitleResp {
  ga.BaseResp base_resp = 1;
  repeated string title = 2;
}

message CheckUserIsBannedPostReq {
  ga.BaseReq base_req = 1;
  uint32 tab_id = 2;
  uint32 ban_post_type = 3; // 配置类型，0-专区禁止发帖配置, 1-禁止发布搭子卡/私聊配置
}

message CheckUserIsBannedPostResp {
  ga.BaseResp base_resp = 1;
  bool is_banned = 2;   // 是否被禁止发帖
  string banned_reason = 3; // 禁止原因
}

// 综合频道信息
message GetComprehensiveChannelInfoReq {
  ga.BaseReq base_req = 1;
  uint32 tab_id = 2;
  repeated uint32 visible_config_tab_type = 3; // 综合频道默认返回活动tab，后续新增的其他tab由客户端传参控制，详见ConfigTabType
}

message GetComprehensiveChannelInfoResp {
  ga.BaseResp base_resp = 1;
  repeated ConfigTab activity_tabs = 2; // 活动tab列表
  repeated ConfigTab config_tabs = 3; // 动态tab列表
}

// 根据帖子id，获取帖子列表信息
message GetGameFeedByIdsReq{
  ga.BaseReq base_req = 1;
  repeated string post_ids = 2; //帖子id
}

message GetGameFeedByIdsResp{
  ga.BaseResp base_resp = 1;
  repeated GameFeed feeds = 2; // 流列表
}

// 是否展示个人页筛选
message NeedShowPersonalSourceFilterReq  {
  ga.BaseReq base_req = 1;
}

message NeedShowPersonalSourceFilterResp  {
  ga.BaseResp base_resp = 1;
  bool is_show = 2;
}

// 玩法二级tab是否可见
message IsConfigTabVisibleReq {
  ga.BaseReq base_req = 1;
  uint32 tab_id = 2;
  uint32 config_tab_type = 3; // see enum ConfigTabType
}

message IsConfigTabVisibleResp {
  ga.BaseResp base_resp = 1;
  bool is_visible = 2;
}