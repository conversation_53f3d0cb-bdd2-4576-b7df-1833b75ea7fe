syntax = "proto3";

package ga.muse_shining_point_logic;

import "ga_base.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/muse-shining-point-logic";

// 闪光点列表
message GetAllShiningPointRequest{
  BaseReq base_req = 1;
}

message GetAllShiningPointResponse{
  BaseResp base_resp = 1;
  repeated ShiningPointClassify point_classify_list = 2; // 闪光点分类列表
}

// 闪光点认证剩余次数
message UserShiningPointCertCountRemain{
  uint32 total_remain = 1; // 今日总认证剩余次数
  uint32 this_user_remain = 2; // 可帮该用户认证剩余次数
}

// 闪光点分类
message ShiningPointClassify{
  string classify_id = 1; // 闪光点分类id
  string classify_name = 2; // 闪光点分类名称
  repeated ShiningPointInfo point_list = 3; // 闪光点列表
}

// 闪光点
message ShiningPointInfo{
  string point_id = 1; // 闪光点id
  string point_name = 2; // 闪光点名称
}

// 添加用户闪光点
message UpdateUserShiningPointRequest{
  BaseReq base_req = 1;
  repeated string point_list = 2; // 闪光点列表
}

message UpdateUserShiningPointResponse{
  BaseResp base_resp = 1;
}

// 获取用户闪光点
message GetUserShiningPointRequest{
  BaseReq base_req = 1;
  uint32 uid = 2;
  uint32 page_source = 3; // ShiningPointPageSource 客态详情页需要返回认证剩余次数
}

enum ShiningPointPageSource{
  SHINING_POINT_PAGE_SOURCE_UNSPECIFIED = 0;
  SHINING_POINT_PAGE_SOURCE_USER = 1; // 客态详情页
  SHINING_POINT_PAGE_SOURCE_HOME_PAGE_MIX_CHANNEL = 2; // 首页混推流
  SHINING_POINT_PAGE_SOURCE_USER_DETAIL_PAGE = 3; // 个人主页
}

message GetUserShiningPointResponse{
  BaseResp base_resp = 1;
  repeated ShiningPointInfo point_list = 2; // 闪光点列表
  ShiningPointUserInfo user_info = 3;
  repeated ShiningPointExtraInfo cert_list = 4; // 闪光点额外信息
  UserShiningPointCertCountRemain user_cert_remain = 5; // 闪光点认证剩余次数
  ShiningPointGuidanceBubble guidance_bubble = 6; // 引导气泡
}

// 引导气泡类型
enum ShiningPointGuidanceBubbleType{
  SHINING_POINT_GUIDANCE_BUBBLE_TYPE_UNSPECIFIED = 0;
  SHINING_POINT_GUIDANCE_BUBBLE_TYPE_NO_CERT = 1; // 未认证闪光点
  SHINING_POINT_GUIDANCE_BUBBLE_TYPE_NO_ADD = 2; // 未添加闪光点
  SHINING_POINT_GUIDANCE_BUBBLE_TYPE_NEW_UP = 3;  // 在有新的标签上线时，且其中不包含限量闪光点时
  SHINING_POINT_GUIDANCE_BUBBLE_TYPE_NEW_UP_LIMIT = 4; // 在有新的标签上线时，且其中包含限量闪光点时
  SHINING_POINT_GUIDANCE_BUBBLE_TYPE_OTHERS_RECENT_ADD = 5;  // 访问他人个人主页时，若对方最近两周有添加闪光点
  SHINING_POINT_GUIDANCE_BUBBLE_TYPE_OTHERS_RECENT_CERT = 6;  // 访问他人个人主页时，若对方最近两周有点亮的闪光点
}

// 引导气泡
message ShiningPointGuidanceBubble{
  uint32 bubble_type = 1; // ShiningPointGuidanceBubbleTypes
  string bubble_text = 2; // 引导气泡 文案
}

// ShiningPointExtraInfo 闪光点认证信息 按照认证顺序排列
message ShiningPointExtraInfo{
  string point_id = 1; // 闪光点id
  uint32 cert_count = 2; // 认证次数
  uint32 cert_type = 3; // ShiningPointCertType
  bool is_cert_by_me = 4; // 是否被我认证
  uint32 cert_times_by_me = 5; // 我认证的次数
  bool is_cert_by_me_today = 6; // 今日是否被我认证
}

// 闪光点认证类型 是否被好友认证
enum ShiningPointCertType{
  SHINING_POINT_CERT_TYPE_UNSPECIFIED = 0;
  SHINING_POINT_CERT_TYPE_FRIEND = 1; // 好友认证
}

message ShiningPointUserInfo{
  uint32 uid = 1;
  string account = 2;
  uint32 sex = 3;
  string nickname = 4;
}

/* 闪光点 认证 */
message ShiningPointFriendCertRequest{
  BaseReq base_req = 1;
  uint32 uid = 2; // 用户id
  string point_id = 3; // 闪光点id
}

message ShiningPointFriendCertResponse{
  BaseResp base_resp = 1;
}

// 批量认证
message BatchShiningPointFriendCertRequest{
  BaseReq base_req = 1;
  uint32 uid = 2; // 用户id
  repeated string point_id_list = 3; // 闪光点id列表
}

// 批量认证返回
message BatchShiningPointFriendCertResponse{
  BaseResp base_resp = 1;
}

// 引导用户添加闪光点页面
message GetUserAddShiningPointGuidanceRequest{
  BaseReq base_req = 1;
  uint32 page_source = 2; // ShiningPointPageSource SHINING_POINT_PAGE_SOURCE_HOME_PAGE_MIX_CHANNEL
}

message GetUserAddShiningPointGuidanceResponse{
  BaseResp base_resp = 1;
  repeated ShiningPointInfo point_list = 2; // 闪光点列表(随机一部分)
}

// GetShiningPointIntroPageRequest 获取闪光点介绍页请求
message GetShiningPointIntroPageRequest{
  BaseReq base_req = 1;
  uint32 uid = 2; // 用户id
  repeated string point_id_list = 3; // 闪光点id列表
}

// ShiningPointIntroPage 闪光点介绍页
message ShiningPointIntroPage{
  string point_id = 1; // 闪光点id
  string point_name = 2; // 闪光点名称
  repeated string cert_friend_accounts = 3; // 认证好友账号列表
  uint32 cert_count = 4; // 认证次数 xx位好友认证
  uint32 rank = 5; // 排名
  float defeat_rate = 6; // 打败xx.x%用户 保留一位小数
  ShiningPointUserInfo user_info = 7; // 用户信息
}

// GetShiningPointIntroPageResponse 获取闪光点介绍页响应
message GetShiningPointIntroPageResponse{
  BaseResp base_resp = 1;
  repeated ShiningPointIntroPage intro_page_list = 2; // 闪光点介绍页列表
}