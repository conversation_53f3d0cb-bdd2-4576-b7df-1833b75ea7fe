@startuml
'https://plantuml.com/sequence-diagram


actor 客户端 as app



group 引导用户添加闪光点页面
autonumber
app ->logicSvr:页面来源：混推流
logicSvr -> 闪光点svr
闪光点svr --> redis: 我是否有闪光点
redis --> 闪光点svr: 有/无
闪光点svr --> logicSvr: 闪光点列表
logicSvr --> app: 闪光点列表
end

group 查看用户闪光点列表
autonumber
app -> logicSvr: uid
logicSvr -> 闪光点svr: uid
闪光点svr --> redis: 查点亮、被我认证次数，\n今日是否被我认证，近2周点亮
redis --> 闪光点svr:
闪光点svr --> logicSvr: 闪光点列表信息+引导气泡文案
logicSvr --> app
end


group 认证
autonumber
app -> logicSvr: from_uid to_uid point_id
logicSvr -> 闪光点svr: from_uid to_uid point_id
闪光点svr -> mongo: 存入：认证表、计数表
mongo --> 闪光点svr: 最新数值
闪光点svr -> redis:存入：榜单表
闪光点svr --> logicSvr:
logicSvr --> app
end

group 气泡
autonumber
app -> logicSvr: from_uid to_uid
logicSvr -> 闪光点svr: from_uid to_uid
闪光点svr -> redis: from_uid to_uid
redis --> 闪光点svr: 近2周点亮、上新、是否有闪光点
闪光点svr --> logicSvr: 气泡类型、文案
logicSvr --> app: 气泡类型、文案
end






@enduml