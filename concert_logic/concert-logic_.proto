syntax = "proto3";

package ga.concert_logic;

import "ga_base.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/concert-logic";

// 获取歌单
enum ConcertSongType{
    ConcertSongType_UNDEFINED = 0;
    ConcertSongType_HOT = 1;
    ConcertSongType_RCMD = 2;
}

// 获取歌曲选项
message GetConcertSongOptsReq{
    ga.BaseReq base_req = 1;
}
message GetConcertSongOptsResp{
    ga.BaseResp base_resp = 1;
    repeated ConcertSongOpt opts =  2;
}

message ConcertSongOpt{
    string id = 1;
    string name = 2;
    repeated ConcertSongOptElem elems = 3;
}

message ConcertSongOptElem{
    string id = 1;
    string name = 2;
}

message SearchConcertSongReq{
    ga.BaseReq base_req = 1;
    string key_word = 2;
}
message SearchConcertSongResp{
    ga.BaseResp base_resp = 1;
    repeated ConcertSong songs = 2;
}

message GetConcertSongListReq{
    ga.BaseReq base_req = 1;
    repeated ConcertSongOpt opts = 2;
    ConcertSongType song_type = 3;
    ConcertLoadMore load_more = 4; // 首次拉取不传, 加载更多时原封不动地填入上一次GetBeLikedSongListResp中的load_more字段
    uint32 count = 5;
}

message GetConcertSongListResp{
    ga.BaseResp base_resp = 1;
    repeated ConcertSong songs = 2;
    ConcertLoadMore load_more = 3;   // 下一次加载更多时, 将load_more原封不动地填入请求的load_more中; 如果不包含此字段, 表示已经拉完了
}

message ConcertSong{
    string id = 1;
    string name = 2; // 歌名
    string bg = 3; // 封面
    string author = 4; // 制作人
    uint32 hot_val = 5; // 热度值
    string singer = 6; // 歌手名称
    repeated string role_names = 7; // 乐手信息
    bool is_new = 8; // 是否是新歌
}

message ConcertLoadMore{
    uint32 last_page = 1;
    uint32 last_count = 2;
}

// 获取演唱资源
message GetAllConcertResourceReq{
    ga.BaseReq base_req = 1;
    uint32 version = 2;
}
message GetAllConcertResourceResp{
    ga.BaseResp base_resp = 1;
    uint32 version = 2;
    repeated InstrumentRes res_list = 3; // 默认资源
    repeated ConcertStageDecoration decorations = 4; // 舞台装饰

    string default_url = 5; // 默认地址
    string default_md5 = 6; // 默认地址md5
    string default_transition_img = 7; // 舞台切换图片
    ConcertRes sonic_wave = 8; // 声音波纹
}

// 字段名是按设计的切图名称来的
message ConcertStageDecoration{
    Stagetype stagetype = 1;
    ConcertRes light_on_bg = 2;
}

// 舞台类型
enum Stagetype {
    Stagetype_UNDEFINED = 0;
    Stagetype_mild = 1; // 温和
    Stagetype_Excited = 2; // 激动
}

// 点歌
message StartConcertSingingReq{
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2;
    string song_id = 3;
}
message StartConcertSingingResp{
    ga.BaseResp base_resp = 1;
}

// 下载资源通知
message PreloadNotify{
    SingingInfo info = 1;
    string lrc = 2;
    ConcertRes backing_track = 3; // 伴奏
}

// 获取乐谱
message GetMusicBookReq{
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2;
    uint32 game_id = 3;
}
message GetMusicBookResp{
    ga.BaseResp base_resp = 1;
    MusicBook music_book = 2;
}

enum ConcertGameStage{
    ConcertGameStage_UNDEFINED = 0; // 初始状态
    ConcertGameStage_DOWNLOADING = 1; // 下载中
    ConcertGameStage_Singing = 2; // 演唱中
}

message SingingInfo{
    uint32 channel_id = 1;
    uint32 game_id = 2;
    ConcertSimpleSong song = 3;
    repeated BandMember members = 4;
    ConcertGameStage stage = 5;
    uint32 stage_updated_at = 6; // 单位：秒
    uint64 version = 7;
}

message ConcertSimpleSong{
    string song_id = 1;
    string song_name = 2;
    uint32 stage_open_duration = 3; // 舞台开幕时长（单位：秒）
    uint32 first_word_ms = 4; // 以歌曲开始时间为起点的偏移（单位：毫秒）
    uint32 total_duration = 5; // 歌曲时长（单位：毫秒）
    Stagetype stagetype = 6; // 舞台类型
    // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
    uint32 ChosenBy = 7; // 点歌uid
    bool open_backing_track = 8; // 是否打开伴奏
    repeated BandRole band_roles = 9; // 乐队成员列表
    uint32 deviation = 10; // 按键允许的误差（单位：ms）
    uint32 reaction_time= 11; // 下落时间（单位：ms）
    uint32 secondary_deviation = 12; // 按键允许的次级误差（单位：ms）
    uint32 long_decision_interval = 13; // 长按判定间隔（单位：ms）
    bool judge_all_ctrl_zero = 14; // 判断是否需要判断全部操作数为0，默认不需要，需要的时候客户端要判断为0时，不显示等待中状态
}

message BandMember{
    uint32 uid = 1; // 用户id
    BandRole role = 2; // 乐队角色
    string nickname = 3; // 昵称
    string account = 4; // 头像
    uint32 sex = 5; // 性别
    bool can_push_stream = 6;
}

// 乐队成员下麦/推流用户变更/开始演唱
message SingingInfoNotify{
    SingingInfo info = 1;
}

// 成绩更新推送
message UpdateGradeNotify{
    uint32 channel_id = 1;
    uint32 game_id = 2;
    repeated ConcertGrade grade_list = 3;
}

message ConcertResultRole{
    uint32 uid = 1; // 用户id
    BandRole role = 2; // 乐队角色
    string role_name = 3; // 角色名称
    string nickname = 4; // 昵称
    string account = 5; // 头像
    uint32 sex = 6; // 性别
    uint32 mic_id = 7; // 麦位id
    bool is_mvp = 8; // 是否mvp
    ConcertGrade grade = 9; // 演出成绩
}

message ConcertGrade{
    uint32 count = 1; // 演奏分
    float accuracy_rate = 2; // 准确率
    uint32 hits_num = 3;  // 连击数
    uint32 perfect_num = 4; // Perfect数
    uint32 great_num = 5;  // Great数
    uint32 miss = 6;  // Miss数
    string rate = 7; // 评级
    uint32 uid = 8; // 用户id
    string rate_name = 9; // 评级名称
}

// 结束演唱通知
message SongResultNotify{
    SingingInfo info = 1;
    repeated ConcertResultRole roles = 2;
    string song_name = 3;
    uint32 tacit_understanding = 4; // 默契度，0~100
    uint32 game_id = 5;
    string song_id = 6;
    ConcertRes recording_video = 7; // 录制背景视频
}

// 完成下载
message CompleteDownloadingMusicBookReq{
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2;
    uint32 game_id = 3;
}
message CompleteDownloadingMusicBookResp{
    ga.BaseResp base_resp = 1;
}

// 停止演唱
message StopConcertSingingReq{
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2;
    uint32 game_id = 3;
}
message StopConcertSingingResp{
    ga.BaseResp base_resp = 1;
}

// 获取演唱信息
message GetConcertInfoReq{
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2;
}
message GetConcertInfoResp{
    ga.BaseResp base_resp = 1;
    SingingInfo info = 2;
    string lrc = 3;
    ConcertRes backing_track = 4; // 伴奏
}

message UpdateBackingTrackStatusReq{
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2;
    uint32 game_id = 3;
    bool open_backing_track = 4; // 是否打开伴奏
}
message UpdateBackingTrackStatusResp{
    ga.BaseResp base_resp = 1;
}

message MusicBook{
    BandRole role = 1;
    repeated KeyMapRhythmPoint key_map_rhythm_points = 2; // 键位节奏点
    ConcertRes backing_track = 3; // 伴奏
}

message ConcertRes {
    string url = 1;
    string md5 = 2;
}

message InstrumentRes{
    BandRole role = 1;
    uint32 mic_id = 2;
    string name = 3; // 名称
    repeated InstrumentAudio audios = 4; // 音频
    InstrumentAudio hold_mic_audio = 5; // 上麦音效
    string normal_mic_seat = 6; // 空麦图片
    string hold_mic_seat = 7; // 上麦图片
    ConcertRes normal_mic_lottie = 8; // 非演奏中lottie
    ConcertRes playing_mic_lottie = 9; // 演奏中lottie
    string empty_seat_bg = 10; // 结算页空位
    string hold_seat_bg = 11; // 结算页（有人位）
}

enum KeyMapType{
    KeyMapType_UNDEFINED = 0;
    KeyMapType_CLICK = 1; // 单击
    KeyMapType_PRESS = 2; // 长按
    KeyMapType_SLIDE = 3; // 滑动
}

message KeyMapRhythmPoint{
    KeyMapType key_map_type = 1;
    uint32 note_on_at = 2; // 按下按键（单位：毫秒）
    uint32 note_off_at = 3; // 松开按键（单位：毫秒）
    uint32 from_key_map = 4; // 起始键位
    uint32 to_key_map = 5; // 终点键位
}

message ChordAudio{
    string chord = 1; // 和弦名称
    repeated InstrumentAudio audios = 2; // 音频
}

message InstrumentAudio{
    string id = 1;
    uint32 key_map = 2; // 键位
    string url = 3; // 下载链接
    string md5 = 4;
}

enum BandRole{
    BandRole_UNDEFINED = 0;
    BandRole_MAIN_SINGER = 1; // 主唱
    BandRole_LEAD_GUITAR = 2; // 主音吉他
    BandRole_KEYBOARD = 3; // 键盘手
    BandRole_BASSIST = 4; // 贝斯手
    BandRole_DRUMMER = 5; // 鼓手
    BandRole_RHYTHM_GUITAR = 6; // 节奏吉他
}

enum ReportConcertGradeType {
    MISS = 0;
    PERFECT =1;
    GREAT = 2;
}

message ReportConcertSuccCountReq{
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2;
    uint32 game_id = 3;
    repeated ReportConcertGradeType grade_list  = 4;
    bool is_reconnect = 5; // 是否重连 选填
}
message ReportConcertSuccCountResp{
    ga.BaseResp base_resp = 1;
    uint32 interval = 2; // ms，下次上报盘间隔时间
}

message JoinConcertReq{
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2;
    uint32 game_id = 3;
}
message JoinConcertResp{
    ga.BaseResp base_resp = 1;
    SingingInfo info = 2;
}

message ConcertImage{
    string id = 1;
    string img = 2;
    string unselected_img = 3;
    string thumbnail = 4; // 缩略图
    repeated ConcertImageRes images = 5;
    uint32 sex = 6; // 0是女性 1是男性
    uint32 level = 7; // 等级
}

message ConcertImageRes{
    BandRole role = 1;
    ConcertRes res_1 = 2;
    ConcertRes res_2 = 3;
}

// 获取形象配置
message GetAllConcertImageReq{
    ga.BaseReq base_req = 1;
}
message GetAllConcertImageResp{
    ga.BaseResp base_resp = 1;
    repeated ConcertImage images = 2;
    string chosen_image_id = 3; // 已选形象id
}

// 设置用户形象
message SetConcertUserImageReq{
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2;
    string image_id = 3;
}
message SetConcertUserImageResp{
    ga.BaseResp base_resp = 1;
}

// 获取麦上用户形象
message GetAllConcertOnMicUserImageReq{
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2;
}
message GetAllConcertOnMicUserImageResp{
    ga.BaseResp base_resp = 1;
    map<uint32,ConcertImage> user_image = 2;
}

// 用户上麦/更新状态时推送
message ConcertImageNotify{
    uint32 uid = 1;
    ConcertImage image = 2;
    uint32 channel_id = 3;
}

// 获取乐响图鉴
message GetConcertSongTaskProgressListReq{
    ga.BaseReq base_req = 1;
    ConcertLoadMore load_more = 2; // 首次拉取不传, 加载更多时原封不动地填入上一次GetConcertSongTaskProgressListResp中的load_more字段
    uint32 count = 3;
}
message GetConcertSongTaskProgressListResp{
    ga.BaseResp base_resp = 1;
    ConcertLevelDetail level_detail = 2; // 首次请求时有值
    uint32 have_star_song_num = 3; // 首次请求时有值
    uint32 total_song_num = 4; // 首次请求时有值
    repeated ConcertSongTaskProgress list = 5;
    ConcertLoadMore load_more = 6;
}

message ConcertSongTaskProgress{
    string song_id = 1;
    string bg = 2; // 背景
    uint32 curr_star_num = 3; // 当前星星数量
    uint32 total_star_num = 4; // 星星总数
}

message ConcertLevelDetail{
    uint32 level = 1; // 等级
    uint32 star_num = 2; // 星星数量
    uint32 star_num_to_next_level = 3; // 距离下一个等级需要的星星数量
    uint32 ratio = 4; // 等级进度百分比（0~100）
}

// 获取歌曲任务进度详情
message GetConcertSongTaskProgressDetailsReq{
    ga.BaseReq base_req = 1;
    string song_id = 2;
}
message GetConcertSongTaskProgressDetailsResp{
    ga.BaseResp base_resp = 1;
    ConcertSongTaskProgressDetail last_detail = 2; // 上一个
    ConcertSongTaskProgressDetail curr_detail = 3; // 当前展示的歌曲星级
    ConcertSongTaskProgressDetail next_detail = 4; // 下一个
}

message ConcertSongTaskProgressDetail{
    ConcertSongTaskProgress progress = 1;
    uint32 task_num_to_next_star = 2; // 距离下颗星星的挑战数量
    repeated ConcertSongTask  uncompleted_tasks = 3; // 未完成挑战
    repeated ConcertSongTask completed_tasks = 4; // 已完成挑战
}

message ConcertSongTask{
    string title = 1; // 标题
    string desc = 2; // 描述
    string icon = 3; // 图标
}

// 用于强插到点歌列表
message GetConcertSongByIdReq{
    ga.BaseReq base_req = 1;
    string song_id = 2; // 歌曲id
}
message GetConcertSongByIdResp{
    ga.BaseResp base_resp = 1;
    ConcertSong song = 2;
}

// 星级变化通知
message ConcertSongTaskProgressUpdateNotify{
    ConcertSongTaskProgress progress = 1;
    string title = 2;
}

message GetRecentUploadedSongReq{
    ga.BaseReq base_req = 1;
}
message GetRecentUploadedSongResp{
    ga.BaseResp base_resp = 1;
    ConcertSong song = 2;
}