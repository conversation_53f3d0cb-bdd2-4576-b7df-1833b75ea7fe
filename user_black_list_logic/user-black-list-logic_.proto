syntax = "proto3";

package ga.user_black_list_logic;

import "ga_base.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/user-black-list-logic";

message GetUserBlackListReq {
     BaseReq base_req = 1;
     uint32 active_uid = 2;  //拉黑方uid
     uint32 page = 3;        //页码，从1开始
     uint32 count = 4;       //该页个数
}

message GetUserBlackListResp {
    BaseResp base_resp = 1;
    uint32 total = 2;         //黑名单内总数
    repeated GenericMember account_list  = 3;  //被拉黑方账户信息列表
}

enum AddBlackSource {
    ENUM_ADD_BLACK_SOURCE_UNSPECIFIED = 0;
    ENUM_ADD_BLACK_SOURCE_FROM_IM_CHAT = 1;  //从IM聊天页面拉黑
    ENUM_ADD_BLACK_SOURCE_FROM_PERSONAL_HOMEPAGE = 2; //从个人主页拉黑
}

enum FollowStatus {
  ENUM_FOLLOW_STATUS_UNSPECIFIED = 0;
  ENUM_FOLLOW_STATUS_FOLLOW_TARGET = 1;  //已关注对方
  ENUM_FOLLOW_STATUS_UNFOLLOW_TARGET = 2; //未关注对方
}

message AddUserBlackListReq {
     BaseReq base_req = 1;
     uint32 active_uid = 2;   //拉黑方uid
     uint32 passive_uid = 3;  //被拉黑方uid
     string passive_account = 4; //被拉黑方account(若passive_uid=0则根据passive_account获取uid)
     uint32 add_black_source = 5; //拉黑来源;  see AddBlackSource
     uint32 follow_status = 6; // 拉黑时的关注状态; see FollowStatus
}

message AddUserBlackListResp {
     BaseResp base_resp = 1;
     GenericMember account_info = 2;  //被拉黑方用户信息
}

enum DelBlackSource {
  ENUM_DEL_BLACK_SOURCE_UNSPECIFIED = 0;
  ENUM_DEL_BLACK_SOURCE_FROM_BLACK_LIST = 1;  //从黑名单列表移除
}

message DelUserBlackListReq {
     BaseReq base_req = 1;
     uint32 active_uid = 2;   //拉黑方uid
     uint32 passive_uid = 3;  //被拉黑方uid
     uint32 del_black_source = 4; //移除黑名单来源;  see DelBlackSource
}

message DelUserBlackListResp {
     BaseResp base_resp = 1;
     GenericMember account_info = 2;  //被拉黑方用户信息
}

message CheckIsInBlackListReq {
     BaseReq base_req = 1;
     uint32 active_uid = 2;   //拉黑方uid
     uint32 passive_uid = 3;  //被拉黑方uid
}

message CheckIsInBlackListResp {
     BaseResp base_resp = 1;
     // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
     bool bIsIn = 2;               //是否被拉黑
}