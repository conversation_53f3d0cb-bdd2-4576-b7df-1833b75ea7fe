// Code generated by quicksilver-cli. DO NOT EDIT.
package risk_archiver

import(
	"golang.52tt.com/pkg/client"
	"google.golang.org/grpc"
	context "context"
	pb "golang.52tt.com/protocol/services/risk-archiver"
	protocol "golang.52tt.com/pkg/protocol"
)

type IClient interface {
	client.BaseClient
	GetIDCardPageStatus(ctx context.Context, in *pb.GetIDCardPageStatusReq) (*pb.GetIDCardPageStatusResp,protocol.ServerError)
	GetPhonePageStatus(ctx context.Context, in *pb.GetPhonePageStatusReq) (*pb.GetPhonePageStatusResp,protocol.ServerError)
	IsRiskIDCardInReason(ctx context.Context, idCard, encryptIdCard string) (bool,protocol.ServerError)
	IsRiskPhoneInReason(ctx context.Context, phone string) (bool,protocol.ServerError)
	UpdateRisk(ctx context.Context, in *pb.UpdateRiskReq) (*pb.UpdateRiskResp,protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
