package music_song_ktv

import (
	"context"
	"golang.52tt.com/pkg/client"
	pb "golang.52tt.com/protocol/services/rcmd/music_channel"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	GetKtvLikeSong(ctx context.Context, in *pb.MusicSongKtvReq) (out *pb.MusicSongKtvRsp, err error)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
