package smash_egg_notify

import (
	"context"
	"github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/smash-egg-notify"
	"google.golang.org/grpc"
)

const (
	serviceName = "smash-egg-notify"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewNotifyClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.NotifyClient { return c.Stub().(pb.NotifyClient) }

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(grpcClient.UnaryClientInterceptor, traceGRPC.TracedUnaryClientInterceptor(
		tracing.UsingTracer(t), tracing.LogPayloads(true),
	))
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) GetNotify(ctx context.Context, notifyType uint32) (*pb.GetNotifyResp, protocol.ServerError) {
	resp, err := c.typedStub().GetNotify(ctx, &pb.GetNotifyReq{NotifyType: pb.NotifyType(notifyType)})
	return resp, protocol.ToServerError(err)
}

func (c *Client) SetNotify(ctx context.Context, req *pb.SetNotifyReq) (*pb.SetNotifyResp, protocol.ServerError) {
	resp, err := c.typedStub().SetNotify(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetCurrentNotify(ctx context.Context, req *pb.GetCurrentNotifyReq) (*pb.GetCurrentNotifyResp, protocol.ServerError) {
	resp, err := c.typedStub().GetCurrentNotify(ctx, req)
	return resp, protocol.ToServerError(err)
}

// 转转中奖光效
func (c *Client) AddSmashLightEffects(ctx context.Context, req *pb.AddSmashLightEffectsReq) (*pb.AddSmashLightEffectsResp, error) {
	resp, err := c.typedStub().AddSmashLightEffects(ctx, req)
	return resp, protocol.ToServerError(err)

}

func (c *Client) UpdateSmashLightEffect(ctx context.Context, req *pb.UpdateSmashLightEffectReq) (*pb.UpdateSmashLightEffectResp, error) {
	resp, err := c.typedStub().UpdateSmashLightEffect(ctx, req)
	return resp, protocol.ToServerError(err)

}

func (c *Client) DelLightEffectByPackId(ctx context.Context, req *pb.DelLightEffectByPackIdReq) (*pb.DelLightEffectByPackIdResp, error) {
	resp, err := c.typedStub().DelLightEffectByPackId(ctx, req)
	return resp, protocol.ToServerError(err)

}

func (c *Client) GetAllSmashLightEffects(ctx context.Context, req *pb.GetAllSmashLightEffectsReq) (*pb.GetAllSmashLightEffectsResp, error) {
	resp, err := c.typedStub().GetAllSmashLightEffects(ctx, req)
	return resp, protocol.ToServerError(err)

}

func (c *Client) GetLightEffectInfoByPackId(ctx context.Context, packId uint32) (*pb.GetLightEffectInfoByPackIdResp, error) {
	resp, err := c.typedStub().GetLightEffectInfoByPackId(ctx, &pb.GetLightEffectInfoByPackIdReq{
		PackId: packId,
	})
	return resp, protocol.ToServerError(err)

}

func (c *Client) GetSmashActivityVisionConfigById(ctx context.Context, req *pb.GetSmashActivityVisionConfigByIdReq) (*pb.GetSmashActivityVisionConfigByIdResp, error) {
	resp, err := c.typedStub().GetSmashActivityVisionConfigById(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetAllSmashPropConfig(ctx context.Context, req *pb.GetAllSmashPropConfigReq) (*pb.GetAllSmashPropConfigResp, error) {
	resp, err := c.typedStub().GetAllSmashPropConfig(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetSmashPropConfigById(ctx context.Context, req *pb.GetSmashPropConfigByIdReq) (*pb.GetSmashPropConfigByIdResp, error) {
	resp, err := c.typedStub().GetSmashPropConfigById(ctx, req)
	return resp, protocol.ToServerError(err)
}
