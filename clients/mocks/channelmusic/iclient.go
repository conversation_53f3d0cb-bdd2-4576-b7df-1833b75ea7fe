// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/clients/channelmusic (interfaces: IClient)

// Package channelmusic is a generated GoMock package.
package channelmusic

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	channelmusic "golang.52tt.com/protocol/services/channelmusicsvr"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// BatGetChannelPlayingMusicInfo mocks base method.
func (m *MockIClient) BatGetChannelPlayingMusicInfo(arg0 context.Context, arg1 uint32, arg2 *channelmusic.BatGetChannelPlayingMusicInfoReq) (*channelmusic.BatGetChannelPlayingMusicInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatGetChannelPlayingMusicInfo", arg0, arg1, arg2)
	ret0, _ := ret[0].(*channelmusic.BatGetChannelPlayingMusicInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatGetChannelPlayingMusicInfo indicates an expected call of BatGetChannelPlayingMusicInfo.
func (mr *MockIClientMockRecorder) BatGetChannelPlayingMusicInfo(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatGetChannelPlayingMusicInfo", reflect.TypeOf((*MockIClient)(nil).BatGetChannelPlayingMusicInfo), arg0, arg1, arg2)
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// ChannelMusicCtrl mocks base method.
func (m *MockIClient) ChannelMusicCtrl(arg0 context.Context, arg1 uint32, arg2 *channelmusic.ChannelMusicCtrlReq) (*channelmusic.ChannelMusicCtrlResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ChannelMusicCtrl", arg0, arg1, arg2)
	ret0, _ := ret[0].(*channelmusic.ChannelMusicCtrlResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ChannelMusicCtrl indicates an expected call of ChannelMusicCtrl.
func (mr *MockIClientMockRecorder) ChannelMusicCtrl(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChannelMusicCtrl", reflect.TypeOf((*MockIClient)(nil).ChannelMusicCtrl), arg0, arg1, arg2)
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// GetChannelMusicList mocks base method.
func (m *MockIClient) GetChannelMusicList(arg0 context.Context, arg1 uint32, arg2 *channelmusic.GetChannelMusicListReq) (*channelmusic.GetChannelMusicListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelMusicList", arg0, arg1, arg2)
	ret0, _ := ret[0].(*channelmusic.GetChannelMusicListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelMusicList indicates an expected call of GetChannelMusicList.
func (mr *MockIClientMockRecorder) GetChannelMusicList(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelMusicList", reflect.TypeOf((*MockIClient)(nil).GetChannelMusicList), arg0, arg1, arg2)
}

// GetChannelMusicStatus mocks base method.
func (m *MockIClient) GetChannelMusicStatus(arg0 context.Context, arg1 uint32, arg2 *channelmusic.GetChannelMusicStatusReq) (*channelmusic.GetChannelMusicStatusResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelMusicStatus", arg0, arg1, arg2)
	ret0, _ := ret[0].(*channelmusic.GetChannelMusicStatusResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelMusicStatus indicates an expected call of GetChannelMusicStatus.
func (mr *MockIClientMockRecorder) GetChannelMusicStatus(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelMusicStatus", reflect.TypeOf((*MockIClient)(nil).GetChannelMusicStatus), arg0, arg1, arg2)
}

// RemoveChannelReportedMusic mocks base method.
func (m *MockIClient) RemoveChannelReportedMusic(arg0 context.Context, arg1, arg2, arg3 uint32, arg4, arg5 string) (int64, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveChannelReportedMusic", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// RemoveChannelReportedMusic indicates an expected call of RemoveChannelReportedMusic.
func (mr *MockIClientMockRecorder) RemoveChannelReportedMusic(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveChannelReportedMusic", reflect.TypeOf((*MockIClient)(nil).RemoveChannelReportedMusic), arg0, arg1, arg2, arg3, arg4, arg5)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}
