// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/clients/public-go (interfaces: IClient)

// Package public_go is a generated GoMock package.
package public_go

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	public_go "golang.52tt.com/protocol/services/public-go"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// BatchGetPublicAccountConfig mocks base method.
func (m *MockIClient) BatchGetPublicAccountConfig(arg0 context.Context, arg1 []uint32) ([]*public_go.PublicAccountConfig, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetPublicAccountConfig", arg0, arg1)
	ret0, _ := ret[0].([]*public_go.PublicAccountConfig)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchGetPublicAccountConfig indicates an expected call of BatchGetPublicAccountConfig.
func (mr *MockIClientMockRecorder) BatchGetPublicAccountConfig(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetPublicAccountConfig", reflect.TypeOf((*MockIClient)(nil).BatchGetPublicAccountConfig), arg0, arg1)
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// GetPublicAccount mocks base method.
func (m *MockIClient) GetPublicAccount(arg0 context.Context, arg1 uint32) (*public_go.StPublicAccount, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPublicAccount", arg0, arg1)
	ret0, _ := ret[0].(*public_go.StPublicAccount)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetPublicAccount indicates an expected call of GetPublicAccount.
func (mr *MockIClientMockRecorder) GetPublicAccount(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPublicAccount", reflect.TypeOf((*MockIClient)(nil).GetPublicAccount), arg0, arg1)
}

// GetPublicAccountAutoReply mocks base method.
func (m *MockIClient) GetPublicAccountAutoReply(arg0 context.Context, arg1 uint32) (string, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPublicAccountAutoReply", arg0, arg1)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetPublicAccountAutoReply indicates an expected call of GetPublicAccountAutoReply.
func (mr *MockIClientMockRecorder) GetPublicAccountAutoReply(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPublicAccountAutoReply", reflect.TypeOf((*MockIClient)(nil).GetPublicAccountAutoReply), arg0, arg1)
}

// GetPublicAccountByBindId mocks base method.
func (m *MockIClient) GetPublicAccountByBindId(arg0 context.Context, arg1 uint32, arg2 uint64) (*public_go.StPublicAccount, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPublicAccountByBindId", arg0, arg1, arg2)
	ret0, _ := ret[0].(*public_go.StPublicAccount)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetPublicAccountByBindId indicates an expected call of GetPublicAccountByBindId.
func (mr *MockIClientMockRecorder) GetPublicAccountByBindId(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPublicAccountByBindId", reflect.TypeOf((*MockIClient)(nil).GetPublicAccountByBindId), arg0, arg1, arg2)
}

// GetPublicAccountDefaultMessages mocks base method.
func (m *MockIClient) GetPublicAccountDefaultMessages(arg0 context.Context, arg1 uint32) ([]*public_go.PublicAccountDefaultMessage, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPublicAccountDefaultMessages", arg0, arg1)
	ret0, _ := ret[0].([]*public_go.PublicAccountDefaultMessage)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetPublicAccountDefaultMessages indicates an expected call of GetPublicAccountDefaultMessages.
func (mr *MockIClientMockRecorder) GetPublicAccountDefaultMessages(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPublicAccountDefaultMessages", reflect.TypeOf((*MockIClient)(nil).GetPublicAccountDefaultMessages), arg0, arg1)
}

// GetPublicAccountsByBindIdList mocks base method.
func (m *MockIClient) GetPublicAccountsByBindIdList(arg0 context.Context, arg1 uint32, arg2 []uint64) ([]*public_go.StPublicAccount, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPublicAccountsByBindIdList", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*public_go.StPublicAccount)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetPublicAccountsByBindIdList indicates an expected call of GetPublicAccountsByBindIdList.
func (mr *MockIClientMockRecorder) GetPublicAccountsByBindIdList(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPublicAccountsByBindIdList", reflect.TypeOf((*MockIClient)(nil).GetPublicAccountsByBindIdList), arg0, arg1, arg2)
}

// GetPublicAccountsByIdList mocks base method.
func (m *MockIClient) GetPublicAccountsByIdList(arg0 context.Context, arg1 []uint32) ([]*public_go.StPublicAccount, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPublicAccountsByIdList", arg0, arg1)
	ret0, _ := ret[0].([]*public_go.StPublicAccount)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetPublicAccountsByIdList indicates an expected call of GetPublicAccountsByIdList.
func (mr *MockIClientMockRecorder) GetPublicAccountsByIdList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPublicAccountsByIdList", reflect.TypeOf((*MockIClient)(nil).GetPublicAccountsByIdList), arg0, arg1)
}

// GetPublicAccountsByType mocks base method.
func (m *MockIClient) GetPublicAccountsByType(arg0 context.Context, arg1 uint32) (map[uint32]*public_go.StPublicAccount, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPublicAccountsByType", arg0, arg1)
	ret0, _ := ret[0].(map[uint32]*public_go.StPublicAccount)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPublicAccountsByType indicates an expected call of GetPublicAccountsByType.
func (mr *MockIClientMockRecorder) GetPublicAccountsByType(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPublicAccountsByType", reflect.TypeOf((*MockIClient)(nil).GetPublicAccountsByType), arg0, arg1)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}
