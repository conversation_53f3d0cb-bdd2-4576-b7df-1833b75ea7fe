package visit_record

import (
	"context"
	"golang.52tt.com/pkg/client"
	pb "golang.52tt.com/protocol/services/ugc/visit_record"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	AddVisitRecord(ctx context.Context, in *pb.AddVisitRecordReq) (*pb.AddVisitRecordResp, error)
	GetVisitRecord(ctx context.Context, in *pb.GetVisitRecordReq) (*pb.GetVisitRecordResp, error)
	GetUserFollowPost(ctx context.Context, in *pb.GetUserFollowPostReq) (*pb.GetUserFollowPostRsp, error)
	ReportUserFollowPost(ctx context.Context, in *pb.ReportUserFollowPostReq) (*pb.ReportUserFollowPostRsp, error)
	DelUserFollowPost(ctx context.Context, uid uint32, followType pb.FollowType) (*pb.DelUserFollowPostRsp, error)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
