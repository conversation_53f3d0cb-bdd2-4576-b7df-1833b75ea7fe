package geo_topic

import (
	"context"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/ugc/geo_topic"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	CreateGeoTopic(ctx context.Context, in *pb.CreateGeoTopicReq, opts ...grpc.CallOption) (*pb.CreateGeoTopicResp, protocol.ServerError)
	EnableGeoTopic(ctx context.Context, in *pb.EnableGeoTopicReq, opts ...grpc.CallOption) (*pb.EnableGeoTopicResp, protocol.ServerError)
	GetGeoTopicById(ctx context.Context, tid string, opts ...grpc.CallOption) (*pb.GeoTopicInfo, protocol.ServerError)
	GetGeoTopicByCode(ctx context.Context, cityCode string, opts ...grpc.CallOption) (*pb.GeoTopicInfo, protocol.ServerError)
	GetGeoTopicsByIds(ctx context.Context, ids []string, opts ...grpc.CallOption) (map[string]*pb.GeoTopicInfo, protocol.ServerError)
	UpdateGeoTopicPostCount(ctx context.Context, in *pb.UpdateGeoTopicPostCountReq, opts ...grpc.CallOption) protocol.ServerError
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
