package channel_open_game

import (
	"context"

	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/channel-open-game"
	"google.golang.org/grpc"
)

const (
	serviceName = "channel-open-game"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewChannelOpenGameClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.ChannelOpenGameClient { return c.Stub().(pb.ChannelOpenGameClient) }

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) GetSupportGameList(ctx context.Context, in *pb.GetSupportGameListReq) (*pb.GetSupportGameListResp, protocol.ServerError) {
	resp, err := c.typedStub().GetSupportGameList(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatchGetGameInfo(ctx context.Context, in *pb.BatchGetGameInfoReq) (*pb.BatchGetGameInfoResp, error) {
	resp, err := c.typedStub().BatchGetGameInfo(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SetChannelGame(ctx context.Context, in *pb.SetChannelGameReq) (*pb.SetChannelGameResp, protocol.ServerError) {
	resp, err := c.typedStub().SetChannelGame(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SetChannelGameSync(ctx context.Context, in *pb.SetChannelGameSyncReq) (*pb.SetChannelGameSyncResp, protocol.ServerError) {
	resp, err := c.typedStub().SetChannelGameSync(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SetChannelGameAsync(ctx context.Context, in *pb.SetChannelGameAsyncReq) (*pb.SetChannelGameAsyncResp, protocol.ServerError) {
	resp, err := c.typedStub().SetChannelGameAsync(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) UpdateChannelGame(ctx context.Context, in *pb.UpdateChannelGameReq) (*pb.UpdateChannelGameResp, protocol.ServerError) {
	resp, err := c.typedStub().UpdateChannelGame(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SetChannelGameMaster(ctx context.Context, in *pb.SetChannelGameMasterReq) (*pb.SetChannelGameMasterResp, protocol.ServerError) {
	resp, err := c.typedStub().SetChannelGameMaster(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SetChannelGameStatus(ctx context.Context, in *pb.SetChannelGameStatusReq) (*pb.SetChannelGameStatusResp, protocol.ServerError) {
	resp, err := c.typedStub().SetChannelGameStatus(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SetChannelGameMember(ctx context.Context, in *pb.SetChannelGameMemberReq) (*pb.SetChannelGameMemberResp, protocol.ServerError) {
	resp, err := c.typedStub().SetChannelGameMember(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetChannelGameInfo(ctx context.Context, in *pb.GetChannelGameInfoReq) (*pb.GetChannelGameInfoResp, protocol.ServerError) {
	resp, err := c.typedStub().GetChannelGameInfo(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetGameModeInfo(ctx context.Context, in *pb.GetGameModeInfoReq) (*pb.GetGameModeInfoResp, protocol.ServerError) {
	resp, err := c.typedStub().GetGameModeInfo(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SetChannelGameInfo(ctx context.Context, in *pb.SetChannelGameInfoReq) (*pb.SetChannelGameInfoResp, protocol.ServerError) {
	resp, err := c.typedStub().SetChannelGameInfo(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SetChannelGamePlayerOpenId(ctx context.Context, in *pb.SetChannelGamePlayerOpenIdReq) (*pb.SetChannelGamePlayerOpenIdResp, protocol.ServerError) {
	resp, err := c.typedStub().SetChannelGamePlayerOpenId(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetChannelGameBaseInfo(ctx context.Context, in *pb.GetChannelGameBaseInfoReq) (*pb.GetChannelGameBaseInfoResp, protocol.ServerError) {
	resp, err := c.typedStub().GetChannelGameBaseInfo(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetGameMaintain(ctx context.Context, in *pb.GetGameMaintainReq) (*pb.GetGameMaintainResp, protocol.ServerError) {
	resp, err := c.typedStub().GetGameMaintain(ctx, in)
	return resp, protocol.ToServerError(err)
}
