/**
 * Author: Orange
 * Date: 19-5-9
 */

package recommendation

import (
	"context"

	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	"google.golang.org/grpc"

	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	topic_channel "golang.52tt.com/protocol/services/topic_channel/recommendation"
)

const (
	serviceName = "topic-channel-recommendation"
)

type Client struct {
	client.BaseClient
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(
		grpcClient.UnaryClientInterceptor,
		traceGRPC.TracedUnaryClientInterceptor(
			tracing.UsingTracer(t), tracing.LogPayloads(true),
		),
	)
	dopts = append(dopts, grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName, func(cc *grpc.ClientConn) interface{} {
			return topic_channel.NewRecommendationClient(cc)
		}, dopts...),
	}, nil
}

func (c *Client) typedStub() topic_channel.RecommendationClient {
	return c.Stub().(topic_channel.RecommendationClient)
}

func (c *Client) AddRecommendation(ctx context.Context,
	in *topic_channel.AddRecommendationReq) (*topic_channel.AddRecommendationResp, error) {
	resp, err := c.typedStub().AddRecommendation(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) UpdateRecommendation(ctx context.Context,
	in *topic_channel.UpdateRecommendationReq) (*topic_channel.UpdateRecommendationResp, error) {
	resp, err := c.typedStub().UpdateRecommendation(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) RemoveRecommendation(ctx context.Context,
	in *topic_channel.RemoveRecommendationReq) (*topic_channel.RemoveRecommendationResp, error) {
	resp, err := c.typedStub().RemoveRecommendation(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetRecommendationList(ctx context.Context,
	in *topic_channel.GetRecommendationListReq) (*topic_channel.GetRecommendationListResp, error) {
	resp, err := c.typedStub().GetRecommendationList(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) PickRecommendations(ctx context.Context,
	in *topic_channel.PickRecommendationsReq) (*topic_channel.PickRecommendationsResp, error) {
	resp, err := c.typedStub().PickRecommendations(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) IsRecommendation(ctx context.Context,
	in *topic_channel.IsRecommendationReq) (*topic_channel.IsRecommendationResp, error) {
	resp, err := c.typedStub().IsRecommendation(ctx, in)
	return resp, protocol.ToServerError(err)
}


func (c *Client) UpdateRecommendationStatus(ctx context.Context,
	in *topic_channel.UpdateRecommendationStatusReq) (*topic_channel.UpdateRecommendationStatusResp, error) {
	resp, err := c.typedStub().UpdateRecommendationStatus(ctx, in)
	return resp, protocol.ToServerError(err)
}
