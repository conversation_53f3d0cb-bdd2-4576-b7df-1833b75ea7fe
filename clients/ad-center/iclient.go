package ad_center

import (
	"context"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/ad-center"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	BatchGetAd(ctx context.Context, in *pb.BatchGetAdReq) (*pb.BatchGetAdResp, protocol.ServerError)
	GetAd(ctx context.Context, in *pb.GetAdReq) (*pb.GetAdResp, protocol.ServerError)
	SetAd(ctx context.Context, in *pb.SetAdReq) (*pb.SetAdResp, protocol.ServerError)
	SearchAds(ctx context.Context, in *pb.SearchAdsReq) (*pb.SearchAdsResp, protocol.ServerError)
	GetCampaign(ctx context.Context, in *pb.GetCampaignReq) (*pb.GetCampaignResp, protocol.ServerError)
	SetCampaign(ctx context.Context, in *pb.SetCampaignReq) (*pb.SetCampaignResp, protocol.ServerError)
	SearchCampaigns(ctx context.Context, in *pb.SearchCampaignsReq) (*pb.SearchCampaignsResp, protocol.ServerError)
	GetFilter(ctx context.Context, in *pb.GetFilterReq) (*pb.GetFilterResp, protocol.ServerError)
	SetFilter(ctx context.Context, in *pb.SetFilterReq) (*pb.SetFilterResp, protocol.ServerError)
	SearchFilters(ctx context.Context, in *pb.SearchFiltersReq) (*pb.SearchFiltersResp, protocol.ServerError)
	CheckTagIdMate(ctx context.Context, in *pb.CheckTagIdMateReq) (*pb.CheckTagIdMateResp, protocol.ServerError)
	CheckABTest(ctx context.Context, tag string, uid uint32) (*pb.CheckABTestResp, protocol.ServerError)
	DiagnosisCampaign(ctx context.Context, in *pb.DiagnosisCampaignReq) (*pb.DiagnosisCampaignResp, protocol.ServerError)
	CommitAdExposure(ctx context.Context, in *pb.CommitAdExposureReq) (*pb.CommitAdExposureResp, protocol.ServerError)
	CommitAdClick(ctx context.Context, in *pb.CommitAdClickReq) (*pb.CommitAdClickResp, protocol.ServerError)
	SetABTestInfo(ctx context.Context, in *pb.SetABTestInfoReq) (*pb.SetABTestInfoResp, protocol.ServerError)
	GeneralMsg(ctx context.Context, in *pb.GeneralMsgReq) (*pb.GeneralMsgResp, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
