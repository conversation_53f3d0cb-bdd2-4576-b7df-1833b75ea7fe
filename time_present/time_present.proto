syntax = "proto3"; // 使用 proto3 语法
package ga.time_present; // 定义包名为 ga

import "ga_base.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/time_present";

// TimePresent 时间礼物列表里的ITEM
message TimePresent {
  uint32 id = 1; // 唯一id，仅作标志用
  uint32 item_id = 2; // 礼物id
  UserProfile from_user = 3; // 赠送者
  UserProfile to_user = 4; // 接收者
  uint64 begin_time = 5; // 开始时间
  uint64 end_time = 6; // 结束时间
  uint32 during_time = 7; // 持续时间，仅用于显示时间礼物队列中的效果时长
}

// IntimatePresent
message IntimatePresent {
  uint32 id = 1; // 唯一id，仅作标志用
  uint32 item_id = 2; // 礼物id
  UserProfile from_user = 3; // 赠送者
  UserProfile to_user = 4; // 接收者
  uint64 begin_time = 5; // 开始时间
  uint64 end_time = 6; // 结束时间
  uint32 during_time = 7; // 持续时间，仅用于显示时间礼物队列中的效果时长
}

// TimePresentList 时间礼物列表
message TimePresentList {
  repeated TimePresent items = 1; // 时间礼物列表
}

message GetTimePresentListReq{
  BaseReq base_req = 1;
  uint32 channel_id= 2;
}

message GetTimePresentListResp{
  BaseResp base_resp = 1;
  repeated TimePresent time_present = 2;  // 时间礼物列表
}

message TimePresentChangeChannelImMsg{
  TimePresentChangeChannelImMsgContent change_msg_sender = 1;  // 送礼方
  TimePresentChangeChannelImMsgContent change_msg_receiver = 2;  // 收礼方
  TimePresentChangeChannelImMsgContent change_msg_others = 3; // 其他人
  uint32 item_id =4 ; // 礼物id，用于跳转到礼物架对应礼物
}

message TimePresentChangeChannelImMsgContent{
  uint32 uid = 1; // 用户id
  string change_msg_text = 2; // 文本消息
  string change_msg_highlight = 3; // 高亮文本
}

message GetTimePresentOnShelfReq{
  BaseReq base_req = 1;
  uint32 channel_id = 2; // 房间id
  uint32 channel_scheme_layout_type = 3; // 房间类型 see channel-scheme_.proto SchemeLayoutType
}

message GetTimePresentOnShelfResp{
  BaseResp base_resp = 1;
  repeated TimePresentOnShelf time_present_list = 2;
}

message TimePresentOnShelf{
  uint32 present_id = 1;
}

message LiveIntimatePresentInfo {
  uint32 last_update_time = 1; // 直播间亲密礼物的最后更新时间
  repeated LiveIntimatePresentChannelInfo channel_info = 2; // 直播间亲密礼物的房间信息，用于把麦位替换成直播间亲密礼物的融合特效
  uint32 trigger_channel_id = 3; // 触发变动的房间id
}

// LiveIntimatePresentMsg 直播间亲密礼物消息
message LiveIntimatePresentMsg {
  enum IntimatePresentChangeType {
    INTIMATE_PRESENT_CHANGE_TYPE_UNSPECIFIED = 0; // 未定义
    INTIMATE_PRESENT_CHANGE_TYPE_NONE = 1; // 没有变化
    INTIMATE_PRESENT_CHANGE_TYPE_START = 2;  // 开始
    INTIMATE_PRESENT_CHANGE_TYPE_END = 3;  // 结束
  }
  repeated IntimatePresent items = 1; // 亲密礼物列表
  LiveIntimatePresentInfo live_intimate_present_info = 2; // 直播间当前的亲密礼物状态
  IntimatePresentChangeType change_type = 3; // 变化类型
}


message LiveIntimatePresentChannelInfo{
  uint32 channel_id = 1; // 对应的房间id，pk时作为替换的索引
  uint32 present_id = 2; // 礼物id
  UserProfile from_user = 3; // 赠送者
  UserProfile to_user = 4; // 接收者
  uint64 last_change_time = 5; // 该房间最后发生时间礼物变化的时间
}

// LiveIntimatePresentConfig 直播间亲密礼物配置
message LiveIntimatePresentConfig {
  // 礼物id
  uint32 present_id = 1;
  // 转场动画
  string switch_animation_url = 2;
  string switch_animation_md5 = 3;
  // 时间礼物的融合动画
  string related_animation_url = 4;
  string related_animation_md5 = 5;
  // 时间礼物归属静态图
  string related_static_url = 6;
  // 房间静态背景图
  string background_pic_static_url = 7;
  // 房间动态背景图
  string background_pic_dynamic_url = 8;
  string background_pic_dynamic_md5 = 9;
  // 麦位资源
  string mic_animation_url = 10;
  string mic_animation_md5 = 11;
  uint64 last_update_time = 12;
}

message GetChannelLiveIntimatePresentListReq{
  BaseReq base_req = 1;
  uint32 channel_id = 2;
}

message GetChannelLiveIntimatePresentListResp{
  BaseResp base_resp = 1;
  repeated IntimatePresent items = 2; // 亲密礼物列表
  LiveIntimatePresentInfo live_intimate_present_info = 3; // 直播间当前的亲密礼物状态
}

message GetLiveIntimatePresentConfigListReq{
  BaseReq base_req = 1;
  uint32 last_update_time = 2;
}

message GetLiveIntimatePresentConfigListResp{
  BaseResp base_resp = 1;
  repeated LiveIntimatePresentConfig live_intimate_present_config = 2;
  uint32 last_update_time = 3;
}

message GetLiveIntimatePresentOnShelfReq{
  BaseReq base_req = 1;
  uint32 channel_id = 2;
}

message IntimatePresentOnShelf{
  uint32 present_id = 1;
}

message GetLiveIntimatePresentOnShelfResp{
  BaseResp base_resp = 1;
  repeated IntimatePresentOnShelf intimate_present_list = 2;
}

message LiveIntimatePresentChangeChannelImMsg{
  LiveIntimatePresentChangeChannelImMsgContent change_msg_sender = 1;  // 送礼方
  LiveIntimatePresentChangeChannelImMsgContent change_msg_receiver = 2;  // 收礼方
  LiveIntimatePresentChangeChannelImMsgContent change_msg_others = 3; // 其他人
  uint32 item_id =4 ; // 礼物id，用于跳转到礼物架对应礼物
}

message LiveIntimatePresentChangeChannelImMsgContent{
  uint32 uid = 1; // 用户id
  string change_msg_text = 2; // 文本消息
  string change_msg_highlight = 3; // 高亮文本
}