syntax = "proto3";

package ga.smash_egg;

import "ga_base.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/smash-egg";

// 砸蛋方式
// buf:lint:ignore ENUM_PASCAL_CASE
enum SmashEgg_Source {
  Manual = 0;  //手动
  AUTO = 1;  //自动
}

// 砸蛋状态
// buf:lint:ignore ENUM_PASCAL_CASE
enum SmashEgg_Flag {
  NORMAL = 0;  //正常
  MORPH = 1;  //变身
}

// 砸蛋方式
// buf:lint:ignore ENUM_PASCAL_CASE
enum SmashEgg_Mode {
  NORMAL_MODE = 0;  //普通
  GOLD_MODE = 1;  //金色
}

// 平台
// buf:lint:ignore ENUM_PASCAL_CASE
enum SmashEgg_Platform {
  Unknown_Platform = 0;

  Android = 1;
  iOS = 2;
}

// 应用
// buf:lint:ignore ENUM_PASCAL_CASE
enum SmashEgg_App{
  Unknown_App = 0;

  TT = 1;        //ttvoice
  HUANYOU = 2;    //huanyou
  ZAIYA = 3;      //zaiya
}

//消费记录
// buf:lint:ignore MESSAGE_PASCAL_CASE
message SmashEgg_ConsumeRecord {
  uint64 id = 1;
  uint32 uid = 2;
  uint32 amount = 3;
  uint32 fee = 4;
  string order_id = 5;
  uint32 create_time = 6;
}

//中奖记录
// buf:lint:ignore MESSAGE_PASCAL_CASE
message SmashEgg_WinningRecord {
  uint64 id = 1;
  uint32 uid = 2;
  string nickname = 3;

  SmashEgg_Source source = 4;
  SmashEgg_Flag flag = 5;

  uint32 pack_id = 6;    // 奖品标识
  string pack_name = 7;  // 奖品名
  string pack_pic = 8;  // 奖品图
  uint32 pack_amount = 9;  // 奖品数量

  uint32 create_time = 10;

  string pack_desc = 11;  // 奖品描述

  uint32 notify_prefix = 12;  // notify_id, 为0时不需要展示中奖光效

  string notify_xxx = 13;      // 中奖提示语， 如 幸运碎片
  string notify_xxx_format = 14;  // 中奖提示语格式化字符串, 使用 中奖提示语 替换 “xxx”（小写）， 如 “xxx”66连！稀有麦位框坐骑唾手可得！没有“xxx”占位符时，直接展示本字段文案

  SmashEgg_Mode mode = 15;

  string notify_resource = 16;  // 小奖光效资源url
}


message SmashAwardInfo {
  uint32 id = 1;    // 奖品标识
  string name = 2;  // 奖品名
  string pic = 3;  // 奖品图
  uint32 amount = 4;  // 奖品数量

  enum GiftFlag {
    NORMAL = 0;
    SCARCE = 1;  // 稀缺
    BEST = 2; // 极品
  }
  GiftFlag gift_flag = 5; // 礼物标签，资源放在客户端
}

// buf:lint:ignore MESSAGE_PASCAL_CASE
message SmashEgg_WinningRecordV2 {
  uint64 id = 1;
  uint32 uid = 2;
  SmashEgg_Source source = 3;
  SmashEgg_Flag flag = 4;
  SmashEgg_Mode mode = 5;
  uint32 pack_id = 6;    // 奖品标识
  uint32 create_time = 7;
  repeated SmashAwardInfo award_list = 8;

  uint32 notify_prefix = 9;  // notify_id, 为0时不需要展示中奖光效

  string notify_xxx = 10;      // 中奖提示语， 如 幸运碎片
  string notify_xxx_format = 11;  // 中奖提示语格式化字符串, 使用 中奖提示语 替换 “xxx”（小写）， 如 “xxx”66连！稀有麦位框坐骑唾手可得！没有“xxx”占位符时，直接展示本字段文案
  string notify_resource = 12;    // 光效资源url(图片)
}

// buf:lint:ignore MESSAGE_PASCAL_CASE
message SmashEgg_GetConsumeRecordReq {
  ga.BaseReq base_req = 1;

  uint64 offset = 2;
  uint32 limit = 3;
  uint32 begin_time = 4;
  uint32 end_time = 5;
}

// buf:lint:ignore MESSAGE_PASCAL_CASE
message SmashEgg_GetConsumeRecordResp {
  ga.BaseResp base_resp = 1;

  repeated SmashEgg_ConsumeRecord consume_record_list = 2;
}

// buf:lint:ignore MESSAGE_PASCAL_CASE
message SmashEgg_GetRecentWinningRecordReq {
  ga.BaseReq base_req = 1;

  uint32 limit = 2;
  CurActivityType cur_activity_type = 3;    // 当前活动版本 A/B,see CurActivityType
}

// buf:lint:ignore MESSAGE_PASCAL_CASE
message SmashEgg_GetRecentWinningRecordResp {
  ga.BaseResp base_resp = 1;

  repeated SmashEgg_WinningRecord winning_record_list = 2;
}

// buf:lint:ignore MESSAGE_PASCAL_CASE
message SmashEgg_GetWinningRecordReq {
  ga.BaseReq base_req = 1;

  uint64 offset = 2;
  uint32 limit = 3;
  uint32 begin_time = 4;
  uint32 end_time = 5;
  bool rare = 6;  // 是否稀有礼物
  string page = 7;

  CurActivityType cur_activity_type = 8;    // 当前活动版本 A/B,see CurActivityType

  SmashEgg_Flag flag = 9;
  SmashEgg_Mode mode = 10;
}

// buf:lint:ignore MESSAGE_PASCAL_CASE
message SmashEgg_GetWinningRecordResp {
  ga.BaseResp base_resp = 1;

  repeated SmashEgg_WinningRecord winning_record_list = 2; // 废弃
  string page = 3;

  repeated SmashEgg_WinningRecordV2 winning_record_list_v2 = 4;
}

/*
	Recharge 充值

	amount 剩余转转次数
	balance 剩余T豆
*/

// buf:lint:ignore MESSAGE_PASCAL_CASE
message SmashEgg_RechargeReq {
  ga.BaseReq base_req = 1;

  uint32 amount = 2;

  uint32 channel_id = 3;
  uint32 channel_type = 4;

  SmashEgg_Platform platform = 5;

  CurActivityType cur_activity_type = 6;    // 当前活动版本 A/B,see CurActivityType
}

// buf:lint:ignore MESSAGE_PASCAL_CASE
message SmashEgg_RechargeResp {
  ga.BaseResp base_resp = 1;

  uint32 amount = 2;
  uint64 balance = 3;
}

/*
	Smash 转转

	remain_chance 剩余转转次数
	current_hits 当前全局转转次数
*/

// buf:lint:ignore MESSAGE_PASCAL_CASE
message SmashEgg_SmashReq {
  ga.BaseReq base_req = 1;

  uint32 amount = 2;

  uint32 channel_id = 3;
  uint32 channel_type = 4;

  SmashEgg_Source source = 5;
  SmashEgg_Platform platform = 6;
  SmashEgg_Mode mode = 7;

  CurActivityType cur_activity_type = 8;    // 当前活动版本 A/B,see CurActivityType

  SmashEgg_Flag morph_flag = 9; // 变身状态，与 mode 一起区分玩法模式，分为普通模式、普通变身模式、高级模式
}

// buf:lint:ignore MESSAGE_PASCAL_CASE
message SmashEgg_SmashResp {
  ga.BaseResp base_resp = 1;

  uint32 remain_chance = 2;

  repeated SmashEgg_WinningRecord winning_record_list = 3; // 废弃

  uint32 current_hits = 4;

  uint32 speed = 5; // 废弃
  uint32 overall_speed = 6; // 废弃

  // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
  bool isBingo = 7;

  uint32 guaranteed_val = 8; // 用户当前保底值
  uint32 overall_guaranteed_val = 9; // 配置最大保底值
  SmashEgg_WinningRecordV2 winning = 10; // 中奖信息
}

/*
	GetSmashStatus 获取当前转转状态

	current_hits 当前全局转转次数
	my_remain_hits 用户剩余次数
	my_today_hits 用户今日转转次数

	morph_flag 是否变身
	morph_end_time 变身结束时间戳
*/

// buf:lint:ignore MESSAGE_PASCAL_CASE
message SmashEgg_GetSmashStatusReq {
  ga.BaseReq base_req = 1;
  CurActivityType cur_activity_type = 2;    // 当前活动版本 A/B,see CurActivityType
}

// buf:lint:ignore MESSAGE_PASCAL_CASE
message SmashEgg_GuaranteedInfo {
  SmashEgg_Mode mode = 1;
  SmashEgg_Flag morph_flag = 2;
  uint32 guaranteed_val = 3; // 用户当前保底值
  uint32 overall_guaranteed_val = 4; // 配置最大保底值
}

// buf:lint:ignore MESSAGE_PASCAL_CASE
message SmashEgg_GetSmashStatusResp {
  ga.BaseResp base_resp = 1;

  uint32 current_hits = 2;

  uint32 my_remain_hits = 3;
  uint32 my_today_hits = 4;

  SmashEgg_Flag morph_flag = 5; // 废弃

  uint32 morph_end_time = 6;
  uint32 speed = 7; // 废弃
  uint32 overall_speed = 8; // 废弃

  repeated SmashEgg_GuaranteedInfo guaranteed_info_list = 9; // 各模式下的保底值
}

/*
	GetConfig 获取转转配置

	// 充值礼物 = 魔力球
	recharge_gift_id 充值礼物标识
	recharge_gift_type 充值礼物类型
	recharge_gift_price 充值礼物价格
	recharge_gift_name 充值礼物名
	recharge_gift_pic 充值礼物图片

	morph_hits 变身值，达到此值则变身
	morph_duration 变身持续时长(s)

	daily_limit 用户每日砸蛋次数上限

	is_show 用户是否能看见转转界面
	is_show_gold 转转是否可以切换金色模式
*/

// buf:lint:ignore MESSAGE_PASCAL_CASE
message SmashEgg_GetConfigReq {
  ga.BaseReq base_req = 1;

  SmashEgg_Platform platform = 2;
  uint32 channel_id = 3;
}

// buf:lint:ignore MESSAGE_PASCAL_CASE
message SmashEgg_GetConfigResp {
  ga.BaseResp base_resp = 1;

  uint32 recharge_gift_id = 2;
  uint32 recharge_gift_type = 3;
  uint32 recharge_gift_price = 4;
  string recharge_gift_name = 5;
  string recharge_gift_pic = 6;

  uint32 morph_hits = 7;      //变身击打次数，也就是变身值
  uint32 morph_duration = 8;    //变身持续时间

  uint32 daily_limit = 9;      //用户每日砸蛋次数上限

  bool is_show = 10; // 是否显示转转
  bool is_show_gold = 11; // 是否支持转转金色模式，在 is_show = true 时生效
}


enum CurActivityType{
  CUR_ACTIVITY_TYPE_UNSPECIFIED = 0;
  CUR_ACTIVITY_TYPE_A = 1;              // 活动A
  CUR_ACTIVITY_TYPE_B = 2;              // 活动B
}

// 客户端记录res_version,与每次获取的对应版本号比较，如果版本号不一致，则重新加载新的视觉资源
// 获取活动主题资源
// buf:lint:ignore MESSAGE_PASCAL_CASE
message SmashEgg_GetResourceConfigReq {
  ga.BaseReq base_req = 1;
}

// 各模式配置
// buf:lint:ignore MESSAGE_PASCAL_CASE
message SmashEgg_ModeCfg {
  SmashEgg_Mode mode = 1;
  SmashEgg_Flag morph_flag = 2;
  repeated uint32 recharge_prop_num_options = 3;  // 购买道具数量选项
  uint32 smash_cost_prop_num = 4; // 每次抽奖消耗道具数量
  string guaranteed_gift_pic = 5; // 保底奖励图标
}

// buf:lint:ignore MESSAGE_PASCAL_CASE
message SmashEgg_GetResourceConfigResp {
  ga.BaseResp base_resp = 1;

  CurActivityType cur_activity_type = 2;    // 当前活动版本 A/B,see CurActivityType
  uint32 res_version = 3;       // 资源版本号

  string entry_pic = 4;         // 入口资源，静态图片
  string entry_title = 5;       // 入口主题名称

  string activity_resource = 6;       // 玩法资源zip包
  string activity_resource_md5 = 7;   // 玩法资源zip包 md5
  string text_config = 8;             // 通用文案及字体色值配置。与前端约定json序列化字符串

  // 购买道具相关配置
  string recharge_gift_pic = 9;    // 购买红钻礼物pic
  string recharge_gift_name = 10;   // 红钻礼物名称
  string recharge_prop_pic = 11;   // 抽奖道具pic
  string recharge_prop_name = 12;  // 抽奖道具名称
  string recharge_reminder = 13;   // 购买提醒文案
  uint32 recharge_gift_price = 14; // 购买红钻礼物单价 （T豆）

  // 规则页资源, 资源后缀，客户端根据不同马甲包自行拼接
  string activity_rules = 15;     // 普通玩法规则
  string regular_prize_pool = 16; // 查看奖池-普通奖池
  string gold_prize_pool = 17;    // 查看奖池-高级奖池
  string morph_rules = 18;        // 变身模式规则说明
  string luck_point_rules = 19;   // 普通模式幸运值说明
  string activity_gold_rules = 20;// 金色玩法模式规则说明
  string gold_luck_point_rules = 21; // 金色幸运值说明

  repeated SmashEgg_ModeCfg mode_cfg_list = 22; // 各模式配置

  string morph_prize_pool = 23; // 查看奖池-变身奖池
  string morph_luck_point_rules = 24;   // 变身期间幸运值说明

}

// 道具时效
message PropTimeliness {
  uint32 num = 1;
  int64 fin_timestamp = 2; // 到期时间戳（秒级）
}

message GetUserPropListReq {
  ga.BaseReq base_req = 1;
  bool sort_asc = 2;    // 是否顺序排序
  CurActivityType cur_activity_type = 3;    // 当前活动版本 A/B,see CurActivityType
}

message GetUserPropListResp {
  ga.BaseResp base_resp = 1;
  repeated PropTimeliness prop_list = 2;
  uint32 total_prop_num = 3;    // 余额
}

message GetUserExpirePropNotifyReq {
  ga.BaseReq base_req = 1;
}

message GetUserExpirePropNotifyResp {
  ga.BaseResp base_resp = 1;
  bool need_notify = 2;        // 是否展示红点及浮层提醒
  string notify_content = 3;
}