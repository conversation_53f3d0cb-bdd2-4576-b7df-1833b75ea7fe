syntax = "proto3";

package ga.channel_guide_logic;

import "ga_base.proto";
import "game_card/game_card_.proto";

option go_package = "golang.52tt.com/protocol/app/channel-guide-logic";
option java_package = "com.yiyou.ga.model.proto";

message GetGangUpConfReq {
  ga.BaseReq base_req = 1;
  // required 游戏id
  uint32 game_id = 2;
}

// 游戏群组成员
message GameGroupMember {
  // 群成员account
  string account = 1;
}

// 游戏群组
message GameGroup {
  // 群id
  uint32 id = 1;
  // 群名称
  string name = 2;
  // 群头像
  string avatar = 3;
  // 群人数
  uint32 member_num = 4;
  // 用户是否已加入了群组
  bool user_joined = 5;
  // 加入群组是否需要验证
  uint32 need_verify = 7;

  repeated GameGroupMember member_list = 6;
}

message GetGangUpConfRsp {
  ga.BaseResp base_resp = 1;
  // 游戏群组
  repeated GameGroup game_group_list = 2;
  // 是否开启预约开黑
  bool enabled_booking = 3;
}

enum AppoinmentType {
  NoAppoinment = 0;  //没预约
  FirstAppoinment = 1;    //第一次预约
  SecondAppoinment = 2;   //第二次预约（选游戏卡）
}

message AppoinmentReq{
  ga.BaseReq base_req = 1;
  AppoinmentType appoinment_type = 2;    //预约类型
  repeated ga.game_card.GameCardOpt  opt_list = 3;   //游戏卡选项
  uint32 time_duration = 4;        //时长杪
  uint32 game_tab_id = 5;     //游戏id
  string game_tab_name = 6;  //游戏名称
  bool is_add = 7; //插入
}

message AppoinmentRsp{//若已存在会有已存在的err返回
  ga.BaseResp base_resp = 1;
  repeated int64 select_appoinment_time = 2;
  AppoinmentType appoinment_type = 3;    //预约类型
  int64 left_time = 4; //剩余时间
  uint32 tagid = 5; //游戏卡id
}

message GarTeamNotify{
  repeated string team_accounts = 1;  //车队候选组成员
  string game_icon = 2;  //游戏头像url，拿不到游戏卡时要用到
  string msg = 3;               //xxxx用户昵称，这是你预约的开黑车队，等你来加入
  string join_background_icon = 4;  //等待加入背景图
  uint32 game_tab_id = 5;//游戏id
  string game_tab_name = 6;//游戏名称
  // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
  string teamId = 7; //车队id
  bool is_game_card = 8; //是否有游戏卡
  uint32 game_back_color_num = 9; //背景色数值
  string channel_game_icon = 10;  //房间游戏头像
  uint32 wait_confirm_sec = 11; // 等待用户确认加入车队(秒)
  uint32 wait_enter_sec = 12; // 用户等待进房(秒)
}

message JoinCarTeamReq{
  ga.BaseReq base_req = 1;
  uint32 game_tab_id = 2; //游戏id
  string game_tab_name = 3;  //游戏名称
  // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
  string teamId = 4; //车队id
}

message JoinCarTeamRsp{
  ga.BaseResp base_resp = 1;
  JoinCarTeamNotify join_car_team_notify = 2;
}

enum UserOpeType {
  UserNone = 0;  //没有行为
  UserWait = 1;    //等待
  UserEnterRoom = 2;   //进房
  UserFail = 3; //失败
}

message JoinCarTeamNotify{// (返回或通知）
  uint32 game_tab_id = 1;    //游戏id
  string game_tab_name = 2;   //游戏名称
  uint32 uid = 3;  //返回的用户（前3个用户有可能直接下发告知）
  string msg = 4;  //告知用户加入情况
  uint32 channelid = 5; //加入的房间
  UserOpeType user_ope_type = 6; //弹出等待页或进房
  // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
  string teamId = 7; //车队id
}

enum AppStatus {
  AppNone = 0;   //none
  AppBackGround = 1; //后台
  AppAWait = 2;    //唤醒
}

message AppStatusReportReq{
  ga.BaseReq base_req = 1;
  AppStatus app_status = 2;
}

message AppStatusReportRsp{
  ga.BaseResp base_resp = 1;
}

message CloseGameGroupGuideReq {
  ga.BaseReq base_req = 1;
  uint32 tab_id = 2;
}

message CloseGameGroupGuideRsp {
  ga.BaseResp base_resp = 1;
}

message GetTeamFightGuideReq {
  ga.BaseReq base_req = 1;
  bool is_master = 2;
}

message GetTeamFightGuideRsp {
  ga.BaseResp base_resp = 1;
  uint32 newest_version = 2;
  repeated string urls = 3;
}