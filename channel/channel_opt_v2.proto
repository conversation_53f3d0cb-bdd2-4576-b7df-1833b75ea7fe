syntax = "proto3";

package ga.channel;

import "ga_base.proto";
import "channel/channel_opt_.proto";
import "revenue/revenue.proto";
import "muse_biz_integration_middlelayer_logic/muse_biz_integration_middlelayer_logic.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/channel";


// 进房扩展信息V2
message ChannelEnterOptV2
{
    uint32 member_cnt = 1;                // 房间当前成员数量
    bool is_robot = 2;                    // 是否是机器人进房
    int32 user_sex = 3;                   // 性别
    uint32 enter_source = 4;              // 进入房间类型  see ga::ChannelEnterReq::EChannelEnterSource 来源字段
    uint32 overflow_warning = 5;          // 人数负载告警, see channel_.proto EChannelOverflowWarningType
    bool is_newbie = 6;                   // 是否是新人
    bool is_fresh_register_user = 7;      // 是否是新注册用户

    // 在房间类型为 GUILD_HOME_CHANNEL_TYPE 的 公会主房间里面 才有(8，9, 10, 11)字段
    string guild_official_name = 8;       // 在公会内的职位名字(比如会长/副会长...)
    bool is_guild_channel_permission = 9; // 是否有公会的房间管理权限
    string guild_title_name = 10;         // 在公会内的角色的头衔(比如公会之花...)
    bool is_guild_member = 11;            // 是否是房间对应的公会的成员

    GenericMember follow_member = 12;     // 如果是跟随进房 这里填写跟随的用户信息
    string follow_info_msg = 13;          // 如果是跟随进房 这里填写跟随的描述语句 比如 "从附近的人跟随"

    ChannelEnterOptRevenue revenue_info = 20;
    ga.muse_biz_integration_middlelayer_logic.MtEnterChannelPublicScreenExtend mt_exend_info = 21; // mt进房扩展信息
}

// 进房扩展信息V2-营收
message ChannelEnterOptRevenue
{
    uint32 new_rich_level = 1;    // 用户的财富等级
    uint32 new_charm_level = 2;    // 用户的魅力等级
    RichAndCharmTopRank rich_charm_toprank = 3; // 用户的财富魅力榜 的排名
    ChannelMemberVipLevel vip_level = 4;      // 房间内VIP等级 定义在ga_base 中
    ga.channel.ChannelEnterSpecialEffect special_effect = 5;  // 进房特效
    NobilityInfo nobility_info = 6;                  // 贵族属性 6.49.0开始使用营收进房扩展信息revenue_info
    ChannelLiveFansInfo fans_info = 7;      //语音直播房粉丝信息
    uint32 super_player_level = 8;    // 用户的超级会员等级
    ga.channel.ChannelTailLightList tail_light_list = 9; // 尾灯列表
    bool is_year_member = 10;       //是否是年费会员
    ga.channel.KnightInfo knight_info = 11;
    UserUKWInfo ukw_info = 12;
    repeated NameplateDetailInfo revenue_nameplate = 13;
    ga.channel.ChannelUserNameplateList user_nameplate_list = 14; // 用户的铭牌信息
    ga.revenue.RevenueEnterChannelExtend revenue_info = 15; // 营收进房扩展信息 revenue.proto
}

message MicSizeChangeMicInfo {
    repeated ga.channel.SimpleMicrSpace all_mic_list = 1; // 当前的全量麦位列表
    int64 op_time_ms = 2; // 麦位操作时间,毫秒
}

// 麦位数变更推送
message ChannelMicSizeChangeEvent{
    uint32 cid = 1;
    uint32 op_uid = 2;
    uint32 cur_mic_size = 3; // 麦位数
    uint32 scheme_id = 4;    // 玩法id
    int64 ts = 5;            // 玩法的时间戳,注意跟麦位的时间戳区分开来,毫秒
    MicSizeChangeMicInfo mic_info = 6; // 麦位信息
}