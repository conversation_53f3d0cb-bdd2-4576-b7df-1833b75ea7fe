syntax = "proto2";

package ga.channel;

import "ga_base.proto";

// import "channel_.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/channel";

//-------------房间的可选消息---------------------//

enum RICH_CHARM_LEVEL_UPDATE_TYPE
{
  NO_LEVEL_UPDATE = 0;
  RICH_LEVEL_UPDATE = 1;   // 财富值等级变化
  CHARM_LEVEL_UPDATE = 2;  // 魅力值等级变化
}

message RichUpgradeAward
{
  required string award_name = 1;    // 财富等级升级的奖励列表
}


// 仅用于 CHANNEL_MEMBER_OPT_INFO_CHANGED 消息
message ChannelMemberOpt
{
  optional uint32 new_rich_level = 1;          // 用户的财富等级
  optional uint32 new_charm_level = 2;          // 用户的魅力等级
  optional uint32 rich_charm_level_update = 3;         // 用户的财富魅力值本次是否有触发升级 定义 RICH_CHARM_LEVEL_UPDATE_TYPE
  repeated RichUpgradeAward rich_upgrade_award_list = 4;    // 财富等级升级的奖励列表
}



// 进房间的可选数据字段
// 仅用于 CHANNEL_ENTER_MSG 消息
message ChannelEnterOpt
{
  optional uint32 new_rich_level = 1;    // 用户的财富等级
  optional uint32 new_charm_level = 2;    // 用户的魅力等级
  optional bool is_newbie = 3;    // 是否是新人
  optional uint32 member_cnt = 4;    // 房间当前成员数量
  optional RichAndCharmTopRank rich_charm_toprank = 5; // 用户的财富魅力榜 的排名

  optional GenericMember follow_member = 6;   // 如果是跟随进房 这里填写跟随的用户信息
  optional string follow_info_msg = 7;       // 如果是跟随进房 这里填写跟随的描述语句 比如 "从附近的人跟随"

  // 在房间类型为 GUILD_HOME_CHANNEL_TYPE 的 公会主房间里面 才有(8，9, 10, 11)字段
  optional string guild_official_name = 8;   // 在公会内的职位名字(比如会长/副会长...)
  optional bool is_guild_channel_permission = 9;   // 是否有公会的房间管理权限
  optional string guild_title_name = 10;  // 在公会内的角色的头衔(比如公会之花...)
  optional bool is_guild_member = 11;  // 是否是房间对应的公会的成员

  optional bool is_robot = 12;  // 是否是机器人进房

  // 等级
  optional ChannelMemberVipLevel vip_level = 13;      // 房间内VIP等级 定义在ga_base 中
  optional ChannelEnterSpecialEffect special_effect = 14;  // 进房特效

  optional bool is_fresh_register_user = 15;    // 是否是新注册用户

  optional int32 user_sex = 16;                              // 性别
  optional NobilityInfo nobility_info = 17;                  // 贵族属性
  optional ChannelLiveFansInfo fans_info = 18;      //语音直播房粉丝信息
  //optional bool visible_status = 18;  //true隐身
  optional uint32 enter_source = 19;//进入房间类型  see ga::ChannelEnterReq::EChannelEnterSource 来源字段
  optional uint32 super_player_level = 20;    // 用户的超级会员等级

  optional uint32 overflow_warning = 21; // 人数负载告警, see channel_.proto EChannelOverflowWarningType

  optional ChannelTailLightList tail_light_list = 22; // 尾灯列表

  optional bool is_year_member = 23;       //是否是年费会员

  optional ChannelUserNameplateList user_nameplate_list = 24; // 用户的铭牌信息

  optional KnightInfo knight_info = 25; //

  optional UserUKWInfo ukw_info = 26; //

  repeated NameplateDetailInfo revenue_nameplate = 27; // 营收线个人铭牌结构

  optional bytes revenue_extend_info = 28; // 营收进房扩展信息 revenue.proto RevenueEnterChannelExtend
}

// 骑士信息
message KnightInfo {
  optional string sceen_resource_url = 1; // 骑士进房 全屏特效资源url 优先级低于贵族神王特效(ChannelEnterOpt.NobilityInfo.level=9)
  optional string sceen_resource_url_md5 = 2;
  repeated uint32 knight_anchor_uids = 3; // 骑士守护的主播uid列表 最多不超过9个.
  optional EChannelMemberKnightLevelID knight_level = 4; // 骑士等级
}


enum ChannelEnterSpecialEffectType {
  EnterSpecialEffectTypeNormal = 0;            // 无调整, 牌匾在坐骑下方
  EnterSpecialEffectTypeLayerPlateTop = 1;     // 牌匾在坐骑上方
}

message ChannelEnterSpecialEffect {
  required string resource_id = 1;
  required string version = 2;
  optional string extend_json = 3; // 通用拓展json
  optional string custom_text = 4; // 自定义文案
  optional string fusion_ttid = 5; // 融合特效对方id
  optional ChannelEnterSpecialEffectType custom_type = 6; // 特效界面定制
}

// 仅用于 CHANNEL_EXIT_MSG 消息
message ChannelExitOpt
{
  optional uint32 member_cnt = 1;    // 房间当前成员数量
  optional uint32 member_cnt_factor = 2; //人数系数
}

enum ECHANNEL_MSG_SPC_EFFECT_TYPE
{
  ENUM_CHANNEL_MSG_SPC_EFFECT_NONE = 0;  // 没有特效
  ENUM_CHANNEL_MSG_SPC_EFFECT_FIREWORKS = 1;  // 烟花
}

message ChannelMsgSpcEffectResource
{
  optional uint32 resource_id = 1;
  optional string resource_url = 2;     // 消息特效 的资源包 中
  optional string resource_url_ios = 3;  // 消息特效 的资源包 中
  optional string resource_url_pc = 4;   // 消息特效 pc端
}

//消息样式资源
message ChannelMsgStyleResource
{
  //如果有值客户端优先使用这个值
  optional string msg_bg_resource_url = 1;
}

// 房间IM消息的可选数据字段
// 仅用于 CHANNEL_TEXT_MSG / CHANNEL_IMAGE_MSG / CHANEL_IM_EMOJI_MSG消息
message ChannelImOpt
{
  //
  optional uint32 new_rich_level = 1;    // 用户的财富等级
  optional uint32 new_charm_level = 2;    // 用户的魅力等级
  optional bool is_newbie = 3;    // 是否是新人

  // 在房间类型为GUILD_HOME_CHANNEL_TYPE的房间里面 才有(3,4,5)字段 同时其他字段(1,2)不填写
  optional string guild_official_name = 4;  // 在公会内的角色名字(比如会长/副会长...)
  optional bool is_guild_channel_permission = 5;  // 是否有公会的房间管理权限

  // 等级
  optional ChannelMemberVipLevel vip_level = 6;     // 房间内VIP等级 定义在ga_base 中

  // 消息特效
  optional uint32 msg_special_effect_type = 7;     // 消息特效类型 定义在 ECHANNEL_MSG_SPC_EFFECT_TYPE 中
  optional ChannelMsgSpcEffectResource msg_special_effect_resource = 8; // 特效资源包
  optional NobilityInfo nobility_info = 9; // 贵族等级信息 6.49.0开始使用扩展字段 business_ext_info
  optional ChannelLiveFansInfo fans_info = 10; //语音直播房粉丝信息
  optional uint32 super_player_level = 11;    // 用户的超级会员等级

  optional ChannelTailLightList tail_light_list = 12; // 发言尾灯

  optional ChannelUserNameplateList user_nameplate_list = 13; // 用户的铭牌信息

  optional bytes client_ext_context = 14;                    //客户端透传的字段(一般只放不是很重要的数据)，团战房马甲等(看ClientExtContextList)

  optional UserUKWInfo ukw_info = 15; // 神秘人信息

  optional ChannelMsgStyleResource msg_style_resource = 16; //消息样式信息

  optional bytes msg_at_context = 17; //房间@消息透传字段，富文本内容，格式与ugc_.proto -> PostInfo.content 一致

  repeated NameplateDetailInfo revenue_nameplate = 18; // 营收线个人铭牌结构

  repeated ChannelImBusinessExtInfo business_ext_info = 19;  //各业务团队自己的扩展信息

  optional bytes emoji_ext_info = 20; // 表情扩展信息, see ChannelEmojiMsgContent

  optional bytes im_reference = 21; // 消息引用信息, see channel_.proto ChannelImReference

  //客户端之前没对emoji_ext_info做不同消息类型处理，因此用不了这个字段做扩展，需要再重新新增一个字段
  optional bytes msg_ext = 22;// 扩展，消息类型不同结构不同, CHANEL_IM_SYS_SWIFT_EMOJI_MSG 对应ChannelImSysSwiftEmojiContent
}

enum ExtInfoBusinessType
{
  EXT_BUSINESS_TYPE_UNKNOWN = 0;
  EXT_BUSINESS_TYPE_REVENUE = 1;   //营收 see revenue.proto RevenueChannelImExtend
  EXT_BUSINESS_TYPE_MUSIC = 2;     //音乐
}

message ChannelImBusinessExtInfo {
  required ExtInfoBusinessType business_type = 1;
  required bytes ext_info = 2;
}

message ClientExtContextList {
  repeated ClientExtContext list = 1;
}
message ClientExtContext{
  required uint32 type = 1;    //1.团战房的马甲
  required bytes context = 2;
}

// cp麦位框附加内容
message CPHeadwareOpt
{
  optional string cp_headware_img = 1;    // cp麦位框图片url
  optional uint32 cp_uid = 2;    // cp对象uid
  optional string cp_headware_url = 3;    // cp麦位框完整url
  optional string cp_headware_notes = 4;    // cp麦位框备注
  optional string cp_headware_name = 5;    // cp麦位框CP名称（CP，闺蜜...）
  optional string cp_headware_connect_mic_img = 6;    // cp麦位框连麦资源
}

message SimpleMicrSpace
{
  required uint32 mic_id = 1;              // 麦位ID 1 - 9
  optional uint32 mic_state = 2;           // EMicrSpaceState 麦位状态 1 正常 2 禁用 3 不可发言
  optional uint32 uid = 3;
}

// 麦位操作 消息 可选数据字段
// 仅用于 麦位相关的消息
// EN_MIC_QUEUE 上麦消息 DE_MIC_QUEUE 下麦消息 CHANNEL_TAKE_USER_HOLD_MIC 被抱上麦 CHANNEL_KICKED_MIC 踢下麦 CHANNEL_MIC_ENTRY_ENABLE 解锁一个麦位 CHANNEL_MIC_ENTRY_DISABLE 锁定一个麦位
message ChannelMicOpt
{
  repeated SimpleMicrSpace all_mic_list = 1; // 当前的全量麦位列表
  optional uint32 op_mic_uid = 2;        // 涉及本次麦位操作的 用户的UID (比如上麦者，下麦者，被踢者，解锁麦位操作者，锁定麦位操作者)
  optional string op_mic_facemd5 = 3;    // 涉及本次麦位操作的 用户的头像md5 (仅在上麦时填写)
  optional uint32 op_micid = 4;          // 涉及本次麦位操作的 麦位ID
  optional uint64 op_time_ms = 5;        // 本次操作的服务器时间
  optional uint32 curr_micmode = 6;      // 当前房间的麦模式

  optional string op_mic_headware_key = 7;    // 涉及本次麦位操作的 用户的头像装饰框 key (仅在EN_MIC_QUEUE上麦 、 CHANNEL_TAKE_USER_HOLD_MIC被抱上麦 类型的消息时填写)
  optional int32 op_mic_sex = 8;          // 涉及本次麦位操作的 用户的性别(仅在EN_MIC_QUEUE上麦 、 CHANNEL_TAKE_USER_HOLD_MIC被抱上麦 类型的消息时填写)

  // 在房间类型为 GUILD_HOME_CHANNEL_TYPE 的 公会主房间里面 且仅在EN_MIC_QUEUE上麦  才有(9，10)字段
  optional string guild_official_name = 9;   // 在公会内的角色名字(比如会长/副会长...)
  optional bool is_guild_channel_permission = 10;  // 是否有公会的房间管理权限

  //官方认证信息
  optional string certify_title = 11;          //官方认证
  optional string certify_intro = 12;          //认证介绍

  // 等级
  optional ChannelMemberVipLevel vip_level = 13;     // 房间内VIP等级 定义在ga_base 中 (仅在EN_MIC_QUEUE上麦 、 CHANNEL_TAKE_USER_HOLD_MIC被抱上麦 类型的消息时填写)

  optional CPHeadwareOpt op_cp_headware_info = 14;  // 涉及本次麦位操作的 用户的cp头像装饰框信息(仅在EN_MIC_QUEUE上麦 、 CHANNEL_TAKE_USER_HOLD_MIC被抱上麦 类型的消息时填写)
  optional uint32 nobility_level = 15; //贵族等级信息

  optional string certify_style = 16;   // 官方认证样式
  optional string op_mic_headware_url = 17;    // 涉及本次麦位操作的 用户的头像装饰框 url

  optional UserChannelBoxInfo cur_box_info = 18;   //本次操作用户的包厢信息

  optional string v_style = 19; // 正在穿戴的 大v标识

  optional string certify_special_effect_icon_url = 20; // 大v特效url

  repeated uint32 knight_anchor_uids = 21; // 需要展示骑士麦位动画uid列表 包括骑士uid,骑士守护的主播uid
  /* 个人认证标识 */
  optional uint32 cert_type = 22; /* personal-certification.proto CertType */
  optional string icon = 23;
  optional string text = 24;
  repeated string color = 25;
  optional string text_shadow_color = 26; /* 文字阴影颜色 */
  /* 个人认证标识 */

  optional string knight_nameplate_resource_url = 27; // 骑士铭牌

  optional UserUKWInfo ukw_info = 28; // 神秘人信息

  optional ga.EChannelMemberKnightLevelID knight_level = 29; // 骑士等级

  // 麦位框的自定义文案
  optional string headwear_extend_json = 30; // 通用的扩展配置
  optional string headwear_custom_text = 31; // 自定义文案

  /* 个人认证标识 */
  optional ga.PersonalCertOptInfo cert_opt_info = 32;

  optional bytes revenue_mic_extern_data = 33 ;           //以后新增营收相关的数据都在该字段带给客户端 , see ga.RevenueMicExtendOpt

  optional ChannelAudioToken audio_token = 34 [deprecated = true];  // 已废弃，使用audio_token_data
  optional ga.ChannelAudioTokenV2 channel_audio_token = 35 [deprecated = true]; // 已废弃，使用audio_token_data
  optional bytes audio_token_data = 36; // 音频token序列化后数据，see channel-audio-token_.proto AudioToken
}

// 【已废弃】
message ChannelAudioToken {
  optional bytes token = 1;
  optional uint32 permission = 2;            // 位运算，参考channel-audio-token_.proto AudioTokenPermission
  optional uint32 effective_ttl = 3;         // 有效时长，秒
  optional uint32 renewal_ttl = 4;           // 客户端多少秒后开始过来续期
  repeated uint32 retry_interval_list = 5 ;  // 客户端调接口失败后,重试的次数以及间隔
}

// 麦位模式操作 消息 可选数据字段
message ChannelMicModeOpt
{
  optional uint32 mic_mode = 1;    // 当前房间的麦模式
  optional string desc = 2;        // 麦模式描述
  optional uint64 op_time_ms = 3;  // 本次操作的服务器毫秒时间
}

// 移动麦位操作 消息 可选数据字段
// 仅用于 移动麦位的消息
// CHANNEL_CHANGE_MIC_POS 移动麦位消息
message ChannelChangeMicPosOpt
{

  optional uint32 op_uid = 1;        // 移动麦位的用户的UID
  optional uint32 from_mic_id = 2;       // 从哪个麦位ID进行移动
  optional uint32 target_mic_id = 3;     // 移动的目标麦位ID
  optional uint32 target_mic_state = 4;  // 目标麦位的状态
  optional uint64 op_time_ms = 5;        // 本次操作的服务器时间
  optional uint32 nobility_level = 6;    // 贵族等级
}

// 房间配置变更消息操作 消息 可选数据字段
// 仅用于 房间配置变更消息
// CHANNEL_CONFIG_MODIFY_MSG 房间配置变更消息
message ChannelConfModifyOpt
{
  optional uint32 sub_mofdiy_type = 1;   // ChannelMsgSubType 房间名称 密码 麦模式 房间图标 房间描述 房间各种开关 等配置
  optional uint64 op_time_ms = 2;        // 本次操作的服务器时间
  optional bytes sub_pb_info = 3;
}

// buf:lint:ignore MESSAGE_PASCAL_CASE
message ChannelConfModifyOpt_Switch
{
  optional bool is_open = 1;
}
// buf:lint:ignore MESSAGE_PASCAL_CASE
message ChannelConfModifyOpt_Name
{
  optional string new_channelname = 1;
}
// buf:lint:ignore MESSAGE_PASCAL_CASE
message ChannelConfModifyOpt_Pwd
{
  optional bool is_have_pwd = 1;
  optional string pwd_desc = 2;     // 修改密码描述文本
}
// buf:lint:ignore MESSAGE_PASCAL_CASE
message ChannelConfModifyOpt_MicMode
{
  optional uint32 curr_micmode = 1;
  optional string micmode_desc = 2;          // 切模式描述文本
  repeated SimpleMicrSpace all_mic_list = 3; // 当前的全量麦位列表
}

// 头饰变化 消息 可选数据字段
// 仅用于 麦位头饰的消息 
// CHANNEL_USER_HEADWEAR_CHANGED 用户头饰变化 仅在用户佩戴或者取消时 用于广播给房间内的其他用户
message ChannelUserHeadwearOpt
{
  optional uint32 uid = 1;           // 涉及本次头像装饰的用户UID
  optional string headware_key = 2;  // 头像装饰的KEY 有值表示用户佩戴，没有值表示用户取消
  optional CPHeadwareOpt cp_headware_info = 3;// cp麦位头饰信息
  optional string headware_url = 4; // 头像装饰的url
  optional string headwear_extend_json = 5; // 通用的扩展配置
  optional string headwear_custom_text = 6; // 自定义文案

  optional HeadwearExtend headwear_extend = 7; // 麦位框的扩展字段
}

//答题服务 消息
//倒计时消息
message ChannelTriviaGameTimerOpt {
  optional uint32 end_timestamp = 1;
}

//播放动画消息
message ChannelTriviaGameAnimationOpt {
  optional uint32 anima_id = 1;    //动画id
  optional string text = 2;        //动画上显示的字符,如果有的话
}

//题目消息
message ChannelTriviaGameOption {
  optional string option = 1;    //选项内容
  optional uint32 option_num = 2;  //每个选项人数,公布答案时才有值
}

message ChannelTriviaGameQuestionOpt {
  optional uint32 question_id = 1;   //题目ID
  optional uint32 question_idx = 2;   //题目序号，第x题
  optional string question = 3;    //问题
  repeated ChannelTriviaGameOption option = 4;    //选项
  optional uint32 solution = 5;    //答案, 0表示提问状态,非0表示公布答案,1表示第1个选项...
}

//获奖名单
message ChannelTriviaGameWinner {
  optional uint32 uid = 1;
  optional string account = 2;
  optional string nick_name = 3;
  optional uint32 reward = 4; //获得的奖金,单位：分
}

message ChannelTriviaGameWinnerListOpt
{
  repeated ChannelTriviaGameWinner user_list = 1;  //只显示100人
  optional uint32 total_num = 2; //获奖总人数
}

//阶段变化
message ChannelTriviaGamePhaseUpdateOpt {
  optional uint32 act_id = 1;    //活动id
  optional uint32 phase_idx = 2;   //阶段序号
  optional bool is_show_answer = 3;   //是否展示答案
}



//
message ChannelLiveConnectMicApplyOpt
{
  optional bool is_cancel = 1;    // true 表示是取消申请 false 表示是申请
  optional uint32 ramin_apply_cnt = 2;  // 剩余的申请人数
  optional uint32 sex = 3;        // is_cancel = false 时填写 申请者的性别
  optional uint64 op_time_ms = 4;   // 本次操作的服务器时间
}
message ChannelLiveConnectMicHandleOpt
{
  optional bool is_allow = 1;    //ture 表示是通过申请 false表示是拒绝申请
  repeated uint32 uid_list = 2;    // 被操作的UID列表
  optional uint32 ramin_apply_cnt = 3;// 剩余的申请人数
}
message ChannelLiveConnectMicApplyExpireOpt
{
  optional uint32 remain_apply_cnt = 1;  // 剩余的申请人数
  repeated uint32 expire_uid_list = 2;  // 本次超时的用户列表
}

// 排麦变化事件
message ChannelQueueUpMicOpt
{
  enum EQueueUpMicOpType
  {
    ENUM_QUEUE_UP_MIC_APPLY = 1;        // 用户主动申请
    ENUM_QUEUE_UP_MIC_APPLY_CANCEL = 2; // 用户主动取消
    ENUM_QUEUE_UP_MIC_APPLY_KICK = 3;   // 管理员踢除
    ENUM_QUEUE_UP_MIC_APPLY_PASS = 4;   // 管理员同意
  };
  optional uint32 op_uid = 1;
  optional uint32 target_uid = 2;
  optional uint32 ramin_apply_cnt = 3;  // 剩余的申请人数
  optional uint64 op_time_ms = 4;       // 本次操作的服务器时间
  optional uint32 op_type = 5;        // EQueueUpMicOpType

  optional uint32 apply_user_sex = 6;    // 申请者的性别
  optional string apply_user_nick = 7;// 申请者的nickname
  optional string apply_user_account = 8;// 申请者的username
  optional string apply_user_facemd5 = 9;// 仅在用户主动发起申请时 这里才有值。表示用户头像的 MD5
}

// 公会公告
message ChannelGuildBulletinOpt
{
  optional uint32 guild_id = 1;        //
  optional string title_msg = 2;    //
  optional uint32 topic_id = 3;    //
  optional string content_msg = 4;   //
}


// 房间成员VIP等级
message ChannelMemberVipNotifyOpt
{
  optional ChannelMemberVipLevel vip_level = 1; // 定义在ga_base 中
  optional string msg = 2;                      // notify信息
  optional string member_face_md5 = 3;
}


// 文本消息opt CHANNEL_TEXT_SYS_MSG
message ChannelTextSysMsgOpt
{
  optional UserUKWInfo ukw_info = 1;
}

//----------------------channelPresentCount push def begin-----------------

// 给麦上用户送礼时推送 以及 用户上麦时
// type:CHANNEL_PRESNET_COUNT_CHANGE
message ChannelPresentCountNotifyMsg
{
  required uint32 uid = 1;
  required uint32 channel_id = 2;
  required uint32 price = 3;
  optional string bg_suffix = 4;  //背景后缀
  optional string ic_suffix = 5;  //icon后缀
  // 高档位动效资源
  optional UserProfile from_user = 6;
  optional string animate_url = 7;
  optional string animate_md5 = 8;
  optional string expire_ts = 9; //展示时间 可不填，不填则为默认
  optional string preview_pic = 10; // 弱网兜底预览图
}

// 房间送礼统计开关改变时推送
// type:CHANNEL_PRESENT_COUNT_OFF
message ChannelPresentCountStatusMsg
{
  required uint32 channel_id = 1;
  required bool state = 2;//false ,关闭统计；true 开启统计
  optional string style_prefix = 4; //默认样式前缀
  optional string bg_suffix = 5;  //默认样式
  optional string ic_suffix = 6;  //默认样式
  optional string uncompiled_prefix = 7; //未编译的后缀背景图前缀，暂时ios使用
}

//----------------------channelPresentCount push def end-----------------

message ChannelPopUpMsgOpt
{
  required uint32 uid = 1;
  required uint32 channel_id = 2;
  required string content_msg = 3;
}

message CreateTopicChannelMsg {
  required uint32 channel_id = 1;
  required string admin_msg = 2;  //成功在大厅创建房间后，需要在公屏处，该公屏信息仅房主、房管可见
  required string player_msg = 3;  //成功在大厅创建房间后，房间的其他人(非房主、房管)可以看到人的公屏
  required uint32 user_id = 4;  //创建者id
  required uint32 tab_id = 5;    //主题房的tab id
  required string tab_name = 6;  //主题房的tab 名
}

message HideTopicChannelMsg {
  required string content_msg = 1;  //不在大厅显示房间时推送，显示在公屏处， 房主可见
}

message ChannelPlayMicroGameTagChangeOpt
{
  required uint32 channel_id = 1;
  //repeated ChannelMicroUserGameTag game_tag_list=2;
  repeated bytes game_tag_list = 2;         // byte=ChannelPlayMicroGameTagChangeOpt.SerialzeAsString()(channel_.proto)
}

message ChannelPlayMicroGameTagOpt
{
  required uint32 channel_id = 1;
  required uint32 uid = 2;
  required string game_name = 3;
  //required ChannelMicroUserGameTag game_tag = 4;       //see usertag_.proto
  required bytes game_tag = 4;          // byte=ChannelPlayMicroGameTagChangeOpt.SerialzeAsString()(channel_.proto)
}

// 贵族信息变化相关，房间推送消息
message NobilityEventMsg {
  required uint32 uid = 1;
  optional NobilityInfo info = 2;
}

// 用于 channel_.proto ChannelMsgType::CHANNEL_PRESENT_POPUP_ACT_MSG 消息
message ChannelPresentActPopUpOpt {
  enum E_ACT_POPUP_TYPE {
    E_POPUP_TYPE_NONE = 0;
    E_POPUP_TYPE_VALENTINE_ACT = 1; // 2020白色情人节房间送礼弹窗类型
  }
  required uint32 act_type = 1; // E_ACT_POPUP_TYPE
  optional ChannelValentineActPopUpInfo valentine_info = 2;
}

message ChActPopupUserAccount {
  required uint32 uid = 1;
  optional string account = 2;
  optional string nick = 3;
  optional UserProfile user_profile = 4;
}

message ChannelValentineActPopUpInfo {
  required ChActPopupUserAccount from_user = 1;
  required ChActPopupUserAccount target_user = 2;
  required string lottie_url = 3; // Lottie 动效 url
  required string lottie_md5 = 4; // Lottie 动效 md5
  optional uint32 send_ts = 5;    // 时间戳
  optional uint32 expire_ts = 6;  // 展示时间 可不填，不填则为默认
  optional string text = 7;       // 文字显示 （送礼时间）
  optional string left_name_color = 8;
  optional string right_name_color = 9;
  optional string content_color = 10;
  optional string extend_json = 11;  // 额外的json结构，用来控制动画的某些参数
  optional string vap_url = 12; // vap 动效 url
  optional string vap_md5 = 13; // vap 动效 md5
}

message ChannelKickedOpt
{
  enum EChannelKickType
  {
    ENUM_CHANNEL_KICK_BY_ADMIN = 1;             // 被管理员踢
    ENUM_CHANNEL_KICK_BY_RULE = 2;              // 违规被踢
    ENUM_CHANNEL_KICK_BY_OTHER_TERMINAL = 3;    // 被其他终端踢
    ENUM_CHANNEL_KICK_BY_RESERVE = 4;           // 服务端预留类型，用于特殊场景，客户端必须用 kick_text 文案提示用户，文案为空不弹窗
  };
  required uint32 kick_type = 1;
  required string kick_text = 2;  // EChannelKickType
}

message ChannelHourRankSimpleInfo {
  required uint32 cid = 1;    // 房间id
  required uint32 rank = 2;   // 排名
  required uint32 score = 3;  // 分数
}

message ChannelHourRankListMsg {
  repeated ChannelHourRankSimpleInfo rank_list = 1;
  required uint32 tag_id = 2; // 榜单所属分类tag_id
  required uint32 push_interval_sec = 3; // 推送间隔时间周期*2
}

message MicGameNickMsg {
  required bool display_on_mic = 1;     //是否在麦上显示游戏昵称
}

message ChannelDressChangeNotifyMsg {
  required uint32 dress_type = 1;     //装扮类型
  required uint32 dress_id = 2;       //装扮id
  required uint32 channel_id = 3;     //房间ID
}

//带历史行为标签的用户进房推送标签
message EnterChannelToOwnerMsg {
  required uint32 channel_id = 1;
  required string owner_msg = 2;
  required uint32 player_uid = 3;
  required string player_name = 4;
  required string player_account = 5;
}


// 尾灯信息
message ChannelTailLightInfo {
  required uint32 id = 1; // 尾灯id
  required uint32 amount = 2; // 数量
}

// 尾灯列表
message ChannelTailLightList {
  repeated ChannelTailLightInfo tail_light_list = 1; // 尾灯信息
}


// 用户铭牌信息
message ChannelUserNameplateInfo {
  required uint32 id = 1; // 铭牌id
  required uint32 amount = 2; // 数量
  required uint32 type = 3; // 铭牌类型
  required string url = 4; // 铭牌资源url
}

// 用户铭牌列表
message ChannelUserNameplateList {
  repeated ChannelUserNameplateInfo user_nameplate_list = 1; // 用户的铭牌信息
  optional string knight_nameplate_resource_url = 2; // 骑士铭牌资源url
  optional string knight_channel_msg_bg_url = 3; // 骑士反应背景资源url
}


message ChannelHotValueNotifyMsg {
  required uint32 channel_id = 1;
  required uint32 hot_value = 2;
  required uint32 member_cnt_factor = 3; //人数系数//
}


//被踢下麦用户信息
message KickMicUserInfo{
  optional uint32 uid = 1;
  optional uint32 mic_id = 2;          // 涉及本次麦位操作的 麦位ID
  optional uint64 op_time_ms = 3;        // 本次操作的服务器时间
  optional string account = 4;
  optional string nickname = 5;
  optional int32 sex = 6;
}
message BatKickMicOpt{
  repeated SimpleMicrSpace all_mic_list = 1; // 当前的全量麦位列表
  optional uint32 mic_mode = 2;      // 当前房间的麦模式
  repeated KickMicUserInfo kick_mic_user_list = 3;   //被踢下麦用户列表
}

//自动退房push
message AutoExitChannelOpt {
  required uint32 cid = 1;
  required string exit_channel_text = 2;  //自动退房文案
}

message ChannelPunishAbuseOpt {
  optional bool with_punish = 1;    // 是否惩罚
  optional string content_msg = 2;
}

// 进房控制类型变更推送opt CHANNEL_CONTROL_TYPE_CHANGE_MSG
message ChannelControlTypeChangeMsgOpt {
  required uint32 control_type = 1;  // see channel_.proto -> EnterControlType
  optional string desc = 2;          // 描述
}

// 接新团推送协议
message UserReceptionGroupNotifyMsg {
  optional string account = 1; // 用户账号
  optional string nickname = 2;// 用户昵称
  optional string content = 3; // 只是内容文案  客户端：[用户昵称] + 该内容文案 = 最终展示
  optional uint32 uid = 4; // 用户id
}


// 赛事h5链接
message CompetitionLinks {
  optional string competition_button_link = 1; // 悬浮组件H5链接
  optional string competition_detail_link = 2; // 半屏页面H5链接
}

// ugc房间内赛事 悬浮组件消息推送（pb_opt_content）
message CompetitionButtonNotifyMsg {
  enum ShowStatus {
    CLOSE = 0;
    OPEN = 1;
  }
  optional ShowStatus show_status = 1; // show_status,
  map<uint32, CompetitionLinks> links = 2;  // key: market_id
}

// 房间内赛事 ugc房赛事公屏推送（pb_opt_content）
message ChannelCompetitionNotifyMsg {
  // 动作类型
  enum ActionType {
    NORMAL = 0; // 纯文本
    PULL_UP_H5_WEB = 1; // 唤起到h5半屏页面
    FOLLOW_USER = 2; // 关注用户
  }
  // ui样式类型
  enum UiType {
    NORMAL_TEXT_UI_TYPE = 0; // 普通纯文本通知
    JUMP_H5_WEB_UI_TYPE = 1; // 带h5跳转链接
    FOLLOW_USER_UI_TYPE = 2; // 带关注按钮
    NORMAL_TEXT_WITH_BG_UI_TYPE = 3; // 带背景色的普通纯文本通知
  }
  // 队友信息
  message  TeamMate {
    optional uint32 uid = 1;
    optional string account = 2;
    optional string nickname = 3;
  }

  optional uint32 action_type = 1; // 点击关注、查看跳转到h5等动作类型, 取值见 Action_Type
  optional uint32 ui_type = 2; // 消息内容样式，如普通、重要通知，取值见 Ui_Type
  optional string content = 3; // 消息文本内容
  optional string hightlight = 4; // 高亮内容 如昵称、去查看>
  optional uint32 follow_uid = 5; // 关注用户uid
  optional TeamMate teammate = 6; // 队友信息，消息中包含展示队友昵称
  // action_type=1 时拉起半屏的h5链接
  map<uint32, CompetitionLinks> links = 10;  // key: market_id
}


/* 社群聊天室公屏消息的完成任务，回复任务消息 */
message SocialCommunityChatChannelTaskReplyMsgOpt {
  optional string task_id = 1;    // 任务id
  optional uint32 uid = 2;
  optional string nickname = 3;
}

/* 社群聊天室公屏消息的任务消息 */
message SocialCommunityChatChannelTaskMsgOpt {
  optional string task_id = 1;    // 任务id
  optional string task_text = 2;
}

// 需要实名认证的通知
message NeedRealNameAuthNotification {
  // 需要实名的原因类型
  enum ReasonType {
    Unknown = 0;
    MeleeChannelMemberCapacity = 1; // 需要实名才能提高团战玩法人数上限
  }
  required ReasonType reason = 1; // 推送原因
  optional string title = 2;      // 通知标题
  optional string content = 3;    // 通知文案
}

//社群进房通知
message SocialCommunityEnterChannelMsg {
  optional uint32 uid = 1;
  optional uint32 channel_id = 2;
  optional SocialCommunityRole role = 3;//角色
  optional string nick_name = 4;//昵称
  optional string account = 5;//头像
  optional uint32 sex = 6;
  optional bytes mt_extend_info = 7; // mt进房扩展信息"muse_biz_integration_middlelayer_logic/muse_biz_integration_middlelayer_logic.proto" MtEnterChannelPublicScreenExtend;
}

//社群角色通知
message SocialCommunityUserRoleMsg {
  optional uint32 channel_id = 1;
  map<uint32, SocialCommunityRole> uid_role_map = 2;//角色
  optional string social_community_id=3;
}

message SocialCommunityRole{
  optional uint32 role = 1;//角色
  optional string role_text = 2;//角色名称
}

// 上麦引导推送协议
message UpperMicGuideMsg {
  enum UpperMicGuideType {
    UpperMicGuideType_PopUp = 0;    // 老用户、对照组展示弹窗
    UpperMicGuideType_VirtualAvatar = 1;    // 新用户实验组展示麦位虚拟头像
  }
  optional UpperMicGuideType upper_mic_guide_type = 1;    // 上麦引导的类型
}

// 用户未绑定手机号推送协议
message BindPhoneNotifyMsg {
  optional string account = 1;    // 用户账号
  optional string nickname = 2;    // 用户昵称
  optional string content = 3;    // 内容文案
  optional uint32 uid = 4 [deprecated = true];    // 已废弃，不需要用到该字段
  optional uint32 sex = 5;    // 用户性别
}

// 播放列表模式变更推送（战歌/网易云
message ChannelMusicListTypeChangeOpt {
  optional uint32 music_list_type = 1;  // see channel_.proto -> ChannelMusicListType，区分tt歌单及其他第三方渠道歌单
}