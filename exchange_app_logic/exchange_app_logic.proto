syntax = "proto3";

package ga.exchange_app_logic;

import "ga_base.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/exchange-app-logic";



// 获取用户当前积分余额，并判断是否弹窗
message GetUserScoreRequest{
   ga.BaseReq base_req = 1;
   uint32 score_type = 2;  //0礼物积分
}
message GetUserScoreResponse{
   ga.BaseResp base_resp = 1;
   uint32 score_1 = 2; //1类积分
   uint32 score_2 = 3;  //2类积分
   bool show = 4;  //是否弹窗
}

// 兑换积分为T豆
message BeginTransactionRequest{
   ga.BaseReq base_req = 1;
   uint32 score_type = 2;  //0礼物积分
   uint32 score_1 = 3; //1类积分
   uint32 score_2 = 4;  //2类积分
}

message BeginTransactionResponse{
   ga.BaseResp base_resp = 1;
}

