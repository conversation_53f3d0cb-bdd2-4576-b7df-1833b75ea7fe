syntax = "proto3";

/***************营收外部游戏开放通用logic*****************/

package ga.revenue_ext_game_logic;

import "ga_base.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/revenue-ext-game-logic";

// 外部游戏类型
enum ExtGameType {
    EXT_GAME_TYPE_UNSPECIFIED = 0;
    EXT_GAME_TYPE_CUTE_PETS_WAR = 1; // 萌宠宠之战
    EXT_GAME_TYPE_SHEEP_WOLVES_WAR = 2; // 羊羊抗狼
    EXT_GAME_TYPE_SIEGE_OF_CITY = 3; // 兵临城下
}

message CampButtonCfg {
    string button_color = 1;    // 按钮颜色
    string button_text = 2;     // 按钮文案
    string join_text = 3;       // 加入阵营发送的公屏文案
}

message ExtGameOpOpt {
    enum OptEvent {
        OPT_EVENT_UNSPECIFIED = 0;
        OPT_EVENT_GAME_BEGIN = 1;   // 一局游戏开始
        OPT_EVENT_GAME_END = 2;     // 一局游戏结束
    }

    uint32 event_type = 1;
    string round_id = 2;        // 游戏轮次id
    ExtGameOpCfg op_cfg = 3;
    int64 server_time = 4;      // 秒级时间戳
    uint32 game_type = 5;  // 小游戏类型 see ExtGameType
}

// 操作区域的配置
message ExtGameOpCfg {
    repeated CampButtonCfg camp_button_list = 1;    // 阵营加入按钮
    bool quick_gift_enable = 2;                     // 快捷送礼开关
}

// 游戏专属礼物
message ExtGameGiftCfg {
    uint32 gift_id = 1; // 礼物id
    string desc = 2;    // 礼物说明
}

message ExtGameCfg {
    uint32 game_type = 1; // 小游戏类型
    string name = 2;           // 小游戏名字
    string desc = 3;           // 游戏简介
    string pic_url = 4;        // 游戏图标
    string cms_url = 5;        // 规则链接
    repeated ExtGameGiftCfg gift_list = 6;  // 该游戏支持的礼物列表（在送礼栏展示）
    bool resource_display_enable = 7;       // 资源展示位（“活动进度挂件”+“活动资源位” ）,为true时展示
    uint32 rank_type = 8;              // 游戏榜单类型，对应GetExtGameScoreRankRequest中的RankType，rank_type字段
}

message ExtGameJoinCampOpt {
    uint32 uid = 1;
    uint32 channel_id = 2;
    uint32 game_type = 3;
    string user_camp = 4;       // 用户所加入阵营名字
    int64 join_time = 5;        // 加入时间
    string round_id = 6;        // 游戏轮次id
}

// 获取用户的游戏信息
message GetUserExtGameInfoRequest {
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2;     // 直播间id
}

message GetUserExtGameInfoResponse {
    ga.BaseResp base_resp = 1;
    ExtGameCfg game_conf = 2;   // 游戏基础配置
    ExtGameOpCfg op_conf = 3;   // 操作配置
    string user_camp = 4;       // 用户所加入的阵营，若为空则未加入（在加入阵营后，隐藏“加入阵营”按钮，再判断是否展示快捷送礼）
    string round_id = 5;        // 游戏轮次id
}

// 获取游戏配置列表
message GetExtGameCfgListRequest {
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2;     // 直播间id
    //repeated uint32 game_type_list = 3; // 要获取的小游戏列表
}

message GetExtGameCfgListResponse {
    ga.BaseResp base_resp = 1;
    repeated ExtGameCfg conf_list = 2;
}

// （pc端）启动小游戏挂载
message MountExtGameRequest {
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2;     // 直播间id
    uint32 game_type = 3; // 小游戏类型
}

message MountExtGameResponse {
    ga.BaseResp base_resp = 1;
    string serial = 2;         // 挂载成功时返回的启动口令参数
}

// （pc端）停止小游戏挂载
message UnmountExtGameRequest {
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2;
    uint32 game_type = 3; // 小游戏类型
}

message UnmountExtGameResponse {
    ga.BaseResp base_resp = 1;
}

// 用户点击“我想玩”上报
message ReportUserWantPlayRequest {
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2;
    uint32 game_type = 3; // 小游戏类型
}

message ReportUserWantPlayResponse {
    ga.BaseResp base_resp = 1;
}

message UserWantPlaySysMsgOpt{
    uint32 game_type = 1; // 小游戏类型
    string content = 2;        // 公屏文案
}

message GetChannelExtGameAccessRequest{
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2;    // 直播间channel_id
}

message GetChannelExtGameAccessResponse{
    ga.BaseResp base_resp = 1;
    bool have_access = 2;     // 是否展示入口
    StreamInfo stream_info = 3;
}

message StreamInfo {
    enum StreamType {
        STREAM_TYPE_UNSPECIFIED = 0;
        STREAM_TYPE_RTC = 1;
        STREAM_TYPE_L3 = 2;
        STREAM_TYPE_CDN = 3;
    }
    uint32 stream_type = 1; // 视频流类型 see StreamType
    string pull_stream_url = 2;
    string push_stream_url = 3;
}

// ===== 6.34.0 迭代增加游戏积分榜单 begin =================== 
message UserRankInfo{
    UserProfile user_profile = 1;    // 用户信息
    uint64 scores = 2;               // 积分
    uint32 rank = 3;                 // 排名 rank==0||scores==0 表示用户未上榜
    repeated string np_url_list = 4; // 荣誉铭牌资源列表
}

// 获取游戏榜单
message GetExtGameScoreRankRequest{
    ga.BaseReq base_req = 1;
    uint32 game_type = 2;  // 小游戏类型 see ExtGameType
    enum RankType {
        RANK_TYPE_UNSPECIFIED =0;
        RANK_TYPE_WORLD_RANK_SEVEN_DAYS = 1;    // 7日世界榜
    }
    uint32 rank_type = 3;  // 榜单类型，see RankType

    string page_token= 4;  // 分页token 第一页传空
    uint32 page_size = 5;  // 每页数量，默认为50条每页，最大不超过100条
}

message GetExtGameScoreRankResponse{
    ga.BaseResp base_resp = 1;
    repeated UserRankInfo rank_list = 2;
    UserRankInfo user_rank = 3;               // 吸底用户排名
    string next_page_token = 4;               // 服务端返回的token,为空则无需后续请求
    string rank_name = 5;                     // 榜单名称
    string rank_desc = 6;                     // 榜单说明，如："根据近xxx累计游戏积分排名，实时变动"; 其中"xxx" 为占位符，使用rank_desc_high_light替换
    string rank_desc_high_light = 7;          // 榜单说明高亮内容
    string rank_cms_url_suffix = 8;           // 榜单规则链接后缀，前缀域名由客户端根据马甲包去拼接
}

message RankNameplateInfo {
    string np_url = 1;           // 铭牌资源
}

message GetExtGameRankNameplateRequest{
    ga.BaseReq base_req = 1;
    enum ShowType {
        SHOW_TYPE_UNSPECIFIED = 0;
        SHOW_TYPE_CHANNEL_USER_CARD = 1;  // 房间资料卡(房间主播资料卡)
        SHOW_TYPE_PERSONAL_PAGE = 2;      // 个人主页
    }
    uint32 uid = 2;             // 指定用户uid
    uint32 show_type = 3;       // 铭牌展示位置，see ShowType
}

message GetExtGameRankNameplateResponse{
    ga.BaseResp base_resp = 1;
    repeated RankNameplateInfo info_list = 2;
}

// ===== 6.34.0 迭代增加游戏积分榜单 end ===================

// 游戏结算页
message ExtGameSettleOpt {
    uint32 channel_id = 1;
    string effect_url = 2;
    string effect_md5 = 3;
    string effect_json = 4;
}

// ============================== 6.61.0 第三方游戏接入 begin ==============================
// 获取openid
message GetExtGameOpenIdRequest{
    ga.BaseReq base_req = 1;
}

message GetExtGameOpenIdResponse{
    ga.BaseResp base_resp = 1;
    string openid = 2;
}

// 获取登陆js_code
message GetExtGameJsCodeRequest{
    ga.BaseReq base_req = 1;
    string channel_view_id = 2;
    string appid = 3;
}

message GetExtGameJsCodeResponse{
    ga.BaseResp base_resp = 1;
    string openid = 2;
    string js_code = 3;
}

// 获取房间内游戏列表
message GetExtGameAccessListRequest{
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2;
    uint32 channel_type = 3;
}

message ExtGameAccessInfo {
    string appid = 1;
    // 房间内弹窗
    string popup_url = 2;
    uint32 days_show_after_play = 3;   // 玩游戏后多少天再次展示
    uint32 days_show_after_ignore = 4; // 忽略后多少天再次展示

    // 右下角入口浮层+红点
    bool has_red = 5;        // 是否有红点
    string floating_url = 6; // 浮层提醒（有则展示，没有则不展示）
    bool show_in_more = 7;   // 更多中是否展示游戏入口
    string icon = 8;        // 游戏图标（更多中的游戏入口图标）
    string name = 9; // 游戏名称
}

message GetExtGameAccessListResponse{
    ga.BaseResp base_resp = 1;
    repeated ExtGameAccessInfo access_info = 8; //当前只有一个游戏，如果列表有值则，取第一个
}


// 获取随机的白名单房间
message GetExtGameWhiteChannelRequest{
    ga.BaseReq base_req = 1;
}

message GetExtGameWhiteChannelResponse{
    ga.BaseResp base_resp = 1;
    uint32 channel_id = 2;
}

// 判断房间是否可参与游戏
message CheckExtGameChannelRequest{
    ga.BaseReq base_req = 1;
    string channel_view_id = 2;  // 游戏方joinRoom接口传入的room_id（对应平台的channel_view_id）
}

message CheckExtGameChannelResponse{
    ga.BaseResp base_resp = 1;
    uint32 channel_id = 2; // 返回channel_view_id对应的channel_id
}

// 游戏包信息
message ExtGameInfo{
    string appid = 1; // 游戏标识
    string name = 2;    // 游戏名称
    string content = 3; // 更新描述
    string version = 4; // 游戏版本
    uint32 build = 5;   // 游戏build号
    string zip = 6; // zip包名称
    string h5_url = 7;  // 游戏地址
    string full_url = 8; // 全量资源URL地址,下载压缩包
    string md5 = 9;    // md5
    uint32 size = 10; // zip包大小
}

// 游戏信息不管有无权限的用户都返回，客户端提前下载
message GetExtGameInfoListRequest{
    ga.BaseReq base_req = 1;
}

message GetExtGameInfoListResponse{
    ga.BaseResp base_resp = 1;
    repeated ExtGameInfo game_list = 2; //当前只有一个游戏，如果列表有值则，取第一个
}

// deprecated
message GetExtGameChannelInfoByRoomIdRequest{
    ga.BaseReq base_req = 1;
    string channel_view_id = 2;  // 游戏方joinRoom接口传入的room_id（对应我们平台的channel_view_id）
}

// deprecated
message GetExtGameChannelInfoByRoomIdResponse{
    ga.BaseResp base_resp = 1;
    uint32 channel_id = 2; // channelId
}

message ExtGameLoginCheckRequest {
    ga.BaseReq base_req = 1;
    string channel_view_id = 2[deprecated=true];
    uint32 channel_id = 3; // 房间id
}

// 失败直接返回错误码
message ExtGameLoginCheckResponse {
    ga.BaseResp base_resp = 1;
}