syntax="proto2";

package ga.online;

import "ga_base.proto";

option java_package ="com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/online";

// 上报在线用户正在玩的游戏
message ReportPlayingGameReq{
	required BaseReq base_req = 1;
	required string pkg_name = 2;
	optional uint32 game_state = 3; // 1 玩游戏  0 没有玩游戏  （注意客户端没法赋值为0,客户端赋值为0就会导致该字段不存在）
}

message ReportPlayingGameResp{
	required BaseResp base_resp = 1;
}

message FriendsDetail {
	required uint32 uid = 1;
	required uint32 ol_status = 2;        // 0 offline 1 online
	required uint32 last_ol_time = 3;     // 用户状态为离线时 该值表示在线状态最后更新的时间  如果用户状态为在线时 该值无特殊意义 为服务器收到请求时的系统时间
	required string account = 4;

	optional uint32 room_id = 5;         // 开黑房间ID channelID
	optional string game_name = 6;       // 正在玩的游戏名
	optional uint32 room_type = 7;		 // 开黑房间类型, channel_.proto: enum ChannelType
	optional bool room_is_pwd = 8;       // 开黑房间是否有设置密码
	optional string nick_name = 9;

	optional string follow_label_img = 19; 	// 跟随好友头像标头jpg图
	optional string follow_label_text = 20; // 跟随好友头像标头文案

	optional string find_playing_text = 21; //找人玩
	optional string find_playing_img = 22; //找人玩


	optional Nobles noble_info = 30;
	optional bool is_live = 31; //是否在自己房开播中
	optional uint32 last_live_time = 32; //上次直播时间
	optional bool is_pking = 33; //是否在PK中

	optional bool is_performing = 34; // 是否有表演
	optional uint32 room_level = 35; // 房间等级
	optional uint32 follow_cnt = 36;//进房次数
	optional bool is_music_next_director = 37; //是否乐窝主理人

	/* 个人认证标识 */
	optional uint32 cert_type = 38; /* personal-certification.proto CertType */
	optional string icon = 39;
	optional string text = 40;
	repeated string color = 41;
	optional string text_shadow_color = 42; /* 文字阴影颜色 */
	/* 个人认证标识 */

	repeated string ring_color = 43; // 通用光圈颜色
	repeated string ring_color_extra = 44; // 特殊光圈颜色, 格式 = 客户端类型:颜色1,颜色2，如 pc:#A864FF,#A864FE

	// 个人铭牌
	repeated NameplateDetailInfo nameplate_detail_infos = 45;

	// 房间是否发布
	optional bool is_publishing =  46;
	optional uint32 sex =  47;
	optional string recent_intro = 48;//最近跟随介绍

	// 哪个 app 的房间 see ChannelAppType
	optional uint32 channel_app_type = 49;
	// 好友在线信息
	optional OnlineInfo online_info = 50;
}

// app 相关房间类型
enum ChannelAppType {
	// 未定义
	CHANNEL_APP_TYPE_UNSPECIFIED = 0;
	// TT 房间
	CHANNEL_APP_TYPE_TT = 1;
	// T 次元房间
	CHANNEL_APP_TYPE_TCY = 2;
}

//// NameplateType 用户铭牌资源类型
//enum NameplateType {
//	TYPE_ERROR = 0; // 错误类型
//	TYPE_LOTTER = 1;  // 资源类型lotter
//}
//
//message NameplateDetailInfo {
//	optional uint32 id = 1; // 铭牌id
//	optional string name = 2; // 铭牌名称
//	optional string base_url = 3; // 铭牌基础资源url
//	optional string dynamic_url = 4; // 铭牌动态资源的url
//	optional NameplateType type = 5; // 资源类型
//	optional uint32 start_time = 6; // 开始时间
//	optional uint32 finish_time = 7; // 到期时间
//	optional bool is_use = 8; // 是否穿戴
//}

//贵族相关字段
message Nobles {
	optional uint64 value = 1; // 贵族对应积分值
	optional uint64 keep_value = 2; // 保持贵族身份需要积分值
	optional uint32 level = 3; // 贵族等级
	optional uint32 cycle_ts = 4; //
}

// 获取用户的离线好友列表
message GetOfflineFriendsReq {
	required BaseReq base_req = 1;
}

message GetOfflineFriendsResp {
	required BaseResp base_resp = 1;
	repeated FriendsDetail offline_list = 2;
}

// 获取用户的在线好友列表
message GetOnlineFriendsReq {
	required BaseReq base_req = 1;
	optional bool include_following = 2;//包括单向关注的
	repeated uint32 ext_check_online_uidlist = 3; // 额外需要检查是否在线的UID列表
	optional bool enter_channel_cnt = 4;//包括单向关注的
	optional bool only_use_recent_invite = 5;//只使用最近列表
	optional bool is_main_page = 6;//只获取主页的信息
}

message GetOnlineFriendsResp {
	required BaseResp base_resp = 1;
	repeated FriendsDetail online_list = 2;

	repeated FriendsDetail ext_check_offline_list = 3; // 额外需要检查是否在线的UID列表里面已经离线的用户信息
}

// 获取指定群的在线人数信息
message GetGroupOnlineCountReq {
	required BaseReq base_req = 1;
	required uint32 group_id = 2;
}

message GetGroupOnlineCountResp {
	required BaseResp base_resp = 1;
	required uint32 group_id = 2;
	optional uint32 online_cnt = 3;
	optional uint32 offline_cnt = 4;
}

// 好友在线事件的推送消息
message OnlineEventPushMsg {

    enum OnlinePushType {
        OnlinePushTypeDefault = 0;
        OnlinePushTypeUnfollow=1;//取消关注 频道跟随
    }

	required uint32 uid = 1;
	required uint32 ol_status = 2;        	// 0 offline 1 online
	optional uint32 channel_id = 3;       	// 开黑房间ID channelID
	optional string game_name = 4;        	// 正在玩的游戏名
	optional uint32 room_type = 5;			// 开黑房间类型, channel_.proto: enum ChannelType

	optional uint32 channel_id_v2 = 8;	    // 欢城的直播房ID channelID V1 V2同时存在的情况下以V2为准
	optional uint32 room_type_v2 = 9;       // 房间类型, channel_.proto: enum ChannelType  V1 V2同时存在的情况下以V2为准

	optional bool channel_is_pwd = 10;      // 开黑房间是否有设置密码
	optional string account = 11;
	optional string nick_name =12;
	optional OnlinePushType push_type = 13;

	optional string find_playing_text = 14; //在玩啥文案
	optional string find_playing_img = 15;//在玩啥gif

	optional Nobles noble_info = 30;
	optional bool is_live = 31; //是否在自己房开播中
	optional uint32 last_live_time = 32; //上次直播时间
	optional bool is_pking = 33; //是否在PK中

	optional bool is_performing = 34; // 是否有表演
	optional uint32 room_level = 35; // 房间等级
	optional bool is_music_next_director = 36; //是否乐窝主理人
	/* 个人认证标识 */
	optional uint32 cert_type = 37; /* personal-certification.proto CertType */
	optional string icon = 38;
	optional string text = 39;
	repeated string color = 40;
	optional string text_shadow_color = 41; /* 文字阴影颜色 */
	/* 个人认证标识 */

	repeated string ring_color = 42; // 通用光圈颜色, 如：#A864FF
	repeated string ring_color_extra = 43; // 特殊光圈颜色, 格式 = 客户端类型:颜色列表，如 pc:#A864FF,#A864FE

	//个人铭牌
	repeated NameplateDetailInfo nameplate_detail_infos = 44;

	// 房间是否发布
	optional bool is_publishing =  45;
	optional uint32 sex =  46;

	// 哪个 app 的房间 see ChannelAppType
	optional uint32 channel_app_type = 47;
	// 好友在线信息
	optional OnlineInfo online_info = 48;
}

message OnlineInfo {
	// 马甲包 Id
	optional uint32 market_id = 1;
}

enum EFollowChannelAuthSwitchType {
	ENUM_FollowChannelAuth_CLOSE = 0;             // 关闭全部跟随
	ENUM_FollowChannelAuth_ALL_ALLOW = 1;         // 允许所有人跟随
	ENUM_FollowChannelAuth_ONLY_FRIEND_ALLOW = 2; // 只允许好友跟随
	ENUM_FollowChannelAuth_ONLY_FANS_ALLOW = 3;   // 只允许粉丝跟随 （包含了允许好友）
}

// 是否打开 跟随进房开关
message UpdateFollowChannelAuthReq {
	required BaseReq base_req = 1;
	required uint32 uid = 2;
	required bool follow_auth = 3;  // false表示关闭快速加入 (废弃的 过期字段)
	optional uint32 follow_auth_switch = 4;  // see EFollowChannelAuthSwitchType
}

message UpdateFollowChannelAuthResp {
	required BaseResp base_resp = 1;
	optional uint32 follow_auth_switch = 2;  // see EFollowChannelAuthSwitchType
}


message GetFollowChannelAuthReq {
	required BaseReq base_req = 1;
	required uint32 uid = 2;
}

message GetFollowChannelAuthResp {
	required BaseResp base_resp = 1;
	required bool follow_auth = 2;  // false表示关闭快速加入 (废弃的 过期字段)
	optional uint32 follow_auth_switch = 3;  // see EFollowChannelAuthSwitchType
}

//是否显示 输入邀请码
message CheckShowAddInviteCodeReq
{
	required BaseReq base_req = 1;
	required uint32 uid=2;
}
message CheckShowAddInviteCodeResp
{
	required BaseResp base_resp = 1;
	required bool is_show=2;
}

//邀请好友 邀请码验证
message CheckInviteFriendCodeReq
{
	required BaseReq base_req = 1;
	required uint32 uid=2;
	optional uint32 package_type=3;		//客户端包类型
	required string invite_code=4;		//邀请码
	optional string origin_channel=5;	//源包渠道
	optional string current_channel=6;	//安装包当前渠道号 ,
}


message CheckInviteFriendCodeResp
{
	required BaseResp base_resp = 1;
	required bool is_ok=2;				//邀请码验证结果
	required uint32 lives=3;
}

message OnlyTestxxxx{
	optional uint32 a = 1;
	optional uint32 b = 2;
}


// 用户当前的支持跟随的房间信息
message FollowChannelInfo {
    optional uint32 channel_id = 1;
    optional uint32 channel_type = 2;
    optional bool is_lock = 3;
    optional string find_playing_text = 4; // 在玩啥标签信息
    optional string find_playing_img = 5;  // 在玩啥标签的图标
	optional string find_playing_long_text = 6; // 在玩啥标签信息5.4版
	optional uint32 channel_level = 7; // 房间等级
	optional uint32 channel_app_type = 8; // 哪个 app 的房间 see ChannelAppType
}
// 获取当前用户对于指定用户的房间跟随信息
// 用于查看其他人详情页时 显示对方的房间跟随信息
message GetUserFollowChannelInfoReq {
	required BaseReq base_req = 1;
	required uint32 target_uid = 2;
}

message GetUserFollowChannelInfoResp {
	required BaseResp base_resp = 1;
	optional FollowChannelInfo follow_channel = 2;
}

message FollowLabelUpdate {
	required uint32 uid = 1;
	required string account = 2;
	required uint32 timestamp = 3;
	optional string follow_label_img = 4;  // 跟随好友头像标头jpg图
	optional string follow_label_text = 5; // 跟随好友头像标头文案
	optional string game_name = 6; 		   // 游戏名字
}

message GetChannelOnlineMemberCntReq {
	required BaseReq base_req = 1;
	optional uint32 channel_id = 2;
	optional uint32 channel_type = 3;
}

message GetChannelOnlineMemberCntResp {
	required BaseResp base_resp = 1;
	optional uint32 member_cnt = 2;
}