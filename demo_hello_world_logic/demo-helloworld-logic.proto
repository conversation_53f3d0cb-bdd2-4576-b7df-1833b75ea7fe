syntax = "proto3";

package ga.demo_hello_world_logic;
import "ga_base.proto";
option go_package = "golang.52tt.com/protocol/app/demo-helloworld-logic";
option java_package = "com.yiyou.ga.model.proto";

// buf:lint:ignore MESSAGE_PASCAL_CASE
message GA_AddAndEchoReq {
    ga.BaseReq base_req = 1;
    // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
    int64 iCnt = 2;
    // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
    int64 iAdd = 3;
}

// buf:lint:ignore MESSAGE_PASCAL_CASE
message GA_AddAndEchoResp {
    ga.BaseResp base_resp = 1;
    // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
    int64 iRetCnt = 2;
}

