syntax = "proto3";

package ga.channel_live_logic;

import "ga_base.proto";
import "time_present/time_present.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/channel-live-logic";

enum MissionStatus {
    Processing = 0; // 进行中
    Finish = 1;     // 完成
}

//直播任务相关
message ChannelLiveMissionInfo {
    enum MissionType {
        Unknown = 0;
        Daily = 1;          // 每日任务
        Accumulative = 2;   // 累计任务（一次性，完成后便结束）
        Continuation = 3;   // 连续任务（完成后重新开始）
    }

    // 点击‘去完成’的操作类型
    enum MissionOperType {
        None = 0;
        GiftRack = 1;           // 弹出礼物架
        ShareLiveChannel = 2;   // 分享直播间
        JumpFansGift = 3;       // 弹出礼物架粉丝团页
    }

    uint32 mission_id = 1;
    string mission_desc = 2;
    uint32 mission_type = 3;
    string mission_name = 4;
    string mission_icon_url = 5;
    uint32 status = 6;      // 任务状态 see MissionStatus
    uint32 finish_cnt = 7;  // 当前完成次数
    uint32 goal_cnt = 8;    // 完成次数的上限 （任务进度 = finish_cnt/goal_cnt）

    uint32 actor_uid = 9;   // 关注的主播的UID，仅粉丝任务有效
    uint32 oper_type = 10;  // 粉丝任务点击‘去完成’的操作，仅粉丝任务有效 see MissionOperType

    uint32 award_num = 11;  // （用户任务）奖励礼物个数 （粉丝任务）任务完成奖励的亲密值
    string progress_desc = 12; //（用户任务）任务缩略浮层页（轮播）的进度描述  （粉丝任务）进度描述
    /**漏了两个坑**/
    string progress_desc_detail = 15;   // （用户任务）任务详情浮层页中的进度描述

    string knight_desc = 16;  //(粉丝任务) 开通骑士团的任务描述，为空不用展示
    string bubble_desc = 17;  //(粉丝任务) 气泡描述，为空不用展示？
}

// 获取用户任务
message GetUserMissionListReq {
    ga.BaseReq base_req = 1;
}

message GetUserMissionListResp {
    ga.BaseResp base_resp = 1;
    repeated ChannelLiveMissionInfo mission_list = 2;
}

// 获取粉丝任务
message GetFansMissionListReq {
    ga.BaseReq base_req = 1;
    uint32 actor_uid = 2; // 关注的主播UID
    uint32 channel_id = 3;  //房间ID
}

message GetFansMissionListResp {
    ga.BaseResp base_resp = 1;
    repeated ChannelLiveMissionInfo mission_list = 2;
}

// 每隔一段时间处理一次用户任务（用于相关观看时长的任务）
message HandleUserMissionAtIntervalReq {
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2;
    bool only_query = 3;  // 仅获取任务信息，不处理任务（进房时使用）
}

message HandleUserMissionAtIntervalResp {
    ga.BaseResp base_resp = 1;
    repeated ChannelLiveMissionInfo user_mission_list = 2; // 用户任务列表（进行中的任务，即房间右上角任务缩略信息）
    uint32 next_req_interval_sec = 3; // 下一次请求间隔时间
}

// 每隔一段时间处理一次粉丝任务（用于相关观看时长的任务）
message HandleFansMissionAtIntervalReq {
    ga.BaseReq base_req = 1;
    uint32 actor_uid = 2; // 主播UID
    uint32 channel_id = 3;
    bool only_query = 4;  // 仅获取间隔时间，不处理任务（进房时或者刚刚成为粉丝时使用）
}

message HandleFansMissionAtIntervalResp {
    ga.BaseResp base_resp = 1;
    uint32 next_req_interval_sec = 2; // 下一次请求间隔时间
}

message HandleShareLiveChannelMissionReq {
    ga.BaseReq base_req = 1;
    uint32 actor_uid = 2; // 主播UID
    uint32 channel_id = 3;
}

message HandleShareLiveChannelMissionResp {
    ga.BaseResp base_resp = 1;
}

// 用户任务和粉丝任务完成后的推送结构
message MissionAwardOptMsg {
    enum Type {
        Unknown = 0;
        UserMission = 1;
        FansMission = 2;
    }

    uint32 type = 1;
    string text = 2;
    string icon_url = 3;
    uint32 award_num = 4; // 奖励数量
}


// 主播任务
message ActorMissionInfo {
    enum MissionType {
        UnknownMission = 0;
        DayIncomeMission = 1;      // 日流水任务
        DayTimeLengthMission = 2;  // 日时长任务
        WeekIncomeMission = 3;     // 周流水任务
        WeekTimeLengthMission = 4; // 周时长任务
        MonthIncomeMission = 5; // 月直播流水任务
    }

    string mission_name = 1;    // 任务名
    uint32 mission_level = 2;   // 任务等级
    string award_desc = 3;      // 奖励描述
    string award_desc_prefix = 4; // 奖励描述前缀
    uint32 mission_type = 5;    // 任务类型
    uint32 mission_status = 6;  // 任务状态（当子任务都完成时，状态才为完成）see MissionStatus
    repeated ActorSubMissionInfo sub_missions = 7; // 子任务
    bool  is_current_show = 8;  // 是否是当前展示任务
}

// 主播子任务
message ActorSubMissionInfo {
    string sub_name = 1;        // 子任务名
    string progress_desc = 2;   // 子任务进度描述
    uint32 status = 3;          // 子任务进度 see MissionStatus
}

// 获取主播任务
message GetActorMissionListReq {
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2;
}

message GetActorMissionListResp {
    ga.BaseResp base_resp = 1;
    repeated ActorMissionInfo mission_list = 2;
    string mission_desc = 3;  // 任务说明
}

// 进行中的任务相关描述
message ProcessActorMissionDesc {
    string progress_desc = 1;   // 任务进度描述
    string award_desc = 2;      // 任务奖励描述
}

// 获取进行中的主播任务浮层（客户端每隔一段时间调用一次）
message GetProcessActorMissionDescReq {
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2;
}

message GetProcessActorMissionDescResp {
    ga.BaseResp base_resp = 1;
    repeated ProcessActorMissionDesc process_mission_list = 2;
    uint32 next_req_interval_sec = 3; // 下一次请求间隔时间
}





//主播直播 & PK 相关
//搜索主播
message AnchorInfo {
    uint32 uid = 1;
    string account = 2;
    string nickname = 3;
    int32 sex = 4; //girl0  boy1
    uint32 channel_id = 5;
    uint32 channel_live_status = 6; //直播状态 EnumChannelLiveStatus
    uint32 channel_live_pk_status = 7; //PK状态 EnumChannelLivePKStatus
    string alias = 8;//

}

message SearchAnchorReq {
    ga.BaseReq base_req = 1;
    string account = 2;
}

message SearchAnchorResp{
    ga.BaseResp base_resp = 1;
    AnchorInfo anchor_info = 2;
}

//直播权限结构
message LiveChannelInfo{
    uint32 uid = 1; //
    uint32 channel_id = 2; //
    uint32 begin_time = 3; //直播权限开始时间
    uint32 end_time = 4;   //直播权限结束时间
    uint32 create_time = 5;
    string oper_name = 6; // 开通权限运营人员
    bool pk_authority = 7; //是否有PK权限
    bool has_virtual_per = 8;  // 是否有虚拟主播开播权限
}

//查询是否有直播权限
message GetLiveChannelInfoReq{
    ga.BaseReq base_req = 1;
    uint32 uid = 2;
}

message GetLiveChannelInfoResp{
    ga.BaseResp base_resp = 1;
    LiveChannelInfo channel_live_info = 2;
}


//设置直播状态
enum EnumChannelLiveStatus
{
    CLOSE = 0;
    OPEN  = 1;
    PAUSE = 2;
    CONTINUE = 3; //从暂停状态->继续状态
}

//PK阶段
enum EnumChannelLivePKStatus
{
    IDLE = 0;  //无状态
    BEGIN = 1; //开局阶段，可完成首杀
    TOOL = 2; //道具阶段
    LAST = 3; //最后一分钟
    PUNISH = 4; //惩罚阶段
    FINISH = 5; //结束阶段 双方主播依然连麦
}

//麦位上用户心跳
message ChannelLiveHeartbeatReq
{
    ga.BaseReq base_req = 1;
    uint32 uid = 2;
    string account = 3;
    string nick = 4;
    uint32 channel_id = 5;
    uint64 channel_live_id = 6;
    string channel_client_id = 7; // 音频流ID
    uint32 mic_id = 8; //麦位ID
}

message ChannelLiveHeartbeatResp
{
    ga.BaseResp base_resp = 1;
    int64 channel_live_id = 2;
}

//收到PK开始已经在麦位上的上报一次，之后有新用户上麦，或者端语音流变化上报
message ReportClientIDChangeReq {
    ga.BaseReq base_req = 1;
    uint32 uid = 2;
    uint32 channel_id = 3;
    uint32 mic_id = 4;
    uint64 channel_live_id = 5; //直播ID
    string client_id = 6; //语音流ID
    string channel_video_client_id = 7; // 视频流ID
}

message ReportClientIDChangeResp {
    ga.BaseResp base_resp = 1;
}

enum EnumPkMatch {
    PKM_Match_Close = 0;  //因为暂停直播等原因，匹配暂停
    PKM_Matching = 1; //正在匹配
}

// 屏幕类型
enum ScreenType {
  Screen_Type_Invalid = 0;  
  Screen_Type_Landscape = 1;  // 横屏
  Screen_Type_Portrait = 2;  // 竖屏
}

// 虚拟开播信息
message VirtualLiveInfo {
   uint32 screen_type = 1;  //see ScreenType
}

//直播状态信息
message ChannelLiveStatus {
    uint32 uid = 1;
    string account = 2;
    string nickname = 3;
    uint32 sex = 4; //0女，1男
    uint32 channel_id = 5;
    uint64 channel_live_id = 6; //用于区别同一个channel_id的不同场直播，由服务端产生
    // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
    repeated PkMicSpace micList = 7;
    EnumChannelLiveStatus status = 8; //直播状态
    EnumChannelLivePKStatus pk_status = 9; //PK状态
    uint32 begin_time = 10; //开播时间
    EnumPkMatch pk_match_state = 11; //匹配状态
    uint32 anchor_type = 12;  // 主播类型 see AnchorType
    VirtualLiveInfo virtual_live_info = 13;  // 虚拟开播信息
    ga.time_present.LiveIntimatePresentInfo time_present_info = 14; // 直播亲密礼物
}

message PkMicSpace {
    uint32 mic_id = 1;
    uint32 uid = 2;
    string account = 3;
    string nick = 4;
    string channel_client_id = 5; // 语音流ID
    int32 sex = 6;
    string alias = 7; //
    ga.UserProfile user_profile = 8;
    ga.UserUKWInfo ukw_info = 9;
    string channel_video_client_id = 10; // 视频流ID
}

//主播设置直播状态，开启直播，暂停，结束
message ChannelLiveInfo {
    ChannelLiveStatus channel_live_status = 1;
    ChannelLiveStatus other_channel_live_status = 2; //如果是PK中，对方的直播信息
    bool is_challenge = 3; //本人是否是挑战的一方
}

//结束直播数据统计
message ChannelLiveData{
    uint32 live_time = 1; //直播时长，秒单位 根据直播时间算
    uint32 audience_cnt = 2; //直播观众
    uint32 gift_value = 3; //礼物值
    uint32 send_gift_audience_cnt = 4; //送礼人数
    uint32 add_fans = 5; //新增fans
    uint32 add_group_fans = 6; //新增加粉丝团人数
    uint32 anchor_gift_value = 7; //主播收礼值
}

enum AnchorType {
    Anchor_Type_Common = 0;  //普通主播
    Anchor_Type_Virtual = 1; //虚拟主播
}

//主播设置直播状态
message SetChannelLiveStatusReq {
    ga.BaseReq base_req = 1;
    uint32 uid = 2;
    string account = 3;
    string nick = 4;
    uint32 sex = 5;
    uint32 channel_id = 6;
    uint64 channel_live_id = 7; //如果是开始直播可以不用填
    string channel_client_id = 8; // 音频流ID
    EnumChannelLiveStatus status = 9;
    uint32 anchor_type = 10;  //主播类型，see AnchorType
    VirtualLiveInfo virtual_live_info = 11;  // 虚拟开播信息
}

message SetChannelLiveStatusResp {
    ga.BaseResp base_resp = 1;
    int64 channel_live_id = 2;
    EnumChannelLiveStatus status = 3;
}

//进房取直播状态
message GetChannelLiveStatusReq {
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2;
    uint32 uid = 3; //主播UID
}

message GetChannelLiveStatusResp {
    ga.BaseResp base_resp = 1;
    ChannelLiveInfo channel_live_info = 2;
    ChannelLivePkScoreInfo channel_live_pk_info = 3;
    EnumChannelLivePKStatus status = 4;
    uint32 finish_time = 5; //PK结束时间 or 惩罚阶段结束时间
    MultiPkInfo multi_pk_info=6; // 多人PK信息
    MultiPkMatchInfo multi_pk_match_info=7; // 多人PK匹配信息
    repeated MultiPkKnightInfo multi_pk_knight_list = 8;  // 多人PK 骑士信息
    MultiPkSettleInfo multi_pk_settle_info = 9; // 多人PK 结算信息
}


//根据account列表查直播状态 
message BatchGetChannelLiveStatusByAccountReq {
    ga.BaseReq base_req = 1;
    repeated string accounts = 2;
}

message BatchGetChannelLiveStatusByAccountResp {
    ga.BaseResp base_resp = 1;
    repeated ChannelLiveInfo channel_live_info_list = 2; //包括是否PK中状态
}

/*PK相关*/
//PK申请
message ApplyPkReq
{
    ga.BaseReq base_req = 1;
    uint32 uid = 2; //发起申请的主播UID
    string account = 3;
    string nickname = 4;
    uint32 channel_id = 5;
    uint64 channel_live_id = 6; //
    uint32 target_uid = 7;
    uint32 target_channel_id = 8;
}

message ApplyPkResp
{
    ga.BaseResp base_resp = 1;
    uint32 target_uid = 2;
    uint32 target_channel_id = 3;
}

//取消PK申请
message CancelPKApplyReq
{
    ga.BaseReq base_req = 1;
    uint32 uid = 2; //发起申请主播UID
    uint32 target_uid = 3; //目标主播UID
}

message CancelPKApplyResp
{
    ga.BaseResp base_resp = 1;
}

enum EnumApply
{
    accept = 0;
    reject  = 1;
    delete = 2;
    cancel = 3; //取消PK申请
    apply = 4; //刚申请
}

//处理PK连麦申请
message HandlerApplyReq
{
    ga.BaseReq base_req = 1;
    uint32 uid = 2; //接受PK主播的uid
    uint32 channel_id = 3; //接受PK主播的channelID
    uint32 apply_uid = 4; //发起申请PK主播UID
    uint32 apply_channel_id = 5;//发起申请PK主播channelID
    EnumApply oper = 6;
}

message HandlerApplyResp
{
    ga.BaseResp base_resp = 1;
}

message GetApplyListReq{
   ga.BaseReq base_req = 1;
   uint32 uid = 2; //接受PK的直播UID
   uint32 channel_id = 3; //直播房间channelID
}

message Apply{
    uint32 apply_uid = 1;
    uint32 apply_channel_id = 2;
    uint32 apply_time = 3;
}

message GetApplyListResp{
   ga.BaseResp base_resp = 1;   // 错误用法，接口貌似没用到，todo del这个接口
   repeated Apply apply_list = 2;
}

//PK公共数据
message PkCommonInfo{
    uint32 begin_time = 1; //PK开始时间戳
    // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
    SendGiftUserInfo first_killUser = 2;
    EnumChannelLivePKStatus status = 3; //PK阶段
    bool is_extra_time = 4; //是否加时推送
    string pk_extra_time_rule = 5; //PK防偷塔玩法规则
    uint32 extra_left_time = 6; //剩余多少秒会触发加时
    bool is_open_extra_time = 7; //加时玩法是否开启
}

//PK完整信息
message PkInfo {
    PkUserInfo challenge_user = 1;
    ChannelPKSingleScore challenge_anchor = 2;

    PkUserInfo bechallenge_user = 3;
    ChannelPKSingleScore bechallenge_anchor = 4;
    PkCommonInfo common_info = 5;
}

//进房取PK数据接口
message GetPkInfoReq{
    ga.BaseReq base_req = 1;
    uint32 anchor_uid = 2; //主播UID
    uint32 channel_id = 3;
}

message GetPkInfoResp{
    ga.BaseResp base_resp = 1;
    PkInfo pk_info = 2;
}

//主播设置PK状态,用于结束惩罚状态后结束PK，逃跑也用这个接口
message SetPkStatusReq {
    ga.BaseReq base_req = 1;
    uint32 uid = 2;
    uint32 channel_id = 3;
    EnumChannelLivePKStatus status = 4;
}

message SetPkStatusResp {
    ga.BaseResp base_resp = 1;
}

//PK送礼列表
message GetChannelLivePkRankUserReq {
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2;
    uint32 off = 3;
    uint32 cnt = 4;
}

message GetChannelLivePkRankUserResp {
    ga.BaseResp base_resp = 1;
    repeated SendGiftUserInfo user_list = 2;
    uint32 channel_id = 3;
}

//主播PK记录
message ChannelLivePKRecord
{
    uint32 uid = 1; //对手UID
    string account = 2;
    string nick    = 3;
    uint32 channel_id = 4; //对手房间ID
    uint32 begin_time = 5; //开始直播时间戳
    uint32 live_time  = 6; //直播持续时间，S为单位，如果还没结束根据客户端5分钟一次计时算
    uint32 sex = 7; //0女，1男
}

//取pk记录
message GetChannelLivePKRecordReq{
    ga.BaseReq base_req = 1;
    uint32 uid = 2;
    uint32 chanenl_id = 3;
}

message GetChannelLivePKRecordResp{
    ga.BaseResp base_resp = 1;
    repeated ChannelLivePKRecord record_list = 2; //PK记录
    repeated ChannelLiveStatus status_list = 3; //PK记录中对方当前的直播状态，如果对方正在直播的话

}

message PkApplyPushMsg {
    EnumApply msg_ty = 1;
    uint32 uid = 2; //操作用户的UID，比如取消就是做取消操作用户的UID，如果是接受那就是接受用户的UID
    string account = 3; //对方的account
    string nickname = 4;
    int32 sex = 5;
    uint32 channel_id = 6;
}

//PK中，对方房间暂定，继续推送
message PkChannelLiveStatusPushMsg{
    uint32 channel_id = 1;
    EnumChannelLiveStatus status = 2;
    uint32 uid = 3; 
}

//PK分数变化广播消息
//message ChannelPkScorePushMsg
//{
//    ChannelLivePkScoreInfo both_score_info = 1;
//}

//PK分值信息
message ChannelLivePkScoreInfo
{
    ChannelPKSingleScore challenge_channel_score = 1;
    ChannelPKSingleScore be_challenge_channel_score = 2;
}

message PkUserInfo{
    uint32 uid = 1;
    string account = 2;
    string nickname = 3;
    uint32 channel_id = 4;
    EnumChannelLiveStatus status = 5;
    int32 sex = 6;
    string alias = 7;
    ChannelLiveOpponentMicFlag mic_flag = 8;
}

//PK阶段变化推送
message ChannelLivePkStatusPushMsg
{
    PkUserInfo challenge_user = 1; //主播信息
    ChannelPKSingleScore challenge_score = 2;//相关得分

    PkUserInfo bechallenge_user = 3;
    ChannelPKSingleScore bechallenge_score = 4;

    PkCommonInfo common_info = 5;

    SendGiftUserInfo send_gift_user = 6; //送礼人信息，触发PK加时使用
    ga.time_present.LiveIntimatePresentInfo time_present_info = 7; // 直播亲密礼物
}

//PK麦位信息变化推送
message PkMicInfoPushMsg {
    uint32 channel_id = 1;
    repeated PkMicSpace mic_list = 2; //PK麦位信息，通过心跳同步信息
}


//PK加时推送
message PkExtraTimePushMsg {
    // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
    bool IsExtra = 1; //两个人的PK值之和，是否满足加时条件
    string pk_extra_time_rule = 2; //PK防偷塔玩法规则
    uint32 extra_left_time = 3; //剩余多少秒会触发加时
}

//PK推送公共消息, 对应 channel_.proto CHANNEL_LIVE_COMMON_PUSH_MSG = 148;
message ChannelLivePkCommonPushMsg {
    enum PKCOMMONMSGTYPE {
        ENUM_UNKOWN = 0;
        ENUM_EXTRA_TIME = 1;       //  对应 PkExtraTimePushMsg
    }
    PKCOMMONMSGTYPE msg_type = 1;
    bytes msg_bin = 2; //消息序列化，具体消息结构见 enum PKCOMMONMSGTYPE
}

message ToolItem
{
    string item_id = 1; //道具ID
    int64 item_ns = 2; //对象唯一标识，可能有多个一样itemID的道具，使用item_ns区别
    bool be_used = 3; //是否已经使用了
}

//取道具列表接口
message GetMyToolListReq
{
    ga.BaseReq base_req = 1;
    uint32 uid = 2;
    uint32 channel_id = 3;
}

message GetMyToolListResp {
    ga.BaseResp base_resp = 1;
    repeated ToolItem items = 2;
}

enum ItemType {
    Invalid = 0;
    Effect_Type = 1; //氛围道具
    Score_Type = 2; //增加积分道具
    Percent_Type = 3; //按百分比添加道具
    First_Kill_Type = 4; //首杀
    Deduct_Type = 5; //氛围道具到达一定数量后减对方积分
}

message EffectItemMilestone{
    uint32 count = 1;   //数量
    uint32 percent = 2; //百分比 * 100
}

message ItemConfig{
    string item_id = 1;
    string desc = 2; //道具描述
    string icon = 3; //道具icon 资源链接
    string effect_url = 4; //触发方效果资源链接
    string target_effect_url = 5; //对手方效果链接
    string msg = 6; //触发方消息
    string target_msg = 7; //对手方消息
    string gain_msg = 8; //获得时的消息
    ItemType ty = 9; //道具类型
    uint32 value = 10; //道具数值
    string name = 11; //道具名
    uint32 version = 12; //版本号，用于标识最新版本的氛围道具
    repeated EffectItemMilestone milestone_list = 13;
}

message GetItemConfigReq{
    ga.BaseReq base_req = 1;
    repeated string item_id_list = 2; //取对应itemID的配置，如果空表示全部拿。客户端保存包已经取过的配置，只有在发现有客户端没有对应的道具item时才用这个接口
}

message GetItemConfigResp{
    ga.BaseResp base_resp = 1;
    repeated ItemConfig item_conf_list = 2;
    string pk_extra_time_rule = 3; //PK防偷塔玩法规则
    bool pk_extra_switch = 4; //PK防偷塔玩法是否开启
    uint32 extra_left_time = 5; //剩余多少秒会触发加时
    uint32 extra_total_score = 6; //PK双方总分多少满足触发加时
}

//使用道具推送
//道具效果
message SendGiftEffectPushMsg {
    SendGiftUserInfo send_user = 1;
    string item_id = 2;
    string text_msg = 3;
    string target_text_msg = 4;
    string url = 5; //送礼房资源URL道具资源链接
    string target_url = 6; //
    ItemType ty = 7; //道具类型
    int32 value = 8;// 道具数值，比如氛围道具到达一定数量后减对方积分
}

//获得道具推送
message GetGiftPushMsg {
    uint32 uid = 1;
    string account = 2;
    string item_id = 3;
    string nickname = 4;
    uint32 channel_id = 5;
    ga.UserProfile user_profile = 6;  
}

enum AppointPkEventType {
    InValid = 0;
    AppointPkInvite = 1;   // 指定pk邀约
    AppointPkAbstain = 2;  // 指定pk弃权
    AppointPkWin = 3;  // 指定pk胜利 
    AppointPkWaiting = 4;   //等待对方应战
}

message AppointPkEvent{
    uint32 event_type = 1;  //see AppointPkEventType
    uint32 my_uid = 2;
    uint32 other_uid = 3;
    string other_account = 4;
    string other_nickname = 5;
    uint32 count_down_end_time = 6; // 倒计时截止时间 秒
    int32  other_sex = 7;
    uint32 count_down_time = 8;  // 倒计时时长 秒
}

// 接受指定pk邀约
message AcceptAppointPkReq{
   ga.BaseReq base_req = 1;
   uint32 my_uid = 2;
   uint32 other_uid = 3;
   uint32 channel_id = 4;
}
message AcceptAppointPkResp{
   ga.BaseResp base_resp = 1;
}

// 确定收到指定pk推送
message ConfirmAppointPkPushReq{
   ga.BaseReq base_req = 1;
   uint32 my_uid = 2;
   uint32 other_uid = 3;
}
message ConfirmAppointPkPushResp{
   ga.BaseResp base_resp = 1;
}

// 主播进房获取指定pk相关信息
message GetAppointPkInfoReq{
   ga.BaseReq base_req = 1;
   uint32 uid = 2;
}
message GetAppointPkInfoResp{
   ga.BaseResp base_resp = 1;
   AppointPkEvent push_event_info = 2;
}

/*pk end*/

//直播送礼列表
message GetChannelLiveRankUserReq {
    ga.BaseReq base_req = 1;
    uint32 uid = 2; //PK双方的UID，不用区分UID和targetUID
    uint32 target_uid = 3; //PK双方的UID
}

message GetChannelLiveRankUserResp {
    ga.BaseResp base_resp = 1;
    repeated SendGiftUserInfo user_list = 2;
}

//直播观看时长列表
message GetChannelLiveWatchTimeRankUserReq {
    ga.BaseReq base_req = 1;
    uint32 uid = 2; //PK双方的UID，不用区分UID和targetUID
    uint32 target_uid = 3; //PK双方的UID
}

message GetChannelLiveWatchTimeRankUserResp {
    ga.BaseResp base_resp = 1;
    repeated WatchUserInfo user_list = 2;
}

//取本场直播数据统计
message GetChannelLiveDataReq{
    ga.BaseReq base_req = 1;
    uint32 uid = 2; //主播UID
    uint32 channel_id = 3;
}

message GetChannelLiveDataResp{
    ga.BaseResp base_resp = 1;
    ChannelLiveData live_data = 2; //数据统计
}

//直播房间状态广播消息，状态发生变化是房间广播，对应channel_.proto CHANNEL_LIVE_STATUS_MSG = 93; //语音直播房状态推送消息
message ChannelLiveStatusPushMsg
{
    ChannelLiveInfo channel_live_info = 1;
    repeated RecommendChannel channel_list = 2; //路人推荐
    repeated SendGiftUserInfo top_three_user = 3; //送礼前3
    ChannelLiveData live_data = 4; //结束状态才有这个数据
}

//直播数据变化推送
message ChannelLiveDataPushMsg
{
    ChannelLiveData live_data = 1;
}
message RecommendChannel{
    uint32 uid = 1; //主播UID
    string account = 2; //主播account
    uint32 channel_id = 3;
    uint32 tag_id = 4;
    string desc = 5;
    string channel_md5 = 6;
    uint32 rank = 7; // 小时榜排名
}

message SendGiftUserInfo
{
    uint32 uid = 1;
    string account = 2;
    uint32 score = 3;
    uint32 rich = 4;
    uint32 charm = 5;
    string nickname = 6;
    uint32 nobility_level = 7;//6.49.0客户端开始使用新的nobility_info字段
    uint32 group_fans_level = 8;
    uint32 channel_mem_level = 9; //房间等级
    string group_name = 10;   // 粉丝团名称
    ga.FansPlateInfo plate_info = 11;            // 粉丝铭牌信息
    uint32 sex = 12; //0女，1男
    bool first_kill = 13; //是否首杀
    uint32 channel_id = 14; //送礼用户所在房间ID
    ga.UserProfile user_profile = 15;  
    ga.UserUKWInfo ukw_info = 16; 
    ga.NobilityInfo nobility_info = 17;  // 贵族信息
}

message WatchUserInfo
{
    uint32 uid = 1;
    string account = 2;
    uint32 score = 3;
    uint32 rich = 4;
    uint32 charm = 5;
    string nickname = 6;
    uint32 nobility_level = 7; //6.49.0客户端开始使用新的nobility_info字段
    uint32 group_fans_level = 8;
    uint32 channel_mem_level = 9; //房间等级
    string group_name = 10;      // 粉丝团名称
    ga.FansPlateInfo plate_info = 11;            // 粉丝铭牌信息
    ga.NobilityInfo nobility_info = 12;  // 贵族信息
}

//PK分数变化广播消息
message ChannelPKSingleScore {
    uint32 channel_id = 1;
    uint32 score_value = 2;
    repeated SendGiftUserInfo top_score_user = 3; //前三
    SendGiftUserInfo use_item_top_user = 4; //使用道具之王
    repeated PkMicSpace mic_list = 5; //PK麦位信息，通过心跳同步信息
    uint32 effect_item_cnt = 6; //氛围道具数量
    bool first_kill = 7; //本次送礼是否触发了首杀
    SendGiftUserInfo first_kill_user = 8;  // 首杀用户信息
}

message GetAnchorInfoReq {
    ga.BaseReq base_req = 1;
    uint32 uid = 2;
    string account = 3;
}

message GetAnchorInfoResp{
    ga.BaseResp base_resp = 1;
    uint32 uid = 2;
    string acccount = 3;
    uint32 sex = 4;
    string nickname = 5;
    string officialcert = 6;
    string signature = 7;
    uint32 fans_group_cnt = 8;
    uint32 fans_cnt = 9;
}

//开始直播通知粉丝广播
message LiveStatusPushMsg {
    uint32 uid = 1;
    string account = 2;
    uint32 channel_id = 3;
    uint64 channel_live_id = 4;
    string nick = 5; //主播昵称
    string desc = 6; //开播描述
    repeated PkMicSpace mic_list = 7;//麦位框
    EnumChannelLiveStatus status = 8;//
    string sub_title= 9 ;//开播子标题
}

//直播房权限变化
message ChannelLivePermissionPush{
    uint32 anchor_uid = 1;
    uint32 channel_id = 2;
    uint32 ty = 3; //1开，0关
    uint32 pk_authority = 4; // 1开，0关
}

// 多人pk用户信息
message MultiPkUserKafkaInfo {
    uint32 uid = 1; 
    uint64 pk_score = 2; //pk贡献值
    bool first_kill = 3; // 首杀
}

// pk队伍房间kafka信息
message MultiPkRoomKafkaInfo {
   uint32 team_id = 1; // 队id
   uint32 uid = 2;  // 主播UID
   uint32 chanenl_id = 3; //房间id
   repeated MultiPkUserKafkaInfo gift_user_list = 4;  // 送礼用户列表
   uint32 pk_score = 5;  // pk火力值 
   uint32 pk_rank = 6;  // pk排名
   bool is_sponsor = 7;  // 是否发起者
   uint32 pk_result = 8; // pk结果 see MultiPkResult
}

// pk信息
message MultiPkKafkaInfo {
  uint32 pk_id = 1;
  uint32 pk_type = 2; // pk模式 see MultiPkType
  uint32 pk_status = 3;  // see EnumMultiPkStatus
  repeated MultiPkRoomKafkaInfo info_list = 4;  // pk队伍房间列表
  bool   is_auto_pk_type = 5;  //是否是系统指定的pk模式
  uint32 pk_team_type = 6;  //组队类型 see MultiPkTeamType 
}

enum ChannelLiveKafkaEventType {
    InvalidType = 0;
    ChannelLiveType = 1;
    ChannelLivePkType = 2;
    ChannelLiveMultiPkType = 3;  // 多人pk，兼容旧的事件
    ChannelLiveMultiPkNewType = 4;  // 多人pk新类型，对应 message MultiPkKafkaInfo
}

message ChannelLiveKafkaEvent{
    EnumChannelLiveStatus channel_live_status = 1;
    uint32 anchor_uid = 2;
    uint32 channel_id = 3;
    uint32 opp_anchor_uid = 4; //如果是PK，对手的UID
    uint32 opp_channel_id = 5; //如果是PK，对手的channelID
    EnumChannelLivePKStatus channel_pk_status = 6;
    uint32 win_uid = 7; //胜利一方的UID
    uint32 win_channel_id = 8; //胜利一方channelID
    ChannelLiveKafkaEventType ty = 9;
    repeated SendGiftUserInfo user_score_list = 10; //胜利一方的送礼用户列表
    ChannelLivePKMatchType match_model = 11; // 0普通模式 1随机PK
    uint32 anchor_pk_value = 12; // 主播PK总值
    uint32 opp_anchor_pk_value = 13; // PK对手PK总值
    repeated SendGiftUserInfo opp_user_score_list = 14; //失败一方的送礼用户列表
    int64 apply_id = 15;
    int64 match_type = 16; //PK匹配模式
    int64 channel_live_id = 17; //直播场次ID
    int64 create_time = 18; //kfk事件产生时间
    bytes kfk_msg_bin = 19; //ChannelLiveKafkaEventType 对应一个msg结构
    uint32 anchor_type = 20;  // 主播类型 see AnchorType
} 

message PkApplyKafkaEvent {
    EnumApply event_ty = 1;
    uint32 apply_uid = 2;
    uint32 target_uid = 3;
    int64  apply_id = 4; //PK申请ID
    uint32  apply_time = 5; //PK申请时间戳
}

//PK中，对面房间语音状态
enum ChannelLiveOpponentMicFlag {
    CPKMic_OPEN = 0; //开麦，能听到对面声音；PK开始的时候默认开启
    CPKMic_CLOSE = 1; //闭麦，不能听到对面声音
}

//主播设置PK中是否能听到对面语音
message SetChannelLiveOpponentMicFlagReq {
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2;
    ChannelLiveOpponentMicFlag opt_mic_flag = 3;
}

message SetChannelLiveOpponentMicFlagResp{
    ga.BaseResp base_resp = 1;
}

//主播设置PK中能否听到对方房间语音房间推送，所有人根据这个推送确定能否听到对面房间声音。包括主播自己
message ChannelLiveOpponentMicFlagPushMsg {
    ChannelLiveOpponentMicFlag opt_mic_flag = 1;
    uint32 channel_id = 2; //操作禁音的channelID
    uint32 be_ban_mic_channel_id = 3; //被禁音的channelID
}

enum ChannelLivePKMatchType{
    CPK_Match_Nornal = 0; //普通匹配
    CPK_Match_Rank = 1; //排位赛匹配
    CPK_Match_rand = 2; //随机匹配
    CPK_Match_Appoint = 3;  // 指定匹配
}

message StartPkMatchReq{
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2;
    ChannelLivePKMatchType match_type = 3;
    bool is_accept_multi_pk = 4; // [废弃] 是否接受多人PK邀请
}

message StartPkMatchResp{
    ga.BaseResp base_resp = 1;
}

message CancelPkMatchReq{
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2;
}

message CancelPkMatchResp{
    ga.BaseResp base_resp = 1;
    uint32 channel_id = 2;
}

//自己PK赛的阶段信息
message PkCompetitionInfo{
    uint32 level = 1;
    string my_level = 2;
}
//

message PkLimitInfo {
    //需要考虑没有配置次数限制的情况，这两字段都是"",客户端不显示限制提示？
    string time_range = 1; //14:00~20:00
    string limit_cnt = 2; //2次
    string ext_info = 3;//备用
}

//取PK匹配类型和自己PK赛阶段
message GetPKMatchInfoReq {
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2;
}

message GetPKMatchInfoResp {
    ga.BaseResp base_resp = 1;
    ChannelLivePKMatchType pk_match_ty = 2;
    PkCompetitionInfo cpt_info = 3;
    PkLimitInfo pk_limit_info = 4;
}

// 语音直播房粉丝团相关

//  语音直播房粉丝团相关 //
message FansRankInfo {
    uint32 uid = 1;
    string account = 2;
    string nickname = 3;
    int32  sex = 4;                            // 0 female  1 male
    NobilityInfo nobility_info = 5;            // 贵族信息
    ChannelMemberVipLevel vip_level_info = 6;  // vip等级信息
    uint32 rich_level = 7;                    // 财富等级
    uint32 love_value = 8;                     // 粉丝亲密度
    uint32 fans_level = 9;                    // 粉丝等级
    ga.FansPlateInfo plate_info = 10;            // 粉丝铭牌信息
    string group_name = 11;                   // 团名
}

enum EnumFansRankType {
    E_RANK_ACTIVE = 0;
    E_RANK_NEW = 1;
    E_RANK_INACTIVE = 2; // 不活跃粉丝，需维护
}

// 获取粉丝排行榜
message GetFansRankListReq {
    ga.BaseReq base_req = 1;
    uint32 anchor_uid = 2;
    uint32 rank_type = 3;   // see EnumFansRankType
    uint32 offset = 4;
    uint32 limit = 5;
    uint32 channel_id = 6;
}
message GetFansRankListResp {
    ga.BaseResp base_resp = 1;
    repeated FansRankInfo rank_list = 2;      // 排行榜
    uint32 next_offset = 3;        // 下一次分页偏移量
    uint32 active_fans_cnt = 4;    // 活跃粉丝数量
    uint32 inactive_fans_cnt = 5;  // 不活跃粉丝数量
    uint32 new_fans_cnt = 6;       // 新增粉丝数量
}

message FansInfo {
    uint32 uid = 1;
    bool   is_fans = 2;
    uint32 fans_level = 3;
    bool   is_valid = 4;      // 铭牌是否有效
    uint32 love_value = 5;    // 亲密值
    uint32 gap_next_value = 6;  //距离下一等级的亲密值
    uint32 next_level = 7;      // 下一个粉丝等级
    float  value_ratio = 8;      // 百分比
    ga.FansPlateInfo plate_info = 9;    // 粉丝铭牌信息
    string group_name = 10;             // 团名
}

// 获取粉丝信息
message GetFansInfoReq {
    ga.BaseReq base_req = 1;
    uint32 uid = 2;
    uint32 anchor_uid = 3;    // 主播uid
    uint32 cid = 4;   // 房间id
}

message GetFansInfoResp {
    ga.BaseResp base_resp = 1;
    FansInfo fans_info = 2;     // 粉丝信息
}

//粉丝推送信息
message FansNotifyOpt {
    enum EFANSNOTIFYTYPE {
        ENUM_UNKOWN = 0;
        ENUM_FANS_ADD = 1;       // 加入粉丝团
        ENUM_FANS_LEVEL_UP = 2;  // 粉丝升级
    }
    uint32 event_type = 1;    // see EFANSNOTIFYTYPE
    uint32 fans_level = 2;
    string group_name = 3;
    ga.FansPlateInfo plate_info = 4;     // 粉丝铭牌信息
}

//粉丝铭牌重新点亮推送信息
message FansPlatePushMsg {
    bool is_valid = 1;
}

// 获取主播粉丝团信息
message GetAnchorFansInfoReq {
    ga.BaseReq base_req = 1;
    uint32 anchor_uid = 2;
}

message GetAnchorFansInfoResp {
    ga.BaseResp base_resp = 1;
    uint32 fans_cnt = 2;              //粉丝数量
    uint32 item_id = 3;               // 加团礼物id
    ga.FansPlateInfo plate_info = 4;     // 粉丝铭牌信息
    string group_name = 5;                   // 团名

    ga.UserProfile lastweek_top1_user = 6; // 主播资料资料卡 上周top1
    repeated string lastweek_top3_account = 7; // 上周TOP3用户

    string plate_icon = 8;  // 专属铭牌图标
    string gift_icon = 9;  // 专属礼物图标
}

// 获取粉丝加入的粉丝团信息
message FansAddedGroupInfo {
    uint32 anchor_uid = 1;
    string account = 2;
    string nickname = 3;
    uint32 live_status = 4;  //直播状态 EnumChannelLiveStatus
    ga.FansPlateInfo plate_info = 5;   // 粉丝铭牌信息
    uint32 channel_id = 6;     // 房间id
    string group_name = 7;      // 团名
    uint32 fans_level = 8;      // 粉丝等级
    uint32 love_value = 9;   // 亲密值
}
message GetFansAddedGroupListReq{
    ga.BaseReq base_req = 1;
    uint32 fans_uid = 2;
    uint32 offset = 3;
    uint32 limit = 4;
}
message GetFansAddedGroupListResp{
    ga.BaseResp base_resp = 1;
    repeated FansAddedGroupInfo added_group_list = 2;      // 加入的粉丝团列表
    uint32  added_group_cnt = 3;   // 加入的粉丝团数量
    uint32 next_offset = 4;        // 下一次分页偏移量
}

// 主播设置粉丝团名称
message SetFansGroupNameReq {
    ga.BaseReq base_req = 1;
    uint32 anchor_uid = 2;
    string group_name = 3;
}
message SetFansGroupNameResp {
    ga.BaseResp base_resp = 1;
}

// 检测主播是否有设置粉丝团名称权限
message CheckSetGroupNamePermitReq {
    ga.BaseReq base_req = 1;
    uint32 anchor_uid = 2;
}
message CheckSetGroupNamePermitResp {
    ga.BaseResp base_resp = 1;
    uint32 fans_cnt = 2;    //粉丝数量
    uint32 item_id = 3;     // 加团礼物id
    string desc = 4;  // 设置团名说明文案
}

// 检测用户是否是主播粉丝
message CheckUserIsFansReq {
   ga.BaseReq base_req = 1;
   uint32 uid = 2;
   uint32 anchor_uid = 3;  // 主播uid
}
message CheckUserIsFansResp {
   ga.BaseResp base_resp = 1;
   bool is_fans = 2;
   uint32 item_id = 3;     // 加团礼物id
   uint32 fans_level = 4; // 粉丝等级
}


// 获取主播正生效的铭牌列表
message GetAnchorValidPlateListReq {
   ga.BaseReq base_req = 1;
}
message GetAnchorValidPlateListResp {
   ga.BaseResp base_resp = 1;
   repeated ga.GroupNamePlateInfo info_list = 2;
}

// 佩戴铭牌
message WearAnchorPlateReq {
   ga.BaseReq base_req = 1;
   uint32 grant_id = 2; // 铭牌发放id
}
message WearAnchorPlateResp {
   ga.BaseResp base_resp = 1;
}

// 退出粉丝团
message LeaveFansGroupRequest {
   ga.BaseReq base_req = 1;
   uint32 anchor_uid = 2;  // 主播uid
}
message LeaveFansGroupResponse {
   ga.BaseResp base_resp = 1;
}

// 粉丝团专属礼物特权
message FansGiftPrivilege {
   uint32 gift_id = 1; // 礼物id
   uint32 expire_ts = 2;  // 失效时间 单位秒
}

// 获取用户粉丝团专属礼物特权
message GetUserFansGiftPriRequest {
   ga.BaseReq base_req = 1;
   uint32 anchor_uid = 2; // 主播uid
}
message GetUserFansGiftPriResponse {
   ga.BaseResp base_resp = 1;
   repeated FansGiftPrivilege pri_list = 2; 
}


// 主播荣誉铭牌
message HonorNameplate {
    uint32 uid = 1;
    string honor_url = 2;
    string ranking_enter_honor_url = 3; // 榜单入口荣誉铭牌图标
    uint32 expired_time = 4;
}

message GetAnchorHonorNameplateReq {
    ga.BaseReq base_req = 1;
    uint32 actor_uid = 2;
}

message GetAnchorHonorNameplateResp {
    ga.BaseResp base_resp = 1;
    HonorNameplate honor_nameplate = 2;
}

// 直播房排行榜入口的推送结构
message LiveRankingEnterOptMsg {
    uint32 anchor_uid = 1;  // 主播uid
    uint32 channel_id = 2;  // 直播房间id
    string rank_desc = 3;   // 排行榜排名描述
}

enum QueryTimeType {
    QueryTimeType_ThisWeek = 0;   // 本周（实时）
    QueryTimeType_LastWeek = 1;   // 上周
}

enum ChannelLiveRankingType {
    RankingType_Unknown = 0;
    RankingType_FansLove = 1;               // 亲密榜
    RankingType_NewStart_Hardworking = 2;   // 勤奋新星榜
    RankingType_NewStart_Popularity = 3;    // 人气新星榜
    RankingType_High_Quality_Actor = 4;   // 优质歌手榜
    RankingType_Star_Quality_Actor = 5;   // 星级主播榜
    RankingType_New_Quality_Actor = 6;   // 新星主播榜
}

// 主播认证标识
message ExamineCertInfo {
    string item_name = 1;           // 标识名称
    string base_imgurl = 2;         // 标识底图
    string shadow_color = 3;
}

message ChannelLiveRankingMember {
    enum LiveStatus {
        None = 0;
        Living = 1; // 直播中
        PKing = 2;  // PK中
    }

    uint32 actor_uid = 1;
    uint32 score = 2;       // 榜单分数值
    uint32 rank = 3;        // 名次
    uint32 live_status = 4; // 直播状态 see LiveStatus

    string actor_account = 5; // 主播账号
    string actor_nickname = 6;// 主播昵称
    HonorNameplate honor_info = 7; // 主播荣誉铭牌
    string actor_tag_name = 8;  // 主播标签名
    int32 last_week_difference_rank = 9; // (上周排名-本周排名)与上周的排名差，仅获取本周榜单时有效

    uint32 sub_score = 10;  // 勤奋新星榜中score用于直播有效天，sub_score用于直播时长
    uint32 channel_id = 11;
    bool is_follow = 12;   // 是否关注
    repeated string actor_tag_list = 13;   // 主播标签列表
    ExamineCertInfo actor_cert = 14;  // 主播认证标识
    int32 actor_sex = 15;  // 主播性别 0:女 1:男
    repeated ExamineCertInfo actor_cert_list = 16;  // 等级标识和认证标识
}

message MyChannelLiveRank {
    ChannelLiveRankingMember rank_info = 1;
    uint32 last_rank_difference_score = 2; // 与上一名的分数差
    uint32 last_rank_difference_sub_score = 3;  // 与上一名的分数差(用于勤奋新星榜的直播时长差值)
    bool show_valid = 4; // 是否需要展示我的排名信息(true:要展示)
}

message GetRankingListReq {
    ga.BaseReq base_req = 1;
    uint32 actor_uid = 2;
    uint32 ranking_type = 3;    //see ChannelLiveRankingType
    uint32 query_time_type = 4; //see QueryTimeType
}

message GetRankingListResp {
    ga.BaseResp base_resp = 1;
    repeated ChannelLiveRankingMember ranking_list = 2;
    MyChannelLiveRank my_rank_info = 3;
}


enum ChannelLiveReportType {
    UNKOWN_REPORT = 0;
    CHANNEL_CHAT = 1;
    SEND_GIFT = 2;
    UP_MIC    = 3;
    STAY_TIME = 4; //观看时长
    FOLLOW    = 5; //关注
}

message ChannelLiveReportReq {
    ga.BaseReq base_req = 1;
    uint32 report_type = 2; //enum ChannelLiveReportType
    uint32 channel_id = 3;
    uint32 anchor_uid = 4; //主播UID
    uint32 audience_uid = 5; //听众UID
}

message ChannelLiveReportResp{
    ga.BaseResp base_resp = 1;
}


// 直播多人PK =======================================

//PK阶段
enum EnumMultiPkStatus
{
    Multi_Pk_Invalid = 0;  //无效
    Multi_Pk_Link_Mic = 1; //连麦阶段
    Multi_Pk_First_Kill = 2;  // 首杀阶段
    Multi_Pk_Common = 3;  // pk普通阶段
    Multi_Pk_Interact = 4; // pk互动阶段
    MULTI_PK_END_BUT_LINKING_MIC = 5; // 结束连麦阶段
}

enum EnumMultiPkMatchStatus {
    MPK_Match_Close = 0;  //不在匹配
    MPK_Match_Running = 1; //正在随机匹配
    MPK_Match_Applying = 2;  // 正在邀请匹配
}

// 邀请pk主播状态
enum ApplyMultiPkStatus {
    APPLY_MULTI_PK_STATUS_UNSPECIFIED =0; // 无效值
    APPLY_MULTI_PK_STATUS_NOT_LIVE = 1; // 未开播
    APPLY_MULTI_PK_STATUS_PKING = 2; // pk中
    APPLY_MULTI_PK_STATUS_CAN_INVITE = 3; // 可邀请
	APPLY_MULTI_PK_STATUS_INVITED = 4; // 已邀请
    APPLY_MULTI_PK_STATUS_ACCEPTED = 5; // 已接受
    APPLY_MULTI_PK_STATUS_REFUSED = 6; // 已拒绝
}

// 处理PK邀请枚举
enum HandleApplyMultiPkType {
    HANDLE_APPLY_MULTI_PK_TYPE_UNSPECIFIED =0; // 无效值
    HANDLE_APPLY_MULTI_PK_TYPE_ACCEPT = 1; // 同意
	HANDLE_APPLY_MULTI_PK_TYPE_REFUSE = 2; // 拒绝
}

// pk邀请事件类型
enum MultiPkApplyEventType {
    MULTI_PK_APPLY_EVENT_TYPE_UNSPECIFIED =0; // 无效值
    MULTI_PK_APPLY_EVENT_TYPE_APPLY = 1; // 邀请
    MULTI_PK_APPLY_EVENT_TYPE_CANCEL = 2; // 取消邀请
}

// PK模式枚举
enum MultiPkType {
    MULTI_PK_TYPE_UNSPECIFIED =0; // 无效值
    MULTI_PK_TYPE_SINGLE = 1; // 单人
	MULTI_PK_TYPE_TEAM = 2; // 组队
}

// pk结果枚举
enum MultiPkResult {
   MultiPkResult_Invalid = 0;  //无效
   MultiPkResult_Win = 1; // 胜利
   MultiPkResult_Lost = 2; // 失败
   MultiPkResult_Same = 3; // 平局
}


// 最近pk主播状态
enum MultiPkAnchorStatus {
    MULTI_PK_ANCHOR_STATUS_UNSPECIFIED =0; // 无效值
    MULTI_PK_ANCHOR_STATUS_IN_PK = 1; // pk中
	MULTI_PK_ANCHOR_STATUS_CAN_INVITE = 2; // 可邀请
    MULTI_PK_ANCHOR_STATUS_NOT_LIVE = 3; // 未开播
}

// pk队伍类型
enum MultiPkTeamType {
   MultiPkTeamType_Invalid = 0; // 无效
   MultiPkTeamType_APPLY = 1;  // 邀请组队成功
   MultiPkTeamType_MATCH = 2;  // 随机组队成功
}

// 多人pk主播信息
message MultiPkAnchorInfo {
    ga.UserProfile user_profile = 1;
    uint32 anchor_status = 2;   //最近pk主播状态 see ApplyMultiPkStatus
    uint32 live_status = 3;  // 直播状态 see EnumChannelLiveStatus
}

// 多人pk用户信息
message MultiPkUserInfo {
    ga.UserProfile user_profile = 1;

    uint64 pk_score = 2; //pk贡献值

    uint32 rich_level = 3; // 财富等级
    uint32 knight_level = 4; // 骑士等级 定义在ga_base.EChannelMemberKnightLevelID
    bool first_kill = 5; // 首杀

    // 各种等级 要和线上1v1PK的火力榜元素一致 之前是SendGiftUserInfo
    uint32 charm = 6; // 魅力等级
    uint32 nobility_level = 7; // 6.49.0客户端开始使用新的nobility_info字段
    uint32 group_fans_level = 8;
    uint32 channel_mem_level = 9; //房间等级
    string group_name = 10;   // 粉丝团名称
    ga.FansPlateInfo plate_info = 11;// 粉丝铭牌信息
    ga.NobilityInfo nobility_info = 12;  // 贵族信息
}


// 最佳拍档
message BestPartner {
  ga.UserProfile anchor_info = 1; 
  ga.UserProfile user_info = 2; 
  uint64 pk_score = 3; //pk贡献值
  string channel_name = 4; // 战力王所在的房间名称
}

// pk队伍房间信息
message MultiPkRoomInfo {
   uint32 team_id = 1; // 队id
   MultiPkAnchorInfo anchor_info = 2; // 主播信息
   repeated MultiPkUserInfo top_user_list = 3;  // 送礼top榜
   uint32 pk_score = 4;  // pk火力值 
   repeated MultiPkUserInfo knight_list = 5;  // 【废弃】在线骑士列表
   uint32 pk_rank = 6;  // pk排名
   bool is_sponsor = 7;  // 是否发起者
   uint32 chanenl_id = 8;
   uint32 pk_result = 9; // pk结果 see MultiPkResult
}

message MultiPkFirstKillUser {
  ga.UserProfile user_profile = 1;
  uint32 channel_id = 2; 
}

// pk信息
message MultiPkInfo {
  uint32 pk_id = 1;
  uint32 pk_type = 2; // pk模式 see MultiPkType
  uint32 pk_status = 3;  // see EnumMultiPkStatus
  repeated MultiPkRoomInfo room_info_list = 4;  // pk队伍房间列表
  MultiPkFirstKillUser first_kill_user = 5;  // 首杀用户
  uint32 pk_remain_ts = 6; // pk总剩余时间，单位s 废弃
  uint32 pk_status_remain_ts = 7;  // pk阶段剩余时间，单位s 废弃
  uint64 pk_end_ts = 8; // pk总结束时间，单位s
  uint32 pk_status_end_ts = 9;  // pk阶段结束时间，单位s
  bool   is_auto_pk_type = 10;  //是否是系统指定的pk模式
  uint32 pk_team_type = 11;  //组队类型 see MultiPkTeamType 
  int64  push_ts = 12;  // push和get的服务器时间，单位毫秒
  ga.time_present.LiveIntimatePresentInfo time_present_info = 13; // 直播亲密礼物
}


// 动画类型
enum DyncmicEffectType {
    DYNCMIC_EFFECT_TYPE_UNSPECIFIED =0; // 无效值
    DYNCMIC_EFFECT_TYPE_LOW = 1; // 低级动画
	DYNCMIC_EFFECT_TYPE_HIGH = 2; // 高级动画
}

// pk结算信息
message MultiPkSettleInfo {
  uint32 pk_result = 1; // pk结果 see MultiPkResult
  uint32 pk_type = 2; // pk模式 see MultiPkType
  repeated MultiPkRoomInfo room_list = 3;  // 单人匹配模式就只有一个主播
  BestPartner best_partner = 4; // 最佳拍档
  string dyncmic_effect_url = 5;   //动画url
  string dyncmic_effect_md5 = 6;   //动画md5
  string dyncmic_effect_json = 7;  // 动画json配置
  uint32 pk_id = 8; 
  string static_img_url = 9;  // 静态图
  int64 settle_ts = 10;  // 结算时间
  uint32 dyncmic_effect_type = 11; // 动画资源类型 see DyncmicEffectType
}

// pk匹配信息
message MultiPkMatchInfo{
  uint32 match_status = 1;  // 匹配状态 see EnumMultiPkMatchStatus
  repeated ga.UserProfile user_list = 2; // pk队伍
  uint32 sponsor_uid = 3; // 发起者uid
}


// 最近拍档im推送
message BestPartnerImMsg {
  BestPartner partner = 1;
  string small_url = 2;  // 卡片小图url
  string big_url = 3;     // 卡片大图url
  string title = 4;
  string content = 5;
  int64 server_ts = 6;  // 服务器时间
}

// 获取直播多人PK权限
message GetChannelLiveMultiPkPermissionRequest {
    ga.BaseReq base_req = 1;
}
message GetChannelLiveMultiPkPermissionResponse {
    
    ga.BaseResp base_resp = 1;
    bool is_show=2;
}

// 搜索主播
message SerarchMultiPkAnchorRequest {
    
    ga.BaseReq base_req = 1;
    string target_ttid=2;
}
message SerarchMultiPkAnchorResponse {
    
    ga.BaseResp base_resp = 1;
    MultiPkAnchorInfo anchor_info=2; // 如果不存在，anchor_info.uid=0
    uint32 status=3; // 邀请状态 see APPLY_MULTI_PK_STATUS
}

// 获取最近多人PK的主播列表
message GetChannelLiveMultiPkRecordListRequest {
    
    ga.BaseReq base_req = 1;
}
message GetChannelLiveMultiPkRecordListResponse {
    
    ga.BaseResp base_resp = 1;
    repeated MultiPkAnchorInfo anchor_list = 2;
}


// 发起邀请
message ApplyChannelLiveMultiPkRequest {
    
    ga.BaseReq base_req = 1;
    uint32 target_uid = 2;
}
message ApplyChannelLiveMultiPkResponse {
    
    ga.BaseResp base_resp = 1;
}

// 取消邀请
message DisinviteChannelLiveMultiPkRequest {
    ga.BaseReq base_req = 1;
    uint32 target_uid = 2;
}
message DisinviteChannelLiveMultiPkResponse {
    ga.BaseResp base_resp = 1;
}


// 开始随机匹配
message MatchMultiPkRequest {
    
    ga.BaseReq base_req = 1;
}
message MatchMultiPkResponse {
    
    ga.BaseResp base_resp = 1;
}


// 取消PK匹配
message CancelMatchMultiPkRequest {
    
    ga.BaseReq base_req = 1;
}
message CancelMatchMultiPkResponse {
    
    ga.BaseResp base_resp = 1;
}

// 取消多人PK组队
message CancelChannelLiveMultiPkTeamRequest {
    
    ga.BaseReq base_req = 1;
}
message CancelChannelLiveMultiPkTeamResponse {
    
    ga.BaseResp base_resp = 1;
}

// 处理收到PK邀请
message AcceptChannelLiveMultiPkRequest {
    ga.BaseReq base_req = 1;
    uint32 apply_uid=2; // 邀请人uid
    uint32 oper = 3; // see HANDLE_APPLY_MULTI_PK_TYPE
}
message AcceptChannelLiveMultiPkResponse {
    ga.BaseResp base_resp = 1;
}

// 多人pk邀请推送
message MultiPkApplyPushMsg {
   uint32 apply_uid=1; // 邀请人uid
   repeated MultiPkAnchorInfo anchor_list = 2;   // 已有队友
   uint32 apply_ts = 3;  // 邀请时间 单位s
   uint32 event_type = 4; // 邀请事件类型 see MultiPkApplyEventType
}

// 多人pk邀请结果推送
message MultiPkApplyResPushMsg {
    uint32 oper_type = 1; //see HandleApplyMultiPkType
    MultiPkAnchorInfo info = 2;  // 主播信息
} 

// 开始PK
message StartChannelLiveMultiPkRequest{
    ga.BaseReq base_req = 1;

    uint32 pk_id = 2;
    uint32 pk_type = 3; // pk模式 see MultiPkType
    repeated MultiPkTeamSimpleInfo team_list=4;
}
message MultiPkTeamSimpleInfo{
    uint32 team_id = 1; // 队id   组队传1或2
    uint32 uid=2; // 主播uid
}
message StartChannelLiveMultiPkResponse{
    ga.BaseResp base_resp = 1;
    MultiPkInfo multi_pk_info=2;
}

// 获取PK火力榜
message GetChannelLiveMultiPkRankRequest{ 
    ga.BaseReq base_req = 1;
    uint32 anchor_uid=2;//主播uid
}
message GetChannelLiveMultiPkRankResponse{ 
    ga.BaseResp base_resp = 1;
    repeated MultiPkUserInfo rank_list=2;
}


// 获取在线骑士列表
message GetChannelLiveMultiPkKnightListRequest{ 
    
    ga.BaseReq base_req = 1;
    uint32 anchor_uid = 2;// 主播uid
}
message GetChannelLiveMultiPkKnightListResponse{ 
    
    ga.BaseResp base_resp = 1;
    repeated MultiPkUserInfo knight_list=2;
}

// 提前结束多人PK
message StopChannelLiveMultiPkRequest {
    ga.BaseReq base_req = 1;
    uint32 pk_id = 2;
}
message StopChannelLiveMultiPkResponse {
    ga.BaseResp base_resp = 1;
}

// 获取pk队伍信息 废弃
message GetChannelLiveMultiPkTeamInfoRequest {
    ga.BaseReq base_req = 1;
}
message GetChannelLiveMultiPkTeamInfoResponse  {
    ga.BaseResp base_resp = 1;
}


message InitChannelLiveMultiPkTeamRequest {
    ga.BaseReq base_req = 1;
}
message InitChannelLiveMultiPkTeamResponse {
    ga.BaseResp base_resp = 1;
}

 
// 在房骑士信息 推送msg
message MultiPkKnightInfo {

    uint32 anchor_uid=1;
    uint32 channel_id=2;
    repeated MultiPkUserInfo knight_list=3;
}

 

// 直播多人PK end =======================================


//获取虚拟主播房间密钥
message GetVirtualLiveChannelSecretRequest {
	ga.BaseReq base_req = 1;
	uint32 channel_id = 2;
	bool   refresh    = 3;      //是否刷新
}
message GetVirtualLiveChannelSecretResponse {
	ga.BaseResp base_resp = 1;
	string channel_secret = 2;
}