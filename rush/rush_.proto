syntax="proto2";

package ga.rush;
import "ga_base.proto";

option java_package ="com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/rush";

message RushQueueReq
{
    required BaseReq base_req = 1;
    required uint32 type = 2;
}

message RushQueueResp
{
    // buf:lint:ignore ENUM_FIRST_VALUE_ZERO
    enum RushResult
    {
        ALLOW = 1;
        QUEUE = 2;
        DENY = 3;
    }

    required BaseResp base_resp = 1;
    required uint32 rush_result = 2; // RushResult
    optional uint32 rerush_delay = 3; // 只有当RushResult为QUEUE才会有值
}