syntax = "proto3";

package ga.super_channel;

import "ga_base.proto";
import "channel/channel_.proto";
import "ancient_search/ancient-search_.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/super-channel";

message SuperChannelGetChannelListReq {
    BaseReq base_req = 1;
}
message SuperChannelGetChannelListResp {
    BaseResp base_resp = 1;

    // 该接口目前只返回 房间id + 房间类型
    repeated SuperChannelInfo channel_info_list = 2;
}

/*******进房协议数据结构 begin *********/
message SuperChannelEnterReq {
    BaseReq base_req = 1;
    uint32 channel_id = 2;
    uint32 channel_show_id = 3;
    uint32 follow_friend_uid = 4;
    uint32 enter_source = 5; //see ChannelEnterReq::EChannelEnterSource
    string enter_source_second = 6;
    string channel_view_id = 7;
}
message SuperChannelInfo {
    uint32 channel_id = 1; //房间id
    uint32 channel_creator_uid = 2; //房主uid
    string channel_name = 3; //房间名字
    uint32 channel_type = 4; //房间类型
    string channel_icon = 5; //房间图标的MD5
    string channel_desc = 6; //房间话题的描述(标题)
    uint32 channel_show_id = 7; //房间 display_id

    bool is_open_lock_screen_switch = 8; //是否打开了房间锁屏开关
    bool is_open_disable_attachment_msg_switch = 9; //是否打开了房间禁止发图开关
    bool is_open_disable_level_lmt_switch = 10; //是否打开了房间发言用户等级限制开关
    bool is_open_normal_queue_up_mic_switch = 11; //是否开启普通房间的排麦功能

    int64 update_ts = 12; // 更新时间戳

    bool is_locked = 13; //是否加锁

    string channel_view_id = 14 ;
}

message SuperChannelDetailInfo {
    SuperChannelInfo channel_info = 1; //房间基本信息

    string enter_msg = 2; //公屏进房文本 (已废弃)
    string welcome_msg = 3; //公屏欢迎语文本
    string channel_creator_account = 4; //房主account
}

message SuperChannelGRPCPushProxy {
    string addr = 1;
    bool insecure = 2; // 如果为true，则开启非TLS连接
    string identifier = 3; // 用于指定`push-proxy`头部
    string authority = 4; // addr为ip+port形式返回时，下列字段通常会一起返回，需要设置到响应的连接参数中
}

message SuperChannelLayoutInfo {
    uint32 layout_type = 1; //布局类型，见channel-scheme_.proto
    uint32 fallback_layout_type = 2;   //如果旧版客户端不认识layout_type，则用该值做兜底逻辑
    uint32 cur_mic_size = 3;           //当前的麦位数
}
message SuperChannelMicAudioInfo {
    uint32 mic_audio_type = 1;   //枚举：高音质，低音质,KTV等，见channel-scheme_.proto
    //6.59.5版本动态配置音频参数需求新增字段
    map<uint32, string> mic_audio_sdk_info = 2; //key为客户端类型，see ga.TT_CLIENT_TYPE  value为sdk配置信息，json格式,客户端自己解析
    uint64  mic_audio_sdk_info_update_ms = 3; //sdk配置信息更新时间,毫秒时间戳
    uint32 high_bit_rate = 4; // 优先高码率，单位kbps，非零时优先使用该码率
}

message SuperChannelEnterResp {
    BaseResp base_resp = 1;
    SuperChannelDetailInfo info = 2;
    uint32 server_time = 3;
    repeated SuperChannelGRPCPushProxy grpc_push_proxies = 6;
    uint32 mic_mode = 7; //客户端兼容参数
    SuperChannelLayoutInfo layout_info = 8;
    SuperChannelMicAudioInfo mic_audio_info = 9;
    bool audio_token_switch = 10;   //true才进行token校验
    bool push_token_switch = 11;    //true才进行token校验
    uint32 channel_msg_cur_seq_id = 12;               // 房间消息当前序号,客户端订阅完推送通道后，会用该序号拉取房间消息
}
/*******进房协议数据结构 end*********/

/**********退房协议数据结构 begin*********/
message SuperChannelQuitReq {
    BaseReq base_req = 1;
    uint32 channel_id = 2;
}

message SuperChannelQuitResp {
    BaseResp base_resp = 1;
    uint32 channel_id = 2;
}
/**********退房协议数据结构 end*********/

message SuperChannelMicInfo {
    enum MicState {
        UNKNOWN = 0; // 异常
        NORMAL = 1; // 正常
        DISABLE = 2; // 锁麦位 此时麦位不能用
        MUTE = 3; // 麦位禁言 可以上麦但是不能说话
    }

    uint32 mic_id = 1; // 麦位ID
    uint32 mic_state = 2; // 麦位状态，see MicState
    int64 mic_ts = 3; // 状态变更ts

    uint32 uid = 4;
    string account = 5;
    string nick_name = 6;
    int32 sex = 7; // 麦上用户的性别
    string face_md5 = 8; // 麦上用户头像MD5

    uint32 nobility_level = 9; // 贵族等级
}

message SuperChannelHoldMicReq {
    BaseReq base_req = 1;
    uint32 channel_id = 2;
    uint32 uid = 3; // 主动上麦可不填
    uint32 mic_id = 4;
    string account = 5;
}
message SuperChannelHoldMicResp {
    BaseResp base_resp = 1;
    uint32 channel_id = 2;
}

message SuperChannelReleaseMicReq {
    BaseReq base_req = 1;
    uint32 channel_id = 2;
    uint32 uid = 3; // 主动下麦可不填
}
message SuperChannelReleaseMicResp {
    BaseResp base_resp = 1;
    uint32 channel_id = 2;
}

message SuperChannelChangeMicReq {
    BaseReq base_req = 1;
    uint32 channel_id = 2;
    uint32 mic_id = 3;
}
message SuperChannelChangeMicResp {
    BaseResp base_resp = 1;
    uint32 channel_id = 2;
}

message SuperChannelGetMicListReq {
    BaseReq base_req = 1;
    uint32 channel_id = 2;
}
message SuperChannelGetMicListResp {
    BaseResp base_resp = 1;

    uint32 mic_mode = 2;
    repeated SuperChannelMicInfo mic_list = 3;
    int64 server_time_ms = 4; // 64bit 毫秒级 服务器时间

    uint32 channel_id = 5;
}

message SuperChannelSetMicStatusReq {
    BaseReq base_req = 1;
    uint32 channel_id = 2;
    uint32 mic_id = 3; // 麦位ID
    uint32 mic_state = 4;
}
message SuperChannelSetMicStatusResp {
    BaseResp base_resp = 1;
    uint32 channel_id = 2;
}

message SuperChannelSetMicModeReq {
    BaseReq base_req = 1;
    uint32 channel_id = 2;
    uint32 mic_mode = 3; // 麦位模式
    uint32 layout_type = 4;   //新版用这个字段切玩法
}
message SuperChannelSetMicModeResp {
    BaseResp base_resp = 1;
    uint32 channel_id = 2;
}

message SuperChannelSendHoldMicInviteReq {
    BaseReq base_req = 1;
    uint32 channel_id = 2; // 房间id
    uint32 uid = 3; // 被邀请用户
    uint32 mic_id = 4; // 麦位id
}
message SuperChannelSendHoldMicInviteResp {
    BaseResp base_resp = 1;
    uint32 channel_id = 2;
}

message SuperChannelReplyHoldMicInviteReq {
    BaseReq base_req = 1;
    uint32 channel_id = 2; // 房间id
    uint32 mic_id = 3; // 麦位id
    string ticket = 4; // 凭证
    bool is_accept = 5; // 是否接受邀请
}
message SuperChannelReplyHoldMicInviteResp {
    BaseResp base_resp = 1;
    uint32 channel_id = 2;
}

//在线成员列表
message SuperChannelGetMemberListReq {
    BaseReq base_req = 1;
    uint32 channel_id = 2;
    uint32 req_cnt = 3; //每次获取列表最多需要多少条 一般填50
    bool need_my_info = 4; //是否需要自己的信息
}
message SuperChannelMemberInfo {
    uint32 uid = 1; // 成员uid
    string nick_name = 2; // 成员昵称
    string account = 3;
    uint32 sex = 4;
    uint32 gift_count = 5; // 房间消费数
    uint32 rank = 6; // 我的排名
    uint32 d_value = 7; // 距离上一名的差值
    uint32 rich_level = 8; // 用户的财富等级
    NobilityInfo nobility_info = 9; //贵族信息
    uint32 charm_level = 10;        //魅力等级
    SuperPlayerLevel super_player_level = 11; // 会员值
}

message SuperChannelGetMemberListResp {
    BaseResp base_resp = 1;
    uint32 channel_id = 2;
    uint32 all_member_cnt = 3; //当前房间内所有成员数目
    repeated SuperChannelMemberInfo channel_member_list = 4; // 成员列表(按排名排序)
    SuperChannelMemberInfo my_info = 5; // 我的信息
    uint32 last_rank = 6;              //最后一个非0的排名
}

/**********获取活动大房扩展信息接口 begin **************/

enum SuperChannelExtInfoType {
    EXT_INFO_TYPE_UNKNOWN = 0x0;
    EXT_INFO_TYPE_USER = 0x1;
    EXT_INFO_TYPE_ADMIN = 0x2;
}

message SuperChannelGetExtInfoReq {
    BaseReq base_req = 1;
    uint32 channel_id = 2;

    // 如果要用户信息和房间信息就填（0x1|0x2）= 0x3
    uint32 ext_info_type_bitmap = 3;
}

//用户扩展信息
message SuperChannelExtUserInfo {
    bool has_collected_channel = 1; //是否收藏房间
}

message SuperChannelAdminInfo {
    uint32 uid = 1;
    // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
    uint32 Role = 2;
}

//房间扩展信息
message SuperChannelExtChannelInfo {
    repeated SuperChannelAdminInfo admin_list = 1; // 管理页列表
    int64 admin_update_ts = 2;                     // 管理员更新时间戳
    string channel_creator_nickname = 3;           // 房间创建者昵称
    ChannelMediaInfo channel_media_info = 4;       //房间多媒体信息
}

message ChannelMediaInfo{
    string live_video_link = 1;                    // 直播视频链接
    int64 live_update_ts = 2;                      // 直播视频链接更新时间戳
}

message SuperChannelGetExtInfoResp {
    BaseResp base_resp = 1;
    SuperChannelExtChannelInfo channel_ext_info = 2;
    SuperChannelExtUserInfo user_ext_info = 3;
}
/**********获取活动大房扩展信息接口 end **************/

/**********搜索接口 begin **************/
message SuperChannelSearchReq {
    BaseReq base_req = 1;
    string keyword = 2; // 关键字
}

message SuperChannelSearchResp {
    BaseResp base_resp = 1;
    repeated SuperChannelSearchResultItem result_list = 2; // 搜索结果列表
    string config_id = 3;                                  // 搜索结果配置ID
}

message SuperChannelSearchResultItem {
    string title = 1; // 标题
    string bg = 2;    // 背景
    oneof value {
        SuperChannelSearchResultChannel channel = 3;   // 房间
        ga.ancient_search.ContactBrief contact_brief = 4;             // 玩伴
        SuperChannelSearchResultActivity activity = 5; // 活动
    }
}

message SuperChannelSearchResultChannel{
    ga.channel.ChannelDetailInfo channel_detail_info = 1;
    ga.channel.SCTagInfo tag_info = 2;
    ga.channel.PgcChannelExtraInfo pgc_channel_info = 3;
}

message SuperChannelSearchResultActivity {
    string title = 1; // 标题
    string icon = 2; // 图标
    string desc = 3; // 描述
    string jump_url = 4; // 跳转链接
}
/**********搜索接口 end **************/

//玩法信息

message SuperChannelSchemeInfo {
    uint32 scheme_id = 1;      //对应之前的tabid
    string scheme_name = 2;
    uint32 scheme_type = 3;    //enum SchemeType,对应之前的tab_type,游戏，音乐，小游戏等
    SuperChannelLayoutInfo layout_info = 4;
    SuperChannelMicAudioInfo mic_audio_info = 5;
    uint64 ts = 6;             //服务端时间戳,毫秒
}
message GetSuperChannelSchemeInfoReq{
    ga.BaseReq base_req = 1;
    uint32 cid = 2;
}
message GetSuperChannelSchemeInfoResp{
    ga.BaseResp base_resp = 1;
    uint32 cid = 2;
    SuperChannelSchemeInfo scheme_info = 3;
    //兼容旧命令的一些字段(客户端需要哪些字段后面再补上去)
}
