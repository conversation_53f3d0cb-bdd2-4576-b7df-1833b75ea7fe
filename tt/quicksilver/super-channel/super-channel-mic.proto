syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/super-channel";
package super_channel;

service SuperChannelMic {
    // 获取麦位列表
    rpc GetMicList (GetMicListReq) returns (GetMicListResp) {
    }

    // 获取麦位模式
    rpc GetMicMode (GetMicModeReq) returns (GetMicModeResp) {
    }

    // 获取用户所在麦位
    rpc GetUserMic (GetUserMicReq) returns (GetUserMicResp) {
    }

    // 获取麦位列表
    rpc SendHoldMicInvite (SendHoldMicInviteReq) returns (SendHoldMicInviteResp) {
    }

    rpc ReplyHoldMicInvite (ReplyHoldMicInviteReq) returns (ReplyHoldMicInviteResp) {
    }

    // 同步接口，用于调试
    rpc SyncMicList (SyncMicListReq) returns (SyncMicListResp) {
    }
}

message MicInfo {
    uint32 mic_id = 1; // 麦位ID
    uint32 mic_state = 2; // 麦位状态，see super-channel_.proto MicState
    int64 mic_ts = 3; // 状态变更ts
    uint32 mic_uid = 4;

    string account = 5;
    string nick_name = 6;
    int32 sex = 7; // 麦上用户的性别
    string face_md5 = 8; // 麦上用户头像MD5

    uint32 nobility_level = 9; // 贵族等级
}

message GetMicListReq {
    uint32 channel_id = 1;
}
message GetMicListResp {
    uint32 mic_mode = 1;
    repeated MicInfo all_mic_list = 2;
    int64 server_time_ms = 3; // 64bit 毫秒级 服务器时间
}

message GetMicModeReq {
    uint32 channel_id = 1;
}
message GetMicModeResp {
    uint32 mic_mode = 1;
    int64 server_time_ms = 2; // 64bit 毫秒级 服务器时间
}

message GetUserMicReq {
    uint32 channel_id = 1;
    uint32 uid = 2;
}
message GetUserMicResp {
    MicInfo info = 1;
    int64 server_time_ms = 2; // 64bit 毫秒级 服务器时间
}

message SendHoldMicInviteReq {
    uint32 channel_id = 1; // 房间id
    uint32 uid = 2; // 被邀请用户
    uint32 mic_id = 3; // 麦位id
}
message SendHoldMicInviteResp {
    string ticket = 1; // 凭证
}

message ReplyHoldMicInviteReq {
    uint32 channel_id = 1; // 房间id
    uint32 uid = 2; // 被邀请用户
    uint32 mic_id = 3; // 麦位id
    string ticket = 4; // 凭证
}
message ReplyHoldMicInviteResp {
    bool exists = 1;
}

message SyncMicListReq {
    uint32 channel_id = 1;
    uint32 operate_uid = 2;
    uint32 target_uid = 3;
}
message SyncMicListResp {
}