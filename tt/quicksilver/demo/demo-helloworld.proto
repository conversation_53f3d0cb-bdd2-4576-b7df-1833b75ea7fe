syntax = "proto3";
package demo_helloworld;
option go_package = "golang.52tt.com/protocol/services/demo/demo-helloworld";

service DemoHelloWorld {
    rpc AddAndEcho(AddAndEchoReq) returns (AddAndEchoResp) {}

    //读写db
    rpc GetUserInfoFromDB(GetUserInfoFromDBReq) returns (GetUserInfoFromDBResp) {}
    rpc SetUserInfoToDB(SetUserInfoToDBReq) returns (SetUserInfoToDBResp) {}

    //读写redis
    rpc GetUserInfoFromRedis(GetUserInfoFromRedisReq) returns (GetUserInfoFromRedisResp) {}
    rpc SetUserInfoToRedis(SetUserInfoToRedisReq) returns (SetUserInfoToRedisResp) {}
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message AddAndEchoReq {
    int64 iCnt = 1;
    int64 iAdd = 2;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message AddAndEchoResp {
    int64 iRetCnt = 1;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message UserInfo {
    int64 iUid = 1;
    string sName = 2;
}

message GetUserInfoFromDBReq {
    repeated int64 uids = 1;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetUserInfoFromDBResp {
    repeated UserInfo userInfos = 1;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message SetUserInfoToDBReq {
    UserInfo setInfo = 1;
}

message SetUserInfoToDBResp {
}

message GetUserInfoFromRedisReq {
    repeated int64 uids = 1;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetUserInfoFromRedisResp {
    repeated UserInfo userInfos = 1;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message SetUserInfoToRedisReq {
    UserInfo setInfo = 1;
}

message SetUserInfoToRedisResp {
}
