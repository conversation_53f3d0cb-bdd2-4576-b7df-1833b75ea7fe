syntax = "proto3";

package ga.star_trek_logic;

import "ga_base.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/star-trek-logic";


// 奖励包裹信息
message StarTAwardInfo {
    uint32 pack_id = 1;               // 奖品id(gift_id)
    string pack_name = 2;             // 奖品名
    string pack_pic = 3;              // 奖品图
    uint32 pack_amount = 4;           // 奖品数量
    uint32 unit_price = 5;            // 奖品单价 T豆
    uint32 fin_time = 6;              // 过期时间
}

message StarTUserInfo{
    uint32 uid = 1;
    string nickname = 2;
    string account = 3;
}

message StarTrekResultOpt {
    enum AwardResult {
        ConsolationAward = 0;   // 安慰奖
        BingoAward = 1;         // 大奖
        TrekFail = 2;           // 巡航失败
    }

    AwardResult award_result = 1;
    StarTAwardInfo award_info = 2;  // 当award_result = BingoAward or ConsolationAward 时有效
    string notify_text = 3;         // 弹窗文案
}

// 全服 中奖人数补充信息
message StarTrekBreakOpt{
    uint32 bingo_user_num = 1;
}

// 上期回顾
message LastRoundReview{
    uint32 round_id = 1;
    uint32 round_time = 2;            // 场次结束时间
    bool trek_result = 3;               // 巡航结果
    StarTAwardInfo award_pack = 4;      // 上期奖品
    StarTUserInfo user_info = 5;        // 中奖用户

    repeated StarTUserInfo user_list = 6; // 部分中奖用户（用于动画结果展示）
    uint32 bingo_user_num = 7;            // 中奖人数
}

// 下期预告
message NextRoundForecast{
    uint32 round_id = 1;
    StarTAwardInfo award_pack = 2; // 下期奖品预告
}

// 中奖轮播信息
message RollingAwardInfo{
    uint32 uid = 1;
    string nickname = 2;    // 获奖用户名
    string pack_name = 3;   // 奖品名
    string pack_pic = 4;    // 奖品图
    uint32 pack_amount = 5; // 奖品数量

    uint32 bingo_user_num = 6;  // 轮播轮次的中奖人数
}

// 获取星际巡航信息，每5s拉取刷新
message GetStatTrekInfoReq{
    ga.BaseReq base_req = 1;
}

message GetStatTrekInfoResp{
    ga.BaseResp base_resp = 1;
    uint32 round_id = 2;              // 场次id
    uint32 begin_time = 3;            // 本轮开始时间，开始后方可参与
    uint32 end_time = 4;              // 结束时间戳

    StarTAwardInfo award_pack = 5;      // 本期星际礼物信息
    uint32 invest_progress = 6;         // 全站总补给值
    uint32 user_count = 7;              // 参与人数

    LastRoundReview last_s = 8;             // 上期回顾
    NextRoundForecast next_s = 9;           // 下期预告
    repeated RollingAwardInfo records = 10; // 中奖轮播信息
    repeated StarTUserInfo latest_partitions = 11;   // 最新参与列表,为了兼容开奖随机用户展示，最大返回15个用户

    uint32 user_supply = 12;                    // 个人补给值
    uint32 min_invest = 13;                   // 单次补给值下限
    uint32 round_supply_limit = 14;           // 每场巡航个人补给上限
    uint32 user_daily_invest = 15;            // 用户当天总投入值
}

// 废弃
message GetSupplyValueChangeReq{
    ga.BaseReq base_req = 1;
}

// 废弃
message GetSupplyValueChangeResp{
    ga.BaseResp base_resp = 1;
    uint32 server_supply = 2;                // 全服补给值
    uint32 my_supply = 3;                    // 个人补给值
    uint32 min_invest = 4;                   // 单次补给值下限
    uint32 round_supply_limit = 5;           // 每场巡航个人补给上限
}

message SupplyConf{
    uint32 gift_id = 1;
    uint32 gift_type = 2;  // see backpack_.proto PackageItemType
}

message GetSupplyConfReq{
    ga.BaseReq base_req = 1;
}

message GetSupplyConfResp{
    ga.BaseResp base_resp = 1;
    repeated SupplyConf conf_list = 2;
    uint32 daily_limit = 3;   //每日投入补给限制,T豆 add
}

message CostSupplyInfo{
    //uint32 user_item_id = 1;        // 礼物在用户背包中的item_id
    uint32 gift_id = 2;             // 礼物/碎片id
    uint32 gift_type = 3;           // 补给类型, see backpack_.proto PackageItemType
    uint32 gift_num = 4;            // 消耗数量
}

// 去探险
message DoInvestReq{
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2;                      // 用户所在房间id
    uint32 round_id = 3;                        // 参与的场次id
    repeated CostSupplyInfo supply_list = 4;    // 所选方案
    uint32 cost = 5;                            // 所选方案总消耗T豆值
}

message DoInvestResp{
    ga.BaseResp base_resp = 1;
    uint32 round_id = 2;            // 参与场次id
    uint32 my_supply = 3;           // 个人补给值
    uint32 round_supply_limit = 4;  // 个人补给上限
    uint32 min_invest = 5;          // 首次补给值下限
    uint32 server_supply = 6;       // 全服补给值
    uint32 user_daily_invest = 7;   // 当日补给值
}

enum TrekResultType{
    AllType = 0;
    Success = 1;
    Failed = 2;
}

// 获取用户巡航记录
message GetMyTrekRecordReq{
    ga.BaseReq base_req = 1;
    uint32 trek_result = 2;        // see TrekResultType
    string last_page_final_id = 3; // 上一页的最后一条记录id
    uint32 limit = 4;
}

message MyTrekRecord{
    string id= 1;
    uint32 uid = 2;
    uint32 round_id = 3;            // 参与场次
    uint32 round_time = 4;          // 本场结束时间
    bool trek_result = 5;           // 本场巡航结果

    string prize_name = 6;         // 星际礼物名称
    // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
    string Prize_pic = 7;          // 星际礼物pic
    string lucky_user = 8;         // 获得者昵称

    uint32 my_invest = 9;           // 用户投入的补给值
    string my_supply_desc = 10;      // 用户投入补给描述
    string my_award_desc = 11;      // 用户获得奖励

    uint32 bingo_user_num = 12;  // 中奖人数
}

message GetMyTrekRecordResp{
    ga.BaseResp base_resp = 1;
    repeated MyTrekRecord my_record_list = 2;
}


// 往期回顾
message GetAllTrekHistoryReq{
    ga.BaseReq base_req = 1;
    uint32 offset = 2;
    uint32 limit = 3;
}

message GetAllTrekHistoryResp{
    ga.BaseResp base_resp = 1;
    repeated LastRoundReview review_list = 2;
}



message StarTrekNotify{
    bool has_floating = 1;          // 是否开启房间右下角入口浮层提醒
    string floating_text = 2;       // 入口浮层文案
    uint32 floating_duration = 3;   // 入口浮层显示时长

    bool has_public = 4;            // 是否开启公屏提示
    string public_text = 5;         // 公屏文案
    string public_color = 6;        // 公屏文字颜色
}

// 是否可见入口，以及浮层显示
message StarTrekEntryAndNotifyReq{
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2;
}

message StarTrekEntryAndNotifyResp{
    ga.BaseResp base_resp = 1;
    bool cannot_see = 2;                    // 星际巡航不可见入口 (true-不可见，false-可见)
    uint32 day_activity_begin = 3;          // 活动开启日 活动开启时间
    uint32 day_activity_end = 4;            // 当前轮次的结束时间
    StarTrekNotify stat_trek_notify = 5;    // 星际巡航 提醒信息
}
