syntax = "proto3";

package ga.superplayerlogic;

import "ga_base.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/superplayerlogic";

//会员等级信息等
message SuperPlayerInfo {
  uint32 super_player_uid = 1; //uid
  int64 super_player_value = 2; //会员值
  int64 super_player_level = 3; //会员等级

  int64 begin_timestamp = 4; //当前会员周期开始时间戳，没开通过是0
  int64 expire_timestamp = 5; //当前会员周期结束时间戳，没开通过是0,版本号

  string super_player_account = 6; //
  int64 year_member_expire_timestamp = 7; //年费会员结束时间戳

  SuperPlayerVipType super_player_type = 8; //会员类型
  bool is_svip = 9; //是否是svip

  EntryAdvStatus entry_adv_status = 10; // 入口文案状态
  int64 server_time = 11; // 服务器时间

  SuperPlayerExpiredType last_expired_type = 12; //最近到期类型
}

// 超级玩家类型
enum SuperPlayerVipType {
  SUPER_PLAYER_VIP_TYPE_UNSPECIFIED = 0; // 未开通
  SUPER_PLAYER_VIP_TYPE_SUPER_PLAYER = 1; // 超级玩家
  SUPER_PLAYER_VIP_TYPE_SUPER_PLAYER_ANNUAL = 2; // 年费超级玩家
  SUPER_PLAYER_VIP_TYPE_SVIP = 3; // SVIP
  SUPER_PLAYER_VIP_TYPE_SVIP_ANNUAL = 4; // 年费SVIP
}

// 会员到期类型
enum SuperPlayerExpiredType {
  SUPER_PLAYER_EXPIRED_TYPE_UNSPECIFIED = 0;
  SUPER_PLAYER_EXPIRED_TYPE_SUPER_PLAYER = 1; // 超级玩家到期
  SUPER_PLAYER_EXPIRED_TYPE_SVIP = 2; // SVIP到期
}

//会员支付结果推送
message PayResultPushMsg {
  bool result = 1; //true支付成功，false失败
  string reason = 2; //如果失败，失败的理由
  uint32 super_player_uid = 3; //被开通人UID
  string notice_msg = 4; //开通成功提示信息
}

//取会员等级信息等
message GetSuperPlayerInfoReq {
  ga.BaseReq base_req = 1;
  uint32 super_player_uid = 2; //
}

message GetSuperPlayerInfoResp {
  ga.BaseResp base_resp = 1;
  SuperPlayerInfo super_player_info = 2;
}

//批量取接口
message BatchGetSuperPlayerInfoReq {
  ga.BaseReq base_req = 1;
  repeated uint32 super_player_uid_list = 2; //UID列表
  repeated string super_player_account_list = 3; //account列表
}

message BatchGetSuperPlayerInfoResp {
  ga.BaseResp base_resp = 1;
  repeated SuperPlayerInfo super_player_info_list = 2; //会员信息列表
}

//特权枚举
enum EnumSuperPlayerPrivilege {
  Unknown_Privilege = 0;
  Visit_History = 1; //访问历史记录
}

//会员特权
message Privilege {
  int64 id = 1;
  string name = 2; //权利名
  string desc = 3; //描述
}

message LevelPrivilege {
  int64 level = 1; //等级
  repeated Privilege privilege_list = 2; //等级对应的权益列表
}

//会员配置接口
message GetSuperPlayerConfReq{
  ga.BaseReq base_req = 1;
}

message GetSuperPlayerConfResp {
  ga.BaseResp base_resp = 1;
  repeated LevelPrivilege level_privilege_list = 2; //权限列表
  int64 expire_notify_hour = 3; //差多少个小时过期，算即将过期
  repeated SuperPlayerEntryAdvConf adv_conf_list = 4;
}

message SuperPlayerInfoPushMsg {

  // buf:lint:ignore ENUM_PASCAL_CASE
  enum ENUM_SUPER_PLAYER_PUSH_MSG {
    ENUM_UNKOWN = 0;
    ENUM_SUPER_PLAYER_INFO_CHANGE = 1;       //对应SuperPlayerInfo
    ENUM_SUPER_PLAYER_PAY_RESULT  = 2;       //支付结果推送，对应PayResultPushMsg
	ENUM_SUPER_PLAYER_PAY_NOTICE  = 3;       //为别人支付弹框
  }

  ENUM_SUPER_PLAYER_PUSH_MSG msg_type = 1;
  bytes msg_bin = 2; //消息序列化，具体消息结构见 enum ENUM_SUPER_PLAYER_PUSH_MSG
}

//获取特别关心用户列表
message GetUserSpecialConcernReq{
  ga.BaseReq base_req = 1;
  uint32 uid = 2;
}

message GetUserSpecialConcernResp{
  ga.BaseResp base_resp = 1;
  repeated ConcernUser special_concern_list = 2;
  bool show_expire_window = 3; // 是否展示特别关心装扮过期弹框
}

//添加用户到特别关心
message AddUserSpecialConcernReq{
  ga.BaseReq base_req = 1;
  uint32 uid = 2;
  string target_account = 3;
}

message AddUserSpecialConcernResp{
  ga.BaseResp base_resp = 1;
  repeated ConcernUser special_concern_list = 2;
}

//从特别关心移除用户
message DelUserSpecialConcernReq{
  ga.BaseReq base_req = 1;
  uint32 uid = 2;
  string target_account = 3;
}

message DelUserSpecialConcernResp{
  ga.BaseResp base_resp = 1;
  repeated ConcernUser special_concern_list = 2;
}

message SpecialConcernPushMsg{
  uint32 uid = 1;
  repeated ConcernUser special_concern = 2;
}

message ConcernUser{
  uint32 uid = 1;
  string account = 2;
}

/*获取IM搭讪特权剩余次数*/
message GetIMPrivilegeCountReq{
  ga.BaseReq base_req = 1;
  uint32 uid = 2;
}
message GetIMPrivilegeCountResp{
  ga.BaseResp base_resp = 1;
  uint32 uid = 2;
  uint32 cnt = 3;
}

/*使用一次IM搭讪特权*/
message UseIMPrivilegeReq{
  ga.BaseReq base_req = 1;
  uint32 uid = 2;
  uint32 target_uid = 3;
}
message UseIMPrivilegeResp{
  ga.BaseResp base_resp = 1;
}

// 需要与 super-player-svr.proto EntryAdvStatus 维持一致
enum EntryAdvStatus {
  ENUM_ENTRY_ADV_STATUS_NO_OPEN = 0;  // 从未开通普通超级玩家且从未开通SVIP的用户
  ENUM_ENTRY_ADV_STATUS_OPENING = 1;  // 普通超级玩家生效中或SVIP生效中用户，或超级玩家处于待生效且SVIP生效中，或超级玩家处于生效中且SVIP即将过期
  ENUM_ENTRY_ADV_STATUS_SOON_EXPIRE = 2;  // 普通超级玩家即将过期且SVIP即将过期用户，或普通超级玩家即将过期用户（仅剩普通超级玩家）、或SVIP即将过期用户（仅剩svip)
  ENUM_ENTRY_ADV_STATUS_EXPIRED = 3;  // 普通超级玩家已过期且SVIP已过期用户
}

// 会员主入口广告语配置
message SuperPlayerEntryAdvConf
{
   // 需要与 super-player-svr.proto SuperPlayerStatus 维持一致
   enum SuperPlayerStatus {
     ENUM_STATUS_NO_OPEN = 0;  // 未开通
     ENUM_STATUS_OPENING = 1;  // 开通中
     ENUM_STATUS_SOON_EXPIRE = 2;  // 即将过期
     ENUM_STATUS_EXPIRED = 3;  // 已过期
   }
   uint32 super_player_status = 1; // 会员状态 see SuperPlayerStatus svip版本后的版本废弃该字段
   repeated string adv_msg = 2;    // 入口文案
   uint32 red_dot_frequency = 3;  // 红点展示频率, 每 red_dot_frequency 天展示一次
   uint64 begin_ts = 4;  //开始时间
   uint64 end_ts = 5;  // 结束时间
   EntryAdvStatus new_status = 6; // 会员状态 see EntryAdvStatus
}

// 获取会员主入口广告语配置
message GetSuperPlayerEntryAdvConfReq
{
   ga.BaseReq base_req = 1;
}
message GetSuperPlayerEntryAdvConfResp
{
   ga.BaseResp base_resp = 1;
   repeated SuperPlayerEntryAdvConf adv_conf_list = 2;
}


// 在线隐身状态
enum OnlineSwitch
{
  ENUM_ONLINE_SWITCH_UNSPECIFIED = 0;
  ENUM_ONLINE_SWITCH_ONLINE = 1; // 在线
  ENUM_ONLINE_SWITCH_STEALTH = 2; // 隐身
}

// 挚友墙可见范围
enum FellowVisible
{
  ENUM_FELLOW_VISIBLE_UNSPECIFIED = 0;
  ENUM_FELLOW_VISIBLE_ALL = 1; // 允许所有人查看
  ENUM_FELLOW_VISIBLE_PLAYMATES_AND_FANS = 2; // 允许所有玩伴和粉丝查看
  ENUM_FELLOW_VISIBLE_MY_FELLOW = 3; // 仅允许我的挚友查看
  ENUM_FELLOW_VISIBLE_ME = 4; // 仅允许我自己查看
}

// 悄悄看全局开关
enum SneakilyReadSwitch
{
  ENUM_SNEAKILY_READ_SWITCH_UNSPECIFIED = 0;
  ENUM_SNEAKILY_READ_SWITCH_ON = 1; // 开启
  ENUM_SNEAKILY_READ_SWITCH_OFF = 2; // 关闭
}

// SVIP权益设置
message SVIPPrivilegeProfile
{
  OnlineSwitch online_switch = 1; // 在线状态
  FellowVisible fellow_visible = 3; // 挚友墙可见范围
  uint32 week_remain_stealth_times = 2; // 本周剩余隐身次数
  uint32 week_stealth_times = 4; // 本周已消耗隐身次数
  bool is_today_stealth = 5; // 今日是否使用隐身
  SneakilyReadSwitch sneakily_read_switch = 6; // 悄悄看全局开关
}

// 更新SVIP权益设置
message SetUserSVIPPrivilegeProfileReq
{
  ga.BaseReq base_req = 1;
  SVIPPrivilegeProfile privilege_profile = 2;
}
message SetUserSVIPPrivilegeProfileResp
{
  ga.BaseResp base_resp = 1;
  SVIPPrivilegeProfile privilege_profile = 2; // 最新设置
}

// 获取SVIP权益设置
message GetUserSVIPPrivilegeProfileReq
{
  ga.BaseReq base_req = 1;
}
message GetUserSVIPPrivilegeProfileResp
{
  ga.BaseResp base_resp = 1;
  SVIPPrivilegeProfile privilege_profile = 2;
}

// 提前使用SVIP隐身权益天数
message UseSVIPStealthAheadReq
{
  ga.BaseReq base_req = 1;
}
message UseSVIPStealthAheadResp
{
  ga.BaseResp base_resp = 1;
}

// 隐身推送
message SVIPPrivilegeStealthPushMsg
{
  enum PushType
  {
    ENUM_PUSH_TYPE_UNSPECIFIED = 0;
    ENUM_PUSH_TYPE_STEALTH_EXPIRING = 1; // 隐身即将到期
  }
  enum ExpiringType
  {
    ENUM_EXPIRING_TYPE_UNSPECIFIED = 0;
    ENUM_EXPIRING_TYPE_KEEPING = 1; // 可继续隐身
    ENUM_EXPIRING_TYPE_WEEK_TIMES_OVER = 2; // 无法继续隐身，本周隐身次数用尽
    ENUM_EXPIRING_TYPE_SVIP_EXPIRED = 3; // 无法继续隐身，SVIP次日过期
  }
  uint32 uid = 1;
  uint32 week_remain_stealth_times = 2; // 本周剩余隐身次数
  uint64 svip_expire_time = 3; // SVIP过期时间
  uint64 stealth_expire_time = 4; // 隐身过期时间
  PushType push_type = 5; // 推送类型
  ExpiringType expiring_type = 6; // 即将到期弹框类型
  uint64 server_time = 7; // 服务器时间
  bool is_sunday = 8; // 是否周日
}

enum RenewalReminderScene {
  ENUM_RENEWAL_REMINDER_SCENE_UNSPECIFIED = 0;
  ENUM_RENEWAL_REMINDER_SCENE_PERSONAL_PAGE = 1; // 个人主页
  ENUM_RENEWAL_REMINDER_SCENE_ENTRY_BANNER = 2; // 入口banner
  ENUM_RENEWAL_REMINDER_SCENE_SNEAKILY_READ = 3; // 悄悄看
  ENUM_RENEWAL_REMINDER_SCENE_STEALTH = 4; // 隐身在线
  ENUM_RENEWAL_REMINDER_SCENE_FELLOW_VISIBLE = 5; // 挚友墙上锁
  ENUM_RENEWAL_REMINDER_SCENE_STEALTH_ACCESS = 6; // 隐身访问
  ENUM_RENEWAL_REMINDER_SCENE_WHO_MIND_ME = 7; // 谁看过我
  ENUM_RENEWAL_REMINDER_SCENE_PERSONAL_CHANNEL = 8; // 个人房间
}

enum RenewalReminderType {
  ENUM_RENEWAL_REMINDER_TYPE_UNSPECIFIED = 0;
  ENUM_RENEWAL_REMINDER_TYPE_TODAY_NOT_AGAIN = 1; // 关闭后今日不再提醒
  ENUM_RENEWAL_REMINDER_TYPE_DAILY_TIMES = 2; // 每日提醒次数
}

// 获取续费提醒
message GetRenewalReminderReq {
  ga.BaseReq base_req = 1;
  RenewalReminderScene scene = 2; // 场景
}

message GetRenewalReminderResp {
  ga.BaseResp base_resp = 1;
  string reminder_title = 2; // 续费提醒标题
  string reminder = 3; // 续费提醒文案
  RenewalReminderType reminder_type = 4; // 续费提醒类型
  uint32 reminder_times = 5; // 续费提醒次数（若为每日按次提醒则返回该次数）
  string pic_url = 6; // 图片url
  string banner_text = 7; // banner文案
  bool is_half_screen = 8; // 是否跳转半屏
}

message ReportSneakilyReadReq {
  ga.BaseReq base_req = 1;
}
message ReportSneakilyReadResp {
  ga.BaseResp base_resp = 1;
}

// 检查是否展示优惠券弹窗
message CheckCouponPopUpReq {
  ga.BaseReq base_req = 1;
  CouponPopUpScene scene = 2; // 场景
}
message CheckCouponPopUpResp {
  ga.BaseResp base_resp = 1;
  bool is_show = 2;  //是否展示 0 不展示 1 展示
  string resource_url = 3;  //资源url
  string resource_md5 = 4;  //资源md5
}

//会员优惠券弹窗场景
enum CouponPopUpScene {
  ENUM_RENEWAL_POPUP_SCENE_UNSPECIFIED = 0;
  ENUM_RENEWAL_POPUP_SCENE_PERSONAL_PAGE = 1; // 个人主页
  ENUM_RENEWAL_POPUP_SCENE_ENTRY_BANNER = 2; // 拉起在线状态设置
  ENUM_RENEWAL_POPUP_SCENE_SUPER_PLAY_PAGE = 3; // 会员主页
  ENUM_RENEWAL_POPUP_SCENE_WHO_MIND_ME = 7; // 谁看过我
}

message CouponPopUpLimit {
  CouponPopUpScene scene = 1; // 场景
  uint32 day_limit = 2;    // 每日弹窗次数限制
  uint32 week_limit = 3;   // 每周弹窗次数限制
  uint32 cooldown_time = 4; // 弹窗冷却时间

}

// 获取优惠券弹窗
message GetCouponPopUpLimitReq {
  ga.BaseReq base_req = 1;
}
message GetCouponPopUpLimitResp {
  ga.BaseResp base_resp = 1;
  repeated CouponPopUpLimit limit_list = 2; // 弹窗限制列表
}