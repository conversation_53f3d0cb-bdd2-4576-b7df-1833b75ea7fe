syntax = "proto3";

package ga.fellow_logic;

import "ga_base.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/fellow-logic";


enum FellowType {
  ENUM_FELLOW_TYPE_UNKNOWN = 0;  // 未知
  ENUM_FELLOW_TYPE_BRO = 1;  // 基友
  ENUM_FELLOW_TYPE_LADYBRO = 2;  // 闺蜜
  ENUM_FELLOW_TYPE_INTIMATE = 3;  // 知己
  ENUM_FELLOW_TYPE_PARTNER = 4;  // 搭子
}

enum FellowBindType {
  ENUM_FELLOW_BIND_TYPE_UNKNOWN = 0;  // 没有绑定关系
  ENUM_FELLOW_BIND_TYPE_UNIQUE = 1;  // 唯一
  ENUM_FELLOW_BIND_TYPE_MULTI = 2;  // 非唯一
}

enum FellowBindStatus {
  ENUM_FELLOW_BIND_STATUS_NORMAL = 0;  // 正常状态
  ENUM_FELLOW_BIND_STATUS_APART = 1;  // 解绑中
}

enum BanType {
  BAN_TYPE_NORMAL = 0; // 正常
  BAN_TYPE_TMP = 1; // 临时封禁
  BAN_TYPE_PERMANENT = 2; // 永久封禁
}


// 铭牌信息
message PlateUrl {
  string cp_unique = 1;   // cp战铭牌URL
  string date_unique = 2;  // 相亲房铭牌URL
}

message FellowBackground {
  string background_url = 1; // 关系背景
  uint32 source_type = 2; // 关系背景的资源类型，FellowSourceType
  string md5 = 3; // 关系背景的md5
  string background_img = 4; // 动态背景的背景底图，静态不填
}

enum SwitchEnum {
  SWITCH_OFF = 0; // 无切换
  SWITCH_ON = 1; // 可切换
}


message RareInfo {
  uint32 rare_id = 1;              //稀缺关系ID   
  uint32 sub_rare_id = 2;          //我的组合关系ID
  uint32 day_count = 3;            //在一起的天数
  uint32 remain_count = 4;         //剩余天数
  bool bind_status = 5;            //绑定状态
}

// 稀缺信息
message RareTabInfo {
  uint32 to_uid = 1;                 //对方uid
  string to_account = 2;            //账号
  string to_nick_name = 3;           //昵称
  uint32 bind_type = 4;             // 绑定类型 FellowBindType
  uint32 fellow_type = 5;           //see enum FellowType
  string fellow_name = 6;           //挚友关系名称
  RareInfo out_rare = 7;            //外显稀缺关系信息
  FellowBackground present_bg = 8; // 挚友信物背景
  uint32 rare_count = 9;           //可切换的稀缺关系列表数量
  UserProfile user_profile = 11;
  uint32 to_sex = 12; // 性别
}

// 段位信息 （一个等级范围属于一个段位）
message GradingInfo {
  uint32 level = 1; // 等级
  string grading_name = 2; // 等级所处段位名称
  string grading_icon = 3; // 段位图标
  string grading_color = 4; //段位颜色
}

// 挚友信息
message FellowInfo {
  int64 uid = 1; //uid
  string account = 2; //账号
  string nick_name = 3; //昵称
  uint32 fellow_level = 4; //挚友等级
  uint32 fellow_type = 5; //see enum FellowType
  uint32 day_cnt = 6; // 在一起的天数
  string fellow_name = 7; //挚友关系名称
  uint32 bind_type = 8; // 绑定类型 FellowBindType
  uint32 fellow_point = 9; // 挚友值
  FellowBackground background = 10; // 关系背景
  string cp_present_icon = 17;  // CP位置信物 URL
  PlateUrl plate_url = 18;  // PlateUrl 铭牌信息
  uint32 bind_status = 19;  // 绑定状态  see FellowBindStatus
  BanType ban_type = 20; // 封禁状态
  RareInfo bind_rare = 21; //绑定的稀缺关系
  UserProfile user_profile = 22; // 新用户信息(包含神秘人特权)
  string fellow_icon = 23;  //对应的小icon
  string card_colour = 24;  //资料卡颜色
  repeated string room_msg_colour = 25;  //进房消息颜色
  string ligature_url = 26; //挚友连线资源
  GradingInfo grading_info = 27; // 段位信息
  FellowHouseCfg cfg = 28; // 小屋信息
  uint32 sex = 29; // 性别
  }


enum InviteStatus{
  ENUM_INVITE_STATUS_NO_INVITE = 0;  // 未申请
  ENUM_INVITE_STATUS_INVITED = 1;  // 已申请
  ENUM_INVITE_STATUS_SUCCESS = 2;  // 申请成功
  ENUM_INVITE_STATUS_FAILED = 3;  // 申请失败
  ENUM_INVITE_STATUS_CANCELED = 4;  // 申请已撤销
  ENUM_INVITE_STATUS_TIMEOUT = 5;  // 申请超时退回
}

enum FellowSourceType{
  ENUM_FELLOW_SOURCE_TYPE_UNKNOWN = 0;  // 未知
  ENUM_FELLOW_SOURCE_TYPE_PIC = 1;  // 图片
  ENUM_FELLOW_SOURCE_TYPE_VAP = 2;  // VAP
}

// 申请挚友页面提供的用户信息
message FellowInviteUser {
  int64 uid = 1; //uid
  string account = 2; //账号
  string nick_name = 3; //昵称
  uint32 fellow_point = 4; // 挚友值
  uint32 invite_status = 5; // 申请状态
  uint32 sex = 6; // 1男 0女
  string invite_id = 7; // 邀请函id
}

// 信物信息
message FellowPresentInfo {
  uint32 item_id = 1; //礼物id
  string name = 2; //礼物名
  uint32 value = 3; // 价格
  string icon = 4; // 图标
  uint32 price_type = 5; // 1红钻2t豆 , PRESENT_PRICE_TYPE
  FellowBackground unique_background = 6; // 唯一关系背景皮肤
  FellowBackground multi_background = 7; // 非唯一关系背景皮肤
  uint32 already_own = 8; // 是否已经拥有，1 已拥有
}

// 邀请函信息
message FellowInviteInfo {
  string invite_id = 1; //邀请函id
  int64 from_uid = 2; //发送人uid
  string from_account = 3; // 发送人账号
  string from_nickname = 4; // 发送人昵称
  uint32 bind_type = 5; // 绑定类型 FellowBindType
  uint32 fellow_type = 6; // 非唯一绑定类型 FellowType
  uint32 fellow_point = 7; // 与邀请人的挚友值
  uint32 create_time = 8; // 邀请函发送时间
  string fellow_name = 9;
  int64 to_uid = 10; //接收人uid
  string to_account = 11; // 接收人账号
  string to_nickname = 12; // 接收人昵称
  uint32 status = 13; // 邀请函当前状态，req不用填 InviteStatus
  bool with_unlock = 14;
  uint32 from_sex = 15; // 性别 0女1男
  uint32 to_sex = 16; // 性别 0女1男
  FellowPresentInfo present_info = 17; // 信物信息
  uint32 unlock_price = 18;  // 非唯一绑定 - 解锁花费
  uint32 channel_id = 19; // 发起邀请的房间id
  uint32 end_time = 20; // 邀请的到期时间
  string from_fellow_name = 21; // 原来关系名称
}

// 邀请函信息
message FellowTypeInfo {
  uint32 multi_fellow_type = 1; // FellowType
  string multi_fellow_name = 2; // 关系名字
}

message GetFellowListReq{
  ga.BaseReq base_req = 1;
  int64 uid = 2; //uid
}

message GetFellowListResp{
  ga.BaseResp base_resp = 1;
  int64 uid = 2; //uid
  FellowInfo unique_fellow = 3; //唯一挚友
  repeated FellowInfo multi_fellow = 4; // 非唯一挚友列表
  uint32 fellow_position_cnt = 5;  //非唯一挚友坑位数量
  uint32 pending_invite_cnt = 6; // 待处理的申请数量
  UnlockInfo unlock_info = 7; // 解锁信息
  uint32 send_invite_count = 8; //我发出的邀请数量
  repeated RareTabInfo rare_tab_list = 9; //稀缺关系tab列表
  bool svip_visible = 10; //svip 对自己是否可见
}

message UnlockInfo{
  uint32 unlock_price = 1; // 解锁自身下一个挚友位的价格
  uint32 unlock_level = 2; // 解锁自身下一个挚友位所需等级
  uint32 unlock_site = 3;  // 下一个挚友位是第几个
}

message GetFellowCandidateListReq{
  ga.BaseReq base_req = 1;
  uint32 bind_type = 2;  // FellowBindType
  uint32 page = 3; // 页数
  uint32 count =4; // 每页的个数
}

message GetFellowCandidateListResp{
  ga.BaseResp base_resp = 1;
  repeated FellowInviteUser fellow_list = 2; // 可申请的挚友列表
  bool is_reach_end = 3; // 是否到达末尾
}

message GetFellowCandidateInfoReq{
  ga.BaseReq base_req = 1;
  uint32 target_uid = 2;
  bool change_fellow_type = 3;//是否换绑关系
}

message GetFellowCandidateInfoResp{
  ga.BaseResp base_resp = 1;
  repeated FellowPresentInfo present_info = 2;  // 礼物信息
  FellowInviteUser user_info = 3;    // 用户信息
  repeated FellowTypeInfo multi_fellow_option_list = 4;  // 非绑定 - 可选的关系列表
  bool has_multi_fellow_field = 5;   // 非绑定 - 对方是否还有栏位
  uint32 unlock_price = 6;  // 非绑定 - 帮对方解锁花费
  bool has_cp_field = 7;  // 对方是否可以绑定cp
}

message SendFellowInviteReq{
  ga.BaseReq base_req = 1;
  uint32 target_uid = 2;
  uint32 bind_type = 3; // FellowBindType
  uint32 present_id = 4;
  uint32 fellow_type = 5; // FellowType
  bool with_unlock = 6; // 是否带有解锁
}

message SendFellowInviteResp{
  ga.BaseResp base_resp = 1;
  int64 remain_currency = 2;
  int64 remain_tbean = 3;
}

message GetFellowInviteListReq{
  ga.BaseReq base_req = 1;
}

message GetFellowInviteListResp{
  ga.BaseResp base_resp = 1;
  repeated  FellowInviteInfo invite_list = 2;
}

message GetFellowInviteInfoByIdReq{
  ga.BaseReq base_req = 1;
  string invite_id = 2; //邀请函id
}

message GetFellowInviteInfoByIdResp{
  ga.BaseResp base_resp = 1;
  FellowInviteInfo invite_info = 2;
}

message HandleFellowInviteReq{
  ga.BaseReq base_req = 1;
  string invite_id = 2; //邀请函id
  bool is_accept_invite = 3; // 是否接受邀请
}

message HandleFellowInviteResp{
  ga.BaseResp base_resp = 1;
}

message CheckFellowInviteReq{
  ga.BaseReq base_req = 1;
  uint32 bind_type = 2;
}

message CheckFellowInviteResp{
  ga.BaseResp base_resp = 1;
  bool reach_limit = 2; // 是否达到邀请上限
  string cp_name = 3 ; // 如果是唯一绑定关系，已经绑定的用户昵称
  uint32 cp_sex = 4; // 如果是唯一绑定关系，已经绑定的用户性别 0女1男
  UnlockInfo unlock_info = 5; // 自己的解锁信息
}

message UnlockFellowPositionReq{
  ga.BaseReq base_req = 1;
}

message UnlockFellowPositionResp{
  ga.BaseResp base_resp = 1;
  int64 remain_tbean = 2;
}

message UnboundFellowReq{
  ga.BaseReq base_req = 1;
  uint32 target_uid = 2;
}

message UnboundFellowResp{
  ga.BaseResp base_resp = 1;
}

message CancelUnboundFellowReq{
  ga.BaseReq base_req = 1;
  uint32 target_uid = 2;
}

message CancelUnboundFellowResp{
  ga.BaseResp base_resp = 1;
}

message CancelFellowInviteReq{
  ga.BaseReq base_req = 1;
  string invite_id = 2;
}

message CancelFellowInviteResp{
  ga.BaseResp base_resp = 1;
  int64 remain_currency = 2;
  int64 remain_tbean = 3;
}


message GetFellowPointReq{
  ga.BaseReq base_req = 1;
  string fellow_account = 2;  //IM页面无UID 挚友account
}

message GetFellowPointResp{
  ga.BaseResp base_resp = 1;
  uint32 fellow_uid = 2;  //挚友UID
  uint32 fellow_point = 3; // 当前挚友值
  uint32 fellow_level = 4; //挚友等级
  uint32 current_level_point = 5; //当前等级分数
  uint32 next_level_point = 6; //下一等级分数
  string next_award_level = 7; //下一奖励等级描述
  string available_award = 8; //可领取的奖励描述
}

message GetFellowPresentDetailReq{
  ga.BaseReq base_req = 1;
  uint32 target_uid = 2;
}

message GetFellowPresentDetailResp{
  ga.BaseResp base_resp = 1;
  repeated FellowPresentInfo present_list = 2;  // 礼物信息
  FellowPresentInfo fellow_present = 3; // 已绑定的信物
  uint32 fellow_level = 4;
  string fellow_name =5;
  uint32 day_cnt = 6; // 在一起的天数
  uint32 bind_type = 7; // 唯一/非唯一
}

message SendFellowPresentReq{
  ga.BaseReq base_req = 1;
  uint32 target_uid = 2;
  uint32 present_id = 3;
}

message SendFellowPresentResp{
  ga.BaseResp base_resp = 1;
  int64 remain_currency = 2;
  int64 remain_tbean = 3;
  PresentSendItemSimpleInfo present_info =4;
}

message PresentSendItemSimpleInfo
{
  uint32 item_id = 1;
  uint32 count = 2;
  uint32 show_effect = 3;	// 播放特效 ga::PRESENT_SHOW_EFFECT_TYPE
  uint32 show_effect_v2 = 4;	// 播放特效 ga::PRESENT_SHOW_EFFECT_TYPE_V2
  string present_icon = 5;
}

message GetFellowInfoByUidReq{
  ga.BaseReq base_req = 1;
  uint32 target_uid = 2; //账号
}

message GetFellowInfoByUidResp{
  ga.BaseResp base_resp = 1;
  FellowInfo fellow_info = 2;
}

message ChannelSendFellowPresentReq{
  ga.BaseReq base_req = 1;
  uint32 target_uid = 2;
  uint32 present_id = 3;
  uint32 channel_id = 4;
}

message ChannelSendFellowPresentResp{
  ga.BaseResp base_resp = 1;
  int64 remain_currency = 2;
  int64 remain_tbean = 3;
}

message SendChannelFellowInviteReq{
  ga.BaseReq base_req = 1;
  uint32 target_uid = 2;
  uint32 bind_type = 3; // FellowBindType
  uint32 present_id = 4;
  uint32 fellow_type = 5; // FellowType
  bool with_unlock = 6; // 是否带有解锁
  uint32 channel_id = 7; // 房间id
}

message SendChannelFellowInviteResp{
  ga.BaseResp base_resp = 1;
  int64 remain_currency = 2;
  int64 remain_tbean = 3;
}


message HandleChannelFellowInviteReq{
  ga.BaseReq base_req = 1;
  string invite_id = 2; //邀请函id
  bool is_accept_invite = 3; // 是否接受邀请
  uint32 channel_id = 4; // 房间
}

message HandleChannelFellowInviteResp{
  ga.BaseResp base_resp = 1;
}

// 邀请函信息
message ChannelFellowMsg {
  int64 from_uid = 1; //发送人uid
  string from_account = 2; // 发送人账号
  string from_nickname = 3; // 发送人昵称
  uint32 bind_type = 4; // 绑定类型 FellowBindType
  uint32 fellow_type = 5; // 非唯一绑定类型 FellowType
  string fellow_name = 6;
  int64 to_uid = 7; //接收人uid
  string to_account = 8; // 接收人账号
  string to_nickname = 9; // 接收人昵称
  uint32 from_sex = 10; // 性别 0女1男
  uint32 to_sex = 11; // 性别 0女1男
  FellowPresentInfo present_info = 12; // 信物信息
  uint32 status = 13; // see InviteStatus
}


message GetRoomFellowListReq{
  ga.BaseReq base_req = 1;
  uint32 uid = 2; // 查看者 uid
  repeated uint32 next_uid = 3; // 相邻UID列表
  repeated uint32 on_mic_uid = 4; // 麦上不相邻UID列表
}

message GetRoomFellowListResp{
  ga.BaseResp base_resp = 1;
  uint32 uid = 2; //uid
  repeated FellowInfo fellow_list = 3; // 三个挚友信息
  bool svip_visible = 4; //svip权益  是否可见
}


message GetAllChannelFellowInviteReq{
  ga.BaseReq base_req = 1;
  uint32 channel_id = 2;
}

message GetAllChannelFellowInviteResp{
  ga.BaseResp base_resp = 1;
  repeated  FellowInviteInfo send_invite_list = 2;   // 我发出的邀请函
  repeated  FellowInviteInfo received_invite_list = 3;   // 我收到的邀请函
}

message GetChannelFellowCandidateInfoReq{
  ga.BaseReq base_req = 1;
  uint32 target_uid = 2;
  uint32 item_id = 3;
}

// 可选的关系信息
message FellowOptionInfo {
  uint32 bind_type = 1;  // FellowBindType
  uint32 fellow_type = 2; // FellowType
  string fellow_name = 3; // 关系名字
}

message GetChannelFellowCandidateInfoResp{
  ga.BaseResp base_resp = 1;
  FellowPresentInfo present_info = 2;  // 礼物信息
  FellowInviteUser user_info = 3;    // 用户信息
  repeated FellowOptionInfo fellow_option_list = 4;  // 可选的关系列表
  bool has_multi_fellow_field = 5;   // 非唯一 - 对方是否还有栏位
  uint32 unlock_price = 6;  // 非唯一 - 帮对方解锁花费
  bool has_cp_field = 7;  // 唯一 - 对方是否可以绑定唯一关系
}

message GetOnMicFellowListReq{
  ga.BaseReq base_req = 1;
  uint32 channel_id = 2; //当前房间
}


// 麦位挚友信息
message MicFellowInfo {
  uint32 uid = 1; //uid
  uint32 fellow_uid = 2; //
  uint32 fellow_level = 3; //挚友等级
  uint32 fellow_type = 4; //挚友类型 see enum FellowType
  uint32 bind_type = 5; // 绑定类型 FellowBindType
  RareInfo current_rare = 6; // 当前绑定稀缺关系id
  string ligature_url = 7; //挚友连线资源
}


message MicFellowInfoChangeInfo {
  repeated MicFellowInfo mic_fellow = 1;
}


message GetOnMicFellowListResp{
  ga.BaseResp base_resp = 1;
  repeated MicFellowInfo mic_fellow = 2;
}


//我发出的挚友邀请列表
message GetSendInviteListReq{
  ga.BaseReq base_req = 1;
}

message GetSendInviteListResp{
  ga.BaseResp base_resp = 1;
  repeated  FellowInviteInfo invite_list = 2;
}

//换绑关系
message ChangeFellowBindTypeReq {
    ga.BaseReq base_req = 1;
    uint32 op_uid = 2;
    uint32 target_uid = 3;
    uint32 from_bind_type = 4;  //绑定类型 see FellowBindType
    uint32 from_fellow_type = 5;  //挚友类型 see FellowType
    uint32 to_bind_type = 6;  //绑定类型 see FellowBindType
    uint32 to_fellow_type = 7;  //挚友类型 see FellowType
    uint32 present_id = 8;
    bool with_unlock = 9; // 是否带有解锁
}

message ChangeFellowBindTypeResp {
  ga.BaseResp base_resp = 1;
  int64 remain_tbean = 2;
}


// 获取所有稀缺关系配置
message GetRareConfigReq{
  ga.BaseReq base_req = 1;
}

message AnimationConfig {
  string resource_url = 1;  //动画资源地址 zip包
  string md5 = 2;  //动画MD5
}

 
message SubRareConfig {
  uint32 sub_rare_id = 1; // 组合ID  
  string name = 2; // 组合稀缺关系名称
  string rare_flag = 3; // 组合关系进房/im标签
}

message ConnectedForMic{
  string left = 1;  // 单方1在左边时的样式
  string right = 2; // 单方1在右边的样式
}

//MsgNotifyPictures 消息通知图片配置
message MsgNotifyPictures {
  string origin = 1;      // 原图
  string thumbnail = 2;   // 缩略图
}

 
message RareConfig {
  uint32 rare_id = 1;                         // rare_id 
  string name = 2;                            // 稀缺关系名称
  repeated SubRareConfig sub_rare_cfg = 3;    // 组合关系ID和名称
  AnimationConfig cp_animation = 4;           // CP战结算动画
  ConnectedForMic mic_connected = 5;          // 麦上连线样式
  string rare_flag = 6;                       // 非组合关系的进房/im标签
  string card_color = 7;                      // 资料卡挚友标签（单色值）
  FellowBackground cp_bg = 8;                 // 稀缺关系CP位背景
  FellowBackground mid_bg = 9;                // 稀缺关系非CP位背景
  MsgNotifyPictures msg_pictrures = 10;       // 消息通知图片配置
  string friend_space_background = 11;        // 挚友空间场景关系图
}


message GetRareConfigResp{
  ga.BaseResp base_resp = 1;
  repeated RareConfig rare_list = 2;
 }


// 切换绑定的稀缺关系
message SetBindRelationReq{
  ga.BaseReq base_req = 1;
  uint32 uid = 2;
  uint32 to_uid = 3;
  uint32 rare_id = 4; 
  uint32 sub_rare_id = 5; 
  bool bind = 6;   // 取消绑定false 绑定 true
}


// 切换绑定的稀缺关系
message SetBindRelationResp{
  ga.BaseResp base_resp = 1;
  uint32 uid = 2;
  uint32 to_uid = 3;
  uint32 rare_id = 4;
  uint32 sub_rare_id = 5; 
  bool bind = 6;   // 取消绑定false 绑定 true
  uint32 bind_type = 7;//挚友绑定类型
}


// 获取房间可用稀缺关系配置
message GetChannelRareConfigReq{
  ga.BaseReq base_req = 1;
  uint32 channel_id = 2;  
}

message GetChannelRareConfigResp{
  ga.BaseResp base_resp = 1;
  uint32 channel_id = 2;  
  string intro_url = 3; //房间介绍URL
  repeated RareConfig rare_list = 4; 
  string entrance_url = 5; //房间入口URL
}


// 获取两个UID的所有稀缺关系列表
message GetRareListReq{
  ga.BaseReq base_req = 1;
  uint32 uid = 2;
  uint32 to_uid = 3;  
 }

message GetRareListResp{
  ga.BaseResp base_resp = 1;
  uint32 uid = 2;
  uint32 to_uid = 3;                 //对方uid
  string to_account = 4;            //账号
  string to_nick_name = 5;           //昵称
  uint32 bind_type = 6;             // 绑定类型 FellowBindType
  uint32 fellow_type = 7;           //see enum FellowType
  string fellow_name = 8;           //挚友关系名称
  repeated RareInfo rare_list = 9;  //可切换的稀缺关系列表
  FellowBackground present_bg = 10; // 挚友信物背景
 }

 message DelRareReq {
   ga.BaseReq base_req = 1;
   uint32 uid = 2;
   uint32 to_uid = 3;
   uint32 rare_id = 4;
   uint32 sub_rare_id = 5; // 非组合关系传0, 组合关系传0则所有该组合关系都被删除
 }

 message DelRareResp {
   ga.BaseResp base_resp = 1;
 }

message FellowHouseRes {
  enum ResType {
    ENUM_RES_TYPE_UNKNOWN = 0;
    ENUM_RES_TYPE_VAP = 1;
    ENUM_RES_TYPE_LOTTIE = 2;
  }
  uint32 res_type = 1;         // 资源类型
  string house_res = 2;        // 小屋资源
  string house_res_md5 = 3;    // 小屋资源md5
}

message FellowHouseCfg {
  uint32 house_id = 1;      // 小屋配置ID
  string name = 2;          // 小屋名称
  string icon = 3;          // 小屋图标
  FellowHouseRes house_res = 4;     // 小屋资源
  uint32 bonus_ratio = 5;   // 加成比例
  uint32 discount_price = 6;// 优惠价(实际价格)，T豆
}

message FellowHouseInfo {
  FellowHouseCfg cfg = 1;
  ga.UserProfile cp_user = 2;
  int64 expire_ts = 3;    // 过期时间
  uint32 fellow_level = 4; //挚友等级
  uint32 fellow_type = 5; //see enum FellowType
  uint32 day_cnt = 6; // 在一起的天数
  string fellow_name = 7; //挚友关系名称
  uint32 bind_type = 8; // 绑定类型 FellowBindType
  GradingInfo grading_info = 9; // 段位信息
}

// 获取用户正在使用挚友小屋列表
message GetFellowHouseInuseListRequest {
  ga.BaseReq base_req = 1;
  uint32 uid = 2;
}

message GetFellowHouseInuseListResponse {
  ga.BaseResp base_resp = 1;
  repeated FellowHouseInfo list = 2;
  uint32 cp_house_cnt = 3;    // cp关系的小屋套数
  bool svip_visible = 4; //svip权益  是否可见
}

// 获取入口信息
message GetFellowHouseEntryInfoRequest {
  ga.BaseReq base_req = 1;
  uint32 uid = 2;
}

message GetFellowHouseEntryInfoResponse {
  ga.BaseResp base_resp = 1;
  bool has_entry = 2;        // 是否展示入口
  FellowHouseInfo info = 3; // 小屋信息
}

// 个人资料卡 挚友小屋信息 【废弃】
message GetFellowCardInfoRequest {
  ga.BaseReq base_req = 1;
  uint32 target_uid = 2;
}

message SimpleHouseInfo {
  FellowHouseCfg cfg = 1;
  ga.UserProfile cp_user = 2;
}

// 个人资料卡 挚友小屋信息 【废弃】
message GetFellowCardInfoResponse {
  ga.BaseResp base_resp = 1;
  repeated SimpleHouseInfo list = 2; // 小屋信息列表
  bool svip_visible = 3; //svip权益  是否可见
}

message FellowUpgradeAnimationPushMsg {
  string animation_url = 1; // 动画资源
  string md5 = 2; // 资源md5
  string extend_json = 3; // 通用扩展字段
}

message ExpiringHouseInfo{
    uint32 house_id = 1;
    ga.UserProfile cp_user = 2;  // 挚友信息
    string house_icon = 3;
    string house_name = 4;
    int64 exp_ts = 5;        // 过期时间戳
    string popup_id = 6;    // 弹窗id
    string highlight_text = 7;  // 高亮文案
}

// 获取用户即将过期小屋列表
message GetExpiringFellowHouseListRequest {
    ga.BaseReq base_req = 1;
}

message GetExpiringFellowHouseListResponse {
    ga.BaseResp base_resp = 1;
    repeated ExpiringHouseInfo house_list = 2;
    string popup_title = 3;        // 弹窗标题
    string popup_format_text = 4;  // 弹窗自定义文案，
    /* format占位符：存在则替换，不存在则不需替换：
       文案中的{username}替换成cp_user昵称，
       {hightlight}替换为ExpiringHouseInfo.highlight_text  
       {houseName}替换为小屋名称
        */
}