syntax = "proto3";

package ga.revenue;

import "ga_base.proto";
import "numeric_logic/numeric-logic_.proto";
import "userpresent/userpresent_.proto";
import "present_go_logic/present-go-logic_.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/revenue";

// 进房场景 营收扩展信息
message RevenueEnterChannelExtend {
  // 进房荣誉信息
  GloryInfo glory_info = 1;
  repeated ga.userpresent.PresentBoxInfo present_box_list = 2; // 礼物开盒列表
  bool is_has_pgc_ticket = 3;  // 是否有pgc房间体验券
  string roi_cert_url = 4; // ROI渠道用户标识
  ga.SuperPlayerLevel super_player_level = 5; // SVIP
  ga.NobilityInfo nobility_info = 6; // 贵族等级信息
  VACpUserFollowEnterChannelOpt va_follow_opt = 7 [deprecated=true];  // 虚拟形象跟随进房特效opt [废弃]
  VACpEnterFollowEffectOpt va_effect_opt = 8;  // 虚拟形象跟随进房特效opt
  string high_cert_url = 9; // 壕用户标识
  repeated VACpEnterFollowEffectOpt effect_list = 10;  // 通用跟随进房特效opt
  repeated ga.present_go_logic.EmperorBoxInfo emperor_box_list = 11; // 帝王套开盒列表
  VirtualImageEnterFollowOpt virtual_image_enter_follow_opt = 12; // 虚拟形象二期跟随进房特效opt
}

// 公屏场景 营收扩展信息
message RevenueChannelImExtend {
  bool is_has_pgc_ticket = 1;  // 是否有pgc房间体验券
  string roi_cert_url = 2; // ROI渠道用户标识
  ga.SuperPlayerLevel super_player_level = 5; // SVIP
  ga.NobilityInfo nobility_info = 6; // 贵族等级信息
  string high_cert_url = 7; // 壕用户标识
  string title_cert = 8;  // 冠名标识
}

message GloryInfo {
  //（财富魅力榜、活动榜排名）
  repeated ga.numeric_logic.GloryRank user_rank_list = 1;
  // 房间内荣誉角标
  GlorySubscript glory_subscript = 2;
}

message GlorySubscript {
  string head_url = 1; // 头像标识
  string nickname_url = 2; //- 昵称处底色框
  string pc_head_url = 3;  // PC头像
}

// 互踩互赞
message RevenueUserVisitorRecord {
  ga.SuperPlayerLevel super_player_level = 1; // SVIP
}

// VACpUserFollowEnterChannelOpt 废弃 2024.6.28
message VACpUserFollowEnterChannelOpt{
    uint32 uid = 1;
    uint32 follow_uid = 2;         // 被跟随用户uid
    string effect_url = 3;         // 虚拟双人形象进房效果资源url
    string effect_md5 = 4; 
    string effect_extend_json = 5 [deprecated=true]; // 拓展特效json 
    uint32 user_char = 6;          // see virtual_avatar_logic.proto, ResCharacterType
    uint32 followed_user_char = 7;   // see virtual_avatar_logic.proto, ResCharacterType
    string custom_info = 8;        // 自定义信息
}

message VACpEnterFollowEffectOpt{
    string effect_url = 1;         // 虚拟双人形象进房效果资源url
    string effect_md5 = 2; 
    string effect_extend_json = 3; // 拓展特效json
    uint32 user_char = 4;          // see virtual_avatar_logic.proto, ResCharacterType 进房用户人物形象类型
    uint32 followed_user_char = 5;   // see virtual_avatar_logic.proto, ResCharacterType 被跟随用户的人物形象类型
}

message UserVirtualImageInfo {
  ga.UserProfile user_profile = 1;
  repeated uint32 resource_id_list = 2; // 用户穿戴的虚拟形象资源id列表
}

message VirtualImageEnterFollowOpt {
    UserVirtualImageInfo user_virtual_image_info = 1; // 进房用户的虚拟形象
    UserVirtualImageInfo followed_user_virtual_image_info = 2;  // 被跟随用户的虚拟形象
}