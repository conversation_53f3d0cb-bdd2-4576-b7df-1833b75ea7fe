syntax = "proto3";

package ga.channel_recommend_logic;

import "ga_base.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/channel-recommend-logic";

// 推荐抽奖房间信息
message RecLotteryChInfo {
    uint32 channel_id = 1;
    string name = 2;  // 房间名称
    string icon_md5 = 3;   //房间头像
    uint32 lottery_end_ts = 4;  // 抽奖结束时间
    string gift_name = 5;
    string gift_icon = 6;  // 礼物图
    uint32 gift_cnt = 7;  // 礼物数量
    string gift_value_text = 8;  // 礼物价值文案
    uint32 channel_type = 9; // 频道类型 see ga::ChannelType
    uint32 bind_id = 10;
}

// 获取抽奖房间推荐列表
message GetRecLotteryChListRequest {
   ga.BaseReq base_req = 1;
   uint32 page = 2;  // 从 1开始
   uint32 page_size = 3;
}
message GetRecLotteryChListResponse {
   ga.BaseResp base_resp = 1;
   repeated RecLotteryChInfo ch_list = 2;
   uint32 next_page = 3;  // 为0表示结束
}


//房间基础信息
message ChannelBaseInfo {
    uint32 channel_id = 1;
    string name = 2;
    string icon_md5 = 3;   //房间头像
    uint32 channel_type = 4; // 房间类型 see channel_.proto ga::ChannelType
    uint32 bind_id = 5; // 房间绑定id
}

// 用户基础信息
message UserBaseInfo {
    uint32 uid = 1;
    string nick_name = 2;
    string head_img = 3;  // md5
    int32  sex = 4;   // 性别 
    string account = 5;
}

// 房间顶部浮窗
message ChannelTopOverLay {
   // 浮窗类型
   enum EOverLayType {
        E_OVER_LAY_TYPE_UNSPECIFIED = 0;   //无效
        E_OVER_LAY_TYPE_CHANNEL = 1;   // 房间样式浮窗
        E_OVER_LAY_TYPE_USER = 2;  // 用户样式浮窗
   }
   uint32 type = 1;  //see EOverLayType
   ChannelBaseInfo channel_info = 2;
   UserBaseInfo user_info = 3;  
   UserBaseInfo followed_user_info = 4;  //被跟随用户信息
   string channel_tag_text = 5;  // 房间标签文案
   string rec_text = 6;  // 推荐语
   uint32 rec_poor_source = 7;// 推荐池来源 see channel_.proto ERecPoorSourceV2
}

// 房间顶部浮窗
message GetChannelTopOverLayRequest {
   ga.BaseReq base_req = 1;
   uint32 refresh_cnt = 2; // 请求下发次数，客户端本地存储 废弃
   map<uint32,uint32> map_type_cnt = 3;   // 请求下发次数，客户端本地存储 type see EOverLayType
}
message GetChannelTopOverLayResponse {
   ga.BaseResp base_resp = 1;
   ChannelTopOverLay top_over_lay = 2; 
   uint32 interval_ts = 3;  // 下次查询间隔时间，单位秒
}


// 房间跟随浮层信息
message ChannelFollowFloatMsg {
   UserBaseInfo follow_user_info = 1; // 跟随用户信息
   uint32 channel_id = 2;  // 欢迎房间
   string msg_text = 3;    // 房间内浮窗文本
}

enum RevenueSwitchHubType{
    REVENUE_SWITCH_HUB_TYPE_UNSPECIFIED = 0;
    REVENUE_SWITCH_HUB_TYPE_SNACKBAR = 1; // 隐私中 顶部浮窗
}

// 营收开关
message GetRevenueSwitchHubRequest {
    ga.BaseReq base_req = 1;
}
message GetRevenueSwitchHubResponse {
    ga.BaseResp base_resp = 1;
    map<uint32,bool> is_open_map = 2; // RevenueSwitchHubType true：开 false：关
}

// 营收开关
message SetRevenueSwitchHubRequest {
    ga.BaseReq base_req = 1;
    uint32 switch_type = 2; /* RevenueSwitchHubType */
    bool is_open = 3; // true 开

}
message SetRevenueSwitchHubResponse {
    ga.BaseResp base_resp = 1;
}


// 顶部浮窗类型
enum ETopOverLayType {
  E_TOP_OVER_LAY_TYPE_UNSPECIFIED = 0;   //无效
  E_TOP_OVER_LAY_TYPE_CHANNEL = 1;   // 房间样式浮窗
  E_TOP_OVER_LAY_TYPE_USER = 2;  // 用户样式浮窗
  E_TOP_OVER_LAY_TYPE_ESPORT_GOD = 3;  // 电竞大神样式浮窗
}
 // 顶部浮窗
message GetGlobalTopOverLayRequest {
  ga.BaseReq base_req = 1;
}
message GetGlobalTopOverLayResponse {
  ga.BaseResp base_resp = 1;
  uint32 type = 2;   //浮窗类型 ETopOverLayType
  string ui_xml = 3; //浮窗UI-xml
  UserBaseInfo followed_user_info = 4;  //被跟随用户信息
  string report_json_data = 5;          //上报json数据
}


//获取推荐列表房间反馈配置
message GetRecFeedbackConfigRequest {
  ga.BaseReq base_req = 1;
}
message GetRecFeedbackConfigResponse {
  ga.BaseResp base_resp = 1;
  repeated string reason_list = 2;   // 原因列表
}

//推荐房间反馈
message DoRecFeedbackRequest {
  ga.BaseReq base_req = 1;
  uint32 channel_id = 2;
  repeated string reason_list = 3;   // 原因列表
}
message DoRecFeedbackResponse {
  ga.BaseResp base_resp = 1;
} 

