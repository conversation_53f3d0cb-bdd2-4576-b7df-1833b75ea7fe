syntax = "proto3"; // 使用 proto3 语法

package ga.unified_search; // 定义包名为 ga

import "ga_base.proto"; // 引入 ga_base.proto 文件
import "channel/channel_.proto"; // 引入 channel_.proto 文件
import "channel_play/channel-play_.proto"; // 引入 topic-channel_.proto 文件
import "ancient_search/ancient-search_.proto"; // 引入 ancient-search_.proto 文件
import "super_channel/super-channel_.proto"; // 引入 super-channel_.proto 文件
import "game_card/game_card_.proto"; // 引入 game_card.proto 文件
import "guild/guild_.proto";
import "online/friendol_.proto";

option java_package = "com.yiyou.ga.model.proto"; // 生成 Java 代码时的包名
option go_package = "golang.52tt.com/protocol/app/unified-search"; // 生成 Go 代码时的包名

enum UnifiedSearchType { // 枚举 UnifiedSearchType
    UNIFIED_SEARCH_TYPE_UNKNOWN = 0; // 枚举值 0
    UNIFIED_SEARCH_TYPE_USER = 1; // 枚举值 1，表示用户类型
    UNIFIED_SEARCH_TYPE_GUILD = 2; // 枚举值 2，表示公会类型
    UNIFIED_SEARCH_TYPE_CHANNEL = 3; // 枚举值 3，表示房间类型
    UNIFIED_SEARCH_TYPE_CPL = 4; // 枚举值 4，表示明星活动类型
    UNIFIED_SEARCH_TYPE_AREA = 5; // 枚举值 5，表示专区类型
    UNIFIED_SEARCH_TYPE_POST = 6;  // 枚举值 6， 表示动态类型
    UNIFIED_SEARCH_TYPE_COMMUNITY = 7; // 枚举值 7，表示社团类型
    UNIFIED_SEARCH_TYPE_AIGC = 8; // 枚举值 8，表示 AIGC 类型

    UNIFIED_SEARCH_TYPE_ALL = 9999; // 枚举值 
}

message UnifiedSearchReq { // 请求消息体 UnifiedSearchReq
    BaseReq base_req = 1; // 引用 BaseReq 消息体，消息字段编号为 1
    string keyword = 2; // 搜索关键词，消息字段编号为 2
    uint32 page = 3; // 分页页码，消息字段编号为 3
    uint32 count = 4; // 分页大小，消息字段编号为 4
    repeated uint32 type_list = 5; // 搜索结果类型列表，消息字段编号为 5，类型为 uint32，可重复
    // 可选枚举类型： UnifiedSearchType，表示搜索类型

}

message UnifiedSearchResp { // 响应消息体 UnifiedSearchResp
    BaseResp base_resp = 1; // 引用 BaseResp 消息体，消息字段编号为 1
    string keyword = 2; // 搜索关键词，消息字段编号为 2
    string force_link = 3; // 强制链接，当链接存在时，忽略搜索结果，消息字段编号为3
    map<uint32, UnifiedSearchResult> result_list = 4; // 搜索结果列表，key 为 uint32 类型，value 为 UnifiedSearchResult 消息体，消息字段编号为 4，表示 Map 类型
}

message UnifiedSearchResult { // 统一搜索结果消息体 UnifiedSearchResult
    oneof result { // oneof 类型，表示只能在 result 内其中一个字段存在，其余字段不存在
        UnifiedSearchUserResult user_result = 1; // 用户搜索结果，消息字段编号为 1
        UnifiedSearchGuildResult guild_result = 2; // 公会搜索结果，消息字段编号为 2
        UnifiedSearchChannelResult channel_result = 3; // 房间搜索结果，消息字段编号为 3
        UnifiedSearchCPLResult cpl_result = 4; // 明星活动搜索结果，消息字段编号为 
        CommonSearchAreaResult area_result = 5;// 搜索结果 
        CommonSearchPostResult post_result = 6; // 游戏帖子搜索结果
        CommonSearchCommunityResult community_result = 7; // 社团搜索结果
        CommonSearchAIGCResult aigc_result = 8; // AIGC 搜索结果
    }
}

// 统一搜索结果下的子消息体
message UnifiedSearchGuildBrief { // 公会搜索结果中的公会信息
        uint32 guild_id = 1; // 公会 ID，消息字段编号为 1
        uint32 guild_display_id = 2; // 公会展示 ID，消息字段编号为 2
        string name = 3; // 公会名称，消息字段编号为 3
        uint32 need_verify = 4; // 是否需要审核，消息字段编号为 4
        uint32 mem_count = 5; // 公会总人数，消息字段编号为 5
        uint32 gift_pkg_count = 6; // 公会礼包总数，消息字段编号为 6
        string guild_manifesto = 7; // 公会宣言，消息字段编号为 7
        uint32 game_gift_pkg_count = 8; // 某款公会游戏的礼包总数，消息字段编号为 8
        uint32 guild_star_level = 9; // 公会星级，消息字段编号为 9，只在搜索结果中返回
        uint32 home_channel_id = 10; // 公会主房间的 ID，消息字段编号为 10
}


// 定义了用户搜索结果中的用户信息的数据类型
message UnifiedSearchUserBrief {
    // 用户头像md5值
    string face_md5 = 1;
    uint32 sex = 2;
    // 用户签名
    string signature = 3;
    // 用户昵称
    string nick_name = 4;
    // 用户账号
    string account = 5;
    // 用户ID
    uint32 uid = 6;
    // 用户账号别名
    string account_alias = 7;
    // 官方认证标题
    string certify_title = 8;
    // 大V类型
    string certify_style = 9;
    // 直播房间ID
    uint32 channel_id = 10;
    // PK状态，枚举类型
    uint32 pk_status = 11;
    // 大V特别图标动效
    string certify_special_effect_icon = 12;
    // 大V认证类型
    string vcertify_style = 13;
    bool   is_follow = 14;  // 是否用户关注的人

    ga.online.FriendsDetail follow_info = 15; // 跟随信息
    // 按钮显示类型
    uint32 button_display_type = 16;     // see UserButtonDisplayType
}

// 定义了房间搜索结果中的房间信息数据类型
message UnifiedSearchChannelBrief {
    // 房间详情信息
    ga.channel.ChannelDetailInfo channel_detail_info = 1;
    // 房间标签
    ga.channel.SCTagInfo tag_info = 2;

    ga.channel_play.TopicChannelItem  topic_channel = 3;//主题房

    ga.channel.PgcChannelExtraInfo pgc_channel_extra_info = 4;// pgc房间额外信息

}

// 定义了活动搜索结果的数据类型
message UnifiedSearchActivityBrief {
    // 活动标题
    string title = 1;
    // 活动图标
    string icon = 2;
    // 活动描述
    string desc = 3;
    // 跳转链接
    string jump_url = 4;
}


// 定义了专区搜索结果的数据类型
message UnifiedSearchAreaBrief {
    // 专区标题
    string title = 1;
    // 专区图标
    string icon = 2;
    // 专区描述
    string desc = 3;
    // 跳转链接
    string jump_url = 4;
}

// 定义了用户、房间、活动搜索结果中通用的 CPL(Composite Page Layout) 数据类型
message UnifiedSearchCPLBrief {
    // 标题
    string title = 1;
    // 背景
    string bg = 2;
    // 以下 4 个字段中只会出现一个，分别代表用户、房间、活动信息
    oneof value {
        UnifiedSearchUserBrief user_brief = 3;
        UnifiedSearchChannelBrief channel_brief = 4;
        UnifiedSearchActivityBrief activity_brief = 5;
    }
}

// 定义了用户搜索结果的数据类型
message UnifiedSearchUserResult {
    // 是否为最后一页
    bool last_page = 1;
    // 用户信息列表
    repeated UnifiedSearchUserBrief user_brief_list = 2;


}

// 定义了公会搜索结果的数据类型
message UnifiedSearchGuildResult {
    // 是否为最后一页
    bool last_page = 1;
    // 公会信息列表
    repeated UnifiedSearchGuildBrief guild_brief_list = 2;
}

// 定义了房间搜索结果的数据类型
message UnifiedSearchChannelResult {
    // 是否为最后一页
    bool last_page = 1;
    // 房间信息列表
    repeated UnifiedSearchChannelBrief channel_brief_list = 2;
    map<uint32, string> display_channel_map = 3;//channelid与描述的
}

// 定义了通用的搜索结果 CPL 数据类型
message UnifiedSearchCPLResult {
    // 配置ID
    string config_id = 1;
    // CPL 列表
    repeated UnifiedSearchCPLBrief cpl_brief_list = 2;
}


//--------------------------------------------------------------
//--------------------------------------------------------------

enum SiftType {
    UNDEFINED_SIFT_TYPE = 0;    // 未定义筛选类型
    SIFT_TYPE_RECOMMEND = 1;  // 推荐
    SIFT_TYPE_LATEST = 2;     // 最新
}

// 搜索房间类型
enum ChannelSearchType {
    CHANNEL_SEARCH_TYPE_ALL = 0;    // 包括 ugc 和 pgc 房间
    CHANNEL_SEARCH_TYPE_UGC = 1;    // 只搜 ugc 房间
    CHANNEL_SEARCH_TYPE_PGC = 2;    // 只搜 pgc 房间
}

message CommonSearchReq { // 请求消息体 CommonSearchReq
    BaseReq base_req = 1; // 引用 BaseReq 消息体，消息字段编号为 1
    string keyword = 2; // 搜索关键词，消息字段编号为 2
    uint32 offset = 3; // 目前客户端看过多少个数据 开始填0，消息字段编号为 3 
    uint32 type = 4; // 搜索结果类型列表，消息字段编号为 4，类型为 uint32 // 可选枚举类型： UnifiedSearchType，表示搜索类型
    string id = 5;//保留
    repeated uint32 no_browse_channel_list  = 6; //上次请求未曝光id列表
    WordType word_type = 7; //搜索类型，引导词联想词由推荐下发类型，客户端透传上来即可,用户自己输入的搜索为传Default上来即可
    uint32 sift_type = 8;   // 筛选类型 SiftType
    repeated string no_viewed_post_id_list = 9;  // 未浏览的帖子 id 列表
    bool is_search_cnt_incr = 10;   // 是否搜索次数增加
    uint32 channel_search_type = 11;  // see ChannelSearchType
    repeated uint32 filter_category_ids = 12; // 需要过滤掉的category_id
}

message CommonSearchResp { // 响应消息体 CommonSearchResp
    BaseResp base_resp = 1; // 引用 BaseResp 消息体，消息字段编号为 1
    repeated CommonSearchResult result_list = 2; // 搜索结果列表 为 CommonSearchResult 消息体，消息字段编号为 4，表示 Map 类型
    string force_link = 3; // 强制链接，当链接存在时，忽略搜索结果，消息字段编号为3
    string keyword = 4;//输入的keyword而已
    uint32 next_offset = 5;//下一次请求的offset 
    string next_id = 6;//保留
    
}

message CommonSearchResult { // 统一搜索结果消息体 CommonSearchResult
    oneof result { // oneof 类型，表示只能在 result 内其中一个字段存在，其余字段不存在
        CommonSearchUserResult user_result = 1; // 用户搜索结果，消息字段编号为 1
        CommonSearchGuildResult guild_result = 2; // 公会搜索结果，消息字段编号为 2
        CommonSearchChannelResult channel_result = 3; // 房间搜索结果，消息字段编号为 3
        CommonSearchCPLResult cpl_result = 4; // 明星活动搜索结果，消息字段编号为 4 
        CommonSearchAreaResult area_result = 5;// 搜索结果
        CommonSearchPostResult post_result = 7; // 动态搜索结果
        CommonSearchCommunityResult community_result = 8; // 社团搜索结果
        CommonSearchAIGCResult aigc_result = 9; // AIGC 搜索结果
    }
    uint32 search_type = 6;//返回类型 UnifiedSearchType
  
}

message CommonSearchPostResult {
    // 是否为最后一页
    bool last_page = 1;

    // 动态搜索结果列表
    repeated bytes post_result_bin_list = 2;     // 一个 bytes 对应一个 game_ugc.proto 下的 GameFeed
}

enum UserButtonDisplayType {
    USER_BUTTON_DISPLAY_TYPE_UNSPECIFIED = 0;    // 不显示按钮
    USER_BUTTON_DISPLAY_TYPE_ENTER_CHANNEL = 1;  // 显示进房按钮
    USER_BUTTON_DISPLAY_TYPE_FOLLOW = 2;         // 显示关注按钮
    USER_BUTTON_DISPLAY_TYPE_ALREADY_FOLLOW = 3; // 显示“已关注”按钮
}

// 定义了用户搜索结果的数据类型
message CommonSearchUserResult {
    // 是否为最后一页
    bool last_page = 1;
    // 用户信息列表
    repeated ga.ancient_search.ContactBrief user_brief_list = 2;

}



// 定义了公会搜索结果的数据类型
message CommonSearchGuildResult {
    // 是否为最后一页
    bool last_page = 1;
    // 公会信息列表
    repeated ga.guild.GuildBaseInfo guild_brief_list = 2;
}


message CommonSearchChannelResult {
    // 是否为最后一页
    bool last_page = 1;
    // 房间信息列表
    repeated CommonSearchChannelBrief channel_brief_list = 2;
    map<uint32, string> display_channel_map = 3;//channelid与描述的
}

// 定义了房间搜索结果中的房间信息数据类型
message CommonSearchChannelBrief {
    // 房间类型
    enum ChannelStyleType {
        UNIFIED_CHANNEL_TYPE_TYPE = 0; // 枚举值 0，表示未知类型
        CHANNEL_TYPE_COMMON = 1;    // 普通房间
        CHANNEL_TYPE_TOPIC = 2;    // 主题房 样式
    }

    uint32 channel_style_type = 1;//1 普通房间 2主题房  
    ga.channel.ChannelDetailInfo common_channel_info = 2;//普通pgc，搜id返回普通房...
    ga.channel_play.TopicChannelItem  topic_channel = 3;//主题房
    // 房间标签
    ga.channel.SCTagInfo common_channel_tag_info = 4;//普通pgc补上tag 来构造标签

    ga.channel.PgcChannelExtraInfo pgc_channel_extra_info = 5;// pgc房间额外信息


}

// 定义了社团搜索结果的数据类型
message CommonSearchCommunityResult {
    bool last_page = 1;    // 是否为最后一页
    repeated Community community_list = 2; // 社团列表
}

// 社团信息
message Community {
    string id = 1;    // 社团 id
    string name = 2;  // 社团名称
    string intro = 3; // 社团简介
    string logo = 4; // 社团 logo
    string category_type_simple_desc = 5; //社团品类类型短文案
    uint32 member_count = 6; // 社团成员数
}

// 定义了AIGC搜索结果的数据类型
message CommonSearchAIGCResult {
    repeated AIGCInfo aigc_info_list = 1; // AIGC 信息列表
}

message AIGCInfo {
    string content_url = 1; // 内容展示链接
    string jump_url = 2; // 跳转链接
}

message CommonSearchCPLResult {
    string config_id = 1;   
    repeated ga.super_channel.SuperChannelSearchResultItem result_list = 2; // 搜索结果列表
   
}

message CommonSearchAreaBrief {
    ga.game_card.GameCardConfInfo  game_card_result = 1;
    uint32 tab_id = 2;//游戏tabid
}

message CommonSearchAreaResult {
      repeated CommonSearchAreaBrief game_card_result_list = 1;
}


//--------------------------------------------------------------
//--------------------------------------------------------------

enum WordType {
  Default = 0; // 默认
  AssociateLabel = 1; // 联想词标签类型
  AssociateUserName = 2; // 联想词用户类型
  GuideRegLabel = 3; // 引导词初始化标签
  GuideRealtimeLabel = 4; // 引导词实时标签
  GuideHistoryLabel = 5; // 引导词历史标签
  HistoryWord = 6; // 历史搜索
  PreferWord = 7; // 猜你喜欢
  AssociateTopic = 8; // 联想词话题类型
  GuideTopicLabel = 9; // 引导词话题标签
  AssociateSquareCategory = 10; // 联想词广场品类
}


// 搜索引导词数据类型
message SearchGuideWords {
    // 引导词内容
    string content = 1;
    WordType type = 2;
}

// 搜索引导词请求数据类型
message SearchGuideWordsReq {
     BaseReq base_req = 1; // 引用 BaseReq 消息体，消息字段编号为 1
    enum AreaType {
        UNIFIED_AREA_TYPE_TYPE = 0; // 枚举值 0，表示未知类型
        AREA_TYPE_MATCH = 1;    // 匹配 
        AREA_TYPE_MUSIC = 2;    // 音乐专区搜索
        AREA_TYPE_ENTERTAINMENT = 3;    // 娱乐tab搜索
        AREA_TYPE_IM = 4; //im搜索
        AREA_TYPE_MAIN_PAGE = 5;//主页
        AREA_TYPE_MAIN_GAME = 6;//游戏专区
    }
    // 当前页面所在的专区类型：1 匹配 2 音乐 3 娱乐 ...
    uint32 area_type = 2;
    uint32 tab_id = 3; // 用户请求引导词所在垂直列表，非垂直列表传0
    // 未曝光的引导词
    repeated string no_browse_list = 4;
}

// 搜索引导词响应数据类型
message SearchGuideWordsResp {
    BaseResp base_resp = 1; // 引用 BaseResp 消息体，消息字段编号为 1
    // 引导词列表
    repeated SearchGuideWords guide_words_list = 2;
    
}

//---------------------------------------------------------------
//联想词
message SearchSuggestReq {
    BaseReq base_req = 1; // 引用 BaseReq 消息体，消息字段编号为 1
    string query = 2;     // 搜索关键词
}
 
message SearchSuggestResp {
    BaseResp base_resp = 1; // 引用 BaseResp 消息体，消息字段编号为 1
    repeated SuggestWords suggest = 2;   // 联想词列表
    string force_link = 3; // 强制链接，当链接存在时，忽略搜索结果，消息字段编号为3
    // 关键词
    string important_word = 4;
}

message SuggestWords {
    string word = 1;        // 联想词内容
    WordType type = 2;      // 联想词类型
    bool is_follow = 3;     // 是否是关注
}


