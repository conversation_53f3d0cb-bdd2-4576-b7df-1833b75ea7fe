syntax = "proto3";

package ga.music_nest_logic;

import "ga_base.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/music-nest-logic";

enum LiveStatus
{
  REMOVE_STATUS = 0; //已下架
  NOT_START_STATUS = 1;  //未开始
  WARN_UP_STATUS = 2;    //预热中
  LIVING_STATUS = 3;      //开播中
  CLOSED_STATUS = 4;      //已结束
}

enum PatternType
{
  UNKNOWN_PATTERN_TYPE = 0;
  CP_TYPE = 1;
  NORMAL_TYPE_1 = 2;
  NORMAL_TYPE_2 = 3;
  BATTLE_TYPE_1V1 = 4;
  BATTLE_TYPE_3V3 = 5;
  BATTLE_HOME_TYPE = 6;
}

// 获取乐窝配置的社群信息
message GetMusicNestSocialCommunityReq {
  ga.BaseReq base_req = 1;
  uint32 channel_id = 2;
}

message GetMusicNestSocialCommunityResp {
  ga.BaseResp base_resp = 1;
  string partner_prefix_name = 2;  // 合作方（厂牌）前缀名称
  repeated SocialCommunity social_community_list = 3;
}

message GetMusicNestCoverAndLiveListReq {
  ga.BaseReq base_req = 1;
}

message SocialCommunity {
  string id = 1;
  string name = 2;
  string logo = 3;
}

message GetMusicNestCoverAndLiveListResp {
  message CoverInfo {
    uint32 id = 1; // 活动id
    string short_cover_image = 2; //封面url 短图
  }
  message LiveInfo {
    message GuestInfo {
      uint32 uid = 1;
      string guest_head_image = 2;  //嘉宾的头像
    }
    message GuestInfos {
      repeated GuestInfo guest_info = 1;
    }
    message SongInfo {
      string song_name = 1;  //歌曲名称
    }
    uint32 id = 1; // 活动id
    string short_cover_image = 2; //封面url
    string category_title = 3; //分类标题
    string background_url = 4; //分类底图
    uint32 num_audience = 5; //房间在线人数
    uint32 channel_id = 6;
    LiveStatus live_status = 7; //开播状态
    bool is_recommend = 8;  // 是否推荐场次
    string topic = 9; //活动主题
    repeated SongInfo song_infos = 10;
    repeated GuestInfos guest_infos_array = 11;
    PatternType pattern_type = 12;  //嘉宾模式
    string long_cover_image = 13;  // 活动长图
    uint32 num_audience_joined = 14; // 多少人曾经一起作
    string partner_prefix_name = 15;  // 合作方（厂牌）前缀名称
    repeated SocialCommunity social_community_list = 16;
  }

  ga.BaseResp base_resp = 1;
  repeated CoverInfo cover_infos = 2; //乐窝封面
  repeated LiveInfo live_infos = 3;  //乐窝现场
  string title = 4; /* 标题 可为空 */
  string jump_url = 5; /* 跳转链接 (h5) 可为空 */
  string lottie_url = 6;    /*lottie 链接*/
  string lottie_md5 = 7;     /*lottie  md5*/
  string logo_picture = 8;   /*标题图片url*/
  string logo_text = 9;      /*标题文本*/
}

message SubMusicNestReq {
  ga.BaseReq base_req = 1;
  bool is_sub = 2; //目前只能为true
}

message SubMusicNestResp {
  ga.BaseResp base_resp = 1;
}

message GetMusicNestHomePageReq {
  ga.BaseReq base_req = 1;

}

message GetMusicNestHomePageResp {
  message ActivityInfo {
    uint32 id = 1; //活动id
    string topic = 2; // 活动主题
    uint32 channel_id = 3;
    uint32 num_audience = 4;
    LiveStatus live_status = 5;
    uint32 live_on_time = 6;
    string short_cover_image = 7;  //短图
    string long_cover_image = 8;   //长图
    string frame_color = 9;       //边框颜色
    bool is_recommend = 10;
    bool is_sub_activity = 11;  // 是否已订阅这场活动
    uint32 warm_up_time = 12;
  }
  ga.BaseResp base_resp = 1;
  repeated ActivityInfo activity_infos = 2; //乐窝时间线内容
  message CategoryActivityInfo {
    string main_title = 1; //分类主标题
    string sub_title = 2; // 分类副标题
    repeated ActivityInfo activity_infos = 3;
  }
  repeated CategoryActivityInfo category_activity_infos = 3;
  bool is_sub_music_nest = 4; // 是否已进驻乐窝
}

message SubMusicNestActivityReq {
  ga.BaseReq base_req = 1;
  uint32 id = 2;
}

message SubMusicNestActivityResp {
  ga.BaseResp base_resp = 1;
}

// 乐窝节目单协议
enum StageType
{
  UNKNOWN_STAGE_TYPE = 0;
  SOLO = 1;
  BATTLE = 2;
  INTRODUCTION = 3;
}

message MusicNestPerformanceGuest {
  message GuestInfo {
    uint32 uid = 1;
    string ttid = 2;  //嘉宾的头像
    string nick_name = 3; // 嘉宾昵称
    int32 sex = 4;
  }

  repeated GuestInfo guest_infos = 1;
  string introduction = 2;  // 介绍
  string pk_icon = 3;   // 双人环节的pk图
}

message MusicNestPerformanceStage {
  StageType stage_type = 1; // 环节标记
  uint32 id = 2; // 环节序号, 必须大于0
  string title = 3; // 标题
  string icon = 4; // 纯介绍环节的图标
  string introduction = 5; // 纯介绍环节的介绍, 存在则忽略嘉宾列表
  repeated MusicNestPerformanceGuest guest_list = 6; // 嘉宾列表
}

enum MusicNestPerformanceStageRange {
  BEGIN = 0;  // 代表演出未开始
  END = 999;  // 代表演出结束
}

message MusicNestPerformanceInfo {
  uint32 channel_id = 1; // 房间标识

  string title = 2;
  string entry_icon = 3; // 节目单入口底图

  uint32 current_stage_id = 4; // 当前节目

  repeated MusicNestPerformanceStage stage_list = 5; // 节目单

  int64 stage_update_time = 6; // 当前节目更新时间戳
  int64 performance_update_time = 7; // 节目单更新时间戳
  string id = 8; //节目清单唯一id
}

// 获取
message GetMusicNestPerformanceReq {
  ga.BaseReq base_req = 1;

  uint32 channel_id = 2; // 房间标识
}

enum MusicNestPerformanceType{
  INVALID_MUSIC_NEST_PERFORMANCE_TYPE = 0;
  NORMAL_PERFORMANCE = 1;
  NEXT_ACTIVITY = 2;
}
message GetMusicNestPerformanceResp {
  ga.BaseResp base_resp = 1;
  MusicNestPerformanceInfo performance = 2;
  uint32 performance_type = 3; /* MusicNestPerformanceType */
  NextDirectionAct next_act = 4; /* 下一场引导 */
}

message SetCurrentMusicNestPerformanceStageReq {
  ga.BaseReq base_req = 1;

  uint32 channel_id = 2; // 房间标识
  uint32 current_stage_id = 3; // 当前节目
  string id = 4; // 节目清单唯一标识id
}

message SetCurrentMusicNestPerformanceStageResp {
  ga.BaseResp base_resp = 1;
}

/* 关闭下一场引导 */
message CloseNestDirectionActReq {
  ga.BaseReq base_req = 1;
  uint32 channel_id = 2; // 房间标识
}

message CloseNestDirectionActResp {
  ga.BaseResp base_resp = 1;
}

// 乐窝活动已看过相关协议
// 获取本场活动已看过的人数 已看过的人数根据后台的规则来制定
message VisitedInfo {
  uint32 visited_size = 1;
  bool need_use = 2; // 是否需要使用visited_size的值， true为用，false为不用
}

message GetSpecifiedChannelVisitedSizeReq {
  ga.BaseReq base_req = 1;
  repeated uint32 channel_id_list = 2;
}

message GetSpecifiedChannelVisitedSizeResp {
  ga.BaseResp base_resp = 1;
  map<uint32, VisitedInfo> visited_info_list = 2;
}

// ----------- 推送结构 -------------

// 房间节目单变更采用
message MusicNestPerformanceChange {
  MusicNestPerformanceInfo performance = 1;
}

// 房间当前节目环节变更
message MusicNestPerformanceStageChange {
  uint32 channel_id = 1; // 房间标识
  uint32 current_stage_id = 2; // 当前节目

  int64 stage_update_time = 3; // 当前节目更新时间戳 纳秒
  int64 performance_update_time = 4; // 节目单更新时间戳 纳秒
}

message NextDirectionAct{
  uint32 channel_id = 1; /* 跳转 */
  uint32 id = 2;
  string title = 3; /* 标题 可为空 */
  string category_title = 4; //分类标题
  uint32 expire_time = 5; /* 消失的时间 s */
  string short_cover_image = 6; //封面url
}

/* 下一场引导 */
message NextDirectionActNotify{
  uint32 channel_id = 1; /* 当前房间 */
  NextDirectionAct next_act = 2;
}

/* 下一场引导消失 */
message DisappearNextDirectionActNotify{
  uint32 channel_id = 1; /* 当前房间 */
}

// 已看过人数的推送
// 房间已看过人数推送
message VisitedSizeInfo {
  VisitedInfo visited_info = 1;
}

// 乐窝活动节目进行情况信息推送
message CokeRuleInfo {
  uint32 act_id = 1; /* 乐窝活动id */
  uint32 channel_id = 2;
  uint32 stay_ts = 3; // 停留stay_ts可以获得一个快乐水
  uint32 coke_num = 4; /*     - 每获得「Y」个快乐水，将额外获得「Z」票数 */
  uint32 vote_num = 5; /*     - 每获得「Y」个快乐水，将额外获得「Z」票数 */
}

message CokeAct{
  CokeRuleInfo coke_rule_info = 1;
  bool is_open_coke = 2; // true: 开启快乐水 false：关闭快乐水
  uint32 left_coke = 3; // 请求用户剩余的可乐数量
}

//(和乐窝活动相关的汇总）上架 预热中
message MusicNestLiveActivitiesNotify {
  CokeAct coke_info = 1;
}

// 进房后乐窝活动进行期间的信息获取 (和乐窝活动相关的汇总）
message GetMusicNestLiveInfoReq {
  ga.BaseReq base_req = 1;
  uint32 channel_id = 2; // 房间标识
}

message GetMusicNestLiveInfoResp {
  ga.BaseResp base_resp = 1;
  uint32 channel_id = 2; // 房间标识
  CokeAct coke_info = 3;
}

//实现设置，更新，获取欢迎弹层
message WelcomePop{
  // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
  bool   startFlag = 1;
  string topic = 2; // 主题
  // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
  repeated string optionOne = 3; //选项
  // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
  string pictureUrl = 4;   //头图
}
message OptionStruct{
  // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
  string optionName = 1;
  // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
  uint32  optionId = 2;
}
message SendWelcomePop{
  // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
  bool   startFlag = 1;
  string topic = 2; // 主题
  // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
  repeated OptionStruct optionOne = 3; //选项
  // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
  string pictureUrl = 4;   //头图
}

//用户进房消息
message GetWelcomePopReq{
  ga.BaseReq base_req = 1;
  uint32 channel_id = 2;
}
message GetWelcomePopResp{
  ga.BaseResp base_resp = 1;
  SendWelcomePop welcom_msg = 2;
}


//用户点击消息触发公屏推送
message UserClickPopReq{
  ga.BaseReq base_req = 1;
  uint32 channel_id = 2;
  // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
  OptionStruct optionOne = 3; // 带上内容，后端仅做校验，打印用途
}
message UserClickPopResp{
  ga.BaseResp base_resp = 1;
}


