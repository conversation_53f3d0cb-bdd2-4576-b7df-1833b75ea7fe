syntax = "proto3";

/***************tt营收通用logic*****************/

package ga.tt_rev_common_logic;

import "ga_base.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/tt_rev_common_logic";

// 充值(首充)活动入口信息
message RechargeActEntryInfo {
    enum LocationType {
        LOCATION_TYPE_UNSPECIFIED = 0;
        LOCATION_TYPE_ROOM = 1;      // 房间内入口（房间广告位左侧）
        LOCATION_TYPE_ROOM_GIFT = 2; // 房间底部(礼物架)入口
        LOCATION_TYPE_IM_GIFT = 3;   // IM页底部(礼物架)入口
        LOCATION_TYPE_GIFTSHELF = 4; // 礼物架中的入口（包括房间内、IM页的礼物架入口）
    }

    uint32 location_type = 1; // 充值(首充)活动入口位置
    string entry_img = 2;     // 充值(首充)活动入口图片
}

message GetNewRechargeActEntryInfoRequest{
    ga.BaseReq base_req = 1;
}

message GetNewRechargeActEntryInfoResponse{
    ga.BaseResp base_resp = 1;
    bool show_entry = 2; // 是否展示活动入口
    repeated RechargeActEntryInfo entry_list = 3;
    // 入口文案配置：固定格式：文案+礼物图片+礼物名
    string entry_desc = 4;          //首充入口文字描述, desc 为""时，不展示入口文案
    string entry_img_url = 5;       //首充入口礼物图标url
    string entry_present_name = 6;  //首充入口礼物名
    int64 finish_ts = 7;            // 首充活动结束时间
    uint32 local_cache_sec = 8;      // 本地缓存时间(单位：秒)
}

// 首充奖励信息
message RechargeActAwardInfo {
    enum ItemType {
        ITEM_TYPE_UNSPECIFIED = 0;
        ITEM_TYPE_TBEAN = 1;        // T豆
        ITEM_TYPE_RED_DIAMONDD = 2; // 红钻
        ITEM_TYPE_GOLD_DIAMOND_WITH_BOUNS = 3; // 红钻加成（魅力礼物）
        ITEM_TYPE_VIRTUAL_DRESS_DAY= 4; // 虚拟装扮（天）
    }
    uint32 item_type = 1 [deprecated = true]; // [deprecated = true] 
    string name = 2;            // 奖励名称
    string static_img = 3;      // 静态图片
    string gif_url = 4;         // 动态图片
    uint32 amount = 5 [deprecated = true]; // [deprecated = true]
    string unit_text = 6;       // 单位文案,对应奖励图标右上角的文案。如：x3, 3天
    string award_type_text = 7; // 奖励类型文案，如：麦位框
}

// 首充档位信息
message RechargeActLevelInfo {
    uint32 level_id = 1;
    uint32 price = 2;        // 对应单位金额（RMB)
    string set_desc_url = 3; // 套装描述图片
    uint32 bean_saved = 4;   // 节省T豆
    repeated RechargeActAwardInfo award_list = 5;
    string product_id = 6;   // 对应的商品id
}

// 获取首充活动弹窗信息
message GetNewRechargeActPopupInfoRequest{
    ga.BaseReq base_req = 1;
}

message GetNewRechargeActPopupInfoResponse{
    ga.BaseResp base_resp = 1;
    repeated RechargeActLevelInfo info_list = 2;
    string rule_url = 3; // 规则页链接
}

message NewFirstRechargeFinNotify {
    uint32 uid = 1;
}

// 获取充值页banner信息
message GetRechargeBannerInfoRequest{
    ga.BaseReq base_req = 1;
}

message RechargeBannerInfo {
    enum BannerType {
        BANNER_TYPE_UNSPECIFIED = 0;
        BANNER_TYPE_WEEK_CARD = 1;      // 周卡banner
        BANNER_TYPE_FIRST_RECHARGE = 2; // 首充活动
    }
    uint32 banner_type = 1;
    string banner_pic = 2;
    string jump_url = 3;
}

message GetRechargeBannerInfoResponse{
    ga.BaseResp base_resp = 1;
    repeated RechargeBannerInfo banner_list = 2;
}

message CheckCanModifySexRequest {
    ga.BaseReq base_req = 1;
}

message CheckCanModifySexResponse {
    ga.BaseResp base_resp = 1;
    bool need_toast = 2; // 是否需要弹窗提示
    string toast = 3; // 提示文案
}