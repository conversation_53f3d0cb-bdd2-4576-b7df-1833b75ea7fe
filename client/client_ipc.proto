syntax="proto2";

package ga.client;

option java_package ="com.yiyou.ga.model.ipc.proto";
option go_package = "golang.52tt.com/protocol/app/client";

// buf:lint:ignore ENUM_FIRST_VALUE_ZERO
enum IPCCMDType{
    // Server endpoint starts from 1 to 1000000
    TYPE_NOTIFY_NEW_APP_ON_TOP = 1000;
    // Client endpoint starts from 1000001  	
}

message NewAppOnTop {
    required string package_name = 1; 
}
