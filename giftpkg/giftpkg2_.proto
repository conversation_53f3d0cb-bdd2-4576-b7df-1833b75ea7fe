syntax="proto2";

package ga.giftpkg;
import "ga_base.proto";

option java_package ="com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/giftpkg";

// 礼包类型: 激活码，游戏礼包，代金券，充值卡，首充号，周边
// buf:lint:ignore ENUM_FIRST_VALUE_ZERO
enum EGiftPktItemType{
    
	GIFTPKT_ITEM_TYPE_CDKEY  = 1;         // 激活码
    GIFTPKT_ITEM_TYPE_GAMEGIFT  = 2;      // 游戏礼包
	GIFTPKT_ITEM_TYPE_VOUCHER  = 3;       // 代金券
	GIFTPKT_ITEM_TYPE_RECHARG_CARD  = 4;  // 充值卡
	GIFTPKT_ITEM_TYPE_FIRST_VOUCHER_ACCOUNT = 5;   // 首充号
	GIFTPKT_ITEM_TYPE_ZHOUBIAN  = 6;       // 周边 ^_^
	
	GIFTPKT_ITEM_TYPE_MIGRATE_FIN_FLAG  = 254; // 一个特殊的类型码 对外没有意义 需要略过 用于内部迁移数据的标记 
	GIFTPKT_ITEM_TYPE_ALL  = 255;              // 一个特殊的类型码 不表示任何类型 用于获取全部类型
}

// 礼包来源: TT官方礼包中心 会长录入
// buf:lint:ignore ENUM_FIRST_VALUE_ZERO
enum EGiftPktSourceType{
	
	GIFTPKT_SOURCE_TYPE_TT  = 1;          // TT官方的礼包 来自礼包中心
    GIFTPKT_SOURCE_TYPE_GUILDOWNER  = 2;  // 会长录入
	GIFTPKT_SOURCE_TYPE_OPER_ACTIVE  = 3; // 运营活动
	
	GIFTPKT_SOURCE_TYPE_OLDDATA  = 254;  // 一个特殊类型的礼包来源, 表明数据来源与老接口迁移的旧数据 不知道原始来源
	GIFTPKT_SOURCE_TYPE_ALL  = 255;      // 一个特殊的礼包来源 不表示任何特定来源类型 用于获取全部来源类型
}

//礼包平台类型
// buf:lint:ignore ENUM_FIRST_VALUE_ZERO
enum TTGiftPlatform {
    TT_GIFT_ANDROID         = 1;
    TT_GIFT_IOS             = 2;
    TT_GIFT_ALL_PLATFORM    = 255;
}


// 兑换形式: 兑换码，卡号+密码
// buf:lint:ignore ENUM_FIRST_VALUE_ZERO
enum EGiftPktExchangeType{
	GIFTPKT_EXCHANGE_TYPE_ONLY_CODEID  = 1;         // 兑换码
    GIFTPKT_EXCHANGE_TYPE_CODEID_AND_PASSWORD = 2;  // 卡密
	GIFTPKT_EXCHANGE_TYPE_NONE = 255;               // 没有兑换信息
}


// 只有卡号 的礼物的兑换信息
// buf:lint:ignore MESSAGE_PASCAL_CASE
message ExchangeInfo_CodeID {
    required string code_id = 1;
}

// 卡号+密码 的礼物的兑换信息
// buf:lint:ignore MESSAGE_PASCAL_CASE
message ExchangeInfo_CodeID_Pwd {
    required string code_id 	= 1;
    required string pwd	= 2;
}


// 礼包的货币类型
// buf:lint:ignore ENUM_FIRST_VALUE_ZERO
enum EGiftPktCurrencyType{
	
	GIFTPKT_CURRENCY_TYPE_RED_DIAMOND = 1;                // 红钻
    GIFTPKT_CURRENCY_TYPE_GUILD_CONTRIBUTION  = 2;        // 公会贡献值
	GIFTPKT_CURRENCY_TYPE_GUILD_MEMBER_CONTRIBUTION  = 3; // 公会成员的贡献值
	GIFTPKT_CURRENCY_TYPE_OLDDATA = 254;                  // 特殊的货币类型, 表明数据来源与老接口迁移的旧数据 不知道礼包购买时使用的支付货币类型
}

// 礼包的概要描述
message GiftProduct{
    // buf:lint:ignore ENUM_FIRST_VALUE_ZERO
    enum ProductStatus {
        Normal = 1; // 可购买
        SoldOut = 2;// 已售完
		TaoHao = 3; // 可淘号
		USER_FETCHED = 4; // 用户已领取
    }

    required uint64 product_id      = 1;    // 该礼包ID, 高32bit是公会ID（1~100是预留的） 低32bit是自定义的商品ID
    required string name            = 2;    // 该礼包名称
    required string description     = 3;    // 该礼包描述
	required string usage           = 4;    // 该礼包用法描述
    required uint32 status          = 5;    // 该礼包状态 see ProductStatus
	
	required uint32 currency_type   = 6;   // 消耗代币类型 see EGiftPktCurrencyType
    required uint32 price           = 7;   // 商品价格
	
    required uint32 game_id         = 8;    // 绑定的游戏ID
    required string icon_url        = 9;    // 商品图标

    required uint32 item_type          = 10;   // item类型, see EGiftPktItemType
	required uint32 item_exchange_type = 11;   // 兑换类型, see EGiftPktExchangeType
	required uint32 item_source_type = 12;     // 礼包来源, see EGiftPktSourceType
	
	optional uint32 vaild_s_date   = 13;   // 该礼包的有效期
    optional uint32 vaild_e_date   = 14;   // 该礼包的有效期
	optional uint32 remain          = 15;  // 剩余数量
    optional uint32 total           = 16;  // 总数量
	optional string game_name      = 17;
	optional uint32 platform       = 18; //平台
	optional uint32 day_limit	   = 19; //入会天数限制
}

// 礼包的实际物品item结构
message GiftItem
{
	required uint32 item_id      = 1;   // item ID
	required bytes exchange_info = 2;   // 根据兑换类型 EGiftPktExchangeType 判断使用那种兑换数据的结构体  ExchangeInfo_CodeIDPassword / ExchangeInfo_CodeID
}

// 一个完整的礼包结构
message GiftProductDetail
{
	required GiftProduct product  = 1;    // 公共信息
	repeated GiftItem item_list   = 2;    // 实际礼物列表
}

// 2.9.0新版 我的宝箱列表拉取请求
message MyGiftBoxItem
{
	required uint32 box_item_id = 1;            // 物品在宝箱中的编号
	required GiftProductDetail gift_detail = 2; // 物品信息
	required bool is_taohao = 3;        // 该物品是否是淘号得来的
	required uint32 joinbox_time  = 4;  // 该物品加入宝箱的时间
	optional string game_pack_name = 5; // 如果有绑定游戏 那么这填游戏包名
}

message GetMyGiftBoxItemListReq {
    required BaseReq base_req                      = 1;
	required uint32 begin_box_item_id              = 2;
	required uint32 count                          = 3;
	required uint32 gift_type                      = 4; // EGiftPktItemType 礼物类型 包括 激活码 代金券 首充号 游戏礼包 ... 如果是全部类型使用 GIFTPKT_ITEM_TYPE_ALL
	required uint32 gift_source                    = 5; // EGiftPktSourceType 礼包的来源 TT官方 会长上传 如果是全部类型使用 GIFTPKT_SOURCE_TYPE_ALL
	optional bool is_include_begin_itemid          = 6; // 返回的数据是否包含begin_box_item_id对应的数据 默认不包含
}

message GetMyGiftBoxItemListResp {
    required BaseResp base_resp             = 1;
    repeated MyGiftBoxItem box_item_list    = 2;    //
	
	optional uint32 req_begin_box_itemid    = 3;    // 请求协议中的 begin_box_item_id 字段原样带回
	optional uint32 req_count               = 4;    // 请求协议中的 count 字段原样带回
	optional uint32 req_gift_type           = 5;    // 请求协议中的 gift_type 字段原样带回
	optional uint32 req_gift_source         = 6;    // 请求协议中的 gift_source 字段原样带回
	optional bool req_is_include_begin_id   = 7;    // 请求协议中的 is_include_begin_itemid 字段原样带回
	
	optional bool is_have_more         = 8;         // 是否还有更多数据
}

//
